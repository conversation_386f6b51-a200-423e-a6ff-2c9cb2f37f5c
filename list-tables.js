// Script para listar tabelas do Supabase
const { createClient } = require('@supabase/supabase-js');

// Usar as credenciais do arquivo .env.example
const supabaseUrl = 'https://ubwzukpsqcrwzfbppoux.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVid3p1a3BzcWNyd3pmYnBwb3V4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzMwNzc1NzcsImV4cCI6MjA0ODY1MzU3N30.SusnN1yvYZRIivt4l3NYCimIUJChIt3oa9KmTWYiVA0';

// Criar cliente Supabase
const supabase = createClient(supabaseUrl, supabaseKey);

async function listTables() {
  try {
    // Consultar tabelas do esquema public
    const { data, error } = await supabase
      .from('pg_tables')
      .select('*')
      .eq('schemaname', 'public');

    if (error) {
      console.error('Erro ao consultar tabelas:', error);
      return;
    }

    console.log('Tabelas encontradas:');
    console.log(data);
  } catch (err) {
    console.error('Erro ao executar consulta:', err);
  }
}

// Executar a função
listTables();
