import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  base: '/', // Configuração necessária para garantir carregamento correto de recursos
  server: {
    host: true,
    port: 8081,
    strictPort: false,
    open: true, // Abre automaticamente no navegador
    watch: {
      usePolling: true, // Resolver problemas de detecção de mudanças
    },
  },
  plugins: [
    react({
      // Configuração explícita para React refresh
      fastRefresh: true,
    }),
    mode === 'development' && componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  // Garantir que os assets são servidos corretamente
  build: {
    sourcemap: true,
    outDir: 'dist',
    assetsDir: 'assets',
    rollupOptions: {
      output: {
        manualChunks: undefined, // Desativar chunking automático para evitar erros 404
        chunkFileNames: 'assets/js/[name]-[hash].js',
        entryFileNames: 'assets/js/[name]-[hash].js',
        assetFileNames: 'assets/[ext]/[name]-[hash].[ext]',
      },
    },
  },
  optimizeDeps: {
    force: true, // Forçar otimização de dependências para evitar problemas de cache
  },
}));