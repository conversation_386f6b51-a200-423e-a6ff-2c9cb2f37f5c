-- Solução para o problema de recursão infinita nas políticas de segurança da tabela "profiles"

-- 1. Criar uma função que busca todos os usuários sem causar recursão infinita
CREATE OR REPLACE FUNCTION public.buscar_todos_usuarios()
RETURNS SETOF profiles
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT * FROM profiles LIMIT 100;
$$;

-- 2. Criar uma função que busca um usuário específico pelo ID
CREATE OR REPLACE FUNCTION public.buscar_usuario_por_id(usuario_id uuid)
RETURNS profiles
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT * FROM profiles WHERE id = usuario_id LIMIT 1;
$$;

-- 3. Função para atualizar ou criar perfil de usuário sem causar recursão infinita
CREATE OR REPLACE FUNCTION public.atualizar_perfil_usuario(
  usuario_id uuid,
  usuario_nome text,
  usuario_role text,
  usuario_status text
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Verificar se o perfil já existe
  IF EXISTS (SELECT 1 FROM public.profiles WHERE id = usuario_id) THEN
    -- Atualizar perfil existente
    UPDATE public.profiles 
    SET 
      nome = usuario_nome,
      role = usuario_role,
      status = usuario_status,
      updated_at = NOW()
    WHERE id = usuario_id;
  ELSE
    -- Criar novo perfil
    INSERT INTO public.profiles (id, nome, role, status, created_at, updated_at)
    VALUES (usuario_id, usuario_nome, usuario_role, usuario_status, NOW(), NOW());
  END IF;
END;
$$;

-- 4. Função para executar SQL seguro (para administradores)
CREATE OR REPLACE FUNCTION public.executar_sql_seguro(sql_query text)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Verificar se o usuário atual é administrador
  IF EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid() AND profiles.role = 'admin'
  ) THEN
    -- Executar a consulta SQL
    EXECUTE sql_query;
  ELSE
    RAISE EXCEPTION 'Apenas administradores podem executar SQL seguro';
  END IF;
END;
$$;

-- 5. Ajustar as políticas de segurança da tabela "profiles" para evitar recursão infinita
-- Primeiro, remover políticas existentes que podem estar causando recursão
DROP POLICY IF EXISTS "Usuários podem ver seus próprios perfis" ON profiles;
DROP POLICY IF EXISTS "Administradores podem ver todos os perfis" ON profiles;

-- Criar novas políticas com verificações mais simples
CREATE POLICY "Usuários podem ver seus próprios perfis"
ON profiles FOR SELECT
USING (auth.uid() = id);

CREATE POLICY "Administradores podem ver todos os perfis"
ON profiles FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid() AND profiles.role = 'admin'
  )
);

-- 6. Criar uma visualização que pode ser usada para buscar usuários sem problemas de recursão
CREATE OR REPLACE VIEW public.usuarios_view AS
SELECT
  p.id,
  p.email,
  p.nome,
  p.role,
  p.status,
  p.foto_url,
  p.created_at
FROM profiles p;

-- Conceder permissões na visualização
GRANT SELECT ON usuarios_view TO authenticated;
