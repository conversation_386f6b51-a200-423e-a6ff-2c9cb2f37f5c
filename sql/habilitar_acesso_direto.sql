-- Corrigir problema de recursão infinita nas políticas de segurança
-- Este script deve ser executado no SQL Editor do Supabase

-- 1. Desativar temporariamente RLS para facilitar a correção
ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;

-- 2. Remover todas as políticas existentes que podem estar causando recursão
DROP POLICY IF EXISTS "Usuários podem ver seus próprios perfis" ON profiles;
DROP POLICY IF EXISTS "Administradores podem ver todos os perfis" ON profiles;
DROP POLICY IF EXISTS "Usuários podem atualizar seus próprios perfis" ON profiles;
DROP POLICY IF EXISTS "Administradores podem atualizar todos os perfis" ON profiles;
DROP POLICY IF EXISTS "Usuários podem excluir seus próprios perfis" ON profiles;
DROP POLICY IF EXISTS "Administradores podem excluir todos os perfis" ON profiles;
DROP POLICY IF EXISTS "Usuários podem inserir seus próprios perfis" ON profiles;
DROP POLICY IF EXISTS "Administradores podem inserir todos os perfis" ON profiles;

-- 3. Criar políticas que não causem recursão infinita
-- 3.1. Políticas de SELECT
CREATE POLICY "Acesso universal de leitura"
ON profiles FOR SELECT
USING (true);

-- 3.2. Políticas de UPDATE
CREATE POLICY "Usuários podem atualizar seus próprios perfis"
ON profiles 
FOR UPDATE
USING (auth.uid() = id);

-- Esta política usa uma comparação direta para administradores
CREATE POLICY "Administradores podem atualizar todos os perfis"
ON profiles 
FOR UPDATE
USING (
  auth.jwt() ->> 'role' = 'admin' -- Usa JWT diretamente, evitando recursão
);

-- 3.3. Políticas de INSERT
CREATE POLICY "Usuários podem inserir seus próprios perfis"
ON profiles 
FOR INSERT
WITH CHECK (auth.uid() = id);

CREATE POLICY "Administradores podem inserir perfis"
ON profiles 
FOR INSERT
WITH CHECK (
  auth.jwt() ->> 'role' = 'admin'
);

-- 3.4. Políticas de DELETE
CREATE POLICY "Usuários podem excluir seus próprios perfis"
ON profiles 
FOR DELETE
USING (auth.uid() = id);

CREATE POLICY "Administradores podem excluir perfis"
ON profiles 
FOR DELETE
USING (
  auth.jwt() ->> 'role' = 'admin'
);

-- 4. Reativar RLS
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- 5. Garantir permissões no nível da tabela
GRANT ALL ON profiles TO authenticated;
