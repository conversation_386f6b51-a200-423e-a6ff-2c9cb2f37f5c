-- Script para adicionar individualmente cada novo papel ao tipo user_role
-- Esta abordagem é mais segura quando já existem dados usando o tipo

DO $$
BEGIN
  -- 1. Tentar adicionar cada novo valor, ignorando erros se já existir
  
  -- Adicionar 'gerente_geral' se não existir
  BEGIN
    IF NOT EXISTS (
      SELECT 1 FROM pg_enum 
      WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'user_role')
      AND enumlabel = 'gerente_geral'
    ) THEN
      ALTER TYPE user_role ADD VALUE 'gerente_geral';
      RAISE NOTICE 'Valor gerente_geral adicionado com sucesso';
    ELSE
      RAISE NOTICE 'Valor gerente_geral já existe, pulando...';
    END IF;
  EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'Erro ao adicionar gerente_geral: %', SQLERRM;
  END;
  
  -- Adicionar 'gerente_rpv' se não existir
  BEGIN
    IF NOT EXISTS (
      SELECT 1 FROM pg_enum 
      WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'user_role')
      AND enumlabel = 'gerente_rpv'
    ) THEN
      ALTER TYPE user_role ADD VALUE 'gerente_rpv';
      RAISE NOTICE 'Valor gerente_rpv adicionado com sucesso';
    ELSE
      RAISE NOTICE 'Valor gerente_rpv já existe, pulando...';
    END IF;
  EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'Erro ao adicionar gerente_rpv: %', SQLERRM;
  END;
  
  -- Adicionar 'captador' se não existir
  BEGIN
    IF NOT EXISTS (
      SELECT 1 FROM pg_enum 
      WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'user_role')
      AND enumlabel = 'captador'
    ) THEN
      ALTER TYPE user_role ADD VALUE 'captador';
      RAISE NOTICE 'Valor captador adicionado com sucesso';
    ELSE
      RAISE NOTICE 'Valor captador já existe, pulando...';
    END IF;
  EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'Erro ao adicionar captador: %', SQLERRM;
  END;
  
  -- Adicionar 'operacional_precatorio' se não existir
  BEGIN
    IF NOT EXISTS (
      SELECT 1 FROM pg_enum 
      WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'user_role')
      AND enumlabel = 'operacional_precatorio'
    ) THEN
      ALTER TYPE user_role ADD VALUE 'operacional_precatorio';
      RAISE NOTICE 'Valor operacional_precatorio adicionado com sucesso';
    ELSE
      RAISE NOTICE 'Valor operacional_precatorio já existe, pulando...';
    END IF;
  EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'Erro ao adicionar operacional_precatorio: %', SQLERRM;
  END;
  
  -- Adicionar 'operacional_rpv' se não existir
  BEGIN
    IF NOT EXISTS (
      SELECT 1 FROM pg_enum 
      WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'user_role')
      AND enumlabel = 'operacional_rpv'
    ) THEN
      ALTER TYPE user_role ADD VALUE 'operacional_rpv';
      RAISE NOTICE 'Valor operacional_rpv adicionado com sucesso';
    ELSE
      RAISE NOTICE 'Valor operacional_rpv já existe, pulando...';
    END IF;
  EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'Erro ao adicionar operacional_rpv: %', SQLERRM;
  END;
  
  -- Adicionar 'operacional_completo' se não existir
  BEGIN
    IF NOT EXISTS (
      SELECT 1 FROM pg_enum 
      WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'user_role')
      AND enumlabel = 'operacional_completo'
    ) THEN
      ALTER TYPE user_role ADD VALUE 'operacional_completo';
      RAISE NOTICE 'Valor operacional_completo adicionado com sucesso';
    ELSE
      RAISE NOTICE 'Valor operacional_completo já existe, pulando...';
    END IF;
  EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'Erro ao adicionar operacional_completo: %', SQLERRM;
  END;
  
  -- 2. Listar os valores do tipo após as alterações
  RAISE NOTICE 'Valores do tipo user_role após as alterações:';
  FOR rec IN
    SELECT enumlabel
    FROM pg_enum
    WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'user_role')
    ORDER BY enumsortorder
  LOOP
    RAISE NOTICE '%', rec.enumlabel;
  END LOOP;
  
END $$; 