-- Script para casos onde não é possível alterar o tipo enumerado existente
-- Este script cria e usa uma tabela de mapeamento entre os papéis antigos e novos
-- Deve ser usado apenas se o script principal apresentar problemas

-- 1. <PERSON><PERSON><PERSON> tabel<PERSON> de mapeamento de papéis
CREATE TABLE IF NOT EXISTS role_mapping (
  old_role TEXT NOT NULL,
  new_role TEXT NOT NULL,
  description TEXT,
  PRIMARY KEY (old_role, new_role)
);

-- 2. Inserir os mapeamentos dos papéis antigos para os novos
-- Adaptar conforme os papéis reais existentes no seu banco de dados
INSERT INTO role_mapping (old_role, new_role, description) VALUES
  ('admin', 'admin', 'Administrador (ADC)'),
  ('gerente_precatorio', 'gerente_precatorio', 'Gerente de Precatório'),
  ('gerente_operacional', 'gerente_geral', 'Gere<PERSON> (antes gerente_operacional)'),
  ('assistente', 'operacional_completo', 'Funcionário Operacional (antes assistente)')
ON CONFLICT (old_role, new_role) DO NOTHING;

-- 3. Função para converter papel antigo para novo
CREATE OR REPLACE FUNCTION convert_old_role_to_new(p_old_role TEXT)
RETURNS TEXT AS $$
DECLARE
  v_new_role TEXT;
BEGIN
  SELECT new_role INTO v_new_role
  FROM role_mapping
  WHERE old_role = p_old_role;
  
  RETURN COALESCE(v_new_role, p_old_role);
END;
$$ LANGUAGE plpgsql;

-- 4. Backup da tabela profiles para segurança
CREATE TABLE IF NOT EXISTS profiles_backup AS
SELECT * FROM profiles;

-- 5. Atualizar os perfis usando a tabela de mapeamento (NÃO EXECUTE ANTES DE BACKUP)
DO $$
BEGIN
  -- Comentar/descomentar esta linha para executar a atualização
  -- RAISE NOTICE 'NÃO EXECUTANDO A ATUALIZAÇÃO - Remova este comentário para executar';
  -- RETURN;
  
  -- Método 1: Usar CAST para converter o texto para o tipo enum
  UPDATE profiles p
  SET role = m.new_role::user_role
  FROM role_mapping m
  WHERE p.role::TEXT = m.old_role;
  
  -- Método 2 (alternativo): Se o método 1 falhar, tente este que usa uma expressão CASE
  -- Esta abordagem evita a necessidade de fazer cast direto
  /*
  UPDATE profiles p
  SET role = CASE m.new_role
              WHEN 'admin' THEN 'admin'::user_role
              WHEN 'gerente_geral' THEN 'gerente_geral'::user_role
              WHEN 'gerente_precatorio' THEN 'gerente_precatorio'::user_role
              WHEN 'gerente_rpv' THEN 'gerente_rpv'::user_role
              WHEN 'captador' THEN 'captador'::user_role
              WHEN 'operacional_precatorio' THEN 'operacional_precatorio'::user_role
              WHEN 'operacional_rpv' THEN 'operacional_rpv'::user_role
              WHEN 'operacional_completo' THEN 'operacional_completo'::user_role
              ELSE p.role
            END
  FROM role_mapping m
  WHERE p.role::TEXT = m.old_role;
  */
  
  RAISE NOTICE 'Perfis atualizados com os novos papéis';
END $$;

-- 6. Criação de papéis temporários para usuários novos
-- Se você não pode alterar o tipo enum, crie funções para mapear entre os valores
CREATE OR REPLACE FUNCTION get_effective_role(p_stored_role TEXT)
RETURNS TEXT AS $$
BEGIN
  RETURN convert_old_role_to_new(p_stored_role);
END;
$$ LANGUAGE plpgsql;

-- 7. Alternativa para verificar permissões sem depender de tipos enumerados
CREATE OR REPLACE FUNCTION has_permission_text(
  p_user_id UUID,
  p_permission TEXT
) RETURNS BOOLEAN AS $$
DECLARE
  v_role TEXT;
  v_effective_role TEXT;
  v_has_permission BOOLEAN;
BEGIN
  -- Buscar o papel do usuário
  SELECT role::TEXT INTO v_role
  FROM profiles
  WHERE id = p_user_id;
  
  -- Converter para o papel efetivo
  v_effective_role := convert_old_role_to_new(v_role);
  
  -- Verificar permissão com base em uma lógica no código em vez de tabela
  -- Isso é uma simplificação - idealmente você teria uma tabela de permissões real
  IF v_effective_role = 'admin' THEN
    -- Admin tem todas as permissões
    v_has_permission := TRUE;
  ELSIF v_effective_role = 'gerente_geral' THEN
    -- Gerente geral tem todas as permissões exceto as administrativas
    v_has_permission := p_permission NOT IN ('gerenciar_usuarios', 'configurar_sistema', 
                                           'criar_automacao', 'editar_automacao', 'excluir_automacao');
  ELSIF v_effective_role = 'gerente_precatorio' THEN
    -- Para gerentes de precatório, permitir acesso a clientes e precatórios
    v_has_permission := p_permission LIKE '%cliente%' OR p_permission LIKE '%precatorio%';
  -- Adicionar mais casos conforme necessário
  ELSE
    -- Para outros papéis, não permitir acesso por padrão
    v_has_permission := FALSE;
  END IF;
  
  RETURN v_has_permission;
END;
$$ LANGUAGE plpgsql;

-- IMPORTANTE: Este script é uma solução alternativa e não deve ser usado
-- se você conseguir implementar a solução principal que modifica os tipos
-- enumerados diretamente. 