-- Funções auxiliares para o sistema

-- Função para verificar se uma coluna existe em uma tabela
CREATE OR REPLACE FUNCTION check_column_exists(
  p_table_name TEXT,
  p_column_name TEXT
) RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = p_table_name
    AND column_name = p_column_name
  );
END;
$$;

-- Função para criar a função check_column_exists (chamada pelo script Node.js)
CREATE OR REPLACE FUNCTION create_check_column_exists_function()
RETURNS VOID
LANGUAGE plpgsql
AS $$
BEGIN
  -- Esta função apenas assegura que check_column_exists exista
  IF NOT EXISTS (
    SELECT 1 
    FROM pg_proc 
    WHERE proname = 'check_column_exists'
  ) THEN
    EXECUTE 
    $SQL$
    CREATE FUNCTION check_column_exists(
      p_table_name TEXT,
      p_column_name TEXT
    ) RETURNS BOOLEAN
    LANGUAGE plpgsql
    AS $FUNC$
    BEGIN
      RETURN EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = p_table_name
        AND column_name = p_column_name
      );
    END;
    $FUNC$;
    $SQL$;
  END IF;
END;
$$;

-- Trigger para atualizar o campo updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  -- Verificar se a coluna updated_at existe
  IF EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = TG_TABLE_NAME
    AND column_name = 'updated_at'
  ) THEN
    NEW.updated_at = NOW();
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Criar trigger para atualizar updated_at na tabela profiles
DO $$
BEGIN
  -- Verificar se o trigger existe
  IF NOT EXISTS (
    SELECT 1 FROM pg_trigger WHERE tgname = 'set_profiles_updated_at'
  ) THEN
    -- Verificar se a coluna updated_at existe
    IF EXISTS (
      SELECT 1
      FROM information_schema.columns
      WHERE table_schema = 'public'
      AND table_name = 'profiles'
      AND column_name = 'updated_at'
    ) THEN
      EXECUTE 'CREATE TRIGGER set_profiles_updated_at
                BEFORE UPDATE ON profiles
                FOR EACH ROW
                EXECUTE FUNCTION update_updated_at_column()';
    END IF;
  END IF;
END $$; 