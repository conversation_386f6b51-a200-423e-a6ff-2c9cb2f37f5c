-- Script para conceder permissões diretas para contornar o problema
-- Execute este script no SQL Editor do Supabase para resolver problemas de permissão

-- Versão simplificada focada apenas no problema de atualização de cargo
-- 1. Desabilitar temporariamente RLS para permitir modificações
ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;

-- 2. Conceder to<PERSON> as permissões para authenticated
GRANT ALL PRIVILEGES ON TABLE profiles TO authenticated;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- 3. Definir políticas universais simplificadas
DROP POLICY IF EXISTS "Permissão universal" ON profiles;
CREATE POLICY "Permissão universal" ON profiles USING (true);

-- 4. Reativar RLS com as novas permissões
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- 5. <PERSON> necess<PERSON><PERSON>, forçar refresh de políticas
NOTIFY pgrst, 'reload schema';
