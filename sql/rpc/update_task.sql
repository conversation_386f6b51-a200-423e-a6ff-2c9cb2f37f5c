-- Função para atualizar uma tarefa sem acionar recursão infinita nas políticas RLS
CREATE OR REPLACE FUNCTION update_task(
  p_task_id UUID,
  p_title TEXT DEFAULT NULL,
  p_description TEXT DEFAULT NULL,
  p_status TEXT DEFAULT NULL,
  p_priority TEXT DEFAULT NULL,
  p_assignee_id UUID DEFAULT NULL,
  p_due_date TIMESTAMP WITH TIME ZONE DEFAULT NULL
  -- Removido p_area pois a coluna não existe no banco de dados
)
RETURNS tasks
LANGUAGE plpgsql
SECURITY DEFINER -- Execute com privilégios do criador da função
AS $$
DECLARE
  v_task tasks;
  v_user_id UUID;
  v_user_role TEXT;
  v_can_update BOOLEAN := FALSE;
BEGIN
  -- Obter ID e papel do usuário atual
  v_user_id := auth.uid();
  
  -- Buscar o papel do usuário
  SELECT role INTO v_user_role 
  FROM public.profiles 
  WHERE id = v_user_id;
  
  -- Verificar permissões
  IF v_user_role = 'admin' THEN
    v_can_update := TRUE; -- Admin pode atualizar qualquer tarefa
  ELSE
    -- Buscar a tarefa atual
    SELECT * INTO v_task
    FROM tasks
    WHERE id = p_task_id;
    
    IF NOT FOUND THEN
      RAISE EXCEPTION 'Tarefa não encontrada';
    END IF;
    
    -- Verificar permissões baseadas no papel
    IF v_user_role IN ('gerente_geral', 'gerente_precatorio', 'gerente_rpv') THEN
      -- Comentadas as verificações baseadas na coluna 'area' que não existe
      -- Gerentes podem atualizar tarefas que criaram ou baseado em seu perfil
      IF (v_user_role = 'gerente_geral') OR
         (v_task.created_by = v_user_id) THEN
        v_can_update := TRUE;
      END IF;
    ELSIF v_user_role = 'operacional_completo' THEN
      -- Operacional pode atualizar tarefas atribuídas a ele ou que ele criou
      IF v_task.assignee_id = v_user_id OR v_task.created_by = v_user_id THEN
        v_can_update := TRUE;
      END IF;
    END IF;
  END IF;
  
  -- Se não tem permissão, lançar erro
  IF NOT v_can_update THEN
    RAISE EXCEPTION 'Sem permissão para atualizar esta tarefa';
  END IF;
  
  -- Realizar a atualização
  UPDATE tasks
  SET
    title = COALESCE(p_title, title),
    description = COALESCE(p_description, description),
    status = COALESCE(p_status, status),
    priority = COALESCE(p_priority, priority),
    assignee_id = COALESCE(p_assignee_id, assignee_id),
    due_date = COALESCE(p_due_date, due_date),
    -- Removida referência à coluna area que não existe
    --area = COALESCE(p_area, area),
    updated_at = NOW()
  WHERE id = p_task_id
  RETURNING * INTO v_task;
  
  RETURN v_task;
END;
$$; 