-- Função para criar uma nova tarefa sem acionar recursão infinita nas políticas RLS
CREATE OR REPLACE FUNCTION create_task(
  p_title TEXT,
  p_description TEXT,
  p_status TEXT,
  p_priority TEXT,
  p_created_by TEXT DEFAULT '',
  p_assignee_id TEXT DEFAULT NULL,
  p_cliente_id TEXT DEFAULT NULL,
  p_precatorio_id TEXT DEFAULT NULL,
  p_due_date TIMESTAMP WITH TIME ZONE DEFAULT NULL,
  p_area TEXT DEFAULT 'AMBOS',
  p_id UUID DEFAULT gen_random_uuid()
)
RETURNS tasks
LANGUAGE plpgsql
SECURITY DEFINER -- Execute com privilégios do criador da função
AS $$
DECLARE
  v_created_at TIMESTAMP WITH TIME ZONE := NOW();
  v_task tasks;
BEGIN
  -- Inserir a tarefa diretamente, ignorando as políticas RLS
  INSERT INTO tasks (
    id, 
    title, 
    description, 
    status, 
    priority, 
    created_by, 
    assignee_id,
    cliente_id,
    precatorio_id,
    due_date,
    created_at,
    updated_at,
    area
  ) VALUES (
    p_id,
    p_title,
    p_description,
    p_status,
    p_priority,
    p_created_by,
    p_assignee_id,
    p_cliente_id,
    p_precatorio_id,
    p_due_date,
    v_created_at,
    v_created_at,
    p_area
  )
  RETURNING * INTO v_task;
  
  RETURN v_task;
END;
$$; 