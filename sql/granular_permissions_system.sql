-- Sistema de Permissões Granulares para Controle de Dados
-- Este script cria tabelas e funções para controle granular de acesso a dados

-- 1. <PERSON>riar enum para tipos de visibilidade de dados
DO $$ BEGIN
  CREATE TYPE data_visibility_type AS ENUM (
    'own_only',           -- Apenas dados próprios
    'team_only',          -- Apenas dados da equipe
    'department_only',    -- Apenas dados do departamento
    'role_based',         -- Baseado no cargo/role
    'specific_users',     -- Usuários específicos
    'all_data',           -- Todos os dados
    'custom_filter'       -- Filtro customizado
  );
EXCEPTION
  WHEN duplicate_object THEN null;
END $$;

-- 2. <PERSON>riar tabela de configurações de visibilidade de dados por usuário
CREATE TABLE IF NOT EXISTS user_data_visibility (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  resource_type TEXT NOT NULL, -- 'clientes', 'precatorios', 'rpv', 'tarefas', 'documentos'
  visibility_type data_visibility_type NOT NULL DEFAULT 'own_only',
  
  -- Configurações específicas baseadas no tipo de visibilidade
  allowed_user_ids UUID[] DEFAULT '{}', -- Para 'specific_users'
  allowed_roles TEXT[] DEFAULT '{}',    -- Para 'role_based'
  allowed_departments TEXT[] DEFAULT '{}', -- Para 'department_only'
  custom_filter_conditions JSONB,      -- Para 'custom_filter'
  
  -- Configurações adicionais
  can_view_sensitive_data BOOLEAN DEFAULT false,
  can_export_data BOOLEAN DEFAULT false,
  can_view_financial_data BOOLEAN DEFAULT false,
  can_view_personal_data BOOLEAN DEFAULT false,
  
  -- Metadados
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES profiles(id),
  
  -- Índices únicos
  UNIQUE(user_id, resource_type)
);

-- 3. Criar tabela de filtros personalizados
CREATE TABLE IF NOT EXISTS custom_data_filters (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  filter_name TEXT NOT NULL,
  resource_type TEXT NOT NULL,
  filter_conditions JSONB NOT NULL,
  is_default BOOLEAN DEFAULT false,
  is_shared BOOLEAN DEFAULT false,
  shared_with_users UUID[] DEFAULT '{}',
  shared_with_roles TEXT[] DEFAULT '{}',
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(user_id, filter_name, resource_type)
);

-- 4. Criar tabela de grupos de usuários para permissões
CREATE TABLE IF NOT EXISTS user_permission_groups (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  group_name TEXT NOT NULL UNIQUE,
  description TEXT,
  created_by UUID REFERENCES profiles(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Criar tabela de membros dos grupos
CREATE TABLE IF NOT EXISTS user_group_members (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  group_id UUID NOT NULL REFERENCES user_permission_groups(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  added_by UUID REFERENCES profiles(id),
  added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(group_id, user_id)
);

-- 6. Criar função para obter configurações de visibilidade de um usuário
CREATE OR REPLACE FUNCTION get_user_data_visibility(
  p_user_id UUID,
  p_resource_type TEXT DEFAULT NULL
) RETURNS TABLE (
  resource_type TEXT,
  visibility_type data_visibility_type,
  allowed_user_ids UUID[],
  allowed_roles TEXT[],
  allowed_departments TEXT[],
  custom_filter_conditions JSONB,
  can_view_sensitive_data BOOLEAN,
  can_export_data BOOLEAN,
  can_view_financial_data BOOLEAN,
  can_view_personal_data BOOLEAN
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    udv.resource_type,
    udv.visibility_type,
    udv.allowed_user_ids,
    udv.allowed_roles,
    udv.allowed_departments,
    udv.custom_filter_conditions,
    udv.can_view_sensitive_data,
    udv.can_export_data,
    udv.can_view_financial_data,
    udv.can_view_personal_data
  FROM user_data_visibility udv
  WHERE udv.user_id = p_user_id
  AND (p_resource_type IS NULL OR udv.resource_type = p_resource_type);
END;
$$ LANGUAGE plpgsql;

-- 7. Criar função para verificar se um usuário pode ver dados específicos
CREATE OR REPLACE FUNCTION can_user_view_data(
  p_user_id UUID,
  p_resource_type TEXT,
  p_resource_owner_id UUID DEFAULT NULL,
  p_resource_metadata JSONB DEFAULT NULL
) RETURNS BOOLEAN AS $$
DECLARE
  v_visibility_config RECORD;
  v_user_profile RECORD;
  v_owner_profile RECORD;
BEGIN
  -- Obter perfil do usuário
  SELECT role, departamento INTO v_user_profile
  FROM profiles WHERE id = p_user_id;
  
  -- Se for admin, sempre pode ver
  IF v_user_profile.role = 'admin' THEN
    RETURN TRUE;
  END IF;
  
  -- Obter configuração de visibilidade
  SELECT * INTO v_visibility_config
  FROM user_data_visibility
  WHERE user_id = p_user_id AND resource_type = p_resource_type;
  
  -- Se não há configuração específica, usar padrão baseado no role
  IF NOT FOUND THEN
    CASE v_user_profile.role
      WHEN 'gerente_geral' THEN RETURN TRUE;
      WHEN 'gerente_precatorio', 'gerente_rpv' THEN 
        RETURN p_resource_type IN ('precatorios', 'rpv', 'clientes', 'tarefas');
      ELSE RETURN p_resource_owner_id = p_user_id; -- Apenas próprios dados
    END CASE;
  END IF;
  
  -- Verificar baseado no tipo de visibilidade
  CASE v_visibility_config.visibility_type
    WHEN 'own_only' THEN
      RETURN p_resource_owner_id = p_user_id;
      
    WHEN 'team_only' THEN
      -- Verificar se o dono do recurso está na mesma equipe
      SELECT role, departamento INTO v_owner_profile
      FROM profiles WHERE id = p_resource_owner_id;
      RETURN v_owner_profile.departamento = v_user_profile.departamento;
      
    WHEN 'department_only' THEN
      SELECT departamento INTO v_owner_profile
      FROM profiles WHERE id = p_resource_owner_id;
      RETURN v_owner_profile.departamento = ANY(v_visibility_config.allowed_departments)
             OR v_owner_profile.departamento = v_user_profile.departamento;
             
    WHEN 'role_based' THEN
      SELECT role INTO v_owner_profile
      FROM profiles WHERE id = p_resource_owner_id;
      RETURN v_owner_profile.role = ANY(v_visibility_config.allowed_roles);
      
    WHEN 'specific_users' THEN
      RETURN p_resource_owner_id = ANY(v_visibility_config.allowed_user_ids)
             OR p_user_id = ANY(v_visibility_config.allowed_user_ids);
             
    WHEN 'all_data' THEN
      RETURN TRUE;
      
    WHEN 'custom_filter' THEN
      -- Implementar lógica de filtro customizado baseado em JSONB
      -- Por enquanto, retornar TRUE (implementar conforme necessário)
      RETURN TRUE;
      
    ELSE
      RETURN FALSE;
  END CASE;
END;
$$ LANGUAGE plpgsql;

-- 8. Criar função para aplicar configurações padrão de visibilidade
CREATE OR REPLACE FUNCTION apply_default_data_visibility(
  p_user_id UUID,
  p_role TEXT
) RETURNS VOID AS $$
BEGIN
  -- Limpar configurações existentes
  DELETE FROM user_data_visibility WHERE user_id = p_user_id;
  
  -- Aplicar configurações baseadas no role
  CASE p_role
    WHEN 'admin' THEN
      -- Admin vê tudo
      INSERT INTO user_data_visibility (user_id, resource_type, visibility_type, can_view_sensitive_data, can_export_data, can_view_financial_data, can_view_personal_data)
      VALUES 
        (p_user_id, 'clientes', 'all_data', true, true, true, true),
        (p_user_id, 'precatorios', 'all_data', true, true, true, true),
        (p_user_id, 'rpv', 'all_data', true, true, true, true),
        (p_user_id, 'tarefas', 'all_data', true, true, true, true),
        (p_user_id, 'documentos', 'all_data', true, true, true, true);
        
    WHEN 'gerente_geral' THEN
      -- Gerente geral vê quase tudo
      INSERT INTO user_data_visibility (user_id, resource_type, visibility_type, can_view_sensitive_data, can_export_data, can_view_financial_data, can_view_personal_data)
      VALUES 
        (p_user_id, 'clientes', 'all_data', true, true, true, false),
        (p_user_id, 'precatorios', 'all_data', true, true, true, false),
        (p_user_id, 'rpv', 'all_data', true, true, true, false),
        (p_user_id, 'tarefas', 'all_data', true, false, false, false),
        (p_user_id, 'documentos', 'team_only', false, false, false, false);
        
    WHEN 'gerente_precatorio' THEN
      -- Gerente de precatório vê dados relacionados
      INSERT INTO user_data_visibility (user_id, resource_type, visibility_type, can_view_sensitive_data, can_export_data, can_view_financial_data, can_view_personal_data)
      VALUES 
        (p_user_id, 'clientes', 'team_only', true, true, true, false),
        (p_user_id, 'precatorios', 'all_data', true, true, true, false),
        (p_user_id, 'rpv', 'own_only', false, false, false, false),
        (p_user_id, 'tarefas', 'team_only', true, false, false, false),
        (p_user_id, 'documentos', 'team_only', false, false, false, false);
        
    WHEN 'gerente_rpv' THEN
      -- Gerente de RPV vê dados relacionados
      INSERT INTO user_data_visibility (user_id, resource_type, visibility_type, can_view_sensitive_data, can_export_data, can_view_financial_data, can_view_personal_data)
      VALUES 
        (p_user_id, 'clientes', 'team_only', true, true, true, false),
        (p_user_id, 'precatorios', 'own_only', false, false, false, false),
        (p_user_id, 'rpv', 'all_data', true, true, true, false),
        (p_user_id, 'tarefas', 'team_only', true, false, false, false),
        (p_user_id, 'documentos', 'team_only', false, false, false, false);
        
    WHEN 'captador' THEN
      -- Captador vê principalmente clientes
      INSERT INTO user_data_visibility (user_id, resource_type, visibility_type, can_view_sensitive_data, can_export_data, can_view_financial_data, can_view_personal_data)
      VALUES 
        (p_user_id, 'clientes', 'own_only', false, false, false, false),
        (p_user_id, 'precatorios', 'own_only', false, false, false, false),
        (p_user_id, 'rpv', 'own_only', false, false, false, false),
        (p_user_id, 'tarefas', 'own_only', false, false, false, false),
        (p_user_id, 'documentos', 'own_only', false, false, false, false);
        
    ELSE
      -- Operacionais veem dados limitados
      INSERT INTO user_data_visibility (user_id, resource_type, visibility_type, can_view_sensitive_data, can_export_data, can_view_financial_data, can_view_personal_data)
      VALUES 
        (p_user_id, 'clientes', 'team_only', false, false, false, false),
        (p_user_id, 'precatorios', 'team_only', false, false, false, false),
        (p_user_id, 'rpv', 'team_only', false, false, false, false),
        (p_user_id, 'tarefas', 'own_only', false, false, false, false),
        (p_user_id, 'documentos', 'own_only', false, false, false, false);
  END CASE;
END;
$$ LANGUAGE plpgsql;

-- 9. Criar trigger para aplicar configurações padrão quando um usuário é criado
CREATE OR REPLACE FUNCTION trigger_apply_default_data_visibility()
RETURNS TRIGGER AS $$
BEGIN
  -- Aplicar configurações padrão de visibilidade de dados
  PERFORM apply_default_data_visibility(NEW.id, NEW.role);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Criar trigger se não existir
DROP TRIGGER IF EXISTS apply_default_data_visibility_trigger ON profiles;
CREATE TRIGGER apply_default_data_visibility_trigger
  AFTER INSERT ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION trigger_apply_default_data_visibility();

-- 10. Criar índices para performance
CREATE INDEX IF NOT EXISTS idx_user_data_visibility_user_resource ON user_data_visibility(user_id, resource_type);
CREATE INDEX IF NOT EXISTS idx_custom_data_filters_user_resource ON custom_data_filters(user_id, resource_type);
CREATE INDEX IF NOT EXISTS idx_user_group_members_user ON user_group_members(user_id);
CREATE INDEX IF NOT EXISTS idx_user_group_members_group ON user_group_members(group_id);

-- 11. Aplicar configurações padrão para usuários existentes
DO $$
DECLARE
  user_record RECORD;
BEGIN
  FOR user_record IN SELECT id, role FROM profiles WHERE role IS NOT NULL LOOP
    PERFORM apply_default_data_visibility(user_record.id, user_record.role);
  END LOOP;
END $$;
