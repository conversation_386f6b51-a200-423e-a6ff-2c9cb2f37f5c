-- Criar funções RPC para atualização segura de cargos
-- Execute este script no SQL Editor do Supabase

-- Função para atualizar cargo sem depender das políticas de segurança com recursão
CREATE OR REPLACE FUNCTION atualizar_cargo_usuario(usuario_id UUID, novo_cargo TEXT)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER -- Executa com permissões do criador da função, não do chamador
AS $$
DECLARE
  resultado JSONB;
BEGIN
  -- Atualização direta sem passar por RLS
  UPDATE profiles
  SET 
    role = novo_cargo,
    updated_at = NOW()
  WHERE id = usuario_id
  RETURNING to_jsonb(profiles.*) INTO resultado;
  
  -- Retorna os dados atualizados ou NULL se não encontrado
  RETURN resultado;
EXCEPTION
  WHEN OTHERS THEN
    RAISE EXCEPTION 'Erro ao atualizar cargo: %', SQLERRM;
END;
$$;

-- Função para executar SQL personalizado com segurança
-- Somente para administradores
CREATE OR REPLACE FUNCTION executar_sql_seguro(comando TEXT)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER -- Executa com permissões do criador da função, não do chamador
AS $$
DECLARE
  usuario_atual UUID := auth.uid();
  usuario_role TEXT;
  resultado JSONB;
BEGIN
  -- Verificar se o usuário é administrador
  SELECT role INTO usuario_role FROM profiles WHERE id = usuario_atual;
  
  IF usuario_role != 'admin' THEN
    RAISE EXCEPTION 'Acesso negado: apenas administradores podem executar SQL personalizado';
  END IF;
  
  -- Apenas permite comandos UPDATE em profiles
  IF comando NOT ILIKE 'UPDATE profiles%' THEN
    RAISE EXCEPTION 'Comando não permitido: apenas UPDATE na tabela profiles é permitido';
  END IF;
  
  -- Executar o comando
  EXECUTE comando;
  
  -- Retornar sucesso
  resultado := jsonb_build_object('sucesso', true, 'mensagem', 'Comando executado com sucesso');
  RETURN resultado;
EXCEPTION
  WHEN OTHERS THEN
    RAISE EXCEPTION 'Erro ao executar SQL: %', SQLERRM;
END;
$$;
