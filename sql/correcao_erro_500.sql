-- Correção para erros 500 no Supabase ao atualizar perfis
-- Este script resolve problemas específicos de recursão e permissão

-- Desabilitar temporariamente RLS para fazer ajustes
ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;

-- Remover todas as políticas existentes para evitar conflitos
DO $$ 
DECLARE
    pol RECORD;
BEGIN
    FOR pol IN SELECT policyname FROM pg_policies WHERE tablename = 'profiles' LOOP
        EXECUTE format('DROP POLICY IF EXISTS %I ON profiles', pol.policyname);
    END LOOP;
END $$;

-- <PERSON><PERSON>r polí<PERSON> simples para SELECT
CREATE POLICY "profiles_select_policy" ON profiles
    FOR SELECT USING (true);

-- Criar polí<PERSON> simples para UPDATE
CREATE POLICY "profiles_update_policy" ON profiles
    FOR UPDATE USING (true);

-- Criar polí<PERSON> simples para INSERT
CREATE POLICY "profiles_insert_policy" ON profiles
    FOR INSERT WITH CHECK (true);

-- Criar política simples para DELETE
CREATE POLICY "profiles_delete_policy" ON profiles
    FOR DELETE USING (true);

-- Reativar RLS
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Conceder permissões explícitas
GRANT ALL ON TABLE profiles TO authenticated;
GRANT ALL ON TABLE profiles TO service_role;
GRANT ALL ON TABLE profiles TO anon;

-- Forçar atualização do cache de permissões
NOTIFY pgrst, 'reload schema';
