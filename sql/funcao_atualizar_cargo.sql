-- Função para atualizar cargo sem depender das políticas de segurança com recursão
-- Aqui estamos corrigindo o tipo de dados (user_role em vez de TEXT)

-- Primeiro, vamos verificar e criar o tipo enum se necessário
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_role') THEN
        CREATE TYPE user_role AS ENUM ('admin', 'gerente', 'operacional');
    END IF;
END$$;

-- Agora criamos a função com o tipo correto
CREATE OR REPLACE FUNCTION atualizar_cargo_usuario(usuario_id UUID, novo_cargo user_role)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER -- Executa com permissões do criador da função, não do chamador
AS $$
DECLARE
  resultado JSONB;
BEGIN
  -- Atualização direta sem passar por RLS
  UPDATE profiles
  SET 
    role = novo_cargo,
    updated_at = NOW()
  WHERE id = usuario_id
  RETURNING to_jsonb(profiles.*) INTO resultado;
  
  -- <PERSON><PERSON><PERSON> os dados atualizados ou NULL se não encontrado
  RETURN resultado;
EXCEPTION
  WHEN OTHERS THEN
    RAISE EXCEPTION 'Erro ao atualizar cargo: %', SQLERRM;
END;
$$;

-- Criar função auxiliar para SQL direto
CREATE OR REPLACE FUNCTION executar_sql_seguro(comando TEXT)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  resultado JSONB;
BEGIN
  -- Executar o comando
  EXECUTE comando;
  
  -- Retornar sucesso
  resultado := jsonb_build_object('sucesso', true, 'mensagem', 'Comando executado com sucesso');
  RETURN resultado;
EXCEPTION
  WHEN OTHERS THEN
    RAISE EXCEPTION 'Erro ao executar SQL: %', SQLERRM;
END;
$$;
