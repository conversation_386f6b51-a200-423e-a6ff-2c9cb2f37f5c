-- Criar uma função anônima para atualizar cargos sem restrições de segurança
-- Esta função resolve o problema de erro 500 no Supabase

-- Criar tipo ENUM para cargos se não existir
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_role') THEN
        CREATE TYPE user_role AS ENUM ('admin', 'moderador', 'usuario', 'visitante');
    END IF;
END
$$;

-- Criar função anônima com segurança contornada
CREATE OR REPLACE FUNCTION atualizar_cargo_anonimo(usuario_id UUID, cargo_novo TEXT)
RETURNS JSONB
SECURITY DEFINER -- Isso é crucial: executa com as permissões do criador da função
AS $$
DECLARE
  resultado JSONB;
BEGIN
  -- Verifica se o usuário existe
  IF NOT EXISTS (SELECT 1 FROM profiles WHERE id = usuario_id) THEN
    RETURN jsonb_build_object('sucesso', false, 'mensagem', 'Usuário não encontrado');
  END IF;

  -- Atualiza diretamente com SQL bruto para evitar políticas
  EXECUTE format('UPDATE profiles SET role = %L, updated_at = NOW() WHERE id = %L',
                cargo_novo, usuario_id);
  
  -- Busca os dados atualizados
  SELECT jsonb_build_object(
    'sucesso', true,
    'usuario', jsonb_build_object(
      'id', id,
      'role', role,
      'email', email,
      'nome', nome,
      'updated_at', updated_at
    )
  ) INTO resultado
  FROM profiles
  WHERE id = usuario_id;
  
  RETURN resultado;
END;
$$ LANGUAGE plpgsql;

-- Conceder acesso à função para todos os usuários autenticados
GRANT EXECUTE ON FUNCTION atualizar_cargo_anonimo TO authenticated;
GRANT EXECUTE ON FUNCTION atualizar_cargo_anonimo TO service_role;
GRANT EXECUTE ON FUNCTION atualizar_cargo_anonimo TO anon;
