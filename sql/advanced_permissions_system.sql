-- Advanced Permissions System SQL

-- 1. Create ENUM types for roles and permissions
DO $$
BEGIN
  -- Create user_role type if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_role') THEN
    CREATE TYPE user_role AS ENUM (
      'admin',
      'gerente_geral',
      'gerente_precatorio',
      'gerente_rpv',
      'captador',
      'operacional_precatorio',
      'operacional_rpv',
      'operacional_completo',
      'assistente'
    );
  END IF;

  -- Create permission_type if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'permission_type') THEN
    CREATE TYPE permission_type AS ENUM (
      -- Permissões para clientes
      'visualizar_cliente',
      'criar_cliente',
      'editar_cliente',
      'excluir_cliente',

      -- Permissões para precatórios
      'visualizar_precatorio',
      'criar_precatorio',
      'editar_precatorio',
      'excluir_precatorio',

      -- Permissões para RPVs
      'visualizar_rpv',
      'criar_rpv',
      'editar_rpv',
      'excluir_rpv',

      -- Permissões para tarefas
      'visualizar_tarefa',
      'criar_tarefa',
      'editar_tarefa',
      'excluir_tarefa',
      'visualizar_todas_tarefas',

      -- Permissões para documentos
      'visualizar_documento',
      'criar_documento',
      'editar_documento',
      'excluir_documento',

      -- Permissões para relatórios
      'visualizar_relatorio_precatorio',
      'visualizar_relatorio_rpv',
      'visualizar_relatorio_captacao',
      'visualizar_relatorio_completo',

      -- Permissões para usuários e configurações
      'gerenciar_usuarios',
      'configurar_sistema',
      'gerenciar_permissoes'
    );
  END IF;
END $$;

-- 2. Create custom_roles table
CREATE TABLE IF NOT EXISTS custom_roles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  nome TEXT NOT NULL,
  descricao TEXT,
  cor TEXT,
  icone TEXT,
  is_system BOOLEAN DEFAULT false,
  is_deleted BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  deleted_at TIMESTAMP WITH TIME ZONE
);

-- 3. Create role_default_permissions table
CREATE TABLE IF NOT EXISTS role_default_permissions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  role_id UUID NOT NULL REFERENCES custom_roles(id) ON DELETE CASCADE,
  resource_type TEXT NOT NULL,
  action TEXT NOT NULL,
  allowed BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(role_id, resource_type, action)
);

-- 4. Create user_specific_permissions table
CREATE TABLE IF NOT EXISTS user_specific_permissions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  resource_type TEXT NOT NULL,
  resource_id TEXT,
  action TEXT NOT NULL,
  allowed BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, resource_type, resource_id, action)
);

-- 5. Create page_access table
CREATE TABLE IF NOT EXISTS page_access (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  page_path TEXT NOT NULL,
  can_access BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, page_path)
);

-- 6. Create task_visibility table
CREATE TABLE IF NOT EXISTS task_visibility (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL UNIQUE,
  can_see_own_tasks BOOLEAN NOT NULL DEFAULT true,
  can_see_team_tasks BOOLEAN NOT NULL DEFAULT false,
  can_see_all_tasks BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. Create task_visibility_users table (for specific users whose tasks can be seen)
CREATE TABLE IF NOT EXISTS task_visibility_users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  visibility_id UUID NOT NULL REFERENCES task_visibility(id) ON DELETE CASCADE,
  visible_user_id UUID NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(visibility_id, visible_user_id)
);

-- 8. Create permission_logs table
CREATE TABLE IF NOT EXISTS permission_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  admin_id UUID NOT NULL,
  user_id UUID NOT NULL,
  action TEXT NOT NULL,
  resource_type TEXT NOT NULL,
  resource_id TEXT,
  old_value JSONB,
  new_value JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 9. Create custom_views table
CREATE TABLE IF NOT EXISTS custom_views (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  view_type TEXT NOT NULL,
  configuration JSONB NOT NULL,
  is_default BOOLEAN DEFAULT false,
  created_by UUID NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 10. Create user_view_assignments table
CREATE TABLE IF NOT EXISTS user_view_assignments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  view_id UUID NOT NULL REFERENCES custom_views(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, view_id)
);

-- 11. Create user_relationships table (for hierarchical access)
CREATE TABLE IF NOT EXISTS user_relationships (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  supervisor_id UUID NOT NULL,
  subordinate_id UUID NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(supervisor_id, subordinate_id)
);

-- 12. Insert default roles
INSERT INTO custom_roles (nome, descricao, cor, icone, is_system)
VALUES
  ('Administrador', 'Acesso completo ao sistema', '#FF5733', 'Shield', true),
  ('Gerente Geral', 'Gerencia precatórios e RPVs', '#33A1FF', 'Users', true),
  ('Gerente de Precatório', 'Gerencia apenas precatórios', '#33FF57', 'FileText', true),
  ('Gerente de RPV', 'Gerencia apenas RPVs', '#F033FF', 'FileText', true),
  ('Captador', 'Responsável pela captação de clientes', '#FFFF33', 'UserPlus', true),
  ('Operacional Precatório', 'Operações com precatórios', '#33FFF6', 'ClipboardList', true),
  ('Operacional RPV', 'Operações com RPVs', '#FF33A8', 'ClipboardList', true),
  ('Operacional Completo', 'Operações com precatórios e RPVs', '#A833FF', 'Briefcase', true),
  ('Assistente', 'Acesso limitado ao sistema', '#B5B5B5', 'UserCheck', true)
ON CONFLICT DO NOTHING;

-- 13. Create function to get user permissions
CREATE OR REPLACE FUNCTION get_user_permissions(p_user_id UUID)
RETURNS JSONB AS $$
DECLARE
  v_user_role TEXT;
  v_result JSONB;
  v_role_id UUID;
BEGIN
  -- Get user role from auth.users
  SELECT role INTO v_user_role
  FROM auth.users
  WHERE id = p_user_id;

  -- Get role_id from custom_roles
  SELECT id INTO v_role_id
  FROM custom_roles
  WHERE nome = v_user_role
  AND is_deleted = false;

  -- Build result JSON
  SELECT jsonb_build_object(
    'role_permissions', COALESCE(
      (SELECT jsonb_agg(
        jsonb_build_object(
          'resource_type', resource_type,
          'action', action,
          'allowed', allowed
        )
      )
      FROM role_default_permissions
      WHERE role_id = v_role_id), '[]'::jsonb),

    'specific_permissions', COALESCE(
      (SELECT jsonb_agg(
        jsonb_build_object(
          'resource_type', resource_type,
          'resource_id', resource_id,
          'action', action,
          'allowed', allowed
        )
      )
      FROM user_specific_permissions
      WHERE user_id = p_user_id), '[]'::jsonb),

    'task_visibility', COALESCE(
      (SELECT jsonb_build_object(
        'can_see_own_tasks', tv.can_see_own_tasks,
        'can_see_team_tasks', tv.can_see_team_tasks,
        'can_see_all_tasks', tv.can_see_all_tasks,
        'visible_user_ids', COALESCE(
          (SELECT jsonb_agg(tvu.visible_user_id)
           FROM task_visibility_users tvu
           WHERE tvu.visibility_id = tv.id), '[]'::jsonb)
      )
      FROM task_visibility tv
      WHERE tv.user_id = p_user_id),
      jsonb_build_object(
        'can_see_own_tasks', true,
        'can_see_team_tasks', v_user_role != 'captador',
        'can_see_all_tasks', v_user_role = 'admin' OR v_user_role = 'gerente_geral',
        'visible_user_ids', '[]'::jsonb
      )),

    'page_access', COALESCE(
      (SELECT jsonb_agg(
        jsonb_build_object(
          'page_path', page_path,
          'can_access', can_access
        )
      )
      FROM page_access
      WHERE user_id = p_user_id), '[]'::jsonb)
  ) INTO v_result;

  RETURN v_result;
END;
$$ LANGUAGE plpgsql;

-- 14. Create function to update user permission
CREATE OR REPLACE FUNCTION update_user_permission(
  p_admin_id UUID,
  p_user_id UUID,
  p_resource_type TEXT,
  p_resource_id TEXT,
  p_action TEXT,
  p_allowed BOOLEAN
) RETURNS BOOLEAN AS $$
DECLARE
  v_old_value JSONB;
  v_new_value JSONB;
BEGIN
  -- Get old value for logging
  SELECT jsonb_build_object('allowed', allowed)
  INTO v_old_value
  FROM user_specific_permissions
  WHERE user_id = p_user_id
  AND resource_type = p_resource_type
  AND (resource_id = p_resource_id OR (resource_id IS NULL AND p_resource_id IS NULL))
  AND action = p_action;

  -- Set new value
  v_new_value := jsonb_build_object('allowed', p_allowed);

  -- Insert or update permission
  INSERT INTO user_specific_permissions (
    user_id, resource_type, resource_id, action, allowed
  )
  VALUES (
    p_user_id, p_resource_type, p_resource_id, p_action, p_allowed
  )
  ON CONFLICT (user_id, resource_type, resource_id, action)
  DO UPDATE SET
    allowed = p_allowed,
    updated_at = NOW();

  -- Log the change
  INSERT INTO permission_logs (
    admin_id, user_id, action, resource_type, resource_id, old_value, new_value
  )
  VALUES (
    p_admin_id, p_user_id, 'update_permission', p_resource_type, p_resource_id, v_old_value, v_new_value
  );

  RETURN true;
END;
$$ LANGUAGE plpgsql;

-- 15. Create function to update page access
CREATE OR REPLACE FUNCTION update_page_access(
  p_admin_id UUID,
  p_user_id UUID,
  p_page_path TEXT,
  p_can_access BOOLEAN
) RETURNS BOOLEAN AS $$
DECLARE
  v_old_value JSONB;
  v_new_value JSONB;
BEGIN
  -- Get old value for logging
  SELECT jsonb_build_object('can_access', can_access)
  INTO v_old_value
  FROM page_access
  WHERE user_id = p_user_id
  AND page_path = p_page_path;

  -- Set new value
  v_new_value := jsonb_build_object('can_access', p_can_access);

  -- Insert or update page access
  INSERT INTO page_access (
    user_id, page_path, can_access
  )
  VALUES (
    p_user_id, p_page_path, p_can_access
  )
  ON CONFLICT (user_id, page_path)
  DO UPDATE SET
    can_access = p_can_access,
    updated_at = NOW();

  -- Log the change
  INSERT INTO permission_logs (
    admin_id, user_id, action, resource_type, resource_id, old_value, new_value
  )
  VALUES (
    p_admin_id, p_user_id, 'update_page_access', 'page', p_page_path, v_old_value, v_new_value
  );

  RETURN true;
END;
$$ LANGUAGE plpgsql;

-- 16. Create function to update task visibility settings
CREATE OR REPLACE FUNCTION update_task_visibility(
  p_admin_id UUID,
  p_user_id UUID,
  p_can_see_own_tasks BOOLEAN,
  p_can_see_team_tasks BOOLEAN,
  p_can_see_all_tasks BOOLEAN,
  p_visible_user_ids UUID[]
) RETURNS BOOLEAN AS $$
DECLARE
  v_old_value JSONB;
  v_new_value JSONB;
  v_visibility_id UUID;
  v_user_id UUID;
BEGIN
  -- Get visibility ID if exists
  SELECT id INTO v_visibility_id
  FROM task_visibility
  WHERE user_id = p_user_id;

  -- Get old value for logging
  IF v_visibility_id IS NOT NULL THEN
    SELECT jsonb_build_object(
      'can_see_own_tasks', can_see_own_tasks,
      'can_see_team_tasks', can_see_team_tasks,
      'can_see_all_tasks', can_see_all_tasks,
      'visible_user_ids', (
        SELECT jsonb_agg(visible_user_id)
        FROM task_visibility_users
        WHERE visibility_id = v_visibility_id
      )
    )
    INTO v_old_value
    FROM task_visibility
    WHERE id = v_visibility_id;
  END IF;

  -- Set new value
  v_new_value := jsonb_build_object(
    'can_see_own_tasks', p_can_see_own_tasks,
    'can_see_team_tasks', p_can_see_team_tasks,
    'can_see_all_tasks', p_can_see_all_tasks,
    'visible_user_ids', to_jsonb(p_visible_user_ids)
  );

  -- Insert or update task visibility
  IF v_visibility_id IS NULL THEN
    INSERT INTO task_visibility (
      user_id, can_see_own_tasks, can_see_team_tasks, can_see_all_tasks
    )
    VALUES (
      p_user_id, p_can_see_own_tasks, p_can_see_team_tasks, p_can_see_all_tasks
    )
    RETURNING id INTO v_visibility_id;
  ELSE
    UPDATE task_visibility
    SET
      can_see_own_tasks = p_can_see_own_tasks,
      can_see_team_tasks = p_can_see_team_tasks,
      can_see_all_tasks = p_can_see_all_tasks,
      updated_at = NOW()
    WHERE id = v_visibility_id;
  END IF;

  -- Delete existing visible users
  DELETE FROM task_visibility_users
  WHERE visibility_id = v_visibility_id;

  -- Insert new visible users
  IF p_visible_user_ids IS NOT NULL AND array_length(p_visible_user_ids, 1) > 0 THEN
    FOREACH v_user_id IN ARRAY p_visible_user_ids
    LOOP
      INSERT INTO task_visibility_users (visibility_id, visible_user_id)
      VALUES (v_visibility_id, v_user_id);
    END LOOP;
  END IF;

  -- Log the change
  INSERT INTO permission_logs (
    admin_id, user_id, action, resource_type, resource_id, old_value, new_value
  )
  VALUES (
    p_admin_id, p_user_id, 'update_task_visibility', 'task_visibility', NULL, v_old_value, v_new_value
  );

  RETURN true;
END;
$$ LANGUAGE plpgsql;

-- 17. Create function to manage custom views
CREATE OR REPLACE FUNCTION manage_custom_view(
  p_admin_id UUID,
  p_view_id UUID,
  p_name TEXT,
  p_description TEXT,
  p_view_type TEXT,
  p_configuration JSONB,
  p_is_default BOOLEAN,
  p_operation TEXT -- 'create', 'update', 'delete'
) RETURNS UUID AS $$
DECLARE
  v_view_id UUID;
  v_old_value JSONB;
  v_new_value JSONB;
BEGIN
  -- Set new value for logging
  v_new_value := jsonb_build_object(
    'name', p_name,
    'description', p_description,
    'view_type', p_view_type,
    'configuration', p_configuration,
    'is_default', p_is_default
  );

  IF p_operation = 'create' THEN
    -- Create new view
    INSERT INTO custom_views (
      name, description, view_type, configuration, is_default, created_by
    )
    VALUES (
      p_name, p_description, p_view_type, p_configuration, p_is_default, p_admin_id
    )
    RETURNING id INTO v_view_id;

    -- Log the creation
    INSERT INTO permission_logs (
      admin_id, user_id, action, resource_type, resource_id, old_value, new_value
    )
    VALUES (
      p_admin_id, p_admin_id, 'create_custom_view', 'custom_view', v_view_id::text, NULL, v_new_value
    );

    RETURN v_view_id;

  ELSIF p_operation = 'update' THEN
    -- Get old value for logging
    SELECT jsonb_build_object(
      'name', name,
      'description', description,
      'view_type', view_type,
      'configuration', configuration,
      'is_default', is_default
    )
    INTO v_old_value
    FROM custom_views
    WHERE id = p_view_id;

    -- Update existing view
    UPDATE custom_views
    SET
      name = p_name,
      description = p_description,
      view_type = p_view_type,
      configuration = p_configuration,
      is_default = p_is_default,
      updated_at = NOW()
    WHERE id = p_view_id
    RETURNING id INTO v_view_id;

    -- Log the update
    INSERT INTO permission_logs (
      admin_id, user_id, action, resource_type, resource_id, old_value, new_value
    )
    VALUES (
      p_admin_id, p_admin_id, 'update_custom_view', 'custom_view', v_view_id::text, v_old_value, v_new_value
    );

    RETURN v_view_id;

  ELSIF p_operation = 'delete' THEN
    -- Get old value for logging
    SELECT jsonb_build_object(
      'name', name,
      'description', description,
      'view_type', view_type,
      'configuration', configuration,
      'is_default', is_default
    )
    INTO v_old_value
    FROM custom_views
    WHERE id = p_view_id;

    -- Delete view
    DELETE FROM custom_views
    WHERE id = p_view_id
    RETURNING id INTO v_view_id;

    -- Log the deletion
    INSERT INTO permission_logs (
      admin_id, user_id, action, resource_type, resource_id, old_value, new_value
    )
    VALUES (
      p_admin_id, p_admin_id, 'delete_custom_view', 'custom_view', v_view_id::text, v_old_value, NULL
    );

    RETURN v_view_id;
  END IF;

  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 18. Create function to assign view to user
CREATE OR REPLACE FUNCTION assign_view_to_user(
  p_admin_id UUID,
  p_user_id UUID,
  p_view_id UUID,
  p_assign BOOLEAN -- true to assign, false to unassign
) RETURNS BOOLEAN AS $$
DECLARE
  v_view_name TEXT;
BEGIN
  -- Get view name for logging
  SELECT name INTO v_view_name
  FROM custom_views
  WHERE id = p_view_id;

  IF p_assign THEN
    -- Assign view to user
    INSERT INTO user_view_assignments (user_id, view_id)
    VALUES (p_user_id, p_view_id)
    ON CONFLICT (user_id, view_id) DO NOTHING;

    -- Log the assignment
    INSERT INTO permission_logs (
      admin_id, user_id, action, resource_type, resource_id, old_value, new_value
    )
    VALUES (
      p_admin_id, p_user_id, 'assign_view', 'custom_view', p_view_id::text,
      NULL,
      jsonb_build_object('view_name', v_view_name)
    );
  ELSE
    -- Unassign view from user
    DELETE FROM user_view_assignments
    WHERE user_id = p_user_id AND view_id = p_view_id;

    -- Log the unassignment
    INSERT INTO permission_logs (
      admin_id, user_id, action, resource_type, resource_id, old_value, new_value
    )
    VALUES (
      p_admin_id, p_user_id, 'unassign_view', 'custom_view', p_view_id::text,
      jsonb_build_object('view_name', v_view_name),
      NULL
    );
  END IF;

  RETURN true;
END;
$$ LANGUAGE plpgsql;

-- 19. Create function to manage user relationships (hierarchical access)
CREATE OR REPLACE FUNCTION manage_user_relationship(
  p_admin_id UUID,
  p_supervisor_id UUID,
  p_subordinate_id UUID,
  p_operation TEXT -- 'create', 'delete'
) RETURNS BOOLEAN AS $$
BEGIN
  IF p_operation = 'create' THEN
    -- Create relationship
    INSERT INTO user_relationships (supervisor_id, subordinate_id)
    VALUES (p_supervisor_id, p_subordinate_id)
    ON CONFLICT (supervisor_id, subordinate_id) DO NOTHING;

    -- Log the creation
    INSERT INTO permission_logs (
      admin_id, user_id, action, resource_type, resource_id, old_value, new_value
    )
    VALUES (
      p_admin_id, p_subordinate_id, 'create_relationship', 'user_relationship', p_supervisor_id::text,
      NULL,
      jsonb_build_object('supervisor_id', p_supervisor_id, 'subordinate_id', p_subordinate_id)
    );
  ELSIF p_operation = 'delete' THEN
    -- Delete relationship
    DELETE FROM user_relationships
    WHERE supervisor_id = p_supervisor_id AND subordinate_id = p_subordinate_id;

    -- Log the deletion
    INSERT INTO permission_logs (
      admin_id, user_id, action, resource_type, resource_id, old_value, new_value
    )
    VALUES (
      p_admin_id, p_subordinate_id, 'delete_relationship', 'user_relationship', p_supervisor_id::text,
      jsonb_build_object('supervisor_id', p_supervisor_id, 'subordinate_id', p_subordinate_id),
      NULL
    );
  END IF;

  RETURN true;
END;
$$ LANGUAGE plpgsql;