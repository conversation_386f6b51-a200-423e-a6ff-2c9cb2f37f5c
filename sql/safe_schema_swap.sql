-- Script para atualização segura do tipo user_role usando schema swap
-- Esta abordagem é mais segura pois cria um esquema totalmente novo em paralelo

-- 1. Criar um novo schema para a migração
CREATE SCHEMA IF NOT EXISTS migration_temp;

-- 2. Criar o novo tipo no schema temporário
CREATE TYPE migration_temp.user_role AS ENUM (
  'admin',                   -- Admini<PERSON>dor (ADC)
  'gerente_geral',           -- Gerente Geral de Precatórios e RPVs
  'gerente_precatorio',      -- Gerente de Precatório
  'gerente_rpv',             -- Gerente de RPV
  'captador',                -- Captador
  'operacional_precatorio',  -- Funcionário Operacional de Precatório
  'operacional_rpv',         -- Funcionário Operacional de RPV
  'operacional_completo',    -- Funcionário Operacional Completo (Precatório + RPV)
  'assistente',              -- Valor antigo mantido para compatibilidade
  'gerente_operacional'      -- Valor antigo mantido para compatibilidade
);

-- 3. Criar uma cópia da tabela profiles no schema temporário
CREATE TABLE migration_temp.profiles (
  LIKE public.profiles INCLUDING ALL
);

-- Remover a coluna role da tabela temporária
ALTER TABLE migration_temp.profiles DROP COLUMN IF EXISTS role;

-- Adicionar a coluna role com o novo tipo
ALTER TABLE migration_temp.profiles 
  ADD COLUMN role migration_temp.user_role;

-- 4. Criar tabela de mapeamento
CREATE TABLE migration_temp.role_mapping (
  old_role TEXT PRIMARY KEY,
  new_role TEXT NOT NULL
);

-- 5. Inserir os mapeamentos
INSERT INTO migration_temp.role_mapping (old_role, new_role) VALUES
  ('admin', 'admin'),
  ('gerente_precatorio', 'gerente_precatorio'),
  ('gerente_operacional', 'gerente_geral'),
  ('assistente', 'operacional_completo');

-- 6. Copiar todos os dados exceto a coluna role
INSERT INTO migration_temp.profiles 
  (id, email, nome, foto_url, status, created_at, updated_at, gerente_responsavel)
SELECT 
  id, email, nome, foto_url, status, created_at, updated_at, gerente_responsavel
FROM public.profiles;

-- 7. Atualizar a coluna role na tabela temporária
DO $$
DECLARE
  r RECORD;
BEGIN
  FOR r IN 
    SELECT p.id, COALESCE(m.new_role, p.role::TEXT) AS new_role
    FROM public.profiles p
    LEFT JOIN migration_temp.role_mapping m ON p.role::TEXT = m.old_role
  LOOP
    EXECUTE format('UPDATE migration_temp.profiles SET role = %L::migration_temp.user_role WHERE id = %L', r.new_role, r.id);
  END LOOP;
END $$;

-- 8. Contar registros para verificar se tudo foi copiado corretamente
DO $$
DECLARE
  public_count INTEGER;
  temp_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO public_count FROM public.profiles;
  SELECT COUNT(*) INTO temp_count FROM migration_temp.profiles;
  
  IF public_count = temp_count THEN
    RAISE NOTICE 'Contagem de registros confere: % registros', public_count;
  ELSE
    RAISE WARNING 'Discrepância na contagem de registros: public (%) vs temp (%)', public_count, temp_count;
    RETURN;
  END IF;
  
  -- Verificar contagem por papel
  RAISE NOTICE 'Distribuição de papéis na tabela original:';
  FOR r IN 
    SELECT role::TEXT, COUNT(*) 
    FROM public.profiles 
    GROUP BY role
  LOOP
    RAISE NOTICE '% - %', r.role, r.count;
  END LOOP;
  
  RAISE NOTICE 'Distribuição de papéis na tabela nova:';
  FOR r IN 
    SELECT role::TEXT, COUNT(*) 
    FROM migration_temp.profiles 
    GROUP BY role
  LOOP
    RAISE NOTICE '% - %', r.role, r.count;
  END LOOP;
END $$;

-- 9. A partir daqui, o script está comentado para segurança.
-- Revise os resultados antes de executar as etapas de swap.
/*
-- Backup da tabela original
CREATE TABLE profiles_backup AS SELECT * FROM profiles;

-- Renomear a tabela original
ALTER TABLE public.profiles RENAME TO profiles_old;

-- Mover a tabela temporária para o schema public
ALTER TABLE migration_temp.profiles SET SCHEMA public;

-- Mover o tipo temporário para o schema public
CREATE TYPE public.user_role_new AS ENUM (
  'admin',                   -- Administrador (ADC)
  'gerente_geral',           -- Gerente Geral de Precatórios e RPVs
  'gerente_precatorio',      -- Gerente de Precatório
  'gerente_rpv',             -- Gerente de RPV
  'captador',                -- Captador
  'operacional_precatorio',  -- Funcionário Operacional de Precatório
  'operacional_rpv',         -- Funcionário Operacional de RPV
  'operacional_completo',    -- Funcionário Operacional Completo (Precatório + RPV)
  'assistente',              -- Valor antigo mantido para compatibilidade
  'gerente_operacional'      -- Valor antigo mantido para compatibilidade
);

-- Modificar a coluna na tabela definitiva
ALTER TABLE public.profiles 
  ALTER COLUMN role TYPE public.user_role_new USING role::TEXT::public.user_role_new;

-- Descartar objetos temporários
DROP TABLE IF EXISTS migration_temp.role_mapping;
DROP SCHEMA IF EXISTS migration_temp CASCADE;
DROP TYPE IF EXISTS public.user_role;
ALTER TYPE public.user_role_new RENAME TO user_role;

-- Limpar
-- DROP TABLE IF EXISTS profiles_old;
*/

-- Instruções para continuar após verificação:
-- 1. Remova os comentários das etapas 9 e execute o script novamente
-- 2. Verifique se tudo funcionou corretamente
-- 3. Se houver problemas, você pode restaurar com:
--    DROP TABLE IF EXISTS profiles;
--    ALTER TABLE profiles_old RENAME TO profiles;
``` 