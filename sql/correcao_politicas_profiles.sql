-- Corrigir políticas da tabela profiles para resolver o problema de recursão
-- Este script mantém a segurança mas evita o problema de recursão infinita

-- 1. Desativar RLS temporariamente
ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;

-- 2. Remover todas as políticas existentes que podem estar causando o problema
DROP POLICY IF EXISTS "Usuários podem ver seus próprios perfis" ON profiles;
DROP POLICY IF EXISTS "Administradores podem ver todos os perfis" ON profiles;
DROP POLICY IF EXISTS "Usuários podem atualizar seus próprios perfis" ON profiles;
DROP POLICY IF EXISTS "Administradores podem atualizar todos os perfis" ON profiles;
DROP POLICY IF EXISTS "Acesso universal de leitura" ON profiles;
DROP POLICY IF EXISTS "Acesso universal de atualização" ON profiles;
DROP POLICY IF EXISTS "Permissão universal" ON profiles;

-- 3. Criar políticas corretas sem recursão
-- Política de leitura - todos podem ver todos os perfis
CREATE POLICY "Política de leitura de perfis"
ON profiles FOR SELECT
USING (true);

-- Política de atualização - usuários podem atualizar seus próprios perfis
CREATE POLICY "Usuários atualizam próprios perfis"
ON profiles 
FOR UPDATE
USING (auth.uid() = id);

-- Política de atualização para administradores
-- Usa auth.jwt() para evitar recursão infinita
CREATE POLICY "Administradores atualizam qualquer perfil"
ON profiles 
FOR UPDATE
USING (
  (auth.jwt() ->> 'role')::text = 'admin'
);

-- 4. Reativar RLS
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- 5. Garantir permissões básicas
GRANT SELECT, UPDATE ON profiles TO authenticated;
