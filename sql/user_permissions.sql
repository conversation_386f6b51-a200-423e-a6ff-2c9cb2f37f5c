-- Criar o tipo enumerado para permissões
CREATE TYPE permission_type AS ENUM (
  'visualizar_clientes',
  'criar_clientes',
  'editar_clientes',
  'excluir_clientes',
  'visualizar_funcionarios',
  'criar_funcionarios',
  'editar_funcionarios',
  'excluir_funcionarios',
  'visualizar_precatorios',
  'criar_precatorios',
  'editar_precatorios',
  'excluir_precatorios',
  'visualizar_tarefas',
  'criar_tarefas',
  'editar_tarefas',
  'excluir_tarefas',
  'visualizar_automacao',
  'criar_automacao',
  'editar_automacao',
  'excluir_automacao',
  'visualizar_relatorios',
  'gerenciar_permissoes'
);

-- Criar tabela de permissões
CREATE TABLE IF NOT EXISTS permissions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  role TEXT NOT NULL,
  permission permission_type NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(role, permission)
);

-- Atualizar a coluna role da tabela profiles para usar o tipo ENUM
ALTER TABLE profiles 
  DROP CONSTRAINT IF EXISTS profiles_role_check,
  ADD CONSTRAINT profiles_role_check 
  CHECK (role IN ('admin', 'gerente_precatorio', 'gerente_operacional', 'assistente'));

-- Inserir permissões padrão para cada perfil
-- Admin tem todas as permissões
INSERT INTO permissions (role, permission)
SELECT 'admin', unnest(enum_range(NULL::permission_type))
ON CONFLICT (role, permission) DO NOTHING;

-- Gerente de Precatório
INSERT INTO permissions (role, permission)
VALUES
  ('gerente_precatorio', 'visualizar_clientes'),
  ('gerente_precatorio', 'criar_clientes'),
  ('gerente_precatorio', 'editar_clientes'),
  ('gerente_precatorio', 'visualizar_funcionarios'),
  ('gerente_precatorio', 'visualizar_precatorios'),
  ('gerente_precatorio', 'criar_precatorios'),
  ('gerente_precatorio', 'editar_precatorios'),
  ('gerente_precatorio', 'visualizar_tarefas'),
  ('gerente_precatorio', 'criar_tarefas'),
  ('gerente_precatorio', 'editar_tarefas'),
  ('gerente_precatorio', 'visualizar_relatorios')
ON CONFLICT (role, permission) DO NOTHING;

-- Gerente Operacional
INSERT INTO permissions (role, permission)
VALUES
  ('gerente_operacional', 'visualizar_clientes'),
  ('gerente_operacional', 'criar_clientes'),
  ('gerente_operacional', 'editar_clientes'),
  ('gerente_operacional', 'visualizar_funcionarios'),
  ('gerente_operacional', 'visualizar_precatorios'),
  ('gerente_operacional', 'editar_precatorios'),
  ('gerente_operacional', 'visualizar_tarefas'),
  ('gerente_operacional', 'criar_tarefas'),
  ('gerente_operacional', 'editar_tarefas'),
  ('gerente_operacional', 'visualizar_automacao'),
  ('gerente_operacional', 'criar_automacao'),
  ('gerente_operacional', 'editar_automacao'),
  ('gerente_operacional', 'visualizar_relatorios')
ON CONFLICT (role, permission) DO NOTHING;

-- Assistente
INSERT INTO permissions (role, permission)
VALUES
  ('assistente', 'visualizar_clientes'),
  ('assistente', 'visualizar_precatorios'),
  ('assistente', 'visualizar_tarefas'),
  ('assistente', 'editar_tarefas')
ON CONFLICT (role, permission) DO NOTHING;

-- Criar função para obter permissões de um papel
CREATE OR REPLACE FUNCTION get_role_permissions(role_name TEXT)
RETURNS TABLE (permission permission_type) AS $$
BEGIN
  RETURN QUERY
  SELECT p.permission FROM permissions p WHERE p.role = role_name;
END;
$$ LANGUAGE plpgsql;

-- Criar uma view para facilitar a visualização das permissões por papel
CREATE OR REPLACE VIEW role_permissions AS
SELECT
  role,
  array_agg(permission) AS permissions
FROM
  permissions
GROUP BY
  role;

-- Criar função para verificar se um papel tem uma permissão específica
CREATE OR REPLACE FUNCTION has_permission(role_name TEXT, perm permission_type)
RETURNS BOOLEAN AS $$
DECLARE
  has_perm BOOLEAN;
BEGIN
  SELECT EXISTS (
    SELECT 1 FROM permissions WHERE role = role_name AND permission = perm
  ) INTO has_perm;
  
  RETURN has_perm;
END;
$$ LANGUAGE plpgsql; 