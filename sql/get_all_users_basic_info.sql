-- Função para obter informações básicas de todos os usuários
-- Esta função evita a recursão infinita nas políticas de segurança
CREATE OR REPLACE FUNCTION public.get_all_users_basic_info()
RETURNS SETOF json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT json_build_object(
    'id', auth.users.id,
    'email', auth.users.email,
    'nome', profiles.nome,
    'role', profiles.role,
    'status', profiles.status,
    'foto_url', profiles.foto_url,
    'created_at', profiles.created_at
  )
  FROM auth.users
  LEFT JOIN public.profiles ON auth.users.id = profiles.id
  LIMIT 100;
END;
$$;
