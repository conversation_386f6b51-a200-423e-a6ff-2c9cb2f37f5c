-- <PERSON>ript para criar o usuário administrador padrão
-- Email: <EMAIL>
-- Senha: Senha2025

-- Primeiro, verificamos se o usuário já existe na tabela de autenticação do Supabase
DO $$
DECLARE
  admin_user_id UUID;
BEGIN
  -- Verificar se o usuário já existe na tabela auth.users
  SELECT id INTO admin_user_id
  FROM auth.users
  WHERE email = '<EMAIL>';
  
  -- Se o usuário não existir, criar o usuário na tabela auth.users
  IF admin_user_id IS NULL THEN
    -- Inserir o usuário administrador na tabela auth.users
    -- NOTA: Esta operação precisa de privilégios de administrador no Supabase
    -- Normalmente seria usada a função auth.create_user ao invés de INSERT direto
    INSERT INTO auth.users (
      instance_id,
      id,
      aud,
      role,
      email,
      encrypted_password,
      email_confirmed_at,
      created_at,
      updated_at
    )
    VALUES (
      '00000000-0000-0000-0000-000000000000',
      gen_random_uuid(),
      'authenticated',
      'authenticated',
      '<EMAIL>',
      -- A senha criptografada seria gerada pelo Supabase em uma operação real
      -- Este é apenas um placeholder - a senha real deve ser definida pelo Supabase
      crypt('Senha2025', gen_salt('bf')),
      now(),
      now(),
      now()
    )
    RETURNING id INTO admin_user_id;
    
    -- Confirmar o email do usuário
    UPDATE auth.users
    SET email_confirmed_at = now()
    WHERE id = admin_user_id;
  END IF;
  
  -- Verificar se o perfil já existe
  IF NOT EXISTS (SELECT 1 FROM public.profiles WHERE id = admin_user_id) THEN
    -- Inserir o perfil do administrador na tabela profiles
    INSERT INTO public.profiles (
      id,
      email,
      nome,
      role,
      status,
      created_at,
      updated_at
    )
    VALUES (
      admin_user_id,
      '<EMAIL>',
      'Administrador',
      'admin',
      'ativo',
      now(),
      now()
    );
  ELSE
    -- Garantir que o usuário existente tenha o papel de administrador
    -- Usando a variável NOW() para atualizar tanto o updated_at quanto os outros campos
    UPDATE public.profiles
    SET 
      role = 'admin', 
      status = 'ativo',
      updated_at = now()
    WHERE id = admin_user_id;
  END IF;
END $$;

-- IMPORTANTE: Para criar o usuário administrador em produção,
-- recomenda-se usar a interface de administração do Supabase ou as APIs
-- de gerenciamento de usuários, pois a manipulação direta das tabelas auth
-- pode não funcionar conforme esperado.
--
-- Método alternativo usando API do Supabase:
-- 1. Usar POST /auth/v1/admin/users com API key de service_role
-- 2. Corpo da requisição: 
--    {
--      "email": "<EMAIL>", 
--      "password": "Senha2025", 
--      "email_confirm": true
--    }
-- 3. Em seguida, inserir na tabela profiles os dados adicionais 