-- Script para atualizar diretamente os papéis de usuário
-- Este script atualiza cada papel individualmente, evitando problemas de cast

-- 1. Backup da tabela profiles para segurança (IMPORTANTE)
CREATE TABLE IF NOT EXISTS profiles_backup AS
SELECT * FROM profiles;

-- 2. Atualizar diretamente cada papel sem usar mapeamentos
DO $$
BEGIN
  -- Verificar se o backup foi feito
  IF NOT EXISTS (SELECT 1 FROM profiles_backup LIMIT 1) THEN
    RAISE EXCEPTION 'ABORTANDO: Backup não foi criado corretamente antes da atualização!';
  END IF;
  
  -- Atualizações individuais para cada papel
  
  -- Converter 'assistente' para 'operacional_completo'
  UPDATE profiles
  SET role = 'operacional_completo'::user_role
  WHERE role::TEXT = 'assistente';
  
  -- Converter 'gerente_operacional' para 'gerente_geral'
  UPDATE profiles
  SET role = 'gerente_geral'::user_role
  WHERE role::TEXT = 'gerente_operacional';
  
  -- Outros mapeamentos específicos podem ser adicionados aqui
  
  RAISE NOTICE 'Papéis atualizados com sucesso';
END $$;

-- 3. Verificar o resultado
-- SELECT role, COUNT(*) FROM profiles GROUP BY role;

-- 4. Se precisar restaurar da backup:
-- 
-- TRUNCATE profiles;
-- INSERT INTO profiles SELECT * FROM profiles_backup; 