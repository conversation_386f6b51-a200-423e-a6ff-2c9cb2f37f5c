-- <PERSON><PERSON><PERSON> para criar as tabelas de cargos personalizados
-- Execute este script no SQL Editor do Supabase

-- 1. Tabela de cargos personalizados
CREATE TABLE IF NOT EXISTS custom_roles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  nome VARCHAR(100) NOT NULL,
  descricao TEXT,
  cor VARCHAR(20) NOT NULL DEFAULT '#3b82f6',
  icone VARCHAR(50),
  is_system BOOLEAN NOT NULL DEFAULT false,
  is_deleted BOOLEAN NOT NULL DEFAULT false,
  deleted_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Tabel<PERSON> de permissões padrão para cargos
CREATE TABLE IF NOT EXISTS role_default_permissions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  role_id UUID NOT NULL REFERENCES custom_roles(id),
  resource_type VARCHAR(50) NOT NULL,
  action VARCHAR(50) NOT NULL,
  allowed BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(role_id, resource_type, action)
);

-- 3. Tabela de acesso a páginas para cargos
CREATE TABLE IF NOT EXISTS role_page_access (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  role_id UUID NOT NULL REFERENCES custom_roles(id),
  page_path VARCHAR(100) NOT NULL,
  can_access BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(role_id, page_path)
);

-- 4. Inserir cargos do sistema
INSERT INTO custom_roles (nome, descricao, cor, is_system, created_at, updated_at)
VALUES
  ('Administrador', 'Acesso completo ao sistema', '#ef4444', true, NOW(), NOW()),
  ('Gerente Geral', 'Gerencia precatórios e RPVs', '#3b82f6', true, NOW(), NOW()),
  ('Gerente de Precatório', 'Gerencia apenas precatórios', '#10b981', true, NOW(), NOW()),
  ('Gerente de RPV', 'Gerencia apenas RPVs', '#f59e0b', true, NOW(), NOW()),
  ('Captador', 'Responsável pela captação de clientes', '#8b5cf6', true, NOW(), NOW()),
  ('Operacional - Precatório', 'Operações com precatórios', '#06b6d4', true, NOW(), NOW()),
  ('Operacional - RPV', 'Operações com RPVs', '#ec4899', true, NOW(), NOW()),
  ('Operacional - Completo', 'Operações com precatórios e RPVs', '#f97316', true, NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- 5. Função para aplicar permissões padrão de um cargo a um usuário
CREATE OR REPLACE FUNCTION apply_role_default_permissions(
  p_user_id UUID,
  p_role_id UUID
)
RETURNS VOID
LANGUAGE plpgsql
AS $$
BEGIN
  -- Limpar permissões específicas existentes
  DELETE FROM user_specific_permissions
  WHERE user_id = p_user_id;
  
  -- Inserir novas permissões baseadas no cargo
  INSERT INTO user_specific_permissions (
    user_id,
    resource_type,
    resource_id,
    action,
    allowed,
    created_at
  )
  SELECT
    p_user_id,
    rdp.resource_type,
    NULL,
    rdp.action,
    rdp.allowed,
    NOW()
  FROM
    role_default_permissions rdp
  WHERE
    rdp.role_id = p_role_id;
    
  -- Aplicar acesso a páginas
  DELETE FROM user_page_access
  WHERE user_id = p_user_id;
  
  INSERT INTO user_page_access (
    user_id,
    page_path,
    can_access,
    created_at
  )
  SELECT
    p_user_id,
    rpa.page_path,
    rpa.can_access,
    NOW()
  FROM
    role_page_access rpa
  WHERE
    rpa.role_id = p_role_id;
END;
$$;
