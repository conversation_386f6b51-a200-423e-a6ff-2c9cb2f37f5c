-- Script para recriar completamente o tipo user_role
-- ATENÇÃO: Este script faz alterações estruturais significativas na base de dados.
-- Fazer backup completo antes de executar!

-- 1. <PERSON><PERSON><PERSON> backup das tabelas que usam user_role
CREATE TABLE IF NOT EXISTS profiles_backup AS SELECT * FROM profiles;

-- 2. Criar uma tabela temporária para armazenar os mapeamentos de papel
CREATE TABLE IF NOT EXISTS role_mapping_temp (
  id UUID PRIMARY KEY,
  old_role TEXT,
  new_role TEXT
);

-- 3. Backup dos valores atuais com seus IDs
INSERT INTO role_mapping_temp (id, old_role)
SELECT id, role::TEXT
FROM profiles;

-- 4. Verificar valores únicos existentes
DO $$
DECLARE
  unique_roles TEXT;
BEGIN
  SELECT string_agg(DISTINCT role::TEXT, ', ')
  INTO unique_roles
  FROM profiles;
  
  RAISE NOTICE 'Valores únicos de role atualmente na tabela profiles: %', unique_roles;
END $$;

-- 5. <PERSON>mover a coluna role da tabela profiles
ALTER TABLE profiles DROP COLUMN role;

-- 6. <PERSON><PERSON><PERSON> o tipo user_role existente
DROP TYPE IF EXISTS user_role;

-- 7. Criar um novo tipo user_role com todos os valores necessários
CREATE TYPE user_role AS ENUM (
  'admin',                   -- Administrador (ADC)
  'gerente_geral',           -- Gerente Geral de Precatórios e RPVs
  'gerente_precatorio',      -- Gerente de Precatório
  'gerente_rpv',             -- Gerente de RPV
  'captador',                -- Captador
  'operacional_precatorio',  -- Funcionário Operacional de Precatório
  'operacional_rpv',         -- Funcionário Operacional de RPV
  'operacional_completo',    -- Funcionário Operacional Completo (Precatório + RPV)
  'assistente',              -- Valor antigo mantido para compatibilidade
  'gerente_operacional'      -- Valor antigo mantido para compatibilidade
);

-- 8. Adicionar a coluna role de volta à tabela profiles
ALTER TABLE profiles ADD COLUMN role user_role;

-- 9. Preencher a tabela de mapeamento com os valores novos
UPDATE role_mapping_temp
SET new_role = CASE old_role
  WHEN 'admin' THEN 'admin'
  WHEN 'gerente_precatorio' THEN 'gerente_precatorio'
  WHEN 'gerente_operacional' THEN 'gerente_geral'
  WHEN 'assistente' THEN 'operacional_completo'
  -- Adicione outros mapeamentos se necessário
  ELSE old_role
END;

-- 10. Atualizar a coluna role na tabela profiles
DO $$
DECLARE
  r RECORD;
BEGIN
  FOR r IN SELECT id, new_role FROM role_mapping_temp
  LOOP
    EXECUTE format('UPDATE profiles SET role = %L::user_role WHERE id = %L', r.new_role, r.id);
  END LOOP;
END $$;

-- 11. Verificar resultados
DO $$
DECLARE
  missing_roles INTEGER;
  success_message TEXT;
BEGIN
  SELECT COUNT(*)
  INTO missing_roles
  FROM profiles
  WHERE role IS NULL;
  
  IF missing_roles > 0 THEN
    RAISE WARNING 'Atenção: % perfis ainda estão com role NULL após a migração', missing_roles;
  ELSE
    SELECT string_agg(role::TEXT || ': ' || COUNT(*)::TEXT, ', ')
    INTO success_message
    FROM profiles
    GROUP BY role;
    
    RAISE NOTICE 'Migração concluída com sucesso. Distribuição de papéis: %', success_message;
  END IF;
END $$;

-- 12. Limpar tabelas temporárias
DROP TABLE IF EXISTS role_mapping_temp;

-- Não exclua a tabela de backup até confirmar que está tudo correto
-- DROP TABLE IF EXISTS profiles_backup; 