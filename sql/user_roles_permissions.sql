-- Atualização do sistema de papéis e permissões

-- 1. Verificar e atualizar o tipo enumerado para os papéis de usuário
DO $$
BEGIN
  -- Verificar se o tipo user_role já existe
  IF EXISTS (
    SELECT 1 FROM pg_type WHERE typname = 'user_role'
  ) THEN
    -- Se já existe, alterar para adicionar os novos valores que possam estar faltando
    
    -- Adicionar 'gerente_geral' se não existir
    IF NOT EXISTS (
      SELECT 1 FROM pg_enum 
      WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'user_role')
      AND enumlabel = 'gerente_geral'
    ) THEN
      ALTER TYPE user_role ADD VALUE 'gerente_geral' AFTER 'admin';
    END IF;
    
    -- Adicionar 'gerente_rpv' se não existir
    IF NOT EXISTS (
      SELECT 1 FROM pg_enum 
      WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'user_role')
      AND enumlabel = 'gerente_rpv'
    ) THEN
      ALTER TYPE user_role ADD VALUE 'gerente_rpv' AFTER 'gerente_precatorio';
    END IF;
    
    -- Adicionar 'captador' se não existir
    IF NOT EXISTS (
      SELECT 1 FROM pg_enum 
      WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'user_role')
      AND enumlabel = 'captador'
    ) THEN
      ALTER TYPE user_role ADD VALUE 'captador' AFTER 'gerente_rpv';
    END IF;
    
    -- Adicionar 'operacional_precatorio' se não existir
    IF NOT EXISTS (
      SELECT 1 FROM pg_enum 
      WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'user_role')
      AND enumlabel = 'operacional_precatorio'
    ) THEN
      ALTER TYPE user_role ADD VALUE 'operacional_precatorio' AFTER 'captador';
    END IF;
    
    -- Adicionar 'operacional_rpv' se não existir
    IF NOT EXISTS (
      SELECT 1 FROM pg_enum 
      WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'user_role')
      AND enumlabel = 'operacional_rpv'
    ) THEN
      ALTER TYPE user_role ADD VALUE 'operacional_rpv' AFTER 'operacional_precatorio';
    END IF;
    
    -- Adicionar 'operacional_completo' se não existir
    IF NOT EXISTS (
      SELECT 1 FROM pg_enum 
      WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'user_role')
      AND enumlabel = 'operacional_completo'
    ) THEN
      ALTER TYPE user_role ADD VALUE 'operacional_completo' AFTER 'operacional_rpv';
    END IF;
    
    -- Remover valores antigos se necessário (isso requer cuidado)
    -- Não vou adicionar essa lógica por segurança sem conhecer o estado atual da aplicação
    
  ELSE
    -- Se não existe, criar o tipo
    CREATE TYPE user_role AS ENUM (
      'admin', -- Administrador (ADC)
      'gerente_geral', -- Gerente Geral de Precatórios e RPVs
      'gerente_precatorio', -- Gerente de Precatório
      'gerente_rpv', -- Gerente de RPV
      'captador', -- Captador
      'operacional_precatorio', -- Funcionário Operacional de Precatório
      'operacional_rpv', -- Funcionário Operacional de RPV
      'operacional_completo' -- Funcionário Operacional Completo (Precatório + RPV)
    );
  END IF;
END $$;

-- 2. Atualizar a coluna role na tabela profiles para usar o tipo user_role
-- Nota: Se o tipo já existe e tem os mesmos valores, isso não fará alterações
ALTER TABLE profiles 
  DROP CONSTRAINT IF EXISTS profiles_role_check,
  ALTER COLUMN role TYPE user_role USING role::user_role;

-- 3. Adicionar coluna para o gerente responsável (para o caso de captadores)
ALTER TABLE profiles
  ADD COLUMN IF NOT EXISTS gerente_responsavel UUID REFERENCES profiles(id);

-- 4. Atualizar ou criar a tabela de tipos de processo/documento
-- Primeiro, verificamos se existe uma tabela tipos_processo
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = 'tipos_processo'
  ) THEN
    CREATE TABLE tipos_processo (
      id SERIAL PRIMARY KEY,
      nome VARCHAR(100) NOT NULL,
      descricao TEXT,
      categoria VARCHAR(20) NOT NULL CHECK (categoria IN ('PRECATORIO', 'RPV', 'AMBOS')),
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    
    -- Inserir valores padrão
    INSERT INTO tipos_processo (nome, descricao, categoria) VALUES
      ('Precatório Federal', 'Precatório da Justiça Federal', 'PRECATORIO'),
      ('Precatório Estadual', 'Precatório da Justiça Estadual', 'PRECATORIO'),
      ('Precatório Municipal', 'Precatório da Justiça Municipal', 'PRECATORIO'),
      ('RPV Federal', 'Requisição de Pequeno Valor Federal', 'RPV'),
      ('RPV Estadual', 'Requisição de Pequeno Valor Estadual', 'RPV'),
      ('RPV Municipal', 'Requisição de Pequeno Valor Municipal', 'RPV');
  END IF;
END $$;

-- 5. Atualizar a tabela de precatórios para incluir tipo RPV ou Precatório
ALTER TABLE precatorios
  ADD COLUMN IF NOT EXISTS tipo_id INTEGER REFERENCES tipos_processo(id),
  ADD COLUMN IF NOT EXISTS categoria VARCHAR(20) CHECK (categoria IN ('PRECATORIO', 'RPV')),
  ADD COLUMN IF NOT EXISTS captador_id UUID REFERENCES profiles(id);

-- 6. Adicionar campo de área nas tarefas para filtrar por tipo (Precatório/RPV)
ALTER TABLE tarefas
  ADD COLUMN IF NOT EXISTS area VARCHAR(20) CHECK (area IN ('PRECATORIO', 'RPV', 'AMBOS')),
  ADD COLUMN IF NOT EXISTS responsavel_id UUID REFERENCES profiles(id);

-- 7. Verificar e criar o tipo enumerado para permissões
DO $$
BEGIN
  -- Verificar se o tipo permission_type já existe
  IF EXISTS (
    SELECT 1 FROM pg_type WHERE typname = 'permission_type'
  ) THEN
    -- Aqui poderia ter lógica para adicionar novos valores (similar ao user_role)
    -- Deixaremos isso para uma implementação futura para evitar complexidade
    RAISE NOTICE 'Tipo permission_type já existe. Não será modificado.';
  ELSE
    -- Se não existe, criar o tipo
    CREATE TYPE permission_type AS ENUM (
      -- Permissões para clientes
      'criar_cliente',
      'editar_cliente',
      'excluir_cliente',
      'visualizar_cliente',
      
      -- Permissões para Precatórios
      'criar_precatorio',
      'editar_precatorio',
      'excluir_precatorio',
      'visualizar_precatorio',
      
      -- Permissões para RPVs
      'criar_rpv',
      'editar_rpv',
      'excluir_rpv',
      'visualizar_rpv',
      
      -- Permissões para tarefas
      'criar_tarefa',
      'editar_tarefa',
      'excluir_tarefa',
      'visualizar_tarefa',
      'visualizar_todas_tarefas',
      
      -- Permissões para automações
      'criar_automacao',
      'editar_automacao',
      'excluir_automacao',
      'visualizar_automacao',
      
      -- Permissões para relatórios
      'visualizar_relatorio_precatorio',
      'visualizar_relatorio_rpv',
      'visualizar_relatorio_captacao',
      'visualizar_relatorio_completo',
      
      -- Permissões para usuários e configurações
      'gerenciar_usuarios',
      'configurar_sistema'
    );
  END IF;
END $$;

-- 8. Criar tabela de permissões se não existir
CREATE TABLE IF NOT EXISTS permissions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  role user_role NOT NULL,
  permission permission_type NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(role, permission)
);

-- 9. Inserir permissões para cada papel
-- 9.1 Administrador (acesso completo)
INSERT INTO permissions (role, permission)
SELECT 'admin', unnest(enum_range(NULL::permission_type))
ON CONFLICT (role, permission) DO NOTHING;

-- 9.2 Gerente Geral
INSERT INTO permissions (role, permission) VALUES
  ('gerente_geral', 'criar_cliente'),
  ('gerente_geral', 'editar_cliente'),
  ('gerente_geral', 'excluir_cliente'),
  ('gerente_geral', 'visualizar_cliente'),
  ('gerente_geral', 'criar_precatorio'),
  ('gerente_geral', 'editar_precatorio'),
  ('gerente_geral', 'excluir_precatorio'),
  ('gerente_geral', 'visualizar_precatorio'),
  ('gerente_geral', 'criar_rpv'),
  ('gerente_geral', 'editar_rpv'),
  ('gerente_geral', 'excluir_rpv'),
  ('gerente_geral', 'visualizar_rpv'),
  ('gerente_geral', 'criar_tarefa'),
  ('gerente_geral', 'editar_tarefa'),
  ('gerente_geral', 'excluir_tarefa'),
  ('gerente_geral', 'visualizar_tarefa'),
  ('gerente_geral', 'visualizar_todas_tarefas'),
  ('gerente_geral', 'visualizar_relatorio_precatorio'),
  ('gerente_geral', 'visualizar_relatorio_rpv')
ON CONFLICT (role, permission) DO NOTHING;

-- 9.3 Gerente de Precatório
INSERT INTO permissions (role, permission) VALUES
  ('gerente_precatorio', 'criar_cliente'),
  ('gerente_precatorio', 'editar_cliente'),
  ('gerente_precatorio', 'excluir_cliente'),
  ('gerente_precatorio', 'visualizar_cliente'),
  ('gerente_precatorio', 'criar_precatorio'),
  ('gerente_precatorio', 'editar_precatorio'),
  ('gerente_precatorio', 'excluir_precatorio'),
  ('gerente_precatorio', 'visualizar_precatorio'),
  ('gerente_precatorio', 'criar_tarefa'),
  ('gerente_precatorio', 'editar_tarefa'),
  ('gerente_precatorio', 'excluir_tarefa'),
  ('gerente_precatorio', 'visualizar_tarefa'),
  ('gerente_precatorio', 'visualizar_relatorio_precatorio')
ON CONFLICT (role, permission) DO NOTHING;

-- 9.4 Gerente de RPV
INSERT INTO permissions (role, permission) VALUES
  ('gerente_rpv', 'criar_cliente'),
  ('gerente_rpv', 'editar_cliente'),
  ('gerente_rpv', 'excluir_cliente'),
  ('gerente_rpv', 'visualizar_cliente'),
  ('gerente_rpv', 'criar_rpv'),
  ('gerente_rpv', 'editar_rpv'),
  ('gerente_rpv', 'excluir_rpv'),
  ('gerente_rpv', 'visualizar_rpv'),
  ('gerente_rpv', 'criar_tarefa'),
  ('gerente_rpv', 'editar_tarefa'),
  ('gerente_rpv', 'excluir_tarefa'),
  ('gerente_rpv', 'visualizar_tarefa'),
  ('gerente_rpv', 'visualizar_relatorio_rpv')
ON CONFLICT (role, permission) DO NOTHING;

-- 9.5 Captador
INSERT INTO permissions (role, permission) VALUES
  ('captador', 'criar_cliente'),
  ('captador', 'editar_cliente'),
  ('captador', 'excluir_cliente'),
  ('captador', 'visualizar_cliente'),
  ('captador', 'criar_precatorio'),
  ('captador', 'editar_precatorio'),
  ('captador', 'visualizar_precatorio'),
  ('captador', 'criar_rpv'),
  ('captador', 'editar_rpv'),
  ('captador', 'visualizar_rpv'),
  ('captador', 'visualizar_tarefa'),
  ('captador', 'visualizar_relatorio_captacao')
ON CONFLICT (role, permission) DO NOTHING;

-- 9.6 Funcionário Operacional de Precatório
INSERT INTO permissions (role, permission) VALUES
  ('operacional_precatorio', 'visualizar_cliente'),
  ('operacional_precatorio', 'visualizar_precatorio'),
  ('operacional_precatorio', 'editar_precatorio'),
  ('operacional_precatorio', 'criar_tarefa'),
  ('operacional_precatorio', 'editar_tarefa'),
  ('operacional_precatorio', 'visualizar_tarefa'),
  ('operacional_precatorio', 'visualizar_relatorio_precatorio')
ON CONFLICT (role, permission) DO NOTHING;

-- 9.7 Funcionário Operacional de RPV
INSERT INTO permissions (role, permission) VALUES
  ('operacional_rpv', 'visualizar_cliente'),
  ('operacional_rpv', 'visualizar_rpv'),
  ('operacional_rpv', 'editar_rpv'),
  ('operacional_rpv', 'criar_tarefa'),
  ('operacional_rpv', 'editar_tarefa'),
  ('operacional_rpv', 'visualizar_tarefa'),
  ('operacional_rpv', 'visualizar_relatorio_rpv')
ON CONFLICT (role, permission) DO NOTHING;

-- 9.8 Funcionário Operacional Completo
INSERT INTO permissions (role, permission) VALUES
  ('operacional_completo', 'visualizar_cliente'),
  ('operacional_completo', 'visualizar_precatorio'),
  ('operacional_completo', 'editar_precatorio'),
  ('operacional_completo', 'visualizar_rpv'),
  ('operacional_completo', 'editar_rpv'),
  ('operacional_completo', 'criar_tarefa'),
  ('operacional_completo', 'editar_tarefa'),
  ('operacional_completo', 'visualizar_tarefa'),
  ('operacional_completo', 'visualizar_relatorio_precatorio'),
  ('operacional_completo', 'visualizar_relatorio_rpv')
ON CONFLICT (role, permission) DO NOTHING;

-- 10. Criar função para verificar permissões
CREATE OR REPLACE FUNCTION has_permission(
  p_user_id UUID,
  p_permission permission_type
) RETURNS BOOLEAN AS $$
DECLARE
  v_role user_role;
  v_has_permission BOOLEAN;
BEGIN
  -- Buscar o papel do usuário
  SELECT role INTO v_role
  FROM profiles
  WHERE id = p_user_id;
  
  -- Verificar se tem a permissão
  SELECT EXISTS (
    SELECT 1 FROM permissions
    WHERE role = v_role AND permission = p_permission
  ) INTO v_has_permission;
  
  RETURN v_has_permission;
END;
$$ LANGUAGE plpgsql; 