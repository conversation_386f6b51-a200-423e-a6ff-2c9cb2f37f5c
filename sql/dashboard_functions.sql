-- Funções SQL para o Dashboard do Sistema Carbonário e Carbonaro
-- Otimizadas para Supabase e com suporte a RLS (Row Level Security) implícito.
-- Recomenda-se que as tabelas referenciadas (precatorios, tasks, profiles, clientes)
-- tenham políticas RLS adequadas configuradas.

-- Extensões necessárias (se não habilitadas)
-- CREATE EXTENSION IF NOT EXISTS tablefunc; -- Para CROSSTAB, se necessário no futuro.

--------------------------------------------------------------------------------
-- Função 1: Obter Métricas Gerais do Dashboard
--------------------------------------------------------------------------------
-- Descrição: Retorna métricas agregadas sobre precatórios.
-- Parâmetros:
--   user_id_param: UUID do usuário solicitante (para RLS em potencial).
--   user_role_param: Role do usuário (para lógica de permissão customizada).
--   start_date_param: Data de início para filtrar precatórios (opcional).
--   end_date_param: Data de fim para filtrar precatórios (opcional).
--   tipos_precatorio_param: Array de tipos de precatório para filtrar (opcional).
--   status_precatorio_param: Array de status de precatório para filtrar (opcional).
--   responsaveis_param: Array de UUIDs de responsáveis para filtrar (opcional).
--   valor_minimo_param: Valor mínimo do precatório para filtrar (opcional).
--   valor_maximo_param: Valor máximo do precatório para filtrar (opcional).
-- Retorna: Um registro com as métricas calculadas.

CREATE OR REPLACE FUNCTION get_dashboard_general_metrics(
    user_id_param UUID,
    user_role_param TEXT,
    start_date_param TIMESTAMPTZ DEFAULT NULL,
    end_date_param TIMESTAMPTZ DEFAULT NULL,
    tipos_precatorio_param TEXT[] DEFAULT NULL,
    status_precatorio_param TEXT[] DEFAULT NULL,
    responsaveis_param UUID[] DEFAULT NULL,
    valor_minimo_param NUMERIC DEFAULT NULL,
    valor_maximo_param NUMERIC DEFAULT NULL
)
RETURNS TABLE (
    total_precatorios BIGINT,
    valor_total_precatorios NUMERIC,
    total_precatorios_ativos BIGINT,
    novos_precatorios_periodo BIGINT -- Novos precatórios dentro do período de start_date e end_date
)
LANGUAGE plpgsql
STABLE -- A função não modifica o banco de dados e retorna os mesmos resultados para os mesmos inputs dentro de uma transação.
-- SECURITY INVOKER -- Executa com as permissões do usuário que a chama (padrão). RLS das tabelas será aplicada.
AS $$
BEGIN
    -- Validação de Parâmetros (exemplo básico)
    IF start_date_param IS NOT NULL AND end_date_param IS NOT NULL AND start_date_param > end_date_param THEN
        RAISE EXCEPTION 'Data de início não pode ser maior que a data de fim.';
    END IF;

    RETURN QUERY
    WITH filtered_precatorios AS (
        SELECT
            p.id,
            p.valor,
            p.status,
            p.created_at
        FROM
            precatorios p -- Supabase RLS é aplicada aqui automaticamente
        WHERE
            (start_date_param IS NULL OR p.created_at >= start_date_param) AND
            (end_date_param IS NULL OR p.created_at <= end_date_param) AND
            (tipos_precatorio_param IS NULL OR p.tipo = ANY(tipos_precatorio_param)) AND
            (status_precatorio_param IS NULL OR p.status = ANY(status_precatorio_param)) AND
            (responsaveis_param IS NULL OR p.responsavel_id = ANY(responsaveis_param)) AND
            (valor_minimo_param IS NULL OR p.valor >= valor_minimo_param) AND
            (valor_maximo_param IS NULL OR p.valor <= valor_maximo_param) AND
            p.status <> 'excluido' -- Exemplo de um filtro global que pode estar na RLS também
    )
    SELECT
        COUNT(fp.id) AS total_precatorios,
        SUM(fp.valor) AS valor_total_precatorios,
        COUNT(CASE WHEN fp.status NOT IN ('concluido', 'cancelado', 'arquivado', 'pago') THEN 1 END) AS total_precatorios_ativos,
        COUNT(CASE WHEN start_date_param IS NOT NULL AND end_date_param IS NOT NULL THEN 1 ELSE NULL END) AS novos_precatorios_periodo
    FROM
        filtered_precatorios fp;

EXCEPTION
    WHEN OTHERS THEN
        -- Logar o erro (ex: usando uma tabela de log ou RAISE NOTICE)
        RAISE NOTICE 'Erro em get_dashboard_general_metrics: %', SQLERRM;
        -- Retornar valores padrão em caso de erro para não quebrar a aplicação
        RETURN QUERY SELECT 0::BIGINT, 0::NUMERIC, 0::BIGINT, 0::BIGINT;
END;
$$;

COMMENT ON FUNCTION get_dashboard_general_metrics IS 'Calcula métricas gerais de precatórios para o dashboard, com filtros e tratamento de erro básico.';

--------------------------------------------------------------------------------
-- Função 2: Calcular Desempenho da Equipe
--------------------------------------------------------------------------------
-- Descrição: Retorna métricas de desempenho para membros da equipe.
-- Parâmetros:
--   user_id_param: UUID do usuário solicitante.
--   user_role_param: Role do usuário.
--   start_date_param: Data de início para filtrar atividades (opcional).
--   end_date_param: Data de fim para filtrar atividades (opcional).
-- Retorna: Tabela com dados de desempenho por usuário.

CREATE OR REPLACE FUNCTION get_dashboard_team_performance(
    requesting_user_id_param UUID,
    requesting_user_role_param TEXT,
    start_date_param TIMESTAMPTZ DEFAULT NULL,
    end_date_param TIMESTAMPTZ DEFAULT NULL
)
RETURNS TABLE (
    user_id UUID,
    nome TEXT,
    cargo TEXT,
    foto_url TEXT,
    tarefas_concluidas BIGINT,
    precatorios_processados BIGINT,
    tempo_medio_processamento_precatorio NUMERIC -- Em dias
)
LANGUAGE plpgsql
STABLE
AS $$
DECLARE
    can_view_all BOOLEAN;
BEGIN
    -- Determinar se o usuário pode ver todos os dados da equipe
    can_view_all := requesting_user_role_param IN ('admin', 'gerente_geral', 'gerente_precatorio', 'gerente_rpv');

    RETURN QUERY
    SELECT
        prof.id AS user_id,
        prof.nome,
        COALESCE(prof.cargo, prof.role) AS cargo, -- Usar cargo se disponível, senão role
        prof.foto_url,
        COUNT(DISTINCT t.id) FILTER (WHERE t.status = 'concluida' AND (start_date_param IS NULL OR t.updated_at >= start_date_param) AND (end_date_param IS NULL OR t.updated_at <= end_date_param)) AS tarefas_concluidas,
        COUNT(DISTINCT p.id) FILTER (WHERE (start_date_param IS NULL OR p.created_at >= start_date_param) AND (end_date_param IS NULL OR p.created_at <= end_date_param)) AS precatorios_processados,
        AVG(
            CASE
                WHEN p.status IN ('concluido', 'pago', 'arquivado') AND p.created_at IS NOT NULL AND p.updated_at IS NOT NULL THEN
                    EXTRACT(EPOCH FROM (p.updated_at - p.created_at)) / (60*60*24) -- Diferença em dias
                ELSE NULL
            END
        ) AS tempo_medio_processamento_precatorio
    FROM
        profiles prof
    LEFT JOIN
        tasks t ON prof.id = t.responsavel_id
    LEFT JOIN
        precatorios p ON prof.id = p.responsavel_id
    WHERE
        prof.status = 'ativo' AND -- Considerar apenas usuários ativos
        (can_view_all OR prof.id = requesting_user_id_param) -- Lógica de visibilidade
    GROUP BY
        prof.id, prof.nome, prof.cargo, prof.role, prof.foto_url
    ORDER BY
        tarefas_concluidas DESC, precatorios_processados DESC;

EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Erro em get_dashboard_team_performance: %', SQLERRM;
        RETURN QUERY SELECT NULL::UUID, NULL::TEXT, NULL::TEXT, NULL::TEXT, 0::BIGINT, 0::BIGINT, 0::NUMERIC;
END;
$$;

COMMENT ON FUNCTION get_dashboard_team_performance IS 'Calcula métricas de desempenho da equipe, respeitando permissões de visualização.';

--------------------------------------------------------------------------------
-- Função 3: Obter Dados de Evolução Temporal
--------------------------------------------------------------------------------
-- Descrição: Retorna contagens de precatórios e tarefas agrupados por período.
-- Parâmetros:
--   user_id_param: UUID do usuário solicitante.
--   user_role_param: Role do usuário.
--   start_date_param: Data de início da análise.
--   end_date_param: Data de fim da análise.
--   group_by_period: Granularidade do agrupamento ('day', 'week', 'month').
-- Retorna: Tabela com dados de evolução por período.

CREATE OR REPLACE FUNCTION get_dashboard_evolution_data(
    user_id_param UUID,
    user_role_param TEXT,
    start_date_param TIMESTAMPTZ,
    end_date_param TIMESTAMPTZ,
    group_by_period TEXT DEFAULT 'day' -- 'day', 'week', 'month'
)
RETURNS TABLE (
    periodo_inicio TIMESTAMPTZ,
    count_precatorios BIGINT,
    count_tarefas BIGINT
)
LANGUAGE plpgsql
STABLE
AS $$
BEGIN
    IF start_date_param > end_date_param THEN
        RAISE EXCEPTION 'Data de início não pode ser maior que a data de fim para evolução.';
    END IF;
    IF group_by_period NOT IN ('day', 'week', 'month') THEN
        RAISE EXCEPTION 'Período de agrupamento inválido. Use "day", "week", ou "month".';
    END IF;

    RETURN QUERY
    WITH date_series AS (
        SELECT generate_series(
            date_trunc(group_by_period, start_date_param),
            date_trunc(group_by_period, end_date_param),
            ('1 ' || group_by_period)::INTERVAL
        ) AS periodo_inicio
    )
    SELECT
        ds.periodo_inicio,
        (SELECT COUNT(p.id)
         FROM precatorios p
         WHERE date_trunc(group_by_period, p.created_at) = ds.periodo_inicio
           AND p.status <> 'excluido'
           -- Adicionar aqui filtros de RLS se a política da tabela não for suficiente
           -- Ex: AND (user_role_param = 'admin' OR p.responsavel_id = user_id_param)
        ) AS count_precatorios,
        (SELECT COUNT(t.id)
         FROM tasks t
         WHERE date_trunc(group_by_period, t.created_at) = ds.periodo_inicio
           AND t.status <> 'excluida'
           -- Adicionar aqui filtros de RLS
        ) AS count_tarefas
    FROM
        date_series ds
    ORDER BY
        ds.periodo_inicio;

EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Erro em get_dashboard_evolution_data: %', SQLERRM;
        RETURN QUERY SELECT NULL::TIMESTAMPTZ, 0::BIGINT, 0::BIGINT;
END;
$$;

COMMENT ON FUNCTION get_dashboard_evolution_data IS 'Fornece dados de evolução temporal para precatórios e tarefas.';

--------------------------------------------------------------------------------
-- Função 4: Obter Estatísticas de Distribuição (Status/Tipo)
--------------------------------------------------------------------------------
-- Descrição: Retorna a contagem de itens agrupados por uma dimensão específica.
-- Parâmetros:
--   user_id_param: UUID do usuário solicitante.
--   user_role_param: Role do usuário.
--   dimension_param: Qual dimensão agrupar ('status_precatorio', 'tipo_precatorio', 'status_tarefa').
--   start_date_param: Data de início para filtrar (opcional).
--   end_date_param: Data de fim para filtrar (opcional).
-- Retorna: Tabela com contagens por valor da dimensão.

CREATE OR REPLACE FUNCTION get_dashboard_distribution_stats(
    user_id_param UUID,
    user_role_param TEXT,
    dimension_param TEXT, -- 'status_precatorio', 'tipo_precatorio', 'status_tarefa'
    start_date_param TIMESTAMPTZ DEFAULT NULL,
    end_date_param TIMESTAMPTZ DEFAULT NULL
)
RETURNS TABLE (
    dimension_value TEXT,
    count_items BIGINT
)
LANGUAGE plpgsql
STABLE
AS $$
BEGIN
    IF dimension_param = 'status_precatorio' THEN
        RETURN QUERY
        SELECT
            p.status AS dimension_value,
            COUNT(p.id) AS count_items
        FROM
            precatorios p
        WHERE
            (start_date_param IS NULL OR p.created_at >= start_date_param) AND
            (end_date_param IS NULL OR p.created_at <= end_date_param) AND
            p.status <> 'excluido'
            -- RLS da tabela precatorios é aplicada
        GROUP BY
            p.status
        ORDER BY
            count_items DESC;
    ELSIF dimension_param = 'tipo_precatorio' THEN
        RETURN QUERY
        SELECT
            p.tipo AS dimension_value,
            COUNT(p.id) AS count_items
        FROM
            precatorios p
        WHERE
            (start_date_param IS NULL OR p.created_at >= start_date_param) AND
            (end_date_param IS NULL OR p.created_at <= end_date_param) AND
            p.status <> 'excluido'
            -- RLS da tabela precatorios é aplicada
        GROUP BY
            p.tipo
        ORDER BY
            count_items DESC;
    ELSIF dimension_param = 'status_tarefa' THEN
        RETURN QUERY
        SELECT
            t.status AS dimension_value,
            COUNT(t.id) AS count_items
        FROM
            tasks t
        WHERE
            (start_date_param IS NULL OR t.created_at >= start_date_param) AND
            (end_date_param IS NULL OR t.created_at <= end_date_param) AND
            t.status <> 'excluida'
            -- RLS da tabela tasks é aplicada
        GROUP BY
            t.status
        ORDER BY
            count_items DESC;
    ELSE
        RAISE EXCEPTION 'Dimensão inválida: %. Use "status_precatorio", "tipo_precatorio", ou "status_tarefa".', dimension_param;
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Erro em get_dashboard_distribution_stats para dimensão %: %', dimension_param, SQLERRM;
        RETURN QUERY SELECT NULL::TEXT, 0::BIGINT;
END;
$$;

COMMENT ON FUNCTION get_dashboard_distribution_stats IS 'Calcula distribuição de itens (precatórios, tarefas) por status ou tipo.';

--------------------------------------------------------------------------------
-- Função 5: Obter Estatísticas de Tarefas
--------------------------------------------------------------------------------
-- Descrição: Retorna métricas agregadas sobre tarefas.
-- Parâmetros:
--   user_id_param: UUID do usuário solicitante.
--   user_role_param: Role do usuário.
--   start_date_param: Data de início para filtrar tarefas (opcional).
--   end_date_param: Data de fim para filtrar tarefas (opcional).
--   responsaveis_param: Array de UUIDs de responsáveis para filtrar (opcional).
-- Retorna: Um registro com as métricas de tarefas.

CREATE OR REPLACE FUNCTION get_dashboard_tasks_stats(
    user_id_param UUID,
    user_role_param TEXT,
    start_date_param TIMESTAMPTZ DEFAULT NULL,
    end_date_param TIMESTAMPTZ DEFAULT NULL,
    responsaveis_param UUID[] DEFAULT NULL
)
RETURNS TABLE (
    total_tarefas BIGINT,
    tarefas_pendentes BIGINT,
    tarefas_concluidas BIGINT,
    tarefas_atrasadas BIGINT -- Tarefas com prazo vencido e não concluídas
)
LANGUAGE plpgsql
STABLE
AS $$
BEGIN
    RETURN QUERY
    WITH filtered_tasks AS (
        SELECT
            t.id,
            t.status,
            t.prazo_final
        FROM
            tasks t -- RLS da tabela tasks é aplicada
        WHERE
            (start_date_param IS NULL OR t.created_at >= start_date_param) AND
            (end_date_param IS NULL OR t.created_at <= end_date_param) AND
            (responsaveis_param IS NULL OR t.responsavel_id = ANY(responsaveis_param)) AND
            t.status <> 'excluida'
    )
    SELECT
        COUNT(ft.id) AS total_tarefas,
        COUNT(CASE WHEN ft.status NOT IN ('concluida', 'cancelada') THEN 1 END) AS tarefas_pendentes,
        COUNT(CASE WHEN ft.status = 'concluida' THEN 1 END) AS tarefas_concluidas,
        COUNT(CASE WHEN ft.prazo_final < NOW() AND ft.status NOT IN ('concluida', 'cancelada') THEN 1 END) AS tarefas_atrasadas
    FROM
        filtered_tasks ft;

EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Erro em get_dashboard_tasks_stats: %', SQLERRM;
        RETURN QUERY SELECT 0::BIGINT, 0::BIGINT, 0::BIGINT, 0::BIGINT;
END;
$$;

COMMENT ON FUNCTION get_dashboard_tasks_stats IS 'Calcula métricas gerais de tarefas para o dashboard.';

--------------------------------------------------------------------------------
-- Sugestões de Índices para Otimização (a serem criados separadamente)
--------------------------------------------------------------------------------
/*
-- Tabela 'precatorios':
CREATE INDEX IF NOT EXISTS idx_precatorios_status ON precatorios(status);
CREATE INDEX IF NOT EXISTS idx_precatorios_tipo ON precatorios(tipo);
CREATE INDEX IF NOT EXISTS idx_precatorios_responsavel_id ON precatorios(responsavel_id);
CREATE INDEX IF NOT EXISTS idx_precatorios_created_at ON precatorios(created_at);
CREATE INDEX IF NOT EXISTS idx_precatorios_updated_at ON precatorios(updated_at);
CREATE INDEX IF NOT EXISTS idx_precatorios_valor ON precatorios(valor);

-- Tabela 'tasks':
CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks(status);
CREATE INDEX IF NOT EXISTS idx_tasks_responsavel_id ON tasks(responsavel_id);
CREATE INDEX IF NOT EXISTS idx_tasks_created_at ON tasks(created_at);
CREATE INDEX IF NOT EXISTS idx_tasks_updated_at ON tasks(updated_at);
CREATE INDEX IF NOT EXISTS idx_tasks_prazo_final ON tasks(prazo_final);

-- Tabela 'profiles':
CREATE INDEX IF NOT EXISTS idx_profiles_status ON profiles(status); -- Se 'status' for usado em filtros
CREATE INDEX IF NOT EXISTS idx_profiles_role ON profiles(role); -- Se 'role' for usado em filtros
CREATE INDEX IF NOT EXISTS idx_profiles_cargo ON profiles(cargo); -- Se 'cargo' for usado em filtros

-- Tabela 'clientes': (Se usada em funções futuras do dashboard)
CREATE INDEX IF NOT EXISTS idx_clientes_status ON clientes(status);
CREATE INDEX IF NOT EXISTS idx_clientes_created_at ON clientes(created_at);
*/

-- Exemplo de como chamar as funções (para teste):
/*
SELECT * FROM get_dashboard_general_metrics(
    '00000000-0000-0000-0000-000000000000', -- Substituir por um UUID de usuário válido
    'admin', -- Role do usuário
    '2024-01-01T00:00:00Z',
    '2024-12-31T23:59:59Z'
);

SELECT * FROM get_dashboard_team_performance(
    '00000000-0000-0000-0000-000000000000',
    'admin'
);

SELECT * FROM get_dashboard_evolution_data(
    '00000000-0000-0000-0000-000000000000',
    'admin',
    '2024-01-01T00:00:00Z',
    '2024-03-31T23:59:59Z',
    'month'
);

SELECT * FROM get_dashboard_distribution_stats(
    '00000000-0000-0000-0000-000000000000',
    'admin',
    'status_precatorio'
);

SELECT * FROM get_dashboard_tasks_stats(
    '00000000-0000-0000-0000-000000000000',
    'admin'
);
*/
