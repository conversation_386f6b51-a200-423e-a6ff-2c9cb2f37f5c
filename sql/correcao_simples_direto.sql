-- Script simplificado para contornar o problema de recursão
-- Este script deve ser executado em cada parte separadamente no Supabase

-- PARTE 1: Desativar temporariamente RLS
ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;

-- PARTE 2: Remover TODAS as políticas existentes
DROP POLICY IF EXISTS "Acesso universal de leitura" ON profiles;
DROP POLICY IF EXISTS "Acesso universal de atualização" ON profiles;
DROP POLICY IF EXISTS "Usuários podem ver seus próprios perfis" ON profiles;
DROP POLICY IF EXISTS "Administradores podem ver todos os perfis" ON profiles;
DROP POLICY IF EXISTS "Usuários podem atualizar seus próprios perfis" ON profiles;
DROP POLICY IF EXISTS "Administradores podem atualizar todos os perfis" ON profiles;
DROP POLICY IF EXISTS "Usuários podem excluir seus próprios perfis" ON profiles;
DROP POLICY IF EXISTS "Administradores podem excluir todos os perfis" ON profiles;
DROP POLICY IF EXISTS "Usuários podem inserir seus próprios perfis" ON profiles;
DROP POLICY IF EXISTS "Administradores podem inserir todos os perfis" ON profiles;

-- PARTE 3: Criar políticas simples
CREATE POLICY "Acesso universal de leitura"
ON profiles FOR SELECT
USING (true);

CREATE POLICY "Acesso universal de atualização"
ON profiles FOR UPDATE
USING (true);

CREATE POLICY "Acesso universal de inserção"
ON profiles FOR INSERT
WITH CHECK (true);

CREATE POLICY "Acesso universal de exclusão"
ON profiles FOR DELETE
USING (true);

-- PARTE 4: Reativar RLS
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- PARTE 5: Garantir permissões
GRANT ALL ON profiles TO authenticated;
