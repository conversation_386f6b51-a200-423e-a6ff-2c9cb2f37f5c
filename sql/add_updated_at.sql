-- Script para adicionar a coluna updated_at na tabela profiles

-- Verificar se a coluna já existe
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'profiles'
    AND column_name = 'updated_at'
  ) THEN
    -- Adicionar a coluna updated_at
    ALTER TABLE public.profiles 
    ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
    
    -- Definir o valor inicial para ser igual ao created_at
    UPDATE public.profiles 
    SET updated_at = created_at 
    WHERE updated_at IS NULL;
    
    RAISE NOTICE 'Coluna updated_at adicionada com sucesso à tabela profiles';
  ELSE
    RAISE NOTICE 'A coluna updated_at já existe na tabela profiles';
  END IF;
END $$; 