-- Funções RPC para perfil de usuário
-- Execute este script no SQL Editor do Supabase

-- Função para buscar perfil de usuário de forma segura
CREATE OR REPLACE FUNCTION public.get_user_profile_safe(target_user_id UUID)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  current_user_id UUID := auth.uid();
  current_user_role TEXT;
  profile_data JSON;
BEGIN
  -- Verificar se o usuário está autenticado
  IF current_user_id IS NULL THEN
    RAISE EXCEPTION 'Usuário não autenticado';
  END IF;

  -- Buscar role do usuário atual
  SELECT role INTO current_user_role 
  FROM profiles 
  WHERE id = current_user_id;

  -- Verificar permissões: usuário pode ver próprio perfil ou admin pode ver qualquer perfil
  IF target_user_id != current_user_id AND current_user_role != 'admin' THEN
    RAISE EXCEPTION 'Acesso negado: você não tem permissão para visualizar este perfil';
  END IF;

  -- Buscar dados do perfil
  SELECT json_build_object(
    'id', p.id,
    'email', au.email,
    'nome', p.nome,
    'role', p.role,
    'cargo', p.cargo,
    'departamento', p.departamento,
    'foto_url', p.foto_url,
    'data_entrada', p.data_entrada,
    'status', p.status,
    'telefone', p.telefone,
    'created_at', p.created_at,
    'updated_at', p.updated_at,
    'custom_role_id', p.custom_role_id
  ) INTO profile_data
  FROM profiles p
  JOIN auth.users au ON p.id = au.id
  WHERE p.id = target_user_id;

  RETURN profile_data;
END;
$$;

-- Função para buscar métricas de usuário de forma segura
CREATE OR REPLACE FUNCTION public.get_user_metrics_safe(target_user_id UUID)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  current_user_id UUID := auth.uid();
  current_user_role TEXT;
  metrics_data JSON;
  total_precatorios INTEGER := 0;
  precatorios_concluidos INTEGER := 0;
  precatorios_em_andamento INTEGER := 0;
  total_clientes INTEGER := 0;
  clientes_ativos INTEGER := 0;
  total_tarefas INTEGER := 0;
  tarefas_concluidas INTEGER := 0;
  tarefas_pendentes INTEGER := 0;
  valor_total_precatorios NUMERIC := 0;
BEGIN
  -- Verificar se o usuário está autenticado
  IF current_user_id IS NULL THEN
    RAISE EXCEPTION 'Usuário não autenticado';
  END IF;

  -- Buscar role do usuário atual
  SELECT role INTO current_user_role 
  FROM profiles 
  WHERE id = current_user_id;

  -- Verificar permissões: usuário pode ver próprias métricas ou admin pode ver qualquer métrica
  IF target_user_id != current_user_id AND current_user_role != 'admin' THEN
    RAISE EXCEPTION 'Acesso negado: você não tem permissão para visualizar estas métricas';
  END IF;

  -- Calcular métricas de precatórios
  SELECT 
    COUNT(*),
    COUNT(*) FILTER (WHERE sp.nome IN ('concluido', 'finalizado')),
    COUNT(*) FILTER (WHERE sp.nome IN ('em_andamento', 'processando')),
    COALESCE(SUM(p.valor_total), 0)
  INTO 
    total_precatorios,
    precatorios_concluidos,
    precatorios_em_andamento,
    valor_total_precatorios
  FROM precatorios p
  LEFT JOIN status_precatorios sp ON p.status_id = sp.id
  WHERE (p.responsavel_id = target_user_id OR p.created_by = target_user_id)
    AND p.is_deleted = false;

  -- Calcular métricas de clientes
  SELECT 
    COUNT(*),
    COUNT(*) FILTER (WHERE status = 'ativo')
  INTO 
    total_clientes,
    clientes_ativos
  FROM clientes
  WHERE responsavel_id = target_user_id
    AND is_deleted = false;

  -- Calcular métricas de tarefas
  SELECT 
    COUNT(*),
    COUNT(*) FILTER (WHERE status IN ('completed', 'done', 'concluida')),
    COUNT(*) FILTER (WHERE status IN ('pending', 'todo', 'pendente'))
  INTO 
    total_tarefas,
    tarefas_concluidas,
    tarefas_pendentes
  FROM tasks
  WHERE (assignee_id = target_user_id OR created_by = target_user_id);

  -- Construir JSON de resposta
  SELECT json_build_object(
    'totalPrecatorios', total_precatorios,
    'precatoriosConcluidos', precatorios_concluidos,
    'precatoriosEmAndamento', precatorios_em_andamento,
    'totalClientes', total_clientes,
    'clientesAtivos', clientes_ativos,
    'totalTarefas', total_tarefas,
    'tarefasConcluidas', tarefas_concluidas,
    'tarefasPendentes', tarefas_pendentes,
    'valorTotalPrecatorios', valor_total_precatorios,
    'metasMensais', json_build_object(
      'precatorios', json_build_object(
        'atual', precatorios_concluidos,
        'meta', 50,
        'progresso', CASE 
          WHEN 50 > 0 THEN ROUND((precatorios_concluidos::NUMERIC / 50) * 100, 2)
          ELSE 0 
        END
      ),
      'clientes', json_build_object(
        'atual', clientes_ativos,
        'meta', 30,
        'progresso', CASE 
          WHEN 30 > 0 THEN ROUND((clientes_ativos::NUMERIC / 30) * 100, 2)
          ELSE 0 
        END
      ),
      'faturamento', json_build_object(
        'atual', 'R$ ' || TO_CHAR(valor_total_precatorios, 'FM999G999G999D00'),
        'meta', 'R$ 200.000,00',
        'progresso', CASE 
          WHEN 200000 > 0 THEN ROUND((valor_total_precatorios / 200000) * 100, 2)
          ELSE 0 
        END
      )
    )
  ) INTO metrics_data;

  RETURN metrics_data;
END;
$$;

-- Função para buscar dados de performance mensal do usuário
CREATE OR REPLACE FUNCTION public.get_user_performance_monthly(target_user_id UUID)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  current_user_id UUID := auth.uid();
  current_user_role TEXT;
  performance_data JSON;
  monthly_data JSON[];
  month_record RECORD;
BEGIN
  -- Verificar se o usuário está autenticado
  IF current_user_id IS NULL THEN
    RAISE EXCEPTION 'Usuário não autenticado';
  END IF;

  -- Buscar role do usuário atual
  SELECT role INTO current_user_role 
  FROM profiles 
  WHERE id = current_user_id;

  -- Verificar permissões
  IF target_user_id != current_user_id AND current_user_role != 'admin' THEN
    RAISE EXCEPTION 'Acesso negado: você não tem permissão para visualizar estes dados';
  END IF;

  -- Gerar dados dos últimos 6 meses
  monthly_data := ARRAY[]::JSON[];
  
  FOR i IN 0..5 LOOP
    DECLARE
      month_start DATE := DATE_TRUNC('month', CURRENT_DATE - INTERVAL '1 month' * i)::DATE;
      month_end DATE := (DATE_TRUNC('month', CURRENT_DATE - INTERVAL '1 month' * i) + INTERVAL '1 month' - INTERVAL '1 day')::DATE;
      month_name TEXT := TO_CHAR(month_start, 'Mon');
      precatorios_count INTEGER := 0;
      clientes_count INTEGER := 0;
      tarefas_count INTEGER := 0;
    BEGIN
      -- Contar precatórios do mês
      SELECT COUNT(*) INTO precatorios_count
      FROM precatorios p
      WHERE (p.responsavel_id = target_user_id OR p.created_by = target_user_id)
        AND p.created_at::DATE BETWEEN month_start AND month_end
        AND p.is_deleted = false;

      -- Contar clientes do mês
      SELECT COUNT(*) INTO clientes_count
      FROM clientes c
      WHERE c.responsavel_id = target_user_id
        AND c.created_at::DATE BETWEEN month_start AND month_end
        AND c.is_deleted = false;

      -- Contar tarefas do mês
      SELECT COUNT(*) INTO tarefas_count
      FROM tasks t
      WHERE (t.assignee_id = target_user_id OR t.created_by = target_user_id)
        AND t.created_at::DATE BETWEEN month_start AND month_end;

      -- Adicionar ao array
      monthly_data := monthly_data || json_build_object(
        'mes', month_name,
        'precatorios', precatorios_count,
        'clientes', clientes_count,
        'meta', 15,
        'tarefas', tarefas_count
      );
    END;
  END LOOP;

  -- Reverter array para ordem cronológica
  SELECT json_agg(elem ORDER BY (elem->>'mes')) INTO performance_data
  FROM unnest(array_reverse(monthly_data)) AS elem;

  RETURN performance_data;
END;
$$;

-- Conceder permissões para usuários autenticados
GRANT EXECUTE ON FUNCTION public.get_user_profile_safe(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_metrics_safe(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_performance_monthly(UUID) TO authenticated;
