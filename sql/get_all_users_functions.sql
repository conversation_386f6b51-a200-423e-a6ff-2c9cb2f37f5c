-- Função para obter informações básicas de todos os usuários
-- Esta função evita a recursão infinita nas políticas de segurança
CREATE OR REPLACE FUNCTION public.get_all_users_basic_info()
RETURNS SETOF json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT json_build_object(
    'id', auth.users.id,
    'email', auth.users.email,
    'nome', profiles.nome,
    'role', profiles.role,
    'status', profiles.status,
    'foto_url', profiles.foto_url,
    'created_at', profiles.created_at
  )
  FROM auth.users
  LEFT JOIN public.profiles ON auth.users.id = profiles.id
  LIMIT 100;
END;
$$;

-- Função para obter perfis de usuários por IDs
-- Esta função evita a recursão infinita nas políticas de segurança
CREATE OR REPLACE FUNCTION public.get_user_profiles_by_ids(user_ids uuid[])
RETURNS SETOF json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT json_build_object(
    'id', profiles.id,
    'email', auth.users.email,
    'nome', profiles.nome,
    'role', profiles.role,
    'status', profiles.status,
    'foto_url', profiles.foto_url,
    'created_at', profiles.created_at
  )
  FROM public.profiles
  JOIN auth.users ON profiles.id = auth.users.id
  WHERE profiles.id = ANY(user_ids);
END;
$$;

-- Comentários sobre as funções:
-- 1. Usamos SECURITY DEFINER para que as funções sejam executadas com as permissões do criador
--    e não do chamador, evitando assim as políticas de segurança que causam recursão infinita
-- 2. Retornamos JSON para facilitar o processamento no frontend
-- 3. Limitamos a 100 usuários para evitar sobrecarga
-- 4. A segunda função aceita um array de UUIDs para buscar vários perfis de uma vez
