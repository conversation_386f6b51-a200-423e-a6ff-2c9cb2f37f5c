-- Função para atualizar ou criar perfil de usuário
-- Esta função evita a recursão infinita nas políticas de segurança
CREATE OR REPLACE FUNCTION public.atualizar_perfil_usuario(
  usuario_id uuid,
  usuario_nome text,
  usuario_role text,
  usuario_status text
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Verificar se o perfil já existe
  IF EXISTS (SELECT 1 FROM public.profiles WHERE id = usuario_id) THEN
    -- Atualizar perfil existente
    UPDATE public.profiles 
    SET 
      nome = usuario_nome,
      role = usuario_role,
      status = usuario_status,
      updated_at = NOW()
    WHERE id = usuario_id;
  ELSE
    -- Criar novo perfil
    INSERT INTO public.profiles (id, nome, role, status, created_at, updated_at)
    VALUES (usuario_id, usuario_nome, usuario_role, usuario_status, NOW(), NOW());
  END IF;
END;
$$;
