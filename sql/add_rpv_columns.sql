-- <PERSON>ript para adicionar campos relacionados a RPV na tabela de precatórios

-- 1. Verificar se a tabela precatorios existe
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = 'precatorios'
  ) THEN
    RAISE EXCEPTION 'A tabela precatorios não existe';
  END IF;
END $$;

-- 2. Adicionar campo para indicar se é Precatório ou RPV
ALTER TABLE precatorios
  ADD COLUMN IF NOT EXISTS tipo VARCHAR(20) CHECK (tipo IN ('PRECATORIO', 'RPV')) DEFAULT 'PRECATORIO';

-- 3. Adicionar campos específicos para RPV
ALTER TABLE precatorios
  ADD COLUMN IF NOT EXISTS valor_limite_rpv DECIMAL(15,2),
  ADD COLUMN IF NOT EXISTS data_deposito DATE,
  ADD COLUMN IF NOT EXISTS data_saque DATE,
  ADD COLUMN IF NOT EXISTS data_transferencia DATE,
  ADD COLUMN IF NOT EXISTS banco_deposito VARCHAR(100),
  ADD COLUMN IF NOT EXISTS agencia_deposito VARCHAR(20),
  ADD COLUMN IF NOT EXISTS conta_deposito VARCHAR(20),
  ADD COLUMN IF NOT EXISTS foi_sacado BOOLEAN DEFAULT FALSE,
  ADD COLUMN IF NOT EXISTS foi_transferido BOOLEAN DEFAULT FALSE;

-- 4. Adicionar campos para rastreamento de criação/atualização
ALTER TABLE precatorios
  ADD COLUMN IF NOT EXISTS criado_por UUID REFERENCES profiles(id),
  ADD COLUMN IF NOT EXISTS atualizado_por UUID REFERENCES profiles(id),
  ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE;

-- 5. Configurar trigger para atualizar o campo updated_at automaticamente
CREATE OR REPLACE FUNCTION update_precatorios_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 6. Criar trigger se não existir
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_trigger WHERE tgname = 'set_precatorios_updated_at'
  ) THEN
    CREATE TRIGGER set_precatorios_updated_at
    BEFORE UPDATE ON precatorios
    FOR EACH ROW
    EXECUTE FUNCTION update_precatorios_updated_at();
  END IF;
END $$;

-- 7. Criar view para facilitar consultas de RPVs
CREATE OR REPLACE VIEW rpvs AS
SELECT *
FROM precatorios
WHERE tipo = 'RPV';

-- 8. Criar view para facilitar consultas de Precatórios (sem RPVs)
CREATE OR REPLACE VIEW precatorios_sem_rpv AS
SELECT *
FROM precatorios
WHERE tipo = 'PRECATORIO' OR tipo IS NULL;

-- 9. Atualizar campo tipo para PRECATORIO em registros existentes
UPDATE precatorios
SET tipo = 'PRECATORIO'
WHERE tipo IS NULL; 