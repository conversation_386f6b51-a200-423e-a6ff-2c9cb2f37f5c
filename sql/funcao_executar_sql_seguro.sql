-- Criar função para executar comandos SQL diretamente
-- Esta função permite contornar o RLS para atualizações específicas

CREATE OR REPLACE FUNCTION executar_sql_seguro(comando text)
RETURNS JSONB
SECURITY DEFINER -- Executa com as permissões do criador da função
AS $$
DECLARE
  resultado JSONB;
BEGIN
  -- Executar o comando SQL
  EXECUTE comando;
  
  -- Retornar sucesso
  resultado := jsonb_build_object('sucesso', true, 'mensagem', 'Comando executado com sucesso');
  
  RETURN resultado;
EXCEPTION WHEN OTHERS THEN
  -- Capturar qualquer erro e retornar informações
  resultado := jsonb_build_object(
    'sucesso', false,
    'erro', SQLERRM,
    'codigo', SQLSTATE
  );
  
  RETURN resultado;
END;
$$ LANGUAGE plpgsql;

-- Conceder acesso à função para usuários autenticados
GRANT EXECUTE ON FUNCTION executar_sql_seguro TO authenticated;
GRANT EXECUTE ON FUNCTION executar_sql_seguro TO service_role;
GRANT EXECUTE ON FUNCTION executar_sql_seguro TO anon;
