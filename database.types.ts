export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      curriculum_adaptations: {
        Row: {
          adaptation_date: string | null
          adaptation_notes: string | null
          adaptation_reason: string
          added_topics: Json | null
          created_at: string | null
          id: string
          modified_topics: Json | null
          new_difficulty: string | null
          previous_difficulty: string | null
          removed_topics: Json | null
          schedule_id: string
          updated_at: string | null
        }
        Insert: {
          adaptation_date?: string | null
          adaptation_notes?: string | null
          adaptation_reason: string
          added_topics?: Json | null
          created_at?: string | null
          id?: string
          modified_topics?: Json | null
          new_difficulty?: string | null
          previous_difficulty?: string | null
          removed_topics?: Json | null
          schedule_id: string
          updated_at?: string | null
        }
        Update: {
          adaptation_date?: string | null
          adaptation_notes?: string | null
          adaptation_reason?: string
          added_topics?: Json | null
          created_at?: string | null
          id?: string
          modified_topics?: Json | null
          new_difficulty?: string | null
          previous_difficulty?: string | null
          removed_topics?: Json | null
          schedule_id?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      proficiency_assessments: {
        Row: {
          assessment_date: string | null
          assessment_notes: string | null
          created_at: string | null
          grammar_level: number | null
          id: string
          language: string
          listening_level: number | null
          overall_level: string
          profile_id: string
          reading_level: number | null
          speaking_level: number | null
          updated_at: string | null
          vocabulary_level: number | null
          writing_level: number | null
        }
        Insert: {
          assessment_date?: string | null
          assessment_notes?: string | null
          created_at?: string | null
          grammar_level?: number | null
          id?: string
          language: string
          listening_level?: number | null
          overall_level: string
          profile_id: string
          reading_level?: number | null
          speaking_level?: number | null
          updated_at?: string | null
          vocabulary_level?: number | null
          writing_level?: number | null
        }
        Update: {
          assessment_date?: string | null
          assessment_notes?: string | null
          created_at?: string | null
          grammar_level?: number | null
          id?: string
          language?: string
          listening_level?: number | null
          overall_level?: string
          profile_id?: string
          reading_level?: number | null
          speaking_level?: number | null
          updated_at?: string | null
          vocabulary_level?: number | null
          writing_level?: number | null
        }
        Relationships: []
      }
      profiles: {
        Row: {
          age_range: string | null
          created_at: string | null
          id: string
          is_onboarding_complete: boolean | null
          is_subscribed: boolean | null
          language_level: string | null
          last_interaction: string | null
          name: string | null
          occupation: string | null
          onboarding_completed_at: string | null
          onboarding_step: string | null
          phone_number: string
          preferred_days_of_week: number[] | null
          preferred_study_duration: number | null
          preferred_study_time: string[] | null
          stripe_customer_id: string | null
          subscription_end_date: string | null
          subscription_id: string | null
          subscription_status: boolean | null
          target_language: string | null
          updated_at: string | null
        }
        Insert: {
          age_range?: string | null
          created_at?: string | null
          id?: string
          is_onboarding_complete?: boolean | null
          is_subscribed?: boolean | null
          language_level?: string | null
          last_interaction?: string | null
          name?: string | null
          occupation?: string | null
          onboarding_completed_at?: string | null
          onboarding_step?: string | null
          phone_number: string
          preferred_days_of_week?: number[] | null
          preferred_study_duration?: number | null
          preferred_study_time?: string[] | null
          stripe_customer_id?: string | null
          subscription_end_date?: string | null
          subscription_id?: string | null
          subscription_status?: boolean | null
          target_language?: string | null
          updated_at?: string | null
        }
        Update: {
          age_range?: string | null
          created_at?: string | null
          id?: string
          is_onboarding_complete?: boolean | null
          is_subscribed?: boolean | null
          language_level?: string | null
          last_interaction?: string | null
          name?: string | null
          occupation?: string | null
          onboarding_completed_at?: string | null
          onboarding_step?: string | null
          phone_number?: string
          preferred_days_of_week?: number[] | null
          preferred_study_duration?: number | null
          preferred_study_time?: string[] | null
          stripe_customer_id?: string | null
          subscription_end_date?: string | null
          subscription_id?: string | null
          subscription_status?: boolean | null
          target_language?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      reminders: {
        Row: {
          content: string
          id: string
          metadata: Json | null
          profile_id: string
          reminder_date: string
          sent_at: string | null
          topic_id: string | null
          type: string
        }
        Insert: {
          content: string
          id?: string
          metadata?: Json | null
          profile_id: string
          reminder_date: string
          sent_at?: string | null
          topic_id?: string | null
          type: string
        }
        Update: {
          content?: string
          id?: string
          metadata?: Json | null
          profile_id?: string
          reminder_date?: string
          sent_at?: string | null
          topic_id?: string | null
          type?: string
        }
        Relationships: [
          {
            foreignKeyName: "reminders_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "reminders_topic_id_fkey"
            columns: ["topic_id"]
            isOneToOne: false
            referencedRelation: "study_topics"
            referencedColumns: ["id"]
          },
        ]
      }
      revision_contents: {
        Row: {
          completed_at: string | null
          content: string
          creation_date: string | null
          difficulty_level: string
          exercises: string | null
          feedback: string | null
          id: string
          is_completed: boolean | null
          profile_id: string
          resources: string | null
          review_type: string
          topic_id: string | null
          updated_at: string | null
        }
        Insert: {
          completed_at?: string | null
          content: string
          creation_date?: string | null
          difficulty_level: string
          exercises?: string | null
          feedback?: string | null
          id?: string
          is_completed?: boolean | null
          profile_id: string
          resources?: string | null
          review_type: string
          topic_id?: string | null
          updated_at?: string | null
        }
        Update: {
          completed_at?: string | null
          content?: string
          creation_date?: string | null
          difficulty_level?: string
          exercises?: string | null
          feedback?: string | null
          id?: string
          is_completed?: boolean | null
          profile_id?: string
          resources?: string | null
          review_type?: string
          topic_id?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      stripe_events: {
        Row: {
          created_at: string | null
          event_data: Json
          event_type: string
          id: string
          processed_at: string | null
        }
        Insert: {
          created_at?: string | null
          event_data: Json
          event_type: string
          id: string
          processed_at?: string | null
        }
        Update: {
          created_at?: string | null
          event_data?: Json
          event_type?: string
          id?: string
          processed_at?: string | null
        }
        Relationships: []
      }
      study_reminders: {
        Row: {
          created_at: string | null
          days_of_week: number[]
          id: string
          is_active: boolean | null
          profile_id: string | null
          time: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          days_of_week: number[]
          id?: string
          is_active?: boolean | null
          profile_id?: string | null
          time: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          days_of_week?: number[]
          id?: string
          is_active?: boolean | null
          profile_id?: string | null
          time?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "study_reminders_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      study_schedules: {
        Row: {
          created_at: string | null
          days_of_week: number[] | null
          difficulty_level: string | null
          end_date: string
          id: string
          is_active: boolean | null
          minutes_per_session: number | null
          preferred_study_time: string[] | null
          profile_id: string | null
          schedule_type: string
          start_date: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          days_of_week?: number[] | null
          difficulty_level?: string | null
          end_date: string
          id?: string
          is_active?: boolean | null
          minutes_per_session?: number | null
          preferred_study_time?: string[] | null
          profile_id?: string | null
          schedule_type: string
          start_date: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          days_of_week?: number[] | null
          difficulty_level?: string | null
          end_date?: string
          id?: string
          is_active?: boolean | null
          minutes_per_session?: number | null
          preferred_study_time?: string[] | null
          profile_id?: string | null
          schedule_type?: string
          start_date?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "study_schedules_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      study_topics: {
        Row: {
          completed: boolean | null
          completion_date: string | null
          created_at: string | null
          exercises: Json | null
          id: string
          main_topic: string
          notes: string | null
          resources: Json | null
          schedule_id: string | null
          subtopics: Json
          topic_date: string
          updated_at: string | null
        }
        Insert: {
          completed?: boolean | null
          completion_date?: string | null
          created_at?: string | null
          exercises?: Json | null
          id?: string
          main_topic: string
          notes?: string | null
          resources?: Json | null
          schedule_id?: string | null
          subtopics: Json
          topic_date: string
          updated_at?: string | null
        }
        Update: {
          completed?: boolean | null
          completion_date?: string | null
          created_at?: string | null
          exercises?: Json | null
          id?: string
          main_topic?: string
          notes?: string | null
          resources?: Json | null
          schedule_id?: string | null
          subtopics?: Json
          topic_date?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "study_topics_schedule_id_fkey"
            columns: ["schedule_id"]
            isOneToOne: false
            referencedRelation: "study_schedules"
            referencedColumns: ["id"]
          },
        ]
      }
      user_memories: {
        Row: {
          content: string
          created_at: string | null
          id: string
          importance: number
          last_accessed: string | null
          memory_type: string
          metadata: Json | null
          profile_id: string
        }
        Insert: {
          content: string
          created_at?: string | null
          id?: string
          importance?: number
          last_accessed?: string | null
          memory_type: string
          metadata?: Json | null
          profile_id: string
        }
        Update: {
          content?: string
          created_at?: string | null
          id?: string
          importance?: number
          last_accessed?: string | null
          memory_type?: string
          metadata?: Json | null
          profile_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_memories_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      create_study_schedule: {
        Args: { schedule: Json } | { schedule: Json }
        Returns: {
          created_at: string | null
          days_of_week: number[] | null
          difficulty_level: string | null
          end_date: string
          id: string
          is_active: boolean | null
          minutes_per_session: number | null
          preferred_study_time: string[] | null
          profile_id: string | null
          schedule_type: string
          start_date: string
          updated_at: string | null
        }[]
      }
      get_daily_schedule: {
        Args: { p_profile_id: string; p_date: string }
        Returns: {
          schedule_id: string
          topic_id: string
          topic_date: string
          main_topic: string
          subtopics: Json
          resources: Json
          exercises: Json
          completed: boolean
          preferred_study_time: string[]
          minutes_per_session: number
          progress_minutes_studied: number
          progress_completed_exercises: number
          progress_notes: string
          progress_mood: string
        }[]
      }
      update_profile: {
        Args:
          | {
              p_phone_number: string
              p_name?: string
              p_language_level?: string
              p_target_language?: string
              p_age_range?: string
              p_occupation?: string
              p_is_onboarding_complete?: boolean
              p_preferred_study_time?: string[]
              p_preferred_days_of_week?: number[]
              p_preferred_study_duration?: number
              p_onboarding_step?: string
            }
          | {
              p_phone_number: string
              p_name?: string
              p_language_level?: string
              p_target_language?: string
              p_learning_goals?: string
              p_preferred_schedule?: string
              p_age_range?: string
              p_occupation?: string
              p_interests?: string[]
              p_is_onboarding_complete?: boolean
            }
        Returns: {
          age_range: string | null
          created_at: string | null
          id: string
          is_onboarding_complete: boolean | null
          is_subscribed: boolean | null
          language_level: string | null
          last_interaction: string | null
          name: string | null
          occupation: string | null
          onboarding_completed_at: string | null
          onboarding_step: string | null
          phone_number: string
          preferred_days_of_week: number[] | null
          preferred_study_duration: number | null
          preferred_study_time: string[] | null
          stripe_customer_id: string | null
          subscription_end_date: string | null
          subscription_id: string | null
          subscription_status: boolean | null
          target_language: string | null
          updated_at: string | null
        }[]
      }
      update_study_topic: {
        Args: { p_topic_id: string; p_updates: Json }
        Returns: {
          completed: boolean | null
          completion_date: string | null
          created_at: string | null
          exercises: Json | null
          id: string
          main_topic: string
          notes: string | null
          resources: Json | null
          schedule_id: string | null
          subtopics: Json
          topic_date: string
          updated_at: string | null
        }
      }
      verificar_assinatura_stripe: {
        Args: { cliente_id: string }
        Returns: boolean
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
