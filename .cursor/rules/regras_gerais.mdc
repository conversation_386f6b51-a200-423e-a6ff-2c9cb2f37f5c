---
description:
globs:
alwaysApply: true
---
# Regras de Consulta ao Banco e Geração de Componentes

Este documento descreve as convenções para consultar dados do banco e criar novos componentes de página ou visualização.

## Consulta de Dados

1.  **Serviços Dedicados**: Todas as consultas ao banco de dados devem ser encapsuladas em funções dentro de arquivos de serviço localizados em `src/services/`. Por exemplo, para buscar clientes, use `[clientesService.ts](mdc:src/services/clientesService.ts)`.
2.  **Funções de Busca**: Utilize funções específicas como `buscar<Entidade>` ou `buscar<Entidade>ComFiltros` para obter dados.
3.  **Tratamento de Erros**: Use `try...catch` blocos para capturar erros de consulta e `toast` (da biblioteca `sonner`) para notificar o usuário.
4.  **Estado de Carregamento**: Implemente um estado `isLoading` para fornecer feedback visual durante as consultas. Veja `[Customers.tsx](mdc:src/pages/Customers.tsx)` como exemplo.
5.  **Conversão de Dados**: Se necessário, crie uma função `converterPara<Interface>` para mapear os dados retornados do banco para a interface utilizada no frontend (e.g., `converterParaCliente` em `[Customers.tsx](mdc:src/pages/Customers.tsx)`).

## Geração de Componentes de Página/Visualização

1.  **Estrutura**: Componentes de página (rotas) geralmente residem em `src/pages/`.
2.  **Componentes Reutilizáveis**: Utilize componentes da UI padrão de `src/components/ui/` (como `Card`, `Button`, `Table`, `Input`, `Select`, `Badge`).
3.  **Componentes Específicos**: Crie componentes específicos para a funcionalidade (e.g., filtros, cards de itens, paginação) em um subdiretório dentro de `src/components/`, como `src/components/Customers/`.
4.  **Estado**: Gerencie o estado do componente usando `useState` e `useEffect` para buscar dados e reagir a mudanças (filtros, paginação).
5.  **Visualização**: Considere fornecer múltiplas visualizações de dados (e.g., Tabela e Cards como em `[Customers.tsx](mdc:src/pages/Customers.tsx)`) usando um estado `viewMode`.
6.  **Permissões**: Use `PermissionButton` ou `PermissionSection` de `src/components/` para controlar o acesso a ações com base nas permissões do usuário.
7.  **Navegação**: Use `useNavigate` do `react-router-dom` para navegação entre páginas.