---
description: Regras de consulta ao banco de dados e geracao de componentes novos 
globs: 
---


1. Uso Consistente dos Componentes shadcn ui
	•	Objetivo: Manter a consistência visual e funcional do app utilizando os elementos da biblioteca shadcn ui.
	•	Regra:
	•	Ao iniciar o desenvolvimento de qualquer nova interface ou componente, sempre importe e utilize os componentes pré-definidos da shadcn ui.
	•	Verifique na documentação oficial (ou no repositório do projeto) se há atualizações ou novos padrões que devam ser seguidos.

2. Conexão e Verificação de Tabelas no Supabase
	•	Objetivo: Garantir que, ao estabelecer a conexão com o Supabase, todas as operações que envolvem tabelas sejam realizadas somente se a tabela em questão já existir.
	•	Regra:
	•	Antes de efetuar operações:
	•	Ao tentar acessar uma tabela, inclua uma verificação para confirmar que ela existe.
	•	Se a existência da tabela não puder ser confirmada automaticamente, gere um código SQL de consulta para validação manual.
	•	Exemplo de código SQL para verificação:

SELECT EXISTS (
  SELECT 1 
  FROM information_schema.tables 
  WHERE table_schema = 'public' 
    AND table_name = 'nome_da_tabela'
);

	•	Este comando retorna um valor booleano que indica se a tabela existe no esquema ‘public’.

	•	Fluxo de ação:
	1.	Tentar a verificação automaticamente dentro do fluxo do app.
	2.	Se o sistema não tiver certeza da existência da tabela (por exemplo, em caso de erro ou de ausência de dados na verificação), envie o código SQL acima para que você possa realizar uma consulta manual.
	3.	Com base no retorno dessa consulta, prossiga:
	•	Se a tabela existir: Continue com as operações planejadas.
	•	Se a tabela não existir: Considere criar a tabela ou ajustar a lógica do app conforme a necessidade.

3. Documentação e Monitoramento
	•	Regra de Documentação:
	•	Registre cada verificação e operação que envolva a conexão com o Supabase em logs ou documentação interna do projeto. Isso facilitará a depuração e futuras manutenções.
	•	Regra de Feedback:
	•	Quando o sistema enviar o código SQL para verificação manual, garanta que haja um mecanismo para inserir o resultado dessa verificação de volta no fluxo do app, permitindo a continuidade ou a interrupção da operação conforme apropriado.

4. Considerações Finais
	•	Consistência: Mantenha sempre a mesma abordagem em todas as partes do app. Toda vez que um novo componente for adicionado ou uma nova tabela for acessada, aplique as regras acima.
	•	Flexibilidade: Caso surjam novas necessidades ou alterações na API do Supabase ou na biblioteca shadcn ui, revise e atualize estas regras para refletir as melhores práticas atuais.

