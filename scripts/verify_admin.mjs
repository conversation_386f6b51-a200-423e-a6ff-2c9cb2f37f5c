// Script para verificar a configuração do usuário administrador
import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';
dotenv.config();

// Configuração do Supabase
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://ubwzukpsqcrwzfbppoux.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY; // Chave de serviço (não a chave anônima)

if (!supabaseServiceKey) {
  console.error('Erro: SUPABASE_SERVICE_KEY não está definida no arquivo .env');
  process.exit(1);
}

// Criar cliente Supabase com a chave de serviço
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function verifyAdminUser() {
  try {
    console.log('Verificando usuário administrador...');
    
    // 1. Tentar fazer login com o usuário
    const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'barcelona'
    });
    
    if (signInError) {
      console.error('Erro ao fazer login:', signInError.message);
      process.exit(1);
    }
    
    console.log('Login bem-sucedido!');
    console.log('Detalhes do usuário:');
    console.log('- ID:', signInData.user.id);
    console.log('- Email:', signInData.user.email);
    console.log('- Role:', signInData.user.role);
    console.log('- Metadata:', JSON.stringify(signInData.user.user_metadata, null, 2));
    
    // 2. Listar todos os usuários para verificar se apenas o administrador existe
    const { data: users, error: usersError } = await supabase.auth.admin.listUsers();
    
    if (usersError) {
      console.error('Erro ao listar usuários:', usersError.message);
    } else {
      console.log(`\nTotal de usuários no sistema: ${users.users.length}`);
      console.log('Lista de usuários:');
      users.users.forEach((user, index) => {
        console.log(`${index + 1}. ${user.email} (${user.role || 'sem papel definido'})`);
      });
    }
    
    // 3. Verificar permissões específicas
    try {
      const { data: permissions, error: permissionsError } = await supabase
        .from('user_specific_permissions')
        .select('*')
        .eq('user_id', signInData.user.id);
      
      if (permissionsError) {
        console.warn('Aviso ao verificar permissões:', permissionsError.message);
      } else {
        console.log(`\nPermissões específicas: ${permissions.length}`);
        console.log('Permissões:');
        permissions.forEach((perm, index) => {
          console.log(`${index + 1}. ${perm.resource_type}:${perm.action} = ${perm.allowed ? 'Permitido' : 'Negado'}`);
        });
      }
    } catch (error) {
      console.warn('Aviso ao verificar permissões:', error.message);
    }
    
  } catch (error) {
    console.error('Erro ao verificar usuário administrador:', error);
  } finally {
    process.exit(0);
  }
}

verifyAdminUser();
