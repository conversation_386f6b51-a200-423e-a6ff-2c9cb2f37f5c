// Script para simular um perfil de administrador no localStorage
import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';
import * as fs from 'fs';
dotenv.config();

// Configuração do Supabase
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://ubwzukpsqcrwzfbppoux.supabase.co';
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY; // Chave anônima para simular cliente

if (!supabaseAnonKey) {
  console.error('Erro: VITE_SUPABASE_ANON_KEY não está definida no arquivo .env');
  process.exit(1);
}

// Criar cliente Supabase com a chave anônima (para simular o cliente)
const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function simulateAdminProfile() {
  try {
    console.log('Simulando perfil de administrador...');
    
    // 1. Fazer login para obter o token de acesso
    const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'barcelona'
    });
    
    if (signInError) {
      console.error('Erro ao fazer login:', signInError.message);
      process.exit(1);
    }
    
    console.log('Login bem-sucedido!');
    console.log('ID do usuário:', signInData.user.id);
    
    // 2. Criar o perfil de administrador com a estrutura esperada pelo AuthContext
    const userProfile = {
      id: signInData.user.id,
      email: '<EMAIL>',
      nome: 'Caio Justo',
      role: 'admin', // Deve ser um dos valores definidos em UserRole
      foto_url: null,
      status: 'ativo',
      created_at: new Date().toISOString(),
      cargo: 'Administrador Principal',
      departamento: 'Diretoria',
      telefone: '+5511999999999',
      data_entrada: new Date().toISOString().split('T')[0]
    };
    
    console.log('Perfil de administrador simulado:');
    console.log(userProfile);
    
    // 3. Criar um script JavaScript para injetar o perfil no localStorage
    const injectScript = `
    // Script para injetar o perfil de administrador no localStorage
    (function() {
      try {
        // Perfil de administrador
        const userProfile = ${JSON.stringify(userProfile, null, 2)};
        
        // Salvar no localStorage
        localStorage.setItem('userProfile', JSON.stringify(userProfile));
        
        // Verificar se foi salvo corretamente
        const savedProfile = localStorage.getItem('userProfile');
        if (savedProfile) {
          console.log('Perfil de administrador injetado com sucesso no localStorage!');
          console.log('Perfil:', JSON.parse(savedProfile));
        } else {
          console.error('Falha ao injetar perfil no localStorage!');
        }
      } catch (error) {
        console.error('Erro ao injetar perfil:', error);
      }
    })();
    `;
    
    // 4. Salvar o script em um arquivo
    fs.writeFileSync('public/inject-profile.js', injectScript);
    console.log('Script de injeção salvo em public/inject-profile.js');
    
    // 5. Criar uma página HTML para injetar o perfil
    const injectHtml = `
    <!DOCTYPE html>
    <html lang="pt-BR">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Injetar Perfil de Administrador</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
          line-height: 1.6;
        }
        h1 {
          color: #333;
        }
        .success {
          color: green;
          font-weight: bold;
        }
        .error {
          color: red;
          font-weight: bold;
        }
        pre {
          background-color: #f5f5f5;
          padding: 10px;
          border-radius: 5px;
          overflow-x: auto;
        }
        button {
          background-color: #4CAF50;
          color: white;
          padding: 10px 15px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 16px;
          margin-top: 10px;
        }
        button:hover {
          background-color: #45a049;
        }
      </style>
    </head>
    <body>
      <h1>Injetar Perfil de Administrador</h1>
      <p>Esta página irá injetar um perfil de administrador no localStorage do seu navegador.</p>
      
      <h2>Perfil a ser injetado:</h2>
      <pre id="profile-json">${JSON.stringify(userProfile, null, 2)}</pre>
      
      <button id="inject-btn">Injetar Perfil</button>
      <button id="clear-btn">Limpar Perfil</button>
      
      <h2>Status:</h2>
      <div id="status">Aguardando ação...</div>
      
      <h2>Próximos passos:</h2>
      <ol>
        <li>Clique no botão "Injetar Perfil" acima</li>
        <li>Verifique se o status indica sucesso</li>
        <li>Acesse a aplicação em <a href="http://localhost:8082/" target="_blank">http://localhost:8082/</a></li>
        <li>Faça login com as seguintes credenciais:
          <ul>
            <li>Email: <EMAIL></li>
            <li>Senha: barcelona</li>
          </ul>
        </li>
      </ol>
      
      <script>
        document.getElementById('inject-btn').addEventListener('click', function() {
          try {
            // Perfil de administrador
            const userProfile = ${JSON.stringify(userProfile)};
            
            // Salvar no localStorage
            localStorage.setItem('userProfile', JSON.stringify(userProfile));
            
            // Verificar se foi salvo corretamente
            const savedProfile = localStorage.getItem('userProfile');
            if (savedProfile) {
              document.getElementById('status').innerHTML = '<p class="success">Perfil de administrador injetado com sucesso no localStorage!</p>';
            } else {
              document.getElementById('status').innerHTML = '<p class="error">Falha ao injetar perfil no localStorage!</p>';
            }
          } catch (error) {
            document.getElementById('status').innerHTML = '<p class="error">Erro ao injetar perfil: ' + error.message + '</p>';
          }
        });
        
        document.getElementById('clear-btn').addEventListener('click', function() {
          try {
            localStorage.removeItem('userProfile');
            document.getElementById('status').innerHTML = '<p class="success">Perfil removido do localStorage!</p>';
          } catch (error) {
            document.getElementById('status').innerHTML = '<p class="error">Erro ao remover perfil: ' + error.message + '</p>';
          }
        });
      </script>
    </body>
    </html>
    `;
    
    // 6. Salvar a página HTML
    fs.writeFileSync('public/inject-profile.html', injectHtml);
    console.log('Página HTML salva em public/inject-profile.html');
    
    console.log('\nSimulação de perfil de administrador concluída!');
    console.log('Instruções:');
    console.log('1. Acesse http://localhost:8082/inject-profile.html');
    console.log('2. Clique no botão "Injetar Perfil"');
    console.log('3. Acesse a aplicação em http://localhost:8082/');
    console.log('4. Faça login com as seguintes credenciais:');
    console.log('   - Email: <EMAIL>');
    console.log('   - Senha: barcelona');
    
  } catch (error) {
    console.error('Erro ao simular perfil de administrador:', error);
  } finally {
    process.exit(0);
  }
}

simulateAdminProfile();
