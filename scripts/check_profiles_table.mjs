// Script para verificar a estrutura da tabela profiles
import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';
dotenv.config();

// Configuração do Supabase
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://ubwzukpsqcrwzfbppoux.supabase.co';
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY; // Chave anônima para simular cliente

if (!supabaseAnonKey) {
  console.error('Erro: VITE_SUPABASE_ANON_KEY não está definida no arquivo .env');
  process.exit(1);
}

// Criar cliente Supabase com a chave anônima (para simular o cliente)
const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function checkProfilesTable() {
  try {
    console.log('Verificando estrutura da tabela profiles...');
    
    // 1. Verificar se a tabela profiles existe
    const { data: tableExists, error: tableError } = await supabase
      .from('profiles')
      .select('id')
      .limit(1);
    
    if (tableError) {
      console.error('Erro ao verificar tabela profiles:', tableError.message);
      
      // Verificar se o erro é devido à tabela não existir
      if (tableError.message.includes('relation "profiles" does not exist')) {
        console.log('A tabela profiles não existe!');
      }
      
      process.exit(1);
    }
    
    console.log('A tabela profiles existe.');
    
    // 2. Verificar a estrutura da tabela
    const { data: columns, error: columnsError } = await supabase
      .rpc('get_table_columns', { table_name: 'profiles' });
    
    if (columnsError) {
      console.error('Erro ao verificar colunas da tabela profiles:', columnsError.message);
      
      // Tentar uma abordagem alternativa
      console.log('Tentando abordagem alternativa...');
      
      // Fazer uma consulta simples para inferir a estrutura
      const { data: sampleData, error: sampleError } = await supabase
        .from('profiles')
        .select('*')
        .limit(1);
      
      if (sampleError) {
        console.error('Erro ao obter amostra de dados:', sampleError.message);
      } else if (sampleData && sampleData.length > 0) {
        console.log('Estrutura inferida da tabela profiles:');
        console.log(Object.keys(sampleData[0]));
      } else {
        console.log('Nenhum dado encontrado na tabela profiles.');
      }
    } else {
      console.log('Colunas da tabela profiles:');
      console.log(columns);
    }
    
    // 3. Verificar se há algum perfil existente
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('id, email')
      .limit(5);
    
    if (profilesError) {
      console.error('Erro ao verificar perfis existentes:', profilesError.message);
    } else {
      console.log(`Encontrados ${profiles.length} perfis:`);
      profiles.forEach((profile, index) => {
        console.log(`${index + 1}. ID: ${profile.id}, Email: ${profile.email || 'N/A'}`);
      });
    }
    
    // 4. Tentar fazer login e verificar o erro específico
    console.log('\nTestando login para verificar o erro específico...');
    
    const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'barcelona'
    });
    
    if (signInError) {
      console.error('Erro ao fazer login:', signInError.message);
      process.exit(1);
    }
    
    console.log('Login bem-sucedido!');
    console.log('ID do usuário:', signInData.user.id);
    
    // 5. Verificar se o perfil do usuário existe
    const { data: userProfile, error: userProfileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', signInData.user.id)
      .single();
    
    if (userProfileError) {
      console.error('Erro ao verificar perfil do usuário:', userProfileError.message);
      
      if (userProfileError.message.includes('No rows found')) {
        console.log('O perfil do usuário não existe! Precisamos criá-lo.');
      }
    } else {
      console.log('Perfil do usuário encontrado:');
      console.log(userProfile);
    }
    
    // 6. Verificar a função RPC que pode estar sendo usada
    console.log('\nVerificando funções RPC disponíveis...');
    
    const { data: functions, error: functionsError } = await supabase
      .rpc('get_available_functions');
    
    if (functionsError) {
      console.error('Erro ao verificar funções RPC:', functionsError.message);
    } else if (functions && functions.length > 0) {
      console.log('Funções RPC disponíveis:');
      functions.forEach((func, index) => {
        console.log(`${index + 1}. ${func.name}`);
      });
    } else {
      console.log('Nenhuma função RPC encontrada ou função get_available_functions não existe.');
    }
    
  } catch (error) {
    console.error('Erro ao verificar tabela profiles:', error);
  } finally {
    process.exit(0);
  }
}

checkProfilesTable();
