import { supabase } from '../src/lib/supabase';

async function criarPrecatorioTeste() {
  console.log('Criando precatório de teste...');

  // Dados do precatório de teste
  const precatorioData = {
    numero_precatorio: `TESTE-${Math.floor(Math.random() * 100000)}`,
    status: 'analise',
    valor_total: 150000,
    natureza: 'Alimentar',
    prioridade: 'alta',
    data_entrada: new Date().toISOString(),
    data_previsao_pagamento: new Date(new Date().setMonth(new Date().getMonth() + 6)).toISOString(),
    observacoes: 'Precatório de teste criado automaticamente',
    tags: ['teste', 'exemplo', 'automação'],
    entidade_devedora: 'Governo Federal'
  };

  // Inserir no banco de dados
  const { data, error } = await supabase
    .from('precatorios')
    .insert([precatorioData])
    .select()
    .single();

  if (error) {
    console.error('Erro ao criar precatório de teste:', error);
    return;
  }

  console.log('Precatório de teste criado com sucesso:', data);

  // Criar um registro de histórico
  const historicoData = {
    precatorio_id: data.id,
    acao: 'Criação',
    data: new Date().toISOString(),
    usuario: 'Sistema de Teste',
    detalhes: 'Precatório de teste criado automaticamente'
  };

  await supabase
    .from('precatorios_historico')
    .insert([historicoData]);

  // Criar documentos de exemplo
  const documentosData = [
    {
      precatorio_id: data.id,
      nome: 'Petição Inicial',
      tipo: 'documento',
      status: 'pendente',
      data_upload: new Date().toISOString()
    },
    {
      precatorio_id: data.id,
      nome: 'Procuração',
      tipo: 'autenticação',
      status: 'aprovado',
      data_upload: new Date().toISOString()
    }
  ];

  await supabase
    .from('precatorios_documentos')
    .insert(documentosData);

  console.log('Documentos e histórico criados com sucesso.');
  console.log('Processo de criação de precatório de teste concluído.');
}

// Executar a função
criarPrecatorioTeste()
  .then(() => {
    console.log('Script finalizado com sucesso.');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Erro ao executar o script:', error);
    process.exit(1);
  });
