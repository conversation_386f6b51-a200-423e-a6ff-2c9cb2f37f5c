-- Criar tabela kanban_colunas
CREATE TABLE IF NOT EXISTS kanban_colunas (
  id SERIAL PRIMARY KEY,
  nome VARCHAR(255) NOT NULL,
  cor VARCHAR(50) NOT NULL,
  tipo VARCHAR(20) NOT NULL CHECK (tipo IN ('PRECATORIO', 'RPV', 'AMBOS')),
  ordem INTEGER NOT NULL,
  status_id VARCHAR(100) NOT NULL UNIQUE,
  ativo BOOLEAN NOT NULL DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- <PERSON><PERSON><PERSON> co<PERSON><PERSON> padrão
INSERT INTO kanban_colunas (nome, cor, tipo, ordem, status_id, ativo)
VALUES
  ('Análise', '#3b82f6', 'AMBOS', 1, 'analise', TRUE),
  ('Proposta TMJ', '#8b5cf6', 'AMBOS', 2, 'proposta_tmj', TRUE),
  ('Proposta BTG', '#ec4899', 'AMBOS', 3, 'proposta_btg', TRUE),
  ('Negociação', '#f59e0b', 'AMBOS', 4, 'negociacao', TRUE),
  ('Documentação', '#10b981', 'AMBOS', 5, 'documentacao', TRUE),
  ('Pagamento', '#6366f1', 'AMBOS', 6, 'pagamento', TRUE),
  ('Concluído', '#22c55e', 'AMBOS', 7, 'concluido', TRUE),
  ('Cancelado', '#ef4444', 'AMBOS', 8, 'cancelado', TRUE);

-- Criar trigger para atualizar o campo updated_at automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = NOW();
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_kanban_colunas_updated_at
BEFORE UPDATE ON kanban_colunas
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Criar políticas RLS (Row Level Security)
ALTER TABLE kanban_colunas ENABLE ROW LEVEL SECURITY;

-- Política para permitir leitura para todos os usuários autenticados
CREATE POLICY kanban_colunas_select_policy
ON kanban_colunas FOR SELECT
TO authenticated
USING (TRUE);

-- Política para permitir inserção, atualização e exclusão apenas para administradores
CREATE POLICY kanban_colunas_insert_policy
ON kanban_colunas FOR INSERT
TO authenticated
WITH CHECK (
  EXISTS (
    SELECT 1 FROM auth.users
    WHERE auth.users.id = auth.uid()
    AND auth.users.role = 'admin'
  )
);

CREATE POLICY kanban_colunas_update_policy
ON kanban_colunas FOR UPDATE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM auth.users
    WHERE auth.users.id = auth.uid()
    AND auth.users.role = 'admin'
  )
);

CREATE POLICY kanban_colunas_delete_policy
ON kanban_colunas FOR DELETE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM auth.users
    WHERE auth.users.id = auth.uid()
    AND auth.users.role = 'admin'
  )
);
