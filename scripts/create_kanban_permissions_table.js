// Script para criar a tabela kanban_column_permissions usando o cliente Supabase
const { createClient } = require('@supabase/supabase-js');

// Configuração do Supabase
const supabaseUrl = "https://ubwzukpsqcrwzfbppoux.supabase.co";
const supabaseKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVid3p1a3BzcWNyd3pmYnBwb3V4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzMwNzc1NzcsImV4cCI6MjA0ODY1MzU3N30.SusnN1yvYZRIivt4l3NYCimIUJChIt3oa9KmTWYiVA0";

// Criar cliente Supabase
const supabase = createClient(supabaseUrl, supabaseKey);

// SQL para criar a tabela
const createTableSQL = `
-- Criar tabela kanban_column_permissions se não existir
CREATE TABLE IF NOT EXISTS kanban_column_permissions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  column_id UUID NOT NULL,
  can_view BO<PERSON>EAN NOT NULL DEFAULT true,
  can_edit BOOLEAN NOT NULL DEFAULT false,
  can_delete BOOLEAN NOT NULL DEFAULT false,
  can_move_cards BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, column_id)
);

-- Criar trigger para atualizar o campo updated_at automaticamente
CREATE OR REPLACE FUNCTION update_kanban_permissions_updated_at()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = NOW();
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Criar trigger para a tabela kanban_column_permissions
DROP TRIGGER IF EXISTS update_kanban_column_permissions_updated_at ON kanban_column_permissions;
CREATE TRIGGER update_kanban_column_permissions_updated_at
BEFORE UPDATE ON kanban_column_permissions
FOR EACH ROW
EXECUTE FUNCTION update_kanban_permissions_updated_at();

-- Criar políticas RLS (Row Level Security)
ALTER TABLE kanban_column_permissions ENABLE ROW LEVEL SECURITY;

-- Política para permitir leitura para todos os usuários autenticados
DROP POLICY IF EXISTS kanban_column_permissions_select_policy ON kanban_column_permissions;
CREATE POLICY kanban_column_permissions_select_policy
ON kanban_column_permissions FOR SELECT
TO authenticated
USING (TRUE);

-- Política para permitir inserção, atualização e exclusão
DROP POLICY IF EXISTS kanban_column_permissions_insert_policy ON kanban_column_permissions;
CREATE POLICY kanban_column_permissions_insert_policy
ON kanban_column_permissions FOR INSERT
TO authenticated
WITH CHECK (
  -- Administradores podem inserir qualquer registro
  (SELECT role FROM auth.users WHERE id = auth.uid()) = 'admin'
  -- Usuários podem inserir registros apenas para si mesmos
  OR user_id = auth.uid()
);

DROP POLICY IF EXISTS kanban_column_permissions_update_policy ON kanban_column_permissions;
CREATE POLICY kanban_column_permissions_update_policy
ON kanban_column_permissions FOR UPDATE
TO authenticated
USING (
  -- Administradores podem atualizar qualquer registro
  (SELECT role FROM auth.users WHERE id = auth.uid()) = 'admin'
  -- Usuários podem atualizar apenas seus próprios registros
  OR user_id = auth.uid()
);

DROP POLICY IF EXISTS kanban_column_permissions_delete_policy ON kanban_column_permissions;
CREATE POLICY kanban_column_permissions_delete_policy
ON kanban_column_permissions FOR DELETE
TO authenticated
USING (
  -- Administradores podem excluir qualquer registro
  (SELECT role FROM auth.users WHERE id = auth.uid()) = 'admin'
  -- Usuários podem excluir apenas seus próprios registros
  OR user_id = auth.uid()
);
`;

async function createTable() {
  try {
    console.log('Criando tabela kanban_column_permissions...');
    
    // Executar o SQL para criar a tabela
    const { data, error } = await supabase.rpc('executar_sql_seguro', {
      sql_query: createTableSQL
    });
    
    if (error) {
      console.error('Erro ao criar tabela:', error);
      return;
    }
    
    console.log('Tabela criada com sucesso!');
    
    // Inserir permissões padrão para administradores
    const insertAdminPermissionsSQL = `
    INSERT INTO kanban_column_permissions (user_id, column_id, can_view, can_edit, can_delete, can_move_cards)
    SELECT 
      u.id, 
      c.id, 
      TRUE, 
      TRUE, 
      TRUE, 
      TRUE
    FROM 
      auth.users u
    CROSS JOIN 
      kanban_colunas_personalizadas c
    WHERE 
      u.role = 'admin'
      AND NOT EXISTS (
        SELECT 1 FROM kanban_column_permissions p 
        WHERE p.user_id = u.id AND p.column_id = c.id
      )
    ON CONFLICT (user_id, column_id) DO NOTHING;
    `;
    
    console.log('Inserindo permissões para administradores...');
    
    const { data: insertData, error: insertError } = await supabase.rpc('executar_sql_seguro', {
      sql_query: insertAdminPermissionsSQL
    });
    
    if (insertError) {
      console.error('Erro ao inserir permissões para administradores:', insertError);
      return;
    }
    
    console.log('Permissões para administradores inseridas com sucesso!');
    
    // Inserir permissões para o usuário <EMAIL>
    const insertCaioPermissionsSQL = `
    INSERT INTO kanban_column_permissions (user_id, column_id, can_view, can_edit, can_delete, can_move_cards)
    SELECT 
      u.id, 
      c.id, 
      TRUE, 
      TRUE, 
      TRUE, 
      TRUE
    FROM 
      auth.users u
    CROSS JOIN 
      kanban_colunas_personalizadas c
    WHERE 
      u.email = '<EMAIL>'
      AND NOT EXISTS (
        SELECT 1 FROM kanban_column_permissions p 
        WHERE p.user_id = u.id AND p.column_id = c.id
      )
    ON CONFLICT (user_id, column_id) DO NOTHING;
    `;
    
    console.log('Inserindo permissõ<NAME_EMAIL>...');
    
    const { data: caioData, error: caioError } = await supabase.rpc('executar_sql_seguro', {
      sql_query: insertCaioPermissionsSQL
    });
    
    if (caioError) {
      console.error('Erro ao inserir permissõ<NAME_EMAIL>:', caioError);
      return;
    }
    
    console.log('Permissõ<NAME_EMAIL> inseridas com sucesso!');
    
    // Verificar as permissões configuradas
    const verifyPermissionsSQL = `
    SELECT 
      u.email as usuario,
      u.role as papel,
      c.nome as coluna,
      p.can_view,
      p.can_edit,
      p.can_delete,
      p.can_move_cards
    FROM 
      kanban_column_permissions p
    JOIN 
      auth.users u ON p.user_id = u.id
    JOIN 
      kanban_colunas_personalizadas c ON p.column_id = c.id
    ORDER BY 
      u.email, c.ordem;
    `;
    
    console.log('Verificando permissões configuradas...');
    
    const { data: verifyData, error: verifyError } = await supabase.rpc('executar_sql_seguro', {
      sql_query: verifyPermissionsSQL
    });
    
    if (verifyError) {
      console.error('Erro ao verificar permissões:', verifyError);
      return;
    }
    
    console.log('Permissões configuradas:', verifyData);
    
  } catch (error) {
    console.error('Erro:', error);
  }
}

// Executar a função
createTable();
