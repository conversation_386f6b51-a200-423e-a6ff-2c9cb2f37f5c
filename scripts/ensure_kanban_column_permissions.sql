-- <PERSON><PERSON>t para garantir que a tabela kanban_column_permissions exista
-- e tenha a estrutura correta

-- Criar tabela kanban_column_permissions se não existir
CREATE TABLE IF NOT EXISTS kanban_column_permissions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  column_id UUID NOT NULL,
  can_view BOOLEAN NOT NULL DEFAULT true,
  can_edit BOOLEAN NOT NULL DEFAULT false,
  can_delete BOOLEAN NOT NULL DEFAULT false,
  can_move_cards BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, column_id)
);

-- <PERSON><PERSON>r trigger para atualizar o campo updated_at automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = NOW();
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- <PERSON><PERSON><PERSON> trigger para a tabela kanban_column_permissions
DROP TRIGGER IF EXISTS update_kanban_column_permissions_updated_at ON kanban_column_permissions;
CREATE TRIGGER update_kanban_column_permissions_updated_at
BEFORE UPDATE ON kanban_column_permissions
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Criar políticas RLS (Row Level Security)
ALTER TABLE kanban_column_permissions ENABLE ROW LEVEL SECURITY;

-- Política para permitir leitura para todos os usuários autenticados
DROP POLICY IF EXISTS kanban_column_permissions_select_policy ON kanban_column_permissions;
CREATE POLICY kanban_column_permissions_select_policy
ON kanban_column_permissions FOR SELECT
TO authenticated
USING (TRUE);

-- Política para permitir inserção, atualização e exclusão apenas para administradores
DROP POLICY IF EXISTS kanban_column_permissions_insert_policy ON kanban_column_permissions;
CREATE POLICY kanban_column_permissions_insert_policy
ON kanban_column_permissions FOR INSERT
TO authenticated
WITH CHECK (
  EXISTS (
    SELECT 1 FROM auth.users
    WHERE auth.users.id = auth.uid()
    AND auth.users.role = 'admin'
  )
);

DROP POLICY IF EXISTS kanban_column_permissions_update_policy ON kanban_column_permissions;
CREATE POLICY kanban_column_permissions_update_policy
ON kanban_column_permissions FOR UPDATE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM auth.users
    WHERE auth.users.id = auth.uid()
    AND auth.users.role = 'admin'
  )
);

DROP POLICY IF EXISTS kanban_column_permissions_delete_policy ON kanban_column_permissions;
CREATE POLICY kanban_column_permissions_delete_policy
ON kanban_column_permissions FOR DELETE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM auth.users
    WHERE auth.users.id = auth.uid()
    AND auth.users.role = 'admin'
  )
);

-- Inserir permissões padrão para administradores (opcional)
-- Isso garante que administradores tenham acesso a todas as colunas
INSERT INTO kanban_column_permissions (user_id, column_id, can_view, can_edit, can_delete, can_move_cards)
SELECT 
  u.id, 
  c.id, 
  TRUE, 
  TRUE, 
  TRUE, 
  TRUE
FROM 
  auth.users u
CROSS JOIN 
  kanban_colunas_personalizadas c
WHERE 
  u.role = 'admin'
  AND NOT EXISTS (
    SELECT 1 FROM kanban_column_permissions p 
    WHERE p.user_id = u.id AND p.column_id = c.id
  )
ON CONFLICT (user_id, column_id) DO NOTHING;
