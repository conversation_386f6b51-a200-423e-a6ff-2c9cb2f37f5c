-- <PERSON>ript para criar a tabela kanban_column_permissions
-- Execute este script diretamente no console SQL do Supabase

-- Criar tabela kanban_column_permissions se não existir
CREATE TABLE IF NOT EXISTS kanban_column_permissions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  column_id UUID NOT NULL,
  can_view BOOLEAN NOT NULL DEFAULT true,
  can_edit BOOLEAN NOT NULL DEFAULT false,
  can_delete BOOLEAN NOT NULL DEFAULT false,
  can_move_cards BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, column_id)
);

-- Criar trigger para atualizar o campo updated_at automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = NOW();
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- <PERSON><PERSON>r trigger para a tabela kanban_column_permissions
DROP TRIGGER IF EXISTS update_kanban_column_permissions_updated_at ON kanban_column_permissions;
CREATE TRIGGER update_kanban_column_permissions_updated_at
BEFORE UPDATE ON kanban_column_permissions
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Criar políticas RLS (Row Level Security)
ALTER TABLE kanban_column_permissions ENABLE ROW LEVEL SECURITY;

-- Política para permitir leitura para todos os usuários autenticados
DROP POLICY IF EXISTS kanban_column_permissions_select_policy ON kanban_column_permissions;
CREATE POLICY kanban_column_permissions_select_policy
ON kanban_column_permissions FOR SELECT
TO authenticated
USING (TRUE);

-- Política para permitir inserção, atualização e exclusão
DROP POLICY IF EXISTS kanban_column_permissions_insert_policy ON kanban_column_permissions;
CREATE POLICY kanban_column_permissions_insert_policy
ON kanban_column_permissions FOR INSERT
TO authenticated
WITH CHECK (
  -- Administradores podem inserir qualquer registro
  (SELECT role FROM auth.users WHERE id = auth.uid()) = 'admin'
  -- Usuários podem inserir registros apenas para si mesmos
  OR user_id = auth.uid()
);

DROP POLICY IF EXISTS kanban_column_permissions_update_policy ON kanban_column_permissions;
CREATE POLICY kanban_column_permissions_update_policy
ON kanban_column_permissions FOR UPDATE
TO authenticated
USING (
  -- Administradores podem atualizar qualquer registro
  (SELECT role FROM auth.users WHERE id = auth.uid()) = 'admin'
  -- Usuários podem atualizar apenas seus próprios registros
  OR user_id = auth.uid()
);

DROP POLICY IF EXISTS kanban_column_permissions_delete_policy ON kanban_column_permissions;
CREATE POLICY kanban_column_permissions_delete_policy
ON kanban_column_permissions FOR DELETE
TO authenticated
USING (
  -- Administradores podem excluir qualquer registro
  (SELECT role FROM auth.users WHERE id = auth.uid()) = 'admin'
  -- Usuários podem excluir apenas seus próprios registros
  OR user_id = auth.uid()
);

-- Inserir permissões padrão para administradores
INSERT INTO kanban_column_permissions (user_id, column_id, can_view, can_edit, can_delete, can_move_cards)
SELECT
  u.id,
  c.id,
  TRUE,
  TRUE,
  TRUE,
  TRUE
FROM
  auth.users u
CROSS JOIN
  kanban_colunas_personalizadas c
WHERE
  u.role = 'admin'
  AND NOT EXISTS (
    SELECT 1 FROM kanban_column_permissions p
    WHERE p.user_id = u.id AND p.column_id = c.id
  )
ON CONFLICT (user_id, column_id) DO NOTHING;

-- Inserir permissões padrão para o usuário <EMAIL>
INSERT INTO kanban_column_permissions (user_id, column_id, can_view, can_edit, can_delete, can_move_cards)
SELECT
  u.id,
  c.id,
  TRUE,
  TRUE,
  TRUE,
  TRUE
FROM
  auth.users u
CROSS JOIN
  kanban_colunas_personalizadas c
WHERE
  u.email = '<EMAIL>'
  AND NOT EXISTS (
    SELECT 1 FROM kanban_column_permissions p
    WHERE p.user_id = u.id AND p.column_id = c.id
  )
ON CONFLICT (user_id, column_id) DO NOTHING;

-- Verificar as permissões configuradas
SELECT
  u.email as usuario,
  u.role as papel,
  c.nome as coluna,
  p.can_view,
  p.can_edit,
  p.can_delete,
  p.can_move_cards
FROM
  kanban_column_permissions p
JOIN
  auth.users u ON p.user_id = u.id
JOIN
  kanban_colunas_personalizadas c ON p.column_id = c.id
ORDER BY
  u.email, c.ordem;
