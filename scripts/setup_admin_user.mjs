// Script para configurar usuário administrador e remover outros usuários
import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';
dotenv.config();

// Configuração do Supabase
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://ubwzukpsqcrwzfbppoux.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY; // Chave de serviço (não a chave anônima)

if (!supabaseServiceKey) {
  console.error('Erro: SUPABASE_SERVICE_KEY não está definida no arquivo .env');
  process.exit(1);
}

// Criar cliente Supabase com a chave de serviço
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function setupAdminUser() {
  try {
    console.log('Configurando usuário administrador...');
    
    // 1. Listar todos os usuários existentes
    const { data: users, error: usersError } = await supabase.auth.admin.listUsers();
    
    if (usersError) {
      throw usersError;
    }
    
    console.log(`Encontrados ${users.users.length} usuários no sistema.`);
    
    // 2. Verificar se o usuário <EMAIL> já existe
    const existingUser = users.users.find(u => u.email === '<EMAIL>');
    let adminUserId;
    
    if (existingUser) {
      adminUserId = existingUser.id;
      console.log('Usuário <EMAIL> já existe, atualizando...');
      
      // Atualizar senha e metadados
      const { error: updateError } = await supabase.auth.admin.updateUserById(
        adminUserId,
        {
          password: 'barcelona',
          email: '<EMAIL>',
          user_metadata: {
            nome: 'Caio Justo',
            role: 'admin',
            cargo: 'Administrador Principal',
            departamento: 'Diretoria',
            telefone: '+5511999999999',
            data_entrada: new Date().toISOString().split('T')[0]
          },
          app_metadata: {
            role: 'admin'
          }
        }
      );
      
      if (updateError) {
        throw updateError;
      }
      
      console.log('Usuário atualizado com sucesso.');
    } else {
      console.log('Criando novo usuário <EMAIL>...');
      
      // Criar novo usuário
      const { data: newUser, error: createError } = await supabase.auth.admin.createUser({
        email: '<EMAIL>',
        password: 'barcelona',
        email_confirm: true,
        user_metadata: {
          nome: 'Caio Justo',
          role: 'admin',
          cargo: 'Administrador Principal',
          departamento: 'Diretoria',
          telefone: '+5511999999999',
          data_entrada: new Date().toISOString().split('T')[0]
        },
        app_metadata: {
          role: 'admin'
        }
      });
      
      if (createError) {
        throw createError;
      }
      
      adminUserId = newUser.user.id;
      console.log('Usuário criado com sucesso:', adminUserId);
    }
    
    // 3. Atualizar o papel do usuário na tabela auth.users
    const { error: updateRoleError } = await supabase.auth.admin.updateUserById(
      adminUserId,
      { role: 'admin' }
    );
    
    if (updateRoleError) {
      throw updateRoleError;
    }
    
    console.log('Papel do usuário atualizado para admin');
    
    // 4. Verificar se o perfil existe e atualizá-lo
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', adminUserId)
      .single();
    
    if (profileError && profileError.code !== 'PGRST116') {
      console.log('Erro ao verificar perfil:', profileError.message);
    }
    
    // Criar ou atualizar o perfil
    const profileInfo = {
      id: adminUserId,
      name: 'Caio Justo',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      occupation: 'Administrador Principal',
      // Adicionar outros campos conforme necessário
    };
    
    const { error: upsertError } = await supabase
      .from('profiles')
      .upsert(profileInfo);
    
    if (upsertError) {
      console.warn('Aviso ao atualizar perfil:', upsertError.message);
    } else {
      console.log('Perfil atualizado com sucesso');
    }
    
    // 5. Verificar se a tabela custom_roles existe e obter o papel de administrador
    try {
      const { data: rolesData, error: rolesError } = await supabase
        .from('custom_roles')
        .select('id')
        .eq('nome', 'Administrador')
        .single();
      
      if (rolesError && rolesError.code !== 'PGRST116') {
        console.warn('Aviso ao verificar papéis:', rolesError.message);
      } else if (rolesData) {
        // Atualizar o perfil com o custom_role_id se a tabela existir
        const { error: updateProfileError } = await supabase
          .from('profiles')
          .update({ custom_role_id: rolesData.id })
          .eq('id', adminUserId);
        
        if (updateProfileError) {
          console.warn('Aviso ao atualizar papel no perfil:', updateProfileError.message);
        } else {
          console.log('Perfil atualizado com o papel de administrador');
        }
      }
    } catch (error) {
      console.warn('Aviso ao verificar papéis:', error.message);
    }
    
    // 6. Adicionar permissões específicas
    try {
      // Verificar se a tabela user_specific_permissions existe
      const { data: permissionData, error: permissionCheckError } = await supabase
        .from('user_specific_permissions')
        .select('id')
        .limit(1);
      
      if (!permissionCheckError) {
        // Adicionar todas as permissões possíveis
        const permissionsToAdd = [
          { resource_type: 'system', action: 'gerenciar_permissoes', allowed: true },
          { resource_type: 'system', action: 'configurar_sistema', allowed: true },
          { resource_type: 'user', action: 'gerenciar_usuarios', allowed: true },
          { resource_type: 'cliente', action: 'visualizar_cliente', allowed: true },
          { resource_type: 'cliente', action: 'criar_cliente', allowed: true },
          { resource_type: 'cliente', action: 'editar_cliente', allowed: true },
          { resource_type: 'cliente', action: 'excluir_cliente', allowed: true },
          { resource_type: 'precatorio', action: 'visualizar_precatorio', allowed: true },
          { resource_type: 'precatorio', action: 'criar_precatorio', allowed: true },
          { resource_type: 'precatorio', action: 'editar_precatorio', allowed: true },
          { resource_type: 'precatorio', action: 'excluir_precatorio', allowed: true },
          { resource_type: 'rpv', action: 'visualizar_rpv', allowed: true },
          { resource_type: 'rpv', action: 'criar_rpv', allowed: true },
          { resource_type: 'rpv', action: 'editar_rpv', allowed: true },
          { resource_type: 'rpv', action: 'excluir_rpv', allowed: true },
          { resource_type: 'tarefa', action: 'visualizar_tarefa', allowed: true },
          { resource_type: 'tarefa', action: 'criar_tarefa', allowed: true },
          { resource_type: 'tarefa', action: 'editar_tarefa', allowed: true },
          { resource_type: 'tarefa', action: 'excluir_tarefa', allowed: true },
          { resource_type: 'tarefa', action: 'visualizar_todas_tarefas', allowed: true },
          { resource_type: 'documento', action: 'visualizar_documento', allowed: true },
          { resource_type: 'documento', action: 'criar_documento', allowed: true },
          { resource_type: 'documento', action: 'editar_documento', allowed: true },
          { resource_type: 'documento', action: 'excluir_documento', allowed: true },
          { resource_type: 'relatorio', action: 'visualizar_relatorio_completo', allowed: true }
        ];
        
        // Adicionar cada permissão
        for (const permission of permissionsToAdd) {
          const { error: permissionError } = await supabase
            .from('user_specific_permissions')
            .upsert({
              user_id: adminUserId,
              resource_type: permission.resource_type,
              action: permission.action,
              allowed: permission.allowed
            });
          
          if (permissionError) {
            console.warn(`Aviso ao adicionar permissão ${permission.resource_type}:${permission.action}:`, permissionError.message);
          }
        }
        
        console.log('Permissões específicas adicionadas com sucesso');
      }
    } catch (error) {
      console.warn('Aviso ao adicionar permissões específicas:', error.message);
    }
    
    // 7. Remover outros usuários (exceto o administrador)
    console.log('Removendo outros usuários...');
    let removedCount = 0;
    
    for (const user of users.users) {
      if (user.id !== adminUserId) {
        try {
          const { error: deleteError } = await supabase.auth.admin.deleteUser(user.id);
          
          if (deleteError) {
            console.warn(`Aviso ao remover usuário ${user.email}:`, deleteError.message);
          } else {
            removedCount++;
            console.log(`Usuário ${user.email} removido com sucesso.`);
          }
        } catch (error) {
          console.warn(`Aviso ao remover usuário ${user.email}:`, error.message);
        }
      }
    }
    
    console.log(`${removedCount} usuários removidos com sucesso.`);
    console.log('Configuração do usuário administrador concluída!');
    console.log('Email: <EMAIL>');
    console.log('Senha: barcelona');
    
  } catch (error) {
    console.error('Erro ao configurar usuário administrador:', error);
  } finally {
    process.exit(0);
  }
}

setupAdminUser();
