// Script para corrigir problemas de login e perfil
import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';
dotenv.config();

// Configuração do Supabase
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://ubwzukpsqcrwzfbppoux.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY; // Chave de serviço (não a chave anônima)

console.log('Iniciando script de correção de perfil...');
console.log('URL do Supabase:', supabaseUrl);

if (!supabaseServiceKey) {
  console.error('Erro: SUPABASE_SERVICE_KEY não está definida no arquivo .env');
  process.exit(1);
}

// Criar cliente Supabase com a chave de serviço
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function fixProfileLogin() {
  try {
    console.log('Iniciando verificação e correção de perfil...');

    // 1. Verificar se o usuário existe na tabela auth.users
    const { data: userData, error: userError } = await supabase.auth.admin.getUserById(
      'ae20f986-9e4e-4398-82ca-c4576f5f5655'
    );

    if (userError) {
      console.error('Erro ao verificar usuário:', userError.message);
      return;
    }

    if (!userData.user) {
      console.error('Usuário não encontrado na tabela auth.users');
      return;
    }

    console.log('Usuário encontrado:', userData.user.email);

    // 2. Verificar se o perfil existe na tabela profiles
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', 'ae20f986-9e4e-4398-82ca-c4576f5f5655')
      .single();

    if (profileError) {
      console.error('Erro ao verificar perfil:', profileError.message);

      // 3. Se o perfil não existir, criar um novo
      console.log('Tentando criar perfil para o usuário...');

      const { data: insertData, error: insertError } = await supabase
        .from('profiles')
        .insert([
          {
            id: 'ae20f986-9e4e-4398-82ca-c4576f5f5655',
            email: '<EMAIL>',
            nome: 'Caio Justo',
            role: 'admin',
            status: 'ativo',
            custom_role_id: '91db2de1-bc77-41a4-b3af-b7f17939b60e',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }
        ])
        .select();

      if (insertError) {
        console.error('Erro ao criar perfil:', insertError.message);
      } else {
        console.log('Perfil criado com sucesso:', insertData[0]);
      }
    } else {
      console.log('Perfil encontrado:', profileData);

      // 4. Verificar se o perfil está completo e atualizar se necessário
      if (!profileData.role || profileData.role !== 'admin' || !profileData.custom_role_id) {
        console.log('Atualizando perfil com informações completas...');

        const { data: updateData, error: updateError } = await supabase
          .from('profiles')
          .update({
            role: 'admin',
            status: 'ativo',
            custom_role_id: '91db2de1-bc77-41a4-b3af-b7f17939b60e',
            updated_at: new Date().toISOString()
          })
          .eq('id', 'ae20f986-9e4e-4398-82ca-c4576f5f5655')
          .select();

        if (updateError) {
          console.error('Erro ao atualizar perfil:', updateError.message);
        } else {
          console.log('Perfil atualizado com sucesso:', updateData[0]);
        }
      }
    }

    console.log('\nVerificação e correção concluídas!');
    console.log('Instruções para login:');
    console.log('1. Limpe os cookies e o armazenamento local do navegador');
    console.log('2. Feche a aplicação e reinicie o servidor com npm run dev');
    console.log('3. Acesse a aplicação em http://localhost:8081/');
    console.log('4. Faça login com as seguintes credenciais:');
    console.log('   - Email: <EMAIL>');
    console.log('   - Senha: barcelona');

  } catch (error) {
    console.error('Erro ao corrigir perfil de login:', error);
  } finally {
    process.exit(0);
  }
}

fixProfileLogin();
