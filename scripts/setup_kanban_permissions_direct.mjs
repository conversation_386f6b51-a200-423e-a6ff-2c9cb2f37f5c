// Script para configurar permissões do Kanban usando a API direta do Supabase
import { createClient } from '@supabase/supabase-js';

// Configuração do Supabase
const supabaseUrl = "https://ubwzukpsqcrwzfbppoux.supabase.co";
const supabaseKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVid3p1a3BzcWNyd3pmYnBwb3V4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzMwNzc1NzcsImV4cCI6MjA0ODY1MzU3N30.SusnN1yvYZRIivt4l3NYCimIUJChIt3oa9KmTWYiVA0";

console.log('Usando URL do Supabase:', supabaseUrl);
console.log('Chave configurada com sucesso');

// Criar cliente Supabase
const supabase = createClient(supabaseUrl, supabaseKey);

async function setupKanbanPermissions() {
  try {
    console.log('Iniciando configuração de permissões do Kanban...');

    // 1. Verificar se a tabela kanban_column_permissions existe
    console.log('Verificando se a tabela kanban_column_permissions existe...');

    try {
      const { data: tableExists, error: tableError } = await supabase
        .from('kanban_column_permissions')
        .select('id')
        .limit(1);

      if (tableError && tableError.code === '42P01') { // Código para "relation does not exist"
        console.log('Tabela kanban_column_permissions não existe. Criando...');

        // Criar a tabela usando a API do Supabase
        // Nota: Não podemos criar tabelas diretamente com a API do Supabase
        // Precisamos usar o SQL, que requer permissões de administrador
        console.error('Não é possível criar a tabela sem permissões de administrador.');
        console.log('Por favor, crie a tabela manualmente no painel do Supabase.');

      } else {
        console.log('Tabela kanban_column_permissions já existe.');
      }
    } catch (error) {
      console.error('Erro ao verificar tabela:', error);
    }

    // 2. Buscar usuários administradores
    console.log('Buscando usuários administradores...');

    // Definir manualmente alguns usuários administradores para teste
    const adminUsers = [
      { id: '<EMAIL>' }, // Substitua pelo ID real do usuário admin
      { id: '<EMAIL>' } // Outro admin
    ];

    console.log(`Usando ${adminUsers.length} usuários administradores predefinidos.`);

    // 3. Buscar colunas do Kanban
    console.log('Buscando colunas do Kanban...');
    const { data: kanbanColumns, error: columnsError } = await supabase
      .from('kanban_colunas_personalizadas')
      .select('id')
      .eq('is_deleted', false);

    if (columnsError) {
      console.error('Erro ao buscar colunas do Kanban:', columnsError);
    } else {
      console.log(`Encontradas ${kanbanColumns?.length || 0} colunas do Kanban.`);
    }

    // 4. Adicionar permissões para administradores
    if (adminUsers && adminUsers.length > 0 && kanbanColumns && kanbanColumns.length > 0) {
      console.log('Adicionando permissões para administradores...');

      const permissionsToInsert = [];

      // Criar permissões para cada combinação de admin e coluna
      for (const admin of adminUsers) {
        for (const column of kanbanColumns) {
          permissionsToInsert.push({
            user_id: admin.id,
            column_id: column.id,
            can_view: true,
            can_edit: true,
            can_delete: true,
            can_move_cards: true
          });
        }
      }

      // Inserir permissões em lotes
      if (permissionsToInsert.length > 0) {
        const { data: insertedPermissions, error: insertError } = await supabase
          .from('kanban_column_permissions')
          .upsert(permissionsToInsert, { onConflict: 'user_id,column_id' });

        if (insertError) {
          console.error('Erro ao inserir permissões para administradores:', insertError);
        } else {
          console.log(`Inseridas ${permissionsToInsert.length} permissões para administradores.`);
        }
      }
    }

    // 5. Verificar permissões configuradas
    console.log('Verificando permissões configuradas...');

    const { data: permissions, error: permissionsError } = await supabase
      .from('kanban_column_permissions')
      .select('*');

    if (permissionsError) {
      console.error('Erro ao verificar permissões:', permissionsError);
    } else {
      console.log(`Foram configuradas ${permissions?.length || 0} permissões.`);
    }

    console.log('Configuração de permissões do Kanban concluída com sucesso!');
  } catch (error) {
    console.error('Erro durante a configuração de permissões do Kanban:', error);
    process.exit(1);
  }
}

// Executar a função principal
setupKanbanPermissions();
