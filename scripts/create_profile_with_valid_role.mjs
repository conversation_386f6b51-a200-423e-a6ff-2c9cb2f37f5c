// Script para criar um perfil com um valor válido para o enum user_role
import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';
dotenv.config();

// Configuração do Supabase
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://ubwzukpsqcrwzfbppoux.supabase.co';
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY; // Chave anônima para simular cliente

if (!supabaseAnonKey) {
  console.error('Erro: VITE_SUPABASE_ANON_KEY não está definida no arquivo .env');
  process.exit(1);
}

// Criar cliente Supabase com a chave anônima (para simular o cliente)
const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function createProfileWithValidRole() {
  try {
    console.log('Criando perfil com valor válido para o enum user_role...');
    
    // 1. Fazer login para obter o token de acesso
    const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'barcelona'
    });
    
    if (signInError) {
      console.error('Erro ao fazer login:', signInError.message);
      process.exit(1);
    }
    
    console.log('Login bem-sucedido!');
    console.log('ID do usuário:', signInData.user.id);
    
    // 2. Tentar diferentes valores para o enum user_role
    console.log('\nTestando valores para o enum user_role...');
    
    const possibleRoles = [
      'admin',
      'gerente_geral',
      'gerente_precatorio',
      'gerente_rpv',
      'captador',
      'operacional_precatorio',
      'operacional_rpv',
      'operacional_completo',
      'assistente'
    ];
    
    let validRole = null;
    
    for (const role of possibleRoles) {
      console.log(`Testando role: ${role}`);
      
      const { error: insertError } = await supabase
        .from('profiles')
        .insert({
          id: signInData.user.id,
          nome: 'Caio Justo',
          email: '<EMAIL>',
          role: role,
          cargo: 'Administrador',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          status: 'ativo',
          telefone: '+5511999999999',
          departamento: 'Diretoria'
        });
      
      if (!insertError) {
        console.log(`Role válida encontrada: ${role}`);
        validRole = role;
        break;
      } else if (!insertError.message.includes('invalid input value for enum user_role')) {
        console.log(`Erro diferente ao inserir com role ${role}:`, insertError.message);
        
        // Verificar se o erro é devido a uma violação de chave primária
        if (insertError.message.includes('duplicate key value violates unique constraint')) {
          console.log('O perfil já existe. Tentando atualizar...');
          
          // Tentar atualizar o perfil
          const { error: updateError } = await supabase
            .from('profiles')
            .update({
              nome: 'Caio Justo',
              email: '<EMAIL>',
              role: role,
              cargo: 'Administrador',
              updated_at: new Date().toISOString(),
              status: 'ativo',
              telefone: '+5511999999999',
              departamento: 'Diretoria'
            })
            .eq('id', signInData.user.id);
          
          if (!updateError) {
            console.log(`Perfil atualizado com sucesso usando role: ${role}`);
            validRole = role;
            break;
          } else {
            console.error(`Erro ao atualizar perfil com role ${role}:`, updateError.message);
          }
        }
      }
    }
    
    if (!validRole) {
      console.log('Nenhum valor válido encontrado para o enum user_role.');
      console.log('Tentando criar o perfil sem a coluna role...');
      
      const { error: insertError } = await supabase
        .from('profiles')
        .insert({
          id: signInData.user.id,
          nome: 'Caio Justo',
          email: '<EMAIL>',
          cargo: 'Administrador',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          status: 'ativo',
          telefone: '+5511999999999',
          departamento: 'Diretoria'
        });
      
      if (insertError) {
        console.error('Erro ao inserir perfil sem a coluna role:', insertError.message);
        
        // Verificar se o erro é devido a uma violação de chave primária
        if (insertError.message.includes('duplicate key value violates unique constraint')) {
          console.log('O perfil já existe. Tentando atualizar sem a coluna role...');
          
          // Tentar atualizar o perfil
          const { error: updateError } = await supabase
            .from('profiles')
            .update({
              nome: 'Caio Justo',
              email: '<EMAIL>',
              cargo: 'Administrador',
              updated_at: new Date().toISOString(),
              status: 'ativo',
              telefone: '+5511999999999',
              departamento: 'Diretoria'
            })
            .eq('id', signInData.user.id);
          
          if (updateError) {
            console.error('Erro ao atualizar perfil sem a coluna role:', updateError.message);
          } else {
            console.log('Perfil atualizado com sucesso sem a coluna role!');
          }
        }
      } else {
        console.log('Perfil inserido com sucesso sem a coluna role!');
      }
    }
    
    // 3. Verificar se o perfil foi inserido/atualizado
    console.log('\nVerificando se o perfil existe...');
    
    const { data: userProfile, error: userProfileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', signInData.user.id);
    
    if (userProfileError) {
      console.error('Erro ao verificar perfil do usuário:', userProfileError.message);
    } else if (userProfile.length === 0) {
      console.log('Perfil não encontrado!');
    } else {
      console.log('Perfil do usuário encontrado:');
      console.log(userProfile[0]);
    }
    
    console.log('\nCriação de perfil concluída!');
    console.log('Instruções para login:');
    console.log('1. Limpe os cookies e o armazenamento local do navegador');
    console.log('2. Acesse a aplicação em http://localhost:8082/');
    console.log('3. Faça login com as seguintes credenciais:');
    console.log('   - Email: <EMAIL>');
    console.log('   - Senha: barcelona');
    
  } catch (error) {
    console.error('Erro ao criar perfil com valor válido para o enum user_role:', error);
  } finally {
    process.exit(0);
  }
}

createProfileWithValidRole();
