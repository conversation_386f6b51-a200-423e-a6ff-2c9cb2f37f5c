// Script para criar um perfil compatível com a estrutura da tabela
import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';
dotenv.config();

// Configuração do Supabase
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://ubwzukpsqcrwzfbppoux.supabase.co';
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY; // Chave anônima para simular cliente

if (!supabaseAnonKey) {
  console.error('Erro: VITE_SUPABASE_ANON_KEY não está definida no arquivo .env');
  process.exit(1);
}

// Criar cliente Supabase com a chave anônima (para simular o cliente)
const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function createCompatibleProfile() {
  try {
    console.log('Criando perfil compatível...');
    
    // 1. Fazer login para obter o token de acesso
    const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'barcelona'
    });
    
    if (signInError) {
      console.error('Erro ao fazer login:', signInError.message);
      process.exit(1);
    }
    
    console.log('Login bem-sucedido!');
    console.log('ID do usuário:', signInData.user.id);
    
    // 2. Verificar a estrutura da tabela profiles
    console.log('\nVerificando estrutura da tabela profiles...');
    
    // Fazer uma consulta para obter todas as colunas
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .limit(1);
    
    if (error) {
      console.error('Erro ao verificar tabela profiles:', error.message);
      process.exit(1);
    }
    
    // Tentar inferir a estrutura da tabela
    let tableStructure = {};
    
    // Tentar diferentes combinações de colunas comuns
    const possibleColumns = [
      'id', 'user_id', 'name', 'nome', 'email', 'avatar_url', 'role', 'cargo',
      'created_at', 'updated_at', 'deleted_at', 'status', 'phone', 'telefone',
      'address', 'endereco', 'city', 'cidade', 'state', 'estado', 'country', 'pais',
      'zip_code', 'cep', 'bio', 'biografia', 'website', 'site', 'social_media', 'redes_sociais',
      'company', 'empresa', 'position', 'posicao', 'department', 'departamento',
      'birth_date', 'data_nascimento', 'gender', 'genero', 'language', 'idioma',
      'timezone', 'fuso_horario', 'preferences', 'preferencias', 'settings', 'configuracoes',
      'last_login', 'ultimo_login', 'is_active', 'ativo', 'is_verified', 'verificado',
      'is_admin', 'admin', 'is_staff', 'staff', 'is_superuser', 'superuser',
      'groups', 'grupos', 'permissions', 'permissoes', 'custom_role_id', 'papel_id'
    ];
    
    // Testar cada coluna possível
    for (const column of possibleColumns) {
      const { data: testData, error: testError } = await supabase
        .from('profiles')
        .select(column)
        .limit(1);
      
      if (!testError) {
        console.log(`Coluna encontrada: ${column}`);
        tableStructure[column] = true;
      }
    }
    
    console.log('\nEstrutura da tabela profiles:');
    console.log(Object.keys(tableStructure));
    
    // 3. Criar um perfil compatível com a estrutura da tabela
    console.log('\nCriando perfil compatível...');
    
    const profileData = {
      id: signInData.user.id
    };
    
    // Adicionar campos com base na estrutura da tabela
    if (tableStructure['name']) profileData.name = 'Caio Justo';
    if (tableStructure['nome']) profileData.nome = 'Caio Justo';
    if (tableStructure['email']) profileData.email = '<EMAIL>';
    if (tableStructure['avatar_url']) profileData.avatar_url = null;
    if (tableStructure['role']) profileData.role = 'authenticated';
    if (tableStructure['cargo']) profileData.cargo = 'Administrador';
    if (tableStructure['created_at']) profileData.created_at = new Date().toISOString();
    if (tableStructure['updated_at']) profileData.updated_at = new Date().toISOString();
    if (tableStructure['status']) profileData.status = 'ativo';
    if (tableStructure['phone'] || tableStructure['telefone']) {
      profileData[tableStructure['phone'] ? 'phone' : 'telefone'] = '+5511999999999';
    }
    if (tableStructure['custom_role_id']) profileData.custom_role_id = null;
    
    console.log('Dados do perfil a serem inseridos:');
    console.log(profileData);
    
    // Tentar inserir o perfil
    const { error: insertError } = await supabase
      .from('profiles')
      .insert(profileData);
    
    if (insertError) {
      console.error('Erro ao inserir perfil:', insertError.message);
      
      // Verificar se o erro é devido a uma violação de chave primária
      if (insertError.message.includes('duplicate key value violates unique constraint')) {
        console.log('O perfil já existe. Tentando atualizar...');
        
        // Remover o ID para atualização
        const updateData = { ...profileData };
        delete updateData.id;
        
        // Tentar atualizar o perfil
        const { error: updateError } = await supabase
          .from('profiles')
          .update(updateData)
          .eq('id', signInData.user.id);
        
        if (updateError) {
          console.error('Erro ao atualizar perfil:', updateError.message);
        } else {
          console.log('Perfil atualizado com sucesso!');
        }
      }
    } else {
      console.log('Perfil inserido com sucesso!');
    }
    
    // 4. Verificar se o perfil foi inserido/atualizado
    console.log('\nVerificando se o perfil existe...');
    
    const { data: userProfile, error: userProfileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', signInData.user.id);
    
    if (userProfileError) {
      console.error('Erro ao verificar perfil do usuário:', userProfileError.message);
    } else if (userProfile.length === 0) {
      console.log('Perfil não encontrado!');
    } else {
      console.log('Perfil do usuário encontrado:');
      console.log(userProfile[0]);
    }
    
    console.log('\nCriação de perfil compatível concluída!');
    console.log('Instruções para login:');
    console.log('1. Limpe os cookies e o armazenamento local do navegador');
    console.log('2. Acesse a aplicação em http://localhost:8082/');
    console.log('3. Faça login com as seguintes credenciais:');
    console.log('   - Email: <EMAIL>');
    console.log('   - Senha: barcelona');
    
  } catch (error) {
    console.error('Erro ao criar perfil compatível:', error);
  } finally {
    process.exit(0);
  }
}

createCompatibleProfile();
