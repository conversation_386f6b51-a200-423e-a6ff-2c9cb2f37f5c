// Script para criar o usuário administrador usando a API do Supabase
// Execute com: node scripts/create_admin.js

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Obter as variáveis de ambiente
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY; // Você precisa adicionar esta chave ao .env

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Erro: As variáveis de ambiente VITE_SUPABASE_URL e SUPABASE_SERVICE_KEY devem estar configuradas.');
  process.exit(1);
}

// Criar cliente Supabase com permissões de serviço
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createAdminUser() {
  try {
    console.log('Criando usuário administrador...');
    
    // 1. Criar usuário
    const { data: userData, error: userError } = await supabase.auth.admin.createUser({
      email: '<EMAIL>',
      password: 'Senha2025',
      email_confirm: true, // Confirmar email automaticamente
    });

    if (userError) {
      // Verificar se o erro é porque o usuário já existe
      if (userError.message.includes('already exists')) {
        console.log('Usuário <EMAIL> já existe. Atualizando papel para admin...');
        
        // Buscar o usuário existente para obter o ID
        const { data: existingUser, error: findError } = await supabase.auth.admin
          .listUsers();
        
        if (findError) {
          throw new Error(`Erro ao buscar usuário existente: ${findError.message}`);
        }
        
        const adminUser = existingUser.users.find(user => user.email === '<EMAIL>');
        
        if (!adminUser) {
          throw new Error('Usuário <EMAIL> não encontrado, mesmo após erro de "já existe".');
        }
        
        // Verificar se a coluna updated_at existe
        const { data: columnInfo, error: columnError } = await supabase.rpc(
          'check_column_exists',
          { 
            p_table_name: 'profiles', 
            p_column_name: 'updated_at' 
          }
        );
        
        // Se não conseguir verificar, tentamos com e sem o campo
        const hasUpdatedAt = columnError ? true : columnInfo;
        
        // Atualizar o perfil do usuário existente
        const updateData = { 
          role: 'admin',
          nome: 'Administrador',
          status: 'ativo'
        };
        
        // Adicionar updated_at se a coluna existir
        if (hasUpdatedAt) {
          updateData.updated_at = new Date();
        }
        
        const { error: updateError } = await supabase
          .from('profiles')
          .update(updateData)
          .eq('id', adminUser.id);
          
        if (updateError) {
          throw new Error(`Erro ao atualizar perfil existente: ${updateError.message}`);
        }
        
        console.log('Perfil de administrador atualizado com sucesso!');
        return;
      } else {
        throw new Error(`Erro ao criar usuário: ${userError.message}`);
      }
    }
    
    if (!userData || !userData.user) {
      throw new Error('Nenhum dado de usuário retornado após a criação.');
    }
    
    console.log(`Usuário criado com sucesso: ID ${userData.user.id}`);

    // 2. Criar perfil do usuário
    const { error: profileError } = await supabase
      .from('profiles')
      .insert({
        id: userData.user.id,
        email: '<EMAIL>',
        nome: 'Administrador',
        role: 'admin',
        status: 'ativo',
        created_at: new Date(),
        updated_at: new Date()
      });

    if (profileError) {
      throw new Error(`Erro ao criar perfil: ${profileError.message}`);
    }

    console.log('Usuário administrador criado com sucesso!');
    console.log('Email: <EMAIL>');
    console.log('Senha: Senha2025');
    
  } catch (error) {
    console.error('Erro:', error.message);
    process.exit(1);
  }
}

// Adicionar função RPC para verificar coluna (se não existir)
async function createHelperFunction() {
  try {
    const { error } = await supabase.rpc('create_check_column_exists_function');
    
    if (error && !error.message.includes('already exists')) {
      console.warn('Aviso: Não foi possível criar função auxiliar:', error.message);
    }
  } catch (error) {
    console.warn('Aviso: Erro ao criar função auxiliar:', error.message);
  }
}

// Executar em sequência
async function init() {
  try {
    await createHelperFunction();
    await createAdminUser();
  } catch (error) {
    console.error('Erro na inicialização:', error);
    process.exit(1);
  }
}

init(); 