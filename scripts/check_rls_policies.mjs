// Script para verificar as políticas de segurança da tabela profiles
import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';
dotenv.config();

// Configuração do Supabase
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://ubwzukpsqcrwzfbppoux.supabase.co';
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY; // Chave anônima para simular cliente

if (!supabaseAnonKey) {
  console.error('Erro: VITE_SUPABASE_ANON_KEY não está definida no arquivo .env');
  process.exit(1);
}

// Criar cliente Supabase com a chave anônima (para simular o cliente)
const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function checkRLSPolicies() {
  try {
    console.log('Verificando políticas de segurança...');
    
    // 1. Fazer login para obter o token de acesso
    const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'barcelona'
    });
    
    if (signInError) {
      console.error('Erro ao fazer login:', signInError.message);
      process.exit(1);
    }
    
    console.log('Login bem-sucedido!');
    console.log('ID do usuário:', signInData.user.id);
    console.log('Papel do usuário:', signInData.user.role);
    
    // 2. Verificar se o usuário pode acessar a tabela profiles
    console.log('\nVerificando acesso à tabela profiles...');
    
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .limit(1);
    
    if (profileError) {
      console.error('Erro ao acessar tabela profiles:', profileError.message);
    } else {
      console.log('Acesso à tabela profiles bem-sucedido!');
      console.log('Dados retornados:', profileData);
    }
    
    // 3. Tentar inserir um perfil para o usuário
    console.log('\nTentando inserir perfil para o usuário...');
    
    const { error: insertError } = await supabase
      .from('profiles')
      .insert({
        id: signInData.user.id,
        name: 'Caio Justo',
        email: '<EMAIL>',
        role: 'authenticated',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });
    
    if (insertError) {
      console.error('Erro ao inserir perfil:', insertError.message);
      
      // Verificar se o erro é devido a uma violação de chave primária
      if (insertError.message.includes('duplicate key value violates unique constraint')) {
        console.log('O perfil já existe. Tentando atualizar...');
        
        // Tentar atualizar o perfil
        const { error: updateError } = await supabase
          .from('profiles')
          .update({
            name: 'Caio Justo',
            email: '<EMAIL>',
            role: 'authenticated',
            updated_at: new Date().toISOString()
          })
          .eq('id', signInData.user.id);
        
        if (updateError) {
          console.error('Erro ao atualizar perfil:', updateError.message);
        } else {
          console.log('Perfil atualizado com sucesso!');
        }
      }
    } else {
      console.log('Perfil inserido com sucesso!');
    }
    
    // 4. Verificar se o perfil foi inserido/atualizado
    console.log('\nVerificando se o perfil existe...');
    
    const { data: userProfile, error: userProfileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', signInData.user.id)
      .single();
    
    if (userProfileError) {
      console.error('Erro ao verificar perfil do usuário:', userProfileError.message);
    } else {
      console.log('Perfil do usuário encontrado:');
      console.log(userProfile);
    }
    
    // 5. Verificar a sessão atual
    console.log('\nVerificando sessão atual...');
    
    const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError) {
      console.error('Erro ao verificar sessão:', sessionError.message);
    } else if (!sessionData.session) {
      console.error('Sessão não encontrada!');
    } else {
      console.log('Sessão válida!');
      console.log('Usuário autenticado:', sessionData.session.user.email);
      console.log('Papel do usuário:', sessionData.session.user.role);
      console.log('Token de acesso:', sessionData.session.access_token.substring(0, 20) + '...');
    }
    
    console.log('\nVerificação concluída!');
    console.log('Instruções para login:');
    console.log('1. Limpe os cookies e o armazenamento local do navegador');
    console.log('2. Acesse a aplicação em http://localhost:8082/');
    console.log('3. Faça login com as seguintes credenciais:');
    console.log('   - Email: <EMAIL>');
    console.log('   - Senha: barcelona');
    
  } catch (error) {
    console.error('Erro ao verificar políticas de segurança:', error);
  } finally {
    process.exit(0);
  }
}

checkRLSPolicies();
