// Script para reiniciar a aplicação e limpar dados do navegador
import { exec } from 'child_process';
import * as http from 'http';
import * as fs from 'fs';
import * as path from 'path';

// Função para criar um servidor HTTP simples
function createServer() {
  const htmlContent = `
<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Reiniciar Aplicação</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    h1 {
      color: #2563eb;
    }
    .card {
      background-color: #f3f4f6;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;
    }
    button {
      background-color: #2563eb;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
    }
    button:hover {
      background-color: #1d4ed8;
    }
    code {
      background-color: #e5e7eb;
      padding: 2px 4px;
      border-radius: 4px;
      font-family: monospace;
    }
    .success {
      color: #059669;
      font-weight: bold;
    }
    .warning {
      color: #d97706;
      font-weight: bold;
    }
    ol {
      padding-left: 20px;
    }
    li {
      margin-bottom: 10px;
    }
  </style>
</head>
<body>
  <h1>Reiniciar Aplicação</h1>
  
  <div class="card">
    <h2>Instruções para limpar dados do navegador</h2>
    <ol>
      <li>Abra as ferramentas de desenvolvedor (F12 ou Cmd+Option+I no Mac)</li>
      <li>Vá para a aba "Application" (ou "Aplicativo")</li>
      <li>No painel esquerdo, expanda "Storage" (ou "Armazenamento")</li>
      <li>Clique em "Clear site data" (ou "Limpar dados do site")</li>
      <li>Feche esta janela e abra a aplicação em <a href="http://localhost:8081" target="_blank">http://localhost:8081</a></li>
    </ol>
  </div>
  
  <div class="card">
    <h2>Credenciais de login</h2>
    <p><strong>Email:</strong> <EMAIL></p>
    <p><strong>Senha:</strong> barcelona</p>
  </div>
  
  <div class="card">
    <h2>Reiniciar servidor de desenvolvimento</h2>
    <p>Se a aplicação ainda não estiver funcionando, reinicie o servidor de desenvolvimento:</p>
    <code>npm run dev</code>
  </div>
  
  <p class="warning">Nota: Este servidor será encerrado automaticamente após 5 minutos.</p>
</body>
</html>
  `;

  const server = http.createServer((req, res) => {
    res.writeHead(200, { 'Content-Type': 'text/html' });
    res.end(htmlContent);
  });

  server.listen(8082, () => {
    console.log('Servidor de instruções iniciado em http://localhost:8082');
    console.log('Abra este URL no navegador para ver as instruções');
    
    // Abrir o navegador automaticamente
    const url = 'http://localhost:8082';
    const command = process.platform === 'darwin' ? `open "${url}"` : 
                   process.platform === 'win32' ? `start "${url}"` : 
                   `xdg-open "${url}"`;
    
    exec(command, (error) => {
      if (error) {
        console.error('Erro ao abrir o navegador:', error);
      }
    });
    
    // Encerrar o servidor após 5 minutos
    setTimeout(() => {
      console.log('Encerrando servidor de instruções...');
      server.close();
      process.exit(0);
    }, 5 * 60 * 1000);
  });
}

// Iniciar o servidor de instruções
createServer();

// Iniciar o servidor de desenvolvimento em segundo plano
console.log('Iniciando servidor de desenvolvimento...');
exec('npm run dev', (error, stdout, stderr) => {
  if (error) {
    console.error(`Erro ao iniciar o servidor: ${error.message}`);
    return;
  }
  if (stderr) {
    console.error(`Erro do servidor: ${stderr}`);
    return;
  }
  console.log(`Saída do servidor: ${stdout}`);
});
