// Script para criar um usuário <NAME_EMAIL>
import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';
dotenv.config();

// Configuração do Supabase
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://ubwzukpsqcrwzfbppoux.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY; // Chave de serviço (não a chave anônima)

if (!supabaseServiceKey) {
  console.error('Erro: SUPABASE_SERVICE_KEY não está definida no arquivo .env');
  process.exit(1);
}

// Criar cliente Supabase com a chave de serviço
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createAdminUser() {
  try {
    console.log('Configurando usuário <NAME_EMAIL>...');

    // 1. Verificar se o usuário já existe
    console.log('Buscando usuário por email...');
    const { data: users, error: usersError } = await supabase.auth.admin.listUsers();

    if (usersError) {
      throw usersError;
    }

    // Encontrar o usuário pelo email
    const existingUser = users.users.find(u => u.email === '<EMAIL>');
    let userId;

    if (!existingUser) {
      console.log('Usuário não encontrado, tentando criar...');

      // Criar usuário
      const { data: newUser, error: createError } = await supabase.auth.admin.createUser({
        email: '<EMAIL>',
        password: 'Senha@123',
        email_confirm: true, // Confirmar email automaticamente
        user_metadata: {
          nome: 'Mateus Administrador',
          role: 'admin'
        },
        app_metadata: {
          role: 'admin'
        }
      });

      if (createError) {
        throw createError;
      }

      userId = newUser.user.id;
      console.log('Usuário criado com sucesso:', userId);
    } else {
      userId = existingUser.id;
      console.log('Usuário encontrado, atualizando para administrador:', userId);

      // Redefinir a senha do usuário
      const { error: passwordError } = await supabase.auth.admin.updateUserById(
        userId,
        { password: 'Senha@123' }
      );

      if (passwordError) {
        console.warn('Aviso: Não foi possível redefinir a senha:', passwordError.message);
      } else {
        console.log('Senha redefinida com sucesso');
      }

      // Atualizar metadados do usuário
      const { error: updateError } = await supabase.auth.admin.updateUserById(
        userId,
        {
          user_metadata: {
            nome: 'Mateus Administrador',
            role: 'admin'
          },
          app_metadata: {
            role: 'admin'
          }
        }
      );

      if (updateError) {
        throw updateError;
      }

      console.log('Metadados do usuário atualizados com sucesso');
    }

    // 2. Verificar se o perfil foi criado automaticamente pelo trigger
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();

    if (profileError && profileError.code !== 'PGRST116') {
      // PGRST116 significa que não encontrou o registro, o que é esperado
      console.log('Perfil não encontrado, criando manualmente...');
    } else if (profileData) {
      console.log('Perfil já existe, atualizando...');
    }

    // 3. Criar ou atualizar o perfil
    const { error: upsertError } = await supabase
      .from('profiles')
      .upsert({
        id: userId,
        email: '<EMAIL>',
        nome: 'Mateus Administrador',
        role: 'admin',
        status: 'ativo',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

    if (upsertError) {
      throw upsertError;
    }

    console.log('Perfil criado/atualizado com sucesso');

    // 4. Atualizar o papel do usuário na tabela auth.users
    const { error: updateRoleError } = await supabase.auth.admin.updateUserById(
      userId,
      { role: 'admin' }
    );

    if (updateRoleError) {
      throw updateRoleError;
    }

    console.log('Papel do usuário atualizado para admin');

    // 5. Adicionar permissões específicas para o usuário
    // Verificar se a tabela custom_roles existe
    const { data: rolesData, error: rolesError } = await supabase
      .from('custom_roles')
      .select('id')
      .eq('nome', 'Administrador')
      .single();

    if (rolesError && rolesError.code !== 'PGRST116') {
      throw rolesError;
    }

    let adminRoleId;

    if (!rolesData) {
      // Criar papel de administrador se não existir
      const { data: newRole, error: createRoleError } = await supabase
        .from('custom_roles')
        .insert({
          nome: 'Administrador',
          descricao: 'Acesso completo ao sistema',
          cor: '#FF5733',
          icone: 'Shield',
          is_system: true
        })
        .select()
        .single();

      if (createRoleError) {
        throw createRoleError;
      }

      adminRoleId = newRole.id;
      console.log('Papel de administrador criado:', adminRoleId);
    } else {
      adminRoleId = rolesData.id;
      console.log('Papel de administrador encontrado:', adminRoleId);
    }

    // 6. Atualizar o perfil com o custom_role_id
    const { error: updateProfileError } = await supabase
      .from('profiles')
      .update({ custom_role_id: adminRoleId })
      .eq('id', userId);

    if (updateProfileError) {
      throw updateProfileError;
    }

    console.log('Perfil atualizado com o papel de administrador');

    // 7. Verificar se a tabela user_specific_permissions existe e adicionar permissões
    try {
      // Primeiro, verificar se a permissão já existe
      const { data: existingPermission } = await supabase
        .from('user_specific_permissions')
        .select('id')
        .eq('user_id', userId)
        .eq('resource_type', 'system')
        .eq('action', 'gerenciar_permissoes')
        .maybeSingle();

      if (existingPermission) {
        console.log('Permissão específica já existe, atualizando...');

        const { error: updatePermissionError } = await supabase
          .from('user_specific_permissions')
          .update({ allowed: true })
          .eq('id', existingPermission.id);

        if (updatePermissionError) {
          console.warn('Aviso: Não foi possível atualizar permissão específica:', updatePermissionError.message);
        } else {
          console.log('Permissão específica atualizada com sucesso');
        }
      } else {
        console.log('Adicionando permissão específica...');

        const { error: permissionError } = await supabase
          .from('user_specific_permissions')
          .insert({
            user_id: userId,
            resource_type: 'system',
            action: 'gerenciar_permissoes',
            allowed: true
          });

        if (permissionError) {
          console.warn('Aviso: Não foi possível adicionar permissão específica:', permissionError.message);
        } else {
          console.log('Permissão específica adicionada com sucesso');
        }
      }
    } catch (error) {
      console.warn('Aviso: Erro ao gerenciar permissão específica:', error.message);
    }

    console.log('Usuário administrador criado com sucesso!');
    console.log('Email: <EMAIL>');
    console.log('Senha: Senha@123');

  } catch (error) {
    console.error('Erro ao criar usuário administrador:', error);
  } finally {
    process.exit(0);
  }
}

createAdminUser();
