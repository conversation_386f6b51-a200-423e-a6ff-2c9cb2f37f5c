// Script para verificar o contexto de autenticação e o comportamento do aplicativo
import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';
dotenv.config();

// Configuração do Supabase
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://ubwzukpsqcrwzfbppoux.supabase.co';
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY; // Chave anônima para simular cliente

if (!supabaseAnonKey) {
  console.error('Erro: VITE_SUPABASE_ANON_KEY não está definida no arquivo .env');
  process.exit(1);
}

// Criar cliente Supabase com a chave anônima (para simular o cliente)
const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function checkAuthContext() {
  try {
    console.log('Verificando contexto de autenticação...');
    
    // 1. Fazer login para obter o token de acesso
    const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'barcelona'
    });
    
    if (signInError) {
      console.error('Erro ao fazer login:', signInError.message);
      process.exit(1);
    }
    
    console.log('Login bem-sucedido!');
    console.log('ID do usuário:', signInData.user.id);
    console.log('Papel do usuário:', signInData.user.role);
    console.log('Metadados do usuário:', signInData.user.user_metadata);
    console.log('Metadados da aplicação:', signInData.user.app_metadata);
    
    // 2. Verificar a sessão atual
    console.log('\nVerificando sessão atual...');
    
    const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError) {
      console.error('Erro ao verificar sessão:', sessionError.message);
    } else if (!sessionData.session) {
      console.error('Sessão não encontrada!');
    } else {
      console.log('Sessão válida!');
      console.log('Usuário autenticado:', sessionData.session.user.email);
      console.log('Papel do usuário:', sessionData.session.user.role);
      console.log('Token de acesso:', sessionData.session.access_token.substring(0, 20) + '...');
      
      // Decodificar o token JWT para verificar as claims
      const tokenParts = sessionData.session.access_token.split('.');
      if (tokenParts.length === 3) {
        try {
          const payload = JSON.parse(Buffer.from(tokenParts[1], 'base64').toString());
          console.log('\nClaims do token JWT:');
          console.log(payload);
        } catch (e) {
          console.error('Erro ao decodificar token JWT:', e.message);
        }
      }
    }
    
    // 3. Verificar se o usuário pode acessar a tabela profiles
    console.log('\nVerificando acesso à tabela profiles...');
    
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .limit(1);
    
    if (profileError) {
      console.error('Erro ao acessar tabela profiles:', profileError.message);
    } else {
      console.log('Acesso à tabela profiles bem-sucedido!');
      console.log('Dados retornados:', profileData);
    }
    
    // 4. Verificar se o usuário pode acessar outras tabelas
    console.log('\nVerificando acesso a outras tabelas...');
    
    // Lista de tabelas comuns para testar
    const tablesToTest = [
      'users',
      'auth_users',
      'clients',
      'clientes',
      'customers',
      'precatorios',
      'rpvs',
      'tarefas',
      'tasks',
      'documentos',
      'documents',
      'settings',
      'configuracoes',
      'roles',
      'papeis',
      'permissions',
      'permissoes'
    ];
    
    for (const table of tablesToTest) {
      const { data, error } = await supabase
        .from(table)
        .select('*')
        .limit(1);
      
      if (!error) {
        console.log(`Acesso à tabela ${table} bem-sucedido!`);
        console.log(`Dados retornados: ${data.length > 0 ? 'Sim' : 'Não'}`);
      }
    }
    
    console.log('\nVerificação do contexto de autenticação concluída!');
    console.log('Instruções para login:');
    console.log('1. Limpe os cookies e o armazenamento local do navegador');
    console.log('2. Acesse a aplicação em http://localhost:8082/');
    console.log('3. Faça login com as seguintes credenciais:');
    console.log('   - Email: <EMAIL>');
    console.log('   - Senha: barcelona');
    console.log('\nObservações:');
    console.log('- O usuário está autenticado com sucesso');
    console.log('- O papel do usuário é "authenticated"');
    console.log('- O erro 400 ao acessar a tabela profiles pode ser devido a políticas de segurança');
    console.log('- O aplicativo pode estar esperando um perfil com uma estrutura específica');
    console.log('- Recomendação: Verifique o código do aplicativo para entender como ele espera que o perfil seja estruturado');
    
  } catch (error) {
    console.error('Erro ao verificar contexto de autenticação:', error);
  } finally {
    process.exit(0);
  }
}

checkAuthContext();
