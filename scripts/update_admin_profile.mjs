// Script para atualizar o perfil do usuário administrador
import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';
dotenv.config();

// Configuração do Supabase
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://ubwzukpsqcrwzfbppoux.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY; // Chave de serviço (não a chave anônima)

if (!supabaseServiceKey) {
  console.error('Erro: SUPABASE_SERVICE_KEY não está definida no arquivo .env');
  process.exit(1);
}

// Criar cliente Supabase com a chave de serviço
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function updateAdminProfile() {
  try {
    console.log('Atualizando perfil do usuário administrador...');
    
    // 1. Obter o ID do usuário
    const { data: userData, error: userError } = await supabase.auth
      .signInWithPassword({
        email: '<EMAIL>',
        password: 'barcelona'
      });
    
    if (userError) {
      throw userError;
    }
    
    const userId = userData.user.id;
    console.log('ID do usuário:', userId);
    
    // 2. Verificar a estrutura da tabela profiles
    const { data: columnsData, error: columnsError } = await supabase
      .from('information_schema.columns')
      .select('column_name')
      .eq('table_name', 'profiles')
      .eq('table_schema', 'public');
    
    if (columnsError) {
      console.warn('Aviso ao verificar colunas da tabela profiles:', columnsError.message);
    } else {
      console.log('Colunas da tabela profiles:');
      const columns = columnsData.map(col => col.column_name);
      console.log(columns);
      
      // 3. Atualizar o perfil com base nas colunas disponíveis
      const profileData = {
        id: userId
      };
      
      // Adicionar campos com base nas colunas disponíveis
      if (columns.includes('name')) profileData.name = 'Caio Justo';
      if (columns.includes('occupation')) profileData.occupation = 'Administrador Principal';
      if (columns.includes('created_at')) profileData.created_at = new Date().toISOString();
      if (columns.includes('updated_at')) profileData.updated_at = new Date().toISOString();
      if (columns.includes('age_range')) profileData.age_range = '30-40';
      if (columns.includes('target_language')) profileData.target_language = 'Português';
      if (columns.includes('language_level')) profileData.language_level = 'Nativo';
      if (columns.includes('is_onboarding_complete')) profileData.is_onboarding_complete = true;
      if (columns.includes('onboarding_completed_at')) profileData.onboarding_completed_at = new Date().toISOString();
      if (columns.includes('is_subscribed')) profileData.is_subscribed = true;
      if (columns.includes('subscription_status')) profileData.subscription_status = true;
      if (columns.includes('phone_number')) profileData.phone_number = '+5511999999999';
      
      console.log('Dados do perfil a serem atualizados:', profileData);
      
      const { error: updateError } = await supabase
        .from('profiles')
        .upsert(profileData);
      
      if (updateError) {
        console.warn('Aviso ao atualizar perfil:', updateError.message);
      } else {
        console.log('Perfil atualizado com sucesso!');
      }
    }
    
    // 4. Verificar se a tabela custom_roles existe e criar se necessário
    try {
      const { data: rolesData, error: rolesError } = await supabase
        .from('custom_roles')
        .select('id')
        .eq('nome', 'Administrador')
        .maybeSingle();
      
      let adminRoleId;
      
      if (rolesError && rolesError.code !== 'PGRST116') {
        console.warn('Aviso ao verificar papéis:', rolesError.message);
      } else if (!rolesData) {
        // Criar papel de administrador
        console.log('Criando papel de administrador...');
        
        const { data: newRole, error: createRoleError } = await supabase
          .from('custom_roles')
          .insert({
            nome: 'Administrador',
            descricao: 'Acesso completo ao sistema',
            cor: '#FF5733',
            icone: 'Shield',
            is_system: true
          })
          .select()
          .single();
        
        if (createRoleError) {
          console.warn('Aviso ao criar papel de administrador:', createRoleError.message);
        } else {
          adminRoleId = newRole.id;
          console.log('Papel de administrador criado:', adminRoleId);
        }
      } else {
        adminRoleId = rolesData.id;
        console.log('Papel de administrador encontrado:', adminRoleId);
      }
      
      // Atualizar o perfil com o custom_role_id
      if (adminRoleId) {
        const { error: updateProfileError } = await supabase
          .from('profiles')
          .update({ custom_role_id: adminRoleId })
          .eq('id', userId);
        
        if (updateProfileError) {
          console.warn('Aviso ao atualizar papel no perfil:', updateProfileError.message);
        } else {
          console.log('Perfil atualizado com o papel de administrador');
        }
      }
    } catch (error) {
      console.warn('Aviso ao gerenciar papéis:', error.message);
    }
    
    console.log('Atualização do perfil concluída!');
    
  } catch (error) {
    console.error('Erro ao atualizar perfil do usuário administrador:', error);
  } finally {
    process.exit(0);
  }
}

updateAdminProfile();
