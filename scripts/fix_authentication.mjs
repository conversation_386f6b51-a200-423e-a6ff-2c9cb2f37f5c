// Script para verificar e corrigir problemas de autenticação
import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';
dotenv.config();

// Configuração do Supabase
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://ubwzukpsqcrwzfbppoux.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY; // Chave de serviço (não a chave anônima)
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY; // Chave anônima para simular cliente

if (!supabaseServiceKey) {
  console.error('Erro: SUPABASE_SERVICE_KEY não está definida no arquivo .env');
  process.exit(1);
}

// Criar cliente Supabase com a chave de serviço (para operações administrativas)
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

// Criar cliente Supabase com a chave anônima (para simular o cliente)
const supabaseClient = createClient(supabaseUrl, supabaseAnonKey);

async function fixAuthentication() {
  try {
    console.log('Verificando e corrigindo problemas de autenticação...');
    
    // 1. Verificar se o usuário existe
    const { data: users, error: usersError } = await supabaseAdmin.auth.admin.listUsers();
    
    if (usersError) {
      throw usersError;
    }
    
    const adminUser = users.users.find(u => u.email === '<EMAIL>');
    
    if (!adminUser) {
      console.error('Usuário <EMAIL> não encontrado!');
      
      // Criar o usuário se não existir
      console.log('Criando usuário administrador...');
      
      const { data: newUser, error: createError } = await supabaseAdmin.auth.admin.createUser({
        email: '<EMAIL>',
        password: 'barcelona',
        email_confirm: true,
        user_metadata: {
          nome: 'Caio Justo',
          role: 'admin'
        },
        app_metadata: {
          role: 'admin'
        }
      });
      
      if (createError) {
        throw createError;
      }
      
      console.log('Usuário criado com sucesso:', newUser.user.id);
    } else {
      console.log('Usuário encontrado:', adminUser.id);
      
      // 2. Redefinir a senha do usuário
      console.log('Redefinindo senha do usuário...');
      
      const { error: updateError } = await supabaseAdmin.auth.admin.updateUserById(
        adminUser.id,
        { password: 'barcelona' }
      );
      
      if (updateError) {
        console.warn('Aviso ao redefinir senha:', updateError.message);
      } else {
        console.log('Senha redefinida com sucesso');
      }
      
      // 3. Atualizar metadados do usuário
      console.log('Atualizando metadados do usuário...');
      
      const { error: metadataError } = await supabaseAdmin.auth.admin.updateUserById(
        adminUser.id,
        {
          user_metadata: {
            nome: 'Caio Justo',
            role: 'admin',
            email: '<EMAIL>',
            email_verified: true
          },
          app_metadata: {
            role: 'admin',
            provider: 'email',
            providers: ['email']
          }
        }
      );
      
      if (metadataError) {
        console.warn('Aviso ao atualizar metadados:', metadataError.message);
      } else {
        console.log('Metadados atualizados com sucesso');
      }
      
      // 4. Atualizar o papel do usuário
      console.log('Atualizando papel do usuário...');
      
      const { error: roleError } = await supabaseAdmin.auth.admin.updateUserById(
        adminUser.id,
        { role: 'admin' }
      );
      
      if (roleError) {
        console.warn('Aviso ao atualizar papel:', roleError.message);
      } else {
        console.log('Papel atualizado com sucesso');
      }
    }
    
    // 5. Testar login com o usuário
    console.log('\nTestando login com o usuário...');
    
    const { data: signInData, error: signInError } = await supabaseClient.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'barcelona'
    });
    
    if (signInError) {
      console.error('Erro ao fazer login:', signInError.message);
    } else {
      console.log('Login bem-sucedido!');
      console.log('Sessão criada com sucesso');
      console.log('Token de acesso:', signInData.session.access_token.substring(0, 20) + '...');
      console.log('Token de atualização:', signInData.session.refresh_token);
      console.log('Expira em:', new Date(signInData.session.expires_at * 1000).toLocaleString());
      
      // 6. Verificar se a sessão é válida
      console.log('\nVerificando sessão...');
      
      const { data: sessionData, error: sessionError } = await supabaseClient.auth.getSession();
      
      if (sessionError) {
        console.error('Erro ao verificar sessão:', sessionError.message);
      } else if (!sessionData.session) {
        console.error('Sessão não encontrada!');
      } else {
        console.log('Sessão válida!');
        console.log('Usuário autenticado:', sessionData.session.user.email);
        console.log('Papel do usuário:', sessionData.session.user.role);
      }
    }
    
    console.log('\nVerificação e correção concluídas!');
    console.log('Instruções para login:');
    console.log('1. Limpe os cookies e o armazenamento local do navegador');
    console.log('2. Acesse a aplicação em http://localhost:8082/');
    console.log('3. Faça login com as seguintes credenciais:');
    console.log('   - Email: <EMAIL>');
    console.log('   - Senha: barcelona');
    
  } catch (error) {
    console.error('Erro ao verificar e corrigir autenticação:', error);
  } finally {
    process.exit(0);
  }
}

fixAuthentication();
