// Script para criar a tabela kanban_column_permissions
import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';
dotenv.config();

// Configuração do Supabase
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://ubwzukpsqcrwzfbppoux.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY; // Chave de serviço (não a chave anônima)

if (!supabaseServiceKey) {
  console.error('Erro: SUPABASE_SERVICE_KEY não está definida no arquivo .env');
  process.exit(1);
}

// Criar cliente Supabase com a chave de serviço
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createKanbanColumnPermissionsTable() {
  try {
    console.log('Criando a tabela kanban_column_permissions...');

    // Criar a tabela
    const { error: createTableError } = await supabase
      .from('kanban_column_permissions')
      .insert([
        {
          id: '00000000-0000-0000-0000-000000000000',
          user_id: '00000000-0000-0000-0000-000000000000',
          column_id: '00000000-0000-0000-0000-000000000000',
          can_view: true,
          can_edit: false,
          can_delete: false
        }
      ]);

    if (createTableError) {
      if (createTableError.message.includes('does not exist')) {
        console.log('A tabela não existe. Criando tabela...');

        // Executar consulta SQL para criar a tabela
        const { data, error } = await supabase
          .from('kanban_column_permissions')
          .select('*')
          .limit(1);

        if (error && error.message.includes('does not exist')) {
          console.log('Confirmado que a tabela não existe. Criando via SQL...');

          // Tentar criar a tabela usando a API do Supabase
          const { data: queryData, error: queryError } = await supabase
            .from('_sql')
            .select('*')
            .eq('query', `
              CREATE TABLE IF NOT EXISTS public.kanban_column_permissions (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                user_id UUID NOT NULL,
                column_id UUID NOT NULL,
                can_view BOOLEAN NOT NULL DEFAULT true,
                can_edit BOOLEAN NOT NULL DEFAULT false,
                can_delete BOOLEAN NOT NULL DEFAULT false,
                created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                UNIQUE(user_id, column_id)
              );
            `);

          if (queryError) {
            console.warn('Erro ao criar tabela via SQL:', queryError.message);
            console.log('Tentando método alternativo...');

            // Tentar criar a tabela usando a API REST do Supabase
            console.log('Usando o método POST para criar a tabela...');

            // Tentar inserir um registro para forçar a criação da tabela
            const { error: insertError } = await supabase
              .from('kanban_column_permissions')
              .insert([
                {
                  user_id: '00000000-0000-0000-0000-000000000000',
                  column_id: '00000000-0000-0000-0000-000000000000',
                  can_view: true,
                  can_edit: false,
                  can_delete: false
                }
              ]);

            if (insertError) {
              console.warn('Erro ao inserir registro:', insertError.message);
            } else {
              console.log('Tabela criada com sucesso!');
            }
          } else {
            console.log('Tabela criada com sucesso via SQL!');
          }
        } else {
          console.log('A tabela já existe.');
        }
      } else {
        console.warn('Erro ao criar tabela:', createTableError.message);
      }
    } else {
      console.log('Tabela criada com sucesso!');
    }

    // Tentar obter um usuário para teste
    console.log('Obtendo um usuário para teste...');
    const { data: users, error: usersError } = await supabase
      .from('profiles')
      .select('id')
      .limit(1);

    if (usersError || !users || users.length === 0) {
      console.warn('Não foi possível obter usuários para teste:',
        usersError ? usersError.message : 'Nenhum usuário encontrado');
      return;
    }

    const testUserId = users[0].id;

    // Tentar obter uma coluna para teste
    console.log('Obtendo uma coluna para teste...');
    const { data: columns, error: columnsError } = await supabase
      .from('kanban_colunas_personalizadas')
      .select('id')
      .limit(1);

    if (columnsError || !columns || columns.length === 0) {
      console.warn('Não foi possível obter colunas para teste:',
        columnsError ? columnsError.message : 'Nenhuma coluna encontrada');
      return;
    }

    const testColumnId = columns[0].id;

    // Inserir um registro de teste
    console.log('Inserindo registro de teste...');
    const { data: insertData, error: insertError } = await supabase
      .from('kanban_column_permissions')
      .insert([
        {
          user_id: testUserId,
          column_id: testColumnId,
          can_view: true,
          can_edit: true,
          can_delete: false
        }
      ])
      .select();

    if (insertError) {
      console.warn('Erro ao inserir registro de teste:', insertError.message);
    } else {
      console.log('Registro de teste inserido com sucesso:', insertData);
    }

    console.log('Tabela kanban_column_permissions criada com sucesso!');

  } catch (error) {
    console.error('Erro ao criar tabela kanban_column_permissions:', error.message);
    process.exit(1);
  }
}

// Executar a função principal
createKanbanColumnPermissionsTable()
  .then(() => {
    console.log('Script concluído com sucesso!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Erro ao executar o script:', error.message);
    process.exit(1);
  });
