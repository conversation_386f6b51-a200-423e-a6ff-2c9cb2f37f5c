// Script para criar um perfil de administrador compatível com a estrutura esperada
import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';
dotenv.config();

// Configuração do Supabase
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://ubwzukpsqcrwzfbppoux.supabase.co';
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY; // Chave anônima para simular cliente

if (!supabaseAnonKey) {
  console.error('Erro: VITE_SUPABASE_ANON_KEY não está definida no arquivo .env');
  process.exit(1);
}

// Criar cliente Supabase com a chave anônima (para simular o cliente)
const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function createAdminProfile() {
  try {
    console.log('Criando perfil de administrador...');
    
    // 1. Fazer login para obter o token de acesso
    const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'barcelona'
    });
    
    if (signInError) {
      console.error('Erro ao fazer login:', signInError.message);
      process.exit(1);
    }
    
    console.log('Login bem-sucedido!');
    console.log('ID do usuário:', signInData.user.id);
    
    // 2. Criar o perfil de administrador com a estrutura esperada pelo AuthContext
    const profileData = {
      id: signInData.user.id,
      email: '<EMAIL>',
      nome: 'Caio Justo',
      role: 'admin', // Deve ser um dos valores definidos em UserRole
      foto_url: null,
      status: 'ativo',
      created_at: new Date().toISOString(),
      cargo: 'Administrador Principal',
      departamento: 'Diretoria',
      telefone: '+5511999999999',
      data_entrada: new Date().toISOString().split('T')[0]
    };
    
    console.log('Dados do perfil a serem inseridos:');
    console.log(profileData);
    
    // 3. Tentar inserir o perfil
    const { error: insertError } = await supabase
      .from('profiles')
      .insert(profileData);
    
    if (insertError) {
      console.error('Erro ao inserir perfil:', insertError.message);
      
      // Verificar se o erro é devido a uma violação de chave primária
      if (insertError.message.includes('duplicate key value violates unique constraint')) {
        console.log('O perfil já existe. Tentando atualizar...');
        
        // Tentar atualizar o perfil
        const { error: updateError } = await supabase
          .from('profiles')
          .update({
            email: profileData.email,
            nome: profileData.nome,
            role: profileData.role,
            foto_url: profileData.foto_url,
            status: profileData.status,
            cargo: profileData.cargo,
            departamento: profileData.departamento,
            telefone: profileData.telefone,
            data_entrada: profileData.data_entrada,
            updated_at: new Date().toISOString()
          })
          .eq('id', profileData.id);
        
        if (updateError) {
          console.error('Erro ao atualizar perfil:', updateError.message);
          
          // Se o erro for relacionado ao tipo enum, tentar com outros valores
          if (updateError.message.includes('invalid input value for enum')) {
            console.log('Erro de tipo enum. Tentando com outros valores...');
            
            // Tentar com cada um dos valores possíveis de UserRole
            const possibleRoles = [
              'admin',
              'gerente_geral',
              'gerente_precatorio',
              'gerente_rpv',
              'captador',
              'operacional_precatorio',
              'operacional_rpv',
              'operacional_completo'
            ];
            
            for (const role of possibleRoles) {
              console.log(`Tentando com role: ${role}`);
              
              const { error: roleUpdateError } = await supabase
                .from('profiles')
                .update({
                  email: profileData.email,
                  nome: profileData.nome,
                  role: role,
                  foto_url: profileData.foto_url,
                  status: profileData.status,
                  cargo: profileData.cargo,
                  departamento: profileData.departamento,
                  telefone: profileData.telefone,
                  data_entrada: profileData.data_entrada,
                  updated_at: new Date().toISOString()
                })
                .eq('id', profileData.id);
              
              if (!roleUpdateError) {
                console.log(`Perfil atualizado com sucesso usando role: ${role}`);
                break;
              }
            }
          }
        } else {
          console.log('Perfil atualizado com sucesso!');
        }
      }
    } else {
      console.log('Perfil inserido com sucesso!');
    }
    
    // 4. Verificar se o perfil foi inserido/atualizado
    console.log('\nVerificando se o perfil existe...');
    
    const { data: userProfile, error: userProfileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', signInData.user.id)
      .single();
    
    if (userProfileError) {
      console.error('Erro ao verificar perfil do usuário:', userProfileError.message);
    } else {
      console.log('Perfil do usuário encontrado:');
      console.log(userProfile);
      
      // 5. Salvar o perfil no localStorage para uso pelo AuthContext
      const userProfileForStorage = {
        id: userProfile.id,
        email: userProfile.email,
        nome: userProfile.nome,
        role: userProfile.role,
        foto_url: userProfile.foto_url,
        status: userProfile.status,
        cargo: userProfile.cargo,
        departamento: userProfile.departamento,
        telefone: userProfile.telefone,
        data_entrada: userProfile.data_entrada
      };
      
      console.log('Salvando perfil no localStorage...');
      localStorage.setItem('userProfile', JSON.stringify(userProfileForStorage));
      console.log('Perfil salvo no localStorage!');
    }
    
    console.log('\nCriação de perfil de administrador concluída!');
    console.log('Instruções para login:');
    console.log('1. Limpe os cookies e o armazenamento local do navegador');
    console.log('2. Acesse a aplicação em http://localhost:8082/');
    console.log('3. Faça login com as seguintes credenciais:');
    console.log('   - Email: <EMAIL>');
    console.log('   - Senha: barcelona');
    
  } catch (error) {
    console.error('Erro ao criar perfil de administrador:', error);
  } finally {
    process.exit(0);
  }
}

createAdminProfile();
