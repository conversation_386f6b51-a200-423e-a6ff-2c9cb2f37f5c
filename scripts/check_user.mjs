// Script para verificar se o usuário <EMAIL> existe
import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';
dotenv.config();

// Configuração do Supabase
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://ubwzukpsqcrwzfbppoux.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY; // Chave de serviço (não a chave anônima)

if (!supabaseServiceKey) {
  console.error('Erro: SUPABASE_SERVICE_KEY não está definida no arquivo .env');
  process.exit(1);
}

// Criar cliente Supabase com a chave de serviço
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkUser() {
  try {
    console.log('Verificando usuário <EMAIL>...');
    
    // Tentar fazer login com o usuário
    const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'Senha@123'
    });
    
    if (signInError) {
      console.error('Erro ao fazer login:', signInError.message);
    } else {
      console.log('Login bem-sucedido!');
      console.log('Usuário:', signInData.user);
      console.log('Sessão:', signInData.session);
    }
    
  } catch (error) {
    console.error('Erro ao verificar usuário:', error);
  }
}

checkUser();
