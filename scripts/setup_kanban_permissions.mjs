// Script para configurar permissões do Kanban
import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Obter o diretório atual
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuração do Supabase
// Valores hardcoded para teste - em produção, use variáveis de ambiente
const supabaseUrl = "https://ubwzukpsqcrwzfbppoux.supabase.co";
const supabaseKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVid3p1a3BzcWNyd3pmYnBwb3V4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzMwNzc1NzcsImV4cCI6MjA0ODY1MzU3N30.SusnN1yvYZRIivt4l3NYCimIUJChIt3oa9KmTWYiVA0";

console.log('Usando URL do Supabase:', supabaseUrl);
console.log('Chave configurada com sucesso');

if (!supabaseUrl || !supabaseKey) {
  console.error('Erro: Variáveis de ambiente VITE_SUPABASE_URL e VITE_SUPABASE_SERVICE_KEY/VITE_SUPABASE_ANON_KEY são necessárias.');
  process.exit(1);
}

// Criar cliente Supabase
const supabase = createClient(supabaseUrl, supabaseKey);

async function setupKanbanPermissions() {
  try {
    console.log('Iniciando configuração de permissões do Kanban...');

    // Verificar se a tabela kanban_column_permissions existe
    console.log('Verificando se a tabela kanban_column_permissions existe...');

    try {
      const { data: tableExists, error: tableError } = await supabase
        .from('kanban_column_permissions')
        .select('id')
        .limit(1);

      if (tableError && tableError.code === '42P01') { // Código para "relation does not exist"
        console.log('Tabela kanban_column_permissions não existe. Criando...');

        // Ler o script SQL para criar a tabela
        const createTableSql = fs.readFileSync(
          path.join(__dirname, 'ensure_kanban_column_permissions.sql'),
          'utf8'
        );

        // Executar o script SQL
        const { error: createError } = await supabase.rpc('executar_sql_seguro', {
          sql_query: createTableSql
        });

        if (createError) {
          console.error('Erro ao criar tabela:', createError);
          throw createError;
        }

        console.log('Tabela kanban_column_permissions criada com sucesso!');
      } else {
        console.log('Tabela kanban_column_permissions já existe.');
      }
    } catch (error) {
      console.error('Erro ao verificar tabela:', error);

      // Tentar criar a tabela mesmo assim
      console.log('Tentando criar a tabela mesmo assim...');

      // Ler o script SQL para criar a tabela
      const createTableSql = fs.readFileSync(
        path.join(__dirname, 'ensure_kanban_column_permissions.sql'),
        'utf8'
      );

      // Executar o script SQL
      try {
        const { error: createError } = await supabase.rpc('executar_sql_seguro', {
          sql_query: createTableSql
        });

        if (createError) {
          console.error('Erro ao criar tabela:', createError);
        } else {
          console.log('Tabela kanban_column_permissions criada com sucesso!');
        }
      } catch (execError) {
        console.error('Erro ao executar SQL para criar tabela:', execError);
      }
    }

    // Adicionar permissões padrão
    console.log('Adicionando permissões padrão...');

    // Ler o script SQL para adicionar permissões padrão
    const addPermissionsSql = fs.readFileSync(
      path.join(__dirname, 'add_default_kanban_permissions.sql'),
      'utf8'
    );

    // Executar o script SQL
    try {
      const { error: permissionsError } = await supabase.rpc('executar_sql_seguro', {
        sql_query: addPermissionsSql
      });

      if (permissionsError) {
        console.error('Erro ao adicionar permissões padrão:', permissionsError);
        throw permissionsError;
      }

      console.log('Permissões padrão adicionadas com sucesso!');
    } catch (execError) {
      console.error('Erro ao executar SQL para adicionar permissões:', execError);

      // Tentar executar as consultas individualmente
      console.log('Tentando executar as consultas individualmente...');

      // Dividir o script em consultas individuais
      const queries = addPermissionsSql.split(';')
        .map(query => query.trim())
        .filter(query => query.length > 0);

      for (let i = 0; i < queries.length; i++) {
        const query = queries[i];

        try {
          console.log(`Executando consulta ${i + 1}/${queries.length}...`);

          const { error: queryError } = await supabase.rpc('executar_sql_seguro', {
            sql_query: query + ';'
          });

          if (queryError) {
            console.error(`Erro ao executar consulta ${i + 1}:`, queryError);
          } else {
            console.log(`Consulta ${i + 1} executada com sucesso!`);
          }
        } catch (queryExecError) {
          console.error(`Erro ao executar consulta ${i + 1}:`, queryExecError);
        }
      }
    }

    // Verificar permissões configuradas
    console.log('Verificando permissões configuradas...');

    const { data: permissions, error: permissionsError } = await supabase
      .from('kanban_column_permissions')
      .select('*');

    if (permissionsError) {
      console.error('Erro ao verificar permissões:', permissionsError);
    } else {
      console.log(`Foram configuradas ${permissions.length} permissões.`);
    }

    console.log('Configuração de permissões do Kanban concluída com sucesso!');
  } catch (error) {
    console.error('Erro durante a configuração de permissões do Kanban:', error);
    process.exit(1);
  }
}

// Executar a função principal
setupKanbanPermissions();
