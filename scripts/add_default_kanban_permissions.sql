-- Script para adicionar permissões padrão para colunas do Kanban
-- Este script adiciona permissões para todos os usuários administradores
-- e configura permissões básicas para outros usuários

-- 1. <PERSON><PERSON><PERSON><PERSON> que a tabela kanban_column_permissions exista
CREATE TABLE IF NOT EXISTS kanban_column_permissions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  column_id UUID NOT NULL,
  can_view BOOLEAN NOT NULL DEFAULT true,
  can_edit BOOLEAN NOT NULL DEFAULT false,
  can_delete BOOLEAN NOT NULL DEFAULT false,
  can_move_cards BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, column_id)
);

-- 2. Adicionar permissões para administradores
-- Primeiro, identificar todos os usuários administradores
WITH admin_users AS (
  SELECT id FROM auth.users WHERE role = 'admin'
),
-- Obter todas as colunas do Kanban
kanban_columns AS (
  SELECT id FROM kanban_colunas_personalizadas WHERE is_deleted = false
)
-- Inserir permissões para cada combinação de admin e coluna
INSERT INTO kanban_column_permissions (user_id, column_id, can_view, can_edit, can_delete, can_move_cards)
SELECT 
  admin_users.id, 
  kanban_columns.id, 
  true, -- can_view
  true, -- can_edit
  true, -- can_delete
  true  -- can_move_cards
FROM 
  admin_users
CROSS JOIN 
  kanban_columns
ON CONFLICT (user_id, column_id) 
DO UPDATE SET 
  can_view = true,
  can_edit = true,
  can_delete = true,
  can_move_cards = true,
  updated_at = NOW();

-- 3. Adicionar permissões básicas para gerentes de precatórios
-- Primeiro, identificar todos os gerentes de precatórios
WITH gerente_users AS (
  SELECT id FROM auth.users WHERE role = 'gerente_precatorio'
),
-- Obter todas as colunas do Kanban
kanban_columns AS (
  SELECT id FROM kanban_colunas_personalizadas WHERE is_deleted = false
)
-- Inserir permissões para cada combinação de gerente e coluna
INSERT INTO kanban_column_permissions (user_id, column_id, can_view, can_edit, can_delete, can_move_cards)
SELECT 
  gerente_users.id, 
  kanban_columns.id, 
  true,  -- can_view
  true,  -- can_edit
  false, -- can_delete
  true   -- can_move_cards
FROM 
  gerente_users
CROSS JOIN 
  kanban_columns
ON CONFLICT (user_id, column_id) 
DO UPDATE SET 
  can_view = true,
  can_edit = true,
  can_delete = false,
  can_move_cards = true,
  updated_at = NOW();

-- 4. Adicionar permissões básicas para usuários operacionais
-- Primeiro, identificar todos os usuários operacionais
WITH operacional_users AS (
  SELECT id FROM auth.users 
  WHERE role IN ('operacional_precatorio', 'operacional_rpv', 'operacional_completo')
),
-- Obter todas as colunas do Kanban
kanban_columns AS (
  SELECT id FROM kanban_colunas_personalizadas WHERE is_deleted = false
)
-- Inserir permissões para cada combinação de usuário operacional e coluna
INSERT INTO kanban_column_permissions (user_id, column_id, can_view, can_edit, can_delete, can_move_cards)
SELECT 
  operacional_users.id, 
  kanban_columns.id, 
  true,  -- can_view
  false, -- can_edit
  false, -- can_delete
  true   -- can_move_cards
FROM 
  operacional_users
CROSS JOIN 
  kanban_columns
ON CONFLICT (user_id, column_id) 
DO UPDATE SET 
  can_view = true,
  can_edit = false,
  can_delete = false,
  can_move_cards = true,
  updated_at = NOW();

-- 5. Adicionar permissões básicas para captadores
-- Primeiro, identificar todos os captadores
WITH captador_users AS (
  SELECT id FROM auth.users WHERE role = 'captador'
),
-- Obter apenas as colunas iniciais do fluxo (primeiras 2 colunas por ordem)
initial_columns AS (
  SELECT id FROM kanban_colunas_personalizadas 
  WHERE is_deleted = false
  ORDER BY ordem
  LIMIT 2
)
-- Inserir permissões para cada combinação de captador e coluna inicial
INSERT INTO kanban_column_permissions (user_id, column_id, can_view, can_edit, can_delete, can_move_cards)
SELECT 
  captador_users.id, 
  initial_columns.id, 
  true,  -- can_view
  false, -- can_edit
  false, -- can_delete
  false  -- can_move_cards
FROM 
  captador_users
CROSS JOIN 
  initial_columns
ON CONFLICT (user_id, column_id) 
DO UPDATE SET 
  can_view = true,
  can_edit = false,
  can_delete = false,
  can_move_cards = false,
  updated_at = NOW();

-- 6. Verificar as permissões configuradas
SELECT 
  u.email as usuario,
  u.role as papel,
  c.nome as coluna,
  p.can_view,
  p.can_edit,
  p.can_delete,
  p.can_move_cards
FROM 
  kanban_column_permissions p
JOIN 
  auth.users u ON p.user_id = u.id
JOIN 
  kanban_colunas_personalizadas c ON p.column_id = c.id
ORDER BY 
  u.email, c.ordem;
