// Script para corrigir o perfil do usuário
import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';
dotenv.config();

// Configuração do Supabase
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://ubwzukpsqcrwzfbppoux.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY; // Chave de serviço (não a chave anônima)
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY; // Chave anônima para simular cliente

if (!supabaseServiceKey) {
  console.error('Erro: SUPABASE_SERVICE_KEY não está definida no arquivo .env');
  process.exit(1);
}

// Criar cliente Supabase com a chave de serviço (para operações administrativas)
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

// Criar cliente Supabase com a chave anônima (para simular o cliente)
const supabaseClient = createClient(supabaseUrl, supabaseAnonKey);

async function fixProfile() {
  try {
    console.log('Corrigindo perfil do usuário...');
    
    // 1. Fazer login para obter o ID do usuário
    const { data: signInData, error: signInError } = await supabaseClient.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'barcelona'
    });
    
    if (signInError) {
      console.error('Erro ao fazer login:', signInError.message);
      process.exit(1);
    }
    
    const userId = signInData.user.id;
    console.log('Login bem-sucedido!');
    console.log('ID do usuário:', userId);
    
    // 2. Verificar a estrutura da tabela profiles
    console.log('\nVerificando estrutura da tabela profiles...');
    
    // Fazer uma consulta simples para inferir a estrutura
    const { data: tableInfo, error: tableError } = await supabaseClient
      .from('profiles')
      .select('*')
      .limit(1);
    
    if (tableError && !tableError.message.includes('No rows found')) {
      console.error('Erro ao verificar tabela profiles:', tableError.message);
      
      // Verificar se o erro é devido à tabela não existir
      if (tableError.message.includes('relation "profiles" does not exist')) {
        console.log('A tabela profiles não existe! Precisamos criá-la.');
        
        // Criar a tabela profiles
        console.log('Criando tabela profiles...');
        
        const createTableSQL = `
          CREATE TABLE IF NOT EXISTS public.profiles (
            id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
            name TEXT,
            email TEXT,
            avatar_url TEXT,
            role TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );
          
          ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
          
          CREATE POLICY "Usuários podem ver seus próprios perfis"
            ON public.profiles
            FOR SELECT
            USING (auth.uid() = id);
            
          CREATE POLICY "Usuários podem atualizar seus próprios perfis"
            ON public.profiles
            FOR UPDATE
            USING (auth.uid() = id);
            
          CREATE POLICY "Administradores podem ver todos os perfis"
            ON public.profiles
            FOR SELECT
            USING (auth.jwt() ->> 'role' = 'authenticated');
            
          CREATE POLICY "Administradores podem atualizar todos os perfis"
            ON public.profiles
            FOR UPDATE
            USING (auth.jwt() ->> 'role' = 'authenticated');
        `;
        
        // Não podemos executar SQL diretamente com a API do Supabase JS
        console.log('Não é possível criar a tabela diretamente. Por favor, crie manualmente no Console do Supabase.');
      }
    } else {
      console.log('A tabela profiles existe.');
    }
    
    // 3. Verificar se o perfil do usuário existe
    const { data: userProfile, error: userProfileError } = await supabaseClient
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();
    
    if (userProfileError) {
      if (userProfileError.message.includes('No rows found')) {
        console.log('O perfil do usuário não existe! Criando perfil...');
        
        // Criar perfil do usuário
        const { error: insertError } = await supabaseClient
          .from('profiles')
          .insert({
            id: userId,
            name: 'Caio Justo',
            email: '<EMAIL>',
            role: 'authenticated',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });
        
        if (insertError) {
          console.error('Erro ao criar perfil:', insertError.message);
          
          // Tentar com o cliente admin
          console.log('Tentando criar perfil com cliente admin...');
          
          const { error: adminInsertError } = await supabaseAdmin
            .from('profiles')
            .insert({
              id: userId,
              name: 'Caio Justo',
              email: '<EMAIL>',
              role: 'authenticated',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            });
          
          if (adminInsertError) {
            console.error('Erro ao criar perfil com cliente admin:', adminInsertError.message);
          } else {
            console.log('Perfil criado com sucesso usando cliente admin!');
          }
        } else {
          console.log('Perfil criado com sucesso!');
        }
      } else {
        console.error('Erro ao verificar perfil do usuário:', userProfileError.message);
      }
    } else {
      console.log('Perfil do usuário encontrado:');
      console.log(userProfile);
      
      // Atualizar o perfil para garantir que está correto
      console.log('Atualizando perfil...');
      
      const { error: updateError } = await supabaseClient
        .from('profiles')
        .update({
          name: 'Caio Justo',
          email: '<EMAIL>',
          role: 'authenticated',
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);
      
      if (updateError) {
        console.error('Erro ao atualizar perfil:', updateError.message);
        
        // Tentar com o cliente admin
        console.log('Tentando atualizar perfil com cliente admin...');
        
        const { error: adminUpdateError } = await supabaseAdmin
          .from('profiles')
          .update({
            name: 'Caio Justo',
            email: '<EMAIL>',
            role: 'authenticated',
            updated_at: new Date().toISOString()
          })
          .eq('id', userId);
        
        if (adminUpdateError) {
          console.error('Erro ao atualizar perfil com cliente admin:', adminUpdateError.message);
        } else {
          console.log('Perfil atualizado com sucesso usando cliente admin!');
        }
      } else {
        console.log('Perfil atualizado com sucesso!');
      }
    }
    
    // 4. Atualizar o papel do usuário na tabela auth.users
    console.log('\nAtualizando papel do usuário...');
    
    const { error: updateRoleError } = await supabaseAdmin.auth.admin.updateUserById(
      userId,
      { 
        role: 'authenticated',
        user_metadata: {
          nome: 'Caio Justo',
          role: 'authenticated'
        },
        app_metadata: {
          role: 'authenticated'
        }
      }
    );
    
    if (updateRoleError) {
      console.error('Erro ao atualizar papel do usuário:', updateRoleError.message);
    } else {
      console.log('Papel do usuário atualizado para authenticated com sucesso!');
    }
    
    console.log('\nCorreção do perfil concluída!');
    console.log('Instruções para login:');
    console.log('1. Limpe os cookies e o armazenamento local do navegador');
    console.log('2. Acesse a aplicação em http://localhost:8082/');
    console.log('3. Faça login com as seguintes credenciais:');
    console.log('   - Email: <EMAIL>');
    console.log('   - Senha: barcelona');
    
  } catch (error) {
    console.error('Erro ao corrigir perfil do usuário:', error);
  } finally {
    process.exit(0);
  }
}

fixProfile();
