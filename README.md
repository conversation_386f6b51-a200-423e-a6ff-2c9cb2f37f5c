# Sistema de Gerenciamento de Precatórios

Um sistema moderno e completo para gerenciamento de precatórios, desenvolvido com React, TypeScript e Tailwind CSS.

## 🚀 Funcionalidades

- **Gestão de Precatórios**
  - Acompanhamento detalhado de precatórios
  - Timeline de atividades
  - Gestão de prazos e audiências
  - Documentos digitais
  - Relatórios e estatísticas

- **Dashboard Inteligente**
  - Visão geral do escritório
  - Indicadores de performance
  - Gráficos e métricas
  - Alertas importantes

- **Gestão de Funcionários**
  - Cadastro e gestão de equipe
  - Análise de performance
  - Distribuição de tarefas
  - Histórico de atividades

## 🛠️ Tecnologias

- React 18
- TypeScript
- Tailwind CSS
- Shadcn/ui
- React Query
- React Router
- Framer Motion
- Recharts
- Supabase

## 📦 Instalação

```bash
# Clone o repositório
git clone https://github.com/seu-usuario/gerenciadorescritorio-22.git

# Entre no diretório
cd gerenciadorescritorio-22

# Instale as dependências
npm install

# Inicie o servidor de desenvolvimento
npm run dev
```

## 🎯 Estrutura do Projeto

```
src/
  ├── components/     # Componentes reutilizáveis
  ├── pages/         # Páginas da aplicação
  ├── lib/           # Utilitários e configurações
  ├── integrations/  # Integrações com serviços externos
  └── styles/        # Estilos globais
```

## 🔒 Variáveis de Ambiente

Crie um arquivo `.env` na raiz do projeto:

```env
VITE_SUPABASE_URL=sua-url-do-supabase
VITE_SUPABASE_ANON_KEY=sua-chave-do-supabase
```

## 📱 Interface

O sistema possui uma interface moderna e responsiva, com:
- Tema claro/escuro
- Animações suaves
- Design adaptativo
- Componentes interativos
- Navegação intuitiva

## 🤝 Contribuindo

1. Faça um fork do projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo [LICENSE](LICENSE) para mais detalhes.

## 📞 Suporte

Para suporte, envie um <NAME_EMAIL> ou abra uma issue no GitHub.
