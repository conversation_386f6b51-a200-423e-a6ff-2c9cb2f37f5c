// This file uses environment variables for Supabase configuration
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

// Use environment variables for Supabase configuration
const SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL || '';
const SUPABASE_PUBLISHABLE_KEY = import.meta.env.VITE_SUPABASE_ANON_KEY || '';

// Log configuration for debugging
console.log('Supabase integration client configuration:');
console.log('URL:', SUPABASE_URL ? 'OK (defined)' : 'NOT DEFINED');
console.log('Key:', SUPABASE_PUBLISHABLE_KEY ? 'OK (defined)' : 'NOT DEFINED');

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);