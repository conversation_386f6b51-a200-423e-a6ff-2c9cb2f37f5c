import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useAuth } from '@/hooks/useAuth';
import {
  getUserPermissions,
  UserPermissionsData,
  checkPermission,
  checkPageAccess,
  checkTaskVisibility,
  clearPermissionsCache
} from '@/services/permissionsService';

interface PermissionsContextType {
  permissions: UserPermissionsData | null;
  loading: boolean;
  error: Error | null;
  hasPermission: (resourceType: string, action: string, resourceId?: string) => boolean;
  canAccessPage: (pagePath: string) => boolean;
  canViewTask: (taskId: string) => Promise<boolean>;
  refreshPermissions: () => Promise<void>;
}

export const PermissionsContext = createContext<PermissionsContextType | undefined>(undefined);

export const PermissionsProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { user } = useAuth();
  const [permissions, setPermissions] = useState<UserPermissionsData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  // Cache local para verificações de permissão
  const [permissionCache, setPermissionCache] = useState<Record<string, boolean>>({});
  const [pageAccessCache, setPageAccessCache] = useState<Record<string, boolean>>({});

  // Carregar permissões do usuário
  const loadPermissions = async () => {
    if (!user) {
      setPermissions(null);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);

      // Tentar obter permissões do usuário
      try {
        const userPermissions = await getUserPermissions(user.id);
        setPermissions(userPermissions);
        setError(null);
      } catch (err) {
        console.error('Erro ao carregar permissões do servidor:', err);

        // Criar permissões padrão baseadas no papel do usuário
        const defaultPermissions: UserPermissionsData = {
          role_permissions: [],
          specific_permissions: [],
          task_visibility: {
            can_see_own_tasks: true,
            can_see_team_tasks: user.role !== 'captador',
            can_see_all_tasks: user.role === 'admin' || user.role === 'gerente_geral',
            visible_user_ids: []
          },
          page_access: []
        };

        setPermissions(defaultPermissions);
      }
    } catch (err) {
      console.error('Erro ao carregar permissões:', err);
      setError(err as Error);
    } finally {
      setLoading(false);
    }
  };

  // Carregar permissões quando o usuário mudar
  useEffect(() => {
    loadPermissions();

    // Limpar caches quando o usuário mudar
    setPermissionCache({});
    setPageAccessCache({});
  }, [user?.id]);

  // Verificar se o usuário tem uma permissão específica
  const hasPermission = (resourceType: string, action: string, resourceId?: string): boolean => {
    if (!user || !permissions) return false;

    // Criar uma chave única para o cache
    const cacheKey = `${resourceType}:${action}:${resourceId || 'global'}`;

    // Verificar se já temos o resultado no cache
    if (permissionCache[cacheKey] !== undefined) {
      return permissionCache[cacheKey];
    }

    // Verificar se é admin (tem todas as permissões)
    if (user.role === 'admin') {
      // Atualizamos o cache em um useEffect para evitar setState durante renderização
      setTimeout(() => {
        setPermissionCache(prev => ({ ...prev, [cacheKey]: true }));
      }, 0);
      return true;
    }

    // Verificar permissões específicas primeiro
    const specificPermission = permissions.specific_permissions.find(
      p => p.resource_type === resourceType &&
           p.action === action &&
           (resourceId ? p.resource_id === resourceId : !p.resource_id)
    );

    if (specificPermission) {
      // Atualizamos o cache em um useEffect para evitar setState durante renderização
      setTimeout(() => {
        setPermissionCache(prev => ({ ...prev, [cacheKey]: specificPermission.allowed }));
      }, 0);
      return specificPermission.allowed;
    }

    // Verificar permissões baseadas em papel (role)
    const hasRolePermission = permissions.role_permissions.some(
      p => p.resource_type === resourceType && p.action === action
    );

    // Atualizamos o cache em um useEffect para evitar setState durante renderização
    setTimeout(() => {
      setPermissionCache(prev => ({ ...prev, [cacheKey]: hasRolePermission }));
    }, 0);
    return hasRolePermission;
  };

  // Verificar se o usuário pode acessar uma página
  const canAccessPage = (pagePath: string): boolean => {
    if (!user || !permissions) return false;

    // Verificar se já temos o resultado no cache
    if (pageAccessCache[pagePath] !== undefined) {
      return pageAccessCache[pagePath];
    }

    // Verificar se é admin (tem acesso a todas as páginas)
    if (user.role === 'admin') {
      // Atualizamos o cache em um setTimeout para evitar setState durante renderização
      setTimeout(() => {
        setPageAccessCache(prev => ({ ...prev, [pagePath]: true }));
      }, 0);
      return true;
    }

    // Verificar configurações específicas de acesso à página
    const pageAccess = permissions.page_access.find(p => p.page_path === pagePath);
    if (pageAccess) {
      // Atualizamos o cache em um setTimeout para evitar setState durante renderização
      setTimeout(() => {
        setPageAccessCache(prev => ({ ...prev, [pagePath]: pageAccess.can_access }));
      }, 0);
      return pageAccess.can_access;
    }

    // Se não houver configuração específica, verificar com base no papel (role)
    let hasAccess = false;

    switch (user.role) {
      case 'admin':
        hasAccess = true;
        break;
      case 'gerente_precatorio':
        hasAccess = true;
        break;
      case 'assistente':
        // Assistentes têm acesso limitado por padrão
        hasAccess = ['/dashboard', '/profile', '/tarefas', '/clientes'].includes(pagePath);
        break;
      default:
        hasAccess = false;
        break;
    }

    // Atualizamos o cache em um setTimeout para evitar setState durante renderização
    setTimeout(() => {
      setPageAccessCache(prev => ({ ...prev, [pagePath]: hasAccess }));
    }, 0);

    return hasAccess;
  };

  // Verificar se o usuário pode ver uma tarefa específica
  const canViewTask = async (taskId: string): Promise<boolean> => {
    if (!user) return false;

    try {
      return await checkTaskVisibility(user.id, taskId);
    } catch (error) {
      console.error('Erro ao verificar visibilidade da tarefa:', error);
      return false;
    }
  };

  // Atualizar permissões
  const refreshPermissions = async (): Promise<void> => {
    if (!user) return;

    // Limpar cache
    clearPermissionsCache(user.id);
    setPermissionCache({});
    setPageAccessCache({});

    // Recarregar permissões
    await loadPermissions();
  };

  return (
    <PermissionsContext.Provider
      value={{
        permissions,
        loading,
        error,
        hasPermission,
        canAccessPage,
        canViewTask,
        refreshPermissions
      }}
    >
      {children}
    </PermissionsContext.Provider>
  );
};

export const usePermissions = (): PermissionsContextType => {
  const context = useContext(PermissionsContext);
  if (context === undefined) {
    throw new Error('usePermissions must be used within a PermissionsProvider');
  }
  return context;
};
