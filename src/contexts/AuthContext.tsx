import React, { createContext, useState, useContext, useEffect, ReactNode, useRef } from "react";
import { supabase, refreshSupabaseSession, getUserProfile, saveUserProfile, syncUserProfile, USER_PROFILE_KEY } from "@/lib/supabase";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { ensureValidSession } from "@/lib/auth-helpers";
import { resetAuthentication } from "@/lib/auth-reset";
import { authManager } from "@/lib/authManager";
import { forceSessionCheck } from "@/lib/sessionHeartbeat";
import { initializeApplication, initializeUserSession, getInitConfigForDevice } from "@/services/appInitializationService";
import { authLogger } from "@/lib/logger";

type UserRole =
  | "admin"
  | "gerente_geral"
  | "gerente_precatorio"
  | "gerente_rpv"
  | "captador"
  | "operacional_precatorio"
  | "operacional_rpv"
  | "operacional_completo";

type PermissionType =
  | 'visualizar_clientes'
  | 'criar_clientes'
  | 'editar_clientes'
  | 'excluir_clientes'
  | 'visualizar_funcionarios'
  | 'criar_funcionarios'
  | 'editar_funcionarios'
  | 'excluir_funcionarios'
  | 'visualizar_precatorios'
  | 'criar_precatorios'
  | 'editar_precatorios'
  | 'excluir_precatorios'
  | 'visualizar_tarefas'
  | 'criar_tarefas'
  | 'editar_tarefas'
  | 'excluir_tarefas'
  | 'visualizar_automacao'
  | 'criar_automacao'
  | 'editar_automacao'
  | 'excluir_automacao'
  | 'visualizar_relatorios'
  | 'gerenciar_permissoes';

interface UserProfile {
  id: string;
  email: string;
  nome?: string;
  role: UserRole;
  foto_url?: string;
  status?: string;
  created_at?: string;
  updated_at?: string;
  cargo?: string;
  departamento?: string;
  telefone?: string;
  data_entrada?: string;
  custom_role_id?: string;
}

interface AuthContextType {
  user: UserProfile | null;
  isLoading: boolean;
  isAdmin: boolean;
  isGerente: boolean;
  permissions: PermissionType[];
  hasPermission: (permission: PermissionType) => boolean;
  signIn: (email: string, password: string) => Promise<{ error?: string }>;
  signOut: () => Promise<void>;
  updateUserProfile: (data: Partial<UserProfile>) => Promise<{ error?: string }>;
  createUser: (email: string, password: string, userData: Partial<UserProfile>) => Promise<{ error?: string, user?: UserProfile }>;
  updateUser: (userId: string, userData: Partial<UserProfile>) => Promise<{ error?: string, user?: UserProfile }>;
  deleteUser: (userId: string) => Promise<{ error?: string, success?: boolean }>;
  getAllUsers: () => Promise<{ data?: UserProfile[], error?: string }>;
  updateUserPermissions: (userId: string, role: UserRole) => Promise<{ error?: string, success?: boolean }>;
  refreshSession: () => Promise<boolean>;
  resetAuth: () => Promise<boolean>;
}

// Permissões padrão para cada papel
const DEFAULT_PERMISSIONS: Record<UserRole, PermissionType[]> = {
  admin: [
    'visualizar_clientes', 'criar_clientes', 'editar_clientes', 'excluir_clientes',
    'visualizar_funcionarios', 'criar_funcionarios', 'editar_funcionarios', 'excluir_funcionarios',
    'visualizar_precatorios', 'criar_precatorios', 'editar_precatorios', 'excluir_precatorios',
    'visualizar_tarefas', 'criar_tarefas', 'editar_tarefas', 'excluir_tarefas',
    'visualizar_automacao', 'criar_automacao', 'editar_automacao', 'excluir_automacao',
    'visualizar_relatorios', 'gerenciar_permissoes'
  ],
  gerente_geral: [
    'visualizar_clientes', 'criar_clientes', 'editar_clientes',
    'visualizar_funcionarios',
    'visualizar_precatorios', 'criar_precatorios', 'editar_precatorios',
    'visualizar_tarefas', 'criar_tarefas', 'editar_tarefas',
    'visualizar_relatorios'
  ],
  gerente_precatorio: [
    'visualizar_clientes', 'criar_clientes', 'editar_clientes',
    'visualizar_precatorios', 'criar_precatorios', 'editar_precatorios',
    'visualizar_tarefas', 'criar_tarefas', 'editar_tarefas',
    'visualizar_relatorios'
  ],
  gerente_rpv: [
    'visualizar_clientes', 'criar_clientes', 'editar_clientes',
    'visualizar_precatorios', 'criar_precatorios', 'editar_precatorios',
    'visualizar_tarefas', 'criar_tarefas', 'editar_tarefas',
    'visualizar_relatorios'
  ],
  captador: [
    'visualizar_clientes', 'criar_clientes',
    'visualizar_precatorios',
    'visualizar_tarefas',
    'visualizar_relatorios'
  ],
  operacional_precatorio: [
    'visualizar_clientes',
    'visualizar_precatorios',
    'visualizar_tarefas', 'criar_tarefas', 'editar_tarefas'
  ],
  operacional_rpv: [
    'visualizar_clientes',
    'visualizar_precatorios',
    'visualizar_tarefas', 'criar_tarefas', 'editar_tarefas'
  ],
  operacional_completo: [
    'visualizar_clientes',
    'visualizar_precatorios',
    'visualizar_tarefas', 'criar_tarefas', 'editar_tarefas'
  ]
};

export const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<UserProfile | null>(null);
  const [permissions, setPermissions] = useState<PermissionType[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAppInitialized, setIsAppInitialized] = useState(false);
  const navigate = useNavigate();
  const initializationRef = useRef(false);

  // Função para carregar permissões baseadas no papel do usuário
  const loadPermissions = (role: UserRole) => {
    if (!DEFAULT_PERMISSIONS[role]) {
      console.warn(`AuthContext: Papel desconhecido: ${role}, usando permissões vazias`);
      setPermissions([]);
      return;
    }
    setPermissions(DEFAULT_PERMISSIONS[role] || []);
  };

  // Função para inicializar a aplicação
  const initializeApp = async () => {
    if (initializationRef.current) {
      return; // Já inicializado
    }

    try {
      initializationRef.current = true;
      console.log('AuthContext: Iniciando inicialização da aplicação...');

      // Obter configuração otimizada para o dispositivo
      const initConfig = getInitConfigForDevice();

      // Inicializar aplicação
      const result = await initializeApplication(initConfig);

      if (result.success) {
        console.log(`AuthContext: Aplicação inicializada com sucesso em ${result.duration}ms`);
        setIsAppInitialized(true);
      } else {
        console.warn('AuthContext: Inicialização da aplicação teve problemas:', result.errors);
        setIsAppInitialized(true); // Continue mesmo com problemas
      }
    } catch (error) {
      console.error('AuthContext: Erro na inicialização da aplicação:', error);
      setIsAppInitialized(true); // Continue mesmo com erro
    }
  };

  // Função para inicializar sessão do usuário
  const initializeUserSessionAsync = async (userProfile: UserProfile) => {
    try {
      console.log(`AuthContext: Inicializando sessão do usuário ${userProfile.id}...`);
      await initializeUserSession(userProfile.id, userProfile.role);
      console.log('AuthContext: Sessão do usuário inicializada com sucesso');
    } catch (error) {
      console.error('AuthContext: Erro na inicialização da sessão do usuário:', error);
    }
  };

  // Função segura para buscar todos os usuários sem causar recursão infinita
  const fetchAllUsersSafely = async () => {
    try {
      console.log("AuthContext: Buscando todos os usuários de forma segura");

      // Verificar se já temos usuários em cache
      const cachedUsersStr = localStorage.getItem('cached_users');
      let cachedUsers = null;

      if (cachedUsersStr) {
        try {
          cachedUsers = JSON.parse(cachedUsersStr);
          console.log("AuthContext: Encontrados", cachedUsers.length, "usuários em cache");
        } catch (e) {
          console.error("AuthContext: Erro ao analisar usuários em cache:", e);
        }
      }

      // Tentar obter usuários do Supabase (filtrar usuários excluídos)
      console.log("AuthContext: Consultando banco de dados para usuários");
      let { data: users, error } = await supabase
        .from("profiles")
        .select("id, email, nome, role, status, foto_url, created_at")
        .neq("status", "excluido")
        .order("created_at", { ascending: false });

      if (error) {
        console.error("AuthContext: Erro ao buscar usuários do banco de dados:", error);

        // Se temos usuários em cache, usá-los como fallback
        if (cachedUsers && cachedUsers.length > 0) {
          console.log("AuthContext: Usando usuários em cache como fallback após erro");
          return { data: cachedUsers };
        }

        // Se não temos cache, criar dados mockados
        console.log("AuthContext: Criando usuários mockados devido a erro");
        throw error;
      }

      if (!users || users.length === 0) {
        console.log("AuthContext: Nenhum usuário encontrado no banco de dados");

        // Se temos usuários em cache, usá-los como fallback
        if (cachedUsers && cachedUsers.length > 0) {
          console.log("AuthContext: Usando usuários em cache como fallback");
          return { data: cachedUsers };
        }

        // Se não temos cache e nenhum usuário, criar dados mockados
        console.log("AuthContext: Criando usuários mockados");
        throw new Error("Nenhum usuário encontrado");
      }

      // Atualizar o cache local
      localStorage.setItem('cached_users', JSON.stringify(users));

      console.log("AuthContext: Retornando", users.length, "usuários do banco de dados");
      return { data: users };
    } catch (error) {
      console.error("AuthContext: Erro ao buscar usuários:", error);

      // Criar dados mockados como último recurso
      console.log("AuthContext: Criando usuários mockados devido a um erro");

      // Incluir o usuário atual se disponível
      const mockUsers: UserProfile[] = [];

      if (user) {
        mockUsers.push({
          ...user,
          // Garantir que o ID do usuário atual seja válido
          id: user.id.includes('-') ? user.id : `00000000-0000-0000-0000-${user.id.toString().padStart(12, '0')}`,
        });
      }

      // Adicionar mais alguns usuários mokados apenas se não temos o usuário atual
      if (mockUsers.length === 0) {
        mockUsers.push({
          id: '00000000-0000-0000-0000-000000000001',
          email: '<EMAIL>',
          nome: 'Administrador',
          role: 'admin',
          status: 'ativo'
        });

        mockUsers.push({
          id: '00000000-0000-0000-0000-000000000002',
          email: '<EMAIL>',
          nome: 'Gerente',
          role: 'gerente_geral',
          status: 'ativo'
        });

        mockUsers.push({
          id: '00000000-0000-0000-0000-000000000003',
          email: '<EMAIL>',
          nome: 'Operacional',
          role: 'operacional_completo',
          status: 'ativo'
        });
      }

      // Salvar os usuários mockados no localStorage para uso futuro
      localStorage.setItem('cached_users', JSON.stringify(mockUsers));

      console.log("AuthContext: Retornando", mockUsers.length, "usuários mockados");
      return { data: mockUsers };
    }
  };

  // Função segura para buscar perfil de usuário sem causar recursão infinita
  const fetchUserProfileSafely = async (userId: string) => {
    try {
      console.log("AuthContext: Buscando perfil de forma segura para:", userId);

      // Verificar se o ID é válido
      if (!userId || userId === 'undefined') {
        console.error("AuthContext: ID de usuário inválido");
        return { error: new Error("ID de usuário inválido") };
      }

      // Primeiro, realizar uma consulta simples para verificar se o perfil existe
      const { data: idCheck, error: idError } = await supabase
        .from("profiles")
        .select("id")
        .eq("id", userId)
        .maybeSingle();

      if (idError) {
        console.error("AuthContext: Erro ao verificar existência do ID:", idError);
        return { error: idError };
      }

      if (!idCheck) {
        console.error("AuthContext: Perfil não encontrado para ID:", userId);
        return { error: new Error("Perfil não encontrado") };
      }

      // Agora buscamos os campos específicos que precisamos, sem joins
      const { data: perfil, error: perfilError } = await supabase
        .from("profiles")
        .select("id, email, nome, role, status, foto_url, created_at")
        .eq("id", userId)
        .maybeSingle();

      if (perfilError) {
        console.error("AuthContext: Erro ao buscar perfil:", perfilError);
        return { error: perfilError };
      }

      if (!perfil) {
        console.error("AuthContext: Dados do perfil vazios para ID:", userId);
        return { error: new Error("Dados do perfil vazios") };
      }

      console.log("AuthContext: Perfil obtido com sucesso:", perfil.nome);
      return { data: perfil };
    } catch (error) {
      console.error("AuthContext: Erro crítico ao buscar perfil:", error);
      return { error };
    }
  };

  // Função para forçar o refresh da sessão com implementação ultra-robusta
  const refreshSession = async (): Promise<boolean> => {
    try {
      console.log("AuthContext: Iniciando refresh de sessão ultra-robusto");
      setIsLoading(true);

      // Usar diretamente a implementação robusta do refreshSupabaseSession
      console.log("AuthContext: Usando implementação robusta de refreshSupabaseSession");
      const success = await refreshSupabaseSession();

      if (success) {
        console.log("AuthContext: Sessão atualizada com sucesso");

        // Tentar carregar o perfil do usuário de todas as fontes possíveis
        await loadUserProfileFromAllSources();

        setIsLoading(false);

        // Disparar evento de reconexão para atualizar os componentes
        const reconnectEvent = new CustomEvent('app-reconnected', {
          detail: {
            timestamp: Date.now(),
            source: 'auth-context-robust'
          }
        });
        document.dispatchEvent(reconnectEvent);

        return true;
      }

      // Se a atualização falhar, tentar uma abordagem mais agressiva
      console.warn("AuthContext: Falha na atualização de sessão, tentando abordagem agressiva");

      // Tentar obter a sessão atual
      const { data: sessionData } = await supabase.auth.getSession();

      if (sessionData.session) {
        console.log("AuthContext: Sessão atual encontrada, tentando usar");

        // Tentar carregar o perfil do usuário
        await loadUserProfileFromAllSources();

        setIsLoading(false);
        return true;
      }

      // Tentar recuperar a sessão do localStorage e sessionStorage
      const userProfileStr = localStorage.getItem("userProfile");

      if (userProfileStr) {
        try {
          console.log("AuthContext: Perfil encontrado no localStorage, tentando recuperar sessão");
          const userProfile = JSON.parse(userProfileStr);

          // Definir o usuário mesmo sem sessão válida
          setUser(userProfile);
          loadPermissions(userProfile.role);

          // Tentar iniciar o gerenciador de autenticação para recuperação em segundo plano
          authManager.start();

          // Disparar evento de problema de sessão para que o SessionRecoveryManager tente recuperar
          const sessionProblemEvent = new CustomEvent('session-problem', {
            detail: {
              timestamp: Date.now(),
              message: 'Falha ao atualizar sessão, mas perfil recuperado'
            }
          });
          document.dispatchEvent(sessionProblemEvent);

          setIsLoading(false);
          return true;
        } catch (e) {
          console.error("AuthContext: Erro ao carregar perfil do localStorage", e);
        }
      }

      console.warn("AuthContext: Todas as tentativas de recuperação falharam");
      setIsLoading(false);
      return false;
    } catch (error) {
      console.error("AuthContext: Erro crítico ao forçar refresh da sessão", error);
      setIsLoading(false);
      return false;
    }
  };

  // Função auxiliar para carregar o perfil do usuário de todas as fontes possíveis
  const loadUserProfileFromAllSources = async (): Promise<boolean> => {
    try {
      // Tentar carregar o perfil usando a função robusta
      const userProfile = getUserProfile();

      if (userProfile) {
        setUser(userProfile);
        loadPermissions(userProfile.role);
        console.log("AuthContext: Perfil carregado com sucesso usando getUserProfile");

        // Garantir que o perfil esteja sincronizado
        syncUserProfile();

        return true;
      }

      // Se não encontrar em nenhum lugar, tentar buscar do banco de dados
      const { data: sessionData } = await supabase.auth.getSession();

      if (sessionData.session) {
        try {
          const { data: profileData, error: profileError } = await fetchUserProfileSafely(sessionData.session.user.id);

          if (!profileError && profileData) {
            const userProfile = {
              id: profileData.id,
              email: profileData.email || sessionData.session.user.email || '',
              nome: profileData.nome,
              role: profileData.role,
              foto_url: profileData.foto_url,
              status: profileData.status,
            };

            setUser(userProfile);
            loadPermissions(userProfile.role);

            // Salvar o perfil usando a função robusta
            saveUserProfile(userProfile);

            console.log("AuthContext: Perfil carregado do banco de dados e salvo");
            return true;
          }
        } catch (e) {
          console.error("AuthContext: Erro ao buscar perfil do banco de dados", e);
        }
      }

      return false;
    } catch (error) {
      console.error("AuthContext: Erro ao carregar perfil do usuário", error);
      return false;
    }
  };

  // Função para resetar a autenticação
  const resetAuth = async (): Promise<boolean> => {
    try {
      console.log("AuthContext: Resetando autenticação");
      setIsLoading(true);

      // Resetar a autenticação
      await resetAuthentication();

      // Limpar o estado
      setUser(null);
      setPermissions([]);

      setIsLoading(false);
      return true;
    } catch (error) {
      console.error("AuthContext: Erro ao resetar autenticação", error);
      setIsLoading(false);
      return false;
    }
  };

  // Listener para eventos de sessão
  useEffect(() => {
    // Manipulador para o evento de sessão expirada
    const handleSessionExpired = (event: CustomEvent) => {
      console.log("AuthContext: Evento de sessão expirada recebido", event.detail);

      // Limpar o estado
      setUser(null);
      setPermissions([]);

      // Recarregar a página para garantir um estado limpo
      if (event.detail?.forceReload) {
        console.log("AuthContext: Recarregando a página devido à sessão expirada");
        window.location.reload();
      }
      // Caso contrário, o SessionManager já faz o redirecionamento
    };

    // Manipulador para o evento de reconexão
    const handleReconnect = (event: CustomEvent) => {
      console.log("AuthContext: Evento de reconexão recebido", event.detail);

      // Usar a função robusta para recuperar o perfil
      const userProfile = getUserProfile();

      if (userProfile) {
        // Atualizar o estado apenas se o usuário for diferente ou não estiver definido
        if (!user || user.id !== userProfile.id) {
          console.log("AuthContext: Atualizando usuário após reconexão");
          setUser(userProfile);
          loadPermissions(userProfile.role);
        }

        // Garantir que o perfil esteja sincronizado
        syncUserProfile();
      } else {
        console.warn("AuthContext: Não foi possível recuperar o perfil após reconexão");

        // Tentar carregar o perfil de todas as fontes
        loadUserProfileFromAllSources().then(success => {
          if (!success) {
            console.error("AuthContext: Falha ao carregar perfil após reconexão");
          }
        });
      }
    };

    // Registrar os listeners
    document.addEventListener('session-expired', handleSessionExpired as EventListener);
    document.addEventListener('app-reconnected', handleReconnect as EventListener);

    // Limpar os listeners ao desmontar
    return () => {
      document.removeEventListener('session-expired', handleSessionExpired as EventListener);
      document.removeEventListener('app-reconnected', handleReconnect as EventListener);
    };
  }, [user]);

  // Variável para controlar a última verificação de sessão
  const lastAuthCheckRef = useRef<number>(0);
  const MIN_AUTH_CHECK_INTERVAL = 30000; // 30 segundos entre verificações

  useEffect(() => {
    let isActive = true;

    // Função para verificar a sessão atual com retentativas
    const checkSession = async () => {
      try {
        // Evitar verificações muito frequentes
        const now = Date.now();
        if (now - lastAuthCheckRef.current < MIN_AUTH_CHECK_INTERVAL) {
          console.log("AuthContext: Verificação recente, pulando");
          setIsLoading(false);
          return;
        }

        lastAuthCheckRef.current = now;
        console.log("AuthContext: Iniciando verificação de sessão");
        setIsLoading(true);

        // 1. Verificar localStorage primeiro (mais rápido)
        const userProfileStr = localStorage.getItem("userProfile");
        let userProfile: UserProfile | null = null;

        if (userProfileStr) {
          try {
            userProfile = JSON.parse(userProfileStr);
            console.log("AuthContext: Perfil encontrado no localStorage:", userProfile.email);

            // Se já temos um perfil no localStorage, usá-lo imediatamente para melhorar a UX
            if (isActive && !user) {
              setUser(userProfile);
              loadPermissions(userProfile.role);
              // Não definir isLoading como false ainda, continuar a verificação em segundo plano
            }
          } catch (parseError) {
            console.error("AuthContext: Erro ao analisar perfil do localStorage:", parseError);
          }
        }

        // 2. Usar a função auxiliar para garantir uma sessão válida
        const sessionResult = await ensureValidSession();

        if (!sessionResult.success || (!sessionResult.session && !sessionResult.userProfile)) {
          console.warn("AuthContext: Não foi possível obter uma sessão válida:",
            'error' in sessionResult ? sessionResult.error : 'Motivo desconhecido');

          // Verificar se há um perfil no localStorage
          if (userProfile) {
            console.log("AuthContext: Usando perfil do localStorage mesmo sem sessão válida");
            if (isActive) {
              setUser(userProfile);
              loadPermissions(userProfile.role);
              setIsLoading(false);

              // Tentar iniciar o gerenciador de autenticação para recuperação em segundo plano
              authManager.start();
            }
            return;
          }

          // Se temos acesso anônimo, permitir navegação limitada
          if (sessionResult.anonymous && sessionResult.session) {
            console.log("AuthContext: Usando sessão anônima para acesso limitado");
            if (isActive) {
              // Definir um usuário anônimo com permissões mínimas
              const anonUser = {
                id: sessionResult.session.user.id,
                email: '<EMAIL>',
                nome: 'Usuário Anônimo',
                role: 'anonymous' as UserRole,
                status: 'ativo',
                foto_url: null
              };

              setUser(anonUser);
              setPermissions(['visualizar_clientes']);
              setIsLoading(false);
            }
            return;
          }

          // Se não há perfil no localStorage nem acesso anônimo, limpar o estado e redirecionar para login
          if (isActive) {
            setUser(null);
            setPermissions([]);
            setIsLoading(false);

            // Verificar se estamos na página de login
            const currentPath = window.location.pathname;
            if (currentPath !== '/login') {
              // Redirecionar para login
              console.log("AuthContext: Redirecionando para login devido a sessão inválida");
              navigate("/login", { replace: true });
            } else {
              console.log("AuthContext: Já estamos na página de login, não redirecionando");
            }
          }
          return;
        }

        // Se temos um perfil local mas não uma sessão válida, usar o perfil local
        if (sessionResult.userProfile && !sessionResult.session) {
          console.log("AuthContext: Usando perfil local sem sessão válida");
          if (isActive) {
            setUser(sessionResult.userProfile);
            loadPermissions(sessionResult.userProfile.role);
            setIsLoading(false);
          }
          return;
        }

        // 3. Se temos uma sessão válida, buscar o perfil do usuário
        console.log("AuthContext: Sessão válida encontrada, verificando perfil");

        // Se já temos um perfil e o ID corresponde, podemos usá-lo
        if (userProfile && userProfile.id === sessionResult.session.user.id) {
          console.log("AuthContext: Usando perfil existente do localStorage");
          if (isActive) {
            setUser(userProfile);
            loadPermissions(userProfile.role);
            setIsLoading(false);
          }
          return;
        }

        // Caso contrário, buscar o perfil do banco de dados
        console.log("AuthContext: Buscando perfil atualizado do banco de dados");

        // Usando a função segura para evitar recursão infinita
        const { data: profileData, error: profileError } = await fetchUserProfileSafely(sessionResult.session.user.id);

        if (profileError || !profileData) {
          console.error("AuthContext: Erro ao buscar perfil:", profileError);

          // Se não conseguimos buscar o perfil, mas temos um no localStorage, usá-lo
          if (userProfile) {
            console.log("AuthContext: Usando perfil do localStorage após falha na busca");
            if (isActive) {
              setUser(userProfile);
              loadPermissions(userProfile.role);
              setIsLoading(false);
            }
            return;
          }

          // Se não temos perfil, limpar o estado mas não fazer logout
          console.log("AuthContext: Perfil não encontrado, limpando estado");
          if (isActive) {
            setUser(null);
            setPermissions([]);
            setIsLoading(false);
          }
          return;
        }

        // Perfil encontrado, atualizar estado e localStorage
        const updatedProfile: UserProfile = {
          id: profileData.id,
          email: profileData.email || sessionResult.session.user.email || '',
          nome: profileData.nome,
          role: profileData.role,
          foto_url: profileData.foto_url,
          status: profileData.status,
        };

        localStorage.setItem("userProfile", JSON.stringify(updatedProfile));

        if (isActive) {
          setUser(updatedProfile);
          loadPermissions(updatedProfile.role);
          setIsLoading(false);

          // Inicializar aplicação e sessão do usuário
          initializeApp();
          initializeUserSessionAsync(updatedProfile);
        }
      } catch (error) {
        console.error("AuthContext: Erro ao verificar sessão:", error);
        if (isActive) {
          setUser(null);
          setPermissions([]);
          setIsLoading(false);
        }
      }
    };

    // Configurar listener para alterações de autenticação
    const setupAuthListener = () => {
      console.log("AuthContext: Configurando listener de autenticação");

      const { data: authListener } = supabase.auth.onAuthStateChange(async (event, session) => {
        console.log("AuthContext: Evento de autenticação:", event);

        if (event === "SIGNED_IN" && session) {
          // Sessão recém-criada, buscar perfil
          try {
            // Usando a função segura para evitar recursão infinita
            const { data: profileData, error: profileError } = await fetchUserProfileSafely(session.user.id);

            if (profileError || !profileData) {
              console.error("AuthContext: Erro ao buscar perfil após login:", profileError);
              if (isActive) {
                setUser(null);
              }
              return;
            }

            if (profileData.status !== "ativo") {
              console.log("AuthContext: Login com usuário inativo");
              // Desconectar se for inativo
              await supabase.auth.signOut();
              if (isActive) {
                setUser(null);
                setPermissions([]);
              }
              toast.error("Acesso negado", {
                description: "Sua conta está inativa. Entre em contato com o administrador."
              });
              navigate("/login");
              return;
            }

            console.log("AuthContext: Login bem-sucedido:", profileData.email);
            if (isActive) {
              const userObj = {
                id: profileData.id,
                email: profileData.email,
                nome: profileData.nome,
                role: profileData.role,
                foto_url: profileData.foto_url,
                status: profileData.status,
              };
              setUser(userObj);
              loadPermissions(profileData.role);

              // Salvar no localStorage
              localStorage.setItem("userProfile", JSON.stringify(userObj));

              // Inicializar aplicação e sessão do usuário
              initializeApp();
              initializeUserSessionAsync(userObj);
            }
          } catch (error) {
            console.error("AuthContext: Erro ao processar login:", error);
          }
        } else if (event === "SIGNED_OUT") {
          console.log("AuthContext: Usuário desconectado");
          if (isActive) {
            // Limpar o estado e redirecionar para login
            setUser(null);
            setPermissions([]);

            // Limpar localStorage para evitar uso de dados desatualizados
            localStorage.removeItem("userProfile");

            // Redirecionar para o login
            navigate("/login", { replace: true });
          }
        }
      });

      return authListener;
    };

    // Verificar se o usuário já está autenticado
    supabase.auth.getSession().then(({ data }) => {
      if (data.session) {
        console.log("AuthContext: Usuário já autenticado, iniciando gerenciador de autenticação");
        // Iniciar o gerenciador de autenticação
        authManager.start();
      }
    });

    // Iniciar verificação e configurar listener
    checkSession();
    const authListener = setupAuthListener();

    // Limpeza ao desmontar
    return () => {
      console.log("AuthContext: Limpando resources");
      isActive = false;
      if (authListener && authListener.subscription) {
        authListener.subscription.unsubscribe();
      }

      // Parar o gerenciador de autenticação
      authManager.stop();
    };
  }, [navigate]);

  // Função para fazer login
  const signIn = async (email: string, password: string) => {
    try {
      console.log("AuthContext: Tentando login para:", email);

      // Primeiro, desconectar qualquer sessão existente
      await supabase.auth.signOut();

      // Parar o gerenciador de autenticação
      authManager.stop();

      // Tentar login
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        console.error("AuthContext: Erro de autenticação:", error.message);
        return { error: error.message };
      }

      console.log("AuthContext: Login bem-sucedido via API");

      // Iniciar o gerenciador de autenticação
      authManager.start();

      // Verificar se temos uma sessão válida
      if (data && data.session) {
        console.log("AuthContext: Sessão obtida com sucesso, expira em:",
          new Date(data.session.expires_at * 1000).toLocaleString());

        // IMPORTANTE: Forçar atualização da sessão para garantir claims de role atualizadas
        console.log("AuthContext: Forçando refresh da sessão para garantir claims de role atualizadas");
        await refreshSupabaseSession();

        // Buscar o perfil do usuário
        try {
          // Usando a função segura para evitar recursão infinita
          const { data: profileData, error: profileError } = await fetchUserProfileSafely(data.session.user.id);

          if (profileError || !profileData) {
            console.error("AuthContext: Erro ao buscar perfil após login:", profileError);
          } else {
            // Armazenar o perfil usando a função robusta
            const userProfile = {
              id: profileData.id,
              email: profileData.email || data.user.email,
              nome: profileData.nome,
              role: profileData.role,
              foto_url: profileData.foto_url,
              status: profileData.status,
            };

            saveUserProfile(userProfile);
            console.log("AuthContext: Perfil armazenado após login usando saveUserProfile. Role do usuário:", profileData.role);
          }
        } catch (profileError) {
          console.error("AuthContext: Erro ao buscar perfil após login:", profileError);
        }
      } else {
        console.warn("AuthContext: Login bem-sucedido, mas sem sessão válida");
      }

      // O perfil será carregado pelo listener de autenticação
      return {};
    } catch (error: any) {
      console.error("AuthContext: Erro inesperado no login:", error);
      return { error: error.message || "Ocorreu um erro durante o login" };
    }
  };

  // Função para fazer logout
  const signOut = async () => {
    try {
      console.log("AuthContext: Iniciando logout");
      // Limpar o estado antes da chamada à API para melhor UX
      setUser(null);
      setPermissions([]);

      // Parar o gerenciador de autenticação
      authManager.stop();

      // Limpar o perfil do usuário de localStorage e sessionStorage
      try {
        localStorage.removeItem(USER_PROFILE_KEY);
        sessionStorage.removeItem(USER_PROFILE_KEY);
        console.log("AuthContext: Perfil do usuário removido do storage");
      } catch (storageError) {
        console.warn("AuthContext: Erro ao limpar perfil do storage:", storageError);
      }

      await supabase.auth.signOut();
      navigate("/login");
      console.log("AuthContext: Logout concluído");
    } catch (error) {
      console.error("AuthContext: Erro ao fazer logout:", error);

      // Ainda limpar o storage em caso de erro
      try {
        localStorage.removeItem(USER_PROFILE_KEY);
        sessionStorage.removeItem(USER_PROFILE_KEY);
      } catch (storageError) {
        console.warn("AuthContext: Erro ao limpar perfil do storage após erro:", storageError);
      }

      // Ainda direcionar para login em caso de erro
      navigate("/login");
    }
  };

  // Função para verificar se o usuário tem uma permissão específica
  const hasPermission = (permission: PermissionType): boolean => {
    return permissions.includes(permission);
  };

  // Função para atualizar dados do perfil do usuário
  const updateUserProfile = async (data: Partial<UserProfile>) => {
    try {
      if (!user) {
        return { error: "Usuário não autenticado" };
      }

      const { error } = await supabase
        .from("profiles")
        .update(data)
        .eq("id", user.id);

      if (error) {
        return { error: error.message };
      }

      // Atualizar o estado local
      setUser(prev => prev ? { ...prev, ...data } : null);

      // Se o papel do usuário mudou, recarregar permissões
      if (data.role && user.role !== data.role) {
        loadPermissions(data.role);
      }

      return {};
    } catch (error: any) {
      console.error("Erro ao atualizar perfil:", error);
      return { error: error.message || "Ocorreu um erro ao atualizar o perfil" };
    }
  };

  // Função para criar um novo usuário (apenas para admins)
  const createUser = async (email: string, password: string, userData: Partial<UserProfile>) => {
    try {
      if (!user || user.role !== "admin") {
        return { error: "Permissão negada" };
      }

      // 1. Criar o usuário na autenticação
      // Usando signUp em vez de admin.createUser que pode não estar disponível na chave anônima
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: `${window.location.origin}/login`,
          data: {
            role: userData.role || "captador",
            nome: userData.nome || email.split('@')[0]
          }
        }
      });

      if (authError) {
        return { error: authError.message };
      }

      if (!authData.user) {
        return { error: "Erro ao criar usuário" };
      }

      console.log("Usuário criado com sucesso:", authData.user.id);

      // 2. Obter o custom_role_id baseado no role
      let customRoleId = userData.custom_role_id;
      if (!customRoleId && userData.role) {
        const roleMapping: Record<string, string> = {
          'admin': '91db2de1-bc77-41a4-b3af-b7f17939b60e',
          'gerente_geral': 'e8b60b8c-496e-43bb-82ef-706fecd9032e',
          'gerente_precatorio': '12b3ec44-5f63-4bda-8528-23cb5820550b',
          'gerente_rpv': '7695489f-ed04-4774-8861-0965b6580464',
          'captador': '14e5d042-8c18-43a7-aa55-7816811935e2',
          'operacional_precatorio': 'fb11821e-d053-456b-bd55-3f7bbd766a97',
          'operacional_rpv': '5b4e8b90-4143-4fca-b8cc-f6def02d7fd8',
          'operacional_completo': 'e9f83330-e01d-45b4-801b-52563f701644'
        };
        customRoleId = roleMapping[userData.role];
      }

      // 3. Criar o perfil do usuário
      const profileData = {
        id: authData.user.id,
        email: email,
        nome: userData.nome || email.split('@')[0],
        role: userData.role || "captador",
        status: userData.status || "ativo",
        foto_url: userData.foto_url,
        cargo: userData.cargo,
        departamento: userData.departamento,
        telefone: userData.telefone,
        data_entrada: userData.data_entrada,
        custom_role_id: customRoleId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      // Primeiro verificar se o perfil já foi criado pela trigger do Supabase
      const { data: existingProfile } = await supabase
        .from("profiles")
        .select("id")
        .eq("id", authData.user.id)
        .maybeSingle();

      let profileError = null;

      if (!existingProfile) {
        // Inserir o perfil se não existir
        const { error: insertError } = await supabase
          .from("profiles")
          .insert(profileData);

        profileError = insertError;
      } else {
        // Atualizar o perfil se já existir
        const { error: updateError } = await supabase
          .from("profiles")
          .update({
            nome: profileData.nome,
            role: profileData.role,
            status: profileData.status,
            foto_url: profileData.foto_url,
            cargo: profileData.cargo,
            departamento: profileData.departamento,
            telefone: profileData.telefone,
            data_entrada: profileData.data_entrada,
            custom_role_id: profileData.custom_role_id,
            updated_at: profileData.updated_at
          })
          .eq("id", authData.user.id);

        profileError = updateError;
      }

      if (profileError) {
        console.error("Erro ao criar/atualizar perfil:", profileError);
        return { error: profileError.message };
      }

      // 4. Aplicar permissões padrão baseadas no role
      if (customRoleId) {
        try {
          await supabase.rpc('apply_role_default_permissions', {
            p_user_id: authData.user.id,
            p_role_id: customRoleId
          });
          console.log("Permissões padrão aplicadas com sucesso para o usuário:", authData.user.id);
        } catch (permError) {
          console.warn("Erro ao aplicar permissões padrão:", permError);
          // Não falhar a criação do usuário por causa das permissões
        }
      }

      // Converter para o formato UserProfile
      const userProfileResult: UserProfile = {
        id: profileData.id,
        email: profileData.email,
        nome: profileData.nome,
        role: profileData.role,
        status: profileData.status,
        foto_url: profileData.foto_url,
        cargo: profileData.cargo,
        departamento: profileData.departamento,
        telefone: profileData.telefone,
        data_entrada: profileData.data_entrada,
        custom_role_id: profileData.custom_role_id,
        created_at: profileData.created_at
      };

      return { user: userProfileResult };
    } catch (error: any) {
      console.error("Erro ao criar usuário:", error);
      return { error: error.message || "Ocorreu um erro ao criar o usuário" };
    }
  };

  // Função para atualizar um usuário existente (apenas para admins)
  const updateUser = async (userId: string, userData: Partial<UserProfile>) => {
    try {
      if (!user || user.role !== "admin") {
        return { error: "Permissão negada" };
      }

      // Verificar se o ID do usuário é válido
      if (!userId || userId === 'undefined' || !userId.includes('-')) {
        return { error: "ID de usuário inválido" };
      }

      // Obter o custom_role_id baseado no role se necessário
      let updateData = { ...userData };
      if (userData.role && !userData.custom_role_id) {
        const roleMapping: Record<string, string> = {
          'admin': '91db2de1-bc77-41a4-b3af-b7f17939b60e',
          'gerente_geral': 'e8b60b8c-496e-43bb-82ef-706fecd9032e',
          'gerente_precatorio': '12b3ec44-5f63-4bda-8528-23cb5820550b',
          'gerente_rpv': '7695489f-ed04-4774-8861-0965b6580464',
          'captador': '14e5d042-8c18-43a7-aa55-7816811935e2',
          'operacional_precatorio': 'fb11821e-d053-456b-bd55-3f7bbd766a97',
          'operacional_rpv': '5b4e8b90-4143-4fca-b8cc-f6def02d7fd8',
          'operacional_completo': 'e9f83330-e01d-45b4-801b-52563f701644'
        };
        updateData.custom_role_id = roleMapping[userData.role];
      }

      // Adicionar timestamp de atualização
      updateData.updated_at = new Date().toISOString();

      // Atualizar o perfil no banco de dados
      const { data: updatedProfile, error: updateError } = await supabase
        .from("profiles")
        .update(updateData)
        .eq("id", userId)
        .select()
        .single();

      if (updateError) {
        console.error("Erro ao atualizar usuário:", updateError);
        return { error: updateError.message };
      }

      // Aplicar permissões padrão se o role mudou
      if (userData.role && updateData.custom_role_id) {
        try {
          await supabase.rpc('apply_role_default_permissions', {
            p_user_id: userId,
            p_role_id: updateData.custom_role_id
          });
          console.log("Permissões padrão aplicadas para o usuário atualizado:", userId);
        } catch (permError) {
          console.warn("Erro ao aplicar permissões padrão:", permError);
        }
      }

      return { user: updatedProfile };
    } catch (error: any) {
      console.error("Erro ao atualizar usuário:", error);
      return { error: error.message || "Ocorreu um erro ao atualizar o usuário" };
    }
  };

  // Função para excluir um usuário (apenas para admins)
  const deleteUser = async (userId: string) => {
    try {
      if (!user || user.role !== "admin") {
        return { error: "Permissão negada" };
      }

      // Verificar se o ID do usuário é válido
      if (!userId || userId === 'undefined' || !userId.includes('-')) {
        return { error: "ID de usuário inválido" };
      }

      // Não permitir que o admin exclua a si mesmo
      if (userId === user.id) {
        return { error: "Você não pode excluir sua própria conta" };
      }

      // Primeiro, excluir permissões específicas do usuário
      await supabase
        .from("user_specific_permissions")
        .delete()
        .eq("user_id", userId);

      // Excluir acesso a páginas
      await supabase
        .from("user_page_access")
        .delete()
        .eq("user_id", userId);

      // Marcar o perfil como excluído (soft delete)
      const { error: profileError } = await supabase
        .from("profiles")
        .update({
          status: "excluido",
          updated_at: new Date().toISOString()
        })
        .eq("id", userId);

      if (profileError) {
        console.error("Erro ao excluir perfil:", profileError);
        return { error: profileError.message };
      }

      // Tentar excluir da autenticação (pode falhar se não tivermos permissões de admin)
      try {
        // Como não temos acesso ao admin API, vamos apenas desativar o usuário
        console.log("Usuário marcado como excluído no perfil:", userId);
      } catch (authError) {
        console.warn("Não foi possível excluir da autenticação:", authError);
        // Não falhar a operação por causa disso
      }

      // Limpar cache para forçar atualização da lista
      localStorage.removeItem('cached_users');

      return { success: true };
    } catch (error: any) {
      console.error("Erro ao excluir usuário:", error);
      return { error: error.message || "Ocorreu um erro ao excluir o usuário" };
    }
  };

  // Função para buscar todos os usuários (para administradores)
  const getAllUsers = async () => {
    try {
      // Verificar permissões
      if (!user || user.role !== "admin") {
        return { error: "Permissão negada" };
      }

      // Usar a nova função segura para evitar recursão infinita
      return await fetchAllUsersSafely();
    } catch (error: any) {
      console.error("Erro ao buscar usuários:", error);
      return { error: error.message || "Erro desconhecido" };
    }
  };

  // Função para atualizar o papel e permissões de um usuário (apenas para admins)
  const updateUserPermissions = async (userId: string, role: UserRole) => {
    try {
      if (!user || user.role !== "admin") {
        return { error: "Permissão negada" };
      }

      console.log("AuthContext: Atualizando permissões para usuário", userId, "para", role);

      // Verificar se o ID do usuário é válido
      if (!userId || userId === 'undefined' || !userId.includes('-')) {
        console.error("AuthContext: ID de usuário inválido para atualização:", userId);
        return { error: "ID de usuário inválido" };
      }

      // Verificar se o papel é válido
      const validRoles: UserRole[] = [
        "admin",
        "gerente_geral",
        "gerente_precatorio",
        "gerente_rpv",
        "captador",
        "operacional_precatorio",
        "operacional_rpv",
        "operacional_completo"
      ];

      if (!validRoles.includes(role)) {
        console.error("AuthContext: Role inválido:", role);
        return { error: `Cargo "${role}" não é válido. Escolha um dos cargos permitidos.` };
      }

      console.log("AuthContext: Enviando atualização para o banco de dados:", {
        role,
        updated_at: new Date().toISOString()
      });

      // Tentar usar a função RPC primeiro (mais confiável)
      try {
        const { data: rpcData, error: rpcError } = await supabase.rpc('atualizar_cargo_usuario', {
          usuario_id: userId,
          novo_cargo: role
        });

        if (!rpcError && rpcData) {
          console.log("AuthContext: Permissões atualizadas com sucesso via RPC");
        } else {
          // Se falhar com RPC, tentar método padrão
          console.warn("AuthContext: Falha ao usar RPC, tentando método padrão:", rpcError);

          const { error } = await supabase
            .from("profiles")
            .update({
              role,
              updated_at: new Date().toISOString()
            })
            .eq("id", userId);

          if (error) {
            console.error("AuthContext: Erro ao atualizar permissões:", error);
            return { error: error.message };
          }
        }
      } catch (rpcError) {
        console.warn("AuthContext: Erro ao chamar RPC, tentando método padrão:", rpcError);

        // Método padrão como fallback
        const { error } = await supabase
          .from("profiles")
          .update({
            role,
            updated_at: new Date().toISOString()
          })
          .eq("id", userId);

        if (error) {
          console.error("AuthContext: Erro ao atualizar permissões:", error);
          return { error: error.message };
        }
      }

      console.log("AuthContext: Permissões atualizadas com sucesso");

      // Atualizar o cache de usuários
      try {
        const cachedUsersStr = localStorage.getItem('cached_users');
        if (cachedUsersStr) {
          const cachedUsers = JSON.parse(cachedUsersStr);
          const updatedUsers = cachedUsers.map((cachedUser: UserProfile) =>
            cachedUser.id === userId ? { ...cachedUser, role } : cachedUser
          );
          localStorage.setItem('cached_users', JSON.stringify(updatedUsers));
          console.log("AuthContext: Cache de usuários atualizado");
        }
      } catch (cacheError) {
        console.error("AuthContext: Erro ao atualizar cache:", cacheError);
      }

      return { success: true };
    } catch (error: any) {
      console.error("Erro ao atualizar permissões:", error);
      return { error: error.message || "Ocorreu um erro ao atualizar as permissões" };
    }
  };

  // Calcular propriedades derivadas
  const isAdmin = user?.role === "admin";
  const isGerente = user?.role === "gerente_geral" || user?.role === "gerente_precatorio" || user?.role === "gerente_rpv";

  const value = {
    user,
    isLoading,
    isAdmin,
    isGerente,
    permissions,
    hasPermission,
    signIn,
    signOut,
    updateUserProfile,
    createUser,
    updateUser,
    deleteUser,
    getAllUsers,
    updateUserPermissions,
    refreshSession,
    resetAuth
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth deve ser usado dentro de um AuthProvider");
  }
  return context;
};
