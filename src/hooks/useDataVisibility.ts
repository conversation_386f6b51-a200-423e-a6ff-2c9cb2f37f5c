import { useState, useEffect, useCallback, useMemo } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { DataVisibilityService, DataVisibilityType } from '@/services/dataVisibilityService';

interface DataVisibilityConfig {
  resource_type: string;
  visibility_type: DataVisibilityType;
  allowed_user_ids: string[];
  allowed_roles: string[];
  allowed_departments: string[];
  can_view_sensitive_data: boolean;
  can_export_data: boolean;
  can_view_financial_data: boolean;
  can_view_personal_data: boolean;
}

interface UseDataVisibilityReturn {
  // Verificações de permissão
  canViewData: (resourceType: string, resourceOwnerId?: string, resourceMetadata?: any) => Promise<boolean>;
  canExportData: (resourceType: string) => Promise<boolean>;
  canViewSensitiveData: (resourceType: string) => Promise<boolean>;
  canViewFinancialData: (resourceType: string) => Promise<boolean>;
  canViewPersonalData: (resourceType: string) => Promise<boolean>;
  
  // Filtros de dados
  filterVisibleData: <T extends { id: string; created_by?: string; responsavel_id?: string; user_id?: string }>(
    data: T[], 
    resourceType: string
  ) => Promise<T[]>;
  
  // Configurações
  getVisibilityConfig: (resourceType: string) => Promise<DataVisibilityConfig | null>;
  getVisibleUserIds: (resourceType: string) => Promise<string[]>;
  
  // Estado
  loading: boolean;
  error: string | null;
  
  // Cache management
  refreshConfig: () => Promise<void>;
}

export function useDataVisibility(): UseDataVisibilityReturn {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [configCache, setConfigCache] = useState<Map<string, DataVisibilityConfig>>(new Map());

  // Limpar cache quando o usuário muda
  useEffect(() => {
    setConfigCache(new Map());
    setError(null);
  }, [user?.id]);

  // Função para obter configuração de visibilidade
  const getVisibilityConfig = useCallback(async (resourceType: string): Promise<DataVisibilityConfig | null> => {
    if (!user) return null;

    // Verificar cache primeiro
    const cached = configCache.get(resourceType);
    if (cached) return cached;

    try {
      const configs = await DataVisibilityService.getUserDataVisibility(user.id, resourceType);
      const config = configs.find(c => c.resource_type === resourceType);
      
      if (config) {
        const formattedConfig: DataVisibilityConfig = {
          resource_type: config.resource_type,
          visibility_type: config.visibility_type,
          allowed_user_ids: config.allowed_user_ids,
          allowed_roles: config.allowed_roles,
          allowed_departments: config.allowed_departments,
          can_view_sensitive_data: config.can_view_sensitive_data,
          can_export_data: config.can_export_data,
          can_view_financial_data: config.can_view_financial_data,
          can_view_personal_data: config.can_view_personal_data
        };
        
        // Atualizar cache
        setConfigCache(prev => new Map(prev).set(resourceType, formattedConfig));
        return formattedConfig;
      }
      
      return null;
    } catch (err) {
      console.error('Erro ao obter configuração de visibilidade:', err);
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
      return null;
    }
  }, [user, configCache]);

  // Verificar se pode ver dados específicos
  const canViewData = useCallback(async (
    resourceType: string, 
    resourceOwnerId?: string, 
    resourceMetadata?: any
  ): Promise<boolean> => {
    if (!user) return false;

    // Admin sempre pode ver tudo
    if (user.role === 'admin') return true;

    try {
      return await DataVisibilityService.canUserViewData(
        user.id, 
        resourceType, 
        resourceOwnerId, 
        resourceMetadata
      );
    } catch (err) {
      console.error('Erro ao verificar permissão de visualização:', err);
      return false;
    }
  }, [user]);

  // Verificar permissões específicas
  const canExportData = useCallback(async (resourceType: string): Promise<boolean> => {
    if (!user) return false;
    if (user.role === 'admin') return true;

    try {
      return await DataVisibilityService.canUserExportData(user.id, resourceType);
    } catch (err) {
      console.error('Erro ao verificar permissão de exportação:', err);
      return false;
    }
  }, [user]);

  const canViewSensitiveData = useCallback(async (resourceType: string): Promise<boolean> => {
    if (!user) return false;
    if (user.role === 'admin') return true;

    try {
      return await DataVisibilityService.canUserViewSensitiveData(user.id, resourceType);
    } catch (err) {
      console.error('Erro ao verificar permissão de dados sensíveis:', err);
      return false;
    }
  }, [user]);

  const canViewFinancialData = useCallback(async (resourceType: string): Promise<boolean> => {
    if (!user) return false;
    if (user.role === 'admin') return true;

    try {
      return await DataVisibilityService.canUserViewFinancialData(user.id, resourceType);
    } catch (err) {
      console.error('Erro ao verificar permissão de dados financeiros:', err);
      return false;
    }
  }, [user]);

  const canViewPersonalData = useCallback(async (resourceType: string): Promise<boolean> => {
    if (!user) return false;
    if (user.role === 'admin') return true;

    const config = await getVisibilityConfig(resourceType);
    return config?.can_view_personal_data || false;
  }, [user, getVisibilityConfig]);

  // Obter IDs de usuários visíveis
  const getVisibleUserIds = useCallback(async (resourceType: string): Promise<string[]> => {
    if (!user) return [];
    if (user.role === 'admin') {
      // Admin pode ver todos os usuários - buscar da base de dados
      try {
        const { data } = await DataVisibilityService.getUserDataVisibility(user.id);
        // Retornar todos os IDs de usuários do sistema
        return []; // Implementar busca de todos os usuários se necessário
      } catch {
        return [];
      }
    }

    try {
      return await DataVisibilityService.getVisibleUsers(user.id, resourceType);
    } catch (err) {
      console.error('Erro ao obter usuários visíveis:', err);
      return [user.id]; // Pelo menos o próprio usuário
    }
  }, [user]);

  // Filtrar dados baseado nas permissões
  const filterVisibleData = useCallback(async <T extends { 
    id: string; 
    created_by?: string; 
    responsavel_id?: string; 
    user_id?: string;
  }>(
    data: T[], 
    resourceType: string
  ): Promise<T[]> => {
    if (!user) return [];
    if (user.role === 'admin') return data;

    try {
      const visibleUserIds = await getVisibleUserIds(resourceType);
      
      return data.filter(item => {
        // Verificar se o item pertence a um usuário visível
        const ownerId = item.created_by || item.responsavel_id || item.user_id;
        
        if (!ownerId) {
          // Se não há dono definido, verificar permissão geral
          return canViewData(resourceType, undefined, item);
        }
        
        return visibleUserIds.includes(ownerId);
      });
    } catch (err) {
      console.error('Erro ao filtrar dados visíveis:', err);
      // Em caso de erro, retornar apenas dados do próprio usuário
      return data.filter(item => {
        const ownerId = item.created_by || item.responsavel_id || item.user_id;
        return ownerId === user.id;
      });
    }
  }, [user, getVisibleUserIds, canViewData]);

  // Atualizar configurações
  const refreshConfig = useCallback(async (): Promise<void> => {
    setConfigCache(new Map());
    setError(null);
  }, []);

  // Verificar se é admin (memoizado)
  const isAdmin = useMemo(() => user?.role === 'admin', [user?.role]);

  return {
    canViewData,
    canExportData,
    canViewSensitiveData,
    canViewFinancialData,
    canViewPersonalData,
    filterVisibleData,
    getVisibilityConfig,
    getVisibleUserIds,
    loading,
    error,
    refreshConfig
  };
}
