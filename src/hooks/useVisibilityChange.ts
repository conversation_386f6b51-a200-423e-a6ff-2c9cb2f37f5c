import { useEffect, useState, useCallback, useRef } from 'react';
import { supabase } from '@/lib/supabase';
import { clearCache } from '@/services/cacheService';
import { clearSupabaseCache } from '@/lib/supabase';

/**
 * Hook simplificado para detectar mudanças de visibilidade da página e atualizar dados
 * quando o usuário retorna à aplicação após inatividade
 *
 * @param refreshData Função para recarregar dados quando a página se torna visível
 * @param options Opções de configuração
 * @returns Estado atual de visibilidade e função para forçar atualização
 */
export function useVisibilityChange(
  refreshData?: () => Promise<void> | void,
  options: {
    clearCacheOnReturn?: boolean;
    refreshSessionOnReturn?: boolean;
    minTimeSinceLastVisible?: number; // tempo mínimo em ms para considerar "inatividade"
  } = {}
) {
  const [isVisible, setIsVisible] = useState<boolean>(document.visibilityState === 'visible');
  const [lastVisibleTime, setLastVisibleTime] = useState<number>(Date.now());

  const {
    clearCacheOnReturn = true,
    refreshSessionOnReturn = true,
    minTimeSinceLastVisible = 5000, // 5 segundos padrão
  } = options;

  // Referência para controlar se o hook está montado
  const isMountedRef = useRef(true);

  // Referência para controlar se há uma atualização em andamento
  const isRefreshingRef = useRef(false);

  // Efeito para limpar as referências quando o componente for desmontado
  useEffect(() => {
    isMountedRef.current = true;
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  // Função para forçar atualização dos dados
  const forceRefresh = useCallback(async () => {
    // Evitar múltiplas atualizações simultâneas
    if (isRefreshingRef.current) {
      console.log('[useVisibilityChange] Atualização já em andamento, ignorando solicitação');
      return;
    }

    try {
      isRefreshingRef.current = true;
      console.log('[useVisibilityChange] Forçando atualização de dados');

      // Adicionar um pequeno atraso para evitar conflitos com o GoTrueClient
      // e outros handlers de visibilidade
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Verificar se o componente ainda está montado e a página ainda está visível
      if (!isMountedRef.current || document.visibilityState !== 'visible') {
        console.log('[useVisibilityChange] Componente desmontado ou página não está mais visível, cancelando atualização');
        return;
      }

      // Atualizar sessão se configurado
      if (refreshSessionOnReturn) {
        console.log('[useVisibilityChange] Atualizando sessão');
        try {
          // Usar o authManager em vez de chamar diretamente o Supabase
          // para aproveitar a lógica de recuperação melhorada
          const { authManager } = await import('@/lib/authManager');

          // Verificar novamente se o componente ainda está montado
          if (!isMountedRef.current) return;

          // Iniciar o gerenciador de autenticação se não estiver ativo
          authManager.start();

          // Adicionar um pequeno atraso para permitir que o gerenciador de autenticação inicialize
          await new Promise(resolve => setTimeout(resolve, 500));

          // Verificar novamente se o componente ainda está montado
          if (!isMountedRef.current) return;

          const sessionValid = await authManager.checkSession();

          // Verificar novamente se o componente ainda está montado
          if (!isMountedRef.current) return;

          if (!sessionValid) {
            console.warn('[useVisibilityChange] Sessão inválida, verificando perfil local');

            // Verificar se temos um perfil no localStorage
            try {
              const userProfileStr = localStorage.getItem("userProfile");
              if (userProfileStr) {
                console.log('[useVisibilityChange] Perfil encontrado no localStorage, permitindo acesso limitado');

                // Disparar evento personalizado para notificar componentes sobre o acesso limitado
                const sessionEvent = new CustomEvent('auth-session-limited', {
                  detail: {
                    timestamp: Date.now(),
                    limitedAccess: true
                  }
                });
                document.dispatchEvent(sessionEvent);

                // Continuar com a atualização de dados mesmo com acesso limitado
                console.log('[useVisibilityChange] Continuando com acesso limitado');
              } else {
                console.warn('[useVisibilityChange] Sem perfil local, não prosseguindo com atualização de dados');
                return;
              }
            } catch (profileError) {
              console.error('[useVisibilityChange] Erro ao verificar perfil no localStorage:', profileError);
              return; // Não continuar se houver erro ao verificar o perfil
            }
          }
        } catch (sessionError) {
          console.error('[useVisibilityChange] Erro ao atualizar sessão:', sessionError);

          // Verificar se temos um perfil no localStorage mesmo em caso de erro
          try {
            const userProfileStr = localStorage.getItem("userProfile");
            if (userProfileStr) {
              console.log('[useVisibilityChange] Perfil encontrado no localStorage após erro, permitindo acesso limitado');

              // Disparar evento personalizado para notificar componentes sobre o acesso limitado
              const sessionEvent = new CustomEvent('auth-session-limited', {
                detail: {
                  timestamp: Date.now(),
                  limitedAccess: true,
                  error: true
                }
              });
              document.dispatchEvent(sessionEvent);

              // Continuar com a atualização de dados mesmo com acesso limitado
              console.log('[useVisibilityChange] Continuando com acesso limitado após erro');
            } else {
              return; // Não continuar se não houver perfil local
            }
          } catch (profileError) {
            console.error('[useVisibilityChange] Erro ao verificar perfil no localStorage após erro de sessão:', profileError);
            return; // Não continuar se houver erro ao verificar o perfil
          }
        }
      }

      // Verificar novamente se o componente ainda está montado
      if (!isMountedRef.current) return;

      // Limpar caches se configurado, mas de forma mais seletiva
      if (clearCacheOnReturn) {
        console.log('[useVisibilityChange] Limpando caches seletivamente');
        // Não limpar todos os caches, apenas os relacionados a dados que precisam ser atualizados
        // Isso evita problemas de permissão e carregamento desnecessário

        // Verificar se estamos em uma página de precatórios
        const path = window.location.pathname;
        if (path.includes('precatorios')) {
          console.log('[useVisibilityChange] Limpando apenas caches de precatórios');
          // Limpar apenas o cache específico para o tipo de visualização atual
          // em vez de limpar todos os caches relacionados a precatórios
          const tipoVisualizacao = new URLSearchParams(window.location.search).get('tipo') || 'PRECATORIO';
          const cacheKey = `precatorios_${tipoVisualizacao}`;
          clearCache(cacheKey, true); // Usar correspondência exata
          console.log(`[useVisibilityChange] Limpando apenas cache específico: ${cacheKey}`);
        } else if (path.includes('clientes')) {
          console.log('[useVisibilityChange] Limpando apenas caches de clientes');
          clearCache('clientes', true); // Usar correspondência exata
        } else if (path.includes('tarefas')) {
          console.log('[useVisibilityChange] Limpando apenas caches de tarefas');
          clearCache('tarefas', true); // Usar correspondência exata
        } else {
          // Para outras páginas, não limpar cache global para evitar problemas
          console.log('[useVisibilityChange] Não limpando caches para esta página');
        }
      }

      // Verificar novamente se o componente ainda está montado
      if (!isMountedRef.current) return;

      // Executar função de atualização de dados se fornecida
      if (refreshData) {
        console.log('[useVisibilityChange] Executando função de atualização de dados');
        await refreshData();
      }
    } catch (error) {
      console.error('[useVisibilityChange] Erro ao atualizar dados:', error);
    } finally {
      // Garantir que a flag seja resetada
      isRefreshingRef.current = false;
    }
  }, [clearCacheOnReturn, refreshSessionOnReturn, refreshData]);

  useEffect(() => {
    // Flag para evitar execuções simultâneas
    let isHandlingVisibilityChange = false;

    const handleVisibilityChange = async () => {
      // Verificar se o componente ainda está montado
      if (!isMountedRef.current) return;

      // Evitar execuções simultâneas
      if (isHandlingVisibilityChange || isRefreshingRef.current) {
        console.log('[useVisibilityChange] Já está processando uma mudança de visibilidade ou atualização, ignorando');
        return;
      }

      isHandlingVisibilityChange = true;

      try {
        const isNowVisible = document.visibilityState === 'visible';
        const now = Date.now();
        const timeSinceLastVisible = now - lastVisibleTime;

        console.log(`[useVisibilityChange] Mudança de visibilidade: ${isNowVisible ? 'visível' : 'oculto'}`);

        // Atualizar estado de visibilidade
        if (isMountedRef.current) {
          setIsVisible(isNowVisible);
        }

        // Se a página se tornou visível
        if (isNowVisible) {
          console.log(`[useVisibilityChange] Tempo desde última visibilidade: ${Math.round(timeSinceLastVisible / 1000)}s`);

          // Se passou tempo suficiente desde a última vez que a página estava visível
          if (timeSinceLastVisible >= minTimeSinceLastVisible) {
            console.log('[useVisibilityChange] Página voltou a ficar visível, atualizando dados');

            // Adicionar um atraso maior para evitar conflitos com outros handlers
            // e dar tempo para o GoTrueClient processar suas próprias operações
            await new Promise(resolve => setTimeout(resolve, 2000));

            // Verificar novamente se o componente ainda está montado e a página ainda está visível
            if (!isMountedRef.current) {
              console.log('[useVisibilityChange] Componente desmontado durante o atraso, cancelando atualização');
              return;
            }

            if (document.visibilityState === 'visible') {
              await forceRefresh();
            } else {
              console.log('[useVisibilityChange] Página não está mais visível, cancelando atualização');
            }
          }
        } else {
          // Quando a página se torna invisível, atualizar o timestamp
          setLastVisibleTime(now);
        }
      } catch (error) {
        console.error('[useVisibilityChange] Erro ao processar mudança de visibilidade:', error);
      } finally {
        // Garantir que a flag seja resetada
        isHandlingVisibilityChange = false;
      }
    };

    // Registrar o listener
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Também registrar um listener para o evento de foco da janela
    const handleWindowFocus = async () => {
      // Verificar se o componente ainda está montado
      if (!isMountedRef.current) return;

      // Evitar execuções simultâneas
      if (isHandlingVisibilityChange || isRefreshingRef.current) {
        console.log('[useVisibilityChange] Já está processando uma mudança de visibilidade ou atualização, ignorando foco');
        return;
      }

      if (document.visibilityState === 'visible' && document.hasFocus()) {
        console.log('[useVisibilityChange] Janela recebeu foco, verificando necessidade de atualização');

        const now = Date.now();
        const timeSinceLastVisible = now - lastVisibleTime;

        // Só atualizar se passou tempo suficiente
        if (timeSinceLastVisible >= minTimeSinceLastVisible) {
          // Adicionar um atraso para evitar conflitos
          await new Promise(resolve => setTimeout(resolve, 2000));

          // Verificar novamente se o componente ainda está montado
          if (!isMountedRef.current) {
            console.log('[useVisibilityChange] Componente desmontado durante o atraso, cancelando atualização após foco');
            return;
          }

          // Verificar novamente se a janela ainda tem foco
          if (document.visibilityState === 'visible' && document.hasFocus()) {
            console.log('[useVisibilityChange] Atualizando dados após foco da janela');
            await forceRefresh();
          }
        }
      }
    };

    window.addEventListener('focus', handleWindowFocus);

    // Limpar os listeners quando o componente for desmontado
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleWindowFocus);
    };
  }, [lastVisibleTime, minTimeSinceLastVisible, forceRefresh]);

  return { isVisible, forceRefresh };
}
