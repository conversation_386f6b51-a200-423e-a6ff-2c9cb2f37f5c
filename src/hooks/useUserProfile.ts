import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { 
  getUserProfileData, 
  getUserMetrics, 
  getUserPerformanceData,
  updateUserProfile,
  UserProfileData,
  UserMetrics,
  UserPerformanceData
} from '@/services/userProfileService';
import { toast } from 'sonner';

interface UseUserProfileReturn {
  // Dados
  profileData: UserProfileData | null;
  metrics: UserMetrics | null;
  performanceData: UserPerformanceData | null;
  
  // Estados
  loading: boolean;
  updating: boolean;
  error: string | null;
  
  // Ações
  refreshData: () => Promise<void>;
  updateProfile: (updates: Partial<UserProfileData>) => Promise<boolean>;
  clearError: () => void;
}

export function useUserProfile(userId?: string): UseUserProfileReturn {
  const { user } = useAuth();
  const targetUserId = userId || user?.id;

  const [profileData, setProfileData] = useState<UserProfileData | null>(null);
  const [metrics, setMetrics] = useState<UserMetrics | null>(null);
  const [performanceData, setPerformanceData] = useState<UserPerformanceData | null>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Função para carregar todos os dados
  const loadAllData = async () => {
    if (!targetUserId) {
      setError('ID do usuário não fornecido');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Carregar dados em paralelo
      const [profile, userMetrics, performance] = await Promise.all([
        getUserProfileData(targetUserId),
        getUserMetrics(targetUserId),
        getUserPerformanceData(targetUserId)
      ]);

      setProfileData(profile);
      setMetrics(userMetrics);
      setPerformanceData(performance);

      if (!profile) {
        setError('Perfil do usuário não encontrado');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
      setError(`Erro ao carregar dados do perfil: ${errorMessage}`);
      console.error('Erro ao carregar dados do perfil:', err);
    } finally {
      setLoading(false);
    }
  };

  // Função para atualizar dados
  const refreshData = async () => {
    await loadAllData();
  };

  // Função para atualizar perfil
  const updateProfile = async (updates: Partial<UserProfileData>): Promise<boolean> => {
    if (!targetUserId) {
      toast.error('ID do usuário não fornecido');
      return false;
    }

    try {
      setUpdating(true);
      setError(null);

      const success = await updateUserProfile(targetUserId, updates);

      if (success) {
        // Atualizar dados locais
        setProfileData(prev => prev ? { ...prev, ...updates } : null);
        toast.success('Perfil atualizado com sucesso');
        
        // Recarregar dados para garantir consistência
        await refreshData();
        return true;
      } else {
        toast.error('Erro ao atualizar perfil');
        return false;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
      setError(`Erro ao atualizar perfil: ${errorMessage}`);
      toast.error('Erro ao atualizar perfil');
      console.error('Erro ao atualizar perfil:', err);
      return false;
    } finally {
      setUpdating(false);
    }
  };

  // Função para limpar erro
  const clearError = () => {
    setError(null);
  };

  // Carregar dados quando o componente monta ou o userId muda
  useEffect(() => {
    loadAllData();
  }, [targetUserId]);

  return {
    profileData,
    metrics,
    performanceData,
    loading,
    updating,
    error,
    refreshData,
    updateProfile,
    clearError
  };
}

// Hook específico para métricas rápidas (sem dados de performance)
export function useUserMetricsOnly(userId?: string) {
  const { user } = useAuth();
  const targetUserId = userId || user?.id;

  const [metrics, setMetrics] = useState<UserMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!targetUserId) {
      setLoading(false);
      return;
    }

    const loadMetrics = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const userMetrics = await getUserMetrics(targetUserId);
        setMetrics(userMetrics);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
        setError(errorMessage);
        console.error('Erro ao carregar métricas:', err);
      } finally {
        setLoading(false);
      }
    };

    loadMetrics();
  }, [targetUserId]);

  return { metrics, loading, error };
}

// Hook para dados de performance apenas
export function useUserPerformanceOnly(userId?: string) {
  const { user } = useAuth();
  const targetUserId = userId || user?.id;

  const [performanceData, setPerformanceData] = useState<UserPerformanceData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!targetUserId) {
      setLoading(false);
      return;
    }

    const loadPerformance = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const performance = await getUserPerformanceData(targetUserId);
        setPerformanceData(performance);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
        setError(errorMessage);
        console.error('Erro ao carregar dados de performance:', err);
      } finally {
        setLoading(false);
      }
    };

    loadPerformance();
  }, [targetUserId]);

  return { performanceData, loading, error };
}
