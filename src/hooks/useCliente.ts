import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { Cliente, Contato, Precatorio, Documento, Atividade, Compromisso } from '@/types/cliente';
import { toast } from '@/components/ui/use-toast';

interface UseClienteReturn {
  cliente: Cliente | null;
  contatos: Contato[];
  precatorios: Precatorio[];
  documentos: Documento[];
  atividades: Atividade[];
  compromissos: Compromisso[];
  isLoading: boolean;
  error: Error | null;
  refresh: () => Promise<void>;
  updateCliente: (data: Partial<Cliente>) => Promise<void>;
  uploadDocumento: (file: File, tipo: Documento['tipo']) => Promise<void>;
  addContato: (contato: Omit<Contato, 'id' | 'cliente_id'>) => Promise<void>;
  deleteDocumento: (documentoId: string) => Promise<void>;
}

export function useCliente(clienteId: string): UseClienteReturn {
  const [cliente, setCliente] = useState<Cliente | null>(null);
  const [contatos, setContatos] = useState<Contato[]>([]);
  const [precatorios, setPrecatorios] = useState<Precatorio[]>([]);
  const [documentos, setDocumentos] = useState<Documento[]>([]);
  const [atividades, setAtividades] = useState<Atividade[]>([]);
  const [compromissos, setCompromissos] = useState<Compromisso[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const loadData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Carregar dados do cliente
      const { data: clienteData, error: clienteError } = await supabase
        .from('clientes')
        .select('*')
        .eq('id', clienteId)
        .single();

      if (clienteError) throw clienteError;
      setCliente(clienteData);

      // Carregar contatos
      const { data: contatosData } = await supabase
        .from('contatos')
        .select('*')
        .eq('cliente_id', clienteId)
        .order('principal', { ascending: false });

      setContatos(contatosData || []);

      // Carregar precatórios
      const { data: precatoriosData } = await supabase
        .from('precatorios')
        .select('*')
        .eq('cliente_id', clienteId)
        .order('data_cadastro', { ascending: false });

      setPrecatorios(precatoriosData || []);

      // Carregar documentos
      const { data: documentosData } = await supabase
        .from('documentos')
        .select('*')
        .eq('cliente_id', clienteId)
        .order('data_upload', { ascending: false });

      setDocumentos(documentosData || []);

      // Carregar atividades
      const { data: atividadesData } = await supabase
        .from('atividades')
        .select('*')
        .eq('cliente_id', clienteId)
        .order('data', { ascending: false })
        .limit(50);

      setAtividades(atividadesData || []);

      // Carregar compromissos
      const { data: compromissosData } = await supabase
        .from('compromissos')
        .select('*')
        .eq('cliente_id', clienteId)
        .gte('data', new Date().toISOString())
        .order('data', { ascending: true });

      setCompromissos(compromissosData || []);

    } catch (err) {
      setError(err as Error);
      toast({
        title: "Erro ao carregar dados",
        description: (err as Error).message,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, [clienteId]);

  const updateCliente = async (data: Partial<Cliente>) => {
    try {
      const { error } = await supabase
        .from('clientes')
        .update(data)
        .eq('id', clienteId);

      if (error) throw error;

      await loadData();
      toast({
        title: "Cliente atualizado",
        description: "As informações foram atualizadas com sucesso.",
      });
    } catch (err) {
      toast({
        title: "Erro ao atualizar",
        description: (err as Error).message,
        variant: "destructive",
      });
    }
  };

  const uploadDocumento = async (file: File, tipo: Documento['tipo']) => {
    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `${clienteId}/${Date.now()}.${fileExt}`;

      // Upload do arquivo
      const { error: uploadError } = await supabase.storage
        .from('documentos')
        .upload(fileName, file);

      if (uploadError) throw uploadError;

      // Criar registro do documento
      const { error: dbError } = await supabase
        .from('documentos')
        .insert({
          cliente_id: clienteId,
          nome: file.name,
          tipo,
          status: 'pendente',
          url: fileName,
        });

      if (dbError) throw dbError;

      await loadData();
      toast({
        title: "Documento enviado",
        description: "O documento foi enviado com sucesso.",
      });
    } catch (err) {
      toast({
        title: "Erro ao enviar documento",
        description: (err as Error).message,
        variant: "destructive",
      });
    }
  };

  const addContato = async (contato: Omit<Contato, 'id' | 'cliente_id'>) => {
    try {
      const { error } = await supabase
        .from('contatos')
        .insert({
          ...contato,
          cliente_id: clienteId,
        });

      if (error) throw error;

      await loadData();
      toast({
        title: "Contato adicionado",
        description: "O contato foi adicionado com sucesso.",
      });
    } catch (err) {
      toast({
        title: "Erro ao adicionar contato",
        description: (err as Error).message,
        variant: "destructive",
      });
    }
  };

  const deleteDocumento = async (documentoId: string) => {
    try {
      const { error } = await supabase
        .from('documentos')
        .delete()
        .eq('id', documentoId);

      if (error) throw error;

      await loadData();
      toast({
        title: "Documento excluído",
        description: "O documento foi excluído com sucesso.",
      });
    } catch (err) {
      toast({
        title: "Erro ao excluir documento",
        description: (err as Error).message,
        variant: "destructive",
      });
    }
  };

  return {
    cliente,
    contatos,
    precatorios,
    documentos,
    atividades,
    compromissos,
    isLoading,
    error,
    refresh: loadData,
    updateCliente,
    uploadDocumento,
    addContato,
    deleteDocumento,
  };
}