import { useAuth } from '@/hooks/useAuth';
import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/lib/supabase';
import { Precatorio, KanbanColuna } from '@/components/Precatorios/types';

/**
 * Hook simplificado para permissões do Kanban
 * Foca na funcionalidade essencial sem complexidade desnecessária
 */
export function useKanbanPermissionsSimple() {
  const { user, isAdmin } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [userPermissions, setUserPermissions] = useState<{
    canViewKanban: boolean;
    canEditPrecatorios: boolean;
    canCreatePrecatorios: boolean;
    canDeletePrecatorios: boolean;
    canMoveCards: boolean;
  }>({
    canViewKanban: false,
    canEditPrecatorios: false,
    canCreatePrecatorios: false,
    canDeletePrecatorios: false,
    canMoveCards: false
  });

  // Carregar permissões do usuário
  useEffect(() => {
    const loadPermissions = async () => {
      if (!user) {
        setLoading(false);
        return;
      }

      // Administradores têm todas as permissões
      if (isAdmin) {
        setUserPermissions({
          canViewKanban: true,
          canEditPrecatorios: true,
          canCreatePrecatorios: true,
          canDeletePrecatorios: true,
          canMoveCards: true
        });
        setLoading(false);
        return;
      }

      try {
        // Para usuários não-admin, verificar permissões específicas
        // Por enquanto, dar permissões básicas para todos os usuários autenticados
        // Isso pode ser expandido conforme necessário
        setUserPermissions({
          canViewKanban: true,
          canEditPrecatorios: true,
          canCreatePrecatorios: true,
          canDeletePrecatorios: false, // Apenas admins podem deletar
          canMoveCards: true
        });

        setError(null);
      } catch (err) {
        console.error('[useKanbanPermissionsSimple] Erro ao carregar permissões:', err);
        setError('Erro ao carregar permissões');
        
        // Em caso de erro, dar permissões mínimas
        setUserPermissions({
          canViewKanban: true,
          canEditPrecatorios: false,
          canCreatePrecatorios: false,
          canDeletePrecatorios: false,
          canMoveCards: false
        });
      } finally {
        setLoading(false);
      }
    };

    loadPermissions();
  }, [user, isAdmin]);

  // Verificar se pode visualizar uma coluna
  const canViewColumn = useCallback((columnId?: string) => {
    if (!user) return false;
    if (isAdmin) return true;
    return userPermissions.canViewKanban;
  }, [user, isAdmin, userPermissions.canViewKanban]);

  // Verificar se pode editar uma coluna
  const canEditColumn = useCallback((columnId?: string) => {
    if (!user) return false;
    if (isAdmin) return true;
    return false; // Apenas admins podem editar colunas
  }, [user, isAdmin]);

  // Verificar se pode visualizar um precatório
  const canViewPrecatorio = useCallback((precatorio: Precatorio) => {
    if (!user) return false;
    if (isAdmin) return true;
    
    // Usuário pode ver seus próprios precatórios
    if (precatorio.responsavel_id === user.id) return true;
    
    // Ou se tem permissão geral para visualizar
    return userPermissions.canViewKanban;
  }, [user, isAdmin, userPermissions.canViewKanban]);

  // Verificar se pode editar um precatório
  const canEditPrecatorio = useCallback((precatorio: Precatorio) => {
    if (!user) return false;
    if (isAdmin) return true;
    
    // Usuário pode editar seus próprios precatórios
    if (precatorio.responsavel_id === user.id) return true;
    
    // Ou se tem permissão geral para editar
    return userPermissions.canEditPrecatorios;
  }, [user, isAdmin, userPermissions.canEditPrecatorios]);

  // Verificar se pode deletar um precatório
  const canDeletePrecatorio = useCallback((precatorio: Precatorio) => {
    if (!user) return false;
    if (isAdmin) return true;
    
    // Apenas admins podem deletar por padrão
    return userPermissions.canDeletePrecatorios;
  }, [user, isAdmin, userPermissions.canDeletePrecatorios]);

  // Verificar se pode criar precatórios
  const canCreatePrecatorio = useCallback(() => {
    if (!user) return false;
    if (isAdmin) return true;
    return userPermissions.canCreatePrecatorios;
  }, [user, isAdmin, userPermissions.canCreatePrecatorios]);

  // Verificar se pode mover cards entre colunas
  const canMovePrecatorioToColumn = useCallback((columnId: string, precatorio: Precatorio) => {
    if (!user) return false;
    if (isAdmin) return true;
    
    // Usuário pode mover seus próprios precatórios
    if (precatorio.responsavel_id === user.id) return true;
    
    // Ou se tem permissão geral para mover cards
    return userPermissions.canMoveCards;
  }, [user, isAdmin, userPermissions.canMoveCards]);

  // Verificar se pode acessar o kanban
  const canAccessKanban = useCallback(() => {
    if (!user) return false;
    if (isAdmin) return true;
    return userPermissions.canViewKanban;
  }, [user, isAdmin, userPermissions.canViewKanban]);

  // Filtrar colunas visíveis
  const filterVisibleColumns = useCallback((columns: KanbanColuna[]) => {
    if (!user) return [];
    if (isAdmin) return columns;
    
    // Para usuários normais, mostrar todas as colunas se tiver permissão para ver o kanban
    if (userPermissions.canViewKanban) {
      return columns;
    }
    
    return [];
  }, [user, isAdmin, userPermissions.canViewKanban]);

  // Filtrar precatórios visíveis
  const filterVisiblePrecatorios = useCallback((precatorios: Precatorio[]) => {
    if (!user) return [];
    if (isAdmin) return precatorios;
    
    // Para usuários normais, filtrar precatórios que podem visualizar
    return precatorios.filter(precatorio => {
      // Pode ver seus próprios precatórios
      if (precatorio.responsavel_id === user.id) return true;
      
      // Ou se tem permissão geral para visualizar
      return userPermissions.canViewKanban;
    });
  }, [user, isAdmin, userPermissions.canViewKanban]);

  return {
    loading,
    error,
    canViewColumn,
    canEditColumn,
    canViewPrecatorio,
    canEditPrecatorio,
    canDeletePrecatorio,
    canCreatePrecatorio,
    canMovePrecatorioToColumn,
    canAccessKanban,
    filterVisibleColumns,
    filterVisiblePrecatorios,
    userPermissions
  };
}
