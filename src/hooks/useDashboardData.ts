import { useState, useEffect, useCallback, useRef } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import {
  getDashboardCompleto,
  getDashboardStats,
  getGraficoStatusPrecatorios,
  getGraficoValorPorMes,
  getPrecatoriosRecentes,
  getTarefasRecentes,
  getDesempenhoEquipeFormatted,
  limparCacheDashboard,
  DashboardData,
  DashboardStats,
  DashboardFilters
} from '@/services/dashboardServiceEnhanced';
import { UserRole } from '@/types/database';
import { usePermissions } from '@/hooks/usePermissions';
import { useVisibilityChange } from '@/hooks/useVisibilityChange';
import { toast } from 'sonner';

// Intervalo de atualização automática (5 minutos)
const AUTO_REFRESH_INTERVAL = 5 * 60 * 1000;

// Tipos de seções do dashboard para loading granular
type DashboardSection = 
  | 'stats' 
  | 'graficoStatus' 
  | 'graficoValor' 
  | 'precatoriosRecentes' 
  | 'tarefasRecentes' 
  | 'desempenhoEquipe'
  | 'all';

// Interface para o hook
interface UseDashboardDataReturn {
  // Dados
  data: DashboardData | null;
  stats: DashboardStats | null;
  graficoStatusPrecatorios: { status: string; quantidade: number }[];
  graficoValorPorMes: { mes: string; valor: number }[];
  precatoriosRecentes: any[];
  tarefasRecentes: any[];
  desempenhoEquipe: { usuario: string; concluidos: number; pendentes: number }[];
  tempoMedioConclusao: number;
  metaAtingida: number;
  metaTotal: number;
  
  // Estado
  isLoading: boolean;
  sectionLoading: Record<DashboardSection, boolean>;
  error: Error | null;
  sectionErrors: Record<DashboardSection, string | null>;
  
  // Filtros
  filters: DashboardFilters;
  setFilters: (filters: DashboardFilters) => void;
  
  // Ações
  refreshData: (section?: DashboardSection) => Promise<void>;
  clearCache: () => void;
  
  // Permissões
  canViewFinancialData: boolean;
  canViewTeamPerformance: boolean;
}

/**
 * Hook para gerenciar dados do dashboard com suporte a multiusuário,
 * cache inteligente, loading states granulares e tratamento de erros.
 */
export function useDashboardData(
  initialFilters: DashboardFilters = {}
): UseDashboardDataReturn {
  // Autenticação e permissões
  const { user, isAdmin, isGerente } = useAuth();
  const { hasPermission } = usePermissions();
  
  // Estados
  const [data, setData] = useState<DashboardData | null>(null);
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [graficoStatusPrecatorios, setGraficoStatusPrecatorios] = useState<{ status: string; quantidade: number }[]>([]);
  const [graficoValorPorMes, setGraficoValorPorMes] = useState<{ mes: string; valor: number }[]>([]);
  const [precatoriosRecentes, setPrecatoriosRecentes] = useState<any[]>([]);
  const [tarefasRecentes, setTarefasRecentes] = useState<any[]>([]);
  const [desempenhoEquipe, setDesempenhoEquipe] = useState<{ usuario: string; concluidos: number; pendentes: number }[]>([]);
  
  // Estados de loading granulares
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [sectionLoading, setSectionLoading] = useState<Record<DashboardSection, boolean>>({
    stats: true,
    graficoStatus: true,
    graficoValor: true,
    precatoriosRecentes: true,
    tarefasRecentes: true,
    desempenhoEquipe: true,
    all: true
  });
  
  // Estados de erro
  const [error, setError] = useState<Error | null>(null);
  const [sectionErrors, setSectionErrors] = useState<Record<DashboardSection, string | null>>({
    stats: null,
    graficoStatus: null,
    graficoValor: null,
    precatoriosRecentes: null,
    tarefasRecentes: null,
    desempenhoEquipe: null,
    all: null
  });
  
  // Filtros
  const [filters, setFiltersState] = useState<DashboardFilters>(initialFilters);
  
  // Refs
  const refreshTimerRef = useRef<NodeJS.Timeout | null>(null);
  const lastRefreshRef = useRef<number>(0);
  const mountedRef = useRef<boolean>(false);
  
  // Verificar permissões
  const canViewFinancialData = isAdmin || isGerente || hasPermission('visualizar_relatorios');
  const canViewTeamPerformance = isAdmin || isGerente;
  
  // Atualizar uma seção específica do loading state
  const updateSectionLoading = useCallback((section: DashboardSection, isLoading: boolean) => {
    setSectionLoading(prev => ({
      ...prev,
      [section]: isLoading
    }));
    
    // Se todas as seções estiverem carregadas, atualizar o estado global
    if (section !== 'all') {
      setSectionLoading(prev => {
        const allSectionsLoaded = Object.entries(prev)
          .filter(([key]) => key !== 'all')
          .every(([_, loading]) => !loading);
          
        return {
          ...prev,
          all: !allSectionsLoaded
        };
      });
    }
  }, []);
  
  // Atualizar um erro específico de seção
  const updateSectionError = useCallback((section: DashboardSection, errorMessage: string | null) => {
    setSectionErrors(prev => ({
      ...prev,
      [section]: errorMessage
    }));
  }, []);
  
  // Atualizar filtros
  const setFilters = useCallback((newFilters: DashboardFilters) => {
    setFiltersState(prev => ({
      ...prev,
      ...newFilters
    }));
    
    // Forçar recarregamento dos dados ao mudar filtros
    refreshData();
  }, []);
  
  // Limpar o cache
  const clearCache = useCallback(() => {
    if (user) {
      limparCacheDashboard(user.id);
      toast.success('Cache do dashboard limpo', {
        description: 'Os dados serão recarregados na próxima atualização'
      });
    }
  }, [user]);
  
  // Função para carregar todos os dados do dashboard
  const loadAllData = useCallback(async () => {
    if (!user) return;
    
    try {
      // Iniciar loading
      setIsLoading(true);
      updateSectionLoading('all', true);
      setError(null);
      updateSectionError('all', null);
      
      // Registrar timestamp de início
      const startTime = performance.now();
      
      // Carregar dados completos
      const result = await getDashboardCompleto(
        user.id,
        user.role as UserRole,
        filters
      );
      
      // Calcular tempo de carregamento
      const loadTime = Math.round(performance.now() - startTime);
      console.log(`Dashboard carregado em ${loadTime}ms`);
      
      // Atualizar estados
      setData(result);
      setStats(result.stats);
      setGraficoStatusPrecatorios(result.graficoStatusPrecatorios);
      setGraficoValorPorMes(result.graficoValorPorMes);
      setPrecatoriosRecentes(result.precatoriosRecentes);
      setTarefasRecentes(result.tarefasRecentes);
      setDesempenhoEquipe(result.desempenhoEquipe);
      
      // Finalizar loading
      setIsLoading(false);
      
      // Atualizar loading de todas as seções
      Object.keys(sectionLoading).forEach(section => {
        updateSectionLoading(section as DashboardSection, false);
      });
      
      // Registrar timestamp da última atualização
      lastRefreshRef.current = Date.now();
    } catch (err) {
      console.error('Erro ao carregar dados do dashboard:', err);
      setError(err instanceof Error ? err : new Error(String(err)));
      updateSectionError('all', err instanceof Error ? err.message : String(err));
      
      // Finalizar loading mesmo com erro
      setIsLoading(false);
      updateSectionLoading('all', false);
      
      // Mostrar toast de erro
      toast.error('Erro ao carregar dashboard', {
        description: err instanceof Error ? err.message : 'Erro desconhecido ao carregar dados'
      });
    }
  }, [user, filters, updateSectionLoading, updateSectionError]);
  
  // Função para carregar uma seção específica
  const loadSection = useCallback(async (section: DashboardSection) => {
    if (!user || section === 'all') return;
    
    try {
      // Iniciar loading da seção
      updateSectionLoading(section, true);
      updateSectionError(section, null);
      
      switch (section) {
        case 'stats':
          const stats = await getDashboardStats(user.id, user.role as UserRole, filters);
          setStats(stats);
          break;
          
        case 'graficoStatus':
          const statusData = await getGraficoStatusPrecatorios(user.id, user.role as UserRole, filters);
          setGraficoStatusPrecatorios(statusData);
          break;
          
        case 'graficoValor':
          const valorData = await getGraficoValorPorMes(user.id, user.role as UserRole, filters);
          setGraficoValorPorMes(valorData);
          break;
          
        case 'precatoriosRecentes':
          const precatorios = await getPrecatoriosRecentes(user.id, user.role as UserRole);
          setPrecatoriosRecentes(precatorios);
          break;
          
        case 'tarefasRecentes':
          const tarefas = await getTarefasRecentes(user.id, user.role as UserRole);
          setTarefasRecentes(tarefas);
          break;
          
        case 'desempenhoEquipe':
          const desempenho = await getDesempenhoEquipeFormatted(user.id, user.role as UserRole);
          setDesempenhoEquipe(desempenho);
          break;
      }
      
      // Finalizar loading da seção
      updateSectionLoading(section, false);
    } catch (err) {
      console.error(`Erro ao carregar seção ${section}:`, err);
      updateSectionError(section, err instanceof Error ? err.message : String(err));
      
      // Finalizar loading mesmo com erro
      updateSectionLoading(section, false);
    }
  }, [user, filters, updateSectionLoading, updateSectionError]);
  
  // Função para atualizar dados (pública)
  const refreshData = useCallback(async (section?: DashboardSection) => {
    // Evitar atualizações muito frequentes (throttling)
    const now = Date.now();
    const timeSinceLastRefresh = now - lastRefreshRef.current;
    
    if (timeSinceLastRefresh < 2000) {
      console.log('Atualização muito frequente, ignorando');
      return;
    }
    
    if (section) {
      await loadSection(section);
    } else {
      await loadAllData();
    }
  }, [loadAllData, loadSection]);
  
  // Configurar auto-refresh
  useEffect(() => {
    if (!mountedRef.current) return;
    
    // Limpar timer existente
    if (refreshTimerRef.current) {
      clearInterval(refreshTimerRef.current);
    }
    
    // Configurar novo timer
    refreshTimerRef.current = setInterval(() => {
      console.log('Auto-refresh do dashboard');
      refreshData();
    }, AUTO_REFRESH_INTERVAL);
    
    // Cleanup
    return () => {
      if (refreshTimerRef.current) {
        clearInterval(refreshTimerRef.current);
      }
    };
  }, [refreshData]);
  
  // Carregar dados iniciais
  useEffect(() => {
    if (!user) return;
    
    loadAllData();
    
    // Marcar componente como montado
    mountedRef.current = true;
    
    // Cleanup
    return () => {
      mountedRef.current = false;
      if (refreshTimerRef.current) {
        clearInterval(refreshTimerRef.current);
      }
    };
  }, [user, loadAllData]);
  
  // Recarregar dados quando o usuário voltar para a página
  useVisibilityChange({
    onVisible: () => {
      // Verificar se passou tempo suficiente desde a última atualização
      const timeSinceLastRefresh = Date.now() - lastRefreshRef.current;
      if (timeSinceLastRefresh > 30000) { // 30 segundos
        console.log('Recarregando dashboard após retorno à página');
        refreshData();
      }
    }
  });
  
  // Atualizar quando os filtros mudarem
  useEffect(() => {
    if (mountedRef.current && user) {
      loadAllData();
    }
  }, [filters, user, loadAllData]);
  
  // Escutar eventos de atualização de dados
  useEffect(() => {
    const handleDataUpdated = () => {
      console.log('Evento de atualização de dados recebido');
      refreshData();
    };
    
    // Registrar listeners
    document.addEventListener('precatorios-updated', handleDataUpdated);
    document.addEventListener('tarefas-updated', handleDataUpdated);
    document.addEventListener('clientes-updated', handleDataUpdated);
    
    // Cleanup
    return () => {
      document.removeEventListener('precatorios-updated', handleDataUpdated);
      document.removeEventListener('tarefas-updated', handleDataUpdated);
      document.removeEventListener('clientes-updated', handleDataUpdated);
    };
  }, [refreshData]);
  
  // Derivar métricas calculadas do estado
  const tempoMedioConclusao = data?.tempoMedioConclusao || 0;
  const metaAtingida = data?.metaAtingida || 0;
  const metaTotal = data?.metaTotal || 100;
  
  return {
    // Dados
    data,
    stats,
    graficoStatusPrecatorios,
    graficoValorPorMes,
    precatoriosRecentes,
    tarefasRecentes,
    desempenhoEquipe,
    tempoMedioConclusao,
    metaAtingida,
    metaTotal,
    
    // Estado
    isLoading,
    sectionLoading,
    error,
    sectionErrors,
    
    // Filtros
    filters,
    setFilters,
    
    // Ações
    refreshData,
    clearCache,
    
    // Permissões
    canViewFinancialData,
    canViewTeamPerformance
  };
}
