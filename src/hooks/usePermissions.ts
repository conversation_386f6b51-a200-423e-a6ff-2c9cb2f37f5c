import { useAuth } from '@/hooks/useAuth';
import { useState, useEffect, useMemo, useCallback } from 'react';
import { supabase } from '@/lib/supabase';
import { Permission, PermissionAction, ResourceType } from '@/types/permissions';
import {
  getEnhancedUserPermissions,
  hasEnhancedPermission,
  EnhancedUserPermissionsData,
  AVAILABLE_PAGES
} from '@/services/enhancedPermissionsService';

/**
 * Hook personalizado para gerenciar permissões do usuário
 */
export function usePermissions() {
  const { user } = useAuth();
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [enhancedPermissions, setEnhancedPermissions] = useState<EnhancedUserPermissionsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Carregar permissões do usuário atual
  const loadPermissions = useCallback(async () => {
    if (!user) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Carregar permissões aprimoradas
      const enhancedData = await getEnhancedUserPermissions(user.id);
      setEnhancedPermissions(enhancedData);

      // Manter compatibilidade com o sistema antigo
      try {
        const { data, error } = await supabase.rpc('get_user_permissions', {
          p_user_id: user.id
        });

        if (!error && data) {
          const rolePermissions = data?.role_permissions || [];
          const specificPermissions = data?.specific_permissions || [];
          setPermissions([...rolePermissions, ...specificPermissions]);
        }
      } catch (legacyError) {
        console.warn('Sistema de permissões legado não disponível:', legacyError);
      }

    } catch (err) {
      console.error('Erro ao carregar permissões:', err);
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
    } finally {
      setLoading(false);
    }
  }, [user]);

  useEffect(() => {
    loadPermissions();
  }, [loadPermissions]);

  // Verificar se o usuário tem uma permissão específica
  const can = (action: PermissionAction | string, resourceType: ResourceType | string, resourceId?: string) => {
    if (!user) return false;
    
    // Administradores têm todas as permissões
    if (user.role === 'admin') return true;
    
    // Verificar permissões específicas para um recurso específico
    if (resourceId) {
      const specificPermission = permissions.find(p => 
        p.action === action && 
        p.resource_type === resourceType && 
        p.resource_id === resourceId
      );
      
      if (specificPermission) {
        return specificPermission.allowed;
      }
    }
    
    // Verificar permissões gerais para o tipo de recurso
    const generalPermission = permissions.find(p => 
      p.action === action && 
      p.resource_type === resourceType && 
      !p.resource_id
    );
    
    return generalPermission ? generalPermission.allowed : false;
  };

  // Verificar se o usuário pode ver um recurso (alias para can('view', ...))
  const canSee = (action: PermissionAction | string, resourceType: ResourceType | string, resourceId?: string) => {
    return can(action, resourceType, resourceId);
  };

  // Verificar se o usuário pode criar um recurso
  const canCreate = (resourceType: ResourceType | string) => {
    return can('create', resourceType);
  };

  // Verificar se o usuário pode editar um recurso
  const canEdit = (resourceType: ResourceType | string, resourceId?: string) => {
    return can('edit', resourceType, resourceId);
  };

  // Verificar se o usuário pode excluir um recurso
  const canDelete = (resourceType: ResourceType | string, resourceId?: string) => {
    return can('delete', resourceType, resourceId);
  };

  // Verificar se o usuário tem acesso a uma página
  const canAccessPage = (pagePath: string) => {
    if (!user) return false;
    if (user.role === 'admin') return true;
    
    // Verificar nas permissões de página
    const pageAccess = permissions.find(p => 
      p.resource_type === 'page' && 
      p.resource_id === pagePath
    );
    
    return pageAccess ? pageAccess.allowed : false;
  };

  // Verificar se o usuário pode ver tarefas de outro usuário
  const canSeeUserTasks = (userId: string) => {
    if (!user) return false;
    if (user.id === userId) return true; // Usuário sempre pode ver suas próprias tarefas
    if (user.role === 'admin') return true;

    // Verificar permissão específica
    return can('view', 'task', userId);
  };

  // Métodos aprimorados usando o novo sistema
  const hasPermission = useCallback((
    resourceType: string,
    action: string,
    resourceId?: string
  ): boolean => {
    if (!enhancedPermissions || !user?.id) return false;

    // Admin tem todas as permissões
    if (user.role === 'admin') return true;

    // Verificar permissões específicas primeiro
    const specificPermission = enhancedPermissions.specific_permissions.find(
      p => p.resource_type === resourceType &&
           p.action === action &&
           (!resourceId || p.resource_id === resourceId)
    );

    if (specificPermission !== undefined) {
      return specificPermission.allowed;
    }

    // Verificar permissões do role
    const rolePermission = enhancedPermissions.role_permissions.find(
      p => p.resource_type === resourceType && p.action === action
    );

    return rolePermission?.allowed || false;
  }, [enhancedPermissions, user]);

  const canAccessPageEnhanced = useCallback((pagePath: string): boolean => {
    if (!enhancedPermissions || !user?.id) return false;

    // Admin tem acesso a todas as páginas
    if (user.role === 'admin') return true;

    // Verificar configurações específicas de acesso à página
    const pageAccess = enhancedPermissions.page_access.find(p => p.page_path === pagePath);
    if (pageAccess !== undefined) {
      return pageAccess.can_access;
    }

    // Fallback para o método antigo
    return canAccessPage(pagePath);
  }, [enhancedPermissions, user, canAccessPage]);

  const canSeeData = useCallback((dataType: 'own' | 'team' | 'all'): boolean => {
    if (!enhancedPermissions || !user?.id) return false;

    // Admin pode ver todos os dados
    if (user.role === 'admin') return true;

    const dataVisibility = enhancedPermissions.data_visibility;

    switch (dataType) {
      case 'own':
        return dataVisibility.can_see_own_data;
      case 'team':
        return dataVisibility.can_see_team_data;
      case 'all':
        return dataVisibility.can_see_all_data;
      default:
        return false;
    }
  }, [enhancedPermissions, user]);

  const isAdmin = user?.role === 'admin';
  const isManager = user?.role === 'gerente_precatorio' || user?.role === 'gerente_operacional';

  const refreshPermissions = useCallback(async () => {
    await loadPermissions();
  }, [loadPermissions]);

  return {
    loading,
    error,
    permissions,
    enhancedPermissions,
    can,
    canSee,
    canCreate,
    canEdit,
    canDelete,
    canAccessPage,
    canSeeUserTasks,
    // Métodos aprimorados
    hasPermission,
    canAccessPageEnhanced,
    canSeeData,
    isAdmin,
    isManager,
    refreshPermissions
  };
}
