import { useCallback, useMemo } from 'react';
import { usePermissions } from '@/hooks/usePermissions';
import { useAuth } from '@/hooks/useAuth';
import { KanbanColuna, Precatorio, CustomView } from '@/components/Precatorios/types';

/**
 * Hook especializado para gerenciar permissões no contexto do Kanban
 */
export function useKanbanPermissions() {
  const { can, canSee, canEdit, canCreate, canDelete, isAdmin } = usePermissions();
  const { user } = useAuth();

  /**
   * Verifica se o usuário pode visualizar uma coluna específica
   */
  const canViewColumn = useCallback((column: KanbanColuna): boolean => {
    // Administradores podem ver todas as colunas
    if (isAdmin) return true;
    
    // Verificar permissão específica para esta coluna
    return canSee('view', 'kanban_column', column.id);
  }, [isAdmin, canSee]);

  /**
   * Verifica se o usuário pode editar uma coluna específica
   */
  const canEditColumn = useCallback((column: KanbanColuna): boolean => {
    // Administradores podem editar todas as colunas
    if (isAdmin) return true;
    
    // Verificar permissão específica para esta coluna
    return canEdit('kanban_column', column.id);
  }, [isAdmin, canEdit]);

  /**
   * Verifica se o usuário pode mover um precatório para uma coluna específica
   */
  const canMovePrecatorioToColumn = useCallback((precatorio: Precatorio, column: KanbanColuna): boolean => {
    // Administradores podem mover qualquer precatório para qualquer coluna
    if (isAdmin) return true;
    
    // Verificar se o usuário pode editar este precatório
    const canEditPrecatorio = canEdit(precatorio.tipo === 'RPV' ? 'rpv' : 'precatorio', precatorio.id);
    
    // Verificar se o usuário pode ver esta coluna
    const canViewThisColumn = canViewColumn(column);
    
    // Verificar se o usuário é o responsável pelo precatório
    const isResponsible = precatorio.responsavel_id === user?.id;
    
    // Verificar se o usuário tem permissão específica para mover precatórios
    const hasMovePrecatorioPermission = can('move', precatorio.tipo === 'RPV' ? 'rpv' : 'precatorio', precatorio.id);
    
    return (canEditPrecatorio || isResponsible || hasMovePrecatorioPermission) && canViewThisColumn;
  }, [isAdmin, canEdit, canViewColumn, can, user]);

  /**
   * Verifica se o usuário pode visualizar um precatório específico
   */
  const canViewPrecatorio = useCallback((precatorio: Precatorio): boolean => {
    // Administradores podem ver todos os precatórios
    if (isAdmin) return true;
    
    // Verificar permissão específica para este precatório
    return canSee('view', precatorio.tipo === 'RPV' ? 'rpv' : 'precatorio', precatorio.id);
  }, [isAdmin, canSee]);

  /**
   * Verifica se o usuário pode editar um precatório específico
   */
  const canEditPrecatorio = useCallback((precatorio: Precatorio): boolean => {
    // Administradores podem editar todos os precatórios
    if (isAdmin) return true;
    
    // Verificar se o usuário é o responsável pelo precatório
    const isResponsible = precatorio.responsavel_id === user?.id;
    
    // Verificar permissão específica para este precatório
    return isResponsible || canEdit(precatorio.tipo === 'RPV' ? 'rpv' : 'precatorio', precatorio.id);
  }, [isAdmin, canEdit, user]);

  /**
   * Verifica se o usuário pode excluir um precatório específico
   */
  const canDeletePrecatorio = useCallback((precatorio: Precatorio): boolean => {
    // Administradores podem excluir todos os precatórios
    if (isAdmin) return true;
    
    // Verificar permissão específica para este precatório
    return canDelete(precatorio.tipo === 'RPV' ? 'rpv' : 'precatorio', precatorio.id);
  }, [isAdmin, canDelete]);

  /**
   * Verifica se o usuário pode criar um precatório
   */
  const canCreatePrecatorio = useCallback((tipo: 'PRECATORIO' | 'RPV'): boolean => {
    // Administradores podem criar qualquer tipo de precatório
    if (isAdmin) return true;
    
    // Verificar permissão específica para criar este tipo de precatório
    return canCreate(tipo === 'RPV' ? 'rpv' : 'precatorio');
  }, [isAdmin, canCreate]);

  /**
   * Verifica se o usuário pode usar uma visualização personalizada específica
   */
  const canUseCustomView = useCallback((view: CustomView): boolean => {
    // Administradores podem usar todas as visualizações
    if (isAdmin) return true;
    
    // Visualizações públicas podem ser usadas por todos
    if (view.is_public) return true;
    
    // Visualizações criadas pelo próprio usuário podem ser usadas
    if (view.user_id === user?.id) return true;
    
    // Verificar permissão específica para esta visualização
    return canSee('view', 'custom_view', view.id);
  }, [isAdmin, canSee, user]);

  /**
   * Verifica se o usuário pode editar uma visualização personalizada específica
   */
  const canEditCustomView = useCallback((view: CustomView): boolean => {
    // Administradores podem editar todas as visualizações
    if (isAdmin) return true;
    
    // Visualizações criadas pelo próprio usuário podem ser editadas
    if (view.user_id === user?.id) return true;
    
    // Verificar permissão específica para esta visualização
    return canEdit('custom_view', view.id);
  }, [isAdmin, canEdit, user]);

  /**
   * Filtra colunas que o usuário tem permissão para visualizar
   */
  const filterVisibleColumns = useCallback((columns: KanbanColuna[]): KanbanColuna[] => {
    // Administradores podem ver todas as colunas
    if (isAdmin) return columns;
    
    // Filtrar colunas que o usuário tem permissão para visualizar
    return columns.filter(column => canViewColumn(column));
  }, [isAdmin, canViewColumn]);

  /**
   * Filtra precatórios que o usuário tem permissão para visualizar
   */
  const filterVisiblePrecatorios = useCallback((precatorios: Precatorio[]): Precatorio[] => {
    // Administradores podem ver todos os precatórios
    if (isAdmin) return precatorios;
    
    // Filtrar precatórios que o usuário tem permissão para visualizar
    return precatorios.filter(precatorio => canViewPrecatorio(precatorio));
  }, [isAdmin, canViewPrecatorio]);

  return {
    canViewColumn,
    canEditColumn,
    canMovePrecatorioToColumn,
    canViewPrecatorio,
    canEditPrecatorio,
    canDeletePrecatorio,
    canCreatePrecatorio,
    canUseCustomView,
    canEditCustomView,
    filterVisibleColumns,
    filterVisiblePrecatorios
  };
}
