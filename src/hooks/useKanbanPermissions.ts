import { useAuth } from '@/hooks/useAuth';
import { usePermissions } from '@/hooks/usePermissions';
import { useState, useEffect, useMemo, useCallback } from 'react';
import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';
import { Precatorio, KanbanColuna } from '@/components/Precatorios/types';

/**
 * Hook personalizado para gerenciar permissões específicas do Kanban
 */
export function useKanbanPermissions() {
  const { user } = useAuth();
  const { can, canSee, isAdmin } = usePermissions();
  const [columnPermissions, setColumnPermissions] = useState<Record<string, {
    can_view: boolean;
    can_edit: boolean;
    can_delete: boolean;
    can_move_cards: boolean;
  }>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  // Carregar permissões de colunas do usuário atual
  useEffect(() => {
    const loadColumnPermissions = async () => {
      if (!user) {
        setLoading(false);
        return;
      }

      // Administradores têm todas as permissões, não precisamos carregar do banco
      if (user.role === 'admin') {
        setLoading(false);
        return;
      }

      try {
        console.log('[useKanbanPermissions] Carregando permissões de colunas para o usuário:', user.id);

        const { data, error } = await supabase
          .from('kanban_column_permissions')
          .select('*')
          .eq('user_id', user.id);

        if (error) {
          console.error('[useKanbanPermissions] Erro ao carregar permissões de colunas:', error);
          setError(new Error(`Erro ao carregar permissões: ${error.message}`));

          // Não retornar aqui para permitir que o código continue e defina loading como false
        } else {
          console.log(`[useKanbanPermissions] Carregadas ${data?.length || 0} permissões de colunas`);
        }

        // Transformar array em objeto indexado por column_id
        const permissionsMap = (data || []).reduce((acc, permission) => {
          acc[permission.column_id] = {
            can_view: permission.can_view,
            can_edit: permission.can_edit,
            can_delete: permission.can_delete,
            can_move_cards: permission.can_move_cards || false // Garantir que exista mesmo se for null
          };
          return acc;
        }, {} as Record<string, any>);

        setColumnPermissions(permissionsMap);
      } catch (error) {
        console.error('[useKanbanPermissions] Erro ao carregar permissões de colunas:', error);
        setError(error instanceof Error ? error : new Error('Erro desconhecido ao carregar permissões'));
      } finally {
        setLoading(false);
      }
    };

    loadColumnPermissions();
  }, [user]);

  // Verificar permissão para visualizar uma coluna
  const canViewColumn = useCallback((columnId?: string) => {
    if (!user) return false;

    // Administradores sempre podem ver todas as colunas
    if (user.role === 'admin') return true;

    // Se não tiver ID da coluna, verifica permissão geral
    if (!columnId) {
      return canSee('view', 'kanban_column');
    }

    // Verificar permissão específica para esta coluna
    // Se não houver permissões específicas definidas, permitir acesso por padrão para evitar problemas
    if (Object.keys(columnPermissions).length === 0) {
      console.log('[useKanbanPermissions] Nenhuma permissão específica definida, permitindo acesso por padrão');
      return true;
    }

    return columnPermissions[columnId]?.can_view || false;
  }, [user, canSee, columnPermissions]);

  // Verificar permissão para editar uma coluna
  const canEditColumn = useCallback((columnId?: string) => {
    if (!user) return false;
    if (user.role === 'admin') return true;

    // Se não tiver ID da coluna, verifica permissão geral
    if (!columnId) {
      return can('edit', 'kanban_column');
    }

    // Verificar permissão específica para esta coluna
    return columnPermissions[columnId]?.can_edit || false;
  }, [user, can, columnPermissions]);

  // Verificar permissão para excluir uma coluna
  const canDeleteColumn = useCallback((columnId?: string) => {
    if (!user) return false;
    if (user.role === 'admin') return true;

    // Se não tiver ID da coluna, verifica permissão geral
    if (!columnId) {
      return can('delete', 'kanban_column');
    }

    // Verificar permissão específica para esta coluna
    return columnPermissions[columnId]?.can_delete || false;
  }, [user, can, columnPermissions]);

  // Verificar permissão para mover precatórios para uma coluna
  const canMovePrecatorioToColumn = useCallback((columnId?: string, precatorioId?: string) => {
    if (!user) return false;
    if (user.role === 'admin') return true;

    // Se não tiver ID da coluna, verifica permissão geral
    if (!columnId) {
      return can('edit', 'kanban_column');
    }

    // Verificar permissão específica para esta coluna
    const hasColumnPermission = columnPermissions[columnId]?.can_move_cards || false;

    // Se tiver permissão para a coluna, já pode mover
    if (hasColumnPermission) return true;

    // Se tiver ID do precatório, verificar se é o responsável
    if (precatorioId) {
      // Verificar se tem permissão para editar o precatório
      return can('edit', 'precatorio', precatorioId);
    }

    return false;
  }, [user, can, columnPermissions]);

  // Verificar permissão para visualizar um precatório
  const canViewPrecatorio = useCallback((precatorioId?: string, tipo: 'precatorio' | 'rpv' = 'precatorio') => {
    if (!user) return false;
    if (user.role === 'admin') return true;

    // Verificar permissão com base no tipo (precatório ou RPV)
    const resourceType = tipo === 'rpv' ? 'rpv' : 'precatorio';

    // Se não tiver ID do precatório, verifica permissão geral
    if (!precatorioId) {
      return canSee('view', resourceType);
    }

    // Verificar permissão específica para este precatório
    return canSee('view', resourceType, precatorioId);
  }, [user, canSee]);

  // Verificar permissão para visualizar um precatório (versão que aceita objeto Precatorio)
  const canViewPrecatorioObj = useCallback((precatorio: Precatorio | null) => {
    if (!precatorio) return false;
    if (!user) return false;
    if (user.role === 'admin') return true;

    // Verificar se o usuário é o responsável pelo precatório
    if (precatorio.responsavel_id === user.id) return true;

    // Verificar permissão com base no tipo
    const tipo = precatorio.tipo === 'RPV' ? 'rpv' : 'precatorio';
    return canSee('view', tipo, precatorio.id);
  }, [user, canSee]);

  // Verificar permissão para editar um precatório
  const canEditPrecatorio = useCallback((precatorioId?: string, tipo: 'precatorio' | 'rpv' = 'precatorio') => {
    if (!user) return false;
    if (user.role === 'admin') return true;

    // Verificar permissão com base no tipo (precatório ou RPV)
    const resourceType = tipo === 'rpv' ? 'rpv' : 'precatorio';

    // Se não tiver ID do precatório, verifica permissão geral
    if (!precatorioId) {
      return can('edit', resourceType);
    }

    // Verificar permissão específica para este precatório
    return can('edit', resourceType, precatorioId);
  }, [user, can]);

  // Verificar permissão para editar um precatório (versão que aceita objeto Precatorio)
  const canEditPrecatorioObj = useCallback((precatorio: Precatorio | null) => {
    if (!precatorio) return false;
    if (!user) return false;
    if (user.role === 'admin') return true;

    // Verificar se o usuário é o responsável pelo precatório
    if (precatorio.responsavel_id === user.id) return true;

    // Verificar permissão com base no tipo
    const tipo = precatorio.tipo === 'RPV' ? 'rpv' : 'precatorio';
    return can('edit', tipo, precatorio.id);
  }, [user, can]);

  // Verificar permissão para excluir um precatório
  const canDeletePrecatorio = useCallback((precatorioId?: string, tipo: 'precatorio' | 'rpv' = 'precatorio') => {
    if (!user) return false;
    if (user.role === 'admin') return true;

    // Verificar permissão com base no tipo (precatório ou RPV)
    const resourceType = tipo === 'rpv' ? 'rpv' : 'precatorio';

    // Se não tiver ID do precatório, verifica permissão geral
    if (!precatorioId) {
      return can('delete', resourceType);
    }

    // Verificar permissão específica para este precatório
    return can('delete', resourceType, precatorioId);
  }, [user, can]);

  // Verificar permissão para excluir um precatório (versão que aceita objeto Precatorio)
  const canDeletePrecatorioObj = useCallback((precatorio: Precatorio | null) => {
    if (!precatorio) return false;
    if (!user) return false;
    if (user.role === 'admin') return true;

    // Verificar se o usuário é o responsável pelo precatório
    if (precatorio.responsavel_id === user.id) return true;

    // Verificar permissão com base no tipo
    const tipo = precatorio.tipo === 'RPV' ? 'rpv' : 'precatorio';
    return can('delete', tipo, precatorio.id);
  }, [user, can]);

  // Verificar permissão para criar um precatório
  const canCreatePrecatorio = useCallback((tipo: 'precatorio' | 'rpv' = 'precatorio') => {
    if (!user) return false;
    if (user.role === 'admin') return true;

    // Verificar permissão com base no tipo (precatório ou RPV)
    const resourceType = tipo === 'rpv' ? 'rpv' : 'precatorio';

    return can('create', resourceType);
  }, [user, can]);

  // Filtrar colunas visíveis com base nas permissões
  const filterVisibleColumns = useCallback((columns: KanbanColuna[]) => {
    if (!user) return [];

    // Administradores sempre podem ver todas as colunas
    if (user.role === 'admin') return columns;

    // Se não houver permissões específicas definidas, permitir acesso a todas as colunas por padrão
    if (Object.keys(columnPermissions).length === 0) {
      console.log('[useKanbanPermissions] Nenhuma permissão específica definida, permitindo acesso a todas as colunas por padrão');
      return columns;
    }

    return columns.filter(column => canViewColumn(column.id));
  }, [user, columnPermissions, canViewColumn]);

  // Filtrar precatórios visíveis com base nas permissões
  const filterVisiblePrecatorios = useCallback((precatorios: Precatorio[]) => {
    if (!user) return [];

    // Administradores sempre podem ver todos os precatórios
    if (user.role === 'admin') return precatorios;

    // Filtrar precatórios com base nas permissões
    return precatorios.filter(precatorio => {
      // Verificar se o usuário é o responsável pelo precatório
      if (precatorio.responsavel_id === user.id) return true;

      // Verificar permissão específica
      return canViewPrecatorio(precatorio.id, precatorio.tipo === 'RPV' ? 'rpv' : 'precatorio');
    });
  }, [user, canViewPrecatorio]);

  // Verificar se o usuário tem permissão para mover um precatório para uma coluna específica
  const canMovePrecatorioFromTo = useCallback((precatorio: Precatorio, sourceColumnId: string, targetColumnId: string) => {
    if (!user) return false;
    if (user.role === 'admin') return true;

    // Verificar se o usuário é o responsável pelo precatório
    if (precatorio.responsavel_id === user.id) return true;

    // Verificar permissão para mover precatórios na coluna de origem
    const canMoveFromSource = canMovePrecatorioToColumn(sourceColumnId, precatorio.id);

    // Verificar permissão para mover precatórios na coluna de destino
    const canMoveToTarget = canMovePrecatorioToColumn(targetColumnId, precatorio.id);

    // Precisa ter permissão em ambas as colunas
    return canMoveFromSource && canMoveToTarget;
  }, [user, canMovePrecatorioToColumn]);

  // Verificar se o usuário tem permissão para acessar o Kanban
  const canAccessKanban = useCallback(() => {
    if (!user) return false;
    if (user.role === 'admin') return true;

    return canSee('view', 'kanban');
  }, [user, canSee]);

  return {
    loading,
    error,
    canViewColumn,
    canEditColumn,
    canDeleteColumn,
    canMovePrecatorioToColumn,
    canMovePrecatorioFromTo,
    canViewPrecatorio,
    canViewPrecatorioObj,
    canEditPrecatorio,
    canEditPrecatorioObj,
    canDeletePrecatorio,
    canDeletePrecatorioObj,
    canCreatePrecatorio,
    canAccessKanban,
    filterVisibleColumns,
    filterVisiblePrecatorios
  };
}
