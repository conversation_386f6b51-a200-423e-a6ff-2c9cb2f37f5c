import { useContext, useCallback } from 'react';
import { PermissionsContext } from '@/contexts/PermissionsContext';
import { useAuth } from '@/hooks/useAuth';

// Mapeamento de permissões padrão baseadas no papel do usuário
const DEFAULT_ROLE_PERMISSIONS: Record<string, Record<string, string[]>> = {
  'admin': {
    'cliente': ['view', 'create', 'edit', 'delete'],
    'precatorio': ['view', 'create', 'edit', 'delete'],
    'rpv': ['view', 'create', 'edit', 'delete'],
    'tarefa': ['view', 'create', 'edit', 'delete', 'view_all'],
    'documento': ['view', 'create', 'edit', 'delete'],
    'relatorio': ['view_precatorio', 'view_rpv', 'view_captacao', 'view_completo'],
    'user': ['view', 'create', 'edit', 'delete'],
    'system': ['configure', 'manage_roles']
  },
  'gerente_geral': {
    'cliente': ['view', 'create', 'edit'],
    'precatorio': ['view', 'create', 'edit'],
    'rpv': ['view', 'create', 'edit'],
    'tarefa': ['view', 'create', 'edit', 'delete', 'view_all'],
    'documento': ['view', 'create', 'edit'],
    'relatorio': ['view_precatorio', 'view_rpv', 'view_captacao']
  },
  'gerente_precatorio': {
    'cliente': ['view'],
    'precatorio': ['view', 'create', 'edit'],
    'tarefa': ['view', 'create', 'edit', 'delete'],
    'documento': ['view', 'create']
  },
  'gerente_rpv': {
    'cliente': ['view'],
    'rpv': ['view', 'create', 'edit'],
    'tarefa': ['view', 'create', 'edit', 'delete'],
    'documento': ['view', 'create']
  },
  'captador': {
    'cliente': ['view', 'create'],
    'precatorio': ['view', 'create'],
    'rpv': ['view', 'create'],
    'tarefa': ['view', 'create'],
    'documento': ['view']
  },
  'operacional_precatorio': {
    'cliente': ['view'],
    'precatorio': ['view', 'edit'],
    'tarefa': ['view', 'create', 'edit'],
    'documento': ['view', 'create']
  },
  'operacional_rpv': {
    'cliente': ['view'],
    'rpv': ['view', 'edit'],
    'tarefa': ['view', 'create', 'edit'],
    'documento': ['view', 'create']
  },
  'operacional_completo': {
    'cliente': ['view'],
    'precatorio': ['view', 'edit'],
    'rpv': ['view', 'edit'],
    'tarefa': ['view', 'create', 'edit'],
    'documento': ['view', 'create']
  }
};

export function usePermissions() {
  const permissionsContext = useContext(PermissionsContext);
  const { user } = useAuth();

  if (!permissionsContext) {
    throw new Error('usePermissions deve ser usado dentro de um PermissionsProvider');
  }

  const { hasPermission, canAccessPage, canViewTask, refreshPermissions, permissions, loading } = permissionsContext;

  // Função auxiliar para verificar se o usuário pode realizar uma ação em um recurso
  const can = useCallback((action: string, resource: string, resourceId?: string): boolean => {
    // Se não há usuário logado, não tem permissão
    if (!user) return false;

    // Administradores têm todas as permissões
    if (user.role === 'admin') return true;

    // Verificar permissão específica usando o contexto
    const contextPermission = hasPermission(resource, action, resourceId);

    // Se o contexto retornou uma permissão, usar ela
    if (contextPermission) return true;

    // Verificar permissões padrão baseadas no papel do usuário
    const rolePermissions = DEFAULT_ROLE_PERMISSIONS[user.role];
    if (rolePermissions && rolePermissions[resource]) {
      return rolePermissions[resource].includes(action);
    }

    return false;
  }, [user, hasPermission]);

  // Função auxiliar para verificar se o usuário pode ver um botão/ação
  const canSee = useCallback((action: string, resource: string, resourceId?: string): boolean => {
    return can(action, resource, resourceId);
  }, [can]);

  // Função auxiliar para verificar se o usuário pode editar um recurso
  const canEdit = useCallback((resource: string, resourceId?: string): boolean => {
    return can('edit', resource, resourceId);
  }, [can]);

  // Função auxiliar para verificar se o usuário pode criar um recurso
  const canCreate = useCallback((resource: string): boolean => {
    return can('create', resource);
  }, [can]);

  // Função auxiliar para verificar se o usuário pode excluir um recurso
  const canDelete = useCallback((resource: string, resourceId?: string): boolean => {
    return can('delete', resource, resourceId);
  }, [can]);

  // Função auxiliar para verificar se o usuário pode visualizar um recurso
  const canView = useCallback((resource: string, resourceId?: string): boolean => {
    return can('view', resource, resourceId);
  }, [can]);

  return {
    can,
    canSee,
    canEdit,
    canCreate,
    canDelete,
    canView,
    canAccessPage,
    canViewTask,
    refreshPermissions,
    permissions,
    loading,
    isAdmin: user?.role === 'admin'
  };
}
