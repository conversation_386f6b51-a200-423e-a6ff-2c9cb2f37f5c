import { supabase } from '@/lib/supabase';

export interface DeletedItem {
  id: string;
  item_id: string;
  item_type: 'precatorio' | 'tag' | 'coluna' | 'cliente';
  item_data: any;
  deleted_by?: string;
  deleted_at: string;
  can_restore: boolean;
  restore_deadline?: string;
  deleted_by_user?: {
    id: string;
    nome: string;
    email: string;
    avatar_url?: string;
  };
}

// Função para buscar itens excluídos
export const fetchDeletedItems = async (
  itemType?: 'precatorio' | 'tag' | 'coluna' | 'cliente',
  limit: number = 50
): Promise<DeletedItem[]> => {
  try {
    let query = supabase
      .from('deleted_items')
      .select(`
        *,
        deleted_by_user:deleted_by (id, nome, email, avatar_url)
      `)
      .eq('can_restore', true)
      .order('deleted_at', { ascending: false })
      .limit(limit);

    // Filtrar por tipo, se especificado
    if (itemType) {
      query = query.eq('item_type', itemType);
    }

    const { data, error } = await query;

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Erro ao buscar itens excluídos:', error);
    throw error;
  }
};

// Função para restaurar um item excluído
export const restoreDeletedItem = async (deletedItemId: string): Promise<any> => {
  try {
    const { data: userData } = await supabase.auth.getUser();
    if (!userData.user) throw new Error('Usuário não autenticado');

    const { data, error } = await supabase.rpc('restore_deleted_item', {
      p_deleted_item_id: deletedItemId,
      p_user_id: userData.user.id
    });

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Erro ao restaurar item excluído:', error);
    throw error;
  }
};

// Função para excluir permanentemente um item
export const deletePermanently = async (deletedItemId: string): Promise<void> => {
  try {
    const { data: userData } = await supabase.auth.getUser();
    if (!userData.user) throw new Error('Usuário não autenticado');

    // Verificar se o usuário é admin
    const { data: userProfile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', userData.user.id)
      .single();

    // Apenas admins podem excluir permanentemente
    if (userProfile?.role !== 'admin') {
      throw new Error('Apenas administradores podem excluir itens permanentemente');
    }

    // Obter informações do item excluído
    const { data: deletedItem, error: getError } = await supabase
      .from('deleted_items')
      .select('item_id, item_type')
      .eq('id', deletedItemId)
      .single();

    if (getError) throw getError;

    // Excluir permanentemente o item original
    switch (deletedItem.item_type) {
      case 'precatorio':
        await supabase
          .from('precatorios')
          .delete()
          .eq('id', deletedItem.item_id);
        break;
      case 'tag':
        await supabase
          .from('tags')
          .delete()
          .eq('id', deletedItem.item_id);
        break;
      case 'coluna':
        await supabase
          .from('kanban_colunas_personalizadas')
          .delete()
          .eq('id', deletedItem.item_id);
        break;
      case 'cliente':
        await supabase
          .from('profiles')
          .delete()
          .eq('id', deletedItem.item_id);
        break;
      default:
        throw new Error(`Tipo de item não suportado: ${deletedItem.item_type}`);
    }

    // Marcar o item como não restaurável
    const { error } = await supabase
      .from('deleted_items')
      .update({ can_restore: false })
      .eq('id', deletedItemId);

    if (error) throw error;
  } catch (error) {
    console.error('Erro ao excluir permanentemente:', error);
    throw error;
  }
};
