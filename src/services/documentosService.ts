import { supabase } from '@/lib/supabase';

export interface Documento {
  id: string;
  nome: string;
  tipo: string;
  status: 'pendente' | 'aprovado' | 'rejeitado';
  data_upload: string;
  tamanho?: string;
  url?: string;
  usuario_id?: string;
  usuario_nome?: string;
  cliente_id?: string;
  precatorio_id?: string;
  categoria?: string;
  descricao?: string;
  tags?: string[];
  versao?: number;
  conteudo?: string;
  updated_at?: string;
  cliente_nome?: string;
  precatorio_numero?: string;
}

// Buscar todos os documentos
export async function buscarDocumentos(filtros?: {
  tipo?: string;
  status?: string;
  cliente_id?: string;
  precatorio_id?: string;
  termo?: string;
}): Promise<Documento[]> {
  try {
    console.log('Buscando documentos com filtros:', filtros);

    // Primeiro, buscar documentos de clientes
    let queryClientes = supabase
      .from('documentos')
      .select(`
        id,
        nome,
        tipo,
        status,
        data_upload,
        url,
        usuario_id,
        cliente_id,
        clientes (nome)
      `);

    // Aplicar filtros para documentos de clientes
    if (filtros?.tipo) {
      queryClientes = queryClientes.eq('tipo', filtros.tipo);
    }

    if (filtros?.status) {
      queryClientes = queryClientes.eq('status', filtros.status);
    }

    if (filtros?.cliente_id) {
      queryClientes = queryClientes.eq('cliente_id', filtros.cliente_id);
    }

    if (filtros?.termo) {
      queryClientes = queryClientes.ilike('nome', `%${filtros.termo}%`);
    }

    const { data: documentosClientes, error: errorClientes } = await queryClientes;

    if (errorClientes) {
      console.error('Erro ao buscar documentos de clientes:', errorClientes);
      // Não lançar erro, apenas logar e continuar
    }

    // Depois, buscar documentos de precatórios
    let queryPrecatorios = supabase
      .from('precatorios_documentos')
      .select(`
        id,
        nome,
        tipo,
        status,
        data_upload,
        precatorio_id,
        precatorios (numero_precatorio, beneficiario_id)
      `);

    // Aplicar filtros para documentos de precatórios
    if (filtros?.tipo) {
      queryPrecatorios = queryPrecatorios.eq('tipo', filtros.tipo);
    }

    if (filtros?.status) {
      queryPrecatorios = queryPrecatorios.eq('status', filtros.status);
    }

    if (filtros?.precatorio_id) {
      queryPrecatorios = queryPrecatorios.eq('precatorio_id', filtros.precatorio_id);
    }

    if (filtros?.termo) {
      queryPrecatorios = queryPrecatorios.ilike('nome', `%${filtros.termo}%`);
    }

    const { data: documentosPrecatorios, error: errorPrecatorios } = await queryPrecatorios;

    if (errorPrecatorios) {
      console.error('Erro ao buscar documentos de precatórios:', errorPrecatorios);
      // Não lançar erro, apenas logar e continuar
    }

    // Formatar documentos de clientes
    const docsClientes = (documentosClientes || []).map(doc => ({
      id: doc.id,
      nome: doc.nome,
      tipo: doc.tipo,
      status: doc.status,
      data_upload: doc.data_upload,
      url: doc.url,
      usuario_id: doc.usuario_id,
      cliente_id: doc.cliente_id,
      cliente_nome: doc.clientes?.nome,
      categoria: 'cliente',
      tamanho: calcularTamanhoFormatado(doc.url || ''),
    }));

    // Formatar documentos de precatórios
    const docsPrecatorios = (documentosPrecatorios || []).map(doc => ({
      id: doc.id,
      nome: doc.nome,
      tipo: doc.tipo,
      status: doc.status,
      data_upload: doc.data_upload,
      precatorio_id: doc.precatorio_id,
      precatorio_numero: doc.precatorios?.numero_precatorio,
      cliente_id: doc.precatorios?.beneficiario_id,
      categoria: 'precatorio',
      tamanho: calcularTamanhoFormatado(''),
    }));

    // Combinar os resultados
    const todosDocumentos = [...docsClientes, ...docsPrecatorios];
    console.log(`Total de documentos encontrados: ${todosDocumentos.length}`);
    return todosDocumentos;
  } catch (error) {
    console.error('Erro ao buscar documentos:', error);
    // Retornar array vazio em caso de erro para não quebrar a interface
    return [];
  }
}

// Buscar documento por ID
export async function buscarDocumentoPorId(id: string, categoria?: 'cliente' | 'precatorio'): Promise<Documento | null> {
  try {
    let documento = null;

    // Se a categoria não for especificada, tentar buscar em ambas as tabelas
    if (!categoria || categoria === 'cliente') {
      const { data, error } = await supabase
        .from('documentos')
        .select(`
          *,
          clientes (nome)
        `)
        .eq('id', id)
        .single();

      if (error && error.code !== 'PGRST116') { // Ignorar erro de "não encontrado"
        console.error('Erro ao buscar documento de cliente:', error);
        throw error;
      }

      if (data) {
        documento = {
          ...data,
          categoria: 'cliente',
          cliente_nome: data.clientes?.nome,
          tamanho: calcularTamanhoFormatado(data.conteudo?.length || data.url?.length || 0),
        };
      }
    }

    // Se não encontrou na tabela de clientes ou a categoria é precatório
    if (!documento && (!categoria || categoria === 'precatorio')) {
      const { data, error } = await supabase
        .from('precatorios_documentos')
        .select(`
          *,
          precatorios (numero_precatorio, beneficiario_id, beneficiarios:beneficiario_id (nome))
        `)
        .eq('id', id)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('Erro ao buscar documento de precatório:', error);
        throw error;
      }

      if (data) {
        documento = {
          ...data,
          categoria: 'precatorio',
          precatorio_numero: data.precatorios?.numero_precatorio,
          cliente_id: data.precatorios?.beneficiario_id,
          cliente_nome: data.precatorios?.beneficiarios?.nome,
          tamanho: calcularTamanhoFormatado(data.conteudo?.length || 0),
        };
      }
    }

    return documento;
  } catch (error) {
    console.error(`Erro ao buscar documento com ID ${id}:`, error);
    throw error;
  }
}

// Upload de documento para cliente
export async function uploadDocumentoCliente(
  file: File,
  clienteId: string,
  tipo: string,
  descricao?: string
): Promise<Documento> {
  try {
    const fileExt = file.name.split('.').pop();
    const fileName = `clientes/${clienteId}/${Date.now()}.${fileExt}`;

    // Upload do arquivo para o storage
    const { error: uploadError } = await supabase.storage
      .from('documents')
      .upload(fileName, file);

    if (uploadError) {
      console.error('Erro ao fazer upload do arquivo:', uploadError);
      throw uploadError;
    }

    // Criar registro do documento no banco
    const { data, error: dbError } = await supabase
      .from('documentos')
      .insert({
        cliente_id: clienteId,
        nome: file.name,
        tipo,
        status: 'pendente',
        url: fileName,
        data_upload: new Date().toISOString(),
        descricao: descricao || '',
      })
      .select()
      .single();

    if (dbError) {
      console.error('Erro ao registrar documento no banco:', dbError);
      throw dbError;
    }

    return {
      ...data,
      categoria: 'cliente',
      tamanho: calcularTamanhoFormatado(file.size),
    };
  } catch (error) {
    console.error('Erro ao fazer upload de documento para cliente:', error);
    throw error;
  }
}

// Upload de documento para precatório
export async function uploadDocumentoPrecatorio(
  file: File,
  precatorioId: string,
  tipo: string,
  descricao?: string
): Promise<Documento> {
  try {
    const fileExt = file.name.split('.').pop();
    const fileName = `precatorios/${precatorioId}/${Date.now()}.${fileExt}`;

    // Upload do arquivo para o storage
    const { error: uploadError } = await supabase.storage
      .from('documents')
      .upload(fileName, file);

    if (uploadError) {
      console.error('Erro ao fazer upload do arquivo:', uploadError);
      throw uploadError;
    }

    // Criar registro do documento no banco
    const { data, error: dbError } = await supabase
      .from('precatorios_documentos')
      .insert({
        precatorio_id: precatorioId,
        nome: file.name,
        tipo,
        status: 'pendente',
        data_upload: new Date().toISOString(),
        url: fileName,
        descricao: descricao || '',
      })
      .select()
      .single();

    if (dbError) {
      console.error('Erro ao registrar documento no banco:', dbError);
      throw dbError;
    }

    return {
      ...data,
      categoria: 'precatorio',
      tamanho: calcularTamanhoFormatado(file.size),
    };
  } catch (error) {
    console.error('Erro ao fazer upload de documento para precatório:', error);
    throw error;
  }
}

// Atualizar status do documento
export async function atualizarStatusDocumento(
  id: string,
  status: 'pendente' | 'aprovado' | 'rejeitado',
  categoria: 'cliente' | 'precatorio'
): Promise<void> {
  try {
    const tabela = categoria === 'cliente' ? 'documentos' : 'precatorios_documentos';

    const { error } = await supabase
      .from(tabela)
      .update({ status })
      .eq('id', id);

    if (error) {
      console.error(`Erro ao atualizar status do documento ${id}:`, error);
      throw error;
    }
  } catch (error) {
    console.error(`Erro ao atualizar status do documento ${id}:`, error);
    throw error;
  }
}

// Excluir documento
export async function excluirDocumento(id: string, categoria: 'cliente' | 'precatorio'): Promise<void> {
  try {
    const tabela = categoria === 'cliente' ? 'documentos' : 'precatorios_documentos';

    // Primeiro, buscar o documento para obter a URL do arquivo
    const { data, error: fetchError } = await supabase
      .from(tabela)
      .select('url')
      .eq('id', id)
      .single();

    if (fetchError) {
      console.error(`Erro ao buscar documento ${id} para exclusão:`, fetchError);
      throw fetchError;
    }

    // Se tiver URL, excluir o arquivo do storage
    if (data?.url) {
      const { error: storageError } = await supabase.storage
        .from('documents')
        .remove([data.url]);

      if (storageError) {
        console.error(`Erro ao excluir arquivo do storage:`, storageError);
        // Continuar mesmo com erro no storage, para garantir que o registro seja removido
      }
    }

    // Excluir o registro do banco
    const { error } = await supabase
      .from(tabela)
      .delete()
      .eq('id', id);

    if (error) {
      console.error(`Erro ao excluir documento ${id}:`, error);
      throw error;
    }
  } catch (error) {
    console.error(`Erro ao excluir documento ${id}:`, error);
    throw error;
  }
}

// Obter URL de download do documento
export async function obterUrlDownload(path: string): Promise<string> {
  try {
    const { data, error } = await supabase.storage
      .from('documents')
      .createSignedUrl(path, 60); // URL válida por 60 segundos

    if (error) {
      console.error('Erro ao gerar URL de download:', error);
      throw error;
    }

    return data.signedUrl;
  } catch (error) {
    console.error('Erro ao obter URL de download:', error);
    throw error;
  }
}

// Criar novo documento de texto
export async function criarNovoDocumento(
  nome: string,
  conteudo: string,
  tipo: string,
  clienteId?: string,
  precatorioId?: string,
  processoId?: string,
  usuarioId?: string
): Promise<Documento> {
  try {
    // Decidir onde salvar com base nos IDs fornecidos
    if (clienteId) {
      // Criar registro do documento no banco para cliente
      const { data, error } = await supabase
        .from('documentos')
        .insert({
          cliente_id: clienteId,
          nome: nome,
          tipo,
          status: 'pendente',
          data_upload: new Date().toISOString(),
          conteudo: conteudo,
          processo_id: processoId,
          usuario_id: usuarioId,
          url: null
        })
        .select()
        .single();

      if (error) {
        console.error('Erro ao registrar documento no banco:', error);
        throw error;
      }

      return {
        ...data,
        categoria: 'cliente',
        tamanho: calcularTamanhoFormatado(conteudo.length),
      };
    } else if (precatorioId) {
      // Criar registro do documento no banco para precatório
      const { data, error } = await supabase
        .from('precatorios_documentos')
        .insert({
          precatorio_id: precatorioId,
          nome: nome,
          tipo,
          status: 'pendente',
          data_upload: new Date().toISOString(),
          conteudo: conteudo,
          processo_id: processoId,
          usuario_id: usuarioId,
          url: null
        })
        .select()
        .single();

      if (error) {
        console.error('Erro ao registrar documento no banco:', error);
        throw error;
      }

      return {
        ...data,
        categoria: 'precatorio',
        tamanho: calcularTamanhoFormatado(conteudo.length),
      };
    } else {
      throw new Error('É necessário fornecer um ID de cliente ou precatório');
    }
  } catch (error) {
    console.error('Erro ao criar novo documento:', error);
    throw error;
  }
}

// Atualizar documento existente
export async function atualizarDocumento(
  id: string,
  dados: {
    nome?: string;
    conteudo?: string;
    tipo?: string;
    status?: 'pendente' | 'aprovado' | 'rejeitado';
    cliente_id?: string;
    precatorio_id?: string;
  },
  categoria: 'cliente' | 'precatorio'
): Promise<Documento> {
  try {
    const tabela = categoria === 'cliente' ? 'documentos' : 'precatorios_documentos';

    // Atualizar o documento no banco
    const { data, error } = await supabase
      .from(tabela)
      .update({
        ...dados,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error(`Erro ao atualizar documento ${id}:`, error);
      throw error;
    }

    return {
      ...data,
      categoria,
      tamanho: calcularTamanhoFormatado(dados.conteudo?.length || 0),
    };
  } catch (error) {
    console.error(`Erro ao atualizar documento ${id}:`, error);
    throw error;
  }
}

// Função auxiliar para calcular tamanho formatado
function calcularTamanhoFormatado(input: string | number): string {
  // Se for uma string (URL), retornar um valor padrão
  if (typeof input === 'string') {
    return '128 KB'; // Valor fictício para demonstração
  }

  // Se for um número (tamanho em bytes)
  const bytes = Number(input);
  if (isNaN(bytes)) return '0 Bytes';

  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
