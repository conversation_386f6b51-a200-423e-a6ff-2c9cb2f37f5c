import { supabase } from '@/lib/supabase';
import { KanbanColuna } from '@/components/Precatorios/types';
import { toast } from 'sonner';

// Variável para controlar o modo offline - sempre desativado
let modoOffline = false;

// Definir explicitamente o modo offline - agora sempre força modo online
export function setModoOffline(offline: boolean): void {
  // Forçar modo online independente do parâmetro
  modoOffline = false;
  console.log('Modo online ativado. Sempre usando Supabase.');
  toast.info('Modo online garantido. As alterações serão salvas no banco de dados.');
}

// Verificar se a tabela status_precatorios existe e criar se necessário
export async function verificarECriarTabelaKanbanColunas(): Promise<boolean> {
  try {
    // Forçar modo online para garantir conexão com Supabase
    modoOffline = false;

    // Tentar buscar dados da tabela para verificar se ela existe
    const { data, error } = await supabase
      .from('status_precatorios')
      .select('count')
      .limit(1);

    // Se não houver erro, a tabela existe
    if (!error) {
      return true;
    }

    // Se o erro indicar que a tabela não existe, mostrar erro
    if (error.message.includes('404') || error.message.includes('does not exist')) {
      toast.error('A tabela status_precatorios não existe. Entre em contato com o administrador.');
      console.error('A tabela status_precatorios não existe.');
      return false;
    }

    console.error('Erro ao verificar tabela status_precatorios:', error);
    return false;
  } catch (error) {
    console.error('Erro ao verificar tabela status_precatorios:', error);
    return false;
  }
}

// Buscar todos os status/colunas do Kanban
export async function buscarTodasColunas(): Promise<KanbanColuna[]> {
  try {
    console.log('Buscando todos os status do Kanban...');

    // Verificar se a tabela existe
    const tabelaExiste = await verificarECriarTabelaKanbanColunas();

    if (!tabelaExiste) {
      console.log('Tabela de status não existe. Usando status padrão...');
      return getColunasDefault('AMBOS');
    }

    // Buscar dados da tabela status_precatorios
    const { data, error } = await supabase
      .from('status_precatorios')
      .select('*')
      .eq('ativo', true)
      .order('ordem', { ascending: true });

    if (error) {
      console.error('Erro ao buscar status do Kanban:', error);
      // Se houver erro, usar colunas padrão
      console.log('Usando status padrão devido a erro...');
      return getColunasDefault('AMBOS');
    }

    if (!data || data.length === 0) {
      console.log('Nenhum status encontrado no banco. Usando status padrão...');

      // Tentar inserir os status padrão
      try {
        const statusPadrao = getColunasDefault('AMBOS').map(coluna => ({
          nome: coluna.name,
          codigo: coluna.status_id,
          cor: coluna.color,
          tipo: coluna.tipo,
          ordem: coluna.ordem,
          ativo: coluna.ativo,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }));

        await supabase.from('status_precatorios').insert(statusPadrao);
        console.log('Status padrão inseridos com sucesso!');
      } catch (e) {
        console.error('Erro ao inserir status padrão:', e);
      }

      return getColunasDefault('AMBOS');
    }

    // Mapear os dados para o formato esperado pelo componente
    const colunas: KanbanColuna[] = data.map(status => ({
      id: status.id,
      name: status.nome,
      color: status.cor,
      tipo: status.tipo || 'AMBOS',
      ordem: status.ordem,
      status_id: status.codigo,
      status_uuid: status.id,
      status_ref_id: status.id,
      ativo: status.ativo,
      alerts: []
    }));

    console.log(`${colunas.length} status encontrados.`);
    return colunas;
  } catch (error) {
    console.error('Erro ao buscar status do Kanban:', error);
    toast.error('Erro ao carregar status do Kanban');
    throw error;
  }
}

// Buscar colunas do Kanban por tipo (PRECATORIO, RPV ou AMBOS)
export async function buscarColunasPorTipo(tipo: 'PRECATORIO' | 'RPV' | 'AMBOS'): Promise<KanbanColuna[]> {
  try {
    console.log(`Buscando colunas do Kanban para o tipo: ${tipo}...`);

    // Verificar se a tabela existe
    const tabelaExiste = await verificarECriarTabelaKanbanColunas();

    // Se a tabela não existe, retornar colunas padrão
    if (!tabelaExiste) {
      console.log('Usando colunas padrão do Kanban...');
      return getColunasDefault(tipo);
    }

    // Buscar colunas da tabela status_precatorios
    const { data, error } = await supabase
      .from('status_precatorios')
      .select('*')
      .or(`tipo.eq.${tipo},tipo.eq.AMBOS,tipo.is.null`)
      .eq('ativo', true)
      .order('ordem', { ascending: true });

    if (error) {
      console.error(`Erro ao buscar status para o tipo ${tipo}:`, error);
      console.log('Usando colunas padrão do Kanban devido a erro...');
      return getColunasDefault(tipo);
    }

    // Se não houver dados, retornar colunas padrão
    if (!data || data.length === 0) {
      console.log(`Nenhum status encontrado para o tipo ${tipo}. Usando colunas padrão...`);
      return getColunasDefault(tipo);
    }

    // Mapear os dados para o formato esperado pelo componente com logs explícitos
    console.log('Dados brutos do banco de dados para mapeamento:', data);

    const colunas: KanbanColuna[] = data.map(status => {
      // Verificar se todos os campos necessários existem
      if (!status.nome) {
        console.warn('Status com dados incompletos:', status);
      }

      // Criar objeto de coluna com mapeamento explícito
      const colunaFormatada: KanbanColuna = {
        id: status.id, // ID do status como ID da coluna
        nome: status.nome,          // Nome do status
        name: status.nome,          // Nome do status (para compatibilidade)
        cor: status.cor,            // Cor do status
        color: status.cor,          // Cor do status (para compatibilidade)
        tipo: status.tipo as 'PRECATORIO' | 'RPV' | 'AMBOS' || 'AMBOS', // Tipo do status
        ordem: status.ordem,        // Ordem do status
        status_id: status.codigo,   // Código do status
        status_uuid: status.id,     // UUID do status
        status_ref_id: status.id,   // UUID do status (para compatibilidade)
        ativo: status.ativo,        // Status ativo
        status: {                   // Informações do status
          id: status.id,
          nome: status.nome,
          codigo: status.codigo,
          cor: status.cor
        },
        alerts: []                  // Alertas vazios por padrão
      };

      // Log detalhado para cada coluna
      console.log(`Coluna mapeada: ID=${colunaFormatada.id}, nome=${colunaFormatada.nome}, status_id=${colunaFormatada.status_id}, status_uuid=${colunaFormatada.status_uuid}`);

      return colunaFormatada;
    });

    // Log detalhado de todas as colunas
    console.log(`${colunas.length} colunas do Kanban para o tipo ${tipo} recuperadas:`);
    colunas.forEach((col, index) => {
      console.log(`Coluna ${index+1}: ID=${col.id}, nome=${col.nome}, status_id=${col.status_id}`);
    });
    return colunas;
  } catch (error) {
    console.error(`Erro ao buscar colunas do Kanban para o tipo ${tipo}:`, error);
    toast.error('Erro ao carregar colunas do Kanban');
    throw error;
  }
}

// Criar novo status/coluna do Kanban
export async function criarColuna(coluna: Omit<KanbanColuna, 'id' | 'alerts'>): Promise<KanbanColuna> {
  try {
    // Normalizar o status_id para evitar problemas de compatibilidade
    const statusIdNormalizado = coluna.status_id.trim().toLowerCase();

    // Criar uma cópia da coluna com o status_id normalizado
    const colunaNormalizada = {
      ...coluna,
      status_id: statusIdNormalizado
    };

    console.log('Criando novo status para o Kanban:', {
      original: coluna,
      normalizada: colunaNormalizada
    });

    // Verificar se a tabela existe
    const tabelaExiste = await verificarECriarTabelaKanbanColunas();

    if (!tabelaExiste) {
      toast.error('A tabela de status não existe. Entre em contato com o administrador.');
      return {
        id: coluna.status_id,
        name: coluna.name,
        color: coluna.color,
        tipo: coluna.tipo,
        ordem: coluna.ordem,
        status_id: coluna.status_id,
        ativo: coluna.ativo !== undefined ? coluna.ativo : true,
        alerts: [{
          tipo: 'error',
          mensagem: 'Erro ao salvar status no banco de dados'
        }]
      };
    }

    try {
      // Verificar se já existe um status com o mesmo código
      const { data: existingData, error: checkError } = await supabase
        .from('status_precatorios')
        .select('*')
        .eq('codigo', statusIdNormalizado);

      console.log('Verificando existência do status:', {
        codigoBuscado: statusIdNormalizado,
        resultado: existingData,
        erro: checkError
      });

      // Se houver erro na consulta
      if (checkError) {
        console.error('Erro ao verificar status existente:', checkError);
        toast.error('Erro ao verificar status existente.');
        throw checkError;
      }

      // Se o status já existe, atualizamos em vez de criar
      if (existingData && existingData.length > 0) {
        console.log('Status já existe, atualizando dados...', {
          statusExistente: existingData[0],
          novosValores: colunaNormalizada
        });

        // Atualizar o status existente
        const { data: updatedData, error: updateError } = await supabase
          .from('status_precatorios')
          .update({
            nome: colunaNormalizada.name,
            cor: colunaNormalizada.color,
            tipo: colunaNormalizada.tipo,
            ordem: colunaNormalizada.ordem,
            ativo: colunaNormalizada.ativo !== undefined ? colunaNormalizada.ativo : true,
            updated_at: new Date().toISOString()
          })
          .eq('id', existingData[0].id)
          .select();

        if (updateError) {
          console.error('Erro ao atualizar status:', updateError);
          toast.error('Erro ao atualizar status.');
          throw updateError;
        }

        // Mapear para o formato esperado
        const statusAtualizado = updatedData[0];
        return {
          id: statusAtualizado.id,
          name: statusAtualizado.nome,
          color: statusAtualizado.cor,
          tipo: statusAtualizado.tipo,
          ordem: statusAtualizado.ordem,
          status_id: statusAtualizado.codigo,
          status_uuid: statusAtualizado.id,
          status_ref_id: statusAtualizado.id,
          ativo: statusAtualizado.ativo,
          alerts: []
        };
      }

      // Inserir o novo status
      const { data, error } = await supabase
        .from('status_precatorios')
        .insert({
          nome: colunaNormalizada.name,
          codigo: statusIdNormalizado,
          cor: colunaNormalizada.color,
          tipo: colunaNormalizada.tipo,
          ordem: colunaNormalizada.ordem,
          ativo: colunaNormalizada.ativo !== undefined ? colunaNormalizada.ativo : true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select();

      console.log('Resultado da criação de status:', { data, error });

      if (error) {
        console.error('Erro ao criar status:', error);
        toast.error('Erro ao criar status.');
        throw error;
      }

      if (!data || data.length === 0) {
        console.error('Nenhum dado retornado após a criação do status');
        toast.error('Erro ao criar status: nenhum dado retornado');
        throw new Error('Nenhum dado retornado após a criação do status');
      }

      // Mapear para o formato esperado pelo componente
      const novoStatus = data[0];
      const novaColuna: KanbanColuna = {
        id: novoStatus.id,
        name: novoStatus.nome,
        color: novoStatus.cor,
        tipo: novoStatus.tipo,
        ordem: novoStatus.ordem,
        status_id: novoStatus.codigo,
        status_uuid: novoStatus.id,
        status_ref_id: novoStatus.id,
        ativo: novoStatus.ativo,
        alerts: []
      };

      console.log('Novo status criado:', novaColuna);
      toast.success('Status criado com sucesso!');
      return novaColuna;
    } catch (dbError) {
      console.error('Erro durante operação no banco de dados:', dbError);
      toast.error('Erro ao criar status.');
      throw dbError;
    }
  } catch (error) {
    console.error('Erro ao criar status:', error);
    toast.error('Erro ao criar status.');

    // Criar objeto para retorno com alerta
    const novaColuna: KanbanColuna = {
      id: coluna.status_id,
      name: coluna.name,
      color: coluna.color,
      tipo: coluna.tipo,
      ordem: coluna.ordem,
      status_id: coluna.status_id,
      ativo: coluna.ativo !== undefined ? coluna.ativo : true,
      alerts: [{
        tipo: 'error',
        mensagem: 'Erro ao salvar status no banco de dados'
      }]
    };
    return novaColuna;
  }
}

// Atualizar status/coluna do Kanban
export async function atualizarColuna(coluna: Omit<KanbanColuna, 'alerts'>): Promise<KanbanColuna> {
  try {
    // Normalizar o status_id para evitar problemas de compatibilidade
    const statusIdNormalizado = coluna.status_id.trim().toLowerCase();

    // Criar uma cópia da coluna com o status_id normalizado
    const colunaNormalizada = {
      ...coluna,
      status_id: statusIdNormalizado
    };

    // Verificar se a tabela existe
    const tabelaExiste = await verificarECriarTabelaKanbanColunas();

    if (!tabelaExiste) {
      toast.error('A tabela de status não existe. Entre em contato com o administrador.');
      return {
        ...coluna,
        alerts: [{
          tipo: 'error',
          mensagem: 'Erro ao atualizar status no banco de dados'
        }]
      };
    }

    try {
      // Verificar se o status existe pelo ID
      const { data: checkData, error: checkError } = await supabase
        .from('status_precatorios')
        .select('*')
        .eq('id', coluna.id)
        .single();

      if (checkError) {
        console.error('Erro ao verificar status existente:', checkError);

        // Tentar buscar pelo código
        const { data: checkByCode, error: codeError } = await supabase
          .from('status_precatorios')
          .select('*')
          .eq('codigo', statusIdNormalizado)
          .single();

        if (codeError || !checkByCode) {
          console.error('Status não encontrado pelo código:', codeError);
          // Se não encontrar, criar um novo
          return criarColuna(colunaNormalizada);
        }

        // Se encontrou pelo código, usar esse ID
        colunaNormalizada.id = checkByCode.id;
      } else if (checkData) {
        // Status encontrado, usar os dados
        colunaNormalizada.id = checkData.id;
      }

      // Atualizar o status
      const { data, error } = await supabase
        .from('status_precatorios')
        .update({
          nome: colunaNormalizada.name,
          cor: colunaNormalizada.color,
          tipo: colunaNormalizada.tipo,
          ordem: colunaNormalizada.ordem,
          ativo: colunaNormalizada.ativo !== undefined ? colunaNormalizada.ativo : true,
          updated_at: new Date().toISOString()
        })
        .eq('id', colunaNormalizada.id)
        .select();

      if (error) {
        console.error('Erro ao atualizar status:', error);
        toast.error('Erro ao atualizar status.');
        throw error;
      }

      if (!data || data.length === 0) {
        console.error('Nenhum dado retornado após a atualização do status');
        toast.error('Erro ao atualizar status: nenhum dado retornado');
        throw new Error('Nenhum dado retornado após a atualização do status');
      }

      console.log('Dados retornados após atualização:', data);

      // Mapear para o formato esperado pelo componente
      const statusAtualizado = data[0];
      const colunaAtualizada: KanbanColuna = {
        id: statusAtualizado.id,
        name: statusAtualizado.nome,
        color: statusAtualizado.cor,
        tipo: statusAtualizado.tipo,
        ordem: statusAtualizado.ordem,
        status_id: statusAtualizado.codigo,
        status_uuid: statusAtualizado.id,
        status_ref_id: statusAtualizado.id,
        ativo: statusAtualizado.ativo,
        alerts: []
      };

      console.log('Status atualizado com sucesso:', colunaAtualizada);
      toast.success('Status atualizado com sucesso!');
      return colunaAtualizada;
    } catch (dbError) {
      console.error('Erro durante operação no banco de dados:', dbError);
      toast.error('Erro ao atualizar status.');

      // Retornar com alerta
      return {
        ...coluna,
        alerts: [{
          tipo: 'error',
          mensagem: 'Erro ao atualizar status no banco de dados'
        }]
      };
    }
  } catch (error) {
    console.error('Erro ao atualizar status:', error);
    toast.error('Erro ao atualizar status.');

    // Retornar com alerta
    return {
      ...coluna,
      alerts: [{
        tipo: 'error',
        mensagem: 'Erro ao atualizar status no banco de dados'
      }]
    };
  }
}

// Excluir status/coluna do Kanban
export async function excluirColuna(status_id: string): Promise<void> {
  try {
    // Normalizar o status_id para evitar problemas de compatibilidade
    const statusIdNormalizado = status_id.trim().toLowerCase();

    console.log(`Excluindo status com código: ${statusIdNormalizado}...`);

    // Verificar se a tabela existe
    const tabelaExiste = await verificarECriarTabelaKanbanColunas();

    if (!tabelaExiste) {
      toast.error('A tabela de status não existe. Entre em contato com o administrador.');
      return;
    }

    try {
      // Verificar se o status existe pelo código
      const { data: checkData, error: checkError } = await supabase
        .from('status_precatorios')
        .select('id, codigo')
        .eq('codigo', statusIdNormalizado)
        .maybeSingle();

      console.log('Verificando se o status existe antes de excluir:', {
        codigoBuscado: statusIdNormalizado,
        resultado: checkData,
        erro: checkError
      });

      // Se não encontrou o status, pode ser considerado como já excluído
      if (!checkData || checkError) {
        console.log(`Status com código ${statusIdNormalizado} não encontrado ou erro na busca.`);
        toast.info('O status já não existe no banco de dados.');
        return;
      }

      // Verificar se existem precatórios usando este status
      const { data: precatoriosData, error: precatoriosError } = await supabase
        .from('precatorios')
        .select('id')
        .eq('status_id', checkData.id)
        .limit(1);

      if (precatoriosData && precatoriosData.length > 0) {
        console.log(`Existem precatórios usando o status ${statusIdNormalizado}. Não é possível excluir.`);
        toast.error('Não é possível excluir este status porque existem precatórios associados a ele.');
        return;
      }

      // Excluir o status
      const { error } = await supabase
        .from('status_precatorios')
        .delete()
        .eq('id', checkData.id);

      // Se houver erro na exclusão
      if (error) {
        console.error(`Erro ao excluir status com código ${statusIdNormalizado}:`, error);
        toast.error(`Erro ao excluir status: ${error.message}`);
        return;
      }

      console.log(`Status com código ${statusIdNormalizado} excluído com sucesso`);
      toast.success('Status excluído com sucesso!');
    } catch (dbError) {
      console.error(`Erro durante operação de exclusão no banco de dados:`, dbError);
      toast.error('Erro ao excluir status.');
    }
  } catch (error) {
    console.error(`Erro ao excluir status com código ${status_id}:`, error);
    toast.error(`Erro ao excluir status: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
  }
}

// Reordenar status/colunas do Kanban
export async function reordenarColunas(colunas: Omit<KanbanColuna, 'alerts'>[]): Promise<KanbanColuna[]> {
  try {
    console.log('Reordenando status do Kanban:', colunas);

    // Verificar se a tabela existe
    const tabelaExiste = await verificarECriarTabelaKanbanColunas();

    if (!tabelaExiste) {
      toast.error('A tabela de status não existe. Entre em contato com o administrador.');
      return colunas.map(coluna => ({
        ...coluna,
        alerts: [{
          tipo: 'error',
          mensagem: 'Erro ao reordenar status'
        }]
      }));
    }

    try {
      // Atualizar a ordem de cada status individualmente
      for (const coluna of colunas) {
        // Verificar se o status existe pelo ID
        const { data: checkData, error: checkError } = await supabase
          .from('status_precatorios')
          .select('id')
          .eq('id', coluna.id)
          .single();

        if (checkError || !checkData) {
          console.error(`Status com ID ${coluna.id} não encontrado:`, checkError);
          continue; // Pular para o próximo status
        }

        // Atualizar a ordem do status
        const { error } = await supabase
          .from('status_precatorios')
          .update({ ordem: coluna.ordem })
          .eq('id', coluna.id);

        if (error) {
          console.error(`Erro ao atualizar ordem do status ${coluna.id}:`, error);
        }
      }

      // Buscar os status atualizados
      const { data, error } = await supabase
        .from('status_precatorios')
        .select('*')
        .order('ordem', { ascending: true });

      if (error) {
        console.error('Erro ao buscar status atualizados:', error);
        toast.error('Erro ao buscar status atualizados.');
        throw error;
      }

      // Mapear para o formato esperado
      const colunasAtualizadas: KanbanColuna[] = data.map(status => ({
        id: status.id,
        name: status.nome,
        color: status.cor,
        tipo: status.tipo || 'AMBOS',
        ordem: status.ordem,
        status_id: status.codigo,
        status_uuid: status.id,
        status_ref_id: status.id,
        ativo: status.ativo,
        alerts: []
      }));

      toast.success('Status reordenados com sucesso!');
      return colunasAtualizadas;
    } catch (dbError) {
      console.error('Erro durante operação de reordenamento no banco de dados:', dbError);
      toast.error('Erro ao reordenar status.');

      // Retornar as colunas originais com alerta
      return colunas.map(coluna => ({
        ...coluna,
        alerts: [{
          tipo: 'error',
          mensagem: 'Erro ao reordenar status'
        }]
      }));
    }
  } catch (error) {
    console.error('Erro ao reordenar status:', error);
    toast.error(`Erro ao reordenar status: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);

    // Retornar as colunas originais com alerta
    return colunas.map(coluna => ({
      ...coluna,
      alerts: [{
        tipo: 'error',
        mensagem: 'Erro ao reordenar status'
      }]
    }));
  }
}

// Esta função não é mais necessária, pois a reordenação é feita diretamente na função reordenarColunas

// Função para obter colunas padrão
function getColunasDefault(tipo: 'PRECATORIO' | 'RPV' | 'AMBOS'): KanbanColuna[] {
  const colunasPadrao: KanbanColuna[] = [
    {
      id: 'analise',
      name: 'Análise',
      color: '#3b82f6',
      tipo: 'AMBOS',
      ordem: 1,
      status_id: 'analise',
      ativo: true,
      alerts: []
    },
    {
      id: 'proposta_tmj',
      name: 'Proposta TMJ',
      color: '#8b5cf6',
      tipo: 'AMBOS',
      ordem: 2,
      status_id: 'proposta_tmj',
      ativo: true,
      alerts: []
    },
    {
      id: 'proposta_btg',
      name: 'Proposta BTG',
      color: '#ec4899',
      tipo: 'AMBOS',
      ordem: 3,
      status_id: 'proposta_btg',
      ativo: true,
      alerts: []
    },
    {
      id: 'negociacao',
      name: 'Negociação',
      color: '#f59e0b',
      tipo: 'AMBOS',
      ordem: 4,
      status_id: 'negociacao',
      ativo: true,
      alerts: []
    },
    {
      id: 'documentacao',
      name: 'Documentação',
      color: '#10b981',
      tipo: 'AMBOS',
      ordem: 5,
      status_id: 'documentacao',
      ativo: true,
      alerts: []
    },
    {
      id: 'pagamento',
      name: 'Pagamento',
      color: '#6366f1',
      tipo: 'AMBOS',
      ordem: 6,
      status_id: 'pagamento',
      ativo: true,
      alerts: []
    },
    {
      id: 'concluido',
      name: 'Concluído',
      color: '#22c55e',
      tipo: 'AMBOS',
      ordem: 7,
      status_id: 'concluido',
      ativo: true,
      alerts: []
    },
    {
      id: 'cancelado',
      name: 'Cancelado',
      color: '#ef4444',
      tipo: 'AMBOS',
      ordem: 8,
      status_id: 'cancelado',
      ativo: true,
      alerts: []
    }
  ];

  return colunasPadrao;
}

// Função auxiliar para mapear um status do formato do banco para o formato da aplicação
function mapearColunaDB(statusDB: any): KanbanColuna {
  if (!statusDB) return null;

  return {
    id: statusDB.id,
    name: statusDB.nome,
    color: statusDB.cor,
    tipo: statusDB.tipo || 'AMBOS',
    ordem: statusDB.ordem,
    status_id: statusDB.codigo,
    status_uuid: statusDB.id,
    status_ref_id: statusDB.id,
    ativo: statusDB.ativo,
    alerts: []
  };
}

// Atualizar contagem nas colunas do Kanban
export async function atualizarContagemColunas(colunas: KanbanColuna[], tipo: 'PRECATORIO' | 'RPV' | 'AMBOS'): Promise<KanbanColuna[]> {
  try {
    // Em vez de usar group, vamos buscar todos os precatórios do tipo e contar manualmente
    const tipoFiltro = tipo === 'AMBOS' ? undefined : tipo;

    // Consulta diferente para cada caso
    let precatoriosData: any[] = [];
    let precatoriosError = null;

    if (tipoFiltro) {
      // Buscar precatórios apenas do tipo especificado
      const result = await supabase
        .from('precatorios')
        .select('id, status_id, tipo');

      if (result.error) {
        precatoriosError = result.error;
      } else {
        // Filtrar pelo tipo após a consulta
        precatoriosData = (result.data || []).filter(p => p.tipo === tipoFiltro);
      }
    } else {
      // Buscar todos os precatórios para AMBOS
      const result = await supabase
        .from('precatorios')
        .select('id, status_id, tipo');

      precatoriosData = result.data || [];
      precatoriosError = result.error;
    }

    if (precatoriosError) {
      console.error(`Erro ao buscar precatórios para o tipo ${tipo}:`, precatoriosError);
      throw precatoriosError;
    }

    // Contar manualmente os precatórios por status_id
    const contagemPorStatusId: Record<string, number> = {};

    precatoriosData.forEach(precatorio => {
      // Usar o status_id (UUID) para contagem
      if (precatorio.status_id) {
        contagemPorStatusId[precatorio.status_id] = (contagemPorStatusId[precatorio.status_id] || 0) + 1;
      }
    });

    // Atualizar a contagem nas colunas
    const colunasAtualizadas = colunas.map(coluna => {
      // Usar o ID da coluna (que é o status_id) para a contagem
      const count = contagemPorStatusId[coluna.id] || 0;

      // Remover contagem anterior do nome, se existir
      const originalName = coluna.name.includes(' (') ? coluna.name.split(' (')[0] : coluna.name;

      return {
        ...coluna,
        name: `${originalName} (${count})`,
        count, // Adicionar contagem explicitamente
        alerts: count > 0 ? [
          { tipo: 'info', mensagem: `${count} ${tipo === 'PRECATORIO' ? 'precatório(s)' : 'RPV(s)'}` }
        ] : []
      };
    });

    return colunasAtualizadas;
  } catch (error) {
    console.error(`Erro ao atualizar contagem nas colunas do Kanban:`, error);
    // Em caso de erro, retornar as colunas originais mas com alerts de erro
    return colunas.map(coluna => ({
      ...coluna,
      alerts: [{ tipo: 'erro', mensagem: 'Erro ao contar precatórios' }]
    }));
  }
}
