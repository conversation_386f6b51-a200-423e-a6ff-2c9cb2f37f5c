import { supabase } from '@/lib/supabase';
import { getCache, setCache, clearCache } from './cacheService';
import { buscarTodosStatus } from './statusPrecatoriosService';

// Tipos
export interface CustomView {
  id: string;
  nome: string;
  descricao?: string;
  user_id: string;
  is_public: boolean;
  is_default: boolean;
  is_favorite: boolean;
  is_admin_created: boolean;
  is_system: boolean;
  status_ids: string[];
  created_at?: string;
  updated_at?: string;
}

export interface UserViewPreference {
  id: string;
  user_id: string;
  view_id: string;
  status_ids: string[];
  is_favorite: boolean;
  is_default: boolean;
  created_at?: string;
  updated_at?: string;
}

// Cache para visualizações
const VIEWS_CACHE_KEY = 'custom_views';
const USER_VIEWS_CACHE_KEY = 'user_view_preferences';
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutos

/**
 * <PERSON><PERSON> todas as visualizações disponíveis para o usuário atual
 */
export async function buscarVisualizacoesDisponiveis(): Promise<CustomView[]> {
  try {
    // Verificar cache
    const cacheKey = `${VIEWS_CACHE_KEY}_disponiveis`;
    const cachedData = getCache<CustomView[]>(cacheKey);
    if (cachedData) {
      return cachedData;
    }

    // Obter o usuário atual
    const { data: userData } = await supabase.auth.getUser();
    const userId = userData?.user?.id;

    if (!userId) {
      console.error('Usuário não autenticado');
      return [];
    }

    // Buscar visualizações do usuário e públicas
    const { data, error } = await supabase
      .from('custom_views')
      .select('*')
      .or(`user_id.eq.${userId},is_public.eq.true`)
      .order('is_default', { ascending: false })
      .order('is_favorite', { ascending: false })
      .order('nome', { ascending: true });

    if (error) {
      console.error('Erro ao buscar visualizações:', error);
      return [];
    }

    // Salvar no cache
    setCache(cacheKey, data, CACHE_DURATION);

    return data;
  } catch (error) {
    console.error('Erro ao buscar visualizações:', error);
    return [];
  }
}

/**
 * Busca as preferências de visualização do usuário atual
 */
export async function buscarPreferenciasVisualizacao(): Promise<UserViewPreference[]> {
  try {
    // Verificar cache
    const cacheKey = `${USER_VIEWS_CACHE_KEY}_usuario`;
    const cachedData = getCache<UserViewPreference[]>(cacheKey);
    if (cachedData) {
      return cachedData;
    }

    // Obter o usuário atual
    const { data: userData } = await supabase.auth.getUser();
    const userId = userData?.user?.id;

    if (!userId) {
      console.error('Usuário não autenticado');
      return [];
    }

    // Buscar preferências do usuário
    const { data, error } = await supabase
      .from('user_view_preferences')
      .select('*')
      .eq('user_id', userId)
      .order('is_default', { ascending: false })
      .order('is_favorite', { ascending: false });

    if (error) {
      console.error('Erro ao buscar preferências de visualização:', error);
      return [];
    }

    // Salvar no cache
    setCache(cacheKey, data, CACHE_DURATION);

    return data;
  } catch (error) {
    console.error('Erro ao buscar preferências de visualização:', error);
    return [];
  }
}

/**
 * Busca uma visualização pelo ID
 */
export async function buscarVisualizacaoPorId(id: string): Promise<CustomView | null> {
  try {
    // Verificar cache
    const cachedData = getCache<CustomView[]>(`${VIEWS_CACHE_KEY}_disponiveis`);
    if (cachedData) {
      const viewCache = cachedData.find(v => v.id === id);
      if (viewCache) return viewCache;
    }

    // Buscar do banco de dados
    const { data, error } = await supabase
      .from('custom_views')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error(`Erro ao buscar visualização com ID ${id}:`, error);
      return null;
    }

    return data;
  } catch (error) {
    console.error(`Erro ao buscar visualização com ID ${id}:`, error);
    return null;
  }
}

/**
 * Cria uma nova visualização personalizada
 */
export async function criarVisualizacao(view: Omit<CustomView, 'id' | 'created_at' | 'updated_at'>): Promise<CustomView | null> {
  try {
    // Obter o usuário atual
    const { data: userData } = await supabase.auth.getUser();
    const userId = userData?.user?.id;

    if (!userId) {
      console.error('Usuário não autenticado');
      throw new Error('Usuário não autenticado');
    }

    // Inserir no banco de dados
    const { data, error } = await supabase
      .from('custom_views')
      .insert({
        ...view,
        user_id: userId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('Erro ao criar visualização:', error);
      throw error;
    }

    // Criar preferência para o usuário
    await supabase
      .from('user_view_preferences')
      .insert({
        user_id: userId,
        view_id: data.id,
        status_ids: view.status_ids,
        is_favorite: view.is_favorite,
        is_default: view.is_default,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

    // Limpar cache
    clearCache(`${VIEWS_CACHE_KEY}_`);
    clearCache(`${USER_VIEWS_CACHE_KEY}_`);

    return data;
  } catch (error) {
    console.error('Erro ao criar visualização:', error);
    throw error;
  }
}

/**
 * Atualiza uma visualização existente
 */
export async function atualizarVisualizacao(id: string, view: Partial<CustomView>): Promise<CustomView | null> {
  try {
    // Verificar se a visualização existe
    const existente = await buscarVisualizacaoPorId(id);
    if (!existente) {
      console.error(`Visualização com ID ${id} não encontrada`);
      throw new Error(`Visualização com ID ${id} não encontrada`);
    }

    // Obter o usuário atual
    const { data: userData } = await supabase.auth.getUser();
    const userId = userData?.user?.id;

    if (!userId) {
      console.error('Usuário não autenticado');
      throw new Error('Usuário não autenticado');
    }

    // Verificar permissão (apenas o criador ou admin pode editar)
    if (existente.user_id !== userId && !existente.is_admin_created) {
      console.error('Sem permissão para editar esta visualização');
      throw new Error('Sem permissão para editar esta visualização');
    }

    // Atualizar no banco de dados
    const { data, error } = await supabase
      .from('custom_views')
      .update({
        ...view,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error(`Erro ao atualizar visualização com ID ${id}:`, error);
      throw error;
    }

    // Atualizar preferência do usuário
    if (view.status_ids) {
      await supabase
        .from('user_view_preferences')
        .update({
          status_ids: view.status_ids,
          is_favorite: view.is_favorite,
          is_default: view.is_default,
          updated_at: new Date().toISOString()
        })
        .eq('view_id', id)
        .eq('user_id', userId);
    }

    // Limpar cache
    clearCache(`${VIEWS_CACHE_KEY}_`);
    clearCache(`${USER_VIEWS_CACHE_KEY}_`);

    return data;
  } catch (error) {
    console.error(`Erro ao atualizar visualização com ID ${id}:`, error);
    throw error;
  }
}

/**
 * Exclui uma visualização
 */
export async function excluirVisualizacao(id: string): Promise<void> {
  try {
    // Verificar se a visualização existe
    const existente = await buscarVisualizacaoPorId(id);
    if (!existente) {
      console.error(`Visualização com ID ${id} não encontrada`);
      throw new Error(`Visualização com ID ${id} não encontrada`);
    }

    // Obter o usuário atual
    const { data: userData } = await supabase.auth.getUser();
    const userId = userData?.user?.id;

    if (!userId) {
      console.error('Usuário não autenticado');
      throw new Error('Usuário não autenticado');
    }

    // Verificar permissão (apenas o criador ou admin pode excluir)
    if (existente.user_id !== userId && !existente.is_admin_created) {
      console.error('Sem permissão para excluir esta visualização');
      throw new Error('Sem permissão para excluir esta visualização');
    }

    // Excluir preferências primeiro
    await supabase
      .from('user_view_preferences')
      .delete()
      .eq('view_id', id);

    // Excluir visualização
    const { error } = await supabase
      .from('custom_views')
      .delete()
      .eq('id', id);

    if (error) {
      console.error(`Erro ao excluir visualização com ID ${id}:`, error);
      throw error;
    }

    // Limpar cache
    clearCache(`${VIEWS_CACHE_KEY}_`);
    clearCache(`${USER_VIEWS_CACHE_KEY}_`);
  } catch (error) {
    console.error(`Erro ao excluir visualização com ID ${id}:`, error);
    throw error;
  }
}

/**
 * Cria uma visualização padrão para o usuário se não existir
 */
export async function criarVisualizacaoPadrao(): Promise<CustomView | null> {
  try {
    // Obter o usuário atual
    const { data: userData } = await supabase.auth.getUser();
    const userId = userData?.user?.id;

    if (!userId) {
      console.error('Usuário não autenticado');
      return null;
    }

    // Verificar se já existe uma visualização padrão
    const { data: existente } = await supabase
      .from('custom_views')
      .select('*')
      .eq('user_id', userId)
      .eq('is_default', true)
      .maybeSingle();

    if (existente) {
      return existente;
    }

    // Buscar todos os status ativos
    const statusList = await buscarTodosStatus();
    const statusIds = statusList.map(s => s.id);

    // Criar visualização padrão
    const novaVisualizacao: Omit<CustomView, 'id' | 'created_at' | 'updated_at'> = {
      nome: 'Visualização Padrão',
      descricao: 'Visualização padrão com todos os status',
      user_id: userId,
      is_public: false,
      is_default: true,
      is_favorite: true,
      is_admin_created: false,
      is_system: false,
      status_ids: statusIds
    };

    return await criarVisualizacao(novaVisualizacao);
  } catch (error) {
    console.error('Erro ao criar visualização padrão:', error);
    return null;
  }
}
