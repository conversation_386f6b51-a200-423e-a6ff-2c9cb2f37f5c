import { supabase } from '@/lib/supabase';
import { Precatorio } from '@/components/Precatorios/types';
import { getCachedData, setCache, clearCacheItem, updateCacheItem } from './cacheService';

// Buscar todos os precatórios
export async function buscarTodosPrecatorios(tipo: 'PRECATORIO' | 'RPV' = 'PRECATORIO'): Promise<Precatorio[]> {
  try {
    // Limpar cache para garantir dados atualizados
    clearCacheItem(`precatorios-${tipo}`);
    clearCacheItem(`precatorios_${tipo}`); // Limpar também o cache do outro serviço

    // Limpar todos os caches relacionados a precatórios
    console.log('Limpando todos os caches relacionados a precatórios...');
    clearCacheItem('precatorios-PRECATORIO');
    clearCacheItem('precatorios-RPV');
    clearCacheItem('precatorios_PRECATORIO');
    clearCacheItem('precatorios_RPV');

    // Chave de cache baseada no tipo
    const cacheKey = `precatorios-${tipo}`;

    // Tentar obter dados do cache ou buscar do Supabase
    return await getCachedData<Precatorio[]>(
      cacheKey,
      async () => {
        console.log(`Iniciando busca de ${tipo === 'PRECATORIO' ? 'precatórios' : 'RPVs'} no Supabase...`);

        // Buscar os dados principais dos precatórios com join para clientes e status
        const { data: precatoriosData, error: precatoriosError } = await supabase
          .from('precatorios')
          .select(`
            *,
            cliente:beneficiario_id(id, nome, email, telefone, cpf_cnpj),
            status_info:status_id(id, nome, codigo, cor)
          `)
          .eq('tipo', tipo)
          .eq('is_deleted', false); // Filtrar apenas precatórios não excluídos

        console.log('Dados brutos dos precatórios:', precatoriosData?.slice(0, 3));

        console.log('Resposta do Supabase (precatórios):', {
          count: precatoriosData?.length,
          error: precatoriosError?.message
        });

        if (precatoriosError) {
          console.error('Erro ao buscar precatórios:', precatoriosError);
          throw precatoriosError;
        }

        // Buscar todas as colunas do kanban
        const { data: colunasData } = await supabase
          .from('kanban_colunas')
          .select(`
            *,
            status:status_uuid(id, nome, codigo, cor)
          `)
          .order('ordem');

        // Criar mapa de status_uuid para colunas
        const statusToColumnMap = {};
        if (colunasData) {
          colunasData.forEach(coluna => {
            if (coluna.status_uuid) {
              statusToColumnMap[coluna.status_uuid] = coluna.id;
            }
          });
        }

        console.log('Mapeamento de status para colunas:', statusToColumnMap);

        // Mapear os dados para o formato esperado
        const precatoriosMapeados = precatoriosData?.map(p => {
          // Log detalhado para cada precatório
          console.log(`Mapeando precatório: ID=${p.id}, número=${p.numero_precatorio}, status_id=${p.status_id}, status_info=`, p.status_info);

          // Determinar o status baseado no status_id ou no campo status
          const statusInfo = p.status_id && p.status_info ? p.status_info : null;
          const statusCodigo = statusInfo ? statusInfo.codigo : (p.status || 'analise');
          const statusNome = statusInfo ? statusInfo.nome : (p.status ? formatarStatus(p.status) : 'Análise');

          // Garantir que o status_id seja preservado
          const statusId = p.status_id || null;

          // Log para debug
          if (statusId) {
            console.log(`Precatório ${p.numero_precatorio} tem status_id: ${statusId}`);
          } else {
            console.log(`Precatório ${p.numero_precatorio} NÃO tem status_id definido, usando status: ${statusCodigo}`);
          }

          return {
            ...p,
            numero: p.numero_precatorio,
            valor: p.valor_total,
            status: statusCodigo, // Usar o código do status
            status_nome: statusNome, // Nome formatado do status
            status_id: statusId, // ID do status (UUID)
            status_info: statusInfo, // Informações completas do status
            cliente: p.cliente ? {
              nome: p.cliente.nome,
              email: p.cliente.email,
              telefone: p.cliente.telefone,
              avatar: ''
            } : undefined
          };
        }) || [];

        console.log('Precatórios mapeados:', precatoriosMapeados.length);

        // Log detalhado dos status dos precatórios para debug
        const statusMap = {};
        precatoriosMapeados.forEach(p => {
          statusMap[p.status_nome] = (statusMap[p.status_nome] || 0) + 1;
        });
        console.log('Distribuição de status dos precatórios:', statusMap);

        // Atualizar os status_id no banco de dados para precatórios que não têm
        precatoriosMapeados.forEach(async p => {
          if (!p.status_id && p.status) {
            console.log(`Precatório ${p.id} não tem status_id. Buscando status_id para código ${p.status}...`);

            // Buscar o status_id correspondente ao código
            const { data: statusData } = await supabase
              .from('status_precatorios')
              .select('id')
              .eq('codigo', p.status)
              .maybeSingle();

            if (statusData) {
              console.log(`Atualizando status_id do precatório ${p.id} para ${statusData.id}`);
              await supabase
                .from('precatorios')
                .update({ status_id: statusData.id })
                .eq('id', p.id);
            }
          }
        });

        return precatoriosMapeados;
      },
      // Cache por apenas 30 segundos para garantir dados atualizados
      30 * 1000
    );
  } catch (error) {
    console.error('Erro na função buscarTodosPrecatorios:', error);
    throw error;
  }
}

// Função para formatar o nome do status
function formatarStatus(codigo: string): string {
  const statusMap: Record<string, string> = {
    'analise': 'Análise',
    'proposta_tmj': 'Proposta TMJ',
    'proposta_btg': 'Proposta BTG',
    'negociacao': 'Negociação',
    'documentacao': 'Documentação',
    'pagamento': 'Pagamento',
    'concluido': 'Concluído',
    'cancelado': 'Cancelado'
  };

  return statusMap[codigo] || codigo;
}

// Função auxiliar para remover acentos
function removerAcentos(texto: string): string {
  return texto.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
}

// Função auxiliar para normalizar o status
function normalizarStatus(status: string): string {
  // Converter para lowercase e remover espaços extras
  const statusNormalizado = String(status || '').toLowerCase().trim();

  // Mapeamento de status conhecidos para seus valores corretos
  const statusMap: Record<string, string> = {
    // Variações de "Análise"
    'análise': 'analise',
    'analise': 'analise',
    'em_analise': 'analise',
    'em análise': 'analise',

    // Variações de "Proposta TMJ"
    'proposta tmj': 'proposta_tmj',
    'proposta_tmj': 'proposta_tmj',

    // Variações de "Proposta BTG"
    'proposta btg': 'proposta_btg',
    'proposta_btg': 'proposta_btg',

    // Variações de "Negociação"
    'negociação': 'negociacao',
    'negociacao': 'negociacao',

    // Variações de "Documentação"
    'documentação': 'documentacao',
    'documentacao': 'documentacao',

    // Variações de "Pagamento"
    'pagamento': 'pagamento',

    // Variações de "Concluído"
    'concluído': 'concluido',
    'concluido': 'concluido',

    // Variações de "Cancelado"
    'cancelado': 'cancelado',

    // Status antigos do sistema
    'novo': 'analise',
    'em_processamento': 'documentacao',
    'aprovado': 'proposta_tmj',
    'rejeitado': 'cancelado'
  };

  // Retornar o status normalizado ou o original se não estiver no mapeamento
  return statusMap[statusNormalizado] || statusNormalizado;
}

// Salvar precatório (criar ou atualizar)
export async function salvarPrecatorio(precatorio: Precatorio): Promise<Precatorio> {
  try {
    console.log(`Iniciando ${precatorio.id ? 'atualização' : 'criação'} de precatório no Supabase...`);

    // Normalizar o status para garantir compatibilidade com as colunas do kanban
    const statusNormalizado = normalizarStatus(precatorio.status);
    console.log(`Status original: "${precatorio.status}", status normalizado: "${statusNormalizado}"`);

    // Atualizar o status no objeto precatorio
    precatorio.status = statusNormalizado;

    // Buscar o status_id correspondente ao status normalizado
    let statusIdParaSalvar = precatorio.status_id;

    if (!statusIdParaSalvar) {
      console.log(`Buscando status_id para código ${statusNormalizado}...`);

      const { data: statusData } = await supabase
        .from('status_precatorios')
        .select('id')
        .eq('codigo', statusNormalizado)
        .maybeSingle();

      if (statusData) {
        statusIdParaSalvar = statusData.id;
        console.log(`Encontrado status_id ${statusIdParaSalvar} para código ${statusNormalizado}`);
      }
    }

    let result;

    if (precatorio.id) {
      // Atualizar precatório existente
      const updateData: any = {
        numero_precatorio: precatorio.numero,
        status: precatorio.status,
        valor_total: precatorio.valor,
        prioridade: precatorio.prioridade,
        entidade_devedora: precatorio.entidade_devedora,
        natureza: precatorio.natureza,
        data_previsao_pagamento: precatorio.data_previsao_pagamento,
        updated_at: new Date().toISOString()
      };

      // Adicionar status_id se disponível
      if (statusIdParaSalvar) {
        updateData.status_id = statusIdParaSalvar;
      }

      const { data, error } = await supabase
        .from('precatorios')
        .update(updateData)
        .eq('id', precatorio.id)
        .select()
        .single();

      if (error) {
        console.error('Erro ao atualizar precatório:', error);
        throw error;
      }

      result = data;
    } else {
      // Criar novo precatório
      const currentDate = new Date().toISOString();

      const insertData: any = {
        numero_precatorio: precatorio.numero_precatorio || precatorio.numero,
        beneficiario_id: precatorio.beneficiario_id,
        tribunal_id: precatorio.tribunal_id,
        status: precatorio.status,
        valor_total: precatorio.valor_total || precatorio.valor,
        prioridade: precatorio.prioridade || 'normal',
        entidade_devedora: precatorio.entidade_devedora || '',
        natureza: precatorio.natureza || '',
        data_previsao_pagamento: precatorio.data_previsao_pagamento || null,
        tipo: precatorio.tipo || 'PRECATORIO',
        categoria: precatorio.tipo || 'PRECATORIO',
        observacoes: precatorio.observacoes || '',
        data_entrada: currentDate,
      };

      // Adicionar status_id se disponível
      if (statusIdParaSalvar) {
        insertData.status_id = statusIdParaSalvar;
      }

      // Adicionar campos de data e is_deleted
      insertData.created_at = currentDate;
      insertData.updated_at = currentDate;
      insertData.is_deleted = false;

      const { data, error } = await supabase
        .from('precatorios')
        .insert([insertData])
        .select()
        .single();

      if (error) {
        console.error('Erro ao criar precatório:', error);
        throw error;
      }

      result = data;
    }

    // Limpar cache de precatórios para forçar nova busca
    clearCacheItem(`precatorios-${precatorio.tipo || 'PRECATORIO'}`);

    console.log(`Precatório ${precatorio.id ? 'atualizado' : 'criado'} com sucesso:`, result);
    return result;
  } catch (error) {
    console.error(`Erro ao ${precatorio.id ? 'atualizar' : 'criar'} precatório:`, error);
    throw error;
  }
}

// Excluir um precatório
export async function excluirPrecatorio(id: string): Promise<void> {
  try {
    console.log(`Iniciando exclusão de precatório com ID ${id} no Supabase...`);

    // Obter o tipo do precatório antes de excluir para limpar o cache correto
    const { data: precatorioData } = await supabase
      .from('precatorios')
      .select('tipo')
      .eq('id', id)
      .single();

    const tipo = precatorioData?.tipo || 'PRECATORIO';

    // Excluir o precatório
    const { error } = await supabase
      .from('precatorios')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Erro ao excluir precatório:', error);
      throw error;
    }

    // Limpar cache de precatórios para forçar nova busca
    clearCacheItem(`precatorios-${tipo}`);

    console.log(`Precatório com ID ${id} excluído com sucesso`);
  } catch (error) {
    console.error('Erro ao excluir precatório:', error);
    throw error;
  }
}

// Atualizar status de um precatório - versão simplificada e mais robusta
export async function atualizarStatusPrecatorio(id: string, novoStatus: string): Promise<void> {
  try {
    console.log(`Atualizando status do precatório ${id} para ${novoStatus}...`);

    // Obter o tipo do precatório antes de atualizar para atualizar o cache correto
    const { data: precatorioData, error: precatorioError } = await supabase
      .from('precatorios')
      .select('tipo, status, status_id')
      .eq('id', id)
      .single();

    if (precatorioError) {
      console.error('Erro ao buscar precatório:', precatorioError);
      throw new Error(`Não foi possível encontrar o precatório com ID ${id}`);
    }

    const tipo = precatorioData?.tipo || 'PRECATORIO';
    const statusAtual = precatorioData?.status;
    const statusIdAtual = precatorioData?.status_id;

    console.log(`Precatório ${id} - Tipo: ${tipo}, Status atual: ${statusAtual}, Status ID atual: ${statusIdAtual}`);

    // Variáveis para armazenar o novo status e status_id
    let statusParaSalvar = '';
    let statusIdParaSalvar = null;

    // Abordagem simplificada: verificar diretamente se o novoStatus é um ID de coluna personalizada
    try {
      // CASO 1: Verificar se o novoStatus é um ID de coluna personalizada
      const { data: colunaData } = await supabase
        .from('kanban_colunas_personalizadas')
        .select('id, nome, status_id')
        .eq('id', novoStatus)
        .eq('is_deleted', false)
        .maybeSingle();

      if (colunaData) {
        console.log(`Coluna personalizada encontrada pelo ID ${novoStatus}: ${colunaData.nome}`);

        // Se a coluna tem status_id, usar diretamente
        if (colunaData.status_id) {
          statusIdParaSalvar = colunaData.status_id;

          // Buscar o código do status
          const { data: statusData } = await supabase
            .from('status_precatorios')
            .select('codigo')
            .eq('id', statusIdParaSalvar)
            .maybeSingle();

          if (statusData) {
            statusParaSalvar = statusData.codigo;
          }
        }
      }
      // CASO 2: Se não for uma coluna personalizada, verificar se é um status direto
      else {
        // Verificar se é um ID de status
        const { data: statusData } = await supabase
          .from('status_precatorios')
          .select('id, codigo')
          .eq('id', novoStatus)
          .maybeSingle();

        if (statusData) {
          statusIdParaSalvar = statusData.id;
          statusParaSalvar = statusData.codigo;
        }
        // Se não for um ID de status, usar como código
        else {
          statusParaSalvar = novoStatus;
        }
      }
    } catch (error) {
      console.error('Erro ao verificar coluna/status:', error);
      // Em caso de erro, usar o novoStatus diretamente como código
      statusParaSalvar = novoStatus;
    }

    // Garantir que temos pelo menos um valor para atualizar
    if (!statusParaSalvar && !statusIdParaSalvar) {
      statusParaSalvar = novoStatus;
    }

    // Atualizar o precatório com o novo status e status_id
    const updateData: any = {
      updated_at: new Date().toISOString()
    };

    // Só atualizar o status se tivermos um valor válido
    if (statusParaSalvar) {
      updateData.status = statusParaSalvar;
    }

    // Só atualizar o status_id se tivermos um valor válido
    if (statusIdParaSalvar) {
      updateData.status_id = statusIdParaSalvar;
    } else if (novoStatus && novoStatus.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
      // Se o novoStatus parece ser um UUID válido e não temos statusIdParaSalvar, usar o novoStatus como status_id
      console.log(`Usando novoStatus como status_id: ${novoStatus}`);
      updateData.status_id = novoStatus;
    }

    console.log(`Atualizando precatório com:`, updateData);

    const { error } = await supabase
      .from('precatorios')
      .update(updateData)
      .eq('id', id);

    if (error) {
      console.error('Erro ao atualizar status do precatório:', error);
      throw new Error(`Falha ao atualizar o status do precatório: ${error.message}`);
    }

    // Limpar o cache para garantir que os dados sejam atualizados
    clearCacheItem(`precatorios-${tipo}`);
    clearCacheItem(`precatorios_${tipo}`);

    console.log(`Status do precatório ${id} atualizado para ${statusParaSalvar} (status_id: ${statusIdParaSalvar})`);

    return;
  } catch (error) {
    console.error('Erro ao atualizar status do precatório:', error);
    throw error;
  }
}
