import { supabase } from '@/lib/supabase';
import { Precatorio, KanbanColuna } from '@/components/Precatorios/types';

/**
 * Serviço simplificado e otimizado para o Kanban de Precatórios
 * Resolve problemas de sincronização entre status e colunas
 */

// Função para garantir que existe uma relação correta entre status e colunas kanban
export async function sincronizarStatusEColunas(): Promise<void> {
  try {
    console.log('[KanbanService] Iniciando sincronização entre status e colunas...');

    // 1. Buscar todos os status ativos
    const { data: statusList, error: statusError } = await supabase
      .from('status_precatorios')
      .select('*')
      .eq('ativo', true)
      .order('ordem');

    if (statusError) {
      console.error('[KanbanService] Erro ao buscar status:', statusError);
      return;
    }

    if (!statusList || statusList.length === 0) {
      console.log('[KanbanService] Nenhum status encontrado, criando status padrão...');
      await criarStatusPadrao();
      return;
    }

    // 2. Verificar se todos os status estão marcados como colunas kanban
    const statusSemKanban = statusList.filter(s => !s.kanban_coluna);

    if (statusSemKanban.length > 0) {
      console.log(`[KanbanService] Atualizando ${statusSemKanban.length} status para serem colunas kanban`);

      for (const status of statusSemKanban) {
        await supabase
          .from('status_precatorios')
          .update({
            kanban_coluna: true,
            visivel: true,
            updated_at: new Date().toISOString()
          })
          .eq('id', status.id);
      }
    }

    // 3. Verificar e corrigir precatórios com status_id inválido
    await corrigirStatusIdPrecatorios(statusList);

    console.log('[KanbanService] Sincronização concluída com sucesso');
  } catch (error) {
    console.error('[KanbanService] Erro na sincronização:', error);
  }
}

// Função para criar status padrão se não existirem
async function criarStatusPadrao(): Promise<void> {
  const statusPadrao = [
    { nome: 'Novo', codigo: 'novo', cor: '#3b82f6', ordem: 1, is_default: true },
    { nome: 'Em Análise', codigo: 'analise', cor: '#8b5cf6', ordem: 2 },
    { nome: 'Em Andamento', codigo: 'em_andamento', cor: '#10b981', ordem: 3 },
    { nome: 'Aguardando', codigo: 'aguardando', cor: '#f59e0b', ordem: 4 },
    { nome: 'Concluído', codigo: 'concluido', cor: '#22c55e', ordem: 5 }
  ];

  const { data: userData } = await supabase.auth.getUser();
  const userId = userData?.user?.id;

  for (const status of statusPadrao) {
    const { error } = await supabase
      .from('status_precatorios')
      .insert({
        nome: status.nome,
        codigo: status.codigo,
        cor: status.cor,
        ordem: status.ordem,
        is_default: status.is_default || false,
        is_system: true,
        ativo: true,
        visivel: true,
        kanban_coluna: true,
        created_by: userId,
        created_at: new Date().toISOString()
      });

    if (error) {
      console.error(`[KanbanService] Erro ao criar status "${status.nome}":`, error);
    } else {
      console.log(`[KanbanService] Status "${status.nome}" criado com sucesso`);
    }
  }
}

// Função para corrigir precatórios com status_id inválido
async function corrigirStatusIdPrecatorios(statusList: any[]): Promise<void> {
  try {
    // Buscar precatórios que podem ter problemas de status_id
    const { data: precatorios, error } = await supabase
      .from('precatorios')
      .select('id, status, status_id')
      .or('is_deleted.is.null,is_deleted.eq.false');

    if (error || !precatorios) {
      console.error('[KanbanService] Erro ao buscar precatórios para correção:', error);
      return;
    }

    const statusMap = new Map(statusList.map(s => [s.codigo, s.id]));
    const statusPadrao = statusList.find(s => s.is_default) || statusList[0];

    let corrigidos = 0;

    for (const precatorio of precatorios) {
      let novoStatusId = null;

      // Se não tem status_id ou tem um status_id inválido
      if (!precatorio.status_id || !statusList.find(s => s.id === precatorio.status_id)) {
        // Tentar mapear pelo código do status
        if (precatorio.status && statusMap.has(precatorio.status)) {
          novoStatusId = statusMap.get(precatorio.status);
        } else {
          // Usar status padrão
          novoStatusId = statusPadrao.id;
        }

        // Atualizar no banco
        if (novoStatusId) {
          const { error: updateError } = await supabase
            .from('precatorios')
            .update({
              status_id: novoStatusId,
              status: statusList.find(s => s.id === novoStatusId)?.codigo || 'novo',
              updated_at: new Date().toISOString()
            })
            .eq('id', precatorio.id);

          if (!updateError) {
            corrigidos++;
          }
        }
      }
    }

    if (corrigidos > 0) {
      console.log(`[KanbanService] Corrigidos ${corrigidos} precatórios com status_id inválido`);
    }
  } catch (error) {
    console.error('[KanbanService] Erro ao corrigir status_id dos precatórios:', error);
  }
}

// Função principal para buscar precatórios para o kanban
export async function buscarPrecatoriosKanban(): Promise<Precatorio[]> {
  try {
    console.log('[KanbanService] Buscando precatórios para kanban...');

    // Garantir sincronização antes de buscar
    await sincronizarStatusEColunas();

    // Buscar precatórios sem join para evitar problemas
    const { data: precatoriosData, error } = await supabase
      .from('precatorios')
      .select(`
        id,
        numero_precatorio,
        valor_total,
        desconto,
        status,
        status_id,
        beneficiario_id,
        responsavel_id,
        tribunal_id,
        data_previsao_pagamento,
        natureza,
        observacoes,
        tags,
        prioridade,
        created_at,
        updated_at,
        tipo
      `)
      .or('is_deleted.is.null,is_deleted.eq.false')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('[KanbanService] Erro ao buscar precatórios:', error);
      throw new Error(`Erro ao carregar precatórios: ${error.message}`);
    }

    if (!precatoriosData || precatoriosData.length === 0) {
      console.log('[KanbanService] Nenhum precatório encontrado');
      return [];
    }

    console.log(`[KanbanService] Encontrados ${precatoriosData.length} precatórios`);

    // Buscar dados relacionados
    const { beneficiarios, responsaveis, tribunais } = await buscarDadosRelacionados(precatoriosData);

    // Mapear para o formato esperado
    const precatorios: Precatorio[] = precatoriosData.map(p => mapearPrecatorio(p, beneficiarios, responsaveis, tribunais));

    return precatorios;
  } catch (error) {
    console.error('[KanbanService] Erro ao buscar precatórios:', error);
    throw error;
  }
}

// Função para buscar colunas do kanban
export async function buscarColunasKanban(): Promise<KanbanColuna[]> {
  try {
    console.log('[KanbanService] Buscando colunas do kanban...');

    // Garantir sincronização
    await sincronizarStatusEColunas();

    // Buscar status que são colunas kanban
    const { data: statusList, error } = await supabase
      .from('status_precatorios')
      .select('*')
      .eq('ativo', true)
      .eq('kanban_coluna', true)
      .order('ordem');

    if (error) {
      console.error('[KanbanService] Erro ao buscar colunas:', error);
      throw new Error(`Erro ao carregar colunas: ${error.message}`);
    }

    if (!statusList || statusList.length === 0) {
      console.log('[KanbanService] Nenhuma coluna encontrada');
      return [];
    }

    // Mapear para formato de colunas
    const colunas: KanbanColuna[] = statusList.map(status => ({
      id: status.id,
      nome: status.nome,
      name: status.nome,
      cor: status.cor,
      color: status.cor,
      tipo: 'AMBOS',
      ordem: status.ordem,
      status_id: status.codigo,
      codigo: status.codigo,
      status_uuid: status.id,
      status_ref_id: status.id,
      ativo: status.ativo,
      is_default: status.is_default || false,
      is_system: status.is_system || false,
      status: {
        id: status.id,
        nome: status.nome,
        codigo: status.codigo,
        cor: status.cor
      }
    }));

    console.log(`[KanbanService] Encontradas ${colunas.length} colunas`);
    return colunas;
  } catch (error) {
    console.error('[KanbanService] Erro ao buscar colunas:', error);
    throw error;
  }
}

// Função auxiliar para buscar dados relacionados
async function buscarDadosRelacionados(precatorios: any[]) {
  const beneficiarioIds = [...new Set(precatorios.filter(p => p.beneficiario_id).map(p => p.beneficiario_id))];
  const responsavelIds = [...new Set(precatorios.filter(p => p.responsavel_id).map(p => p.responsavel_id))];
  const tribunalIds = [...new Set(precatorios.filter(p => p.tribunal_id).map(p => p.tribunal_id))];

  const [beneficiariosResult, responsaveisResult, tribunaisResult] = await Promise.all([
    beneficiarioIds.length > 0 ? supabase.from('clientes').select('id, nome, email, telefone').in('id', beneficiarioIds) : { data: [] },
    responsavelIds.length > 0 ? supabase.from('profiles').select('id, nome, email, foto_url').in('id', responsavelIds) : { data: [] },
    tribunalIds.length > 0 ? supabase.from('tribunais').select('id, nome').in('id', tribunalIds) : { data: [] }
  ]);

  return {
    beneficiarios: new Map((beneficiariosResult.data || []).map(b => [b.id, b])),
    responsaveis: new Map((responsaveisResult.data || []).map(r => [r.id, r])),
    tribunais: new Map((tribunaisResult.data || []).map(t => [t.id, t]))
  };
}

// Função auxiliar para mapear precatório
function mapearPrecatorio(precatorio: any, beneficiarios: Map<string, any>, responsaveis: Map<string, any>, tribunais: Map<string, any>): Precatorio {
  const beneficiario = beneficiarios.get(precatorio.beneficiario_id);
  const responsavel = responsaveis.get(precatorio.responsavel_id);
  const tribunal = tribunais.get(precatorio.tribunal_id);

  return {
    id: precatorio.id,
    numero: precatorio.numero_precatorio || `PR-${precatorio.id.substring(0, 4)}`,
    numero_precatorio: precatorio.numero_precatorio,
    valor: parseFloat(String(precatorio.valor_total || 0)),
    valor_total: precatorio.valor_total,
    desconto: parseFloat(String(precatorio.desconto || 0)),
    status: precatorio.status || 'novo',
    status_id: precatorio.status_id,
    cliente: {
      nome: beneficiario?.nome || 'Cliente não identificado',
      avatar: '',
      email: beneficiario?.email || '',
      telefone: beneficiario?.telefone || ''
    },
    responsavel: {
      nome: responsavel?.nome || 'Não atribuído',
      avatar: responsavel?.foto_url || '',
      cargo: 'Responsável'
    },
    dataCriacao: precatorio.created_at || new Date().toISOString(),
    dataAtualizacao: precatorio.updated_at || new Date().toISOString(),
    dataVencimento: precatorio.data_previsao_pagamento || null,
    tribunal: tribunal?.nome || 'Tribunal não especificado',
    natureza: precatorio.natureza || '',
    documentos: [],
    historico: [],
    observacoes: precatorio.observacoes || '',
    tags: Array.isArray(precatorio.tags) ? precatorio.tags : [],
    prioridade: precatorio.prioridade || 'media',
    beneficiario_id: precatorio.beneficiario_id,
    tribunal_id: precatorio.tribunal_id,
    responsavel_id: precatorio.responsavel_id,
    tipo: precatorio.tipo || 'PRECATORIO'
  };
}

// Função para atualizar status de um precatório
export async function atualizarStatusPrecatorioKanban(precatorioId: string, novoStatusId: string): Promise<boolean> {
  try {
    console.log(`[KanbanService] Atualizando status do precatório ${precatorioId} para ${novoStatusId}`);

    // Buscar o status
    const { data: status, error: statusError } = await supabase
      .from('status_precatorios')
      .select('id, codigo, nome')
      .eq('id', novoStatusId)
      .single();

    if (statusError || !status) {
      console.error('[KanbanService] Status não encontrado:', statusError);
      return false;
    }

    // Atualizar o precatório
    const { error: updateError } = await supabase
      .from('precatorios')
      .update({
        status_id: status.id,
        status: status.codigo,
        updated_at: new Date().toISOString()
      })
      .eq('id', precatorioId);

    if (updateError) {
      console.error('[KanbanService] Erro ao atualizar precatório:', updateError);
      return false;
    }

    console.log(`[KanbanService] Precatório ${precatorioId} atualizado para status ${status.nome}`);
    return true;
  } catch (error) {
    console.error('[KanbanService] Erro ao atualizar status:', error);
    return false;
  }
}
