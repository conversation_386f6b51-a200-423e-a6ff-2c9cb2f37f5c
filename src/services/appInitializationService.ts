/**
 * App Initialization Service
 * Handles application startup, cache warming, and performance optimization
 */

import { 
  warmupApplicationCache, 
  warmupUserSpecificCache, 
  startBackgroundCacheRefresh,
  getWarmupConfigForRole,
  LIGHTWEIGHT_WARMUP_CONFIG 
} from './cacheWarmupService';
import { cacheLogger } from '@/lib/logger';
import { supabase } from '@/lib/supabase';

export interface AppInitializationConfig {
  enableCacheWarmup: boolean;
  enableBackgroundRefresh: boolean;
  warmupTimeout: number; // milliseconds
  skipWarmupOnSlowConnection: boolean;
}

export interface InitializationResult {
  success: boolean;
  duration: number;
  cacheWarmedUp: boolean;
  backgroundRefreshStarted: boolean;
  errors: string[];
}

/**
 * Default initialization configuration
 */
export const DEFAULT_INIT_CONFIG: AppInitializationConfig = {
  enableCacheWarmup: true,
  enableBackgroundRefresh: true,
  warmupTimeout: 10000, // 10 seconds
  skipWarmupOnSlowConnection: true,
};

/**
 * Fast initialization configuration for slower devices/connections
 */
export const FAST_INIT_CONFIG: AppInitializationConfig = {
  enableCacheWarmup: true,
  enableBackgroundRefresh: true,
  warmupTimeout: 5000, // 5 seconds
  skipWarmupOnSlowConnection: true,
};

/**
 * Initialize the application with performance optimizations
 */
export async function initializeApplication(
  config: AppInitializationConfig = DEFAULT_INIT_CONFIG
): Promise<InitializationResult> {
  const startTime = Date.now();
  const errors: string[] = [];
  let cacheWarmedUp = false;
  let backgroundRefreshStarted = false;

  cacheLogger.info('Iniciando inicialização da aplicação...');

  try {
    // Check connection speed if enabled
    if (config.skipWarmupOnSlowConnection) {
      const isSlowConnection = await detectSlowConnection();
      if (isSlowConnection) {
        cacheLogger.info('Conexão lenta detectada, usando configuração otimizada');
        config = { ...config, warmupTimeout: 3000 };
      }
    }

    // Start background cache refresh if enabled
    if (config.enableBackgroundRefresh) {
      try {
        startBackgroundCacheRefresh();
        backgroundRefreshStarted = true;
        cacheLogger.debug('Atualização automática do cache iniciada');
      } catch (error) {
        const errorMsg = 'Erro ao iniciar atualização automática do cache';
        cacheLogger.error(errorMsg, error);
        errors.push(errorMsg);
      }
    }

    // Warm up cache if enabled
    if (config.enableCacheWarmup) {
      try {
        await Promise.race([
          warmupApplicationCache(LIGHTWEIGHT_WARMUP_CONFIG),
          new Promise((_, reject) => 
            setTimeout(() => reject(new Error('Cache warmup timeout')), config.warmupTimeout)
          )
        ]);
        cacheWarmedUp = true;
        cacheLogger.debug('Cache aquecido com sucesso durante a inicialização');
      } catch (error) {
        const errorMsg = 'Erro ou timeout no aquecimento do cache';
        cacheLogger.warn(errorMsg, error);
        errors.push(errorMsg);
        // Continue initialization even if cache warmup fails
      }
    }

    // Initialize performance monitoring
    try {
      await initializePerformanceMonitoring();
      cacheLogger.debug('Monitoramento de performance inicializado');
    } catch (error) {
      const errorMsg = 'Erro ao inicializar monitoramento de performance';
      cacheLogger.error(errorMsg, error);
      errors.push(errorMsg);
    }

    const duration = Date.now() - startTime;
    cacheLogger.info(`Inicialização da aplicação concluída em ${duration}ms`);

    return {
      success: errors.length === 0,
      duration,
      cacheWarmedUp,
      backgroundRefreshStarted,
      errors,
    };

  } catch (error) {
    const duration = Date.now() - startTime;
    const errorMsg = 'Erro crítico na inicialização da aplicação';
    cacheLogger.error(errorMsg, error);
    errors.push(errorMsg);

    return {
      success: false,
      duration,
      cacheWarmedUp,
      backgroundRefreshStarted,
      errors,
    };
  }
}

/**
 * Initialize application for a specific authenticated user
 */
export async function initializeUserSession(
  userId: string, 
  userRole: string
): Promise<void> {
  cacheLogger.info(`Inicializando sessão do usuário ${userId} (${userRole})`);

  try {
    // Get role-specific warmup configuration
    const warmupConfig = getWarmupConfigForRole(userRole);
    
    // Warm up user-specific cache in background
    Promise.resolve().then(async () => {
      try {
        await warmupUserSpecificCache(userId);
        await warmupApplicationCache(warmupConfig, userId);
        cacheLogger.debug(`Cache específico do usuário ${userId} aquecido`);
      } catch (error) {
        cacheLogger.error(`Erro no aquecimento do cache do usuário ${userId}:`, error);
      }
    });

    cacheLogger.info(`Sessão do usuário ${userId} inicializada com sucesso`);
  } catch (error) {
    cacheLogger.error(`Erro na inicialização da sessão do usuário ${userId}:`, error);
    throw error;
  }
}

/**
 * Detect slow connection based on simple timing test
 */
async function detectSlowConnection(): Promise<boolean> {
  try {
    const startTime = Date.now();
    
    // Simple ping to Supabase
    await supabase.from('profiles').select('count').limit(1).single();
    
    const duration = Date.now() - startTime;
    
    // Consider connection slow if ping takes more than 2 seconds
    return duration > 2000;
  } catch (error) {
    // If we can't test, assume slow connection for safety
    cacheLogger.warn('Não foi possível testar velocidade da conexão, assumindo conexão lenta');
    return true;
  }
}

/**
 * Initialize performance monitoring and metrics collection
 */
async function initializePerformanceMonitoring(): Promise<void> {
  // Set up performance observers if available
  if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
    try {
      // Monitor navigation timing
      const navObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.entryType === 'navigation') {
            const navEntry = entry as PerformanceNavigationTiming;
            cacheLogger.debug('Navigation timing:', {
              domContentLoaded: navEntry.domContentLoadedEventEnd - navEntry.domContentLoadedEventStart,
              loadComplete: navEntry.loadEventEnd - navEntry.loadEventStart,
              totalTime: navEntry.loadEventEnd - navEntry.fetchStart,
            });
          }
        });
      });
      
      navObserver.observe({ entryTypes: ['navigation'] });

      // Monitor resource loading
      const resourceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const slowResources = entries.filter(entry => entry.duration > 1000);
        
        if (slowResources.length > 0) {
          cacheLogger.warn(`${slowResources.length} recursos lentos detectados:`, 
            slowResources.map(r => ({ name: r.name, duration: r.duration }))
          );
        }
      });
      
      resourceObserver.observe({ entryTypes: ['resource'] });

    } catch (error) {
      cacheLogger.warn('Erro ao configurar observadores de performance:', error);
    }
  }

  // Set up memory monitoring if available
  if (typeof window !== 'undefined' && 'memory' in performance) {
    const memoryInfo = (performance as any).memory;
    cacheLogger.debug('Informações de memória:', {
      used: Math.round(memoryInfo.usedJSHeapSize / 1024 / 1024) + ' MB',
      total: Math.round(memoryInfo.totalJSHeapSize / 1024 / 1024) + ' MB',
      limit: Math.round(memoryInfo.jsHeapSizeLimit / 1024 / 1024) + ' MB',
    });
  }
}

/**
 * Cleanup function to be called when the app is being closed/refreshed
 */
export function cleanupApplication(): void {
  cacheLogger.info('Executando limpeza da aplicação...');
  
  try {
    // Clear any pending timers or intervals
    // Note: Background refresh intervals are cleared automatically when the page unloads
    
    // Log final cache metrics
    const { getCacheMetrics } = require('./cacheService');
    const finalMetrics = getCacheMetrics();
    cacheLogger.info('Métricas finais do cache:', finalMetrics);
    
    cacheLogger.info('Limpeza da aplicação concluída');
  } catch (error) {
    cacheLogger.error('Erro na limpeza da aplicação:', error);
  }
}

/**
 * Get initialization recommendations based on device capabilities
 */
export function getInitConfigForDevice(): AppInitializationConfig {
  if (typeof window === 'undefined') {
    return DEFAULT_INIT_CONFIG;
  }

  // Check for mobile device
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
    navigator.userAgent
  );

  // Check for slow device (simplified heuristic)
  const isSlowDevice = navigator.hardwareConcurrency && navigator.hardwareConcurrency < 4;

  // Check for limited memory
  const hasLimitedMemory = (navigator as any).deviceMemory && (navigator as any).deviceMemory < 4;

  if (isMobile || isSlowDevice || hasLimitedMemory) {
    cacheLogger.debug('Dispositivo com recursos limitados detectado, usando configuração otimizada');
    return FAST_INIT_CONFIG;
  }

  return DEFAULT_INIT_CONFIG;
}

// Set up cleanup on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', cleanupApplication);
}
