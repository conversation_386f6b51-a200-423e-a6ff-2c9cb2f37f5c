import { supabase } from '@/lib/supabase';

export interface UserProfileData {
  id: string;
  email: string;
  nome?: string;
  role: string;
  cargo?: string;
  departamento?: string;
  foto_url?: string;
  data_entrada?: string;
  status?: string;
  telefone?: string;
  created_at?: string;
  updated_at?: string;
  custom_role_id?: string;
}

export interface UserMetrics {
  totalPrecatorios: number;
  precatoriosConcluidos: number;
  precatoriosEmAndamento: number;
  totalClientes: number;
  clientesAtivos: number;
  totalTarefas: number;
  tarefasConcluidas: number;
  tarefasPendentes: number;
  valorTotalPrecatorios: number;
  metasMensais: {
    precatorios: { atual: number; meta: number; progresso: number };
    clientes: { atual: number; meta: number; progresso: number };
    faturamento: { atual: string; meta: string; progresso: number };
  };
}

export interface UserPerformanceData {
  desempenhoMensal: Array<{
    mes: string;
    precatorios: number;
    clientes: number;
    meta: number;
    tarefas: number;
  }>;
  precatoriosRecentes: Array<{
    id: string;
    numero_precatorio: string;
    cliente: string;
    tipo: string;
    status: string;
    prazo: string;
    valor: string;
    progresso: number;
  }>;
  tarefasRecentes: Array<{
    id: string;
    titulo: string;
    precatorio?: string;
    cliente?: string;
    prazo: string;
    status: string;
    prioridade: string;
    progresso: number;
  }>;
  notificacoes: Array<{
    id: number;
    titulo: string;
    descricao: string;
    data: string;
    lida: boolean;
    tipo: string;
  }>;
}

/**
 * Busca dados completos do perfil do usuário usando função segura
 */
export async function getUserProfileData(userId: string): Promise<UserProfileData | null> {
  try {
    // Usar função RPC segura que verifica permissões
    const { data, error } = await supabase
      .rpc('get_user_profile_safe', { target_user_id: userId });

    if (error) {
      console.error('Erro ao buscar perfil do usuário:', error);
      return null;
    }

    // A função RPC agora retorna JSON diretamente
    return data || null;
  } catch (error) {
    console.error('Erro ao buscar perfil do usuário:', error);
    return null;
  }
}

/**
 * Busca métricas de desempenho do usuário usando função segura
 */
export async function getUserMetrics(userId: string): Promise<UserMetrics> {
  try {
    // Usar função RPC segura que verifica permissões e calcula métricas
    const { data, error } = await supabase
      .rpc('get_user_metrics_safe', { target_user_id: userId });

    if (error) {
      console.error('Erro ao buscar métricas do usuário:', error);
      throw error;
    }

    // A função RPC retorna um JSON com as métricas
    return data || {
      totalPrecatorios: 0,
      precatoriosConcluidos: 0,
      precatoriosEmAndamento: 0,
      totalClientes: 0,
      clientesAtivos: 0,
      totalTarefas: 0,
      tarefasConcluidas: 0,
      tarefasPendentes: 0,
      valorTotalPrecatorios: 0,
      metasMensais: {
        precatorios: { atual: 0, meta: 50, progresso: 0 },
        clientes: { atual: 0, meta: 30, progresso: 0 },
        faturamento: { atual: 'R$ 0', meta: 'R$ 200.000', progresso: 0 }
      }
    };
  } catch (error) {
    console.error('Erro ao buscar métricas do usuário:', error);
    return {
      totalPrecatorios: 0,
      precatoriosConcluidos: 0,
      precatoriosEmAndamento: 0,
      totalClientes: 0,
      clientesAtivos: 0,
      totalTarefas: 0,
      tarefasConcluidas: 0,
      tarefasPendentes: 0,
      valorTotalPrecatorios: 0,
      metasMensais: {
        precatorios: { atual: 0, meta: 50, progresso: 0 },
        clientes: { atual: 0, meta: 30, progresso: 0 },
        faturamento: { atual: 'R$ 0', meta: 'R$ 200.000', progresso: 0 }
      }
    };
  }
}

/**
 * Busca dados de performance e atividades recentes
 */
export async function getUserPerformanceData(userId: string): Promise<UserPerformanceData> {
  try {
    // Buscar dados de performance mensal usando função RPC
    const { data: desempenhoMensal, error: performanceError } = await supabase
      .rpc('get_user_performance_monthly', { target_user_id: userId });

    if (performanceError) {
      console.error('Erro ao buscar performance mensal:', performanceError);
    }

    // Buscar precatórios recentes com dados mais completos
    const { data: precatoriosRecentes, error: precatoriosError } = await supabase
      .from('precatorios')
      .select(`
        id,
        numero_precatorio,
        valor_total,
        status_id,
        created_at,
        updated_at,
        tipo_precatorio,
        cliente:clientes(nome),
        status:status_precatorios(nome)
      `)
      .or(`responsavel_id.eq.${userId},created_by.eq.${userId}`)
      .eq('is_deleted', false)
      .order('updated_at', { ascending: false })
      .limit(5);

    if (precatoriosError) {
      console.error('Erro ao buscar precatórios recentes:', precatoriosError);
    }

    // Buscar tarefas recentes com dados mais completos
    const { data: tarefasRecentes, error: tarefasError } = await supabase
      .from('tasks')
      .select(`
        id,
        title,
        status,
        priority,
        due_date,
        created_at,
        updated_at,
        precatorio_id,
        cliente_id,
        precatorio:precatorios(numero_precatorio),
        cliente:clientes(nome)
      `)
      .or(`assignee_id.eq.${userId},created_by.eq.${userId}`)
      .order('updated_at', { ascending: false })
      .limit(5);

    if (tarefasError) {
      console.error('Erro ao buscar tarefas recentes:', tarefasError);
    }

    // Buscar notificações reais do usuário
    const { data: notificacoesReais, error: notificacoesError } = await supabase
      .from('notifications')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(10);

    if (notificacoesError) {
      console.error('Erro ao buscar notificações:', notificacoesError);
    }

    // Calcular progresso baseado no status
    const calcularProgresso = (status: string): number => {
      const statusMap: { [key: string]: number } = {
        'pendente': 0,
        'em_andamento': 50,
        'processando': 60,
        'em_analise': 40,
        'aguardando': 30,
        'concluido': 100,
        'finalizado': 100,
        'completed': 100,
        'done': 100
      };
      return statusMap[status?.toLowerCase()] || 25;
    };

    // Formatar dados de precatórios
    const precatoriosFormatados = precatoriosRecentes?.map(p => ({
      id: p.id,
      numero_precatorio: p.numero_precatorio || 'N/A',
      cliente: p.cliente?.nome || 'Cliente não informado',
      tipo: p.tipo_precatorio || 'Tributário',
      status: p.status?.nome || 'Desconhecido',
      prazo: p.updated_at ? new Date(p.updated_at).toLocaleDateString('pt-BR') : 'N/A',
      valor: `R$ ${Number(p.valor_total || 0).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`,
      progresso: calcularProgresso(p.status?.nome || '')
    })) || [];

    // Formatar dados de tarefas
    const tarefasFormatadas = tarefasRecentes?.map(t => ({
      id: t.id,
      titulo: t.title || 'Tarefa sem título',
      precatorio: t.precatorio?.numero_precatorio || undefined,
      cliente: t.cliente?.nome || undefined,
      prazo: t.due_date ? new Date(t.due_date).toLocaleDateString('pt-BR') : 'Sem prazo',
      status: t.status || 'Pendente',
      prioridade: t.priority || 'Média',
      progresso: calcularProgresso(t.status || '')
    })) || [];

    // Formatar notificações
    const notificacoesFormatadas = notificacoesReais?.map(n => ({
      id: n.id,
      titulo: n.title || 'Notificação',
      descricao: n.message || 'Sem descrição',
      data: formatarTempoRelativo(n.created_at),
      lida: n.read || false,
      tipo: n.type || 'geral'
    })) || [
      // Notificações padrão se não houver dados reais
      {
        id: 1,
        titulo: 'Bem-vindo ao sistema',
        descricao: 'Explore as funcionalidades disponíveis',
        data: '1 dia atrás',
        lida: true,
        tipo: 'sistema'
      }
    ];

    return {
      desempenhoMensal: desempenhoMensal || [],
      precatoriosRecentes: precatoriosFormatados,
      tarefasRecentes: tarefasFormatadas,
      notificacoes: notificacoesFormatadas
    };
  } catch (error) {
    console.error('Erro ao buscar dados de performance:', error);
    return {
      desempenhoMensal: [],
      precatoriosRecentes: [],
      tarefasRecentes: [],
      notificacoes: []
    };
  }
}

/**
 * Formatar tempo relativo (ex: "2 horas atrás")
 */
function formatarTempoRelativo(dataISO: string): string {
  const agora = new Date();
  const data = new Date(dataISO);
  const diffMs = agora.getTime() - data.getTime();
  const diffMinutos = Math.floor(diffMs / (1000 * 60));
  const diffHoras = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDias = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  if (diffMinutos < 60) {
    return `${diffMinutos} minuto${diffMinutos !== 1 ? 's' : ''} atrás`;
  } else if (diffHoras < 24) {
    return `${diffHoras} hora${diffHoras !== 1 ? 's' : ''} atrás`;
  } else if (diffDias < 7) {
    return `${diffDias} dia${diffDias !== 1 ? 's' : ''} atrás`;
  } else {
    return data.toLocaleDateString('pt-BR');
  }
}

/**
 * Atualiza dados do perfil do usuário
 */
export async function updateUserProfile(userId: string, updates: Partial<UserProfileData>): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('profiles')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId);

    if (error) {
      console.error('Erro ao atualizar perfil:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Erro ao atualizar perfil:', error);
    return false;
  }
}
