import { supabase, executeRobustQuery } from '@/lib/supabase';
import { Precatorio, KanbanColuna } from '@/components/Precatorios/types';
import { buscarTodosStatus, buscarStatusPadrao } from './statusPrecatoriosService';
import { getCache, setCache, clearCache } from './cacheService';

// Função para atualizar os status_id dos precatórios existentes
export async function atualizarStatusIdDosPrecatorios(): Promise<void> {
  try {
    console.log('[KanbanPrecatoriosService] Verificando precatórios sem status_id...');

    // Buscar precatórios sem status_id ou com status_id vazio
    const { data: precatoriosSemStatusId, error: errorPrecatorios } = await supabase
      .from('precatorios')
      .select('id, status, tipo')
      .or('status_id.is.null,status_id.eq.""')
      .not('status', 'is', null)
      .eq('is_deleted', false);

    if (errorPrecatorios) {
      console.error('[KanbanPrecatoriosService] Erro ao buscar precatórios sem status_id:', errorPrecatorios);
      return;
    }

    if (!precatoriosSemStatusId || precatoriosSemStatusId.length === 0) {
      console.log('[KanbanPrecatoriosService] Não foram encontrados precatórios sem status_id.');
      return;
    }

    console.log(`[KanbanPrecatoriosService] Encontrados ${precatoriosSemStatusId.length} precatórios sem status_id.`);

    // Buscar todos os status disponíveis
    const { data: statusList, error: errorStatus } = await supabase
      .from('status_precatorios')
      .select('id, codigo, nome, tipo');

    if (errorStatus || !statusList) {
      console.error('[KanbanPrecatoriosService] Erro ao buscar status:', errorStatus);
      return;
    }

    // Buscar colunas personalizadas do kanban
    const { data: colunasPersonalizadas, error: errorColunas } = await supabase
      .from('kanban_colunas_personalizadas')
      .select('id, nome, status_id, status_uuid')
      .eq('is_deleted', false);

    if (errorColunas) {
      console.warn('[KanbanPrecatoriosService] Erro ao buscar colunas personalizadas:', errorColunas);
      // Continuar mesmo com erro
    }

    // Criar um mapa de código para id
    const statusMap = new Map<string, string>();
    statusList.forEach(status => {
      if (!status.codigo || !status.id) return;

      statusMap.set(status.codigo, status.id);

      // Também mapear versões normalizadas
      const codigoNormalizado = removerAcentos(status.codigo.toLowerCase());
      statusMap.set(codigoNormalizado, status.id);

      // Mapear pelo nome normalizado
      if (status.nome) {
        const nomeNormalizado = removerAcentos(status.nome.toLowerCase()).replace(/\s+/g, '_');
        statusMap.set(nomeNormalizado, status.id);
      }
    });

    // Adicionar mapeamentos das colunas personalizadas
    if (colunasPersonalizadas && colunasPersonalizadas.length > 0) {
      colunasPersonalizadas.forEach(coluna => {
        if (coluna.nome && coluna.id) {
          const nomeNormalizado = removerAcentos(coluna.nome.toLowerCase()).replace(/\s+/g, '_');
          statusMap.set(nomeNormalizado, coluna.id);
        }

        // Mapear status_id da coluna para o id da coluna
        if (coluna.status_id && coluna.id) {
          statusMap.set(coluna.status_id, coluna.id);
        }

        // Mapear status_uuid da coluna para o id da coluna
        if (coluna.status_uuid && coluna.id) {
          statusMap.set(coluna.status_uuid, coluna.id);
        }
      });
    }

    // Atualizar cada precatório
    let atualizados = 0;
    for (const precatorio of precatoriosSemStatusId) {
      if (!precatorio.status) continue;

      // Tentar encontrar o status_id correspondente
      const statusNormalizado = removerAcentos(precatorio.status.toLowerCase());
      let statusId = statusMap.get(precatorio.status) || statusMap.get(statusNormalizado);

      // Se não encontrou, tentar encontrar um status compatível com o tipo do precatório
      if (!statusId) {
        const tipoCompativel = precatorio.tipo === 'RPV' ? 'RPV' : 'PRECATORIO';

        // Encontrar um status compatível com o tipo
        const statusCompativel = statusList.find(s =>
          (s.tipo === 'AMBOS' || s.tipo === tipoCompativel) &&
          s.codigo === 'analise'
        );

        if (statusCompativel) {
          statusId = statusCompativel.id;
          console.log(`[KanbanPrecatoriosService] Usando status compatível para ${precatorio.id}: ${statusCompativel.nome}`);
        } else {
          // Se ainda não encontrou, usar o primeiro status disponível
          statusId = statusList[0]?.id;
          console.log(`[KanbanPrecatoriosService] Usando primeiro status disponível para ${precatorio.id}: ${statusList[0]?.nome}`);
        }
      }

      if (statusId) {
        // Atualizar o precatório
        const { error: updateError } = await supabase
          .from('precatorios')
          .update({ status_id: statusId })
          .eq('id', precatorio.id);

        if (!updateError) {
          atualizados++;
          console.log(`[KanbanPrecatoriosService] Atualizado precatório ${precatorio.id} com status_id ${statusId}`);
        } else {
          console.error(`[KanbanPrecatoriosService] Erro ao atualizar precatório ${precatorio.id}:`, updateError);
        }
      } else {
        console.warn(`[KanbanPrecatoriosService] Não foi possível encontrar status_id para o precatório ${precatorio.id} com status ${precatorio.status}`);
      }
    }

    console.log(`[KanbanPrecatoriosService] Atualizados ${atualizados} de ${precatoriosSemStatusId.length} precatórios.`);
  } catch (error) {
    console.error('[KanbanPrecatoriosService] Erro ao atualizar status_id dos precatórios:', error);
  }
}

// Função para remover acentos de uma string
function removerAcentos(str: string): string {
  if (!str) return '';
  return str.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
}

// Função para buscar todos os precatórios diretamente para o kanban
export async function buscarPrecatoriosParaKanban(): Promise<Precatorio[]> {
  try {
    console.log('[KanbanPrecatoriosService] Iniciando busca de precatórios para o kanban...');

    // Verificar se a sessão está válida antes de buscar os dados
    try {
      console.log('[KanbanPrecatoriosService] Verificando autenticação antes de buscar dados...');

      // Verificar diretamente a sessão do Supabase em vez de usar o sistema de autenticação simplificado
      const { data: sessionData } = await supabase.auth.getSession();

      if (!sessionData.session) {
        console.warn('[KanbanPrecatoriosService] Sessão não encontrada, tentando continuar mesmo assim...');
        // Continuar mesmo sem sessão, pois o usuário pode ser admin e ter acesso direto
        // Não lançar erro para evitar o loop infinito de carregamento
      } else {
        console.log('[KanbanPrecatoriosService] Sessão válida encontrada, continuando...');
      }
    } catch (sessionError) {
      console.error('[KanbanPrecatoriosService] Erro ao verificar autenticação:', sessionError);
      // Continuar mesmo com erro de sessão, pois o Supabase tentará usar o token armazenado
      console.log('[KanbanPrecatoriosService] Tentando continuar mesmo com erro de sessão...');
    }

    // Verificar e atualizar precatórios sem status_id
    try {
      await atualizarStatusIdDosPrecatorios();
    } catch (updateError) {
      console.warn('[KanbanPrecatoriosService] Erro ao atualizar status_id dos precatórios:', updateError);
      // Continuar mesmo com erro, pois isso não deve impedir o carregamento dos precatórios
    }

    // Buscar precatórios com join para status_precatorios para garantir que temos os dados completos
    console.log('[KanbanPrecatoriosService] Buscando precatórios no Supabase...');

    let precatoriosData: any[] = [];
    let precatoriosError: any = null;

    // Abordagem robusta para buscar precatórios
    try {
      console.log('[KanbanPrecatoriosService] Buscando todos os precatórios...');

      // Usar executeRobustQuery para maior confiabilidade, com mais tentativas
      const result = await executeRobustQuery(async () => {
        return await supabase
          .from('precatorios')
          .select(`
            id,
            numero_precatorio,
            valor_total,
            desconto,
            status,
            status_id,
            beneficiario_id,
            responsavel_id,
            tribunal_id,
            data_previsao_pagamento,
            natureza,
            observacoes,
            tags,
            prioridade,
            created_at,
            updated_at,
            tipo,
            is_deleted
          `)
          .or('is_deleted.is.null,is_deleted.eq.false') // Usar filtro OR para incluir registros com is_deleted null ou false
          .order('created_at', { ascending: false });
      }, 'busca de precatórios para kanban', 5); // Aumentar para 5 tentativas

      const { data, error } = result;

      if (error) {
        console.error('[KanbanPrecatoriosService] Erro ao buscar precatórios:', error);
        precatoriosError = error;
      } else if (data && data.length > 0) {
        // Filtrar manualmente os registros não excluídos (redundante com o filtro OR acima, mas por segurança)
        precatoriosData = data.filter(p => p.is_deleted !== true);
        console.log(`[KanbanPrecatoriosService] Consulta retornou ${data.length} precatórios, filtrados para ${precatoriosData.length}`);
      } else {
        console.warn('[KanbanPrecatoriosService] Nenhum precatório encontrado');
      }
    } catch (queryError) {
      console.error('[KanbanPrecatoriosService] Erro ao executar consulta:', queryError);
      precatoriosError = queryError;
    }

    console.log(`[KanbanPrecatoriosService] Resultado da consulta: ${precatoriosData?.length || 0} precatórios encontrados`);
    if (precatoriosError) {
      console.error('[KanbanPrecatoriosService] Erro na consulta:', precatoriosError);
    }

    if (precatoriosError) {
      console.error('[KanbanPrecatoriosService] Erro ao buscar precatórios:', precatoriosError);
      throw new Error(`Falha ao carregar precatórios: ${precatoriosError.message || 'Erro desconhecido'}`);
    }

    if (!precatoriosData || precatoriosData.length === 0) {
      console.warn('[KanbanPrecatoriosService] Nenhum precatório encontrado');
      // Retornar array vazio em vez de lançar erro
      return [];
    }

    // Verificar e corrigir precatórios sem status_id
    const precatoriosParaAtualizar = [];
    for (const precatorio of (precatoriosData || [])) {
      if (!precatorio.status_id && precatorio.status) {
        // Buscar o status_id correspondente ao código
        const { data: statusData } = await supabase
          .from('status_precatorios')
          .select('id')
          .eq('codigo', precatorio.status)
          .maybeSingle();

        if (statusData) {
          precatoriosParaAtualizar.push({
            id: precatorio.id,
            status_id: statusData.id
          });

          // Atualizar o objeto em memória também
          precatorio.status_id = statusData.id;
        }
      }
    }

    // Atualizar precatórios sem status_id no banco de dados
    if (precatoriosParaAtualizar.length > 0) {
      for (const precatorio of precatoriosParaAtualizar) {
        await supabase
          .from('precatorios')
          .update({ status_id: precatorio.status_id })
          .eq('id', precatorio.id);
      }
    }

    // Buscar colunas personalizadas do kanban para verificar a configuração
    const { data: colunasData, error: colunasError } = await supabase
      .from('kanban_colunas_personalizadas')
      .select(`
        *,
        status:status_id(id, nome, codigo, cor)
      `)
      .eq('is_deleted', false)
      .order('ordem');

    if (colunasError) {
      // Se falhar, tentar buscar colunas normais como fallback
      const { data: colunasNormaisData, error: colunasNormaisError } = await supabase
        .from('kanban_colunas')
        .select(`
          *,
          status:status_uuid(id, nome, codigo, cor)
        `)
        .eq('ativo', true);
    }

    // Se não encontrou colunas personalizadas, criar colunas padrão
    if (!colunasData || colunasData.length === 0) {
      await criarColunasPersonalizadasPadrao();
    }

    // Verificar e corrigir precatórios sem status_id
    const precatoriosParaAtualizarStatus = [];

    // Buscar todos os status disponíveis para referência
    const { data: todosStatus } = await supabase
      .from('status_precatorios')
      .select('id, codigo, nome');

    const statusMap = new Map();
    if (todosStatus) {
      todosStatus.forEach(status => {
        statusMap.set(status.codigo, status.id);
        // Também mapear versões normalizadas dos códigos (sem acentos, lowercase)
        const codigoNormalizado = removerAcentos(status.codigo.toLowerCase());
        statusMap.set(codigoNormalizado, status.id);

        // Mapear também pelo nome normalizado
        const nomeNormalizado = removerAcentos(status.nome.toLowerCase()).replace(/\s+/g, '_');
        statusMap.set(nomeNormalizado, status.id);
      });
    }

    // Verificar cada precatório
    for (const precatorio of (precatoriosData || [])) {
      // Se não tem status_id mas tem status (código)
      if (!precatorio.status_id && precatorio.status) {
        // Tentar encontrar o status_id correspondente
        const statusId = statusMap.get(precatorio.status) ||
                         statusMap.get(removerAcentos(precatorio.status.toLowerCase()));

        if (statusId) {
          precatoriosParaAtualizarStatus.push({
            id: precatorio.id,
            status_id: statusId
          });

          // Atualizar em memória
          precatorio.status_id = statusId;
        }
      }
    }

    // Atualizar precatórios sem status_id no banco de dados
    if (precatoriosParaAtualizarStatus.length > 0) {
      for (const precatorio of precatoriosParaAtualizarStatus) {
        await supabase
          .from('precatorios')
          .update({ status_id: precatorio.status_id })
          .eq('id', precatorio.id);
      }
    }

    // Buscar dados básicos de clientes, responsáveis e tribunais
    const beneficiarioIds = [...new Set(precatoriosData?.filter(p => p.beneficiario_id).map(p => p.beneficiario_id) || [])];
    const responsavelIds = [...new Set(precatoriosData?.filter(p => p.responsavel_id).map(p => p.responsavel_id) || [])];
    const tribunalIds = [...new Set(precatoriosData?.filter(p => p.tribunal_id).map(p => p.tribunal_id) || [])];

    // Mapas para armazenar dados relacionados
    let beneficiariosMap = new Map();
    let responsaveisMap = new Map();
    let tribunaisMap = new Map();

    // Buscar beneficiários (clientes)
    if (beneficiarioIds.length > 0) {
      const { data: beneficiariosData } = await supabase
        .from('clientes')
        .select('id, nome, email, telefone')
        .in('id', beneficiarioIds);

      if (beneficiariosData) {
        beneficiariosMap = new Map(beneficiariosData.map(b => [b.id, b]));
      }
    }

    // Buscar responsáveis (profiles)
    if (responsavelIds.length > 0) {
      const { data: responsaveisData } = await supabase
        .from('profiles')
        .select('id, nome, email, foto_url')
        .in('id', responsavelIds);

      if (responsaveisData) {
        responsaveisMap = new Map(responsaveisData.map(r => [r.id, r]));
      }
    }

    // Buscar tribunais
    if (tribunalIds.length > 0) {
      const { data: tribunaisData } = await supabase
        .from('tribunais')
        .select('id, nome')
        .in('id', tribunalIds);

      if (tribunaisData) {
        tribunaisMap = new Map(tribunaisData.map(t => [t.id, t]));
      }
    }

    // Transformar os dados para o formato esperado pelo componente
    const precatorios: Precatorio[] = (precatoriosData || []).map((precatorio) => {
      try {
        // Obter entidades relacionadas dos mapas
        const beneficiario = beneficiariosMap.get(precatorio.beneficiario_id);
        const responsavel = responsaveisMap.get(precatorio.responsavel_id);
        const tribunal = tribunaisMap.get(precatorio.tribunal_id);

        // Garantir que o tipo esteja definido corretamente
        let tipo = precatorio.tipo;
        if (!tipo) {
          tipo = 'PRECATORIO';
        } else if (tipo !== 'PRECATORIO' && tipo !== 'RPV') {
          tipo = tipo.toUpperCase() === 'RPV' ? 'RPV' : 'PRECATORIO';
        }

        // Verificar se o status_id está definido, caso contrário, tentar encontrar um status padrão
        if (!precatorio.status_id && precatorio.status) {
          console.log(`Precatório ${precatorio.id} sem status_id, mas com status: ${precatorio.status}`);

          // Tentar encontrar o status_id correspondente ao status
          const statusEncontrado = todosStatus?.find(s =>
            s.codigo === precatorio.status ||
            s.nome === precatorio.status ||
            removerAcentos(s.codigo.toLowerCase()) === removerAcentos(precatorio.status.toLowerCase()) ||
            removerAcentos(s.nome.toLowerCase()) === removerAcentos(precatorio.status.toLowerCase())
          );

          if (statusEncontrado) {
            precatorio.status_id = statusEncontrado.id;
            console.log(`Status ID encontrado para ${precatorio.status}: ${precatorio.status_id}`);
          } else {
            // Se não encontrou, usar o status padrão
            const statusPadrao = todosStatus?.find(s => s.is_default) || todosStatus?.[0];
            if (statusPadrao) {
              precatorio.status_id = statusPadrao.id;
              console.log(`Usando status padrão para ${precatorio.id}: ${statusPadrao.nome} (${statusPadrao.id})`);
            }
          }
        }

        // Garantir que a data de vencimento seja válida
        let dataVencimento = precatorio.data_previsao_pagamento;
        if (dataVencimento) {
          try {
            // Verificar se a data é válida
            const dataObj = new Date(dataVencimento);
            if (isNaN(dataObj.getTime())) {
              console.warn(`Data de vencimento inválida para precatório ${precatorio.id}: ${dataVencimento}`);
              dataVencimento = null;
            }
          } catch (e) {
            console.warn(`Erro ao processar data de vencimento para precatório ${precatorio.id}: ${e}`);
            dataVencimento = null;
          }
        }

        // Mapear para o formato Precatorio esperado pelo componente
        return {
          id: precatorio.id,
          numero: precatorio.numero_precatorio || `PR-${precatorio.id.substring(0, 4)}`,
          numero_precatorio: precatorio.numero_precatorio,
          valor: parseFloat(String(precatorio.valor_total || 0)),
          valor_total: precatorio.valor_total,
          desconto: parseFloat(String(precatorio.desconto || 0)),
          status: precatorio.status || 'analise',
          status_id: precatorio.status_id, // Importante: Incluir o status_id para o kanban
          cliente: {
            nome: beneficiario?.nome || 'Cliente não identificado',
            avatar: '',
            email: beneficiario?.email || '',
            telefone: beneficiario?.telefone || ''
          },
          responsavel: {
            nome: responsavel?.nome || 'Não atribuído',
            avatar: responsavel?.foto_url || '',
            cargo: 'Responsável'
          },
          dataCriacao: precatorio.created_at || new Date().toISOString(),
          dataAtualizacao: precatorio.updated_at || new Date().toISOString(),
          dataVencimento: dataVencimento || null,
          tribunal: tribunal?.nome || 'Tribunal não especificado',
          natureza: precatorio.natureza || '',
          documentos: [],
          historico: [],
          observacoes: precatorio.observacoes || '',
          tags: Array.isArray(precatorio.tags) ? precatorio.tags : [],
          prioridade: (precatorio.prioridade as any) || 'media',
          beneficiario_id: precatorio.beneficiario_id,
          tribunal_id: precatorio.tribunal_id,
          responsavel_id: precatorio.responsavel_id,
          tipo: tipo
        };
      } catch (mapError) {
        console.error(`[KanbanPrecatoriosService] Erro ao mapear precatório ${precatorio?.id || 'desconhecido'}:`, mapError);

        // Retornar um objeto mínimo para evitar falhas no componente
        return {
          id: precatorio?.id || `fallback-${Math.random().toString(36).substring(2, 9)}`,
          numero: precatorio?.numero_precatorio || 'Erro',
          numero_precatorio: precatorio?.numero_precatorio,
          valor: 0,
          valor_total: 0,
          desconto: 0,
          status: 'erro',
          status_id: null,
          cliente: { nome: 'Erro ao carregar', avatar: '', email: '', telefone: '' },
          responsavel: { nome: 'Erro', avatar: '', cargo: '' },
          dataCriacao: new Date().toISOString(),
          dataAtualizacao: new Date().toISOString(),
          dataVencimento: null,
          tribunal: 'Erro',
          natureza: '',
          documentos: [],
          historico: [],
          observacoes: 'Erro ao carregar dados',
          tags: [],
          prioridade: 'baixa',
          beneficiario_id: null,
          tribunal_id: null,
          responsavel_id: null,
          tipo: 'PRECATORIO'
        };
      }
    });

    return precatorios;
  } catch (error) {
    console.error('Erro na função buscarPrecatoriosParaKanban:', error);
    throw error;
  }
}

// Função para criar colunas personalizadas padrão se não existirem
async function criarColunasPersonalizadasPadrao(): Promise<void> {
  try {
    console.log('[KanbanPrecatoriosService] Verificando se é necessário criar status padrão...');

    // Verificar se já existem status ativos
    const statusList = await buscarTodosStatus(true);

    // Se já existem status, verificar se estão configurados como colunas de kanban
    if (statusList && statusList.length > 0) {
      console.log(`[KanbanPrecatoriosService] Encontrados ${statusList.length} status existentes.`);

      // Verificar quais status não estão marcados como colunas de kanban
      const statusSemColuna = statusList.filter(status => !status.kanban_coluna);

      if (statusSemColuna.length > 0) {
        console.log(`[KanbanPrecatoriosService] Atualizando ${statusSemColuna.length} status para serem colunas de kanban.`);

        // Atualizar status para serem colunas de kanban
        for (const status of statusSemColuna) {
          await supabase
            .from('status_precatorios')
            .update({
              kanban_coluna: true,
              visivel: true,
              updated_at: new Date().toISOString()
            })
            .eq('id', status.id);
        }
      }

      return;
    }

    console.log('[KanbanPrecatoriosService] Nenhum status encontrado. Criando status padrão...');

    // Definir status padrão
    const statusPadrao = [
      { nome: 'Novo', codigo: 'novo', cor: '#3b82f6', ordem: 1, is_default: true },
      { nome: 'Em Análise', codigo: 'analise', cor: '#8b5cf6', ordem: 2 },
      { nome: 'Em Andamento', codigo: 'em_andamento', cor: '#10b981', ordem: 3 },
      { nome: 'Aguardando', codigo: 'aguardando', cor: '#f59e0b', ordem: 4 },
      { nome: 'Concluído', codigo: 'concluido', cor: '#6366f1', ordem: 5 }
    ];

    // Obter o usuário atual para definir como criador dos status
    const { data: userData } = await supabase.auth.getUser();
    const userId = userData?.user?.id;

    // Criar cada status padrão
    for (const status of statusPadrao) {
      try {
        const novoStatus = {
          nome: status.nome,
          codigo: status.codigo,
          cor: status.cor,
          is_default: status.is_default || false,
          is_system: true,
          ativo: true,
          ordem: status.ordem,
          visivel: true,
          kanban_coluna: true,
          created_by: userId,
          created_at: new Date().toISOString()
        };

        const { data, error } = await supabase
          .from('status_precatorios')
          .insert(novoStatus)
          .select()
          .single();

        if (error) {
          console.error(`[KanbanPrecatoriosService] Erro ao criar status "${status.nome}":`, error);
        } else if (data) {
          console.log(`[KanbanPrecatoriosService] Status "${status.nome}" criado com ID ${data.id}`);
        }
      } catch (error) {
        console.error(`[KanbanPrecatoriosService] Erro ao criar status "${status.nome}":`, error);
      }
    }
    console.log('[KanbanPrecatoriosService] Criação de status padrão concluída.');
  } catch (error) {
    console.error('[KanbanPrecatoriosService] Erro ao criar status padrão:', error);
  }
}

// Função auxiliar para remover acentos
function removerAcentos(texto: string): string {
  return texto.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
}

// Função para buscar todas as colunas do kanban
export async function buscarColunasKanban(): Promise<KanbanColuna[]> {
  try {
    console.log('[KanbanPrecatoriosService] Iniciando busca de colunas do kanban...');

    // Verificar se a sessão está válida antes de buscar os dados
    try {
      const { data: sessionData } = await supabase.auth.getSession();
      if (!sessionData.session) {
        console.warn('[KanbanPrecatoriosService] Sessão não encontrada ao buscar colunas, tentando continuar...');
        // Continuar mesmo sem sessão
      }
    } catch (sessionError) {
      console.warn('[KanbanPrecatoriosService] Erro ao verificar sessão para colunas:', sessionError);
      // Continuar mesmo com erro
    }

    // Verificar se existem colunas personalizadas primeiro
    const { data: colunasPersonalizadas, error: colunasError } = await supabase
      .from('kanban_colunas_personalizadas')
      .select('*')
      .eq('is_deleted', false)
      .order('ordem');

    // Se existem colunas personalizadas, usá-las
    if (!colunasError && colunasPersonalizadas && colunasPersonalizadas.length > 0) {
      console.log(`[KanbanPrecatoriosService] Encontradas ${colunasPersonalizadas.length} colunas personalizadas`);

      // Buscar os status correspondentes para obter informações completas
      const statusIds = colunasPersonalizadas
        .filter(c => c.status_id)
        .map(c => c.status_id);

      if (statusIds.length > 0) {
        const { data: statusList } = await supabase
          .from('status_precatorios')
          .select('*')
          .in('id', statusIds);

        // Criar um mapa de status para facilitar o acesso
        const statusMap = new Map();
        if (statusList) {
          statusList.forEach(s => statusMap.set(s.id, s));
        }

        // Mapear colunas personalizadas para o formato esperado
        const colunas = await Promise.all(colunasPersonalizadas.map(async coluna => {
          const status = statusMap.get(coluna.status_id);

          return {
            id: coluna.id,
            nome: coluna.nome,
            name: coluna.nome,
            cor: coluna.cor,
            color: coluna.cor,
            tipo: 'AMBOS',
            ordem: coluna.ordem,
            status_id: status?.codigo || coluna.codigo,
            codigo: status?.codigo || coluna.codigo,
            status_uuid: coluna.status_id,
            status_ref_id: coluna.status_id,
            ativo: true,
            is_default: coluna.is_default,
            is_system: coluna.is_system,
            status: status ? {
              id: status.id,
              nome: status.nome,
              codigo: status.codigo,
              cor: status.cor
            } : undefined
          };
        }));

        // Adicionar contagem de precatórios
        return await adicionarContagemPrecatorios(colunas);
      }
    }

    // Se não encontrou colunas personalizadas, buscar status diretamente
    console.log('[KanbanPrecatoriosService] Buscando status para colunas do kanban...');

    // Buscar todos os status ativos
    const { data: statusList, error: statusError } = await supabase
      .from('status_precatorios')
      .select('*')
      .eq('ativo', true)
      .order('ordem');

    if (statusError || !statusList || statusList.length === 0) {
      console.warn('[KanbanPrecatoriosService] Erro ao buscar status ou nenhum status encontrado, criando status padrão');

      // Criar status padrão se não existirem
      try {
        await criarColunasPersonalizadasPadrao();

        // Buscar novamente após criar os status padrão
        const { data: newStatusList } = await supabase
          .from('status_precatorios')
          .select('*')
          .eq('ativo', true)
          .order('ordem');

        if (newStatusList && newStatusList.length > 0) {
          console.log(`[KanbanPrecatoriosService] ${newStatusList.length} status padrão criados com sucesso`);

          // Mapear para o formato de colunas
          const colunas = await mapearStatusParaColunas(newStatusList);
          return colunas;
        }
      } catch (createError) {
        console.error('[KanbanPrecatoriosService] Erro ao criar status padrão:', createError);
      }

      // Se ainda falhar, retornar colunas padrão em memória
      return getColunasDefault();
    }

    console.log(`[KanbanPrecatoriosService] Encontrados ${statusList.length} status para colunas do kanban`);

    // Mapear status para colunas do Kanban
    const colunas = await mapearStatusParaColunas(statusList);

    return colunas;
  } catch (error) {
    console.error('[KanbanPrecatoriosService] Erro na função buscarColunasKanban:', error);
    // Em caso de erro, retornar colunas padrão
    return getColunasDefault();
  }
}

// Função auxiliar para adicionar contagem de precatórios às colunas
async function adicionarContagemPrecatorios(colunas: KanbanColuna[]): Promise<KanbanColuna[]> {
  try {
    // Buscar todos os precatórios para contagem
    const { data: precatoriosData } = await supabase
      .from('precatorios')
      .select('id, status_id, status')
      .eq('is_deleted', false);

    if (!precatoriosData || precatoriosData.length === 0) {
      return colunas; // Retornar colunas sem contagem
    }

    // Criar mapa de contagem
    const contagemPorStatus: Record<string, number> = {};

    // Contar precatórios por status_id e status
    precatoriosData.forEach(p => {
      if (p.status_id) {
        contagemPorStatus[p.status_id] = (contagemPorStatus[p.status_id] || 0) + 1;
      }
      if (p.status) {
        contagemPorStatus[p.status] = (contagemPorStatus[p.status] || 0) + 1;
      }
    });

    // Adicionar contagem às colunas
    return colunas.map(coluna => {
      // Buscar contagem por status_id, status_uuid, código ou nome
      const count =
        contagemPorStatus[coluna.status_id] ||
        contagemPorStatus[coluna.status_uuid] ||
        contagemPorStatus[coluna.codigo] ||
        0;

      return { ...coluna, count };
    });
  } catch (error) {
    console.error('[KanbanPrecatoriosService] Erro ao adicionar contagem de precatórios:', error);
    return colunas; // Retornar colunas originais em caso de erro
  }
}

// Função auxiliar para mapear status para colunas
async function mapearStatusParaColunas(statusList: any[]): Promise<KanbanColuna[]> {
  console.log('[KanbanPrecatoriosService] Mapeando status para colunas do Kanban...');

  // Buscar contagem de precatórios por status_id de forma síncrona para ter contagens precisas
  let contagemPorStatus: Record<string, number> = {};

  try {
    const { data: precatoriosData, error } = await supabase
      .from('precatorios')
      .select('id, status_id, status')
      .eq('is_deleted', false);

    if (error) {
      console.warn('[KanbanPrecatoriosService] Erro ao buscar contagem de precatórios:', error);
    } else if (precatoriosData && precatoriosData.length > 0) {
      console.log(`[KanbanPrecatoriosService] Encontrados ${precatoriosData.length} precatórios para contagem`);

      // Contar precatórios por status_id
      precatoriosData.forEach(p => {
        // Usar status_id se disponível, caso contrário usar status
        const statusKey = p.status_id || p.status;
        if (statusKey) {
          contagemPorStatus[statusKey] = (contagemPorStatus[statusKey] || 0) + 1;
        }
      });

      console.log('[KanbanPrecatoriosService] Contagem por status:', contagemPorStatus);
    }
  } catch (err) {
    console.warn('[KanbanPrecatoriosService] Erro ao buscar contagem de precatórios:', err);
  }

  // Mapear status para colunas do Kanban com contagem
  const colunas: KanbanColuna[] = statusList.map(status => {
    // Determinar a contagem para este status
    const count = contagemPorStatus[status.id] ||
                 contagemPorStatus[status.codigo] ||
                 0;

    // Criar objeto de coluna com todos os campos necessários
    return {
      id: status.id,
      nome: status.nome,
      name: status.nome,
      cor: status.cor,
      color: status.cor,
      tipo: status.tipo || 'AMBOS',
      ordem: status.ordem || 0,
      status_id: status.codigo, // Usar o código como status_id para compatibilidade
      codigo: status.codigo,
      status_uuid: status.id, // UUID do status para referência
      status_ref_id: status.id, // Referência adicional
      count: count, // Adicionar contagem
      ativo: status.ativo,
      is_default: status.is_default || false,
      is_system: status.is_system || false,
      status: { // Informações do status relacionado
        id: status.id,
        nome: status.nome,
        codigo: status.codigo,
        cor: status.cor
      }
    };
  });

  // Ordenar colunas por ordem
  colunas.sort((a, b) => a.ordem - b.ordem);

  console.log('[KanbanPrecatoriosService] Colunas mapeadas para o Kanban:',
    colunas.map(c => ({ id: c.id, nome: c.nome, count: c.count }))
  );

  return colunas;
}

// Função para atualizar o status de um precatório
export async function atualizarStatusPrecatorio(precatorioId: string, novoStatusId: string): Promise<boolean> {
  try {
    console.log(`[KanbanPrecatoriosService] Atualizando status do precatório ${precatorioId} para ${novoStatusId}`);

    // Verificar se o precatório existe
    const { data: precatorio, error: precatorioError } = await supabase
      .from('precatorios')
      .select('id, status, status_id')
      .eq('id', precatorioId)
      .single();

    if (precatorioError || !precatorio) {
      console.error(`[KanbanPrecatoriosService] Erro ao buscar precatório ${precatorioId}:`, precatorioError);
      return false;
    }

    // Verificar se o status existe
    const { data: status, error: statusError } = await supabase
      .from('status_precatorios')
      .select('id, codigo, nome')
      .eq('id', novoStatusId)
      .single();

    if (statusError) {
      // Se não encontrou pelo ID, tentar buscar pelo código
      const { data: statusPorCodigo, error: statusCodigoError } = await supabase
        .from('status_precatorios')
        .select('id, codigo, nome')
        .eq('codigo', novoStatusId)
        .single();

      if (statusCodigoError || !statusPorCodigo) {
        console.error(`[KanbanPrecatoriosService] Status ${novoStatusId} não encontrado:`, statusCodigoError);
        return false;
      }

      // Usar o status encontrado pelo código
      console.log(`[KanbanPrecatoriosService] Status encontrado pelo código: ${statusPorCodigo.nome} (${statusPorCodigo.id})`);

      // Atualizar o precatório com o novo status
      const { error: updateError } = await supabase
        .from('precatorios')
        .update({
          status_id: statusPorCodigo.id,
          status: statusPorCodigo.codigo,
          updated_at: new Date().toISOString()
        })
        .eq('id', precatorioId);

      if (updateError) {
        console.error(`[KanbanPrecatoriosService] Erro ao atualizar precatório ${precatorioId}:`, updateError);
        return false;
      }

      console.log(`[KanbanPrecatoriosService] Precatório ${precatorioId} atualizado com sucesso para status ${statusPorCodigo.nome}`);
      return true;
    }

    // Atualizar o precatório com o novo status
    const { error: updateError } = await supabase
      .from('precatorios')
      .update({
        status_id: status.id,
        status: status.codigo,
        updated_at: new Date().toISOString()
      })
      .eq('id', precatorioId);

    if (updateError) {
      console.error(`[KanbanPrecatoriosService] Erro ao atualizar precatório ${precatorioId}:`, updateError);
      return false;
    }

    console.log(`[KanbanPrecatoriosService] Precatório ${precatorioId} atualizado com sucesso para status ${status.nome}`);

    // Registrar a movimentação no histórico (opcional)
    try {
      await supabase
        .from('kanban_activity_logs')
        .insert({
          precatorio_id: precatorioId,
          status_anterior_id: precatorio.status_id,
          status_anterior: precatorio.status,
          status_novo_id: status.id,
          status_novo: status.codigo,
          created_at: new Date().toISOString()
        });
    } catch (logError) {
      console.warn('[KanbanPrecatoriosService] Erro ao registrar histórico de movimentação:', logError);
      // Não falhar a operação principal se o log falhar
    }

    return true;
  } catch (error) {
    console.error('[KanbanPrecatoriosService] Erro ao atualizar status do precatório:', error);
    return false;
  }
}

// Função para obter colunas padrão em memória
function getColunasDefault(): KanbanColuna[] {
  return [
    {
      id: 'default-1',
      nome: 'Novo',
      name: 'Novo',
      cor: '#3b82f6',
      color: '#3b82f6',
      tipo: 'AMBOS',
      ordem: 1,
      status_id: 'novo',
      codigo: 'novo',
      status_uuid: null,
      status_ref_id: null,
      ativo: true,
      is_default: true,
      is_system: true
    },
    {
      id: 'default-2',
      nome: 'Em Análise',
      name: 'Em Análise',
      cor: '#8b5cf6',
      color: '#8b5cf6',
      tipo: 'AMBOS',
      ordem: 2,
      status_id: 'analise',
      codigo: 'analise',
      status_uuid: null,
      status_ref_id: null,
      ativo: true,
      is_default: true,
      is_system: true
    },
    {
      id: 'default-3',
      nome: 'Em Andamento',
      name: 'Em Andamento',
      cor: '#10b981',
      color: '#10b981',
      tipo: 'AMBOS',
      ordem: 3,
      status_id: 'em_andamento',
      codigo: 'em_andamento',
      status_uuid: null,
      status_ref_id: null,
      ativo: true,
      is_default: true,
      is_system: true
    },
    {
      id: 'default-4',
      nome: 'Aguardando',
      name: 'Aguardando',
      cor: '#f59e0b',
      color: '#f59e0b',
      tipo: 'AMBOS',
      ordem: 4,
      status_id: 'aguardando',
      codigo: 'aguardando',
      status_uuid: null,
      status_ref_id: null,
      ativo: true,
      is_default: true,
      is_system: true
    },
    {
      id: 'default-5',
      nome: 'Concluído',
      name: 'Concluído',
      cor: '#6366f1',
      color: '#6366f1',
      tipo: 'AMBOS',
      ordem: 5,
      status_id: 'concluido',
      codigo: 'concluido',
      status_uuid: null,
      status_ref_id: null,
      ativo: true,
      is_default: true,
      is_system: true
    }
  ];
}
