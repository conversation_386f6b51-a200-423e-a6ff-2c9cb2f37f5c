import { supabase, refreshSupabaseSession, getCachedData, clearSupabaseCache } from '@/lib/supabase';
import { Evento } from '@/utils/calendarHelpers';
import { v4 as uuidv4 } from 'uuid';
import { ensureValidSession } from '@/lib/auth-helpers';

// Função utilitária para validar se uma string é um UUID válido
const isValidUUID = (str: string): boolean => {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(str);
};

// Interface para o evento como armazenado no banco de dados
interface EventoDB {
  id: string;
  tipo: string;
  titulo: string;
  descricao: string | null;
  data: string;
  hora: string;
  duracao: number;
  status: string;
  prioridade: string;
  local: string | null;
  responsavel: string | null;
  tipo_entidade: string | null;
  entidade_id: string | null;
  created_at: string;
  updated_at: string;
  created_by: string | null;
  updated_by: string | null;
}

// Obter o ID do usuário atual
const getUserId = async (): Promise<string | null> => {
  try {
    // Verificar a sessão atual
    const { data: { session } } = await supabase.auth.getSession();

    if (!session || !session.user) {
      console.warn("getUserId: Nenhuma sessão ou usuário encontrado");
      // Se não há sessão, tentar renovar
      try {
        const { data: refreshData } = await supabase.auth.refreshSession();
        if (refreshData && refreshData.session && refreshData.session.user) {
          console.log("getUserId: Sessão renovada com sucesso");
          return refreshData.session.user.id;
        }
      } catch (refreshError) {
        console.error("getUserId: Erro ao tentar renovar sessão", refreshError);
      }
      return null;
    }

    return session.user.id;
  } catch (error) {
    console.error("getUserId: Erro ao obter ID do usuário", error);
    return null;
  }
};

// Converter de EventoDB para Evento (frontend)
const mapEventoDBToEvento = (eventoDB: EventoDB): Evento => {
  return {
    id: eventoDB.id,
    tipo: eventoDB.tipo,
    titulo: eventoDB.titulo,
    descricao: eventoDB.descricao || '',
    data: eventoDB.data,
    hora: eventoDB.hora,
    duracao: eventoDB.duracao,
    status: eventoDB.status,
    prioridade: eventoDB.prioridade,
    local: eventoDB.local || '',
    responsavel: eventoDB.responsavel || ''
  };
};

// Buscar todos os eventos
export const getEventos = async (): Promise<Evento[]> => {
  try {
    // Verificar autenticação
    const userId = await getUserId();
    if (!userId) {
      // Em vez de lançar um erro, tentamos verificar se há um token no localStorage
      console.log("getEventos: Usuário não autenticado, tentando recuperar token");

      // Verificar se o token supabase existe no localStorage
      const token = localStorage.getItem('sb-ubwzukpsqcrwzfbppoux-auth-token');
      if (!token) {
        console.error("getEventos: Token não encontrado no localStorage");
        return []; // Retornar array vazio ao invés de lançar erro
      }

      try {
        // Tentar renovar a sessão
        await supabase.auth.refreshSession();
        console.log("getEventos: Sessão renovada com sucesso");
      } catch (refreshError) {
        console.error("getEventos: Erro ao renovar sessão", refreshError);
        return []; // Retornar array vazio ao invés de lançar erro
      }
    }

    // Buscar eventos com autenticação renovada
    const { data, error } = await supabase
      .from('eventos_calendario')
      .select('*');

    if (error) {
      console.error('Erro ao buscar eventos:', error);
      // Retornar array vazio ao invés de lançar erro
      return [];
    }

    return (data as EventoDB[]).map(mapEventoDBToEvento);
  } catch (error) {
    console.error('Erro ao buscar eventos:', error);
    // Retornar array vazio ao invés de lançar erro
    return [];
  }
};

// Buscar eventos por intervalo de data com cache
export const getEventosPorPeriodo = async (dataInicio: string, dataFim: string): Promise<Evento[]> => {
  // Criar uma chave de cache baseada no período
  const cacheKey = `eventos_${dataInicio}_${dataFim}`;

  try {
    // Tentar usar cache primeiro para melhorar performance
    return await getCachedData<EventoDB[]>(
      cacheKey,
      async () => {
        try {
          // Verificar autenticação usando o método melhorado
          const authResult = await ensureValidSession();

          if (!authResult.success && !authResult.recoverable) {
            console.error("getEventosPorPeriodo: Erro crítico de autenticação");
            return { data: [], error: new Error("Erro crítico de autenticação") };
          }

          // Tentar buscar eventos mesmo com autenticação limitada
          console.log(`getEventosPorPeriodo: Buscando eventos de ${dataInicio} até ${dataFim}`);

          const { data, error } = await supabase
            .from('eventos_calendario')
            .select('*')
            .gte('data', dataInicio)
            .lte('data', dataFim);

          if (error) {
            // Se for erro de autenticação, tentar renovar sessão e buscar novamente
            if (error.code === "PGRST301" || error.message.includes("JWT") || error.message.includes("401")) {
              console.warn("getEventosPorPeriodo: Erro de autenticação, tentando renovar sessão");

              const { success } = await refreshSupabaseSession();
              if (success) {
                // Tentar novamente após renovar sessão
                const { data: retryData, error: retryError } = await supabase
                  .from('eventos_calendario')
                  .select('*')
                  .gte('data', dataInicio)
                  .lte('data', dataFim);

                if (retryError) {
                  console.error('getEventosPorPeriodo: Erro na segunda tentativa:', retryError);
                  return { data: [], error: retryError };
                }

                return { data: retryData as EventoDB[], error: null };
              }
            }

            console.error('getEventosPorPeriodo: Erro ao buscar eventos:', error);
            return { data: [], error };
          }

          return { data: data as EventoDB[], error: null };
        } catch (error) {
          console.error('getEventosPorPeriodo: Erro não tratado:', error);
          return { data: [], error };
        }
      },
      // Cache de 2 minutos para dados de eventos
      2 * 60 * 1000
    ).then(result => {
      if (result.data) {
        return result.data.map(mapEventoDBToEvento);
      }
      return [];
    });
  } catch (error) {
    console.error('getEventosPorPeriodo: Erro ao processar cache:', error);
    return [];
  }
};

// Buscar evento por ID
export const getEventoPorId = async (id: string): Promise<Evento | null> => {
  try {
    const { data, error } = await supabase
      .from('eventos_calendario')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Erro ao buscar evento:', error);
      throw new Error(`Erro ao buscar evento: ${error.message}`);
    }

    return mapEventoDBToEvento(data as EventoDB);
  } catch (error) {
    console.error('Erro ao buscar evento:', error);
    return null;
  }
};

// Interface para dados do formulário de evento
export interface EventoFormData {
  titulo: string;
  descricao?: string;
  dataInicio: Date;
  duracao: number;
  tipo?: string;
  status?: string;
  prioridade?: string;
  local?: string;
  responsavel?: string;
}

// Criar novo evento
export const criarEvento = async (eventoData: Partial<Evento>): Promise<Evento | null> => {
  try {
    // Limpar o cache de eventos ao criar um novo
    clearSupabaseCache('eventos_');

    // Verificar autenticação
    const authResult = await ensureValidSession();
    let userId = null;

    if (authResult.success && authResult.session) {
      userId = authResult.session.user.id;
    } else {
      // Tentar obter ID do localStorage como fallback
      const userProfileStr = localStorage.getItem("userProfile");
      if (userProfileStr) {
        try {
          const userProfile = JSON.parse(userProfileStr);
          userId = userProfile.id;
        } catch (e) {
          console.error('criarEvento: Erro ao analisar perfil do localStorage:', e);
        }
      }
    }

    // Criar objeto de evento
    const novoEvento: any = {
      id: uuidv4(),
      tipo: eventoData.tipo || 'reuniao',
      titulo: eventoData.titulo,
      descricao: eventoData.descricao || '',
      data: eventoData.data,
      hora: eventoData.hora,
      duracao: eventoData.duracao || 30,
      status: eventoData.status || 'pendente',
      prioridade: eventoData.prioridade || 'media',
      local: eventoData.local || '',
      responsavel: eventoData.responsavel || '',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // Adicionar usuário se disponível
    if (userId) {
      novoEvento.created_by = userId;
      novoEvento.updated_by = userId;
    }

    // Inserir no banco de dados
    const { data, error } = await supabase
      .from('eventos_calendario')
      .insert(novoEvento)
      .select();

    if (error) {
      console.error('Erro ao criar evento:', error);
      throw error;
    }

    if (!data || data.length === 0) {
      throw new Error('Nenhum dado retornado ao criar evento');
    }

    return mapEventoDBToEvento(data[0] as EventoDB);
  } catch (error) {
    console.error('Erro ao criar evento:', error);
    throw error;
  }
};

// Atualizar evento existente
export const atualizarEvento = async (id: string, evento: Partial<Evento>): Promise<Evento | null> => {
  try {
    // Limpar o cache de eventos ao atualizar
    clearSupabaseCache('eventos_');

    // Verificar autenticação usando o método melhorado
    const authResult = await ensureValidSession();

    // Obter ID do usuário de diferentes fontes
    let userId = null;

    // 1. Da sessão, se disponível
    if (authResult.success && authResult.session && authResult.session.user) {
      userId = authResult.session.user.id;
      console.log('atualizarEvento: Usando ID do usuário da sessão:', userId);
    }
    // 2. Tentar obter do localStorage
    else {
      console.warn('atualizarEvento: Sessão inválida ou limitada, tentando obter usuário do localStorage');
      const userProfileStr = localStorage.getItem("userProfile");
      if (userProfileStr) {
        try {
          const userProfile = JSON.parse(userProfileStr);
          userId = userProfile.id;
          console.log('atualizarEvento: Usando ID do usuário do localStorage:', userId);
        } catch (parseError) {
          console.error('atualizarEvento: Erro ao analisar perfil do localStorage:', parseError);
        }
      }
    }

    // Validar se o userId é um UUID válido
    if (userId && !isValidUUID(userId)) {
      console.warn(`atualizarEvento: ID do usuário '${userId}' não é um UUID válido, será definido como null`);
      userId = null;
    }

    const now = new Date().toISOString();

    // Preparar objeto para atualização
    const eventoParaAtualizar: any = {
      ...evento,
      updated_at: now,
    };

    // Adicionar updated_by apenas se tivermos um ID válido
    if (userId) {
      eventoParaAtualizar.updated_by = userId;
    }

    console.log('atualizarEvento: Tentando atualizar evento com ID:', id);

    // Verificar se o ID é válido
    if (!id || !isValidUUID(id)) {
      throw new Error(`ID de evento inválido: ${id}`);
    }

    try {
      // Primeira tentativa: atualização direta
      const { data, error } = await supabase
        .from('eventos_calendario')
        .update(eventoParaAtualizar)
        .eq('id', id)
        .select();

      if (error) {
        // Se for erro de política de segurança, tentar abordagem alternativa
        if (error.code === '42501' || error.message.includes('row-level security policy')) {
          console.warn('atualizarEvento: Erro de política de segurança, tentando abordagem alternativa...');

          // Tentar usar upsert como alternativa
          try {
            // Primeiro, buscar o evento existente para manter campos não alterados
            const { data: existingData } = await supabase
              .from('eventos_calendario')
              .select('*')
              .eq('id', id)
              .single();

            if (existingData) {
              // Mesclar dados existentes com atualizações
              const eventoCompleto = {
                ...existingData,
                ...eventoParaAtualizar
              };

              // Usar upsert para atualizar
              const { data: upsertData, error: upsertError } = await supabase
                .from('eventos_calendario')
                .upsert(eventoCompleto)
                .select();

              if (upsertError) {
                console.error('atualizarEvento: Erro ao usar upsert:', upsertError);
                throw new Error(`Erro ao atualizar evento via upsert: ${upsertError.message}`);
              }

              if (upsertData && upsertData.length > 0) {
                console.log('atualizarEvento: Evento atualizado com sucesso via upsert');
                return mapEventoDBToEvento(upsertData[0] as EventoDB);
              }
            } else {
              console.error('atualizarEvento: Evento não encontrado para atualização alternativa');
              throw new Error('Evento não encontrado para atualização');
            }
          } catch (alternativeError) {
            console.error('atualizarEvento: Erro na abordagem alternativa:', alternativeError);
            throw alternativeError;
          }
        }

        // Outros erros
        console.error('atualizarEvento: Erro ao atualizar evento:', error);
        throw new Error(`Erro ao atualizar evento: ${error.message}`);
      }

      if (!data || data.length === 0) {
        throw new Error('Nenhum dado retornado ao atualizar evento');
      }

      return mapEventoDBToEvento(data[0] as EventoDB);
    } catch (updateError) {
      console.error('atualizarEvento: Erro na atualização:', updateError);
      throw updateError;
    }
  } catch (error) {
    console.error('atualizarEvento: Erro ao atualizar evento:', error);
    throw error; // Propagar o erro para ser tratado pelo componente
  }
};

// Excluir evento
export const excluirEvento = async (id: string): Promise<boolean> => {
  try {
    // Limpar o cache de eventos ao excluir
    clearSupabaseCache('eventos_');

    // Verificar se o ID é válido
    if (!id || !isValidUUID(id)) {
      throw new Error(`ID de evento inválido para exclusão: ${id}`);
    }

    // Verificar autenticação usando o método melhorado
    const authResult = await ensureValidSession();
    if (!authResult.success) {
      console.warn('excluirEvento: Problema de autenticação, tentando mesmo assim');
    }

    console.log('excluirEvento: Tentando excluir evento:', id);

    const { error } = await supabase
      .from('eventos_calendario')
      .delete()
      .eq('id', id);

    if (error) {
      // Se for erro de política de segurança, tentar abordagem alternativa
      if (error.code === '42501' || error.message.includes('row-level security policy')) {
        console.warn('excluirEvento: Erro de política de segurança, tentando abordagem alternativa...');

        // Tentar marcar como excluído em vez de excluir fisicamente
        try {
          const now = new Date().toISOString();
          const { error: updateError } = await supabase
            .from('eventos_calendario')
            .update({
              status: 'cancelado',
              updated_at: now,
              descricao: '[EXCLUÍDO] ' + (await getEventoPorId(id))?.descricao || ''
            })
            .eq('id', id);

          if (updateError) {
            console.error('excluirEvento: Erro na abordagem alternativa:', updateError);
            throw new Error(`Erro ao marcar evento como excluído: ${updateError.message}`);
          }

          console.log('excluirEvento: Evento marcado como cancelado com sucesso');
          return true;
        } catch (alternativeError) {
          console.error('excluirEvento: Erro na abordagem alternativa:', alternativeError);
          throw alternativeError;
        }
      }

      console.error('excluirEvento: Erro ao excluir evento:', error);
      throw new Error(`Erro ao excluir evento: ${error.message}`);
    }

    console.log('excluirEvento: Evento excluído com sucesso');
    return true;
  } catch (error) {
    console.error('excluirEvento: Erro ao excluir evento:', error);
    throw error; // Propagar o erro para ser tratado pelo componente
  }
};