import { supabase } from "@/lib/supabase";

export type DataVisibilityType = 
  | 'own_only'           // Apenas dados próprios
  | 'team_only'          // Apenas dados da equipe
  | 'department_only'    // Apenas dados do departamento
  | 'role_based'         // Baseado no cargo/role
  | 'specific_users'     // Usuários específicos
  | 'all_data'           // Todos os dados
  | 'custom_filter';     // Filtro customizado

export interface DataVisibilityConfig {
  id?: string;
  user_id: string;
  resource_type: string;
  visibility_type: DataVisibilityType;
  allowed_user_ids: string[];
  allowed_roles: string[];
  allowed_departments: string[];
  custom_filter_conditions?: any;
  can_view_sensitive_data: boolean;
  can_export_data: boolean;
  can_view_financial_data: boolean;
  can_view_personal_data: boolean;
  created_at?: string;
  updated_at?: string;
  created_by?: string;
}

export interface CustomDataFilter {
  id?: string;
  user_id: string;
  filter_name: string;
  resource_type: string;
  filter_conditions: any;
  is_default: boolean;
  is_shared: boolean;
  shared_with_users: string[];
  shared_with_roles: string[];
  created_at?: string;
  updated_at?: string;
}

export class DataVisibilityService {
  /**
   * Obter configurações de visibilidade de dados para um usuário
   */
  static async getUserDataVisibility(userId: string, resourceType?: string): Promise<DataVisibilityConfig[]> {
    try {
      const { data, error } = await supabase.rpc('get_user_data_visibility', {
        p_user_id: userId,
        p_resource_type: resourceType
      });

      if (error) {
        console.error('Erro ao obter configurações de visibilidade:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Erro no serviço de visibilidade:', error);
      throw error;
    }
  }

  /**
   * Verificar se um usuário pode ver dados específicos
   */
  static async canUserViewData(
    userId: string, 
    resourceType: string, 
    resourceOwnerId?: string, 
    resourceMetadata?: any
  ): Promise<boolean> {
    try {
      const { data, error } = await supabase.rpc('can_user_view_data', {
        p_user_id: userId,
        p_resource_type: resourceType,
        p_resource_owner_id: resourceOwnerId,
        p_resource_metadata: resourceMetadata
      });

      if (error) {
        console.error('Erro ao verificar permissão de visualização:', error);
        return false;
      }

      return data === true;
    } catch (error) {
      console.error('Erro no serviço de verificação de permissão:', error);
      return false;
    }
  }

  /**
   * Salvar configuração de visibilidade de dados
   */
  static async saveDataVisibilityConfig(config: DataVisibilityConfig): Promise<DataVisibilityConfig> {
    try {
      const { data, error } = await supabase
        .from('user_data_visibility')
        .upsert({
          user_id: config.user_id,
          resource_type: config.resource_type,
          visibility_type: config.visibility_type,
          allowed_user_ids: config.allowed_user_ids,
          allowed_roles: config.allowed_roles,
          allowed_departments: config.allowed_departments,
          custom_filter_conditions: config.custom_filter_conditions,
          can_view_sensitive_data: config.can_view_sensitive_data,
          can_export_data: config.can_export_data,
          can_view_financial_data: config.can_view_financial_data,
          can_view_personal_data: config.can_view_personal_data,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'user_id,resource_type'
        })
        .select()
        .single();

      if (error) {
        console.error('Erro ao salvar configuração de visibilidade:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Erro no serviço de salvamento:', error);
      throw error;
    }
  }

  /**
   * Aplicar configurações padrão de visibilidade para um usuário
   */
  static async applyDefaultDataVisibility(userId: string, role: string): Promise<void> {
    try {
      const { error } = await supabase.rpc('apply_default_data_visibility', {
        p_user_id: userId,
        p_role: role
      });

      if (error) {
        console.error('Erro ao aplicar configurações padrão:', error);
        throw error;
      }
    } catch (error) {
      console.error('Erro no serviço de configurações padrão:', error);
      throw error;
    }
  }

  /**
   * Criar filtro personalizado
   */
  static async createCustomFilter(filter: CustomDataFilter): Promise<CustomDataFilter> {
    try {
      const { data, error } = await supabase
        .from('custom_data_filters')
        .insert({
          user_id: filter.user_id,
          filter_name: filter.filter_name,
          resource_type: filter.resource_type,
          filter_conditions: filter.filter_conditions,
          is_default: filter.is_default,
          is_shared: filter.is_shared,
          shared_with_users: filter.shared_with_users,
          shared_with_roles: filter.shared_with_roles
        })
        .select()
        .single();

      if (error) {
        console.error('Erro ao criar filtro personalizado:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Erro no serviço de criação de filtro:', error);
      throw error;
    }
  }

  /**
   * Obter filtros personalizados de um usuário
   */
  static async getUserCustomFilters(userId: string, resourceType?: string): Promise<CustomDataFilter[]> {
    try {
      let query = supabase
        .from('custom_data_filters')
        .select('*')
        .eq('user_id', userId);

      if (resourceType) {
        query = query.eq('resource_type', resourceType);
      }

      const { data, error } = await query.order('filter_name');

      if (error) {
        console.error('Erro ao obter filtros personalizados:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Erro no serviço de filtros personalizados:', error);
      throw error;
    }
  }

  /**
   * Atualizar filtro personalizado
   */
  static async updateCustomFilter(filterId: string, updates: Partial<CustomDataFilter>): Promise<CustomDataFilter> {
    try {
      const { data, error } = await supabase
        .from('custom_data_filters')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', filterId)
        .select()
        .single();

      if (error) {
        console.error('Erro ao atualizar filtro personalizado:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Erro no serviço de atualização de filtro:', error);
      throw error;
    }
  }

  /**
   * Excluir filtro personalizado
   */
  static async deleteCustomFilter(filterId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('custom_data_filters')
        .delete()
        .eq('id', filterId);

      if (error) {
        console.error('Erro ao excluir filtro personalizado:', error);
        throw error;
      }
    } catch (error) {
      console.error('Erro no serviço de exclusão de filtro:', error);
      throw error;
    }
  }

  /**
   * Obter usuários que um usuário pode visualizar dados
   */
  static async getVisibleUsers(userId: string, resourceType: string): Promise<string[]> {
    try {
      const configs = await this.getUserDataVisibility(userId, resourceType);
      const config = configs.find(c => c.resource_type === resourceType);

      if (!config) {
        return [userId]; // Apenas próprios dados por padrão
      }

      switch (config.visibility_type) {
        case 'own_only':
          return [userId];
        
        case 'specific_users':
          return [...config.allowed_user_ids, userId];
        
        case 'all_data':
          // Retornar todos os usuários do sistema
          const { data: allUsers, error } = await supabase
            .from('profiles')
            .select('id');
          
          if (error) {
            console.error('Erro ao obter todos os usuários:', error);
            return [userId];
          }
          
          return allUsers.map(u => u.id);
        
        case 'team_only':
        case 'department_only':
        case 'role_based':
          // Implementar lógica específica baseada no tipo
          // Por enquanto, retornar apenas o próprio usuário
          return [userId];
        
        default:
          return [userId];
      }
    } catch (error) {
      console.error('Erro ao obter usuários visíveis:', error);
      return [userId];
    }
  }

  /**
   * Verificar se um usuário pode exportar dados
   */
  static async canUserExportData(userId: string, resourceType: string): Promise<boolean> {
    try {
      const configs = await this.getUserDataVisibility(userId, resourceType);
      const config = configs.find(c => c.resource_type === resourceType);
      
      return config?.can_export_data || false;
    } catch (error) {
      console.error('Erro ao verificar permissão de exportação:', error);
      return false;
    }
  }

  /**
   * Verificar se um usuário pode ver dados sensíveis
   */
  static async canUserViewSensitiveData(userId: string, resourceType: string): Promise<boolean> {
    try {
      const configs = await this.getUserDataVisibility(userId, resourceType);
      const config = configs.find(c => c.resource_type === resourceType);
      
      return config?.can_view_sensitive_data || false;
    } catch (error) {
      console.error('Erro ao verificar permissão de dados sensíveis:', error);
      return false;
    }
  }

  /**
   * Verificar se um usuário pode ver dados financeiros
   */
  static async canUserViewFinancialData(userId: string, resourceType: string): Promise<boolean> {
    try {
      const configs = await this.getUserDataVisibility(userId, resourceType);
      const config = configs.find(c => c.resource_type === resourceType);
      
      return config?.can_view_financial_data || false;
    } catch (error) {
      console.error('Erro ao verificar permissão de dados financeiros:', error);
      return false;
    }
  }
}
