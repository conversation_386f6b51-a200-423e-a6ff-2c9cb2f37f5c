import { DataVisibilityService } from './dataVisibilityService';

export interface PermissionAuditResult {
  page: string;
  hasAccess: boolean;
  canView: boolean;
  canCreate: boolean;
  canEdit: boolean;
  canDelete: boolean;
  canExport: boolean;
  canViewSensitive: boolean;
  canViewFinancial: boolean;
  canViewPersonal: boolean;
  visibleUserIds: string[];
  issues: string[];
  recommendations: string[];
}

export interface UserPermissionSummary {
  userId: string;
  userEmail: string;
  userName: string;
  role: string;
  pagePermissions: PermissionAuditResult[];
  overallScore: number;
  criticalIssues: string[];
}

export class PermissionAuditService {
  private static readonly PAGES_CONFIG = {
    'dashboard': {
      name: 'Dashboard',
      requiresAuth: true,
      hasFinancialData: true,
      hasSensitiveData: false,
      hasPersonalData: false
    },
    'clientes': {
      name: '<PERSON>lient<PERSON>',
      requiresAuth: true,
      hasFinancialData: false,
      hasSensitiveData: true,
      hasPersonalData: true
    },
    'precatorios': {
      name: 'Precatórios',
      requiresAuth: true,
      hasFinancialData: true,
      hasSensitiveData: true,
      hasPersonalData: true
    },
    'rpv': {
      name: 'RPV',
      requiresAuth: true,
      hasFinancialData: true,
      hasSensitiveData: true,
      hasPersonalData: true
    },
    'tarefas': {
      name: 'Tarefas',
      requiresAuth: true,
      hasFinancialData: false,
      hasSensitiveData: false,
      hasPersonalData: false
    },
    'documentos': {
      name: 'Documentos',
      requiresAuth: true,
      hasFinancialData: false,
      hasSensitiveData: true,
      hasPersonalData: true
    },
    'usuarios': {
      name: 'Usuários',
      requiresAuth: true,
      hasFinancialData: false,
      hasSensitiveData: true,
      hasPersonalData: true,
      adminOnly: true
    },
    'configuracoes': {
      name: 'Configurações',
      requiresAuth: true,
      hasFinancialData: false,
      hasSensitiveData: false,
      hasPersonalData: true
    }
  };

  /**
   * Auditar permissões de um usuário para uma página específica
   */
  static async auditUserPagePermissions(
    userId: string,
    pageType: string,
    userRole: string = 'captador'
  ): Promise<PermissionAuditResult> {
    const pageConfig = this.PAGES_CONFIG[pageType as keyof typeof this.PAGES_CONFIG];
    const issues: string[] = [];
    const recommendations: string[] = [];

    if (!pageConfig) {
      return {
        page: pageType,
        hasAccess: false,
        canView: false,
        canCreate: false,
        canEdit: false,
        canDelete: false,
        canExport: false,
        canViewSensitive: false,
        canViewFinancial: false,
        canViewPersonal: false,
        visibleUserIds: [],
        issues: [`Página '${pageType}' não encontrada na configuração`],
        recommendations: ['Verificar se a página está corretamente configurada']
      };
    }

    try {
      // Verificar acesso básico
      let hasAccess = true;
      if (pageConfig.adminOnly && userRole !== 'admin') {
        hasAccess = false;
        issues.push(`Usuário não tem permissão de admin para acessar ${pageConfig.name}`);
      }

      // Verificar permissões específicas
      const [
        canViewData,
        canExport,
        canViewSensitive,
        canViewFinancial,
        canViewPersonal,
        visibleUserIds
      ] = await Promise.all([
        DataVisibilityService.canUserViewData(userId, pageType),
        DataVisibilityService.canUserExportData(userId, pageType),
        pageConfig.hasSensitiveData ? DataVisibilityService.canUserViewSensitiveData(userId, pageType) : Promise.resolve(true),
        pageConfig.hasFinancialData ? DataVisibilityService.canUserViewFinancialData(userId, pageType) : Promise.resolve(true),
        pageConfig.hasPersonalData ? DataVisibilityService.canUserViewPersonalData(userId, pageType) : Promise.resolve(true),
        DataVisibilityService.getVisibleUsers(userId, pageType)
      ]);

      // Verificar configurações de visibilidade
      const visibilityConfigs = await DataVisibilityService.getUserDataVisibility(userId, pageType);
      const config = visibilityConfigs.find(c => c.resource_type === pageType);

      // Analisar problemas potenciais
      if (!canViewData) {
        issues.push(`Usuário não pode visualizar dados de ${pageConfig.name}`);
        recommendations.push(`Configurar permissões de visualização para ${pageConfig.name}`);
      }

      if (pageConfig.hasFinancialData && !canViewFinancial) {
        recommendations.push(`Considerar dar acesso a dados financeiros em ${pageConfig.name} se necessário para o cargo`);
      }

      if (pageConfig.hasSensitiveData && !canViewSensitive) {
        recommendations.push(`Avaliar se o usuário precisa de acesso a dados sensíveis em ${pageConfig.name}`);
      }

      if (visibleUserIds.length === 1 && visibleUserIds[0] === userId) {
        recommendations.push(`Usuário só pode ver próprios dados em ${pageConfig.name} - considerar expandir se necessário`);
      }

      if (!config) {
        issues.push(`Nenhuma configuração de visibilidade encontrada para ${pageConfig.name}`);
        recommendations.push(`Aplicar configurações padrão de visibilidade para ${pageConfig.name}`);
      }

      return {
        page: pageType,
        hasAccess,
        canView: canViewData,
        canCreate: hasAccess && canViewData, // Simplificado - pode ser mais complexo
        canEdit: hasAccess && canViewData,   // Simplificado - pode ser mais complexo
        canDelete: hasAccess && userRole === 'admin', // Simplificado
        canExport,
        canViewSensitive,
        canViewFinancial,
        canViewPersonal,
        visibleUserIds,
        issues,
        recommendations
      };

    } catch (error) {
      console.error(`Erro ao auditar permissões para ${pageType}:`, error);
      return {
        page: pageType,
        hasAccess: false,
        canView: false,
        canCreate: false,
        canEdit: false,
        canDelete: false,
        canExport: false,
        canViewSensitive: false,
        canViewFinancial: false,
        canViewPersonal: false,
        visibleUserIds: [],
        issues: [`Erro ao verificar permissões: ${error instanceof Error ? error.message : 'Erro desconhecido'}`],
        recommendations: ['Verificar configuração do sistema de permissões']
      };
    }
  }

  /**
   * Auditar todas as permissões de um usuário
   */
  static async auditUserPermissions(
    userId: string,
    userEmail: string,
    userName: string,
    userRole: string
  ): Promise<UserPermissionSummary> {
    const pageTypes = Object.keys(this.PAGES_CONFIG);
    const pagePermissions: PermissionAuditResult[] = [];
    const criticalIssues: string[] = [];

    for (const pageType of pageTypes) {
      const result = await this.auditUserPagePermissions(userId, pageType, userRole);
      pagePermissions.push(result);

      // Identificar problemas críticos
      if (!result.hasAccess && !this.PAGES_CONFIG[pageType as keyof typeof this.PAGES_CONFIG].adminOnly) {
        criticalIssues.push(`Sem acesso à página ${result.page}`);
      }

      if (result.issues.length > 0) {
        criticalIssues.push(...result.issues);
      }
    }

    // Calcular score geral (0-100)
    const totalPages = pagePermissions.length;
    const accessiblePages = pagePermissions.filter(p => p.hasAccess).length;
    const pagesWithIssues = pagePermissions.filter(p => p.issues.length > 0).length;
    
    const overallScore = Math.round(
      ((accessiblePages / totalPages) * 70) + // 70% baseado no acesso
      (((totalPages - pagesWithIssues) / totalPages) * 30) // 30% baseado na ausência de problemas
    );

    return {
      userId,
      userEmail,
      userName,
      role: userRole,
      pagePermissions,
      overallScore,
      criticalIssues: [...new Set(criticalIssues)] // Remover duplicatas
    };
  }

  /**
   * Gerar relatório de auditoria para múltiplos usuários
   */
  static async generateAuditReport(users: Array<{
    id: string;
    email: string;
    nome?: string;
    role: string;
  }>): Promise<{
    summary: {
      totalUsers: number;
      usersWithIssues: number;
      averageScore: number;
      commonIssues: string[];
    };
    userSummaries: UserPermissionSummary[];
  }> {
    const userSummaries: UserPermissionSummary[] = [];

    for (const user of users) {
      const summary = await this.auditUserPermissions(
        user.id,
        user.email,
        user.nome || user.email,
        user.role
      );
      userSummaries.push(summary);
    }

    // Calcular estatísticas gerais
    const totalUsers = userSummaries.length;
    const usersWithIssues = userSummaries.filter(u => u.criticalIssues.length > 0).length;
    const averageScore = Math.round(
      userSummaries.reduce((sum, u) => sum + u.overallScore, 0) / totalUsers
    );

    // Identificar problemas comuns
    const allIssues = userSummaries.flatMap(u => u.criticalIssues);
    const issueCount = allIssues.reduce((acc, issue) => {
      acc[issue] = (acc[issue] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const commonIssues = Object.entries(issueCount)
      .filter(([_, count]) => count >= Math.ceil(totalUsers * 0.1)) // 10% ou mais dos usuários
      .sort(([_, a], [__, b]) => b - a)
      .slice(0, 5)
      .map(([issue]) => issue);

    return {
      summary: {
        totalUsers,
        usersWithIssues,
        averageScore,
        commonIssues
      },
      userSummaries
    };
  }

  /**
   * Aplicar correções automáticas para problemas comuns
   */
  static async applyAutomaticFixes(userId: string, userRole: string): Promise<{
    applied: string[];
    failed: string[];
  }> {
    const applied: string[] = [];
    const failed: string[] = [];

    try {
      // Aplicar configurações padrão de visibilidade se não existirem
      await DataVisibilityService.applyDefaultDataVisibility(userId, userRole);
      applied.push('Configurações padrão de visibilidade aplicadas');
    } catch (error) {
      failed.push(`Erro ao aplicar configurações padrão: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    }

    return { applied, failed };
  }
}
