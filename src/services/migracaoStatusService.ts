import { supabase } from '@/lib/supabase';
import { buscarStatusPadrao, buscarStatusPorCodigo } from './statusPrecatoriosService';

/**
 * Função para migrar precatórios sem status_id para usar o novo sistema de status
 */
export async function migrarPrecatoriosSemStatusId(): Promise<{ atualizados: number, total: number }> {
  try {
    console.log('[MigracaoStatusService] Iniciando migração de precatórios sem status_id...');

    // Buscar precatórios sem status_id
    const { data: precatoriosSemStatusId, error: errorPrecatorios } = await supabase
      .from('precatorios')
      .select('id, status')
      .is('status_id', null)
      .not('status', 'is', null);

    if (errorPrecatorios) {
      console.error('[MigracaoStatusService] Erro ao buscar precatórios sem status_id:', errorPrecatorios);
      return { atualizados: 0, total: 0 };
    }

    if (!precatoriosSemStatusId || precatoriosSemStatusId.length === 0) {
      console.log('[MigracaoStatusService] Não foram encontrados precatórios sem status_id.');
      return { atualizados: 0, total: 0 };
    }

    console.log(`[MigracaoStatusService] Encontrados ${precatoriosSemStatusId.length} precatórios sem status_id.`);

    // Buscar status padrão para casos onde não encontramos correspondência
    const statusPadrao = await buscarStatusPadrao();
    if (!statusPadrao) {
      console.error('[MigracaoStatusService] Não foi possível encontrar um status padrão para migração.');
      return { atualizados: 0, total: precatoriosSemStatusId.length };
    }

    // Atualizar cada precatório
    let atualizados = 0;
    for (const precatorio of precatoriosSemStatusId) {
      if (!precatorio.status) {
        // Se não tem status, usar o padrão
        const { error: updateError } = await supabase
          .from('precatorios')
          .update({ 
            status_id: statusPadrao.id,
            status: statusPadrao.codigo,
            updated_at: new Date().toISOString()
          })
          .eq('id', precatorio.id);

        if (!updateError) {
          atualizados++;
          console.log(`[MigracaoStatusService] Precatório ${precatorio.id} atualizado com status padrão.`);
        } else {
          console.error(`[MigracaoStatusService] Erro ao atualizar precatório ${precatorio.id}:`, updateError);
        }
        continue;
      }

      // Tentar encontrar o status correspondente pelo código
      const statusObj = await buscarStatusPorCodigo(precatorio.status);
      
      if (statusObj) {
        // Atualizar o precatório com o status_id encontrado
        const { error: updateError } = await supabase
          .from('precatorios')
          .update({ 
            status_id: statusObj.id,
            updated_at: new Date().toISOString()
          })
          .eq('id', precatorio.id);

        if (!updateError) {
          atualizados++;
          console.log(`[MigracaoStatusService] Precatório ${precatorio.id} atualizado com status_id ${statusObj.id}.`);
        } else {
          console.error(`[MigracaoStatusService] Erro ao atualizar precatório ${precatorio.id}:`, updateError);
        }
      } else {
        // Se não encontrou status correspondente, usar o padrão
        const { error: updateError } = await supabase
          .from('precatorios')
          .update({ 
            status_id: statusPadrao.id,
            status: statusPadrao.codigo,
            updated_at: new Date().toISOString()
          })
          .eq('id', precatorio.id);

        if (!updateError) {
          atualizados++;
          console.log(`[MigracaoStatusService] Precatório ${precatorio.id} atualizado com status padrão (não encontrou correspondência para "${precatorio.status}").`);
        } else {
          console.error(`[MigracaoStatusService] Erro ao atualizar precatório ${precatorio.id}:`, updateError);
        }
      }
    }

    console.log(`[MigracaoStatusService] Migração concluída. Atualizados ${atualizados} de ${precatoriosSemStatusId.length} precatórios.`);
    return { atualizados, total: precatoriosSemStatusId.length };
  } catch (error) {
    console.error('[MigracaoStatusService] Erro ao migrar precatórios sem status_id:', error);
    return { atualizados: 0, total: 0 };
  }
}

/**
 * Função para executar a migração automaticamente
 */
export async function executarMigracaoAutomatica(): Promise<void> {
  try {
    console.log('[MigracaoStatusService] Verificando necessidade de migração automática...');
    
    // Verificar se há precatórios sem status_id
    const { data: precatoriosSemStatusId, error: errorPrecatorios } = await supabase
      .from('precatorios')
      .select('id', { count: 'exact', head: true })
      .is('status_id', null)
      .not('status', 'is', null);
      
    if (errorPrecatorios) {
      console.error('[MigracaoStatusService] Erro ao verificar precatórios sem status_id:', errorPrecatorios);
      return;
    }
    
    const count = precatoriosSemStatusId?.length || 0;
    
    if (count > 0) {
      console.log(`[MigracaoStatusService] Encontrados ${count} precatórios sem status_id. Iniciando migração automática...`);
      await migrarPrecatoriosSemStatusId();
    } else {
      console.log('[MigracaoStatusService] Não há necessidade de migração automática.');
    }
  } catch (error) {
    console.error('[MigracaoStatusService] Erro ao executar migração automática:', error);
  }
}
