import { supabase } from '@/lib/supabase';
import { v4 as uuidv4 } from 'uuid';
import { getCachedData, clearCache, CACHE_TTL } from './cacheService';
import { cacheLogger } from '@/lib/logger';

export interface Task {
  id: string;
  title: string;
  description: string;
  status: string;
  priority: string;
  created_by: string;
  assignee_id?: string;
  cliente_id?: string;
  precatorio_id?: string;
  due_date?: string;
  created_at: string;
  updated_at?: string;
  area?: 'PRECATORIO' | 'RPV' | 'AMBOS';
}

export interface TaskStatus {
  id: number;
  name: string;
  color: string;
  order_index: number;
}

export interface TaskAssignment {
  id: string;
  task_id: string;
  user_id: string;
  assigned_at: string;
}

export interface User {
  id: string;
  email: string;
  nome?: string;
  role?: string;
}

export interface Subtask {
  id: string;
  task_id: string;
  title: string;
  completed: boolean;
  due_date?: string;
  created_at: string;
  updated_at?: string;
}

export interface TaskComment {
  id: string;
  task_id: string;
  user_id: string;
  content: string;
  created_at: string;
  updated_at?: string;
  profiles?: {
    id: string;
    nome?: string;
    email: string;
    avatar_url?: string;
  };
}

export interface TaskTemplate {
  id: string;
  titulo: string;
  descricao?: string;
  status?: string;
  prioridade?: string;
  subtarefas: { id: string; titulo: string; concluida: boolean }[];
  tags?: string[];
  created_at: string;
  updated_at?: string;
  created_by: string;
}

// Simulação temporária até que as tabelas sejam criadas no Supabase
let mockSubtasks: Subtask[] = [];
let mockComments: TaskComment[] = [];

// Função para buscar todas as tarefas
export const fetchTasks = async (): Promise<Task[]> => {
  const cacheKey = 'tasks:all';

  return await getCachedData(
    cacheKey,
    async () => {
      cacheLogger.debug('Buscando tarefas do banco de dados...');

      try {
        // Primeiro tentar com RPC function
        const { data: rpcResult, error: rpcError } = await supabase.rpc('get_all_tasks');

        if (!rpcError && rpcResult) {
          cacheLogger.debug(`${rpcResult.length || 0} tarefas encontradas via RPC`);
          return rpcResult;
        }

        if (rpcError) {
          cacheLogger.warn('Erro ao buscar tarefas com RPC, tentando consulta direta:', rpcError);
        }

        // Fallback: consulta direta à tabela tasks
        const { data: directResult, error: directError } = await supabase
          .from('tasks')
          .select('*')
          .order('created_at', { ascending: false });

        if (directError) {
          cacheLogger.error('Erro ao buscar tarefas diretamente:', directError);
          throw new Error(`Erro ao buscar tarefas: ${directError.message}`);
        }

        cacheLogger.debug(`${directResult?.length || 0} tarefas encontradas via consulta direta`);
        return directResult || [];

      } catch (error) {
        cacheLogger.error('Erro geral ao buscar tarefas:', error);
        // Return empty array instead of throwing to prevent app crashes
        return [];
      }
    },
    CACHE_TTL.TASKS
  );
};

// Função para buscar tarefas por cliente
export const fetchTasksByClient = async (clienteId: string): Promise<Task[]> => {
  try {
    // Primeiro, buscamos as tarefas sem joins aninhados
    const { data, error } = await supabase
      .from('tasks')
      .select('id, title, description, status, priority, created_by, assignee_id, cliente_id, precatorio_id, due_date, created_at, updated_at')
      .eq('cliente_id', clienteId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Erro ao buscar tarefas do cliente:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Erro ao buscar tarefas do cliente:', error);
    throw error;
  }
};

// Função para buscar tarefas por precatório
export const fetchTasksByPrecatorio = async (precatorioId: string): Promise<Task[]> => {
  try {
    // Primeiro, buscamos as tarefas sem joins aninhados
    const { data, error } = await supabase
      .from('tasks')
      .select('id, title, description, status, priority, created_by, assignee_id, cliente_id, precatorio_id, due_date, created_at, updated_at')
      .eq('precatorio_id', precatorioId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Erro ao buscar tarefas do precatório:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Erro ao buscar tarefas do precatório:', error);
    throw error;
  }
};

// Função para buscar tarefas atribuídas a um usuário
export const fetchTasksByAssignee = async (userId: string): Promise<Task[]> => {
  try {
    // Primeiro, buscamos as tarefas sem joins aninhados
    const { data, error } = await supabase
      .from('tasks')
      .select('id, title, description, status, priority, created_by, assignee_id, cliente_id, precatorio_id, due_date, created_at, updated_at')
      .eq('assignee_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Erro ao buscar tarefas do usuário:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Erro ao buscar tarefas do usuário:', error);
    throw error;
  }
};

// Função para buscar os status de tarefa disponíveis
export const fetchTaskStatuses = async (): Promise<TaskStatus[]> => {
  try {
    const { data, error } = await supabase
      .from('task_status')
      .select('*')
      .order('order_index', { ascending: true });

    if (error) {
      console.error('Erro ao buscar status de tarefas:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Erro ao buscar status de tarefas:', error);
    throw error;
  }
};

// Função para buscar usuários
export const fetchUsers = async (): Promise<User[]> => {
  try {
    console.log("tasksService - Buscando usuários");

    const { data: profiles, error } = await supabase
      .from('profiles')
      .select('id, email, nome, role')
      .order('nome');

    if (error) {
      console.error('tasksService - Erro ao buscar usuários:', error);
      throw error;
    }

    if (!profiles) {
      console.warn('tasksService - Nenhum usuário encontrado');
      return [];
    }

    console.log("tasksService - Usuários carregados:", profiles.length);
    return profiles;
  } catch (error) {
    console.error('tasksService - Erro ao buscar usuários:', error);
    throw error;
  }
};

// Função para criar uma nova tarefa
export const createTask = async (task: Omit<Task, 'id' | 'created_at'>): Promise<Task> => {
  try {
    console.log("tasksService - Iniciando criação de tarefa:", task);

    // Verificar campos obrigatórios
    if (!task.title || !task.title.trim()) {
      const error = "O título da tarefa é obrigatório";
      console.error("tasksService - Erro:", error);
      throw new Error(error);
    }

    if (!task.status || !task.status.trim()) {
      const error = "O status da tarefa é obrigatório";
      console.error("tasksService - Erro:", error);
      throw new Error(error);
    }

    // Definir valores padrão para campos opcionais
    const newTask: Task = {
      ...task,
      id: uuidv4(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      area: task.area || 'AMBOS'  // Garantir que o campo area seja enviado
    };

    console.log("tasksService - Enviando para Supabase:", newTask);

    // Tentar criar usando RPC primeiro (evita problemas de RLS)
    try {
      const { data: rpcData, error: rpcError } = await supabase.rpc('create_task', {
        p_title: newTask.title,
        p_description: newTask.description,
        p_status: newTask.status,
        p_priority: newTask.priority,
        p_created_by: newTask.created_by,
        p_assignee_id: newTask.assignee_id,
        p_cliente_id: newTask.cliente_id,
        p_precatorio_id: newTask.precatorio_id,
        p_due_date: newTask.due_date
      });

      if (!rpcError && rpcData) {
        console.log("tasksService - Tarefa criada com sucesso via RPC:", rpcData);
        // Clear cache to ensure fresh data on next fetch
        clearCache('tasks:all');
        return rpcData;
      }

      if (rpcError) {
        console.error("tasksService - Erro na criação via RPC:", rpcError);
        console.log("tasksService - Tentando método direto...");
      }
    } catch (rpcError) {
      console.error("tasksService - Erro ao chamar RPC:", rpcError);
    }

    // Método direto (tentativa alternativa)
    try {
      const { data, error } = await supabase
        .from('tasks')
        .insert([newTask])
        .select()
        .single();

      if (error) {
        console.error('tasksService - Erro no Supabase:', error);
        throw new Error(`Erro do banco de dados: ${error.message} (${error.code})`);
      }

      if (!data) {
        console.error('tasksService - Erro: Nenhum dado retornado após inserção');
        throw new Error("Erro ao criar tarefa: nenhum dado retornado após inserção");
      }

      cacheLogger.debug("Tarefa criada com sucesso:", data);

      // Invalidar cache de tarefas
      clearCache('tasks:');
      cacheLogger.debug('Cache de tarefas invalidado após criação');

      return data;
    } catch (insertError) {
      console.error("tasksService - Erro na inserção direta:", insertError);

      // Se ambos os métodos falharam, criar uma tarefa mock e salvá-la apenas localmente
      console.warn("tasksService - Criando tarefa apenas localmente como fallback");

      // Salvar no cache local
      try {
        const storedTasksStr = localStorage.getItem('tasks_cache');
        let tasks: Task[] = [];

        if (storedTasksStr) {
          tasks = JSON.parse(storedTasksStr);
        }

        tasks.unshift(newTask); // Adicionar no início
        localStorage.setItem('tasks_cache', JSON.stringify(tasks));
        console.log("tasksService - Cache local atualizado com tarefa mock");
      } catch (cacheError) {
        console.error("tasksService - Erro ao atualizar cache:", cacheError);
      }

      return newTask;
    }
  } catch (error) {
    console.error('tasksService - Erro ao criar tarefa:', error);
    // Verificar se o erro tem uma mensagem antes de acessá-la
    const errorMessage = error instanceof Error ? error.message : "Erro desconhecido ao criar tarefa";
    throw new Error(errorMessage);
  }
};

// Função auxiliar para obter o ID do usuário atual
async function getCurrentUserId(): Promise<string | null> {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (user) {
      return user.id;
    }

    // Se não conseguir obter o usuário pela API, tente pelo localStorage
    try {
      const userProfileStr = localStorage.getItem('userProfile');
      if (userProfileStr) {
        const userProfile = JSON.parse(userProfileStr);
        if (userProfile && userProfile.id) {
          return userProfile.id;
        }
      }
    } catch (localStorageError) {
      console.error('Erro ao obter usuário do localStorage:', localStorageError);
    }

    return null;
  } catch (error) {
    console.error('Erro ao obter usuário atual:', error);
    return null;
  }
}

// Função para atualizar uma tarefa
export const updateTask = async (id: string, taskData: Partial<Task>): Promise<Task> => {
  try {
    console.log("tasksService - Iniciando atualização de tarefa:", { id, taskData });

    // Tratar valores de data vazios para evitar erros de conversão do timestamp
    const sanitizedTaskData = {
      ...taskData,
      // Converter string vazia para null para evitar erro de timestamp
      due_date: taskData.due_date === '' ? null : taskData.due_date
    };

    // Tentar atualizar usando RPC primeiro (evita problemas de RLS)
    try {
      const { data: rpcData, error: rpcError } = await supabase.rpc('update_task', {
        p_task_id: id,
        p_title: sanitizedTaskData.title,
        p_description: sanitizedTaskData.description,
        p_status: sanitizedTaskData.status,
        p_priority: sanitizedTaskData.priority,
        p_assignee_id: sanitizedTaskData.assignee_id,
        p_due_date: sanitizedTaskData.due_date // Agora já tratado para ser null quando vazio
        // Removido p_area pois a coluna não existe no banco de dados
      });

      if (!rpcError && rpcData) {
        console.log("tasksService - Tarefa atualizada com sucesso via RPC:", rpcData);
        // Clear cache to ensure fresh data on next fetch
        clearCache('tasks:all');
        return rpcData;
      }

      if (rpcError) {
        console.error("tasksService - Erro na atualização via RPC:", rpcError);
        throw new Error(`Erro ao atualizar tarefa: ${rpcError.message}`);
      }

      // This should never be reached, but adding for TypeScript
      throw new Error("Erro inesperado: nenhum resultado retornado");
    } catch (rpcError) {
      console.error("tasksService - Erro ao chamar RPC:", rpcError);
      throw rpcError;
    }
  } catch (error) {
    console.error('tasksService - Erro ao atualizar tarefa:', error);
    throw error;
  }
};

// Função para deletar uma tarefa
export const deleteTask = async (id: string): Promise<void> => {
  try {
    const { error } = await supabase
      .from('tasks')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Erro ao deletar tarefa:', error);
      throw error;
    }

    // Clear cache to ensure fresh data on next fetch
    clearCache('tasks:all');
  } catch (error) {
    console.error('Erro ao deletar tarefa:', error);
    throw error;
  }
};

// Função para atribuir uma tarefa a um usuário
export const assignTask = async (taskId: string, userId: string): Promise<TaskAssignment> => {
  try {
    const assignment = {
      id: uuidv4(),
      task_id: taskId,
      user_id: userId,
      assigned_at: new Date().toISOString(),
    };

    const { data, error } = await supabase
      .from('task_assignments')
      .insert([assignment])
      .select()
      .single();

    if (error) {
      console.error('Erro ao atribuir tarefa:', error);
      throw error;
    }

    // Atualiza o assignee_id na tarefa
    await updateTask(taskId, { assignee_id: userId });

    return data;
  } catch (error) {
    console.error('Erro ao atribuir tarefa:', error);
    throw error;
  }
};

// Função para buscar atribuições de tarefas
export const fetchTaskAssignments = async (taskId: string): Promise<TaskAssignment[]> => {
  try {
    const { data, error } = await supabase
      .from('task_assignments')
      .select('*')
      .eq('task_id', taskId)
      .order('assigned_at', { ascending: false });

    if (error) {
      console.error('Erro ao buscar atribuições de tarefas:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Erro ao buscar atribuições de tarefas:', error);
    throw error;
  }
};

// Função para buscar subtarefas de uma tarefa
export const fetchTaskSubtasks = async (taskId: string): Promise<Subtask[]> => {
  try {
    // Fazer a consulta direta, sabemos que a tabela existe
    const { data, error } = await supabase
      .from('task_subtasks')
      .select('*')
      .eq('task_id', taskId)
      .order('created_at', { ascending: true });

    if (error) {
      console.error('Erro ao buscar subtarefas:', error);
      // Em caso de erro, retornar dados simulados
      return mockSubtasks.filter(st => st.task_id === taskId);
    }

    return data || [];
  } catch (error) {
    console.error('Erro ao buscar subtarefas:', error);

    // Retornar subtarefas simuladas em caso de erro
    return mockSubtasks.filter(st => st.task_id === taskId);
  }
};

// Função para adicionar uma subtarefa
export const addTaskSubtask = async (
  taskId: string,
  subtask: { id: string; titulo: string; concluida: boolean; prazo?: string }
): Promise<Subtask> => {
  try {
    console.log(`tasksService - Adicionando subtarefa à tarefa ${taskId}:`, subtask);

    if (!taskId) {
      throw new Error("ID da tarefa é obrigatório para adicionar uma subtarefa");
    }

    if (!subtask.titulo) {
      throw new Error("Título da subtarefa é obrigatório");
    }

    // Verificar se a tarefa existe
    const { data: taskCheck, error: taskCheckError } = await supabase
      .from('tasks')
      .select('id')
      .eq('id', taskId)
      .maybeSingle();

    if (taskCheckError) {
      console.error('tasksService - Erro ao verificar existência da tarefa:', taskCheckError);
      throw new Error(`Erro ao verificar tarefa: ${taskCheckError.message}`);
    }

    if (!taskCheck) {
      console.error('tasksService - Tarefa não encontrada:', taskId);
      throw new Error(`Tarefa com ID ${taskId} não encontrada`);
    }

    // Preparar dados da subtarefa
    const newSubtask: Subtask = {
      id: subtask.id || uuidv4(),
      task_id: taskId,
      title: subtask.titulo,
      completed: subtask.concluida || false,
      due_date: subtask.prazo,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // Adicionar subtarefa no Supabase
    const { data, error } = await supabase
      .from('task_subtasks')
      .insert([newSubtask])
      .select()
      .single();

    if (error) {
      // Verificar se é um erro de violação de chave única, o que indicaria que a subtarefa já existe
      if (error.code === '23505') {
        console.log('tasksService - Subtarefa já existe, tentando atualizar');

        // Tentar atualizar em vez de inserir
        const { data: updateData, error: updateError } = await supabase
          .from('task_subtasks')
          .update({
            title: newSubtask.title,
            completed: newSubtask.completed,
            due_date: newSubtask.due_date,
            updated_at: newSubtask.updated_at
          })
          .eq('id', newSubtask.id)
          .eq('task_id', taskId)
          .select()
          .single();

        if (updateError) {
          console.error('tasksService - Erro ao atualizar subtarefa:', updateError);
          throw new Error(`Erro ao atualizar subtarefa: ${updateError.message}`);
        }

        console.log('tasksService - Subtarefa atualizada com sucesso:', updateData);
        return updateData;
      }

      console.error('tasksService - Erro ao criar subtarefa:', error);
      throw new Error(`Erro ao criar subtarefa: ${error.message}`);
    }

    console.log('tasksService - Subtarefa adicionada com sucesso:', data);
    return data;
  } catch (error) {
    console.error('tasksService - Erro ao adicionar subtarefa:', error);
    const errorMessage = error instanceof Error ? error.message : "Erro desconhecido ao adicionar subtarefa";
    throw new Error(errorMessage);
  }
};

// Função para atualizar uma subtarefa
export const updateTaskSubtask = async (
  taskId: string,
  subtaskId: string,
  completed: boolean,
  newTitle?: string
): Promise<Subtask> => {
  try {
    const updateData: { completed: boolean; title?: string; updated_at: string } = {
      completed,
      updated_at: new Date().toISOString()
    };

    if (newTitle) {
      updateData.title = newTitle;
    }

    // Fazer a atualização direta, sabemos que a tabela existe
    const { data, error } = await supabase
      .from('task_subtasks')
      .update(updateData)
      .eq('id', subtaskId)
      .eq('task_id', taskId)
      .select()
      .single();

    if (error) {
      console.error('Erro ao atualizar subtarefa:', error);
      // Atualizar na simulação em caso de erro
      mockSubtasks = mockSubtasks.map(st =>
        st.id === subtaskId && st.task_id === taskId
          ? { ...st, ...updateData }
          : st
      );

      const updatedSubtask = mockSubtasks.find(st => st.id === subtaskId && st.task_id === taskId);
      if (!updatedSubtask) throw new Error('Subtarefa não encontrada');

      return updatedSubtask;
    }

    return data;
  } catch (error) {
    console.error('Erro ao atualizar subtarefa:', error);

    // Atualizar na simulação em caso de erro
    const updateData: { completed: boolean; title?: string; updated_at: string } = {
      completed,
      updated_at: new Date().toISOString()
    };

    if (newTitle) {
      updateData.title = newTitle;
    }

    mockSubtasks = mockSubtasks.map(st =>
      st.id === subtaskId && st.task_id === taskId
        ? { ...st, ...updateData }
        : st
    );

    const updatedSubtask = mockSubtasks.find(st => st.id === subtaskId && st.task_id === taskId);
    if (!updatedSubtask) throw new Error('Subtarefa não encontrada');

    return updatedSubtask;
  }
};

// Função para deletar uma subtarefa
export const deleteTaskSubtask = async (taskId: string, subtaskId: string): Promise<void> => {
  try {
    // Fazer a exclusão direta, sabemos que a tabela existe
    const { error } = await supabase
      .from('task_subtasks')
      .delete()
      .eq('id', subtaskId)
      .eq('task_id', taskId);

    if (error) {
      console.error('Erro ao deletar subtarefa:', error);
      // Remover da simulação em caso de erro
      mockSubtasks = mockSubtasks.filter(st => !(st.id === subtaskId && st.task_id === taskId));
    }
  } catch (error) {
    console.error('Erro ao deletar subtarefa:', error);

    // Remover da simulação em caso de erro
    mockSubtasks = mockSubtasks.filter(st => !(st.id === subtaskId && st.task_id === taskId));
  }
};

// Função para buscar comentários de uma tarefa
export const fetchTaskComments = async (taskId: string): Promise<TaskComment[]> => {
  try {
    // Fazer a consulta direta, evitando joins aninhados para prevenir recursão infinita
    const { data, error } = await supabase
      .from('task_comments')
      .select('*')
      .eq('task_id', taskId)
      .order('created_at', { ascending: true });

    if (error) {
      console.error('Erro ao buscar comentários:', error);
      // Em caso de erro, retornar dados simulados
      return mockComments.filter(c => c.task_id === taskId);
    }

    if (!data || data.length === 0) {
      return [];
    }

    // Buscar dados de perfis separadamente
    const userIds = [...new Set(data.map(comment => comment.user_id))];
    const { data: profilesData, error: profilesError } = await supabase
      .from('profiles')
      .select('id, nome, email')
      .in('id', userIds);

    if (profilesError) {
      console.error('Erro ao buscar perfis para comentários:', profilesError);
      return data; // Retorna comentários sem dados de perfil
    }

    // Mapear os comentários com os dados de perfil
    const commentsWithProfiles = data.map(comment => {
      const profile = profilesData?.find(p => p.id === comment.user_id);
      return {
        ...comment,
        profiles: profile ? {
          id: profile.id,
          nome: profile.nome,
          email: profile.email
        } : null
      };
    });

    return commentsWithProfiles;
  } catch (error) {
    console.error('Erro ao buscar comentários:', error);
    throw error;
  }
};

// Função para adicionar um comentário
export const addTaskComment = async (
  taskId: string,
  comment: { id: string; texto: string; autorId: string; dataCriacao: string }
): Promise<TaskComment> => {
  try {
    const newComment = {
      id: comment.id,
      task_id: taskId,
      user_id: comment.autorId,
      content: comment.texto,
      created_at: comment.dataCriacao,
      updated_at: new Date().toISOString(),
    };

    // Fazer a inserção direta, assumindo que a tabela existe
    const { data, error } = await supabase
      .from('task_comments')
      .insert([newComment])
      .select()
      .single();

    if (error) {
      console.error('Erro ao adicionar comentário:', error);
      // Adicionar à simulação em caso de erro
      mockComments.push(newComment);
      return newComment;
    }

    return data;
  } catch (error) {
    console.error('Erro ao adicionar comentário:', error);

    // Adicionar à simulação em caso de erro
    const newComment = {
      id: comment.id,
      task_id: taskId,
      user_id: comment.autorId,
      content: comment.texto,
      created_at: comment.dataCriacao,
      updated_at: new Date().toISOString(),
    };
    mockComments.push(newComment);
    return newComment;
  }
};

// Função para atualizar um comentário
export const updateTaskComment = async (
  taskId: string,
  commentId: string,
  newContent: string
): Promise<TaskComment> => {
  try {
    const updateData = {
      content: newContent,
      updated_at: new Date().toISOString()
    };

    // Fazer a atualização direta, assumindo que a tabela existe
    const { data, error } = await supabase
      .from('task_comments')
      .update(updateData)
      .eq('id', commentId)
      .eq('task_id', taskId)
      .select()
      .single();

    if (error) {
      console.error('Erro ao atualizar comentário:', error);
      // Atualizar na simulação em caso de erro
      mockComments = mockComments.map(c =>
        c.id === commentId && c.task_id === taskId
          ? { ...c, ...updateData }
          : c
      );

      const updatedComment = mockComments.find(c => c.id === commentId && c.task_id === taskId);
      if (!updatedComment) throw new Error('Comentário não encontrado');

      return updatedComment;
    }

    return data;
  } catch (error) {
    console.error('Erro ao atualizar comentário:', error);

    // Atualizar na simulação em caso de erro
    const updateData = {
      content: newContent,
      updated_at: new Date().toISOString()
    };

    mockComments = mockComments.map(c =>
      c.id === commentId && c.task_id === taskId
        ? { ...c, ...updateData }
        : c
    );

    const updatedComment = mockComments.find(c => c.id === commentId && c.task_id === taskId);
    if (!updatedComment) throw new Error('Comentário não encontrado');

    return updatedComment;
  }
};

// Função para deletar um comentário
export const deleteTaskComment = async (taskId: string, commentId: string): Promise<void> => {
  try {
    // Fazer a exclusão direta, assumindo que a tabela existe
    const { error } = await supabase
      .from('task_comments')
      .delete()
      .eq('id', commentId)
      .eq('task_id', taskId);

    if (error) {
      console.error('Erro ao deletar comentário:', error);
      // Remover da simulação em caso de erro
      mockComments = mockComments.filter(c => !(c.id === commentId && c.task_id === taskId));
    }
  } catch (error) {
    console.error('Erro ao deletar comentário:', error);

    // Remover da simulação em caso de erro
    mockComments = mockComments.filter(c => !(c.id === commentId && c.task_id === taskId));
  }
};

// Funções para gerenciar modelos de tarefas
export const fetchTaskTemplates = async (): Promise<TaskTemplate[]> => {
  try {
    const { data, error } = await supabase
      .from('task_templates')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Erro ao buscar modelos de tarefas:', error);
      throw error;
    }

    // Converter os dados do banco para o formato esperado pelo frontend
    const templates: TaskTemplate[] = data.map(template => ({
      id: template.id,
      titulo: template.titulo,
      descricao: template.descricao,
      status: template.status,
      prioridade: template.prioridade,
      subtarefas: template.subtarefas || [],
      tags: template.tags || [],
      created_at: template.created_at,
      updated_at: template.updated_at,
      created_by: template.created_by
    }));

    return templates || [];
  } catch (error) {
    console.error('Erro ao buscar modelos de tarefas:', error);
    throw error;
  }
};

export const createTaskTemplate = async (template: Omit<TaskTemplate, 'id' | 'created_at' | 'updated_at' | 'created_by'>): Promise<TaskTemplate> => {
  try {
    // Obter o ID do usuário atual
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      throw new Error("Usuário não autenticado");
    }

    // Cria o template com os campos necessários
    const newTemplate = {
      id: uuidv4(),
      titulo: template.titulo,
      descricao: template.descricao,
      status: template.status,
      prioridade: template.prioridade,
      subtarefas: template.subtarefas || [],
      tags: template.tags || [],
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
      // Não incluímos created_by para funcionar com a estrutura atual do banco
    };

    // Tentamos inserir sem o created_by primeiro (compatibilidade com a estrutura atual)
    let { data, error } = await supabase
      .from('task_templates')
      .insert([newTemplate])
      .select()
      .single();

    // Se falhar com erro de permissão, tentamos novamente com created_by
    if (error && error.code === '42501') {
      console.log('Tentando criar template com created_by...');
      const templateWithCreatedBy = {
        ...newTemplate,
        created_by: user.id
      };

      const result = await supabase
        .from('task_templates')
        .insert([templateWithCreatedBy])
        .select()
        .single();

      data = result.data;
      error = result.error;
    }

    if (error) {
      console.error('Erro ao criar modelo de tarefa:', error);
      throw error;
    }

    // Adicionamos o created_by no objeto retornado para o frontend
    return {
      ...data,
      created_by: user.id
    };
  } catch (error) {
    console.error('Erro ao criar modelo de tarefa:', error);
    throw error;
  }
};

export const updateTaskTemplate = async (id: string, templateData: Partial<TaskTemplate>): Promise<TaskTemplate> => {
  try {
    // Verificar se o usuário atual tem permissões
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      throw new Error("Usuário não autenticado");
    }

    const updateData = {
      ...templateData,
      updated_at: new Date().toISOString()
    };

    // Tentamos atualizar sem condição de created_by primeiro
    let { data, error } = await supabase
      .from('task_templates')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    // Se falhar com erro de permissão, tentamos novamente com created_by
    if (error && error.code === '42501') {
      console.log('Tentando atualizar template com condição created_by...');
      const result = await supabase
        .from('task_templates')
        .update(updateData)
        .eq('id', id)
        .eq('created_by', user.id)
        .select()
        .single();

      data = result.data;
      error = result.error;
    }

    if (error) {
      console.error('Erro ao atualizar modelo de tarefa:', error);
      throw error;
    }

    // Adicionamos o created_by no objeto retornado para o frontend
    return {
      ...data,
      created_by: data.created_by || user.id
    };
  } catch (error) {
    console.error('Erro ao atualizar modelo de tarefa:', error);
    throw error;
  }
};

export const deleteTaskTemplate = async (id: string): Promise<void> => {
  try {
    // Verificar se o usuário atual tem permissões
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      throw new Error("Usuário não autenticado");
    }

    // Tentamos excluir sem condição de created_by primeiro
    let { error } = await supabase
      .from('task_templates')
      .delete()
      .eq('id', id);

    // Se falhar com erro de permissão, tentamos novamente com created_by
    if (error && error.code === '42501') {
      console.log('Tentando excluir template com condição created_by...');
      const result = await supabase
        .from('task_templates')
        .delete()
        .eq('id', id)
        .eq('created_by', user.id);

      error = result.error;
    }

    if (error) {
      console.error('Erro ao deletar modelo de tarefa:', error);
      throw error;
    }
  } catch (error) {
    console.error('Erro ao deletar modelo de tarefa:', error);
    throw error;
  }
};
