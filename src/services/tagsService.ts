import { supabase } from '@/lib/supabase';

export interface Tag {
  id: string;
  nome: string;
  cor: string;
  descricao?: string;
  icone?: string;
  criado_por?: string;
  created_at?: string;
  updated_at?: string;
}

export interface TagPermission {
  id: string;
  user_id: string;
  tag_id: string;
  can_view: boolean;
  can_edit: boolean;
  can_delete: boolean;
  created_at?: string;
  updated_at?: string;
}

// Função para buscar todas as tags
export const fetchAllTags = async (): Promise<Tag[]> => {
  try {
    const { data, error } = await supabase
      .from('tags')
      .select('*')
      .is('is_deleted', false)
      .order('nome');

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Erro ao buscar tags:', error);
    throw error;
  }
};

// Cache para tags visíveis
let tagsCache: {
  data: Tag[];
  timestamp: number;
} | null = null;

const CACHE_DURATION = 60000; // 1 minuto

// Função para buscar tags visíveis para o usuário atual
export const fetchVisibleTags = async (): Promise<Tag[]> => {
  try {
    // Verificar se há cache válido
    if (tagsCache && Date.now() - tagsCache.timestamp < CACHE_DURATION) {
      console.log('Usando cache de tags');
      return tagsCache.data;
    }

    const { data: userData } = await supabase.auth.getUser();
    if (!userData.user) throw new Error('Usuário não autenticado');

    const userId = userData.user.id;

    // Tentar buscar todas as tags visíveis para o usuário
    try {
      // Primeiro, buscar tags criadas pelo usuário
      const { data: ownTags, error: ownError } = await supabase
        .from('tags')
        .select('*')
        .is('is_deleted', false)
        .eq('criado_por', userId)
        .order('nome');

      if (ownError) throw ownError;

      // Depois, buscar tags com permissão de visualização
      const { data: permissionTags, error: permissionError } = await supabase
        .from('tag_permissions')
        .select(`
          tag_id,
          tags:tag_id (*)
        `)
        .eq('user_id', userId)
        .eq('can_view', true);

      if (permissionError) throw permissionError;

      // Combinar os resultados e remover duplicatas
      const allTags = [
        ...(ownTags || []),
        ...(permissionTags?.map(item => item.tags) || [])
      ];

      // Remover duplicatas por ID
      const uniqueTags = Array.from(
        new Map(allTags.map(tag => [tag.id, tag])).values()
      );

      // Atualizar cache
      tagsCache = {
        data: uniqueTags,
        timestamp: Date.now()
      };

      return uniqueTags;
    } catch (error) {
      console.error('Erro ao buscar tags com permissões:', error);

      // Fallback: buscar apenas tags criadas pelo usuário
      const { data: ownTags, error: ownError } = await supabase
        .from('tags')
        .select('*')
        .is('is_deleted', false)
        .eq('criado_por', userId)
        .order('nome');

      if (ownError) throw ownError;

      // Atualizar cache
      tagsCache = {
        data: ownTags || [],
        timestamp: Date.now()
      };

      return ownTags || [];
    }
  } catch (error) {
    console.error('Erro ao buscar tags visíveis:', error);
    // Retornar array vazio em vez de lançar erro para evitar quebrar a UI
    return [];
  }
};

// Função para limpar o cache de tags
export const clearTagsCache = () => {
  tagsCache = null;
};

// Função para criar uma nova tag
export const createTag = async (tag: Omit<Tag, 'id' | 'created_at' | 'updated_at'>): Promise<Tag> => {
  try {
    const { data: userData } = await supabase.auth.getUser();
    if (!userData.user) throw new Error('Usuário não autenticado');

    const { data, error } = await supabase
      .from('tags')
      .insert({
        ...tag,
        criado_por: userData.user.id
      })
      .select()
      .single();

    if (error) throw error;

    // Limpar o cache de tags para forçar uma nova busca
    clearTagsCache();

    return data;
  } catch (error) {
    console.error('Erro ao criar tag:', error);
    throw error;
  }
};

// Função para atualizar uma tag existente
export const updateTag = async (id: string, tag: Partial<Tag>): Promise<Tag> => {
  try {
    const { data, error } = await supabase
      .from('tags')
      .update(tag)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;

    // Limpar o cache de tags para forçar uma nova busca
    clearTagsCache();

    return data;
  } catch (error) {
    console.error('Erro ao atualizar tag:', error);
    throw error;
  }
};

// Função para excluir uma tag
export const deleteTag = async (id: string): Promise<void> => {
  try {
    // Marcar como excluído em vez de remover do banco de dados
    const { error } = await supabase
      .from('tags')
      .update({ is_deleted: true, deleted_at: new Date().toISOString() })
      .eq('id', id);

    if (error) throw error;

    // Limpar o cache de tags para forçar uma nova busca
    clearTagsCache();
  } catch (error) {
    console.error('Erro ao excluir tag:', error);
    throw error;
  }
};

// Função para obter permissões de uma tag
export const getTagPermissions = async (tagId: string): Promise<TagPermission[]> => {
  try {
    const { data, error } = await supabase
      .from('tag_permissions')
      .select(`
        *,
        profiles:user_id (id, nome, email, avatar_url)
      `)
      .eq('tag_id', tagId);

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Erro ao obter permissões da tag:', error);
    throw error;
  }
};

// Função para atualizar permissões de uma tag
export const updateTagPermission = async (
  tagId: string,
  userId: string,
  permissions: {
    can_view?: boolean;
    can_edit?: boolean;
    can_delete?: boolean;
  }
): Promise<void> => {
  try {
    // Verificar se já existe uma permissão para este usuário e tag
    const { data: existingPermission, error: checkError } = await supabase
      .from('tag_permissions')
      .select('id')
      .eq('tag_id', tagId)
      .eq('user_id', userId)
      .maybeSingle();

    if (checkError) throw checkError;

    if (existingPermission) {
      // Atualizar permissão existente
      const { error } = await supabase
        .from('tag_permissions')
        .update({
          ...permissions,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingPermission.id);

      if (error) throw error;
    } else {
      // Criar nova permissão
      const { error } = await supabase
        .from('tag_permissions')
        .insert({
          tag_id: tagId,
          user_id: userId,
          can_view: permissions.can_view ?? false,
          can_edit: permissions.can_edit ?? false,
          can_delete: permissions.can_delete ?? false
        });

      if (error) throw error;
    }
  } catch (error) {
    console.error('Erro ao atualizar permissão da tag:', error);
    throw error;
  }
};

// Função para verificar se o usuário tem permissão para uma tag
export const checkTagPermission = async (
  tagId: string,
  permission: 'view' | 'edit' | 'delete'
): Promise<boolean> => {
  try {
    const { data: userData } = await supabase.auth.getUser();
    if (!userData.user) return false;

    const userId = userData.user.id;

    // Verificar se o usuário é o criador da tag
    const { data: tag, error: tagError } = await supabase
      .from('tags')
      .select('criado_por')
      .eq('id', tagId)
      .single();

    if (tagError) throw tagError;

    // Se o usuário é o criador, tem todas as permissões
    if (tag.criado_por === userId) return true;

    // Verificar permissão específica
    const { data, error } = await supabase
      .from('tag_permissions')
      .select(permission === 'view' ? 'can_view' : permission === 'edit' ? 'can_edit' : 'can_delete')
      .eq('tag_id', tagId)
      .eq('user_id', userId)
      .single();

    if (error) return false;
    return data[permission === 'view' ? 'can_view' : permission === 'edit' ? 'can_edit' : 'can_delete'] || false;
  } catch (error) {
    console.error('Erro ao verificar permissão da tag:', error);
    return false;
  }
};

// Função para associar tags a um precatório
export const associateTagsToPrecatorio = async (precatorioId: string, tagIds: string[]): Promise<void> => {
  try {
    // Primeiro, remover todas as associações existentes
    const { error: deleteError } = await supabase
      .from('precatorios_tags')
      .delete()
      .eq('precatorio_id', precatorioId);

    if (deleteError) throw deleteError;

    // Se não houver tags para associar, terminar aqui
    if (!tagIds.length) return;

    // Criar novas associações
    const associations = tagIds.map(tagId => ({
      precatorio_id: precatorioId,
      tag_id: tagId
    }));

    const { error } = await supabase
      .from('precatorios_tags')
      .insert(associations);

    if (error) throw error;
  } catch (error) {
    console.error('Erro ao associar tags ao precatório:', error);
    throw error;
  }
};

// Função para obter tags de um precatório
export const getPrecatorioTags = async (precatorioId: string): Promise<Tag[]> => {
  try {
    const { data, error } = await supabase
      .from('precatorios_tags')
      .select(`
        tag_id,
        tags:tag_id (*)
      `)
      .eq('precatorio_id', precatorioId);

    if (error) throw error;
    return data?.map(item => item.tags) || [];
  } catch (error) {
    console.error('Erro ao obter tags do precatório:', error);
    throw error;
  }
};
