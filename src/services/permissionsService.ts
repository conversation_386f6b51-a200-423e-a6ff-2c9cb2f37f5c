import { supabase } from '@/lib/supabase';

export interface UserPermission {
  resource_type: string;
  resource_id?: string;
  action: string;
  allowed: boolean;
}

export interface TaskVisibilitySettings {
  can_see_own_tasks: boolean;
  can_see_team_tasks: boolean;
  can_see_all_tasks: boolean;
  visible_user_ids: string[];
}

export interface PageAccessSetting {
  page_path: string;
  can_access: boolean;
}

export interface UserPermissionsData {
  role_permissions: UserPermission[];
  specific_permissions: UserPermission[];
  task_visibility: TaskVisibilitySettings;
  page_access: PageAccessSetting[];
}

// Cache para permissões
interface CacheEntry {
  data: UserPermissionsData;
  timestamp: number;
  expiresAt: number;
}

let permissionsCache: Record<string, CacheEntry> = {};

// Tempo de expiração do cache em milissegundos (5 minutos)
const CACHE_EXPIRATION = 5 * 60 * 1000;

// Função para limpar o cache expirado
const cleanExpiredCache = () => {
  const now = Date.now();
  Object.keys(permissionsCache).forEach(key => {
    if (permissionsCache[key].expiresAt < now) {
      delete permissionsCache[key];
    }
  });
};

// Limpar cache expirado a cada minuto
setInterval(cleanExpiredCache, 60 * 1000);

// Função para obter todas as permissões de um usuário
export const getUserPermissions = async (userId: string): Promise<UserPermissionsData> => {
  try {
    // Verificar se já temos no cache válido
    const now = Date.now();
    if (permissionsCache[userId] && permissionsCache[userId].expiresAt > now) {
      return permissionsCache[userId].data;
    }

    // Limpar cache expirado
    cleanExpiredCache();

    // Obter o papel do usuário primeiro
    const userRole = await getUserRole(userId);

    // Tentar obter permissões do servidor
    try {
      const { data, error } = await supabase.rpc('get_user_permissions', {
        p_user_id: userId
      });

      if (error) {
        console.error('Erro ao obter permissões do usuário:', error);
        throw error;
      }

      // Garantir que todos os campos existam, mesmo que vazios
      const permissions: UserPermissionsData = {
        role_permissions: data?.role_permissions || [],
        specific_permissions: data?.specific_permissions || [],
        task_visibility: data?.task_visibility || {
          can_see_own_tasks: true,
          can_see_team_tasks: userRole !== 'captador',
          can_see_all_tasks: userRole === 'admin' || userRole === 'gerente_geral',
          visible_user_ids: []
        },
        page_access: data?.page_access || []
      };

      // Salvar no cache com expiração
      permissionsCache[userId] = {
        data: permissions,
        timestamp: Date.now(),
        expiresAt: Date.now() + CACHE_EXPIRATION
      };

      return permissions;
    } catch (error) {
      console.error('Erro ao obter permissões do servidor:', error);

      // Criar permissões padrão baseadas no papel do usuário
      const defaultPermissions: UserPermissionsData = {
        role_permissions: [],
        specific_permissions: [],
        task_visibility: {
          can_see_own_tasks: true,
          can_see_team_tasks: userRole !== 'captador',
          can_see_all_tasks: userRole === 'admin' || userRole === 'gerente_geral',
          visible_user_ids: []
        },
        page_access: []
      };

      // Salvar no cache com expiração
      permissionsCache[userId] = {
        data: defaultPermissions,
        timestamp: Date.now(),
        expiresAt: Date.now() + CACHE_EXPIRATION
      };

      return defaultPermissions;
    }
  } catch (error) {
    console.error('Erro ao obter permissões do usuário:', error);
    throw error;
  }
};

// Função para verificar se um usuário tem permissão para uma ação específica
export const checkPermission = async (
  userId: string,
  resourceType: string,
  action: string,
  resourceId?: string
): Promise<boolean> => {
  try {
    // Obter permissões do usuário (do cache, se disponível)
    const permissions = await getUserPermissions(userId);

    // Verificar se é admin (tem todas as permissões)
    const isAdmin = await isUserAdmin(userId);
    if (isAdmin) return true;

    // Verificar permissões específicas primeiro
    const specificPermission = permissions.specific_permissions.find(
      p => p.resource_type === resourceType &&
           p.action === action &&
           (resourceId ? p.resource_id === resourceId : !p.resource_id)
    );

    if (specificPermission) {
      return specificPermission.allowed;
    }

    // Verificar permissões baseadas em papel (role)
    const hasRolePermission = permissions.role_permissions.some(
      p => p.resource_type === resourceType && p.action === action
    );

    return hasRolePermission;
  } catch (error) {
    console.error('Erro ao verificar permissão:', error);
    return false;
  }
};

// Função para verificar se um usuário pode acessar uma página
export const checkPageAccess = async (userId: string, pagePath: string): Promise<boolean> => {
  try {
    // Verificar se é admin (tem acesso a todas as páginas)
    const isAdmin = await isUserAdmin(userId);
    if (isAdmin) return true;

    // Obter permissões do usuário
    const permissions = await getUserPermissions(userId);

    // Verificar configurações específicas de acesso à página
    const pageAccess = permissions.page_access.find(p => p.page_path === pagePath);
    if (pageAccess) {
      return pageAccess.can_access;
    }

    // Se não houver configuração específica, verificar com base no papel (role)
    const userRole = await getUserRole(userId);

    // Regras padrão baseadas no papel
    switch (userRole) {
      case 'admin':
        return true;
      case 'gerente_precatorio':
        return true;
      case 'assistente':
        // Assistentes têm acesso limitado por padrão
        return ['/dashboard', '/profile', '/tarefas', '/clientes'].includes(pagePath);
      default:
        return false;
    }
  } catch (error) {
    console.error('Erro ao verificar acesso à página:', error);
    return false;
  }
};

// Função para verificar se um usuário pode ver uma tarefa específica
export const checkTaskVisibility = async (userId: string, taskId: string): Promise<boolean> => {
  try {
    // Verificar se é admin (pode ver todas as tarefas)
    const isAdmin = await isUserAdmin(userId);
    if (isAdmin) return true;

    // Obter permissões do usuário
    const permissions = await getUserPermissions(userId);
    const visibility = permissions.task_visibility;

    // Se pode ver todas as tarefas
    if (visibility.can_see_all_tasks) {
      return true;
    }

    // Obter detalhes da tarefa
    const { data: task, error } = await supabase
      .from('tasks')
      .select('assignee_id')
      .eq('id', taskId)
      .single();

    if (error) {
      console.error('Erro ao obter detalhes da tarefa:', error);
      return false;
    }

    // Se pode ver tarefas próprias e é o responsável
    if (visibility.can_see_own_tasks && task.assignee_id === userId) {
      return true;
    }

    // Se pode ver tarefas da equipe e o responsável está na lista de visíveis
    if (visibility.can_see_team_tasks &&
        task.assignee_id &&
        visibility.visible_user_ids.includes(task.assignee_id)) {
      return true;
    }

    return false;
  } catch (error) {
    console.error('Erro ao verificar visibilidade da tarefa:', error);
    return false;
  }
};

// Função para atualizar permissão específica de um usuário
export const updateUserPermission = async (
  adminId: string,
  userId: string,
  resourceType: string,
  action: string,
  allowed: boolean,
  resourceId?: string
): Promise<boolean> => {
  try {
    const { data, error } = await supabase.rpc('update_user_permission', {
      p_admin_id: adminId,
      p_user_id: userId,
      p_resource_type: resourceType,
      p_resource_id: resourceId || null,
      p_action: action,
      p_allowed: allowed
    });

    if (error) {
      console.error('Erro ao atualizar permissão do usuário:', error);
      throw error;
    }

    // Limpar cache de permissões para este usuário
    delete permissionsCache[userId];

    return true;
  } catch (error) {
    console.error('Erro ao atualizar permissão do usuário:', error);
    throw error;
  }
};

// Função para atualizar configurações de visibilidade de tarefas
export const updateTaskVisibilitySettings = async (
  adminId: string,
  userId: string,
  settings: TaskVisibilitySettings
): Promise<boolean> => {
  try {
    const { data, error } = await supabase.rpc('update_task_visibility_settings', {
      p_admin_id: adminId,
      p_user_id: userId,
      p_can_see_own_tasks: settings.can_see_own_tasks,
      p_can_see_team_tasks: settings.can_see_team_tasks,
      p_can_see_all_tasks: settings.can_see_all_tasks,
      p_visible_user_ids: settings.visible_user_ids
    });

    if (error) {
      console.error('Erro ao atualizar configurações de visibilidade de tarefas:', error);
      throw error;
    }

    // Limpar cache de permissões para este usuário
    delete permissionsCache[userId];

    return true;
  } catch (error) {
    console.error('Erro ao atualizar configurações de visibilidade de tarefas:', error);
    throw error;
  }
};

// Função para atualizar acesso a uma página
export const updatePageAccess = async (
  adminId: string,
  userId: string,
  pagePath: string,
  canAccess: boolean
): Promise<boolean> => {
  try {
    const { data, error } = await supabase.rpc('update_page_access', {
      p_admin_id: adminId,
      p_user_id: userId,
      p_page_path: pagePath,
      p_can_access: canAccess
    });

    if (error) {
      console.error('Erro ao atualizar acesso à página:', error);
      throw error;
    }

    // Limpar cache de permissões para este usuário
    delete permissionsCache[userId];

    return true;
  } catch (error) {
    console.error('Erro ao atualizar acesso à página:', error);
    throw error;
  }
};

// Função auxiliar para verificar se um usuário é admin
export const isUserAdmin = async (userId: string): Promise<boolean> => {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', userId)
      .single();

    if (error) {
      console.error('Erro ao verificar papel do usuário:', error);
      return false;
    }

    return data.role === 'admin';
  } catch (error) {
    console.error('Erro ao verificar papel do usuário:', error);
    return false;
  }
};

// Função auxiliar para obter o papel (role) de um usuário
export const getUserRole = async (userId: string): Promise<string | null> => {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', userId)
      .single();

    if (error) {
      console.error('Erro ao obter papel do usuário:', error);
      return null;
    }

    return data.role;
  } catch (error) {
    console.error('Erro ao obter papel do usuário:', error);
    return null;
  }
};

// Função para limpar o cache de permissões
export const clearPermissionsCache = (userId?: string) => {
  if (userId) {
    delete permissionsCache[userId];
  } else {
    permissionsCache = {};
  }
};

// Função para obter estatísticas do cache
export const getPermissionsCacheStats = (): {
  totalEntries: number;
  validEntries: number;
  expiredEntries: number;
  averageAge: number;
} => {
  const now = Date.now();
  const entries = Object.keys(permissionsCache);
  const validEntries = entries.filter(key => permissionsCache[key].expiresAt > now);
  const expiredEntries = entries.filter(key => permissionsCache[key].expiresAt <= now);

  const ages = validEntries.map(key => now - permissionsCache[key].timestamp);
  const averageAge = ages.length > 0 ? ages.reduce((sum, age) => sum + age, 0) / ages.length : 0;

  return {
    totalEntries: entries.length,
    validEntries: validEntries.length,
    expiredEntries: expiredEntries.length,
    averageAge
  };
};
