/**
 * Cache Warmup Service
 * Responsible for preloading critical data into cache during app initialization
 */

import { warmupCache, CACHE_TTL } from './cacheService';
import { cacheLogger } from '@/lib/logger';
import { buscarClientes } from './clientesService';
import { fetchTasks } from './tasksService';
import { buscarTodosStatus } from './statusPrecatoriosService';
import { buscarTodosPrecatorios } from './precatoriosServiceSimples';
import { getUserPermissions } from './permissionsService';
import { useAuth } from '@/hooks/useAuth';

/**
 * Critical data that should be warmed up on app initialization
 */
export interface CacheWarmupConfig {
  // Core business data
  precatorios: boolean;
  clientes: boolean;
  tasks: boolean;
  
  // Configuration data
  statusConfig: boolean;
  kanbanColumns: boolean;
  
  // User-specific data
  userPermissions: boolean;
  userProfile: boolean;
}

/**
 * Default warmup configuration - prioritizes most frequently accessed data
 */
export const DEFAULT_WARMUP_CONFIG: CacheWarmupConfig = {
  precatorios: true,
  clientes: true,
  tasks: true,
  statusConfig: true,
  kanbanColumns: true,
  userPermissions: true,
  userProfile: true,
};

/**
 * Lightweight warmup configuration for faster startup
 */
export const LIGHTWEIGHT_WARMUP_CONFIG: CacheWarmupConfig = {
  precatorios: false,
  clientes: true,
  tasks: false,
  statusConfig: true,
  kanbanColumns: true,
  userPermissions: true,
  userProfile: true,
};

/**
 * Warm up cache with critical application data
 */
export async function warmupApplicationCache(
  config: CacheWarmupConfig = DEFAULT_WARMUP_CONFIG,
  userId?: string
): Promise<void> {
  cacheLogger.info('Iniciando aquecimento do cache da aplicação...');
  
  const warmupFunctions: Record<string, () => Promise<any>> = {};

  // Core business data
  if (config.clientes) {
    warmupFunctions['clientes'] = async () => {
      cacheLogger.debug('Aquecendo cache de clientes...');
      return await buscarClientes();
    };
  }

  if (config.tasks) {
    warmupFunctions['tasks'] = async () => {
      cacheLogger.debug('Aquecendo cache de tarefas...');
      return await fetchTasks();
    };
  }

  if (config.precatorios) {
    warmupFunctions['precatorios-PRECATORIO'] = async () => {
      cacheLogger.debug('Aquecendo cache de precatórios...');
      return await buscarTodosPrecatorios('PRECATORIO');
    };

    warmupFunctions['precatorios-RPV'] = async () => {
      cacheLogger.debug('Aquecendo cache de RPVs...');
      return await buscarTodosPrecatorios('RPV');
    };
  }

  // Configuration data
  if (config.statusConfig) {
    warmupFunctions['status-config'] = async () => {
      cacheLogger.debug('Aquecendo cache de status...');
      return await buscarTodosStatus();
    };
  }

  if (config.kanbanColumns) {
    warmupFunctions['kanban-columns'] = async () => {
      cacheLogger.debug('Aquecendo cache de colunas kanban...');
      // Import dynamically to avoid circular dependencies
      const { buscarColunasPersonalizadas } = await import('./kanbanColunasService');
      return await buscarColunasPersonalizadas();
    };
  }

  // User-specific data
  if (config.userPermissions && userId) {
    warmupFunctions['user-permissions'] = async () => {
      cacheLogger.debug(`Aquecendo cache de permissões do usuário ${userId}...`);
      return await getUserPermissions(userId);
    };
  }

  if (config.userProfile && userId) {
    warmupFunctions['user-profile'] = async () => {
      cacheLogger.debug(`Aquecendo cache do perfil do usuário ${userId}...`);
      // Import dynamically to avoid circular dependencies
      const { supabase } = await import('@/lib/supabase');
      const { data } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();
      return data;
    };
  }

  // Execute warmup
  await warmupCache(warmupFunctions);
  
  cacheLogger.info(`Aquecimento do cache concluído. ${Object.keys(warmupFunctions).length} tipos de dados aquecidos.`);
}

/**
 * Warm up cache for a specific user after authentication
 */
export async function warmupUserSpecificCache(userId: string): Promise<void> {
  cacheLogger.info(`Aquecendo cache específico do usuário ${userId}...`);
  
  const userWarmupFunctions: Record<string, () => Promise<any>> = {
    'user-permissions': async () => {
      return await getUserPermissions(userId);
    },
    
    'user-profile': async () => {
      const { supabase } = await import('@/lib/supabase');
      const { data } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();
      return data;
    },
    
    'user-tasks': async () => {
      const { fetchTasksByAssignee } = await import('./tasksService');
      return await fetchTasksByAssignee(userId);
    },
  };

  await warmupCache(userWarmupFunctions);
  cacheLogger.info(`Cache específico do usuário ${userId} aquecido com sucesso.`);
}

/**
 * Warm up cache for dashboard data
 */
export async function warmupDashboardCache(): Promise<void> {
  cacheLogger.info('Aquecendo cache do dashboard...');
  
  const dashboardWarmupFunctions: Record<string, () => Promise<any>> = {
    'dashboard-clientes': async () => {
      return await buscarClientes();
    },
    
    'dashboard-precatorios': async () => {
      const [precatorios, rpvs] = await Promise.all([
        buscarTodosPrecatorios('PRECATORIO'),
        buscarTodosPrecatorios('RPV')
      ]);
      return { precatorios, rpvs };
    },
    
    'dashboard-tasks': async () => {
      return await fetchTasks();
    },
  };

  await warmupCache(dashboardWarmupFunctions);
  cacheLogger.info('Cache do dashboard aquecido com sucesso.');
}

/**
 * Warm up cache for kanban view
 */
export async function warmupKanbanCache(): Promise<void> {
  cacheLogger.info('Aquecendo cache do kanban...');
  
  const kanbanWarmupFunctions: Record<string, () => Promise<any>> = {
    'kanban-precatorios': async () => {
      return await buscarTodosPrecatorios('PRECATORIO');
    },
    
    'kanban-columns': async () => {
      const { buscarColunasPersonalizadas } = await import('./kanbanColunasService');
      return await buscarColunasPersonalizadas();
    },
    
    'kanban-status': async () => {
      return await buscarTodosStatus();
    },
  };

  await warmupCache(kanbanWarmupFunctions);
  cacheLogger.info('Cache do kanban aquecido com sucesso.');
}

/**
 * Background cache refresh for long-running sessions
 */
export function startBackgroundCacheRefresh(): void {
  cacheLogger.info('Iniciando atualização automática do cache em segundo plano...');
  
  // Refresh critical data every 5 minutes
  setInterval(async () => {
    try {
      await warmupApplicationCache(LIGHTWEIGHT_WARMUP_CONFIG);
      cacheLogger.debug('Atualização automática do cache concluída.');
    } catch (error) {
      cacheLogger.error('Erro na atualização automática do cache:', error);
    }
  }, 5 * 60 * 1000); // 5 minutes
  
  // Cleanup expired cache every 10 minutes
  setInterval(async () => {
    try {
      const { cleanupExpiredCache } = await import('./cacheService');
      const removedCount = cleanupExpiredCache();
      if (removedCount > 0) {
        cacheLogger.debug(`Limpeza automática: ${removedCount} itens expirados removidos.`);
      }
    } catch (error) {
      cacheLogger.error('Erro na limpeza automática do cache:', error);
    }
  }, 10 * 60 * 1000); // 10 minutes
}

/**
 * Get cache warmup recommendations based on user role
 */
export function getWarmupConfigForRole(role: string): CacheWarmupConfig {
  switch (role) {
    case 'admin':
    case 'gerente_geral':
      return DEFAULT_WARMUP_CONFIG; // Full warmup for admins
      
    case 'gerente_precatorio':
    case 'gerente_operacional':
      return {
        ...DEFAULT_WARMUP_CONFIG,
        tasks: false, // Managers might not need all tasks
      };
      
    case 'assistente':
    case 'captador':
      return LIGHTWEIGHT_WARMUP_CONFIG; // Lightweight for regular users
      
    default:
      return LIGHTWEIGHT_WARMUP_CONFIG;
  }
}
