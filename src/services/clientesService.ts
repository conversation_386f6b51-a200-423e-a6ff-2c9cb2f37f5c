import { supabase } from '@/lib/supabase';
import { getCachedData, setCache, clearCache, CACHE_TTL } from './cacheService';
import { cacheLogger } from '@/lib/logger';

export interface Cliente {
  id: string;
  nome: string;
  email: string;
  telefone: string;
  cpf_cnpj: string;
  endereco: string;
  cidade: string;
  estado: string;
  data_cadastro: string;
  notas: string;
  created_at: string;
  tipo: 'pessoa_fisica' | 'pessoa_juridica';
  status: string;
  updated_at: string;
}

export interface ClienteComTotais extends Cliente {
  total_precatorios: number;
  valor_total: number;
}

// Buscar todos os clientes com informações de precatórios
export async function buscarClientes(): Promise<ClienteComTotais[]> {
  const cacheKey = 'clientes:all';

  return await getCachedData(
    cacheKey,
    async () => {
      cacheLogger.debug('Buscando clientes do banco de dados...');

      const { data, error } = await supabase
        .from('view_clientes_com_totais')
        .select('*');

      if (error) {
        cacheLogger.error('Erro ao buscar clientes:', error);
        throw error;
      }

      cacheLogger.debug(`${data?.length || 0} clientes encontrados`);
      return data || [];
    },
    CACHE_TTL.CLIENTES
  );
}

// Buscar um cliente específico por ID
export async function buscarClientePorId(id: string): Promise<ClienteComTotais | null> {
  try {
    const { data, error } = await supabase
      .from('view_clientes_com_totais')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error(`Erro ao buscar cliente com ID ${id}:`, error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error(`Erro ao buscar cliente com ID ${id}:`, error);
    throw error;
  }
}

// Criar um novo cliente
export async function criarCliente(cliente: Omit<Cliente, 'id' | 'created_at' | 'updated_at'>): Promise<Cliente> {
  try {
    const { data, error } = await supabase
      .from('clientes')
      .insert([cliente])
      .select()
      .single();

    if (error) {
      cacheLogger.error('Erro ao criar cliente:', error);
      throw error;
    }

    // Invalidar cache de clientes
    clearCache('clientes:');
    cacheLogger.debug('Cache de clientes invalidado após criação');

    return data;
  } catch (error) {
    cacheLogger.error('Erro ao criar cliente:', error);
    throw error;
  }
}

// Atualizar um cliente existente
export async function atualizarCliente(id: string, cliente: Partial<Omit<Cliente, 'id' | 'created_at' | 'updated_at'>>): Promise<Cliente> {
  try {
    const { data, error } = await supabase
      .from('clientes')
      .update(cliente)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      cacheLogger.error(`Erro ao atualizar cliente com ID ${id}:`, error);
      throw error;
    }

    // Invalidar cache de clientes
    clearCache('clientes:');
    cacheLogger.debug(`Cache de clientes invalidado após atualização do cliente ${id}`);

    return data;
  } catch (error) {
    cacheLogger.error(`Erro ao atualizar cliente com ID ${id}:`, error);
    throw error;
  }
}

// Excluir um cliente
export async function excluirCliente(id: string): Promise<void> {
  try {
    const { error } = await supabase
      .from('clientes')
      .delete()
      .eq('id', id);

    if (error) {
      console.error(`Erro ao excluir cliente com ID ${id}:`, error);
      throw error;
    }
  } catch (error) {
    console.error(`Erro ao excluir cliente com ID ${id}:`, error);
    throw error;
  }
}

// Buscar clientes com filtros básicos
export async function buscarClientesComFiltros(filtros: {
  termo?: string;
  status?: string;
  tipo?: string;
}): Promise<ClienteComTotais[]> {
  try {
    let query = supabase
      .from('view_clientes_com_totais')
      .select('*');

    // Aplicar filtro de termo de busca (nome, email, cpf_cnpj)
    if (filtros.termo) {
      query = query.or(`nome.ilike.%${filtros.termo}%,email.ilike.%${filtros.termo}%,cpf_cnpj.ilike.%${filtros.termo}%`);
    }

    // Aplicar filtro de status
    if (filtros.status && filtros.status !== 'todos') {
      query = query.eq('status', filtros.status);
    }

    // Aplicar filtro de tipo
    if (filtros.tipo && filtros.tipo !== 'todos') {
      query = query.eq('tipo', filtros.tipo);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Erro ao buscar clientes com filtros:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Erro ao buscar clientes com filtros:', error);
    throw error;
  }
}

// Interface para filtros avançados
export interface FiltrosAvancados {
  status?: string[];
  tipo?: string[];
  valorMin?: number;
  valorMax?: number;
  dataInicio?: Date;
  dataFim?: Date;
  precatoriosMin?: number;
  precatoriosMax?: number;
}

// Buscar clientes com filtros avançados
export async function buscarClientesComFiltrosAvancados(filtros: FiltrosAvancados): Promise<ClienteComTotais[]> {
  try {
    console.log('Buscando clientes com filtros avançados:', filtros);

    let query = supabase
      .from('view_clientes_com_totais')
      .select('*');

    // Aplicar filtros de status (múltiplos)
    if (filtros.status && filtros.status.length > 0) {
      query = query.in('status', filtros.status);
    }

    // Aplicar filtros de tipo (múltiplos)
    if (filtros.tipo && filtros.tipo.length > 0) {
      query = query.in('tipo', filtros.tipo);
    }

    // Aplicar filtros de valor
    if (filtros.valorMin !== undefined) {
      query = query.gte('valor_total', filtros.valorMin);
    }

    if (filtros.valorMax !== undefined) {
      query = query.lte('valor_total', filtros.valorMax);
    }

    // Aplicar filtros de quantidade de precatórios
    if (filtros.precatoriosMin !== undefined) {
      query = query.gte('total_precatorios', filtros.precatoriosMin);
    }

    if (filtros.precatoriosMax !== undefined) {
      query = query.lte('total_precatorios', filtros.precatoriosMax);
    }

    // Aplicar filtros de data
    if (filtros.dataInicio !== undefined) {
      query = query.gte('data_cadastro', filtros.dataInicio.toISOString());
    }

    if (filtros.dataFim !== undefined) {
      // Ajustar para o final do dia
      const dataFim = new Date(filtros.dataFim);
      dataFim.setHours(23, 59, 59, 999);
      query = query.lte('data_cadastro', dataFim.toISOString());
    }

    const { data, error } = await query;

    if (error) {
      console.error('Erro ao buscar clientes com filtros avançados:', error);
      throw error;
    }

    console.log(`Encontrados ${data?.length || 0} clientes com os filtros aplicados`);
    return data || [];
  } catch (error) {
    console.error('Erro ao buscar clientes com filtros avançados:', error);
    throw error;
  }
}
