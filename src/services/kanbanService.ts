import { supabase } from '@/lib/supabase';
import { getCache, setCache, clearCache } from '@/services/cacheService';
import { StatusPrecatorio, buscarTodosStatus, buscarStatusPadrao } from './statusPrecatoriosService';
import { CustomView, UserViewPreference, buscarVisualizacoesDisponiveis, buscarPreferenciasVisualizacao, criarVisualizacaoPadrao } from './viewsService';

// Interface para representar uma coluna no Kanban
export interface KanbanColuna {
  id: string;
  nome: string;
  cor: string;
  tipo?: string;
  ordem: number;
  status_id: string;
  ativo?: boolean;
  created_at?: string;
  updated_at?: string;
}

// Interface para representar um cartão no Kanban
export interface KanbanCard {
  id: string;
  titulo: string;
  descricao?: string;
  status_id: string;
  tipo: 'PRECATORIO' | 'RPV';
  cliente_id?: string;
  cliente_nome?: string;
  valor?: number;
  data_vencimento?: string;
  created_at?: string;
  updated_at?: string;
}

export interface ColunaPermission {
  id: string;
  user_id: string;
  coluna_id: string;
  can_view: boolean;
  can_edit: boolean;
  can_delete: boolean;
  can_move_cards: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface KanbanColunaPersonalizada {
  id: string;
  nome: string;
  descricao?: string;
  cor: string;
  icone?: string;
  ordem: number;
  criado_por?: string;
  is_default: boolean;
  is_system: boolean;
  created_at?: string;
  updated_at?: string;
  is_deleted?: boolean;
  deleted_at?: string;
  status_id?: string;
}

// Função auxiliar para mapear status para colunas do kanban
const mapStatusParaColunas = (statusList: StatusPrecatorio[]): KanbanColuna[] => {
  return statusList.map(status => ({
    id: status.id,
    nome: status.nome,
    cor: status.cor,
    tipo: 'AMBOS', // Por padrão, todas as colunas aceitam precatórios e RPVs
    ordem: status.ordem,
    status_id: status.id,
    ativo: status.ativo
  }));
};

// Função para criar status padrão
export const criarStatusPadrao = async (): Promise<StatusPrecatorio[]> => {
  try {
    console.log('[KanbanService] Criando status padrão...');

    // Definir status padrão
    const statusPadrao = [
      { nome: 'Novo', codigo: 'novo', cor: '#3b82f6', ordem: 1 },
      { nome: 'Em Análise', codigo: 'analise', cor: '#8b5cf6', ordem: 2 },
      { nome: 'Em Andamento', codigo: 'em_andamento', cor: '#10b981', ordem: 3 },
      { nome: 'Aguardando', codigo: 'aguardando', cor: '#f59e0b', ordem: 4 },
      { nome: 'Concluído', codigo: 'concluido', cor: '#6366f1', ordem: 5 }
    ];

    const statusCriados: StatusPrecatorio[] = [];

    // Obter o usuário atual
    const { data: userData } = await supabase.auth.getUser();
    const userId = userData?.user?.id;

    // Criar cada status
    for (const status of statusPadrao) {
      try {
        const novoStatus = {
          nome: status.nome,
          codigo: status.codigo,
          cor: status.cor,
          is_default: status.codigo === 'novo', // O status "Novo" é o padrão
          is_system: true,
          ativo: true,
          ordem: status.ordem,
          visivel: true,
          kanban_coluna: true,
          created_by: userId
        };

        const { data, error } = await supabase
          .from('status_precatorios')
          .insert(novoStatus)
          .select()
          .single();

        if (error) {
          console.error(`[KanbanService] Erro ao criar status "${status.nome}":`, error);
        } else if (data) {
          console.log(`[KanbanService] Status "${status.nome}" criado com ID ${data.id}`);
          statusCriados.push(data);
        }
      } catch (error) {
        console.error(`[KanbanService] Erro ao criar status "${status.nome}":`, error);
      }
    }

    return statusCriados;
  } catch (error) {
    console.error('[KanbanService] Erro ao criar status padrão:', error);
    return [];
  }
};

// Função para buscar todas as colunas do Kanban
export const fetchAllColunas = async (): Promise<KanbanColuna[]> => {
  try {
    console.log('[KanbanService] Buscando todas as colunas do Kanban...');

    // Buscar todos os status ativos e visíveis
    const statusList = await buscarTodosStatus(false);

    if (!statusList || statusList.length === 0) {
      console.log('[KanbanService] Nenhum status encontrado. Criando status padrão...');
      const statusCriados = await criarStatusPadrao();
      return mapStatusParaColunas(statusCriados);
    }

    return mapStatusParaColunas(statusList);
  } catch (error) {
    console.error('[KanbanService] Erro ao buscar colunas do Kanban:', error);
    return [];
  }
};

// Função para buscar colunas visíveis para o usuário atual
export const fetchVisibleColunas = async (): Promise<KanbanColuna[]> => {
  try {
    // Verificar se há dados em cache
    const cacheKey = 'kanban_colunas_visiveis';
    const cachedData = getCache<KanbanColuna[]>(cacheKey);

    if (cachedData) {
      console.log('[KanbanService] Usando dados em cache para colunas visíveis do Kanban');
      return cachedData;
    }

    // Obter o usuário atual
    const { data: userData } = await supabase.auth.getUser();
    if (!userData.user) {
      console.warn('[KanbanService] Usuário não autenticado');
      throw new Error('Usuário não autenticado');
    }

    const userId = userData.user.id;

    // Verificar se o usuário é admin
    const { data: userProfile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', userId)
      .single();

    // Admins podem ver todas as colunas
    if (userProfile?.role === 'admin') {
      const colunas = await fetchAllColunas();
      // Armazenar em cache por 1 minuto
      setCache(cacheKey, colunas, 60000);
      return colunas;
    }

    // Buscar preferências de visualização do usuário
    const preferencias = await buscarPreferenciasVisualizacao();

    // Se não tem preferências, usar todas as colunas
    if (!preferencias || preferencias.length === 0) {
      console.log('[KanbanService] Usuário não tem preferências de visualização. Usando todas as colunas.');
      const colunas = await fetchAllColunas();
      setCache(cacheKey, colunas, 60000);
      return colunas;
    }

    // Buscar a visualização padrão
    const visualizacaoPadrao = preferencias.find(p => p.is_default);

    if (!visualizacaoPadrao) {
      console.log('[KanbanService] Usuário não tem visualização padrão. Usando todas as colunas.');
      const colunas = await fetchAllColunas();
      setCache(cacheKey, colunas, 60000);
      return colunas;
    }

    // Buscar todos os status
    const todosStatus = await buscarTodosStatus(true);

    // Filtrar status conforme preferências do usuário
    const statusFiltrados = visualizacaoPadrao.status_ids && visualizacaoPadrao.status_ids.length > 0
      ? todosStatus.filter(s => visualizacaoPadrao.status_ids.includes(s.id))
      : todosStatus;

    // Mapear para colunas
    const colunas = mapStatusParaColunas(statusFiltrados);

    // Armazenar em cache por 1 minuto
    setCache(cacheKey, colunas, 60000);

    return colunas;
  } catch (error) {
    console.error('[KanbanService] Erro ao buscar colunas visíveis:', error);
    // Retornar array vazio em vez de lançar erro para evitar quebrar a UI
    return [];
  }
};

// Função para buscar todas as colunas personalizadas
export const fetchAllColunasPersonalizadas = async (): Promise<KanbanColunaPersonalizada[]> => {
  try {
    console.log('[KanbanService] Buscando todas as colunas personalizadas do Kanban...');

    // Buscar todos os status ativos
    const statusList = await buscarTodosStatus(true);

    // Mapear status para colunas personalizadas
    const colunas: KanbanColunaPersonalizada[] = statusList
      .filter(status => status.kanban_coluna)
      .map(status => ({
        id: status.id,
        nome: status.nome,
        cor: status.cor,
        ordem: status.ordem,
        is_default: status.is_default,
        is_system: status.is_system,
        is_deleted: !status.visivel,
        status_id: status.id
      }));

    console.log(`[KanbanService] Encontradas ${colunas.length} colunas personalizadas`);
    return colunas;
  } catch (error) {
    console.error('[KanbanService] Erro ao buscar colunas personalizadas do Kanban:', error);
    return []; // Retornar array vazio em vez de lançar erro
  }
};

// Função para criar uma nova coluna
export const createColuna = async (coluna: Omit<KanbanColunaPersonalizada, 'id' | 'created_at' | 'updated_at'>): Promise<KanbanColunaPersonalizada> => {
  try {
    console.log('[KanbanService] Criando nova coluna:', coluna.nome);

    const { data: userData } = await supabase.auth.getUser();
    if (!userData.user) throw new Error('Usuário não autenticado');

    // Gerar código para o status a partir do nome
    const codigo = coluna.nome
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '_');

    // Criar um novo status
    const novoStatus = {
      nome: coluna.nome,
      codigo,
      cor: coluna.cor,
      is_default: coluna.is_default || false,
      is_system: coluna.is_system || false,
      ativo: true,
      ordem: coluna.ordem,
      visivel: true,
      kanban_coluna: true,
      created_by: userData.user.id
    };

    // Inserir o status
    const { data, error } = await supabase
      .from('status_precatorios')
      .insert(novoStatus)
      .select()
      .single();

    if (error) {
      console.error('[KanbanService] Erro ao criar status para coluna:', error);
      throw error;
    }

    // Mapear o status para coluna personalizada
    const colunaPersonalizada: KanbanColunaPersonalizada = {
      id: data.id,
      nome: data.nome,
      cor: data.cor,
      ordem: data.ordem,
      is_default: data.is_default,
      is_system: data.is_system,
      criado_por: userData.user.id,
      status_id: data.id
    };

    // Limpar cache
    clearCache('kanban_colunas_');

    return colunaPersonalizada;
  } catch (error) {
    console.error('[KanbanService] Erro ao criar coluna:', error);
    throw error;
  }
};

// Função para atualizar uma coluna existente
export const updateColuna = async (id: string, coluna: Partial<KanbanColuna>): Promise<KanbanColuna> => {
  try {
    console.log('[KanbanService] Atualizando coluna:', id);

    // Verificar se o status existe
    const { data: statusAtual, error: checkError } = await supabase
      .from('status_precatorios')
      .select('*')
      .eq('id', id)
      .single();

    if (checkError) {
      console.error('[KanbanService] Erro ao verificar status:', checkError);
      throw checkError;
    }

    // Não permitir alteração de status do sistema
    if (statusAtual.is_system) {
      console.warn('[KanbanService] Tentativa de modificar status do sistema');
      throw new Error('Não é possível modificar colunas do sistema');
    }

    // Preparar dados para atualização
    const dadosAtualizacao: any = {};

    if (coluna.nome) dadosAtualizacao.nome = coluna.nome;
    if (coluna.cor) dadosAtualizacao.cor = coluna.cor;
    if (coluna.ordem !== undefined) dadosAtualizacao.ordem = coluna.ordem;

    // Atualizar o status
    const { data, error } = await supabase
      .from('status_precatorios')
      .update({
        ...dadosAtualizacao,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('[KanbanService] Erro ao atualizar status:', error);
      throw error;
    }

    // Limpar cache
    clearCache('kanban_colunas_');

    // Mapear para o formato de coluna
    return {
      id: data.id,
      nome: data.nome,
      cor: data.cor,
      tipo: 'AMBOS',
      ordem: data.ordem,
      status_id: data.id,
      ativo: data.ativo
    };
  } catch (error) {
    console.error('[KanbanService] Erro ao atualizar coluna:', error);
    throw error;
  }
};

// Função para excluir uma coluna
export const deleteColuna = async (id: string): Promise<void> => {
  try {
    console.log('[KanbanService] Excluindo coluna:', id);

    // Verificar se o status existe
    const { data: statusAtual, error: checkError } = await supabase
      .from('status_precatorios')
      .select('*')
      .eq('id', id)
      .single();

    if (checkError) {
      console.error('[KanbanService] Erro ao verificar status:', checkError);
      throw checkError;
    }

    // Não permitir exclusão de status do sistema ou padrão
    if (statusAtual.is_system || statusAtual.is_default) {
      console.warn('[KanbanService] Tentativa de excluir status do sistema ou padrão');
      throw new Error('Não é possível excluir colunas do sistema ou padrão');
    }

    // Marcar como invisível e inativo em vez de remover do banco de dados
    const { error } = await supabase
      .from('status_precatorios')
      .update({
        visivel: false,
        kanban_coluna: false,
        updated_at: new Date().toISOString()
      })
      .eq('id', id);

    if (error) {
      console.error('[KanbanService] Erro ao marcar status como invisível:', error);
      throw error;
    }

    // Limpar cache
    clearCache('kanban_colunas_');

    console.log('[KanbanService] Coluna excluída com sucesso');
  } catch (error) {
    console.error('[KanbanService] Erro ao excluir coluna:', error);
    throw error;
  }
};

// Função para reordenar colunas
export const reordenarColunas = async (colunasOrdenadas: { id: string; ordem: number }[]): Promise<void> => {
  try {
    console.log('[KanbanService] Reordenando colunas:', colunasOrdenadas);

    // Atualizar a ordem de cada status
    for (const coluna of colunasOrdenadas) {
      const { error } = await supabase
        .from('status_precatorios')
        .update({
          ordem: coluna.ordem,
          updated_at: new Date().toISOString()
        })
        .eq('id', coluna.id);

      if (error) {
        console.error(`[KanbanService] Erro ao atualizar ordem do status ${coluna.id}:`, error);
        throw error;
      }
    }

    // Limpar cache
    clearCache('kanban_colunas_');

    console.log('[KanbanService] Colunas reordenadas com sucesso');
  } catch (error) {
    console.error('[KanbanService] Erro ao reordenar colunas:', error);
    throw error;
  }
};

// Função para obter permissões de uma coluna
export const getColunaPermissions = async (colunaId: string): Promise<ColunaPermission[]> => {
  try {
    const { data, error } = await supabase
      .from('kanban_coluna_permissions')
      .select(`
        *,
        profiles:user_id (id, nome, email, avatar_url)
      `)
      .eq('coluna_id', colunaId);

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Erro ao obter permissões da coluna:', error);
    throw error;
  }
};

// Função para atualizar permissões de uma coluna
export const updateColunaPermission = async (
  colunaId: string,
  userId: string,
  permissions: {
    can_view?: boolean;
    can_edit?: boolean;
    can_delete?: boolean;
    can_move_cards?: boolean;
  }
): Promise<void> => {
  try {
    // Verificar se já existe uma permissão para este usuário e coluna
    const { data: existingPermission, error: checkError } = await supabase
      .from('kanban_coluna_permissions')
      .select('id')
      .eq('coluna_id', colunaId)
      .eq('user_id', userId)
      .maybeSingle();

    if (checkError) throw checkError;

    if (existingPermission) {
      // Atualizar permissão existente
      const { error } = await supabase
        .from('kanban_coluna_permissions')
        .update({
          ...permissions,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingPermission.id);

      if (error) throw error;
    } else {
      // Criar nova permissão
      const { error } = await supabase
        .from('kanban_coluna_permissions')
        .insert({
          coluna_id: colunaId,
          user_id: userId,
          can_view: permissions.can_view ?? false,
          can_edit: permissions.can_edit ?? false,
          can_delete: permissions.can_delete ?? false,
          can_move_cards: permissions.can_move_cards ?? false
        });

      if (error) throw error;
    }
  } catch (error) {
    console.error('Erro ao atualizar permissão da coluna:', error);
    throw error;
  }
};

// Função para verificar se o usuário tem permissão para uma coluna
export const checkColunaPermission = async (
  colunaId: string,
  permission: 'view' | 'edit' | 'delete' | 'move_cards'
): Promise<boolean> => {
  try {
    const { data: userData } = await supabase.auth.getUser();
    if (!userData.user) return false;

    const userId = userData.user.id;

    // Verificar se o usuário é admin
    const { data: userProfile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', userId)
      .single();

    // Admins têm todas as permissões
    if (userProfile?.role === 'admin') return true;

    // Verificar se o usuário é o criador da coluna
    const { data: coluna, error: colunaError } = await supabase
      .from('kanban_colunas_personalizadas')
      .select('criado_por, is_default, is_system')
      .eq('id', colunaId)
      .single();

    if (colunaError) throw colunaError;

    // Se o usuário é o criador, tem todas as permissões
    if (coluna.criado_por === userId) return true;

    // Colunas padrão podem ser vistas por todos
    if (coluna.is_default && permission === 'view') return true;

    // Verificar permissão específica
    const { data, error } = await supabase
      .from('kanban_coluna_permissions')
      .select(
        permission === 'view' ? 'can_view' :
        permission === 'edit' ? 'can_edit' :
        permission === 'delete' ? 'can_delete' : 'can_move_cards'
      )
      .eq('coluna_id', colunaId)
      .eq('user_id', userId)
      .single();

    if (error) return false;

    return data[
      permission === 'view' ? 'can_view' :
      permission === 'edit' ? 'can_edit' :
      permission === 'delete' ? 'can_delete' : 'can_move_cards'
    ] || false;
  } catch (error) {
    console.error('Erro ao verificar permissão da coluna:', error);
    return false;
  }
};

// Função para mover um precatório para uma coluna
export const moverPrecatorioParaColuna = async (
  precatorioId: string,
  colunaId: string,
  posicao: number
): Promise<void> => {
  try {
    const { data: userData } = await supabase.auth.getUser();
    if (!userData.user) throw new Error('Usuário não autenticado');

    // Verificar se o usuário tem permissão para mover cards nesta coluna
    const temPermissao = await checkColunaPermission(colunaId, 'move_cards');
    if (!temPermissao) {
      throw new Error('Você não tem permissão para mover precatórios para esta coluna');
    }

    // Verificar se já existe uma relação entre este precatório e coluna
    const { data: existingRelation, error: checkError } = await supabase
      .from('precatorios_colunas')
      .select('id')
      .eq('precatorio_id', precatorioId)
      .eq('coluna_id', colunaId)
      .maybeSingle();

    if (checkError) throw checkError;

    if (existingRelation) {
      // Atualizar posição
      const { error } = await supabase
        .from('precatorios_colunas')
        .update({
          posicao,
          movido_por: userData.user.id,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingRelation.id);

      if (error) throw error;
    } else {
      // Remover o precatório de qualquer outra coluna
      const { error: deleteError } = await supabase
        .from('precatorios_colunas')
        .delete()
        .eq('precatorio_id', precatorioId);

      if (deleteError) throw deleteError;

      // Criar nova relação
      const { error } = await supabase
        .from('precatorios_colunas')
        .insert({
          precatorio_id: precatorioId,
          coluna_id: colunaId,
          posicao,
          movido_por: userData.user.id
        });

      if (error) throw error;
    }
  } catch (error) {
    console.error('Erro ao mover precatório para coluna:', error);
    throw error;
  }
};

// Função para obter precatórios de uma coluna
export const getPrecatoriosDaColuna = async (colunaId: string): Promise<any[]> => {
  try {
    const { data, error } = await supabase
      .from('precatorios_colunas')
      .select(`
        precatorio_id,
        posicao,
        precatorios:precatorio_id (*)
      `)
      .eq('coluna_id', colunaId)
      .order('posicao');

    if (error) throw error;
    return data?.map(item => ({
      ...item.precatorios,
      posicao: item.posicao
    })) || [];
  } catch (error) {
    console.error('Erro ao obter precatórios da coluna:', error);
    throw error;
  }
};

// Função para buscar visualizações personalizadas do usuário
export const fetchUserViews = async (): Promise<CustomView[]> => {
  try {
    // Verificar se há dados em cache
    const cacheKey = 'custom_views';
    const cachedData = getCache<CustomView[]>(cacheKey);

    if (cachedData) {
      console.log('Usando dados em cache para visualizações personalizadas');
      return cachedData;
    }

    const { data: userData } = await supabase.auth.getUser();
    if (!userData.user) {
      console.warn('Usuário não autenticado ao buscar visualizações');
      // Criar visualizações padrão para usuários não autenticados
      const defaultViews: CustomView[] = [
        {
          id: 'default-view-1',
          nome: 'Todos os Precatórios e RPVs',
          descricao: 'Visualização padrão',
          user_id: 'system',
          is_public: true,
          layout: 'kanban',
          is_default: true,
          is_favorite: true,
          icone: 'layout',
          cor: '#3b82f6'
        }
      ];
      return defaultViews;
    }

    console.log('Buscando visualizações para o usuário:', userData.user.id);

    try {
      const { data, error } = await supabase.rpc('get_user_views', {
        p_user_id: userData.user.id
      });

      if (error) {
        console.error('Erro ao chamar RPC get_user_views:', error);

        // Tentar fallback direto na tabela
        console.log('Tentando fallback direto na tabela custom_views');
        const { data: fallbackData, error: fallbackError } = await supabase
          .from('custom_views')
          .select('*')
          .or(`user_id.eq.${userData.user.id},is_public.eq.true`)
          .order('is_favorite', { ascending: false })
          .order('created_at', { ascending: false });

        if (fallbackError) {
          console.error('Erro no fallback de visualizações:', fallbackError);
          // Criar visualizações padrão como último recurso
          const defaultViews: CustomView[] = [
            {
              id: 'default-view-1',
              nome: 'Todos os Precatórios e RPVs',
              descricao: 'Visualização padrão',
              user_id: userData.user.id,
              is_public: true,
              layout: 'kanban',
              is_default: true,
              is_favorite: true,
              icone: 'layout',
              cor: '#3b82f6'
            }
          ];
          return defaultViews;
        }

        // Armazenar em cache por 1 minuto
        if (fallbackData && fallbackData.length > 0) {
          setCache(cacheKey, fallbackData, 60000);
          return fallbackData;
        } else {
          // Se não encontrou visualizações, criar uma padrão
          const defaultViews: CustomView[] = [
            {
              id: 'default-view-1',
              nome: 'Todos os Precatórios e RPVs',
              descricao: 'Visualização padrão',
              user_id: userData.user.id,
              is_public: true,
              layout: 'kanban',
              is_default: true,
              is_favorite: true,
              icone: 'layout',
              cor: '#3b82f6'
            }
          ];
          return defaultViews;
        }
      }

      // Armazenar em cache por 1 minuto
      if (data && data.length > 0) {
        setCache(cacheKey, data, 60000);
        return data;
      } else {
        // Se não encontrou visualizações, criar uma padrão
        const defaultViews: CustomView[] = [
          {
            id: 'default-view-1',
            nome: 'Todos os Precatórios e RPVs',
            descricao: 'Visualização padrão',
            user_id: userData.user.id,
            is_public: true,
            layout: 'kanban',
            is_default: true,
            is_favorite: true,
            icone: 'layout',
            cor: '#3b82f6'
          }
        ];
        return defaultViews;
      }
    } catch (innerError) {
      console.error('Erro ao buscar visualizações:', innerError);
      // Criar visualizações padrão como último recurso
      const defaultViews: CustomView[] = [
        {
          id: 'default-view-1',
          nome: 'Todos os Precatórios e RPVs',
          descricao: 'Visualização padrão',
          user_id: userData.user.id,
          is_public: true,
          layout: 'kanban',
          is_default: true,
          is_favorite: true,
          icone: 'layout',
          cor: '#3b82f6'
        }
      ];
      return defaultViews;
    }
  } catch (error) {
    console.error('Erro ao buscar visualizações do usuário:', error);
    // Criar visualizações padrão como último recurso
    const defaultViews: CustomView[] = [
      {
        id: 'default-view-fallback',
        nome: 'Todos os Precatórios e RPVs',
        descricao: 'Visualização padrão',
        user_id: 'system',
        is_public: true,
        layout: 'kanban',
        is_default: true,
        is_favorite: true,
        icone: 'layout',
        cor: '#3b82f6'
      }
    ];
    return defaultViews;
  }
};

// Função para criar colunas personalizadas padrão
export const criarColunasPersonalizadasPadrao = async (): Promise<string[]> => {
  try {
    console.log('[KanbanService] Verificando se é necessário criar colunas personalizadas padrão...');

    // Verificar se já existem colunas personalizadas
    const { data: colunasExistentes, error: errorCheck } = await supabase
      .from('kanban_colunas_personalizadas')
      .select('id, nome')
      .eq('is_deleted', false);

    if (errorCheck) {
      console.error('[KanbanService] Erro ao verificar colunas personalizadas existentes:', errorCheck);
      return [];
    }

    // Se já existem colunas, retornar seus IDs
    if (colunasExistentes && colunasExistentes.length > 0) {
      console.log(`[KanbanService] Já existem ${colunasExistentes.length} colunas personalizadas. Não é necessário criar padrões.`);
      console.log('[KanbanService] Colunas existentes:', colunasExistentes.map(c => `${c.nome} (${c.id})`));
      return colunasExistentes.map(c => c.id);
    }

    console.log('[KanbanService] Nenhuma coluna personalizada encontrada. Criando colunas padrão...');

    // Buscar status existentes para associar às colunas
    let statusListFinal: any[] = [];

    try {
      const { data: statusList, error: statusError } = await supabase
        .from('status_precatorios')
        .select('id, nome, codigo, cor');

      if (statusError) {
        console.error('[KanbanService] Erro ao buscar status para criar colunas padrão:', statusError);
        return [];
      }

      // Inicializar com os status existentes
      statusListFinal = statusList || [];

      console.log(`[KanbanService] Encontrados ${statusListFinal.length} status para associar às colunas.`);
    } catch (statusFetchError) {
      console.error('[KanbanService] Erro ao buscar status:', statusFetchError);
    }

    // Se não existem status, criar status padrão
    if (statusListFinal.length === 0) {
      console.log('[KanbanService] Nenhum status encontrado. Criando status padrão...');

      // Definir status padrão
      const statusPadrao = [
        { nome: 'Novo', codigo: 'novo', cor: '#3b82f6' },
        { nome: 'Em Análise', codigo: 'analise', cor: '#8b5cf6' },
        { nome: 'Em Andamento', codigo: 'em_andamento', cor: '#10b981' },
        { nome: 'Aguardando', codigo: 'aguardando', cor: '#f59e0b' },
        { nome: 'Concluído', codigo: 'concluido', cor: '#6366f1' }
      ];

      // Criar status padrão
      for (const status of statusPadrao) {
        try {
          const { data: statusData, error: statusInsertError } = await supabase
            .from('status_precatorios')
            .insert({
              nome: status.nome,
              codigo: status.codigo,
              cor: status.cor,
              ativo: true,
              is_default: true,
              is_system: true,
              created_at: new Date().toISOString()
            })
            .select('id')
            .single();

          if (statusInsertError) {
            console.error(`[KanbanService] Erro ao criar status padrão "${status.nome}":`, statusInsertError);
          } else if (statusData) {
            console.log(`[KanbanService] Status padrão "${status.nome}" criado com ID ${statusData.id}`);
          }
        } catch (insertError) {
          console.error(`[KanbanService] Erro ao inserir status "${status.nome}":`, insertError);
        }
      }

      // Buscar status novamente após criar os padrões
      try {
        const { data: updatedStatusList } = await supabase
          .from('status_precatorios')
          .select('id, nome, codigo, cor')
          .eq('ativo', true);

        if (updatedStatusList && updatedStatusList.length > 0) {
          console.log(`[KanbanService] Agora temos ${updatedStatusList.length} status disponíveis.`);
          statusListFinal = updatedStatusList;
        }
      } catch (refetchError) {
        console.error('[KanbanService] Erro ao buscar status atualizados:', refetchError);
      }
    }

    // Definir colunas padrão
    const colunasPadrao = [
      { nome: 'Novo', cor: '#3b82f6', ordem: 1, is_default: true, is_system: true, codigo_esperado: 'novo' },
      { nome: 'Em Análise', cor: '#8b5cf6', ordem: 2, is_default: true, is_system: true, codigo_esperado: 'analise' },
      { nome: 'Em Andamento', cor: '#10b981', ordem: 3, is_default: true, is_system: true, codigo_esperado: 'em_andamento' },
      { nome: 'Aguardando', cor: '#f59e0b', ordem: 4, is_default: true, is_system: true, codigo_esperado: 'aguardando' },
      { nome: 'Concluído', cor: '#6366f1', ordem: 5, is_default: true, is_system: true, codigo_esperado: 'concluido' }
    ];

    // Obter o usuário atual para definir como criador das colunas
    const { data: userData } = await supabase.auth.getUser();
    const userId = userData?.user?.id || '00000000-0000-0000-0000-000000000000'; // ID padrão para sistema

    // Função auxiliar para remover acentos
    const removerAcentos = (texto: string): string => {
      return texto.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
    };

    // Array para armazenar os IDs das colunas criadas
    const colunasIds: string[] = [];

    // Criar as colunas padrão
    for (const coluna of colunasPadrao) {
      // Encontrar um status correspondente pelo nome ou código esperado
      const statusCorrespondente = statusListFinal.find(s =>
        removerAcentos(s.nome.toLowerCase()) === removerAcentos(coluna.nome.toLowerCase()) ||
        s.codigo === coluna.codigo_esperado ||
        s.codigo.includes(removerAcentos(coluna.nome.toLowerCase()).replace(/\s+/g, '_'))
      );

      if (statusCorrespondente) {
        console.log(`[KanbanService] Encontrado status correspondente para coluna "${coluna.nome}": ${statusCorrespondente.nome} (${statusCorrespondente.id})`);
      } else {
        console.log(`[KanbanService] Não foi encontrado status correspondente para coluna "${coluna.nome}"`);
      }

      // Criar a coluna personalizada
      const { data: colunaData, error: insertError } = await supabase
        .from('kanban_colunas_personalizadas')
        .insert({
          ...coluna,
          criado_por: userId,
          is_deleted: false,
          status_id: statusCorrespondente?.id || null,
          created_at: new Date().toISOString()
        })
        .select('id')
        .single();

      if (insertError) {
        console.error(`[KanbanService] Erro ao criar coluna padrão "${coluna.nome}":`, insertError);
      } else if (colunaData) {
        console.log(`[KanbanService] Coluna padrão "${coluna.nome}" criada com ID ${colunaData.id}`);
        colunasIds.push(colunaData.id);
      }
    }

    console.log('[KanbanService] Colunas padrão criadas com sucesso:', colunasIds);

    // Atualizar visualizações existentes para usar as novas colunas
    if (colunasIds.length > 0) {
      await atualizarVisualizacoesComNovasColunas(colunasIds);
    }

    return colunasIds;
  } catch (error) {
    console.error('[KanbanService] Erro ao criar colunas personalizadas padrão:', error);
    return [];
  }
};

// Função para atualizar visualizações existentes com novas colunas
async function atualizarVisualizacoesComNovasColunas(colunasIds: string[]): Promise<void> {
  try {
    console.log('[KanbanService] Atualizando visualizações existentes com novas colunas...');

    // Buscar todas as visualizações
    const { data: visualizacoes, error: viewsError } = await supabase
      .from('custom_views')
      .select('id, nome, colunas_selecionadas');

    if (viewsError) {
      console.error('[KanbanService] Erro ao buscar visualizações:', viewsError);
      return;
    }

    if (!visualizacoes || visualizacoes.length === 0) {
      console.log('[KanbanService] Nenhuma visualização encontrada para atualizar.');
      return;
    }

    console.log(`[KanbanService] Encontradas ${visualizacoes.length} visualizações para atualizar.`);

    // Atualizar cada visualização
    for (const view of visualizacoes) {
      // Verificar se a visualização já tem colunas selecionadas
      const colunasAtuais = view.colunas_selecionadas || [];

      // Se não tem colunas ou as colunas não existem mais, atualizar com as novas
      if (colunasAtuais.length === 0) {
        console.log(`[KanbanService] Atualizando visualização "${view.nome}" com novas colunas.`);

        const { error: updateError } = await supabase
          .from('custom_views')
          .update({
            colunas_selecionadas: colunasIds,
            updated_at: new Date().toISOString()
          })
          .eq('id', view.id);

        if (updateError) {
          console.error(`[KanbanService] Erro ao atualizar visualização "${view.nome}":`, updateError);
        } else {
          console.log(`[KanbanService] Visualização "${view.nome}" atualizada com sucesso.`);
        }
      } else {
        console.log(`[KanbanService] Visualização "${view.nome}" já tem colunas selecionadas.`);
      }
    }
  } catch (error) {
    console.error('[KanbanService] Erro ao atualizar visualizações:', error);
  }
}

// Função para criar uma nova visualização personalizada
export const createCustomView = async (view: Omit<CustomView, 'id' | 'created_at' | 'updated_at'>): Promise<CustomView> => {
  try {
    const { data: userData } = await supabase.auth.getUser();
    if (!userData.user) throw new Error('Usuário não autenticado');

    const { data, error } = await supabase
      .from('custom_views')
      .insert({
        ...view,
        user_id: userData.user.id
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Erro ao criar visualização personalizada:', error);
    throw error;
  }
};

// Função para atualizar uma visualização personalizada
export const updateCustomView = async (id: string, view: Partial<CustomView>): Promise<CustomView> => {
  try {
    const { data: userData } = await supabase.auth.getUser();
    if (!userData.user) throw new Error('Usuário não autenticado');

    // Verificar se o usuário é o dono da visualização
    const { data: viewData, error: viewError } = await supabase
      .from('custom_views')
      .select('user_id')
      .eq('id', id)
      .single();

    if (viewError) throw viewError;

    // Verificar se o usuário é admin
    const { data: userProfile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', userData.user.id)
      .single();

    // Apenas o dono ou um admin pode atualizar a visualização
    if (viewData.user_id !== userData.user.id && userProfile?.role !== 'admin') {
      throw new Error('Você não tem permissão para atualizar esta visualização');
    }

    const { data, error } = await supabase
      .from('custom_views')
      .update({
        ...view,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Erro ao atualizar visualização personalizada:', error);
    throw error;
  }
};

// Função para excluir uma visualização personalizada
export const deleteCustomView = async (id: string): Promise<void> => {
  try {
    const { data: userData } = await supabase.auth.getUser();
    if (!userData.user) throw new Error('Usuário não autenticado');

    // Verificar se o usuário é o dono da visualização
    const { data: viewData, error: viewError } = await supabase
      .from('custom_views')
      .select('user_id, is_default')
      .eq('id', id)
      .single();

    if (viewError) throw viewError;

    // Não permitir exclusão de visualizações padrão
    if (viewData.is_default) {
      throw new Error('Não é possível excluir visualizações padrão');
    }

    // Verificar se o usuário é admin
    const { data: userProfile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', userData.user.id)
      .single();

    // Apenas o dono ou um admin pode excluir a visualização
    if (viewData.user_id !== userData.user.id && userProfile?.role !== 'admin') {
      throw new Error('Você não tem permissão para excluir esta visualização');
    }

    const { error } = await supabase
      .from('custom_views')
      .delete()
      .eq('id', id);

    if (error) throw error;
  } catch (error) {
    console.error('Erro ao excluir visualização personalizada:', error);
    throw error;
  }
};

// Função para obter precatórios com base em uma visualização
export const getPrecatoriosByView = async (viewId: string): Promise<any[]> => {
  try {
    console.log(`[KanbanService] Iniciando busca de precatórios para visualização ${viewId}...`);

    // Verificar autenticação
    const { data: userData } = await supabase.auth.getUser();
    if (!userData.user) {
      console.error('[KanbanService] Usuário não autenticado');
      throw new Error('Usuário não autenticado');
    }

    // Buscar a visualização
    const visualizacao = await buscarVisualizacaoPorId(viewId);
    if (!visualizacao) {
      console.error(`[KanbanService] Visualização ${viewId} não encontrada`);
      throw new Error(`Visualização ${viewId} não encontrada`);
    }

    console.log('[KanbanService] Visualização encontrada:', visualizacao.nome);

    // Buscar preferências do usuário para esta visualização
    const preferencias = await buscarPreferenciasVisualizacao();
    const preferencia = preferencias.find(p => p.view_id === viewId);

    // Construir a consulta base
    let query = supabase
      .from('precatorios')
      .select(`
        *,
        cliente:cliente_id (id, nome, cpf, email, telefone),
        status:status_id (id, nome, codigo, cor)
      `)
      .or('is_deleted.is.null,is_deleted.eq.false');

    // Aplicar filtros de tags se existirem
    if (visualizacao.tags_selecionadas && visualizacao.tags_selecionadas.length > 0) {
      console.log('[KanbanService] Aplicando filtro de tags:', visualizacao.tags_selecionadas);
      query = query.overlaps('tags', visualizacao.tags_selecionadas);
    }

    // Aplicar filtros adicionais se existirem
    if (visualizacao.filtros) {
      console.log('[KanbanService] Aplicando filtros adicionais:', visualizacao.filtros);
      // Implementar lógica para aplicar filtros personalizados
    }

    // Verificar se há status selecionados na preferência do usuário
    if (preferencia && preferencia.status_ids && preferencia.status_ids.length > 0) {
      console.log('[KanbanService] Filtrando por status_ids da preferência do usuário:', preferencia.status_ids);
      query = query.in('status_id', preferencia.status_ids);
    }
    // Ou verificar se há status selecionados na visualização
    else if (visualizacao.status_ids && visualizacao.status_ids.length > 0) {
      console.log('[KanbanService] Filtrando por status_ids da visualização:', visualizacao.status_ids);
      query = query.in('status_id', visualizacao.status_ids);
    }

    // Executar a consulta
    console.log('[KanbanService] Executando consulta final de precatórios...');
    const { data, error } = await query.order('created_at', { ascending: false });

    if (error) {
      console.error('[KanbanService] Erro na consulta de precatórios:', error);
      return [];
    }

    console.log(`[KanbanService] Encontrados ${data?.length || 0} precatórios para a visualização`);

    if (!data || data.length === 0) {
      console.log('[KanbanService] Nenhum precatório encontrado, retornando array vazio');
      return [];
    }

    // Verificar se os precatórios têm status_id válido
    const precatoriosSemStatus = data.filter(p => !p.status_id);
    if (precatoriosSemStatus.length > 0) {
      console.log(`[KanbanService] ${precatoriosSemStatus.length} precatórios sem status_id, buscando status padrão...`);

      // Buscar status padrão
      const statusPadrao = await buscarStatusPadrao();

      if (statusPadrao) {
        console.log(`[KanbanService] Status padrão encontrado: ${statusPadrao.id}`);

        // Atualizar precatórios sem status
        for (const precatorio of precatoriosSemStatus) {
          console.log(`[KanbanService] Atualizando precatório ${precatorio.id} com status padrão`);

          // Atualizar no banco de dados
          await supabase
            .from('precatorios')
            .update({ status_id: statusPadrao.id })
            .eq('id', precatorio.id);

          // Atualizar no objeto local
          precatorio.status_id = statusPadrao.id;
        }
      }
    }

    return data;
  } catch (error) {
    console.error('[KanbanService] Erro ao obter precatórios por visualização:', error);
    return [];
  }
};
