import { supabase } from "@/lib/supabase";

export interface SearchResult {
  id: string;
  title: string;
  subtitle?: string;
  type: 'cliente' | 'precatorio' | 'rpv' | 'tarefa';
  icon?: string;
  url: string;
}

export async function searchGlobal(query: string): Promise<SearchResult[]> {
  if (!query || query.trim().length < 2) {
    return [];
  }

  const searchTerm = query.trim().toLowerCase();
  const results: SearchResult[] = [];

  try {
    // Buscar em paralelo para melhor performance
    const [clientesPromise, precatoriosPromise, rpvsPromise, tarefasPromise] = await Promise.allSettled([
      // Buscar clientes
      supabase
        .from('clientes')
        .select('id, nome, email, cpf_cnpj, telefone')
        .or(`nome.ilike.%${searchTerm}%,email.ilike.%${searchTerm}%,cpf_cnpj.ilike.%${searchTerm}%,telefone.ilike.%${searchTerm}%`)
        .limit(5),

      // Buscar precatórios
      supabase
        .from('precatorios')
        .select(`
          id, 
          numero, 
          valor_causa, 
          tribunal,
          cliente:cliente_id (nome),
          tipo
        `)
        .eq('tipo', 'PRECATORIO')
        .or(`numero.ilike.%${searchTerm}%,tribunal.ilike.%${searchTerm}%`)
        .limit(5),

      // Buscar RPVs
      supabase
        .from('precatorios')
        .select(`
          id, 
          numero, 
          valor_causa, 
          tribunal,
          cliente:cliente_id (nome),
          tipo
        `)
        .eq('tipo', 'RPV')
        .or(`numero.ilike.%${searchTerm}%,tribunal.ilike.%${searchTerm}%`)
        .limit(5),

      // Buscar tarefas
      supabase
        .from('tarefas')
        .select('id, titulo, descricao, status')
        .or(`titulo.ilike.%${searchTerm}%,descricao.ilike.%${searchTerm}%`)
        .limit(5)
    ]);

    // Processar resultados de clientes
    if (clientesPromise.status === 'fulfilled' && clientesPromise.value.data) {
      clientesPromise.value.data.forEach(cliente => {
        results.push({
          id: cliente.id,
          title: cliente.nome,
          subtitle: cliente.cpf_cnpj || cliente.email,
          type: 'cliente',
          url: `/customers/${cliente.id}`
        });
      });
    }

    // Processar resultados de precatórios
    if (precatoriosPromise.status === 'fulfilled' && precatoriosPromise.value.data) {
      precatoriosPromise.value.data.forEach(precatorio => {
        results.push({
          id: precatorio.id,
          title: `Precatório: ${precatorio.numero}`,
          subtitle: precatorio.cliente?.nome || 'Cliente não especificado',
          type: 'precatorio',
          url: `/precatorios/${precatorio.id}`
        });
      });
    }

    // Processar resultados de RPVs
    if (rpvsPromise.status === 'fulfilled' && rpvsPromise.value.data) {
      rpvsPromise.value.data.forEach(rpv => {
        results.push({
          id: rpv.id,
          title: `RPV: ${rpv.numero}`,
          subtitle: rpv.cliente?.nome || 'Cliente não especificado',
          type: 'rpv',
          url: `/precatorios/${rpv.id}`
        });
      });
    }

    // Processar resultados de tarefas
    if (tarefasPromise.status === 'fulfilled' && tarefasPromise.value.data) {
      tarefasPromise.value.data.forEach(tarefa => {
        results.push({
          id: tarefa.id,
          title: tarefa.titulo,
          subtitle: `Status: ${tarefa.status}`,
          type: 'tarefa',
          url: `/tarefas?id=${tarefa.id}`
        });
      });
    }

    return results;
  } catch (error) {
    console.error('Erro na pesquisa global:', error);
    return [];
  }
}
