import { cacheLogger } from '@/lib/logger';

// Cache para armazenar dados entre navegações
interface CacheItem<T> {
  data: T;
  timestamp: number;
  expiresAt: number;
  hitCount: number;
  lastAccessed: number;
}

// Cache global para persistir dados entre navegações
const globalCache = new Map<string, CacheItem<any>>();

// Configurações de TTL por tipo de dados
export const CACHE_TTL = {
  // Dados dinâmicos (5 minutos)
  PRECATORIOS: 5 * 60 * 1000,
  CLIENTES: 5 * 60 * 1000,
  TASKS: 3 * 60 * 1000,

  // Dados semi-estáticos (30 minutos)
  PERMISSIONS: 30 * 60 * 1000,
  USER_ROLES: 30 * 60 * 1000,
  STATUS_CONFIG: 30 * 60 * 1000,
  KANBAN_COLUMNS: 15 * 60 * 1000,

  // Dados estáticos (1 hora)
  USER_PROFILE: 60 * 60 * 1000,
  SYSTEM_CONFIG: 60 * 60 * 1000,
} as const;

// Tempo padrão de expiração do cache (5 minutos)
const DEFAULT_CACHE_TTL = CACHE_TTL.PRECATORIOS;

/**
 * Armazena dados no cache global
 * @param key Chave para identificar os dados
 * @param data Dados a serem armazenados
 * @param ttl Tempo de vida do cache em milissegundos (padrão: 5 minutos)
 */
export function setCache<T>(key: string, data: T, ttl = DEFAULT_CACHE_TTL): void {
  const now = Date.now();
  globalCache.set(key, {
    data,
    timestamp: now,
    expiresAt: now + ttl,
    hitCount: 0,
    lastAccessed: now
  });
  cacheLogger.debug(`Dados armazenados em cache: ${key} (TTL: ${Math.round(ttl / 1000)}s)`);
}

/**
 * Obtém dados do cache global
 * @param key Chave para identificar os dados
 * @returns Dados armazenados ou null se não encontrados ou expirados
 */
export function getCache<T>(key: string): T | null {
  const cached = globalCache.get(key);
  const now = Date.now();

  if (!cached) {
    cacheLogger.debug(`Cache miss: ${key}`);
    return null;
  }

  // Verificar se o cache expirou
  if (now > cached.expiresAt) {
    cacheLogger.debug(`Cache expirado: ${key} (idade: ${Math.round((now - cached.timestamp) / 1000)}s)`);
    globalCache.delete(key);
    return null;
  }

  // Atualizar estatísticas de acesso
  cached.hitCount++;
  cached.lastAccessed = now;

  const ageInSeconds = Math.round((now - cached.timestamp) / 1000);
  cacheLogger.debug(`Cache hit: ${key} (idade: ${ageInSeconds}s, hits: ${cached.hitCount})`);
  return cached.data;
}

/**
 * Limpa um item específico do cache
 * @param key Chave do item a ser removido
 */
export function clearCacheItem(key: string): void {
  globalCache.delete(key);
  console.log(`[CacheService] Item removido do cache: ${key}`);
}

/**
 * Limpa todos os itens do cache que correspondem a um padrão
 * @param pattern Padrão para filtrar as chaves (opcional)
 * @param exactMatch Se true, a chave deve ser exatamente igual ao padrão (opcional, padrão: false)
 */
export function clearCache(pattern?: string, exactMatch: boolean = false): void {
  if (pattern) {
    // Limpar apenas chaves que correspondem ao padrão
    const keysToRemove: string[] = [];
    globalCache.forEach((_, key) => {
      if (exactMatch) {
        // Correspondência exata
        if (key === pattern) {
          keysToRemove.push(key);
        }
      } else {
        // Correspondência parcial (comportamento padrão)
        if (key.includes(pattern)) {
          keysToRemove.push(key);
        }
      }
    });

    keysToRemove.forEach(key => {
      globalCache.delete(key);
    });

    console.log(`[CacheService] ${keysToRemove.length} itens removidos do cache com padrão: ${pattern} (${exactMatch ? 'correspondência exata' : 'correspondência parcial'})`);
  } else {
    // Limpar todo o cache
    const count = globalCache.size;
    globalCache.clear();
    console.log(`[CacheService] Todo o cache foi limpo (${count} itens)`);
  }
}

/**
 * Obtém dados do cache ou executa a função de busca se não encontrados
 * @param key Chave para identificar os dados
 * @param fetchFn Função para buscar os dados se não estiverem em cache
 * @param ttl Tempo de vida do cache em milissegundos (padrão: 5 minutos)
 * @returns Dados do cache ou da função de busca
 */
export async function getCachedData<T>(
  key: string,
  fetchFn: () => Promise<T>,
  ttl = DEFAULT_CACHE_TTL
): Promise<T> {
  // Verificar se os dados estão em cache
  const cachedData = getCache<T>(key);

  if (cachedData !== null) {
    return cachedData;
  }

  // Se não estiver em cache, buscar os dados
  try {
    console.log(`[CacheService] Buscando dados frescos para: ${key}`);
    const freshData = await fetchFn();

    // Armazenar os dados no cache
    setCache(key, freshData, ttl);

    return freshData;
  } catch (error) {
    console.error(`[CacheService] Erro ao buscar dados para ${key}:`, error);
    throw error;
  }
}

/**
 * Atualiza um item no cache sem alterar seu tempo de expiração
 * @param key Chave do item a ser atualizado
 * @param updateFn Função que recebe os dados atuais e retorna os dados atualizados
 * @returns true se o item foi atualizado, false se não foi encontrado
 */
export function updateCacheItem<T>(key: string, updateFn: (data: T) => T): boolean {
  const cached = globalCache.get(key);

  if (!cached) {
    console.log(`[CacheService] Item não encontrado para atualização: ${key}`);
    return false;
  }

  const updatedData = updateFn(cached.data);

  globalCache.set(key, {
    ...cached,
    data: updatedData
  });

  console.log(`[CacheService] Item atualizado no cache: ${key}`);
  return true;
}

/**
 * Verifica se um item está em cache e não expirou
 * @param key Chave do item a ser verificado
 * @returns true se o item está em cache e não expirou, false caso contrário
 */
export function isCached(key: string): boolean {
  const cached = globalCache.get(key);
  const now = Date.now();

  if (!cached) {
    return false;
  }

  return now <= cached.expiresAt;
}

/**
 * Obtém informações sobre o cache atual
 * @returns Informações sobre o cache
 */
export function getCacheInfo(): { size: number, keys: string[] } {
  const keys = Array.from(globalCache.keys());
  return {
    size: globalCache.size,
    keys
  };
}

/**
 * Obtém métricas detalhadas do cache
 * @returns Métricas de performance do cache
 */
export function getCacheMetrics() {
  const now = Date.now();
  let totalHits = 0;
  let expiredItems = 0;
  let activeItems = 0;
  const itemsByType = new Map<string, number>();

  globalCache.forEach((item, key) => {
    totalHits += item.hitCount;

    if (now > item.expiresAt) {
      expiredItems++;
    } else {
      activeItems++;
    }

    // Categorizar por tipo de dados
    const type = key.split(':')[0] || 'unknown';
    itemsByType.set(type, (itemsByType.get(type) || 0) + 1);
  });

  return {
    totalItems: globalCache.size,
    activeItems,
    expiredItems,
    totalHits,
    averageHitsPerItem: globalCache.size > 0 ? totalHits / globalCache.size : 0,
    itemsByType: Object.fromEntries(itemsByType),
    memoryUsage: getApproximateMemoryUsage()
  };
}

/**
 * Estima o uso de memória do cache
 * @returns Estimativa do uso de memória em bytes
 */
function getApproximateMemoryUsage(): number {
  let totalSize = 0;

  globalCache.forEach((item) => {
    // Estimativa grosseira do tamanho do objeto em bytes
    const jsonString = JSON.stringify(item.data);
    totalSize += jsonString.length * 2; // UTF-16 uses 2 bytes per character
  });

  return totalSize;
}

/**
 * Limpa automaticamente itens expirados do cache
 * @returns Número de itens removidos
 */
export function cleanupExpiredCache(): number {
  const now = Date.now();
  const keysToRemove: string[] = [];

  globalCache.forEach((item, key) => {
    if (now > item.expiresAt) {
      keysToRemove.push(key);
    }
  });

  keysToRemove.forEach(key => globalCache.delete(key));

  if (keysToRemove.length > 0) {
    cacheLogger.debug(`Limpeza automática: ${keysToRemove.length} itens expirados removidos`);
  }

  return keysToRemove.length;
}

/**
 * Aquece o cache com dados específicos
 * @param warmupFunctions Funções para aquecer diferentes tipos de dados
 */
export async function warmupCache(warmupFunctions: Record<string, () => Promise<any>>): Promise<void> {
  cacheLogger.info('Iniciando aquecimento do cache...');

  const promises = Object.entries(warmupFunctions).map(async ([key, fn]) => {
    try {
      await fn();
      cacheLogger.debug(`Cache aquecido para: ${key}`);
    } catch (error) {
      cacheLogger.error(`Erro ao aquecer cache para ${key}:`, error);
    }
  });

  await Promise.allSettled(promises);
  cacheLogger.info('Aquecimento do cache concluído');
}

// Configurar limpeza automática a cada 10 minutos
if (typeof window !== 'undefined') {
  setInterval(() => {
    cleanupExpiredCache();
  }, 10 * 60 * 1000); // 10 minutos
}
