import { supabase } from "@/lib/supabase";
import { getCachedData, clearCache, setCache, CACHE_TTL } from "./cacheService";
import { authLogger } from "@/lib/logger";

// Tipos para o dashboard
export interface DashboardMetrics {
  totalPrecatorios: number;
  precatoriosPorStatus: Record<string, number>;
  precatoriosPorMes: Record<string, number>;
  tarefasPendentes: number;
  tarefasConcluidas: number;
  clientesAtivos: number;
  desempenhoEquipe: EquipeDesempenho[];
  valorTotalPrecatorios: number;
  precatoriosPorTipo: Record<string, number>;
}

export interface EquipeDesempenho {
  userId: string;
  nome: string;
  cargo: string;
  tarefasConcluidas: number;
  precatoriosProcessados: number;
  tempoMedioProcessamento: number;
  foto_url?: string;
}

export interface DashboardFilters {
  periodo?: 'semana' | 'mes' | 'trimestre' | 'ano' | 'personalizado';
  dataInicio?: string;
  dataFim?: string;
  tiposPrecatorio?: string[];
  statusPrecatorio?: string[];
  responsaveis?: string[];
  departamentos?: string[];
  valorMinimo?: number;
  valorMaximo?: number;
}

export interface DashboardData {
  totalPrecatorios: number;
  precatoriosPorStatus: Record<string, number>;
  precatoriosPorMes: Record<string, number>;
  tarefasPendentes: number;
  tarefasConcluidas: number;
  clientesAtivos: number;
  desempenhoEquipe: EquipeDesempenho[];
  valorTotalPrecatorios: number;
  precatoriosPorTipo: Record<string, number>;
  tempoMedioConclusao: number;
  metaAtingida: number;
  metaTotal: number;
}

export interface DashboardStats {
  totalPrecatorios: number;
  valorTotal: number;
  tarefasPendentes: number;
  clientesAtivos: number;
  crescimentoMensal: number;
  eficienciaEquipe: number;
}

// Configurações padrão
const DEFAULT_CACHE_DURATION = CACHE_TTL.PRECATORIOS;
const MAX_RETRY_ATTEMPTS = 3;
const RETRY_DELAY = 1000; // ms

/**
 * Obtém métricas do dashboard com filtragem baseada em permissões
 * @param userId ID do usuário atual
 * @param userRole Papel do usuário atual
 * @param filters Filtros opcionais para os dados
 * @returns Métricas do dashboard
 */
export async function getDashboardMetrics(
  userId: string,
  userRole: string,
  filters: DashboardFilters = {}
): Promise<DashboardMetrics> {
  try {
    // Chave de cache baseada no usuário e filtros
    const cacheKey = `dashboard:metrics:${userId}:${JSON.stringify(filters)}`;
    
    // Tentar obter do cache primeiro
    return await getCachedData(
      cacheKey,
      async () => {
        authLogger.info(`Buscando métricas do dashboard para usuário ${userId} com role ${userRole}`);
        
        // Construir métricas completas
        const [
          precatoriosData,
          tarefasData,
          clientesData,
          desempenhoEquipe
        ] = await Promise.all([
          getPrecatoriosMetrics(userId, userRole, filters),
          getTarefasMetrics(userId, userRole, filters),
          getClientesMetrics(userId, userRole),
          getDesempenhoEquipe(userId, userRole, filters)
        ]);
        
        return {
          ...precatoriosData,
          ...tarefasData,
          ...clientesData,
          desempenhoEquipe
        };
      },
      // Cache mais curto para admins/gerentes que precisam de dados mais atualizados
      isAdminOrManager(userRole) ? DEFAULT_CACHE_DURATION / 2 : DEFAULT_CACHE_DURATION
    );
  } catch (error) {
    authLogger.error(`Erro ao buscar métricas do dashboard: ${error instanceof Error ? error.message : String(error)}`);
    // Retornar dados vazios em caso de erro para não quebrar a UI
    return getEmptyDashboardMetrics();
  }
}

/**
 * Obtém métricas específicas de precatórios
 */
async function getPrecatoriosMetrics(
  userId: string,
  userRole: string,
  filters: DashboardFilters
): Promise<Partial<DashboardMetrics>> {
  try {
    // Construir a consulta base com campos corretos
    let query = supabase
      .from('precatorios')
      .select('id, status_id, tipo_precatorio, valor_total, created_at, updated_at, responsavel_id');

    // Aplicar filtros baseados no papel do usuário
    if (!isAdminOrManager(userRole)) {
      // Usuários não-admin só veem seus próprios precatórios
      query = query.eq('responsavel_id', userId);
    }

    // Aplicar filtros de data se fornecidos
    if (filters.dataInicio) {
      query = query.gte('created_at', filters.dataInicio);
    }

    if (filters.dataFim) {
      query = query.lte('created_at', filters.dataFim);
    }

    // Aplicar filtros de tipo
    if (filters.tiposPrecatorio && filters.tiposPrecatorio.length > 0) {
      query = query.in('tipo_precatorio', filters.tiposPrecatorio);
    }

    // Aplicar filtros de status
    if (filters.statusPrecatorio && filters.statusPrecatorio.length > 0) {
      query = query.in('status_id', filters.statusPrecatorio);
    }

    // Aplicar filtros de responsáveis
    if (filters.responsaveis && filters.responsaveis.length > 0) {
      query = query.in('responsavel_id', filters.responsaveis);
    }

    // Aplicar filtros de valor
    if (filters.valorMinimo !== undefined) {
      query = query.gte('valor_total', filters.valorMinimo);
    }

    if (filters.valorMaximo !== undefined) {
      query = query.lte('valor_total', filters.valorMaximo);
    }

    // Executar query
    const { data: precatorios, error } = await query;

    if (error) {
      authLogger.error(`Erro ao buscar precatórios: ${error.message}`);
      throw new Error(`Erro ao buscar precatórios: ${error.message}`);
    }

    if (!precatorios || precatorios.length === 0) {
      return {
        totalPrecatorios: 0,
        precatoriosPorStatus: {},
        precatoriosPorMes: {},
        valorTotalPrecatorios: 0,
        precatoriosPorTipo: {}
      };
    }

    // Buscar nomes dos status para melhor exibição
    const { data: statusData } = await supabase
      .from('status_precatorios')
      .select('id, nome');

    const statusMap = statusData?.reduce((acc, status) => {
      acc[status.id] = status.nome;
      return acc;
    }, {} as Record<string, string>) || {};

    // Calcular métricas
    const precatoriosPorStatus: Record<string, number> = {};
    const precatoriosPorTipo: Record<string, number> = {};
    const precatoriosPorMes: Record<string, number> = {};
    let valorTotalPrecatorios = 0;

    precatorios.forEach(precatorio => {
      // Contagem por status (usando nome do status)
      const statusNome = statusMap[precatorio.status_id] || 'Desconhecido';
      precatoriosPorStatus[statusNome] = (precatoriosPorStatus[statusNome] || 0) + 1;

      // Contagem por tipo
      const tipo = precatorio.tipo_precatorio || 'Não informado';
      precatoriosPorTipo[tipo] = (precatoriosPorTipo[tipo] || 0) + 1;

      // Valor total
      valorTotalPrecatorios += Number(precatorio.valor_total || 0);

      // Agrupamento por mês
      const dataCriacao = new Date(precatorio.created_at);
      const mesAno = `${String(dataCriacao.getMonth() + 1).padStart(2, '0')}/${dataCriacao.getFullYear()}`;
      precatoriosPorMes[mesAno] = (precatoriosPorMes[mesAno] || 0) + 1;
    });

    return {
      totalPrecatorios: precatorios.length,
      precatoriosPorStatus,
      precatoriosPorMes,
      valorTotalPrecatorios,
      precatoriosPorTipo
    };
  } catch (error) {
    authLogger.error(`Erro ao calcular métricas de precatórios: ${error instanceof Error ? error.message : String(error)}`);
    return {
      totalPrecatorios: 0,
      precatoriosPorStatus: {},
      precatoriosPorMes: {},
      valorTotalPrecatorios: 0,
      precatoriosPorTipo: {}
    };
  }
}

/**
 * Obtém métricas específicas de tarefas
 */
async function getTarefasMetrics(
  userId: string,
  userRole: string,
  filters: DashboardFilters
): Promise<Partial<DashboardMetrics>> {
  try {
    // Construir a consulta base
    let query = supabase
      .from('tasks')
      .select('id, status, created_at, updated_at, responsavel_id, assignee_id');

    // Aplicar filtros baseados no papel do usuário
    if (!isAdminOrManager(userRole)) {
      // Usuários não-admin só veem suas próprias tarefas
      query = query.or(`responsavel_id.eq.${userId},assignee_id.eq.${userId}`);
    }

    // Aplicar filtros de data se fornecidos
    if (filters.dataInicio) {
      query = query.gte('created_at', filters.dataInicio);
    }

    if (filters.dataFim) {
      query = query.lte('created_at', filters.dataFim);
    }

    // Aplicar filtros de responsáveis
    if (filters.responsaveis && filters.responsaveis.length > 0) {
      query = query.in('responsavel_id', filters.responsaveis);
    }

    // Executar query
    const { data: tarefas, error } = await query;

    if (error) {
      authLogger.error(`Erro ao buscar tarefas: ${error.message}`);
      throw new Error(`Erro ao buscar tarefas: ${error.message}`);
    }

    if (!tarefas || tarefas.length === 0) {
      return {
        tarefasPendentes: 0,
        tarefasConcluidas: 0
      };
    }

    // Calcular métricas
    const tarefasPendentes = tarefas.filter(tarefa =>
      tarefa.status !== 'concluida' && tarefa.status !== 'cancelada'
    ).length;

    const tarefasConcluidas = tarefas.filter(tarefa =>
      tarefa.status === 'concluida'
    ).length;

    return {
      tarefasPendentes,
      tarefasConcluidas
    };
  } catch (error) {
    authLogger.error(`Erro ao calcular métricas de tarefas: ${error instanceof Error ? error.message : String(error)}`);
    return {
      tarefasPendentes: 0,
      tarefasConcluidas: 0
    };
  }
}

/**
 * Obtém métricas específicas de clientes
 */
async function getClientesMetrics(
  userId: string,
  userRole: string
): Promise<Partial<DashboardMetrics>> {
  try {
    // Construir a consulta base
    let query = supabase
      .from('clientes')
      .select('id, status, created_at, responsavel_id');

    // Aplicar filtros baseados no papel do usuário
    if (!isAdminOrManager(userRole)) {
      // Usuários não-admin só veem seus próprios clientes
      query = query.eq('responsavel_id', userId);
    }

    // Executar query
    const { data: clientes, error } = await query;

    if (error) {
      authLogger.error(`Erro ao buscar clientes: ${error.message}`);
      throw new Error(`Erro ao buscar clientes: ${error.message}`);
    }

    if (!clientes || clientes.length === 0) {
      return {
        clientesAtivos: 0
      };
    }

    // Calcular métricas
    const clientesAtivos = clientes.filter(cliente =>
      cliente.status === 'ativo' || !cliente.status // considerar clientes sem status como ativos
    ).length;

    return {
      clientesAtivos
    };
  } catch (error) {
    authLogger.error(`Erro ao calcular métricas de clientes: ${error instanceof Error ? error.message : String(error)}`);
    return {
      clientesAtivos: 0
    };
  }
}

/**
 * Obtém métricas de desempenho da equipe
 */
async function getDesempenhoEquipe(
  userId: string,
  userRole: string,
  filters: DashboardFilters
): Promise<EquipeDesempenho[]> {
  try {
    // Apenas administradores e gerentes podem ver desempenho da equipe
    if (!isAdminOrManager(userRole)) {
      return [];
    }
    
    // Buscar usuários da equipe
    const { data: usuarios, error: usuariosError } = await supabase
      .from('profiles')
      .select('id, nome, role, cargo, foto_url')
      .neq('status', 'excluido');
    
    if (usuariosError || !usuarios) {
      throw new Error(`Erro ao buscar usuários: ${usuariosError?.message || 'Dados não encontrados'}`);
    }
    
    // Para cada usuário, buscar suas tarefas e precatórios
    const desempenhoPromises = usuarios.map(async (usuario) => {
      try {
        // Buscar tarefas concluídas
        const { data: tarefas, error: tarefasError } = await supabase
          .from('tasks')
          .select('id, created_at, updated_at')
          .eq('responsavel_id', usuario.id)
          .eq('status', 'concluida');
        
        if (tarefasError) {
          throw new Error(`Erro ao buscar tarefas do usuário ${usuario.id}: ${tarefasError.message}`);
        }
        
        // Buscar precatórios processados
        const { data: precatorios, error: precatoriosError } = await supabase
          .from('precatorios')
          .select('id, created_at, updated_at')
          .eq('responsavel_id', usuario.id);
        
        if (precatoriosError) {
          throw new Error(`Erro ao buscar precatórios do usuário ${usuario.id}: ${precatoriosError.message}`);
        }
        
        // Calcular tempo médio de processamento (em dias)
        let tempoTotal = 0;
        let precatoriosComTempo = 0;
        
        precatorios?.forEach(precatorio => {
          if (precatorio.created_at && precatorio.updated_at) {
            const dataCriacao = new Date(precatorio.created_at);
            const dataAtualizacao = new Date(precatorio.updated_at);
            const diferencaDias = (dataAtualizacao.getTime() - dataCriacao.getTime()) / (1000 * 3600 * 24);
            
            if (diferencaDias > 0) {
              tempoTotal += diferencaDias;
              precatoriosComTempo++;
            }
          }
        });
        
        const tempoMedioProcessamento = precatoriosComTempo > 0 
          ? tempoTotal / precatoriosComTempo 
          : 0;
        
        return {
          userId: usuario.id,
          nome: usuario.nome || 'Sem nome',
          cargo: usuario.cargo || usuario.role || 'Sem cargo',
          tarefasConcluidas: tarefas?.length || 0,
          precatoriosProcessados: precatorios?.length || 0,
          tempoMedioProcessamento: Number(tempoMedioProcessamento.toFixed(1)),
          foto_url: usuario.foto_url
        };
      } catch (error) {
        authLogger.error(`Erro ao calcular desempenho do usuário ${usuario.id}: ${error instanceof Error ? error.message : String(error)}`);
        return {
          userId: usuario.id,
          nome: usuario.nome || 'Sem nome',
          cargo: usuario.cargo || usuario.role || 'Sem cargo',
          tarefasConcluidas: 0,
          precatoriosProcessados: 0,
          tempoMedioProcessamento: 0,
          foto_url: usuario.foto_url
        };
      }
    });
    
    // Aguardar todas as promessas e ordenar por tarefas concluídas (decrescente)
    const desempenhoEquipe = await Promise.all(desempenhoPromises);
    return desempenhoEquipe.sort((a, b) => b.tarefasConcluidas - a.tarefasConcluidas);
    
  } catch (error) {
    authLogger.error(`Erro ao buscar desempenho da equipe: ${error instanceof Error ? error.message : String(error)}`);
    return [];
  }
}

/**
 * Obtém dados para o gráfico de evolução de precatórios
 * @param userId ID do usuário atual
 * @param userRole Papel do usuário atual
 * @param periodo Período para o gráfico
 * @returns Dados para o gráfico
 */
export async function getEvolucaoPrecatorios(
  userId: string,
  userRole: string,
  periodo: 'semana' | 'mes' | 'trimestre' | 'ano' = 'mes'
): Promise<{ labels: string[], datasets: any[] }> {
  try {
    // Chave de cache baseada no usuário e período
    const cacheKey = `dashboard:evolucao:${userId}:${periodo}`;
    
    return await getCachedData(
      cacheKey,
      async () => {
        // Calcular datas de início e fim baseadas no período
        const dataFim = new Date();
        const dataInicio = new Date();
        
        switch (periodo) {
          case 'semana':
            dataInicio.setDate(dataInicio.getDate() - 7);
            break;
          case 'mes':
            dataInicio.setMonth(dataInicio.getMonth() - 1);
            break;
          case 'trimestre':
            dataInicio.setMonth(dataInicio.getMonth() - 3);
            break;
          case 'ano':
            dataInicio.setFullYear(dataInicio.getFullYear() - 1);
            break;
        }
        
        // Formatar datas para a consulta
        const dataInicioStr = dataInicio.toISOString();
        const dataFimStr = dataFim.toISOString();
        
        // Buscar precatórios criados no período
        const { data: precatorios, error } = await supabase
          .from('precatorios')
          .select('id, created_at, status, tipo')
          .gte('created_at', dataInicioStr)
          .lte('created_at', dataFimStr);
        
        if (error) {
          throw new Error(`Erro ao buscar evolução de precatórios: ${error.message}`);
        }
        
        if (!precatorios || precatorios.length === 0) {
          return { labels: [], datasets: [] };
        }
        
        // Gerar labels e dados baseados no período
        const { labels, dadosPorStatus } = gerarDadosGrafico(precatorios, periodo);
        
        // Formatar para o formato esperado pelo gráfico
        const datasets = Object.entries(dadosPorStatus).map(([status, valores]) => ({
          label: status,
          data: valores,
          borderColor: getColorForStatus(status),
          backgroundColor: getColorForStatus(status, 0.2),
          tension: 0.4
        }));
        
        return { labels, datasets };
      },
      DEFAULT_CACHE_DURATION
    );
  } catch (error) {
    authLogger.error(`Erro ao buscar evolução de precatórios: ${error instanceof Error ? error.message : String(error)}`);
    return { labels: [], datasets: [] };
  }
}

/**
 * Gera dados para o gráfico baseado no período
 */
function gerarDadosGrafico(precatorios: any[], periodo: string) {
  const dadosPorStatus: Record<string, number[]> = {};
  let labels: string[] = [];
  
  // Configurar formatação de data baseada no período
  let formatoData: string;
  let incremento: 'dia' | 'semana' | 'mes';
  
  switch (periodo) {
    case 'semana':
      formatoData = 'DD/MM';
      incremento = 'dia';
      break;
    case 'mes':
      formatoData = 'DD/MM';
      incremento = 'dia';
      break;
    case 'trimestre':
      formatoData = 'MM/YYYY';
      incremento = 'semana';
      break;
    case 'ano':
      formatoData = 'MM/YYYY';
      incremento = 'mes';
      break;
    default:
      formatoData = 'DD/MM/YYYY';
      incremento = 'dia';
  }
  
  // Gerar labels baseados no período
  const dataInicio = new Date();
  const dataFim = new Date();
  
  switch (periodo) {
    case 'semana':
      dataInicio.setDate(dataInicio.getDate() - 7);
      labels = gerarLabelsIntervalo(dataInicio, dataFim, 'dia');
      break;
    case 'mes':
      dataInicio.setMonth(dataInicio.getMonth() - 1);
      labels = gerarLabelsIntervalo(dataInicio, dataFim, 'dia');
      break;
    case 'trimestre':
      dataInicio.setMonth(dataInicio.getMonth() - 3);
      labels = gerarLabelsIntervalo(dataInicio, dataFim, 'semana');
      break;
    case 'ano':
      dataInicio.setFullYear(dataInicio.getFullYear() - 1);
      labels = gerarLabelsIntervalo(dataInicio, dataFim, 'mes');
      break;
  }
  
  // Inicializar contadores para cada status
  const statusUnicos = [...new Set(precatorios.map(p => p.status))];
  statusUnicos.forEach(status => {
    dadosPorStatus[status] = Array(labels.length).fill(0);
  });
  
  // Contar precatórios por período e status
  precatorios.forEach(precatorio => {
    const dataCriacao = new Date(precatorio.created_at);
    let indice = -1;
    
    switch (periodo) {
      case 'semana':
      case 'mes':
        // Formato: DD/MM
        const diaFormatado = `${dataCriacao.getDate().toString().padStart(2, '0')}/${(dataCriacao.getMonth() + 1).toString().padStart(2, '0')}`;
        indice = labels.indexOf(diaFormatado);
        break;
      case 'trimestre':
      case 'ano':
        // Formato: MM/YYYY
        const mesFormatado = `${(dataCriacao.getMonth() + 1).toString().padStart(2, '0')}/${dataCriacao.getFullYear()}`;
        indice = labels.indexOf(mesFormatado);
        break;
    }
    
    if (indice !== -1 && dadosPorStatus[precatorio.status]) {
      dadosPorStatus[precatorio.status][indice]++;
    }
  });
  
  return { labels, dadosPorStatus };
}

/**
 * Gera labels para um intervalo de datas
 */
function gerarLabelsIntervalo(inicio: Date, fim: Date, incremento: 'dia' | 'semana' | 'mes'): string[] {
  const labels: string[] = [];
  const dataAtual = new Date(inicio);
  
  while (dataAtual <= fim) {
    let label = '';
    
    if (incremento === 'dia') {
      label = `${dataAtual.getDate().toString().padStart(2, '0')}/${(dataAtual.getMonth() + 1).toString().padStart(2, '0')}`;
    } else if (incremento === 'semana') {
      label = `${dataAtual.getDate().toString().padStart(2, '0')}/${(dataAtual.getMonth() + 1).toString().padStart(2, '0')}`;
    } else if (incremento === 'mes') {
      label = `${(dataAtual.getMonth() + 1).toString().padStart(2, '0')}/${dataAtual.getFullYear()}`;
    }
    
    labels.push(label);
    
    // Incrementar data
    if (incremento === 'dia') {
      dataAtual.setDate(dataAtual.getDate() + 1);
    } else if (incremento === 'semana') {
      dataAtual.setDate(dataAtual.getDate() + 7);
    } else if (incremento === 'mes') {
      dataAtual.setMonth(dataAtual.getMonth() + 1);
    }
  }
  
  return labels;
}

/**
 * Obtém uma cor para um status específico
 */
function getColorForStatus(status: string, alpha: number = 1): string {
  const coresStatus: Record<string, string> = {
    'novo': `rgba(59, 130, 246, ${alpha})`,          // Azul
    'em_analise': `rgba(139, 92, 246, ${alpha})`,    // Roxo
    'em_processamento': `rgba(245, 158, 11, ${alpha})`, // Amarelo
    'aguardando_cliente': `rgba(249, 115, 22, ${alpha})`, // Laranja
    'concluido': `rgba(34, 197, 94, ${alpha})`,      // Verde
    'cancelado': `rgba(239, 68, 68, ${alpha})`,      // Vermelho
    'pendente': `rgba(107, 114, 128, ${alpha})`,     // Cinza
    'atrasado': `rgba(220, 38, 38, ${alpha})`,       // Vermelho escuro
    'em_revisao': `rgba(168, 85, 247, ${alpha})`,    // Roxo claro
    'aprovado': `rgba(16, 185, 129, ${alpha})`,      // Verde escuro
    'rejeitado': `rgba(244, 63, 94, ${alpha})`,      // Rosa
    'arquivado': `rgba(75, 85, 99, ${alpha})`        // Cinza escuro
  };
  
  return coresStatus[status] || `rgba(156, 163, 175, ${alpha})`; // Cinza médio como padrão
}

/**
 * Verifica se o usuário é admin ou gerente
 */
function isAdminOrManager(role: string): boolean {
  return ['admin', 'gerente_geral', 'gerente_precatorio', 'gerente_rpv'].includes(role);
}

/**
 * Retorna um objeto de métricas vazio para casos de erro
 */
function getEmptyDashboardMetrics(): DashboardMetrics {
  return {
    totalPrecatorios: 0,
    precatoriosPorStatus: {},
    precatoriosPorMes: {},
    tarefasPendentes: 0,
    tarefasConcluidas: 0,
    clientesAtivos: 0,
    desempenhoEquipe: [],
    valorTotalPrecatorios: 0,
    precatoriosPorTipo: {}
  };
}

/**
 * Limpa o cache do dashboard
 * @param userId ID do usuário (opcional)
 */
export function clearDashboardCache(userId?: string): void {
  if (userId) {
    clearCache(`dashboard:metrics:${userId}`);
    clearCache(`dashboard:evolucao:${userId}`);
  } else {
    clearCache('dashboard');
  }
}

/**
 * Função auxiliar para tentar uma operação com retentativas
 */
async function retryOperation<T>(
  operation: () => Promise<T>,
  maxRetries: number = MAX_RETRY_ATTEMPTS,
  delay: number = RETRY_DELAY
): Promise<T> {
  let lastError: any;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      authLogger.warn(`Tentativa ${attempt}/${maxRetries} falhou: ${error instanceof Error ? error.message : String(error)}`);

      if (attempt < maxRetries) {
        // Esperar antes da próxima tentativa (com backoff exponencial)
        await new Promise(resolve => setTimeout(resolve, delay * attempt));
      }
    }
  }

  throw lastError;
}

// Export functions that are expected by useDashboardData hook
export async function getDashboardCompleto(
  userId: string,
  userRole: string,
  filters: DashboardFilters = {}
): Promise<DashboardData> {
  const metrics = await getDashboardMetrics(userId, userRole, filters);

  return {
    ...metrics,
    tempoMedioConclusao: 0, // TODO: Calculate from actual data
    metaAtingida: 0, // TODO: Calculate from actual data
    metaTotal: 100 // TODO: Get from settings
  };
}

export async function getDashboardStats(
  userId: string,
  userRole: string,
  filters: DashboardFilters = {}
): Promise<DashboardStats> {
  const metrics = await getDashboardMetrics(userId, userRole, filters);

  return {
    totalPrecatorios: metrics.totalPrecatorios,
    valorTotal: metrics.valorTotalPrecatorios,
    tarefasPendentes: metrics.tarefasPendentes,
    clientesAtivos: metrics.clientesAtivos,
    crescimentoMensal: 0, // TODO: Calculate from actual data
    eficienciaEquipe: 0 // TODO: Calculate from actual data
  };
}

export async function getGraficoStatusPrecatorios(
  userId: string,
  userRole: string,
  filters: DashboardFilters = {}
): Promise<{ status: string; quantidade: number }[]> {
  const metrics = await getDashboardMetrics(userId, userRole, filters);

  return Object.entries(metrics.precatoriosPorStatus).map(([status, quantidade]) => ({
    status,
    quantidade
  }));
}

export async function getGraficoValorPorMes(
  userId: string,
  userRole: string,
  filters: DashboardFilters = {}
): Promise<{ mes: string; valor: number }[]> {
  const metrics = await getDashboardMetrics(userId, userRole, filters);

  return Object.entries(metrics.precatoriosPorMes).map(([mes, valor]) => ({
    mes,
    valor
  }));
}

export async function getPrecatoriosRecentes(
  userId: string,
  userRole: string,
  limit: number = 5
): Promise<any[]> {
  try {
    let query = supabase
      .from('precatorios')
      .select(`
        id,
        numero_precatorio,
        valor_total,
        status_id,
        created_at,
        cliente:clientes(nome),
        status:status_precatorios(nome)
      `)
      .order('created_at', { ascending: false })
      .limit(limit);

    // Aplicar filtros baseados no papel do usuário
    if (!isAdminOrManager(userRole)) {
      query = query.eq('responsavel_id', userId);
    }

    const { data: precatorios, error } = await query;

    if (error) {
      authLogger.error(`Erro ao buscar precatórios recentes: ${error.message}`);
      throw new Error(`Erro ao buscar precatórios recentes: ${error.message}`);
    }

    return precatorios?.map(precatorio => ({
      ...precatorio,
      numero: precatorio.numero_precatorio,
      valor: precatorio.valor_total,
      status: precatorio.status?.nome || 'Desconhecido'
    })) || [];
  } catch (error) {
    authLogger.error(`Erro ao buscar precatórios recentes: ${error instanceof Error ? error.message : String(error)}`);
    return [];
  }
}

export async function getTarefasRecentes(
  userId: string,
  userRole: string,
  limit: number = 5
): Promise<any[]> {
  try {
    let query = supabase
      .from('tasks')
      .select(`
        id,
        titulo,
        status,
        prioridade,
        created_at,
        data_vencimento,
        responsavel:profiles!tasks_responsavel_id_fkey(nome),
        assignee:profiles!tasks_assignee_id_fkey(nome)
      `)
      .order('created_at', { ascending: false })
      .limit(limit);

    // Aplicar filtros baseados no papel do usuário
    if (!isAdminOrManager(userRole)) {
      query = query.or(`responsavel_id.eq.${userId},assignee_id.eq.${userId}`);
    }

    const { data: tarefas, error } = await query;

    if (error) {
      authLogger.error(`Erro ao buscar tarefas recentes: ${error.message}`);
      throw new Error(`Erro ao buscar tarefas recentes: ${error.message}`);
    }

    return tarefas?.map(tarefa => ({
      ...tarefa,
      responsavel: tarefa.responsavel || tarefa.assignee
    })) || [];
  } catch (error) {
    authLogger.error(`Erro ao buscar tarefas recentes: ${error instanceof Error ? error.message : String(error)}`);
    return [];
  }
}

export async function getDesempenhoEquipeFormatted(
  userId: string,
  userRole: string,
  filters: DashboardFilters = {}
): Promise<{ usuario: string; concluidos: number; pendentes: number }[]> {
  const equipeData = await getDesempenhoEquipe(userId, userRole, filters);

  return equipeData.map(membro => ({
    usuario: membro.nome,
    concluidos: membro.tarefasConcluidas,
    pendentes: 0 // TODO: Calculate pending tasks
  }));
}

export function limparCacheDashboard(userId?: string): void {
  clearDashboardCache(userId);
}
