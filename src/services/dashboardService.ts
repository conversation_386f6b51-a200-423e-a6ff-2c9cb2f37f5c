import { supabase } from '@/lib/supabase';
import { getCachedData, clearCache, CACHE_TTL } from './cacheService';
import { cacheLogger } from '@/lib/logger';

// Interfaces para os dados do dashboard
export interface DashboardStats {
  total_precatorios: number;
  precatorios_ativos: number;
  novos_mes: number;
  valor_total: number;
  valor_pago: number;
  media_prazo: number;
  taxa_sucesso: number;
  total_tarefas: number;
  tarefas_concluidas: number;
  tarefas_pendentes: number;
  total_clientes: number;
  crescimento_mensal: number;
}

export interface HistoricoValores {
  mes: string;
  valor: number;
  pagamentos: number;
}

export interface DistribuicaoStatus {
  name: string;
  value: number;
  color: string;
}

export interface PrecatorioRecente {
  id: string;
  numero: string;
  cliente_nome: string;
  valor_causa: number;
  status: string;
  created_at: string;
  tipo: string;
}

export interface TarefaUrgente {
  id: string;
  titulo: string;
  responsavel_nome: string;
  precatorio_numero?: string;
  prazo: string;
  prioridade: string;
  status: string;
}

export interface DesempenhoEquipe {
  membro: string;
  cargo: string;
  precatorios_processados: number;
  taxa_conclusao: number;
  tempo_medio: number;
}

// Função para obter estatísticas gerais baseadas nas permissões do usuário
export const fetchDashboardStats = async (userRole: string, userId: string): Promise<DashboardStats> => {
  const cacheKey = `dashboard:stats:${userRole}:${userId}`;
  
  return await getCachedData(
    cacheKey,
    async () => {
      cacheLogger.debug('Buscando estatísticas do dashboard...');

      try {
        // Buscar dados baseados no role do usuário
        let precatoriosQuery = supabase.from('precatorios').select('*');
        let tarefasQuery = supabase.from('tasks').select('*');
        
        // Aplicar filtros baseados no role
        if (userRole === 'gerente_precatorio') {
          precatoriosQuery = precatoriosQuery.eq('tipo', 'PRECATORIO');
        } else if (userRole === 'gerente_rpv') {
          precatoriosQuery = precatoriosQuery.eq('tipo', 'RPV');
        } else if (userRole === 'assistente') {
          // Assistentes veem apenas seus próprios dados
          precatoriosQuery = precatoriosQuery.eq('created_by', userId);
          tarefasQuery = tarefasQuery.eq('assignee_id', userId);
        }

        const [precatoriosResult, tarefasResult, clientesResult] = await Promise.all([
          precatoriosQuery,
          tarefasQuery,
          supabase.from('clientes').select('*')
        ]);

        if (precatoriosResult.error) throw precatoriosResult.error;
        if (tarefasResult.error) throw tarefasResult.error;
        if (clientesResult.error) throw clientesResult.error;

        const precatorios = precatoriosResult.data || [];
        const tarefas = tarefasResult.data || [];
        const clientes = clientesResult.data || [];

        // Calcular estatísticas
        const precatoriosAtivos = precatorios.filter(p => p.status !== 'concluido' && p.status !== 'cancelado').length;
        const valorTotal = precatorios.reduce((sum, p) => sum + (p.valor_causa || 0), 0);
        const valorPago = precatorios.filter(p => p.status === 'concluido').reduce((sum, p) => sum + (p.valor_causa || 0), 0);
        const tarefasConcluidas = tarefas.filter(t => t.status === 'concluida').length;
        const tarefasPendentes = tarefas.filter(t => t.status === 'pendente').length;

        // Calcular novos do mês (últimos 30 dias)
        const umMesAtras = new Date();
        umMesAtras.setDate(umMesAtras.getDate() - 30);
        const novosMes = precatorios.filter(p => new Date(p.created_at) > umMesAtras).length;

        // Calcular taxa de sucesso (precatórios concluídos / total)
        const taxaSucesso = precatorios.length > 0 ? Math.round((precatorios.filter(p => p.status === 'concluido').length / precatorios.length) * 100) : 0;

        // Calcular média de prazo (simplificado)
        const mediaPrazo = 180; // Placeholder - seria calculado baseado em dados reais

        return {
          total_precatorios: precatorios.length,
          precatorios_ativos: precatoriosAtivos,
          novos_mes: novosMes,
          valor_total: valorTotal,
          valor_pago: valorPago,
          media_prazo: mediaPrazo,
          taxa_sucesso: taxaSucesso,
          total_tarefas: tarefas.length,
          tarefas_concluidas: tarefasConcluidas,
          tarefas_pendentes: tarefasPendentes,
          total_clientes: clientes.length,
          crescimento_mensal: 15.8 // Placeholder - seria calculado baseado em dados históricos
        };

      } catch (error) {
        cacheLogger.error('Erro ao buscar estatísticas do dashboard:', error);
        throw error;
      }
    },
    CACHE_TTL.PRECATORIOS
  );
};

// Função para obter distribuição por status
export const fetchDistribuicaoStatus = async (userRole: string, userId: string): Promise<DistribuicaoStatus[]> => {
  const cacheKey = `dashboard:status:${userRole}:${userId}`;
  
  return await getCachedData(
    cacheKey,
    async () => {
      cacheLogger.debug('Buscando distribuição por status...');

      try {
        let query = supabase.from('precatorios').select('status');
        
        // Aplicar filtros baseados no role
        if (userRole === 'gerente_precatorio') {
          query = query.eq('tipo', 'PRECATORIO');
        } else if (userRole === 'gerente_rpv') {
          query = query.eq('tipo', 'RPV');
        } else if (userRole === 'assistente') {
          query = query.eq('created_by', userId);
        }

        const { data, error } = await query;
        if (error) throw error;

        // Contar por status
        const statusCount: Record<string, number> = {};
        data?.forEach(item => {
          const status = item.status || 'indefinido';
          statusCount[status] = (statusCount[status] || 0) + 1;
        });

        // Mapear para o formato esperado
        const statusColors: Record<string, string> = {
          'em_processamento': '#3b82f6',
          'aguardando_pagamento': '#f59e0b',
          'concluido': '#10b981',
          'suspenso': '#ef4444',
          'pendente': '#6b7280',
          'indefinido': '#9ca3af'
        };

        const statusNames: Record<string, string> = {
          'em_processamento': 'Em Processamento',
          'aguardando_pagamento': 'Aguardando Pagamento',
          'concluido': 'Concluídos',
          'suspenso': 'Suspensos',
          'pendente': 'Pendentes',
          'indefinido': 'Indefinido'
        };

        return Object.entries(statusCount).map(([status, count]) => ({
          name: statusNames[status] || status,
          value: count,
          color: statusColors[status] || '#9ca3af'
        }));

      } catch (error) {
        cacheLogger.error('Erro ao buscar distribuição por status:', error);
        throw error;
      }
    },
    CACHE_TTL.PRECATORIOS
  );
};

// Função para obter precatórios recentes
export const fetchPrecatoriosRecentes = async (userRole: string, userId: string, limit: number = 5): Promise<PrecatorioRecente[]> => {
  const cacheKey = `dashboard:recentes:${userRole}:${userId}:${limit}`;
  
  return await getCachedData(
    cacheKey,
    async () => {
      cacheLogger.debug('Buscando precatórios recentes...');

      try {
        let query = supabase
          .from('precatorios')
          .select(`
            id,
            numero,
            valor_causa,
            status,
            created_at,
            tipo,
            cliente:cliente_id (nome)
          `)
          .order('created_at', { ascending: false })
          .limit(limit);
        
        // Aplicar filtros baseados no role
        if (userRole === 'gerente_precatorio') {
          query = query.eq('tipo', 'PRECATORIO');
        } else if (userRole === 'gerente_rpv') {
          query = query.eq('tipo', 'RPV');
        } else if (userRole === 'assistente') {
          query = query.eq('created_by', userId);
        }

        const { data, error } = await query;
        if (error) throw error;

        return (data || []).map(item => ({
          id: item.id,
          numero: item.numero || 'N/A',
          cliente_nome: (item.cliente as any)?.nome || 'Cliente não informado',
          valor_causa: item.valor_causa || 0,
          status: item.status || 'indefinido',
          created_at: item.created_at,
          tipo: item.tipo || 'PRECATORIO'
        }));

      } catch (error) {
        cacheLogger.error('Erro ao buscar precatórios recentes:', error);
        throw error;
      }
    },
    CACHE_TTL.PRECATORIOS
  );
};

// Função para obter tarefas urgentes
export const fetchTarefasUrgentes = async (userRole: string, userId: string, limit: number = 5): Promise<TarefaUrgente[]> => {
  const cacheKey = `dashboard:tarefas_urgentes:${userRole}:${userId}:${limit}`;
  
  return await getCachedData(
    cacheKey,
    async () => {
      cacheLogger.debug('Buscando tarefas urgentes...');

      try {
        let query = supabase
          .from('tasks')
          .select(`
            id,
            title,
            due_date,
            priority,
            status,
            assignee:assignee_id (nome),
            precatorio:precatorio_id (numero)
          `)
          .not('due_date', 'is', null)
          .order('due_date', { ascending: true })
          .limit(limit);
        
        // Aplicar filtros baseados no role
        if (userRole === 'assistente') {
          query = query.eq('assignee_id', userId);
        }

        const { data, error } = await query;
        if (error) throw error;

        return (data || []).map(item => ({
          id: item.id,
          titulo: item.title,
          responsavel_nome: (item.assignee as any)?.nome || 'Não atribuído',
          precatorio_numero: (item.precatorio as any)?.numero,
          prazo: item.due_date || '',
          prioridade: item.priority || 'media',
          status: item.status || 'pendente'
        }));

      } catch (error) {
        cacheLogger.error('Erro ao buscar tarefas urgentes:', error);
        throw error;
      }
    },
    CACHE_TTL.TASKS
  );
};

// Função para obter histórico de valores (últimos 6 meses)
export const fetchHistoricoValores = async (userRole: string, userId: string): Promise<HistoricoValores[]> => {
  const cacheKey = `dashboard:historico:${userRole}:${userId}`;

  return await getCachedData(
    cacheKey,
    async () => {
      cacheLogger.debug('Buscando histórico de valores...');

      try {
        // Calcular os últimos 6 meses
        const meses = [];
        const hoje = new Date();
        for (let i = 5; i >= 0; i--) {
          const data = new Date(hoje.getFullYear(), hoje.getMonth() - i, 1);
          meses.push({
            inicio: new Date(data.getFullYear(), data.getMonth(), 1),
            fim: new Date(data.getFullYear(), data.getMonth() + 1, 0),
            nome: data.toLocaleDateString('pt-BR', { month: 'short' }).replace('.', '')
          });
        }

        const historico: HistoricoValores[] = [];

        for (const mes of meses) {
          let query = supabase
            .from('precatorios')
            .select('valor_causa, status, created_at')
            .gte('created_at', mes.inicio.toISOString())
            .lte('created_at', mes.fim.toISOString());

          // Aplicar filtros baseados no role
          if (userRole === 'gerente_precatorio') {
            query = query.eq('tipo', 'PRECATORIO');
          } else if (userRole === 'gerente_rpv') {
            query = query.eq('tipo', 'RPV');
          } else if (userRole === 'assistente') {
            query = query.eq('created_by', userId);
          }

          const { data, error } = await query;
          if (error) throw error;

          const valorTotal = (data || []).reduce((sum, p) => sum + (p.valor_causa || 0), 0);
          const valorPago = (data || []).filter(p => p.status === 'concluido').reduce((sum, p) => sum + (p.valor_causa || 0), 0);

          historico.push({
            mes: mes.nome,
            valor: valorTotal,
            pagamentos: valorPago
          });
        }

        return historico;

      } catch (error) {
        cacheLogger.error('Erro ao buscar histórico de valores:', error);
        throw error;
      }
    },
    CACHE_TTL.PRECATORIOS
  );
};

// Função para obter desempenho da equipe
export const fetchDesempenhoEquipe = async (userRole: string, userId: string): Promise<DesempenhoEquipe[]> => {
  const cacheKey = `dashboard:equipe:${userRole}:${userId}`;

  return await getCachedData(
    cacheKey,
    async () => {
      cacheLogger.debug('Buscando desempenho da equipe...');

      try {
        // Buscar usuários e suas estatísticas
        let usersQuery = supabase
          .from('profiles')
          .select('id, nome, role');

        // Admins veem toda a equipe, outros veem apenas a si mesmos
        if (userRole !== 'admin') {
          usersQuery = usersQuery.eq('id', userId);
        }

        const { data: users, error: usersError } = await usersQuery;
        if (usersError) throw usersError;

        const desempenho: DesempenhoEquipe[] = [];

        for (const user of users || []) {
          // Buscar precatórios processados pelo usuário
          const { data: precatorios, error: precatoriosError } = await supabase
            .from('precatorios')
            .select('status, created_at')
            .eq('created_by', user.id);

          if (precatoriosError) {
            cacheLogger.warn(`Erro ao buscar precatórios para usuário ${user.id}:`, precatoriosError);
            continue;
          }

          // Buscar tarefas do usuário
          const { data: tarefas, error: tarefasError } = await supabase
            .from('tasks')
            .select('status, created_at')
            .eq('assignee_id', user.id);

          if (tarefasError) {
            cacheLogger.warn(`Erro ao buscar tarefas para usuário ${user.id}:`, tarefasError);
            continue;
          }

          const precatoriosProcessados = (precatorios || []).length;
          const tarefasConcluidas = (tarefas || []).filter(t => t.status === 'concluida').length;
          const totalTarefas = (tarefas || []).length;
          const taxaConclusao = totalTarefas > 0 ? Math.round((tarefasConcluidas / totalTarefas) * 100) : 0;

          desempenho.push({
            membro: user.nome || user.id,
            cargo: user.role || 'Usuário',
            precatorios_processados: precatoriosProcessados,
            taxa_conclusao: taxaConclusao,
            tempo_medio: 12 // Placeholder - seria calculado baseado em dados reais
          });
        }

        return desempenho;

      } catch (error) {
        cacheLogger.error('Erro ao buscar desempenho da equipe:', error);
        throw error;
      }
    },
    CACHE_TTL.USER_PROFILE
  );
};

// Função para obter distribuição por tipo (Precatório/RPV)
export const fetchDistribuicaoTipo = async (userRole: string, userId: string): Promise<DistribuicaoStatus[]> => {
  const cacheKey = `dashboard:tipo:${userRole}:${userId}`;

  return await getCachedData(
    cacheKey,
    async () => {
      cacheLogger.debug('Buscando distribuição por tipo...');

      try {
        let query = supabase.from('precatorios').select('tipo');

        // Aplicar filtros baseados no role
        if (userRole === 'gerente_precatorio') {
          query = query.eq('tipo', 'PRECATORIO');
        } else if (userRole === 'gerente_rpv') {
          query = query.eq('tipo', 'RPV');
        } else if (userRole === 'assistente') {
          query = query.eq('created_by', userId);
        }

        const { data, error } = await query;
        if (error) throw error;

        // Contar por tipo
        const tipoCount: Record<string, number> = {};
        data?.forEach(item => {
          const tipo = item.tipo || 'PRECATORIO';
          tipoCount[tipo] = (tipoCount[tipo] || 0) + 1;
        });

        return [
          { name: 'Precatórios', value: tipoCount['PRECATORIO'] || 0, color: '#3b82f6' },
          { name: 'RPVs', value: tipoCount['RPV'] || 0, color: '#10b981' }
        ];

      } catch (error) {
        cacheLogger.error('Erro ao buscar distribuição por tipo:', error);
        throw error;
      }
    },
    CACHE_TTL.PRECATORIOS
  );
};

// Função para limpar cache do dashboard
export const clearDashboardCache = (userRole?: string, userId?: string) => {
  if (userRole && userId) {
    clearCache(`dashboard:stats:${userRole}:${userId}`);
    clearCache(`dashboard:status:${userRole}:${userId}`);
    clearCache(`dashboard:recentes:${userRole}:${userId}:5`);
    clearCache(`dashboard:tarefas_urgentes:${userRole}:${userId}:5`);
    clearCache(`dashboard:historico:${userRole}:${userId}`);
    clearCache(`dashboard:equipe:${userRole}:${userId}`);
    clearCache(`dashboard:tipo:${userRole}:${userId}`);
  } else {
    // Limpar todo o cache do dashboard
    clearCache('dashboard:*');
  }
};
