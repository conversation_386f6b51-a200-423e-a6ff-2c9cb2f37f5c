import { supabase } from '@/lib/supabase';
import { getCache, setCache, clearCache } from './cacheService';

// Tipos
export interface StatusPrecatorio {
  id: string;
  nome: string;
  codigo: string;
  cor: string;
  is_default: boolean;
  is_system: boolean;
  ativo: boolean;
  ordem: number;
  visivel: boolean;
  kanban_coluna: boolean;
  created_at?: string;
  updated_at?: string;
  created_by?: string;
}

// Cache para status
const STATUS_CACHE_KEY = 'status_precatorios';
const STATUS_CACHE_DURATION = 5 * 60 * 1000; // 5 minutos

/**
 * Busca todos os status de precatórios
 */
export async function buscarTodosStatus(incluirInvisivel = false): Promise<StatusPrecatorio[]> {
  try {
    // Verificar cache
    const cachedData = getCache<StatusPrecatorio[]>(STATUS_CACHE_KEY);
    if (cachedData) {
      return incluirInvisivel 
        ? cachedData 
        : cachedData.filter(status => status.visivel);
    }

    // Buscar do banco de dados
    const query = supabase
      .from('status_precatorios')
      .select('*')
      .eq('ativo', true)
      .order('ordem', { ascending: true });

    const { data, error } = await query;

    if (error) {
      console.error('Erro ao buscar status de precatórios:', error);
      throw error;
    }

    // Salvar no cache
    setCache(STATUS_CACHE_KEY, data, STATUS_CACHE_DURATION);

    // Retornar dados filtrados ou não
    return incluirInvisivel 
      ? data 
      : data.filter(status => status.visivel);
  } catch (error) {
    console.error('Erro ao buscar status de precatórios:', error);
    return [];
  }
}

/**
 * Busca um status pelo ID
 */
export async function buscarStatusPorId(id: string): Promise<StatusPrecatorio | null> {
  try {
    // Verificar cache
    const cachedData = getCache<StatusPrecatorio[]>(STATUS_CACHE_KEY);
    if (cachedData) {
      const statusCache = cachedData.find(s => s.id === id);
      if (statusCache) return statusCache;
    }

    // Buscar do banco de dados
    const { data, error } = await supabase
      .from('status_precatorios')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error(`Erro ao buscar status com ID ${id}:`, error);
      return null;
    }

    return data;
  } catch (error) {
    console.error(`Erro ao buscar status com ID ${id}:`, error);
    return null;
  }
}

/**
 * Busca um status pelo código
 */
export async function buscarStatusPorCodigo(codigo: string): Promise<StatusPrecatorio | null> {
  try {
    // Verificar cache
    const cachedData = getCache<StatusPrecatorio[]>(STATUS_CACHE_KEY);
    if (cachedData) {
      const statusCache = cachedData.find(s => s.codigo === codigo);
      if (statusCache) return statusCache;
    }

    // Buscar do banco de dados
    const { data, error } = await supabase
      .from('status_precatorios')
      .select('*')
      .eq('codigo', codigo)
      .single();

    if (error) {
      console.error(`Erro ao buscar status com código ${codigo}:`, error);
      return null;
    }

    return data;
  } catch (error) {
    console.error(`Erro ao buscar status com código ${codigo}:`, error);
    return null;
  }
}

/**
 * Cria um novo status
 */
export async function criarStatus(status: Omit<StatusPrecatorio, 'id' | 'created_at' | 'updated_at'>): Promise<StatusPrecatorio | null> {
  try {
    // Verificar se já existe um status com o mesmo código
    const existente = await buscarStatusPorCodigo(status.codigo);
    if (existente) {
      console.error(`Já existe um status com o código ${status.codigo}`);
      throw new Error(`Já existe um status com o código ${status.codigo}`);
    }

    // Inserir no banco de dados
    const { data, error } = await supabase
      .from('status_precatorios')
      .insert({
        ...status,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('Erro ao criar status:', error);
      throw error;
    }

    // Limpar cache
    clearCache(STATUS_CACHE_KEY);

    return data;
  } catch (error) {
    console.error('Erro ao criar status:', error);
    throw error;
  }
}

/**
 * Atualiza um status existente
 */
export async function atualizarStatus(id: string, status: Partial<StatusPrecatorio>): Promise<StatusPrecatorio | null> {
  try {
    // Verificar se o status existe
    const existente = await buscarStatusPorId(id);
    if (!existente) {
      console.error(`Status com ID ${id} não encontrado`);
      throw new Error(`Status com ID ${id} não encontrado`);
    }

    // Atualizar no banco de dados
    const { data, error } = await supabase
      .from('status_precatorios')
      .update({
        ...status,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error(`Erro ao atualizar status com ID ${id}:`, error);
      throw error;
    }

    // Limpar cache
    clearCache(STATUS_CACHE_KEY);

    return data;
  } catch (error) {
    console.error(`Erro ao atualizar status com ID ${id}:`, error);
    throw error;
  }
}

/**
 * Exclui um status (marcando como inativo)
 */
export async function excluirStatus(id: string): Promise<void> {
  try {
    // Verificar se o status existe
    const existente = await buscarStatusPorId(id);
    if (!existente) {
      console.error(`Status com ID ${id} não encontrado`);
      throw new Error(`Status com ID ${id} não encontrado`);
    }

    // Marcar como inativo
    const { error } = await supabase
      .from('status_precatorios')
      .update({
        ativo: false,
        visivel: false,
        kanban_coluna: false,
        updated_at: new Date().toISOString()
      })
      .eq('id', id);

    if (error) {
      console.error(`Erro ao excluir status com ID ${id}:`, error);
      throw error;
    }

    // Limpar cache
    clearCache(STATUS_CACHE_KEY);
  } catch (error) {
    console.error(`Erro ao excluir status com ID ${id}:`, error);
    throw error;
  }
}

/**
 * Busca o status padrão
 */
export async function buscarStatusPadrao(): Promise<StatusPrecatorio | null> {
  try {
    // Buscar do banco de dados
    const { data, error } = await supabase
      .from('status_precatorios')
      .select('*')
      .eq('is_default', true)
      .eq('ativo', true)
      .single();

    if (error) {
      console.error('Erro ao buscar status padrão:', error);
      
      // Se não encontrou, buscar o primeiro status ativo
      const { data: primeiroStatus, error: errorPrimeiro } = await supabase
        .from('status_precatorios')
        .select('*')
        .eq('ativo', true)
        .order('ordem', { ascending: true })
        .limit(1)
        .single();
        
      if (errorPrimeiro) {
        console.error('Erro ao buscar primeiro status:', errorPrimeiro);
        return null;
      }
      
      return primeiroStatus;
    }

    return data;
  } catch (error) {
    console.error('Erro ao buscar status padrão:', error);
    return null;
  }
}
