import { supabase } from '@/lib/supabase';

export interface CustomRole {
  id: string;
  nome: string;
  descricao?: string;
  cor: string;
  icone?: string;
  is_system: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface RolePermission {
  id: string;
  role_id: string;
  resource_type: string;
  action: string;
  allowed: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface RolePageAccess {
  id: string;
  role_id: string;
  page_path: string;
  can_access: boolean;
  created_at?: string;
  updated_at?: string;
}

// Função para buscar todos os cargos
export const fetchAllRoles = async (): Promise<CustomRole[]> => {
  try {
    const { data, error } = await supabase
      .from('custom_roles')
      .select('*')
      .is('is_deleted', false)
      .order('nome');

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Erro ao buscar cargos:', error);
    throw error;
  }
};

// Função para buscar um cargo específico
export const fetchRoleById = async (roleId: string): Promise<CustomRole> => {
  try {
    const { data, error } = await supabase
      .from('custom_roles')
      .select('*')
      .eq('id', roleId)
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Erro ao buscar cargo:', error);
    throw error;
  }
};

// Função para criar um novo cargo
export const createRole = async (role: Omit<CustomRole, 'id' | 'created_at' | 'updated_at'>): Promise<CustomRole> => {
  try {
    const { data, error } = await supabase
      .from('custom_roles')
      .insert(role)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Erro ao criar cargo:', error);
    throw error;
  }
};

// Função para atualizar um cargo existente
export const updateRole = async (id: string, role: Partial<CustomRole>): Promise<CustomRole> => {
  try {
    const { data, error } = await supabase
      .from('custom_roles')
      .update(role)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Erro ao atualizar cargo:', error);
    throw error;
  }
};

// Função para excluir um cargo
export const deleteRole = async (id: string): Promise<void> => {
  try {
    // Verificar se é um cargo do sistema
    const { data: roleData, error: roleError } = await supabase
      .from('custom_roles')
      .select('is_system')
      .eq('id', id)
      .single();

    if (roleError) throw roleError;

    if (roleData.is_system) {
      throw new Error('Não é possível excluir cargos do sistema');
    }

    // Marcar como excluído em vez de remover do banco de dados
    const { error } = await supabase
      .from('custom_roles')
      .update({ is_deleted: true, deleted_at: new Date().toISOString() })
      .eq('id', id);

    if (error) throw error;
  } catch (error) {
    console.error('Erro ao excluir cargo:', error);
    throw error;
  }
};

// Função para obter permissões padrão de um cargo
export const getRoleDefaultPermissions = async (roleId: string): Promise<RolePermission[]> => {
  try {
    const { data, error } = await supabase
      .from('role_default_permissions')
      .select('*')
      .eq('role_id', roleId);

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Erro ao obter permissões do cargo:', error);
    throw error;
  }
};

// Função para atualizar permissão padrão de um cargo
export const updateRoleDefaultPermission = async (
  roleId: string,
  resourceType: string,
  action: string,
  allowed: boolean
): Promise<void> => {
  try {
    // Verificar se já existe uma permissão para este cargo
    const { data: existingPermission, error: checkError } = await supabase
      .from('role_default_permissions')
      .select('id')
      .eq('role_id', roleId)
      .eq('resource_type', resourceType)
      .eq('action', action)
      .maybeSingle();

    if (checkError) throw checkError;

    if (existingPermission) {
      // Atualizar permissão existente
      const { error } = await supabase
        .from('role_default_permissions')
        .update({
          allowed,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingPermission.id);

      if (error) throw error;
    } else {
      // Criar nova permissão
      const { error } = await supabase
        .from('role_default_permissions')
        .insert({
          role_id: roleId,
          resource_type: resourceType,
          action,
          allowed
        });

      if (error) throw error;
    }
  } catch (error) {
    console.error('Erro ao atualizar permissão do cargo:', error);
    throw error;
  }
};

// Função para obter permissões de acesso a páginas de um cargo
export const getRolePageAccess = async (roleId: string): Promise<RolePageAccess[]> => {
  try {
    const { data, error } = await supabase
      .from('role_page_access')
      .select('*')
      .eq('role_id', roleId);

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Erro ao obter acesso a páginas do cargo:', error);
    throw error;
  }
};

// Função para atualizar permissão de acesso a página de um cargo
export const updateRolePageAccess = async (
  roleId: string,
  pagePath: string,
  canAccess: boolean
): Promise<void> => {
  try {
    // Verificar se já existe um acesso para este cargo
    const { data: existingAccess, error: checkError } = await supabase
      .from('role_page_access')
      .select('id')
      .eq('role_id', roleId)
      .eq('page_path', pagePath)
      .maybeSingle();

    if (checkError) throw checkError;

    if (existingAccess) {
      // Atualizar acesso existente
      const { error } = await supabase
        .from('role_page_access')
        .update({
          can_access: canAccess,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingAccess.id);

      if (error) throw error;
    } else {
      // Criar novo acesso
      const { error } = await supabase
        .from('role_page_access')
        .insert({
          role_id: roleId,
          page_path: pagePath,
          can_access: canAccess
        });

      if (error) throw error;
    }
  } catch (error) {
    console.error('Erro ao atualizar acesso a página do cargo:', error);
    throw error;
  }
};

// Função para aplicar permissões padrão de um cargo a um usuário
export const applyRolePermissionsToUser = async (userId: string, roleId: string): Promise<void> => {
  try {
    const { error } = await supabase.rpc('apply_role_default_permissions', {
      p_user_id: userId,
      p_role_id: roleId
    });

    if (error) throw error;
  } catch (error) {
    console.error('Erro ao aplicar permissões do cargo ao usuário:', error);
    throw error;
  }
};
