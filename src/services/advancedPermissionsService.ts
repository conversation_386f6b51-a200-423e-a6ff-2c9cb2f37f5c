import { supabase } from '@/lib/supabase';
import {
  UserPermissionsData,
  Permission,
  SpecificPermission,
  TaskVisibilitySettings,
  PageAccessSetting,
  CustomRole,
  RoleDefaultPermission,
  CustomView,
  UserViewAssignment,
  PermissionLogEntry,
  UserRelationship
} from '@/types/permissions';
import { getUserRole } from './userService';

// Cache for permissions
interface CacheEntry {
  data: UserPermissionsData;
  timestamp: number;
  expiresAt: number;
}

const CACHE_EXPIRATION = 5 * 60 * 1000; // 5 minutes
const permissionsCache: Record<string, CacheEntry> = {};

// Clear cache for a specific user or all users
export const clearPermissionsCache = (userId?: string) => {
  if (userId) {
    delete permissionsCache[userId];
  } else {
    Object.keys(permissionsCache).forEach(key => {
      delete permissionsCache[key];
    });
  }
};

// Check if a user is an admin
export const isUserAdmin = async (userId: string): Promise<boolean> => {
  try {
    const userRole = await getUserRole(userId);
    return userRole === 'admin';
  } catch (error) {
    console.error('Erro ao verificar se usuário é admin:', error);
    return false;
  }
};

// Get all permissions for a user
export const getUserPermissions = async (userId: string): Promise<UserPermissionsData> => {
  try {
    // Check cache first
    const cachedPermissions = permissionsCache[userId];
    if (cachedPermissions && cachedPermissions.expiresAt > Date.now()) {
      return cachedPermissions.data;
    }

    // Get user role
    const userRole = await getUserRole(userId);

    // Try to get permissions from server
    try {
      const { data, error } = await supabase.rpc('get_user_permissions', {
        p_user_id: userId
      });

      if (error) {
        console.error('Erro ao obter permissões do usuário:', error);
        throw error;
      }

      // Ensure all fields exist, even if empty
      const permissions: UserPermissionsData = {
        role_permissions: data?.role_permissions || [],
        specific_permissions: data?.specific_permissions || [],
        task_visibility: data?.task_visibility || {
          can_see_own_tasks: true,
          can_see_team_tasks: userRole !== 'captador',
          can_see_all_tasks: userRole === 'admin' || userRole === 'gerente_geral',
          visible_user_ids: []
        },
        page_access: data?.page_access || []
      };

      // Save to cache with expiration
      permissionsCache[userId] = {
        data: permissions,
        timestamp: Date.now(),
        expiresAt: Date.now() + CACHE_EXPIRATION
      };

      return permissions;
    } catch (error) {
      console.error('Erro ao obter permissões do servidor:', error);

      // Create default permissions based on user role
      const defaultPermissions: UserPermissionsData = {
        role_permissions: [],
        specific_permissions: [],
        task_visibility: {
          can_see_own_tasks: true,
          can_see_team_tasks: userRole !== 'captador',
          can_see_all_tasks: userRole === 'admin' || userRole === 'gerente_geral',
          visible_user_ids: []
        },
        page_access: []
      };

      // Save to cache with expiration
      permissionsCache[userId] = {
        data: defaultPermissions,
        timestamp: Date.now(),
        expiresAt: Date.now() + CACHE_EXPIRATION
      };

      return defaultPermissions;
    }
  } catch (error) {
    console.error('Erro ao obter permissões do usuário:', error);
    throw error;
  }
};

// Check if a user has permission for a specific action
export const checkPermission = async (
  userId: string,
  resourceType: string,
  action: string,
  resourceId?: string
): Promise<boolean> => {
  try {
    // Get user permissions (from cache if available)
    const permissions = await getUserPermissions(userId);

    // Check if user is admin (has all permissions)
    const isAdmin = await isUserAdmin(userId);
    if (isAdmin) return true;

    // Check specific permissions first
    const specificPermission = permissions.specific_permissions.find(
      p => p.resource_type === resourceType &&
           p.action === action &&
           (resourceId ? p.resource_id === resourceId : !p.resource_id)
    );

    if (specificPermission) {
      return specificPermission.allowed;
    }

    // Check role permissions
    const rolePermission = permissions.role_permissions.find(
      p => p.resource_type === resourceType && p.action === action
    );

    if (rolePermission) {
      return rolePermission.allowed;
    }

    // Default to false if no permission found
    return false;
  } catch (error) {
    console.error('Erro ao verificar permissão:', error);
    return false;
  }
};

// Check if a user can access a page
export const checkPageAccess = async (userId: string, pagePath: string): Promise<boolean> => {
  try {
    // Check if user is admin (has access to all pages)
    const isAdmin = await isUserAdmin(userId);
    if (isAdmin) return true;

    // Get user permissions
    const permissions = await getUserPermissions(userId);

    // Check specific page access settings
    const pageAccess = permissions.page_access.find(p => p.page_path === pagePath);
    if (pageAccess) {
      return pageAccess.can_access;
    }

    // If no specific setting, check based on role
    const userRole = await getUserRole(userId);

    // Default rules based on role
    switch (userRole) {
      case 'admin':
        return true;
      case 'gerente_geral':
      case 'gerente_precatorio':
      case 'gerente_rpv':
        return true;
      case 'operacional_precatorio':
      case 'operacional_rpv':
      case 'operacional_completo':
        // Operational users have access to most pages except admin ones
        return !['/usuarios', '/configuracoes', '/permissoes'].includes(pagePath);
      case 'assistente':
        // Assistants have limited access by default
        return ['/dashboard', '/profile', '/tarefas', '/clientes'].includes(pagePath);
      default:
        return false;
    }
  } catch (error) {
    console.error('Erro ao verificar acesso à página:', error);
    return false;
  }
};

// Check if a user can view a task
export const checkTaskVisibility = async (
  userId: string,
  taskId: string,
  taskOwnerId: string
): Promise<boolean> => {
  try {
    // Check if user is admin (can see all tasks)
    const isAdmin = await isUserAdmin(userId);
    if (isAdmin) return true;

    // Get user permissions
    const permissions = await getUserPermissions(userId);
    const taskVisibility = permissions.task_visibility;

    // Check if user can see all tasks
    if (taskVisibility.can_see_all_tasks) {
      return true;
    }

    // Check if user is the owner of the task
    if (taskVisibility.can_see_own_tasks && userId === taskOwnerId) {
      return true;
    }

    // Check if user can see team tasks and task owner is in their team
    if (taskVisibility.can_see_team_tasks) {
      // Check if there's a supervisor-subordinate relationship
      const { data, error } = await supabase
        .from('user_relationships')
        .select('*')
        .eq('supervisor_id', userId)
        .eq('subordinate_id', taskOwnerId)
        .single();

      if (!error && data) {
        return true;
      }
    }

    // Check if task owner is in the list of visible users
    if (taskVisibility.visible_user_ids.includes(taskOwnerId)) {
      return true;
    }

    // Default to false if no condition is met
    return false;
  } catch (error) {
    console.error('Erro ao verificar visibilidade da tarefa:', error);
    return false;
  }
};

// Update specific permission for a user
export const updateUserPermission = async (
  adminId: string,
  userId: string,
  resourceType: string,
  action: string,
  allowed: boolean,
  resourceId?: string
): Promise<boolean> => {
  try {
    const { data, error } = await supabase.rpc('update_user_permission', {
      p_admin_id: adminId,
      p_user_id: userId,
      p_resource_type: resourceType,
      p_resource_id: resourceId || null,
      p_action: action,
      p_allowed: allowed
    });

    if (error) {
      console.error('Erro ao atualizar permissão do usuário:', error);
      throw error;
    }

    // Clear cache for this user
    clearPermissionsCache(userId);

    return true;
  } catch (error) {
    console.error('Erro ao atualizar permissão do usuário:', error);
    throw error;
  }
};

// Update page access for a user
export const updatePageAccess = async (
  adminId: string,
  userId: string,
  pagePath: string,
  canAccess: boolean
): Promise<boolean> => {
  try {
    const { data, error } = await supabase.rpc('update_page_access', {
      p_admin_id: adminId,
      p_user_id: userId,
      p_page_path: pagePath,
      p_can_access: canAccess
    });

    if (error) {
      console.error('Erro ao atualizar acesso à página:', error);
      throw error;
    }

    // Clear cache for this user
    clearPermissionsCache(userId);

    return true;
  } catch (error) {
    console.error('Erro ao atualizar acesso à página:', error);
    throw error;
  }
};

// Update task visibility settings for a user
export const updateTaskVisibilitySettings = async (
  adminId: string,
  userId: string,
  canSeeOwnTasks: boolean,
  canSeeTeamTasks: boolean,
  canSeeAllTasks: boolean,
  visibleUserIds: string[]
): Promise<boolean> => {
  try {
    const { data, error } = await supabase.rpc('update_task_visibility', {
      p_admin_id: adminId,
      p_user_id: userId,
      p_can_see_own_tasks: canSeeOwnTasks,
      p_can_see_team_tasks: canSeeTeamTasks,
      p_can_see_all_tasks: canSeeAllTasks,
      p_visible_user_ids: visibleUserIds
    });

    if (error) {
      console.error('Erro ao atualizar visibilidade de tarefas:', error);
      throw error;
    }

    // Clear cache for this user
    clearPermissionsCache(userId);

    return true;
  } catch (error) {
    console.error('Erro ao atualizar visibilidade de tarefas:', error);
    throw error;
  }
};

// Manage custom views (create, update, delete)
export const manageCustomView = async (
  adminId: string,
  operation: 'create' | 'update' | 'delete',
  viewData: {
    id?: string;
    name: string;
    description?: string;
    viewType: string;
    configuration: any;
    isDefault: boolean;
  }
): Promise<string> => {
  try {
    const { data, error } = await supabase.rpc('manage_custom_view', {
      p_admin_id: adminId,
      p_view_id: viewData.id || null,
      p_name: viewData.name,
      p_description: viewData.description || null,
      p_view_type: viewData.viewType,
      p_configuration: viewData.configuration,
      p_is_default: viewData.isDefault,
      p_operation: operation
    });

    if (error) {
      console.error(`Erro ao ${operation === 'create' ? 'criar' : operation === 'update' ? 'atualizar' : 'excluir'} visualização personalizada:`, error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error(`Erro ao ${operation === 'create' ? 'criar' : operation === 'update' ? 'atualizar' : 'excluir'} visualização personalizada:`, error);
    throw error;
  }
};

// Assign or unassign a view to a user
export const assignViewToUser = async (
  adminId: string,
  userId: string,
  viewId: string,
  assign: boolean
): Promise<boolean> => {
  try {
    const { data, error } = await supabase.rpc('assign_view_to_user', {
      p_admin_id: adminId,
      p_user_id: userId,
      p_view_id: viewId,
      p_assign: assign
    });

    if (error) {
      console.error(`Erro ao ${assign ? 'atribuir' : 'remover'} visualização para usuário:`, error);
      throw error;
    }

    return true;
  } catch (error) {
    console.error(`Erro ao ${assign ? 'atribuir' : 'remover'} visualização para usuário:`, error);
    throw error;
  }
};

// Manage user relationships (hierarchical access)
export const manageUserRelationship = async (
  adminId: string,
  supervisorId: string,
  subordinateId: string,
  operation: 'create' | 'delete'
): Promise<boolean> => {
  try {
    const { data, error } = await supabase.rpc('manage_user_relationship', {
      p_admin_id: adminId,
      p_supervisor_id: supervisorId,
      p_subordinate_id: subordinateId,
      p_operation: operation
    });

    if (error) {
      console.error(`Erro ao ${operation === 'create' ? 'criar' : 'excluir'} relacionamento entre usuários:`, error);
      throw error;
    }

    // Clear cache for both users
    clearPermissionsCache(supervisorId);
    clearPermissionsCache(subordinateId);

    return true;
  } catch (error) {
    console.error(`Erro ao ${operation === 'create' ? 'criar' : 'excluir'} relacionamento entre usuários:`, error);
    throw error;
  }
};

// Get all custom views
export const getAllCustomViews = async (): Promise<CustomView[]> => {
  try {
    const { data, error } = await supabase
      .from('custom_views')
      .select('*')
      .order('name');

    if (error) {
      console.error('Erro ao obter visualizações personalizadas:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Erro ao obter visualizações personalizadas:', error);
    throw error;
  }
};

// Get views assigned to a user
export const getUserAssignedViews = async (userId: string): Promise<CustomView[]> => {
  try {
    const { data, error } = await supabase
      .from('user_view_assignments')
      .select('view_id, custom_views(*)')
      .eq('user_id', userId)
      .order('custom_views.name');

    if (error) {
      console.error('Erro ao obter visualizações atribuídas ao usuário:', error);
      throw error;
    }

    return data?.map(item => item.custom_views) || [];
  } catch (error) {
    console.error('Erro ao obter visualizações atribuídas ao usuário:', error);
    throw error;
  }
};

// Get permission logs
export const getPermissionLogs = async (
  filters?: {
    userId?: string;
    adminId?: string;
    resourceType?: string;
    action?: string;
    startDate?: string;
    endDate?: string;
  }
): Promise<PermissionLogEntry[]> => {
  try {
    let query = supabase
      .from('permission_logs')
      .select('*')
      .order('created_at', { ascending: false });

    // Apply filters
    if (filters) {
      if (filters.userId) {
        query = query.eq('user_id', filters.userId);
      }
      if (filters.adminId) {
        query = query.eq('admin_id', filters.adminId);
      }
      if (filters.resourceType) {
        query = query.eq('resource_type', filters.resourceType);
      }
      if (filters.action) {
        query = query.eq('action', filters.action);
      }
      if (filters.startDate) {
        query = query.gte('created_at', filters.startDate);
      }
      if (filters.endDate) {
        query = query.lte('created_at', filters.endDate);
      }
    }

    const { data, error } = await query.limit(100);

    if (error) {
      console.error('Erro ao obter logs de permissões:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Erro ao obter logs de permissões:', error);
    throw error;
  }
};