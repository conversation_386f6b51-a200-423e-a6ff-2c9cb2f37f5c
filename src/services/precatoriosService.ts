import { supabase } from '@/lib/supabase';
import { getCachedData, clearCache, CACHE_TTL } from './cacheService';
import { cacheLogger } from '@/lib/logger'; // Usar um logger específico para cache
import { DatabaseError } from '@/types/error'; // Tipo de erro customizado, se existir

// Tipos e Interfaces
export type StatusPrecatorio =
  | 'novo'
  | 'em_analise'
  | 'em_processamento'
  | 'aguardando_cliente'
  | 'concluido'
  | 'cancelado'
  | 'arquivado'
  | 'pago';

export type TipoPrecatorio = 'federal' | 'estadual' | 'municipal' | 'rpv' | 'trabalhista' | 'comum';

export interface Precatorio {
  id: string;
  numero: string;
  cliente_id: string;
  cliente?: { id: string; nome: string; cpf_cnpj?: string }; // População opcional
  tipo: TipoPrecatorio;
  valor: number;
  data_entrada: string; // ISO Date string
  data_vencimento?: string | null; // ISO Date string
  status: StatusPrecatorio;
  responsavel_id?: string | null;
  responsavel?: { id: string; nome: string }; // População opcional
  descricao?: string | null;
  processo_judicial?: string | null;
  vara?: string | null;
  comarca?: string | null;
  tribunal?: string | null;
  observacoes?: string | null;
  prioridade?: boolean;
  created_at: string; // ISO Date string
  updated_at: string; // ISO Date string
  tags?: string[] | null;
  documentos_count?: number; // Contagem de documentos associados
  tarefas_count?: number; // Contagem de tarefas associadas
}

export interface PrecatorioFilter {
  termo?: string;
  status?: StatusPrecatorio[];
  tipo?: TipoPrecatorio[];
  cliente_id?: string;
  responsavel_id?: string | 'meus' | 'sem_responsavel';
  dataInicio?: string; // ISO Date string
  dataFim?: string; // ISO Date string
  valorMinimo?: number;
  valorMaximo?: number;
  prioridade?: boolean;
  tags?: string[];
}

export interface PrecatorioSort {
  sortBy?: keyof Precatorio | string; // Permitir string para campos customizados
  sortDirection?: 'asc' | 'desc';
}

export interface Pagination {
  page?: number;
  limit?: number;
}

export interface PaginatedPrecatorios {
  data: Precatorio[];
  total: number;
  totalPages: number;
  currentPage: number;
}

export interface PrecatorioStats {
  total: number;
  porStatus: Record<StatusPrecatorio, number>;
  porTipo: Record<TipoPrecatorio, number>;
  valorTotal: number;
  valorMedio: number;
  novosUltimos30Dias: number;
  concluidosUltimos30Dias: number;
  tempoMedioProcessamento?: number; // em dias
  emProcessamento?: number; // Adicionado para EstatisticasPrecatorios
  concluidos?: number; // Adicionado para EstatisticasPrecatorios
  tempoMedio?: number; // Adicionado para EstatisticasPrecatorios
  taxaSucesso?: number; // Adicionado para EstatisticasPrecatorios
  novos?: number; // Adicionado para EstatisticasPrecatorios
}

const PRECATORIO_CACHE_PREFIX = 'precatorios';
const SINGLE_PRECATORIO_TTL = CACHE_TTL.PRECATORIOS; // Cache individual mais longo
const LIST_PRECATORIOS_TTL = CACHE_TTL.PRECATORIOS / 2; // Lista pode mudar mais frequentemente

// Helper para construir chave de cache para listas
const getListCacheKey = (filters?: PrecatorioFilter, pagination?: Pagination, sort?: PrecatorioSort): string => {
  return `${PRECATORIO_CACHE_PREFIX}:list:${JSON.stringify(filters || {})}:${JSON.stringify(pagination || {})}:${JSON.stringify(sort || {})}`;
};

// Helper para construir chave de cache para item único
const getItemCacheKey = (id: string): string => {
  return `${PRECATORIO_CACHE_PREFIX}:item:${id}`;
};

// Helper para limpar caches relacionados a precatórios
const clearPrecatorioCaches = (id?: string) => {
  cacheLogger.info(`Limpando caches de precatórios. ID específico: ${id || 'N/A'}`);
  if (id) {
    clearCache(getItemCacheKey(id));
  }
  // Limpar todos os caches de lista e estatísticas
  clearCache(PRECATORIO_CACHE_PREFIX);
  // Disparar evento para outras partes da aplicação saberem da atualização
  document.dispatchEvent(new CustomEvent('precatorios-updated'));
};

/**
 * Busca precatórios com filtros, paginação e ordenação.
 */
export async function getPrecatorios(
  filters: PrecatorioFilter = {},
  pagination: Pagination = { page: 1, limit: 10 },
  sort: PrecatorioSort = { sortBy: 'created_at', sortDirection: 'desc' }
): Promise<PaginatedPrecatorios> {
  const cacheKey = getListCacheKey(filters, pagination, sort);

  return getCachedData(
    cacheKey,
    async () => {
      cacheLogger.debug(`Buscando precatórios no Supabase: filters=${JSON.stringify(filters)}, pagination=${JSON.stringify(pagination)}, sort=${JSON.stringify(sort)}`);
      let query = supabase
        .from('precatorios')
        .select(`
          *,
          cliente:clientes(id, nome, cpf_cnpj),
          responsavel:profiles(id, nome)
        `, { count: 'exact' }); // Pedir contagem total para paginação

      // Aplicar filtros
      if (filters.termo) {
        query = query.or(`numero.ilike.%${filters.termo}%,cliente.nome.ilike.%${filters.termo}%,descricao.ilike.%${filters.termo}%`);
      }
      if (filters.status && filters.status.length > 0) {
        query = query.in('status', filters.status);
      }
      if (filters.tipo && filters.tipo.length > 0) {
        query = query.in('tipo', filters.tipo);
      }
      if (filters.cliente_id) {
        query = query.eq('cliente_id', filters.cliente_id);
      }
      if (filters.responsavel_id) {
        if (filters.responsavel_id === 'meus') {
          const { data: { user } } = await supabase.auth.getUser();
          if (user) {
            query = query.eq('responsavel_id', user.id);
          } else {
            // Se não houver usuário, não retornar nada para "meus"
            return { data: [], total: 0, totalPages: 0, currentPage: pagination.page || 1 };
          }
        } else if (filters.responsavel_id === 'sem_responsavel') {
          query = query.is('responsavel_id', null);
        } else {
          query = query.eq('responsavel_id', filters.responsavel_id);
        }
      }
      if (filters.dataInicio) {
        query = query.gte('data_entrada', filters.dataInicio);
      }
      if (filters.dataFim) {
        query = query.lte('data_entrada', filters.dataFim);
      }
      if (filters.valorMinimo !== undefined) {
        query = query.gte('valor', filters.valorMinimo);
      }
      if (filters.valorMaximo !== undefined) {
        query = query.lte('valor', filters.valorMaximo);
      }
      if (filters.prioridade !== undefined) {
        query = query.eq('prioridade', filters.prioridade);
      }
      if (filters.tags && filters.tags.length > 0) {
        query = query.overlaps('tags', filters.tags); // ou .contains se for para todos os tags
      }

      // Aplicar paginação
      const page = pagination.page || 1;
      const limit = pagination.limit || 10;
      const offset = (page - 1) * limit;
      query = query.range(offset, offset + limit - 1);

      // Aplicar ordenação
      if (sort.sortBy && sort.sortDirection) {
        // Lógica para tratar ordenação por campos de tabelas relacionadas
        const isRelatedSort = String(sort.sortBy).includes('.');
        query = query.order(String(sort.sortBy), {
          ascending: sort.sortDirection === 'asc',
          foreignTable: isRelatedSort ? String(sort.sortBy).split('.')[0] : undefined,
        });
      }
      
      const { data, error, count } = await query;

      if (error) {
        cacheLogger.error('Erro ao buscar precatórios:', error);
        throw new DatabaseError('Falha ao buscar precatórios.', error);
      }
      
      const total = count || 0;
      return {
        data: data as Precatorio[] || [],
        total,
        totalPages: Math.ceil(total / limit),
        currentPage: page,
      };
    },
    LIST_PRECATORIOS_TTL
  );
}

/**
 * Busca um precatório específico pelo ID.
 */
export async function getPrecatorioById(id: string): Promise<Precatorio | null> {
  if (!id) {
    cacheLogger.warn('Tentativa de buscar precatório com ID nulo ou indefinido.');
    return null;
  }
  const cacheKey = getItemCacheKey(id);

  return getCachedData(
    cacheKey,
    async () => {
      cacheLogger.debug(`Buscando precatório no Supabase: id=${id}`);
      const { data, error } = await supabase
        .from('precatorios')
        .select(`
          *,
          cliente:clientes(id, nome, cpf_cnpj),
          responsavel:profiles(id, nome)
        `)
        .eq('id', id)
        .single();

      if (error) {
        if (error.code === 'PGRST116') { // Not found
          cacheLogger.warn(`Precatório com ID ${id} não encontrado.`);
          return null;
        }
        cacheLogger.error(`Erro ao buscar precatório ${id}:`, error);
        throw new DatabaseError(`Falha ao buscar precatório ${id}.`, error);
      }
      return data as Precatorio | null;
    },
    SINGLE_PRECATORIO_TTL
  );
}

/**
 * Cria um novo precatório.
 */
export async function createPrecatorio(data: Partial<Precatorio>): Promise<Precatorio> {
  cacheLogger.debug(`Criando novo precatório no Supabase: data=${JSON.stringify(data)}`);
  // Remover ID se estiver presente, pois será gerado pelo banco
  const { id, ...createData } = data;

  const { data: newPrecatorio, error } = await supabase
    .from('precatorios')
    .insert(createData)
    .select(`
      *,
      cliente:clientes(id, nome, cpf_cnpj),
      responsavel:profiles(id, nome)
    `)
    .single();

  if (error) {
    cacheLogger.error('Erro ao criar precatório:', error);
    throw new DatabaseError('Falha ao criar precatório.', error);
  }

  clearPrecatorioCaches(); // Limpar todos os caches de lista
  return newPrecatorio as Precatorio;
}

/**
 * Atualiza um precatório existente.
 */
export async function updatePrecatorio(id: string, data: Partial<Precatorio>): Promise<Precatorio> {
  if (!id) {
    throw new Error("ID do precatório é obrigatório para atualização.");
  }
  cacheLogger.debug(`Atualizando precatório no Supabase: id=${id}, data=${JSON.stringify(data)}`);
  // Remover campos que não devem ser atualizados diretamente ou são gerenciados pelo DB
  const { created_at, cliente, responsavel, ...updateData } = data;

  const { data: updatedPrecatorio, error } = await supabase
    .from('precatorios')
    .update(updateData)
    .eq('id', id)
    .select(`
      *,
      cliente:clientes(id, nome, cpf_cnpj),
      responsavel:profiles(id, nome)
    `)
    .single();

  if (error) {
    cacheLogger.error(`Erro ao atualizar precatório ${id}:`, error);
    throw new DatabaseError(`Falha ao atualizar precatório ${id}.`, error);
  }
  if (!updatedPrecatorio) {
     throw new DatabaseError(`Precatório ${id} não encontrado para atualização.`);
  }

  clearPrecatorioCaches(id); // Limpar cache do item e listas
  return updatedPrecatorio as Precatorio;
}

/**
 * Exclui um precatório (soft delete).
 * No Supabase, isso geralmente significa atualizar um campo como `deleted_at` ou `status = 'excluido'`.
 * Se for exclusão física, o método seria `.delete()`.
 */
export async function deletePrecatorio(id: string): Promise<void> {
  if (!id) {
    throw new Error("ID do precatório é obrigatório para exclusão.");
  }
  cacheLogger.debug(`Excluindo precatório no Supabase (soft delete): id=${id}`);
  const { error } = await supabase
    .from('precatorios')
    .update({ status: 'cancelado', updated_at: new Date().toISOString() }) // Exemplo de soft delete
    // .delete() // Para exclusão física
    .eq('id', id);

  if (error) {
    cacheLogger.error(`Erro ao excluir precatório ${id}:`, error);
    throw new DatabaseError(`Falha ao excluir precatório ${id}.`, error);
  }

  clearPrecatorioCaches(id); // Limpar cache do item e listas
}

/**
 * Obtém estatísticas sobre os precatórios.
 */
export async function getEstatisticasPrecatorios(filters: PrecatorioFilter = {}): Promise<PrecatorioStats> {
  const cacheKey = `${PRECATORIO_CACHE_PREFIX}:stats:${JSON.stringify(filters)}`;

  return getCachedData(
    cacheKey,
    async () => {
      cacheLogger.debug(`Calculando estatísticas de precatórios no Supabase: filters=${JSON.stringify(filters)}`);
      // Esta query pode ser complexa e pode ser melhorada com Funções RPC no Supabase
      // Por simplicidade, faremos algumas queries separadas ou uma mais genérica
      
      let query = supabase.from('precatorios').select('status, tipo, valor, created_at');

      // Aplicar filtros básicos para estatísticas (pode ser diferente dos filtros de lista)
      if (filters.status && filters.status.length > 0) query = query.in('status', filters.status);
      if (filters.tipo && filters.tipo.length > 0) query = query.in('tipo', filters.tipo);
      // Adicionar mais filtros conforme necessário para as estatísticas

      const { data: precatorios, error } = await query;

      if (error) {
        cacheLogger.error('Erro ao buscar dados para estatísticas:', error);
        throw new DatabaseError('Falha ao calcular estatísticas.', error);
      }

      if (!precatorios || precatorios.length === 0) {
        return {
          total: 0,
          porStatus: {} as Record<StatusPrecatorio, number>,
          porTipo: {} as Record<TipoPrecatorio, number>,
          valorTotal: 0,
          valorMedio: 0,
          novosUltimos30Dias: 0,
          concluidosUltimos30Dias: 0,
          emProcessamento: 0,
          concluidos: 0,
          tempoMedio: 0,
          taxaSucesso: 0,
          novos: 0,
        };
      }

      const stats: PrecatorioStats = {
        total: precatorios.length,
        porStatus: {} as Record<StatusPrecatorio, number>,
        porTipo: {} as Record<TipoPrecatorio, number>,
        valorTotal: 0,
        valorMedio: 0,
        novosUltimos30Dias: 0,
        concluidosUltimos30Dias: 0,
        emProcessamento: 0,
        concluidos: 0,
        tempoMedio: 0, // Será calculado se houver dados de data de conclusão
        taxaSucesso: 0,
        novos: 0, // Similar a novosUltimos30Dias, mas pode ser total de 'novo'
      };

      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      precatorios.forEach(p => {
        // Por status
        stats.porStatus[p.status as StatusPrecatorio] = (stats.porStatus[p.status as StatusPrecatorio] || 0) + 1;
        // Por tipo
        stats.porTipo[p.tipo as TipoPrecatorio] = (stats.porTipo[p.tipo as TipoPrecatorio] || 0) + 1;
        // Valor total
        stats.valorTotal += p.valor || 0;

        // Novos nos últimos 30 dias
        if (new Date(p.created_at) >= thirtyDaysAgo) {
          stats.novosUltimos30Dias++;
        }
        // Concluídos nos últimos 30 dias (assumindo que 'updated_at' reflete a data de conclusão)
        if (p.status === 'concluido' && (p as any).updated_at && new Date((p as any).updated_at) >= thirtyDaysAgo) {
          stats.concluidosUltimos30Dias++;
        }
        if (p.status === 'em_processamento' || p.status === 'em_analise') {
            stats.emProcessamento = (stats.emProcessamento || 0) + 1;
        }
        if (p.status === 'concluido') {
            stats.concluidos = (stats.concluidos || 0) + 1;
        }
        if (p.status === 'novo') {
            stats.novos = (stats.novos || 0) + 1;
        }
      });

      if (stats.total > 0) {
        stats.valorMedio = stats.valorTotal / stats.total;
        if (stats.concluidos && (stats.concluidos + (stats.porStatus['cancelado'] || 0)) > 0) {
            stats.taxaSucesso = (stats.concluidos / (stats.concluidos + (stats.porStatus['cancelado'] || 0))) * 100;
        }
      }
      
      // Cálculo de tempo médio de processamento (exemplo simplificado)
      // Idealmente, isso viria de uma função RPC ou cálculo mais robusto
      const concluidosComDatas = precatorios.filter(p => p.status === 'concluido' && p.created_at && (p as any).updated_at);
      if (concluidosComDatas.length > 0) {
        const totalDiasProcessamento = concluidosComDatas.reduce((sum, p) => {
            const inicio = new Date(p.created_at).getTime();
            const fim = new Date((p as any).updated_at).getTime();
            return sum + (fim - inicio) / (1000 * 60 * 60 * 24);
        }, 0);
        stats.tempoMedioProcessamento = totalDiasProcessamento / concluidosComDatas.length;
        stats.tempoMedio = stats.tempoMedioProcessamento; // Para compatibilidade com EstatisticasPrecatorios
      }


      return stats;
    },
    LIST_PRECATORIOS_TTL // Estatísticas podem usar o mesmo TTL de listas ou um pouco mais longo
  );
}

/**
 * Prepara dados de precatórios para exportação.
 * Pode retornar os dados diretamente ou uma URL para um arquivo gerado no backend.
 */
export async function exportarPrecatorios(filters: PrecatorioFilter = {}): Promise<{ data?: Precatorio[], url?: string, message?: string }> {
  cacheLogger.debug(`Preparando exportação de precatórios: filters=${JSON.stringify(filters)}`);
  try {
    // Para exportação, geralmente não aplicamos paginação, mas todos os filtros sim.
    const { data: precatorios } = await getPrecatorios(filters, { limit: 10000, page: 1 }, {}); // Limite alto para pegar "todos"

    if (!precatorios || precatorios.length === 0) {
      return { message: 'Nenhum precatório encontrado para exportar com os filtros aplicados.' };
    }

    // Simplesmente retornar os dados para o frontend formatar (ex: para CSV)
    // Em um cenário real, isso poderia chamar uma função RPC no Supabase que gera um arquivo.
    return { data: precatorios };

  } catch (error) {
    cacheLogger.error('Erro ao preparar dados para exportação:', error);
    throw new DatabaseError('Falha ao exportar precatórios.', error);
  }
}

/**
 * Busca precatórios (alias para getPrecatorios para compatibilidade)
 */
export async function buscarPrecatorios(
  filters: PrecatorioFilter = {},
  pagination: Pagination = { page: 1, limit: 10 },
  sort: PrecatorioSort = { sortBy: 'created_at', sortDirection: 'desc' }
): Promise<PaginatedPrecatorios> {
  return getPrecatorios(filters, pagination, sort);
}

/**
 * Busca todos os precatórios sem paginação (para compatibilidade)
 */
export async function buscarTodosPrecatorios(
  tipo: TipoPrecatorio = 'federal'
): Promise<Precatorio[]> {
  try {
    cacheLogger.debug(`Buscando todos os precatórios do tipo ${tipo}`);

    const filters: PrecatorioFilter = { tipo: [tipo] };
    const { data: precatorios } = await getPrecatorios(filters, { limit: 10000, page: 1 }, {});

    return precatorios || [];
  } catch (error) {
    cacheLogger.error(`Erro ao buscar todos os precatórios do tipo ${tipo}:`, error);
    throw new DatabaseError(`Falha ao buscar precatórios do tipo ${tipo}.`, error);
  }
}
