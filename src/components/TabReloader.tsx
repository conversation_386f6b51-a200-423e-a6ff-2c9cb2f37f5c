import { useEffect, useRef } from 'react';

/**
 * Componente que força o recarregamento da página quando o usuário retorna à aba
 * Esta é uma solução para o problema de persistência de sessão
 */
export function TabReloader() {
  const lastVisibilityChangeRef = useRef(Date.now());
  const reloadTimeoutRef = useRef<number | null>(null);
  const isReloadingRef = useRef(false);

  useEffect(() => {
    // Função para lidar com mudanças de visibilidade
    const handleVisibilityChange = () => {
      const now = Date.now();
      const timeSinceLastChange = now - lastVisibilityChangeRef.current;
      lastVisibilityChangeRef.current = now;

      // Se a página se tornou visível e ficou oculta por mais de 10 segundos
      // Tempo aumentado para reduzir recarregamentos desnecessários
      if (document.visibilityState === 'visible' && timeSinceLastChange > 10000) {
        // Evitar múltiplos recarregamentos
        if (isReloadingRef.current) {
          return;
        }

        isReloadingRef.current = true;

        // Limpar qualquer timeout pendente
        if (reloadTimeoutRef.current) {
          window.clearTimeout(reloadTimeoutRef.current);
          reloadTimeoutRef.current = null;
        }

        // Definir um timeout para recarregar a página
        reloadTimeoutRef.current = window.setTimeout(() => {
          // Verificar se ainda estamos visíveis
          if (document.visibilityState === 'visible') {
            // Salvar a URL atual no sessionStorage
            try {
              sessionStorage.setItem('last_url', window.location.pathname);
              sessionStorage.setItem('reload_timestamp', Date.now().toString());
            } catch (error) {
              // Erro silencioso
            }

            // Recarregar a página silenciosamente
            window.location.reload();
          } else {
            isReloadingRef.current = false;
          }
        }, 1500); // Aumentamos o atraso para dar mais tempo à página
      }
    };

    // Registrar o listener de visibilidade
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Verificar se estamos retornando de um recarregamento - sem logs
    try {
      const reloadTimestamp = sessionStorage.getItem('reload_timestamp');
      const lastUrl = sessionStorage.getItem('last_url');

      if (reloadTimestamp && lastUrl) {
        const timestamp = parseInt(reloadTimestamp, 10);
        const now = Date.now();

        // Se o recarregamento foi recente (menos de 5 segundos)
        if (now - timestamp < 5000) {
          // Limpar os dados de recarregamento
          sessionStorage.removeItem('reload_timestamp');

          // Se a URL atual não corresponde à URL salva e não estamos na página de login
          if (lastUrl !== window.location.pathname &&
              !window.location.pathname.includes('/login')) {
            // Limpar a URL salva
            sessionStorage.removeItem('last_url');

            // Redirecionar para a última URL
            window.location.href = lastUrl;
          } else {
            // Limpar a URL salva
            sessionStorage.removeItem('last_url');
          }
        } else {
          // Limpar dados antigos
          sessionStorage.removeItem('reload_timestamp');
          sessionStorage.removeItem('last_url');
        }
      }
    } catch (error) {
      // Erro silencioso
    }

    // Cleanup
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);

      if (reloadTimeoutRef.current) {
        window.clearTimeout(reloadTimeoutRef.current);
        reloadTimeoutRef.current = null;
      }
    };
  }, []);

  // Este componente não renderiza nada
  return null;
}

export default TabReloader;
