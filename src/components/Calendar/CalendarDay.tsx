import React from "react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Evento } from "../../utils/calendarHelpers";
import { getEventosDoDia } from "../../utils/calendarHelpers";
import { tipoEventoConfig, eventColors } from "../../data/eventConfig";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { CalendarIcon, Clock, MapPin, User } from "lucide-react";

interface CalendarDayProps {
  currentDate: Date;
  filteredEvents: Evento[];
  onEventClick: (event: Evento) => void;
}

export const CalendarDay: React.FC<CalendarDayProps> = ({
  currentDate,
  filteredEvents,
  onEventClick
}) => {
  const eventosHoje = getEventosDoDia(currentDate, filteredEvents);
  const formattedDate = format(currentDate, 'EEEE, d MMMM yyyy', { locale: ptBR });
  const isToday = new Date().toDateString() === currentDate.toDateString();

  // Horários para exibir na grade
  const visibleHours = { start: 7, end: 22 };
  const hourSlotHeightClass = "h-6"; // Classe Tailwind para altura (24px) - BEM Reduzido!
  const pixelsPerHour = 24; // Altura em pixels correspondente a h-6
  const totalPixelHeight = 24 * pixelsPerHour; // Altura total da grade

  // Horas a serem mostradas
  const hoursToShow = [];
  for (let i = 0; i < 24; i++) {
    hoursToShow.push(i);
  }

  // Funções de cálculo de posição/altura em pixels
  const getTopOffset = (hour: number, minute: number = 0): number => {
      return (hour + minute / 60) * pixelsPerHour;
  };
  const getEventHeight = (durationMinutes: number): number => {
      return (durationMinutes / 60) * pixelsPerHour;
  };

  // Função para calcular a hora de fim de um evento de forma segura
  const calcularHoraFim = (hour: number, minute: number, duracao: number): string => {
    try {
      // Calcular minutos totais
      let totalMinutos = hour * 60 + minute + duracao;

      // Calcular nova hora e minuto
      const novaHora = Math.floor(totalMinutos / 60) % 24;
      const novoMinuto = totalMinutos % 60;

      // Formatar como string HH:MM
      return `${novaHora.toString().padStart(2, '0')}:${novoMinuto.toString().padStart(2, '0')}`;
    } catch (error) {
      console.error('Erro ao calcular hora de fim:', error);
      return '--:--';
    }
  };

  return (
    <div className="space-y-2"> {/* Espaçamento ainda menor */}
      <div className="flex items-center justify-between flex-wrap gap-1 px-1"> {/* Menos padding */}
        <h2 className="text-lg font-bold">{formattedDate}</h2> {/* Fonte menor */}
        <div className="flex items-center gap-1.5">
          <Badge variant="outline" className={isToday ? "bg-gray-200 dark:bg-gray-800 text-[10px] px-1.5 py-0" : "text-[10px] px-1.5 py-0"}>
            {isToday ? "Hoje" : ""}
          </Badge>
          <Button
            variant="outline"
            size="sm" // Voltar para sm
            className="h-6 px-1.5 text-[10px]" // Manter classes de tamanho
            onClick={() => alert("Funcionalidade de criar evento será implementada em breve!")}
          >
            Criar evento
          </Button>
        </div>
      </div>

      <ScrollArea className="flex-1 relative border rounded-md">
        <div className={`relative bg-gray-50 dark:bg-gray-900 overflow-hidden`} style={{ height: `${totalPixelHeight}px` }}>
          <div className="sticky top-0 bg-white dark:bg-gray-800 z-10 px-1 py-0.5 border-b text-center shadow-sm">
            <span className="text-[10px] font-medium text-muted-foreground">
              Visível: {visibleHours.start}:00 - {visibleHours.end}:00
            </span>
          </div>

          <div className="absolute inset-0 grid grid-cols-[40px_1fr]"> {/* Coluna de hora AINDA mais estreita */}
            <div className="pt-0 pr-1 border-r border-gray-100 dark:border-gray-800/50">
              {hoursToShow.map((hour) => (
                <div
                  key={hour}
                  className={`text-right text-[10px] text-gray-400 ${hourSlotHeightClass} flex items-center justify-end pr-1 border-b border-gray-100 dark:border-gray-800/50`}
                >
                  {hour}:00
                </div>
              ))}
            </div>

            <div className="relative">
              {hoursToShow.map((hour) => (
                <div
                  key={`line-${hour}`}
                  className="absolute w-full h-px bg-gray-200 dark:bg-gray-700/50"
                  style={{ top: `${getTopOffset(hour)}px` }}
                ></div>
              ))}

              {isToday && (
                <div
                  className="absolute w-full opacity-10 bg-gray-200 dark:bg-gray-700 z-0"
                  style={{ top: `${getTopOffset(new Date().getHours())}px`, height: `${pixelsPerHour}px` }}
                ></div>
              )}

              {isToday && (
                <div
                  className="absolute w-full h-0.5 bg-red-500 z-10 flex items-center"
                  style={{ top: `${getTopOffset(new Date().getHours(), new Date().getMinutes())}px` }}
                >
                  <span className="px-0.5 text-[8px] text-white bg-red-500 rounded-sm ml-0.5 shadow">
                    {format(new Date(), 'HH:mm')}
                  </span>
                </div>
              )}

              {eventosHoje.map(evento => {
                const [hour, minute] = evento.hora.split(':').map(Number);
                const topPx = getTopOffset(hour, minute);
                const heightPx = getEventHeight(evento.duracao);
                const opacity = (hour < visibleHours.start || hour >= visibleHours.end) ? 'opacity-40' : 'opacity-100';

                return (
                  <TooltipProvider key={evento.id}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div
                          className={`absolute rounded-sm p-px shadow ${eventColors[evento.tipo] || 'bg-gray-300 dark:bg-gray-700'} hover:brightness-110 transition-all cursor-pointer overflow-hidden ${opacity}`}
                          style={{
                            top: `${topPx}px`,
                            height: `${Math.max(heightPx, 12)}px`, // Altura mínima BEM pequena
                            left: '2px', // Quase colado
                            right: '2px'
                          }}
                          onClick={() => onEventClick(evento)}
                        >
                          <div className="flex items-start space-x-0.5 h-full">
                            <span className="text-[9px] mt-px opacity-70">{tipoEventoConfig[evento.tipo]?.icon}</span>
                            <div className="flex-1 min-w-0">
                              <h3 className="font-medium text-[10px] leading-tight truncate">{evento.titulo}</h3>
                              {heightPx > 20 && (
                                <p className="text-[9px] opacity-60 truncate leading-tight">{evento.descricao}</p>
                              )}
                            </div>
                          </div>
                        </div>
                      </TooltipTrigger>
                      <TooltipContent side="right">
                        <div className="p-2 space-y-2 max-w-xs">
                          <div className="font-bold">{evento.titulo}</div>
                          <div className="text-sm">{evento.descricao}</div>
                          <div className="flex items-center text-xs">
                            <Clock className="w-3 h-3 mr-1" />
                            {evento.hora} - {calcularHoraFim(hour, minute, evento.duracao)}
                          </div>
                          <div className="flex items-center text-xs">
                            <MapPin className="w-3 h-3 mr-1" />
                            {evento.local}
                          </div>
                          <div className="flex items-center text-xs">
                            <User className="w-3 h-3 mr-1" />
                            {evento.responsavel}
                          </div>
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                );
              })}
            </div>
          </div>
        </div>
      </ScrollArea>

      {eventosHoje.length === 0 && (
        <div className="flex flex-col items-center justify-center p-4 text-center text-gray-500 border rounded-lg bg-white dark:bg-gray-800 mt-3">
          <CalendarIcon className="w-10 h-10 mb-1 text-gray-400" />
          <h3 className="text-base font-medium">Nenhum evento para este dia</h3>
          <p className="max-w-sm mt-1 text-xs">
            {isToday
              ? "Você não tem nenhum evento agendado para hoje."
              : `Você não tem nenhum evento agendado para ${format(currentDate, 'd MMMM', { locale: ptBR })}.`
            }
          </p>
          <Button
            variant="outline"
            size="sm" // Voltar para sm
            className="mt-3 h-6 px-2 text-[10px]" // Manter classes de tamanho
            onClick={() => alert("Funcionalidade de criar evento será implementada em breve!")}
          >
            Criar evento
          </Button>
        </div>
      )}
    </div>
  );
};