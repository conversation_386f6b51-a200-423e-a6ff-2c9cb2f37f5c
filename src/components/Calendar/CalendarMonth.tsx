import React from "react";
import { format, isSameDay } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Evento } from "../../utils/calendarHelpers";
import { getEventosDoDia } from "../../utils/calendarHelpers";
import { tipoEventoConfig, eventColors } from "../../data/eventConfig";
import { Card, CardContent } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Clock } from "lucide-react";

interface CalendarMonthProps {
  currentDate: Date;
  filteredEvents: Evento[];
  onEventClick: (event: Evento) => void;
  onDateClick: (date: Date) => void;
}

export const CalendarMonth: React.FC<CalendarMonthProps> = ({
  currentDate,
  filteredEvents,
  onEventClick,
  onDateClick
}) => {
  const year = currentDate.getFullYear();
  const month = currentDate.getMonth();

  // Primeiro dia do mês
  const firstDay = new Date(year, month, 1);
  // Último dia do mês
  const lastDay = new Date(year, month + 1, 0);

  // Dia da semana do primeiro dia (0 = domingo, 6 = sábado)
  const firstDayOfWeek = firstDay.getDay();

  // Total de dias no mês
  const daysInMonth = lastDay.getDate();

  // Criar array para todos os dias do mês + dias do mês anterior para preencher a primeira semana
  const days = [];

  // Adicionar dias do mês anterior
  for (let i = 0; i < firstDayOfWeek; i++) {
    const prevMonthDay = new Date(year, month, -firstDayOfWeek + i + 1);
    const dayEvents = getEventosDoDia(prevMonthDay, filteredEvents);

    days.push({
      date: prevMonthDay,
      isCurrentMonth: false,
      hasEvents: dayEvents.length > 0,
      events: dayEvents
    });
  }

  // Adicionar dias do mês atual
  for (let i = 1; i <= daysInMonth; i++) {
    const currentDay = new Date(year, month, i);
    const dayEvents = getEventosDoDia(currentDay, filteredEvents);

    days.push({
      date: currentDay,
      isCurrentMonth: true,
      isToday: isSameDay(new Date(), currentDay),
      hasEvents: dayEvents.length > 0,
      events: dayEvents
    });
  }

  // Adicionar dias do próximo mês para completar a grade
  const remainingDays = 7 - (days.length % 7);
  if (remainingDays < 7) {
    for (let i = 1; i <= remainingDays; i++) {
      const nextMonthDay = new Date(year, month + 1, i);
      const dayEvents = getEventosDoDia(nextMonthDay, filteredEvents);

      days.push({
        date: nextMonthDay,
        isCurrentMonth: false,
        hasEvents: dayEvents.length > 0,
        events: dayEvents
      });
    }
  }

  // Agrupar dias em semanas
  const weeks = [];
  for (let i = 0; i < days.length; i += 7) {
    weeks.push(days.slice(i, i + 7));
  }

  // Garantir que temos 6 semanas para mostrar (42 dias) para evitar cortes
  while (weeks.length < 6) {
    const lastWeek = weeks[weeks.length - 1];
    const lastDay = lastWeek[lastWeek.length - 1].date;

    const nextWeek = [];
    for (let i = 1; i <= 7; i++) {
      const nextDay = new Date(lastDay);
      nextDay.setDate(lastDay.getDate() + i);
      const dayEvents = getEventosDoDia(nextDay, filteredEvents);

      nextWeek.push({
        date: nextDay,
        isCurrentMonth: false,
        hasEvents: dayEvents.length > 0,
        events: dayEvents,
        isToday: isSameDay(nextDay, new Date())
      });
    }

    weeks.push(nextWeek);
  }

  return (
    <div className="space-y-4">
      <h2 className="text-2xl font-bold">{format(new Date(year, month), 'MMMM yyyy', { locale: ptBR })}</h2>
      <Card className="overflow-hidden">
        <CardContent className="p-0">
          {/* Cabeçalho dos dias da semana */}
          <div className="grid grid-cols-7 bg-gray-100 dark:bg-gray-800 border-b">
            {['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'].map((day, i) => (
              <div key={i} className="p-2 text-center font-medium text-sm">{day}</div>
            ))}
          </div>

          {/* Grade do calendário */}
          <div className="bg-white dark:bg-gray-900">
            {weeks.map((week, weekIndex) => (
              <div key={weekIndex} className="grid grid-cols-7 border-b last:border-b-0">
                {week.map((day, dayIndex) => (
                  <div
                    key={dayIndex}
                    className={`min-h-[140px] p-1 border-r last:border-r-0 ${
                      day.isCurrentMonth ? 'bg-white dark:bg-gray-900' : 'bg-gray-50 dark:bg-gray-800/30 text-gray-400 dark:text-gray-500'
                    } ${day.isToday ? 'bg-muted dark:bg-muted' : ''} cursor-pointer transition-colors hover:bg-gray-50 dark:hover:bg-gray-800/70`}
                    onClick={() => onDateClick(day.date)}
                  >
                    <div className={`text-right p-1 ${
                      day.isToday
                        ? 'bg-muted-foreground text-muted dark:bg-muted-foreground dark:text-muted rounded-full w-7 h-7 flex items-center justify-center ml-auto'
                        : ''
                    }`}>
                      {format(day.date, 'd')}
                    </div>

                    <ScrollArea className="h-[105px]">
                      <div className="space-y-1 mt-1 pr-1">
                        {day.events.length > 0 && (
                          day.events.slice(0, 4).map(evento => (
                            <TooltipProvider key={evento.id}>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <div
                                    className={`p-1 rounded text-xs flex items-center ${eventColors[evento.tipo]} cursor-pointer truncate mb-1 hover:brightness-95 dark:hover:brightness-110 transition-all`}
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      onEventClick(evento);
                                    }}
                                  >
                                    <span className="mr-1 flex-shrink-0">{tipoEventoConfig[evento.tipo].icon}</span>
                                    <span className="truncate">{evento.titulo}</span>
                                  </div>
                                </TooltipTrigger>
                                <TooltipContent side="right" sideOffset={5}>
                                  <div className="p-3 space-y-2 max-w-xs">
                                    <div className="font-bold text-sm">{evento.titulo}</div>
                                    {evento.descricao && (
                                      <div className="text-sm text-muted-foreground">{evento.descricao}</div>
                                    )}
                                    <div className="flex items-center text-xs text-muted-foreground">
                                      <Clock className="w-3 h-3 mr-1" />
                                      {evento.hora}
                                    </div>
                                    <div className="text-xs flex items-center mt-1">
                                      <span className={`inline-block w-2 h-2 rounded-full mr-1.5 ${eventColors[evento.tipo].replace('bg-', 'bg-')}`}></span>
                                      <span className="text-muted-foreground">{tipoEventoConfig[evento.tipo].nome}</span>
                                    </div>
                                  </div>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          ))
                        )}
                        {day.events.length > 4 && (
                          <div
                            className="text-xs text-center text-gray-500 bg-gray-100 dark:bg-gray-800 rounded py-0.5 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors mt-1 font-medium"
                            onClick={(e) => {
                              e.stopPropagation();
                              onDateClick(day.date);
                            }}
                          >
                            +{day.events.length - 4} mais evento{day.events.length - 4 > 1 ? 's' : ''}
                          </div>
                        )}
                      </div>
                    </ScrollArea>
                  </div>
                ))}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};