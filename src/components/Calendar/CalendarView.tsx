import React, { useCallback } from 'react';
import { useSupabase } from '@supabase/auth-helpers-react';
import { toast } from '@/components/ui/use-toast';

// Carregar eventos
const carregarEventos = useCallback(async () => {
  try {
    setLoading(true);
    
    // Verificar se temos uma sessão ativa
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      console.error("Sessão não encontrada. Usuário não está autenticado.");
      toast({
        title: "Erro de autenticação",
        description: "Sessão expirada. Por favor, faça login novamente.",
        variant: "destructive",
      });
      return;
    }

    let eventos: Evento[] = [];
    
    // Se temos um intervalo de datas selecionado, buscar por período
    if (periodo.dataInicio && periodo.dataFim) {
      eventos = await getEventosPorPeriodo(
        periodo.dataInicio, 
        periodo.dataFim
      );
    } else {
      // <PERSON><PERSON><PERSON> contrá<PERSON>, buscar todos os eventos
      eventos = await getEventos();
    }
    
    setEventos(eventos);
  } catch (error) {
    console.error("Erro ao carregar eventos:", error);
    toast({
      title: "Erro",
      description: error instanceof Error 
        ? error.message 
        : "Não foi possível carregar os eventos. Tente novamente mais tarde.",
      variant: "destructive",
    });
  } finally {
    setLoading(false);
  }
}, [periodo.dataInicio, periodo.dataFim, toast]); 