import { useState, useEffect, useCallback, useMemo } from 'react';

import { format, addDays, subDays, addMonths, subMonths, startOfMonth, endOfMonth, startOfDay, endOfDay, startOfWeek, endOfWeek } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { ChevronLeft, ChevronRight, CalendarDays, Calendar as CalendarIcon, Grid, Loader2, Plus, Filter as FilterIcon } from 'lucide-react';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { CalendarDay } from './CalendarDay';
import { CalendarWeek } from './CalendarWeek';
import { CalendarMonth } from './CalendarMonth';
import { CalendarAgenda } from './CalendarAgenda';
import { CalendarFilters } from './CalendarFilters';
import { EventDialog } from './EventDialog';
import { Evento, Visualizacao, Filtro } from '../../utils/calendarHelpers';
// import { eventos as eventosData } from '../../data/mockEvents';
import { getEventosPorPeriodo } from '@/services/eventosService';
import { useToast } from '@/components/ui/use-toast';
import { supabase } from '@/lib/supabase';
import { cn } from '@/lib/utils';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";

interface CalendarProps {
  layout?: 'default' | 'compact' | 'list-only';
}

const Calendar = ({ layout = 'default' }: CalendarProps) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  // Definir visualização padrão com base no layout
  const [visualizacao, setVisualizacao] = useState<Visualizacao>(layout === 'list-only' ? 'agenda' : 'mes');
  const [eventos, setEventos] = useState<Evento[]>([]);
  const [filteredEvents, setFilteredEvents] = useState<Evento[]>([]);
  const [filtros, setFiltros] = useState<Filtro>({
    tipo: [],
    status: [],
    texto: '',
  });
  const [selectedEvent, setSelectedEvent] = useState<Evento | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isCreatingEvent, setIsCreatingEvent] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  // Configurar altura da viewport para dispositivos móveis (corrigir altura em iOS)
  useEffect(() => {
    const setVh = () => {
      const vh = window.innerHeight * 0.01;
      document.documentElement.style.setProperty('--vh', `${vh}px`);
    };

    // Executar imediatamente
    setVh();

    // Adicionar um pequeno atraso para garantir que a altura seja correta após a rotação da tela
    const debouncedSetVh = () => {
      setTimeout(setVh, 100);
    };

    window.addEventListener('resize', debouncedSetVh);
    window.addEventListener('orientationchange', debouncedSetVh);

    return () => {
      window.removeEventListener('resize', debouncedSetVh);
      window.removeEventListener('orientationchange', debouncedSetVh);
    };
  }, []);

  // Função melhorada para renovar a sessão a cada 5 minutos
  useEffect(() => {
    let mounted = true;

    // Verificar tokens existentes para depuração
    console.log("Calendar: Tokens disponíveis:",
      Object.keys(localStorage).filter(k => k.includes('sb-') || k.includes('supabase')));

    // Função para renovar a sessão de forma mais robusta
    const refreshSession = async () => {
      if (!mounted) return;

      try {
        console.log('Calendar: Renovando sessão do Supabase...');

        // Verificar sessão atual antes de tentar renovar
        const { data: sessionData } = await supabase.auth.getSession();
        if (!sessionData.session) {
          console.warn('Calendar: Sessão não encontrada, tentando recuperar...');
        }

        // Tentar renovar a sessão
        const { data, error } = await supabase.auth.refreshSession();

        if (error) {
          console.error('Calendar: Erro ao renovar sessão:', error);
          toast({
            title: "Erro de autenticação",
            description: "Houve um problema ao renovar sua sessão. Os dados continuarão disponíveis, mas você pode precisar fazer login novamente em breve.",
            variant: "destructive"
          });
        } else if (data.session) {
          console.log('Calendar: Sessão renovada com sucesso');

          // Se não tivermos perfil no localStorage, mas tivermos sessão, tentar criar
          const userProfileStr = localStorage.getItem("userProfile");
          if (!userProfileStr && data.session.user) {
            try {
              console.log('Calendar: Tentando recuperar perfil para localStorage');
              const { data: profileData } = await supabase
                .from("profiles")
                .select("*")
                .eq("id", data.session.user.id)
                .single();

              if (profileData) {
                localStorage.setItem("userProfile", JSON.stringify(profileData));
                console.log('Calendar: Perfil recuperado para localStorage');
              }
            } catch (profileError) {
              console.warn('Calendar: Erro ao recuperar perfil:', profileError);
            }
          }
        }
      } catch (error) {
        console.error('Calendar: Erro inesperado ao renovar sessão:', error);
      }
    };

    // Renovar imediatamente e depois a cada 5 minutos
    refreshSession();
    const interval = setInterval(refreshSession, 5 * 60 * 1000);

    return () => {
      mounted = false;
      clearInterval(interval);
    };
  }, []);

  // Buscar eventos do banco de dados
  const fetchEventos = useCallback(async () => {
    try {
      setIsLoading(true);
      console.log("Calendar: Buscando eventos...");

      // Sempre tentar renovar a sessão antes de buscar eventos
      try {
        await supabase.auth.refreshSession();
      } catch (refreshError) {
        console.warn("Calendar: Não foi possível renovar a sessão antes de buscar eventos:", refreshError);
        // Continuar mesmo assim, talvez a sessão atual ainda seja válida
      }

      // Calcular intervalo de datas
      let dataInicio: Date, dataFim: Date;

      if (visualizacao === 'dia') {
        dataInicio = startOfDay(currentDate);
        dataFim = endOfDay(currentDate);
      } else if (visualizacao === 'semana') {
        dataInicio = startOfWeek(currentDate, { weekStartsOn: 0 });
        dataFim = endOfWeek(currentDate, { weekStartsOn: 0 });
      } else if (visualizacao === 'mes') {
        dataInicio = startOfMonth(currentDate);
        dataFim = endOfMonth(currentDate);
      } else if (visualizacao === 'agenda') {
        // Para agenda, mostrar 30 dias a partir de hoje
        dataInicio = startOfDay(new Date());
        dataFim = addDays(dataInicio, 30);
      } else {
        dataInicio = startOfWeek(currentDate, { weekStartsOn: 0 });
        dataFim = endOfWeek(currentDate, { weekStartsOn: 0 });
      }

      // Formatar datas
      const dataInicioStr = format(dataInicio, 'yyyy-MM-dd');
      const dataFimStr = format(dataFim, 'yyyy-MM-dd');

      console.log(`Calendar: Buscando eventos de ${dataInicioStr} até ${dataFimStr}`);

      // Buscar eventos com tratamento de erros aprimorado
      try {
        const eventosCarregados = await getEventosPorPeriodo(dataInicioStr, dataFimStr);
        setEventos(eventosCarregados);
        console.log(`Calendar: ${eventosCarregados.length} eventos carregados com sucesso`);
      } catch (fetchError: any) {
        console.error("Calendar: Erro ao carregar eventos:", fetchError);

        // Verificar se é erro de autenticação (401)
        if (fetchError.code === "PGRST301" ||
            (fetchError.message && fetchError.message.includes("JWT")) ||
            fetchError.status === 401) {
          console.warn("Calendar: Erro de autenticação ao buscar eventos, tentando renovar sessão");

          // Tentar renovar sessão e buscar novamente
          try {
            const { data } = await supabase.auth.refreshSession();
            if (data.session) {
              console.log("Calendar: Sessão renovada, tentando buscar eventos novamente");
              const eventosRetentativa = await getEventosPorPeriodo(dataInicioStr, dataFimStr);
              setEventos(eventosRetentativa);
              console.log(`Calendar: ${eventosRetentativa.length} eventos carregados após renovação da sessão`);
            }
          } catch (retryError) {
            console.error("Calendar: Falha na segunda tentativa:", retryError);
            toast({
              title: "Erro de autenticação",
              description: "Não foi possível validar sua sessão. Por favor, recarregue a página ou faça login novamente.",
              variant: "destructive"
            });
          }
        } else {
          // Outro tipo de erro
          toast({
            title: "Erro",
            description: "Não foi possível carregar os eventos. Por favor, tente novamente.",
            variant: "destructive"
          });
        }
      }
    } catch (error) {
      console.error("Calendar: Erro não tratado:", error);
      toast({
        title: "Erro inesperado",
        description: "Ocorreu um erro ao processar sua solicitação.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }, [currentDate, visualizacao, toast]);

  // Carregar eventos quando a data ou visualização mudar
  useEffect(() => {
    fetchEventos();
  }, [currentDate, visualizacao, fetchEventos]);

  // Atualizar eventos filtrados quando os filtros ou eventos mudarem
  useEffect(() => {
    if (!Array.isArray(eventos)) {
      console.error("Eventos não é um array válido:", eventos);
      setFilteredEvents([]);
      return;
    }
    try {
      let result = [...eventos];
      if (filtros.tipo && filtros.tipo.length > 0) {
        result = result.filter(evento => evento && evento.tipo && filtros.tipo.includes(evento.tipo));
      }
      if (filtros.status && filtros.status.length > 0) {
        result = result.filter(evento => evento && evento.status && filtros.status.includes(evento.status));
      }
      if (filtros.texto) {
        const termo = filtros.texto.toLowerCase();
        result = result.filter(evento => {
          if (!evento) return false;
          const tituloMatch = evento.titulo && evento.titulo.toLowerCase().includes(termo);
          const descricaoMatch = evento.descricao && evento.descricao.toLowerCase().includes(termo);
          const localMatch = evento.local && evento.local.toLowerCase().includes(termo);
          return tituloMatch || descricaoMatch || localMatch;
        });
      }
      result = result.filter(evento => evento && evento.id && evento.titulo && evento.data);
      setFilteredEvents(result);
    } catch (error) {
      console.error("Erro ao filtrar eventos:", error);
      setFilteredEvents([]);
    }
  }, [filtros, eventos]);

  const handleDateChange = (direction: 'prev' | 'next') => {
    if (visualizacao === 'dia') {
      setCurrentDate(prev => (direction === 'prev' ? subDays(prev, 1) : addDays(prev, 1)));
    } else if (visualizacao === 'semana') {
      setCurrentDate(prev => (direction === 'prev' ? subDays(prev, 7) : addDays(prev, 7)));
    } else if (visualizacao === 'mes' || visualizacao === 'agenda') {
      setCurrentDate(prev => (direction === 'prev' ? subMonths(prev, 1) : addMonths(prev, 1)));
    }
  };

  const handleEventClick = (event: Evento) => {
    setSelectedEvent(event);
    setIsCreatingEvent(false);
    setIsDialogOpen(true);
  };

  const handleDateClick = (date: Date) => {
    setCurrentDate(date);
    setVisualizacao('dia');
  };

  const handleTodayClick = () => {
    setCurrentDate(new Date());
  };

  const handleEventUpdated = () => {
    fetchEventos();
  };

  const handleAddEvent = () => {
    if (isLoading) {
      toast({
        title: "Aguarde",
        description: "Carregando dados do calendário. Tente novamente em alguns instantes.",
        variant: "default",
      });
      return;
    }
    setSelectedEvent(null);
    setIsCreatingEvent(true);
    setIsDialogOpen(true);
  };

  const renderAgendaView = () => {
    if (isLoading) {
      return (
        <div className="p-4 text-center">
          <Loader2 className="w-12 h-12 text-gray-500 animate-spin mb-4 mx-auto" />
          <p className="text-gray-500">Carregando eventos...</p>
        </div>
      );
    }
    if (!filteredEvents || !Array.isArray(filteredEvents) || filteredEvents.length === 0) {
      return (
        <div className="p-4 text-center">
          <CalendarIcon className="w-12 h-12 text-gray-300 dark:text-gray-600 mb-4 mx-auto" />
          <p className="text-gray-500">Nenhum evento encontrado para exibir na agenda.</p>
        </div>
      );
    }
    const eventosValidos = filteredEvents.filter(evento => evento && evento.data && typeof evento.data === 'string');
    if (eventosValidos.length !== filteredEvents.length) {
      console.warn("Alguns eventos têm formato de data inválido:", filteredEvents.filter(e => !e || !e.data || typeof e.data !== 'string'));
    }
    return (
      <CalendarAgenda
        currentDate={currentDate}
        filteredEvents={eventosValidos}
        onEventClick={handleEventClick}
      />
    );
  };

  // Contar filtros ativos
  const filtrosAtivosCount = useMemo(() => {
    let count = 0;
    if (filtros.texto) count++;
    if (filtros.tipo.length > 0) count++;
    if (filtros.status.length > 0) count++;
    return count;
  }, [filtros]);

  // Renderizar apenas a lista de eventos se o layout for list-only
  if (layout === 'list-only') {
    return (
      <div className="flex flex-col h-full w-full overflow-hidden">
        {isLoading ? (
          <div className="flex items-center justify-center p-8">
            <Loader2 className="h-6 w-6 animate-spin text-primary" />
          </div>
        ) : (
          <ScrollArea className="h-[calc(100vh-220px)]">
            <div className="p-4">
              <CalendarAgenda
                currentDate={currentDate}
                filteredEvents={filteredEvents.filter(evento => evento && evento.data && typeof evento.data === 'string')}
                onEventClick={handleEventClick}
              />
            </div>
          </ScrollArea>
        )}
      </div>
    );
  }

  return (
    <div className={cn(
      "flex flex-col h-full w-full bg-card rounded-lg shadow-sm border overflow-hidden",
      layout === 'compact' && "max-h-[1000px]"
    )}>
      {/* Cabeçalho Fixo com Controles - Mais Compacto */}
      <div className="flex flex-wrap items-center justify-between gap-2 p-2 border-b bg-muted/30 flex-shrink-0">
        {/* Controles de Navegação de Data (Grupo Esquerda) */}
        <div className="flex items-center gap-1.5">
          <Button variant="outline" size="icon" className="h-8 w-8" onClick={handleTodayClick} title="Hoje">
            <CalendarIcon className="h-3.5 w-3.5" />
          </Button>
          <Button variant="outline" size="icon" className="h-8 w-8" onClick={() => handleDateChange('prev')} title="Anterior">
            <ChevronLeft className="h-3.5 w-3.5" />
          </Button>
          <Button variant="outline" size="icon" className="h-8 w-8" onClick={() => handleDateChange('next')} title="Próximo">
            <ChevronRight className="h-3.5 w-3.5" />
          </Button>
          <div className="text-base font-medium ml-1 capitalize min-w-[140px] text-center whitespace-nowrap">
             {visualizacao === 'dia' && format(currentDate, "EEEE, dd MMMM", { locale: ptBR })}
             {visualizacao === 'semana' && `Semana ${format(startOfWeek(currentDate, { weekStartsOn: 0 }), 'dd/MM')} - ${format(endOfWeek(currentDate, { weekStartsOn: 0 }), 'dd/MM')}`}
             {(visualizacao === 'mes' || visualizacao === 'agenda') && format(currentDate, "MMMM yyyy", { locale: ptBR })}
          </div>
        </div>

        {/* Controles de Visualização e Ações (Grupo Direita) */}
        <div className="flex items-center gap-1.5">
          {/* Botão de Filtros com Dialog */}
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm" className="relative h-8 px-2">
                <FilterIcon className="h-3.5 w-3.5 md:mr-1" />
                <span className="hidden md:inline">Filtros</span>
                {filtrosAtivosCount > 0 && (
                   <Badge variant="secondary" className="absolute -top-1.5 -right-1.5 h-4 w-4 p-0 justify-center rounded-full bg-primary text-primary-foreground text-[9px]">
                    {filtrosAtivosCount}
                  </Badge>
                )}
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[85vh] overflow-hidden p-0">
              <div className="bg-gradient-to-r from-muted/20 to-muted/10 dark:from-muted/30 dark:to-muted/20 p-6 relative shadow-sm">
                <h2 className="text-2xl font-bold flex items-center gap-2">
                  <FilterIcon className="h-6 w-6 text-primary" />
                  Filtros do Calendário
                </h2>
                <p className="text-muted-foreground mt-1">Configure os filtros para visualizar apenas os eventos desejados</p>
              </div>
              <div className="p-6">
                <CalendarFilters
                  filtros={filtros}
                  setFiltros={setFiltros}
                  eventos={eventos}
                />
              </div>
            </DialogContent>
          </Dialog>

          {/* Botão Novo Evento */}
          <Button
            variant="default"
            size="sm"
            onClick={handleAddEvent}
            className="h-8 px-2"
            disabled={isLoading}
          >
            <Plus className="h-3.5 w-3.5 md:mr-1" />
            <span className="hidden md:inline">Novo</span>
          </Button>

          {/* Seleção de Visualização */}
          {(layout === 'default' || layout === 'compact') && (
            <Tabs
              defaultValue={visualizacao}
              className="w-auto"
              onValueChange={(value) => setVisualizacao(value as Visualizacao)}
            >
              <TabsList className="h-8">
                <TabsTrigger value="dia" className="px-2 text-xs h-full" title="Dia"><Grid className="h-3.5 w-3.5" /></TabsTrigger>
                <TabsTrigger value="semana" className="px-2 text-xs h-full" title="Semana"><CalendarDays className="h-3.5 w-3.5" /></TabsTrigger>
                <TabsTrigger value="mes" className="px-2 text-xs h-full" title="Mês"><CalendarIcon className="h-3.5 w-3.5" /></TabsTrigger>
              </TabsList>
            </Tabs>
          )}
        </div>
      </div>

      {/* Área Principal Scrollable */}
      <div className={cn(
          "calendar-content flex-1 overflow-auto",
          "relative", // Para posicionar o spinner
          "scrollbar-thin scrollbar-track-transparent scrollbar-thumb-neutral-200 dark:scrollbar-thumb-neutral-700",
          "hover:scrollbar-thumb-neutral-300 dark:hover:scrollbar-thumb-neutral-600"
        )}
      >
        {isLoading ? (
          <div className="absolute inset-0 flex items-center justify-center bg-background/50 z-10">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : (
          <div className="p-1 md:p-2"> {/* Adiciona um padding interno à área de scroll */}
            {visualizacao === 'dia' && (
              <CalendarDay
                currentDate={currentDate}
                filteredEvents={filteredEvents}
                onEventClick={handleEventClick}
              />
            )}
            {visualizacao === 'semana' && (
              <CalendarWeek
                currentDate={currentDate}
                filteredEvents={filteredEvents}
                onEventClick={handleEventClick}
                onDateClick={handleDateClick}
              />
            )}
            {visualizacao === 'mes' && (
              <CalendarMonth
                currentDate={currentDate}
                filteredEvents={filteredEvents}
                onEventClick={handleEventClick}
                onDateClick={handleDateClick}
              />
            )}
            {visualizacao === 'agenda' && renderAgendaView()}
          </div>
        )}
      </div>

      {/* Modal de Evento */}
      <EventDialog
        evento={isCreatingEvent ? null : selectedEvent}
        isOpen={isDialogOpen || isCreatingEvent}
        onClose={() => {
          setIsDialogOpen(false);
          setIsCreatingEvent(false);
          setSelectedEvent(null);
        }}
        onEventUpdated={handleEventUpdated}
      />
    </div>
  );
};

export default Calendar;