import React, { useMemo } from "react";
import { format, isToday, isTomorrow, isYesterday, isSameWeek, isSameMonth } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Evento } from "../../utils/calendarHelpers";
import { tipoEventoConfig, eventColors } from "../../data/eventConfig";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Clock, MapPin, Calendar as CalendarIcon } from "lucide-react";

interface CalendarAgendaProps {
  currentDate: Date;
  filteredEvents: Evento[];
  onEventClick: (event: Evento) => void;
}

// Função para agrupar eventos por dia
const agruparEventosPorDia = (eventos: Evento[]) => {
  console.log("Eventos recebidos para agrupamento:", eventos);
  
  const eventosAgrupados: Record<string, Evento[]> = {};
  
  eventos.forEach(evento => {
    // Verificar se evento.data é uma string válida
    if (!evento.data || typeof evento.data !== 'string') {
      console.error("Evento com data inválida:", evento);
      return;
    }
    
    // Usar a data completa como chave
    const chave = evento.data;
    
    if (!eventosAgrupados[chave]) {
      eventosAgrupados[chave] = [];
    }
    
    eventosAgrupados[chave].push(evento);
  });
  
  // Ordenar eventos dentro de cada dia por hora
  Object.keys(eventosAgrupados).forEach(dia => {
    eventosAgrupados[dia].sort((a, b) => {
      const horaA = a.hora.split(':').map(Number);
      const horaB = b.hora.split(':').map(Number);
      
      if (horaA[0] !== horaB[0]) {
        return horaA[0] - horaB[0];
      }
      
      return horaA[1] - horaB[1];
    });
  });
  
  console.log("Eventos agrupados:", eventosAgrupados);
  return eventosAgrupados;
};

// Função para formatar a representação do dia
const formatarDia = (data: string) => {
  // Verificar se data é uma string válida
  if (!data || typeof data !== 'string') {
    console.error("Data inválida para formatação:", data);
    return "Data inválida";
  }
  
  try {
    // Converter a data ISO para objeto Date
    const [ano, mes, dia] = data.split('-').map(Number);
    
    if (isNaN(ano) || isNaN(mes) || isNaN(dia)) {
      console.error("Componentes de data inválidos:", { ano, mes, dia });
      return "Data inválida";
    }
    
    const date = new Date(ano, mes - 1, dia);
    
    if (isToday(date)) {
      return "Hoje";
    } else if (isTomorrow(date)) {
      return "Amanhã";
    } else if (isYesterday(date)) {
      return "Ontem";
    } else if (isSameWeek(date, new Date(), { weekStartsOn: 0 })) {
      return format(date, "EEEE", { locale: ptBR });
    } else if (isSameMonth(date, new Date())) {
      return format(date, "dd 'de' MMMM", { locale: ptBR });
    } else {
      return format(date, "dd 'de' MMMM 'de' yyyy", { locale: ptBR });
    }
  } catch (error) {
    console.error("Erro ao formatar data:", error, data);
    return "Data inválida";
  }
};

export const CalendarAgenda: React.FC<CalendarAgendaProps> = ({
  currentDate,
  filteredEvents,
  onEventClick
}) => {
  console.log("CalendarAgenda recebeu eventos:", filteredEvents);
  
  // Ordenar eventos por data e hora
  const eventosOrdenados = useMemo(() => {
    if (!filteredEvents || !Array.isArray(filteredEvents) || filteredEvents.length === 0) {
      console.log("Nenhum evento para ordenar");
      return [];
    }
    
    try {
      return [...filteredEvents].sort((a, b) => {
        // Verificar se as datas são válidas
        if (!a.data || !b.data || typeof a.data !== 'string' || typeof b.data !== 'string') {
          console.error("Datas inválidas durante ordenação:", { a: a.data, b: b.data });
          return 0;
        }
        
        // Comparar por data
        try {
          const [anoA, mesA, diaA] = a.data.split('-').map(Number);
          const [anoB, mesB, diaB] = b.data.split('-').map(Number);
          
          if (isNaN(anoA) || isNaN(mesA) || isNaN(diaA) || 
              isNaN(anoB) || isNaN(mesB) || isNaN(diaB)) {
            console.error("Componentes de data inválidos durante ordenação:", 
              { anoA, mesA, diaA, anoB, mesB, diaB });
            return 0;
          }
          
          const dateA = new Date(anoA, mesA - 1, diaA);
          const dateB = new Date(anoB, mesB - 1, diaB);
          
          const diffData = dateA.getTime() - dateB.getTime();
          
          if (diffData !== 0) {
            return diffData;
          }
          
          // Se a data for a mesma, comparar por hora
          const [horaA, minutoA] = a.hora.split(':').map(Number);
          const [horaB, minutoB] = b.hora.split(':').map(Number);
          
          if (horaA !== horaB) {
            return horaA - horaB;
          }
          
          return minutoA - minutoB;
        } catch (error) {
          console.error("Erro ao ordenar eventos:", error, { a, b });
          return 0;
        }
      });
    } catch (error) {
      console.error("Erro ao processar ordenação de eventos:", error);
      return [];
    }
  }, [filteredEvents]);
  
  const eventosAgrupados = useMemo(() => {
    return agruparEventosPorDia(eventosOrdenados);
  }, [eventosOrdenados]);
  
  // Verificar se há eventos para exibir
  console.log("Número de grupos de eventos:", Object.keys(eventosAgrupados).length);
  
  return (
    <div className="h-full flex flex-col">
      <h2 className="text-2xl font-bold mb-4">Agenda</h2>
      
      {Object.keys(eventosAgrupados).length > 0 ? (
        <div className="flex-1 overflow-hidden">
          <ScrollArea className="h-full">
            <div className="pr-4 space-y-6">
              {Object.entries(eventosAgrupados).map(([data, eventos]) => {
                try {
                  // Converter a data ISO para objeto Date
                  const [ano, mes, dia] = data.split('-').map(Number);
                  
                  if (isNaN(ano) || isNaN(mes) || isNaN(dia)) {
                    console.error("Componentes de data inválidos ao renderizar:", { ano, mes, dia });
                    return null;
                  }
                  
                  const dataObj = new Date(ano, mes - 1, dia);
                  
                  return (
                    <div key={data} className="mb-6">
                      <div className="flex items-center mb-2">
                        <CalendarIcon className="w-5 h-5 mr-2 text-gray-500" />
                        <h3 className="text-lg font-medium">
                          {formatarDia(data)}
                          <span className="text-sm font-normal text-gray-500 ml-2">
                            {format(dataObj, "dd/MM/yyyy", { locale: ptBR })}
                          </span>
                        </h3>
                      </div>
                      
                      <Card>
                        <CardContent className="p-0">
                          {eventos.map((evento, index) => (
                            <React.Fragment key={evento.id}>
                              <div 
                                className="flex p-3 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800/70 transition-colors"
                                onClick={() => onEventClick(evento)}
                              >
                                <div className="w-16 flex-shrink-0 text-center">
                                  <div className="text-sm font-semibold">{evento.hora}</div>
                                </div>
                                
                                <div className="flex-grow">
                                  <div className="flex items-center gap-2">
                                    <h4 className="font-medium">{evento.titulo}</h4>
                                    <Badge 
                                      className={`${eventColors[evento.tipo].replace('bg-', 'bg-opacity-20 ')} text-xs`}
                                    >
                                      {tipoEventoConfig[evento.tipo].icon} {tipoEventoConfig[evento.tipo].nome}
                                    </Badge>
                                  </div>
                                  
                                  {evento.descricao && (
                                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                      {evento.descricao}
                                    </p>
                                  )}
                                  
                                  {evento.local && (
                                    <div className="flex items-center text-xs text-gray-500 mt-2">
                                      <MapPin className="w-3 h-3 mr-1" />
                                      {evento.local}
                                    </div>
                                  )}
                                </div>
                                
                                <div 
                                  className={`w-1 self-stretch rounded-r-sm ${eventColors[evento.tipo]}`}
                                />
                              </div>
                              
                              {index < eventos.length - 1 && (
                                <Separator />
                              )}
                            </React.Fragment>
                          ))}
                        </CardContent>
                      </Card>
                    </div>
                  );
                } catch (error) {
                  console.error("Erro ao renderizar grupo de eventos:", error, data);
                  return null;
                }
              })}
            </div>
          </ScrollArea>
        </div>
      ) : (
        <Card className="flex-1">
          <CardContent className="flex flex-col items-center justify-center h-full py-8">
            <CalendarIcon className="w-12 h-12 text-gray-300 dark:text-gray-600 mb-4" />
            <p className="text-center text-gray-500">Nenhum evento encontrado com os filtros selecionados.</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}; 