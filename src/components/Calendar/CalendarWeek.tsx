import React from "react";
import { format, startOfWeek, addDays, isSameDay, isToday } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Evento } from "../../utils/calendarHelpers";
import { getEventosDoDia } from "../../utils/calendarHelpers";
import { tipoEventoConfig, eventColors } from "../../data/eventConfig";
import { Card, CardContent } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Clock, MapPin } from "lucide-react";

interface CalendarWeekProps {
  currentDate: Date;
  filteredEvents: Evento[];
  onEventClick: (event: Evento) => void;
  onDateClick: (date: Date) => void;
}

export const CalendarWeek: React.FC<CalendarWeekProps> = ({
  currentDate,
  filteredEvents,
  onEventClick,
  onDateClick
}) => {
  // Obtém o primeiro dia da semana (domingo)
  const startDate = startOfWeek(currentDate, { weekStartsOn: 0 });

  // Gera os dias da semana
  const weekDays = Array.from({ length: 7 }, (_, i) => {
    const day = addDays(startDate, i);
    return {
      date: day,
      events: getEventosDoDia(day, filteredEvents),
      isToday: isToday(day)
    };
  });

  // Horários para exibir na grade (das 7h às 22h)
  const hoursOfDay = Array.from({ length: 16 }, (_, i) => i + 7);

  return (
    <div className="space-y-4">
      <h2 className="text-2xl font-bold">
        Semana de {format(startDate, "dd", { locale: ptBR })} a {format(addDays(startDate, 6), "dd 'de' MMMM 'de' yyyy", { locale: ptBR })}
      </h2>

      <Card className="overflow-hidden">
        <CardContent className="p-0">
          {/* Cabeçalho com os dias da semana */}
          <div className="grid grid-cols-8 bg-gray-100 dark:bg-gray-800 border-b">
            <div className="p-2 text-center text-sm font-medium border-r dark:border-gray-700">Horário</div>
            {weekDays.map((day, index) => (
              <div
                key={index}
                className={`p-2 text-center cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors ${
                  day.isToday ? 'bg-muted dark:bg-muted' : ''
                }`}
                onClick={() => onDateClick(day.date)}
              >
                <div className="font-medium">{format(day.date, "EEE", { locale: ptBR })}</div>
                <div className={`text-sm ${day.isToday ? 'text-muted-foreground dark:text-muted-foreground font-semibold' : 'text-gray-500'}`}>
                  {format(day.date, "dd/MM")}
                </div>
              </div>
            ))}
          </div>

          {/* Grade de horários */}
          <ScrollArea className="h-[70vh]">
            <div className="relative">
              {hoursOfDay.map((hour, index) => (
                <div
                  key={hour}
                  className={`grid grid-cols-8 border-b last:border-b-0 dark:border-gray-700 ${
                    index % 2 === 0 ? 'bg-white dark:bg-gray-900' : 'bg-gray-50 dark:bg-gray-800/30'
                  }`}
                >
                  {/* Coluna de horário */}
                  <div className="p-2 text-center text-sm text-gray-500 border-r dark:border-gray-700">
                    {`${String(hour).padStart(2, '0')}:00`}
                  </div>

                  {/* Colunas dos dias */}
                  {weekDays.map((day, dayIndex) => (
                    <div
                      key={dayIndex}
                      className={`relative min-h-[80px] border-r last:border-r-0 dark:border-gray-700 ${
                        day.isToday ? 'bg-muted/30 dark:bg-muted/10' : ''
                      }`}
                      onClick={() => onDateClick(day.date)}
                    >
                      {/* Eventos para este dia e horário */}
                      {day.events
                        .filter(evento => {
                          const [h, m] = evento.hora.split(':').map(Number);
                          return h === hour;
                        })
                        .map(evento => (
                          <TooltipProvider key={evento.id}>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <div
                                  className={`absolute inset-x-1 my-1 p-1 rounded-sm ${eventColors[evento.tipo]} text-xs cursor-pointer truncate`}
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    onEventClick(evento);
                                  }}
                                >
                                  <div className="font-medium truncate">
                                    {evento.titulo}
                                  </div>
                                  <div className="truncate text-xs opacity-90">
                                    {evento.hora}
                                  </div>
                                </div>
                              </TooltipTrigger>
                              <TooltipContent>
                                <div className="p-2 space-y-1 max-w-xs">
                                  <div className="font-bold">{evento.titulo}</div>
                                  <div className="text-sm">{evento.descricao}</div>
                                  <div className="flex items-center text-xs">
                                    <Clock className="w-3 h-3 mr-1" />
                                    {evento.hora}
                                  </div>
                                  {evento.local && (
                                    <div className="flex items-center text-xs">
                                      <MapPin className="w-3 h-3 mr-1" />
                                      {evento.local}
                                    </div>
                                  )}
                                </div>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        ))}
                    </div>
                  ))}
                </div>
              ))}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  );
};