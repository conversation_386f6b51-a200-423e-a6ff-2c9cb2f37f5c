.ProseMirror {
  outline: none !important;
  caret-color: currentColor;
  min-height: 100%;
  padding-bottom: 200px; /* Espaço extra no final para permitir cliques */
}

.ProseMirror p.is-editor-empty:first-child::before {
  color: #adb5bd;
  content: attr(data-placeholder);
  float: left;
  height: 0;
  pointer-events: none;
}

.ProseMirror:focus {
  outline: none !important;
  box-shadow: none !important;
  border-color: transparent !important;
}

/* Remover bordas azuis */
.ProseMirror *::selection {
  background: rgba(59, 130, 246, 0.2);
  border-radius: 0;
}

.dark .ProseMirror *::selection {
  background: rgba(96, 165, 250, 0.3);
}

/* Melhorar a performance da seleção */
.ProseMirror {
  -webkit-user-select: text;
  user-select: text;
  cursor: text;
  transition: none !important;
}

/* Remover bordas azuis em elementos selecionados */
.ProseMirror:focus-visible {
  outline: none !important;
  box-shadow: none !important;
  border-color: transparent !important;
}

/* Melhorar a aparência do cursor */
.ProseMirror {
  caret-color: #3b82f6;
  caret-shape: bar;
}

.dark .ProseMirror {
  caret-color: #60a5fa;
}

/* Melhorar a experiência de clique */
.editor-container {
  position: relative;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

/* Garantir que o editor ocupe todo o espaço disponível */
.ProseMirror {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

/* Corrigir o problema de exibição das listas */
.ProseMirror ul li::marker,
.ProseMirror ol li::marker {
  display: inline !important;
  color: currentColor !important;
}

/* Garantir que os marcadores de lista sejam visíveis */
.ProseMirror ul,
.ProseMirror ol {
  display: block !important;
}

/* Garantir que os parágrafos se expandam para preencher o espaço */
.ProseMirror p {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
  min-height: 1.5em;
}

/* Adicionar uma área de clique no final do documento */
.ProseMirror::after {
  content: '';
  display: block;
  min-height: 200px;
  pointer-events: all;
  cursor: text;
}

/* Corrigir problema de clique */
.editor-container .ProseMirror {
  z-index: 1;
}

.editor-container > div[class*="absolute"] {
  z-index: 0;
}

/* Garantir que as listas sejam exibidas corretamente */
.prose ul > li::before,
.prose ol > li::before {
  display: none !important;
}

.prose ul,
.prose ol {
  list-style-position: outside !important;
  margin-left: 1em !important;
}

/* Corrigir espaçamento das listas */
.prose ul li,
.prose ol li {
  margin-top: 0.25em !important;
  margin-bottom: 0.25em !important;
  padding-left: 0.5em !important;
}

/* Estilos específicos para as classes de lista */
.ProseMirror ul.bullet-list {
  list-style-type: disc !important;
  padding-left: 1.5rem !important;
}

.ProseMirror ul.bullet-list li {
  list-style-type: disc !important;
  display: list-item !important;
}

.ProseMirror ol.ordered-list {
  list-style-type: decimal !important;
  padding-left: 1.5rem !important;
}

.ProseMirror ol.ordered-list li {
  list-style-type: decimal !important;
  display: list-item !important;
}

/* Estilo para o caractere '/' */
.ProseMirror p:last-child:not(:only-child)::after {
  content: attr(data-placeholder);
  float: right;
  color: #adb5bd;
  pointer-events: none;
  height: 0;
}

/* Melhorar a visibilidade do menu de comandos */
.tippy-box[data-animation=fade][data-state=visible] {
  opacity: 1 !important;
}

.tippy-box[data-animation=fade][data-state=hidden] {
  opacity: 0 !important;
}

/* Melhorar a aparência dos títulos */
.ProseMirror h1 {
  font-size: 1.75rem;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
}

.ProseMirror h2 {
  font-size: 1.5rem;
  margin-top: 1.25rem;
  margin-bottom: 0.5rem;
}

.ProseMirror h3 {
  font-size: 1.25rem;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
}

/* Melhorar a aparência das listas */
.ProseMirror ul {
  padding-left: 1.5rem;
  list-style-type: disc !important;
}

.ProseMirror ol {
  padding-left: 1.5rem;
  list-style-type: decimal !important;
}

.ProseMirror li {
  margin-bottom: 0.25rem;
  display: list-item !important;
}

.ProseMirror ul li {
  list-style-type: disc !important;
}

.ProseMirror ol li {
  list-style-type: decimal !important;
}

/* Corrigir listas aninhadas */
.ProseMirror ul ul {
  list-style-type: circle !important;
}

.ProseMirror ul ul ul {
  list-style-type: square !important;
}

.ProseMirror ol ol {
  list-style-type: lower-alpha !important;
}

.ProseMirror ol ol ol {
  list-style-type: lower-roman !important;
}

/* Melhorar a aparência das listas de tarefas */
.ProseMirror ul[data-type="taskList"] {
  list-style: none !important;
  padding: 0 !important;
}

.ProseMirror ul[data-type="taskList"] li {
  display: flex !important;
  align-items: flex-start !important;
  margin-bottom: 0.5rem !important;
  list-style-type: none !important;
}

.ProseMirror ul[data-type="taskList"] li > label {
  margin-right: 0.5rem !important;
  user-select: none !important;
}

.ProseMirror ul[data-type="taskList"] li > div {
  flex: 1 !important;
}

/* Corrigir a aparência das listas no tema escuro */
.dark .ProseMirror ul,
.dark .ProseMirror ol {
  color: rgba(255, 255, 255, 0.9) !important;
}

.dark .prose ul > li,
.dark .prose ol > li {
  color: rgba(255, 255, 255, 0.9) !important;
}

/* Melhorar a aparência das citações */
.ProseMirror blockquote {
  border-left: 3px solid #e9ecef;
  padding-left: 1rem;
  font-style: italic;
  color: #6c757d;
}

/* Melhorar a aparência dos blocos de código */
.ProseMirror pre {
  background-color: #f8f9fa;
  padding: 0.75rem;
  border-radius: 0.25rem;
  font-family: monospace;
  overflow-x: auto;
}

.dark .ProseMirror pre {
  background-color: #2d3748;
}

/* Melhorar a aparência das tabelas */
.ProseMirror table {
  border-collapse: collapse;
  width: 100%;
  margin: 1rem 0;
  overflow: hidden;
}

.ProseMirror table td,
.ProseMirror table th {
  border: 1px solid #e9ecef;
  padding: 0.5rem;
}

.dark .ProseMirror table td,
.dark .ProseMirror table th {
  border-color: #4a5568;
}

.ProseMirror table th {
  background-color: #f8f9fa;
  font-weight: bold;
}

.dark .ProseMirror table th {
  background-color: #2d3748;
}

/* Melhorar a aparência dos links */
.ProseMirror a {
  color: #3182ce;
  text-decoration: underline;
}

.dark .ProseMirror a {
  color: #63b3ed;
}

/* Melhorar a aparência do texto destacado */
.ProseMirror mark {
  background-color: #fef9c3;
  padding: 0 0.25rem;
  border-radius: 0.125rem;
}

.dark .ProseMirror mark {
  background-color: #92400e;
  color: #fef9c3;
}
