import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { criarNovoDocumento, Documento } from '@/services/documentosService';
import { toast } from 'sonner';
import { Save, FileText } from 'lucide-react';
import { RichTextEditor } from '@/components/RichTextEditor';

interface DocumentEditorProps {
  documento: Documento | null;
  clientes: any[];
  precatorios: any[];
  onDocumentoAdicionado: (documento: Documento) => void;
  onCancel: () => void;
}

// Schema de validação
const formSchema = z.object({
  nome: z.string().min(3, { message: 'O nome deve ter pelo menos 3 caracteres' }),
  conteudo: z.string().min(1, { message: 'O conteúdo não pode estar vazio' }),
  tipo: z.string().min(1, { message: 'Selecione o tipo de documento' }),
  categoria: z.enum(['cliente', 'precatorio'], {
    required_error: 'Selecione a categoria'
  }),
  clienteId: z.string().optional(),
  precatorioId: z.string().optional(),
}).refine(data => {
  if (data.categoria === 'cliente') {
    return !!data.clienteId;
  }
  if (data.categoria === 'precatorio') {
    return !!data.precatorioId;
  }
  return true;
}, {
  message: 'Selecione um cliente ou precatório',
  path: ['clienteId'],
});

export function DocumentEditor({
  documento,
  clientes,
  precatorios,
  onDocumentoAdicionado,
  onCancel
}: DocumentEditorProps) {
  const [isSaving, setIsSaving] = useState(false);

  // Inicializar formulário
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      nome: documento?.nome || '',
      conteudo: '',
      tipo: documento?.tipo || '',
      categoria: 'cliente',
      clienteId: documento?.cliente_id || '',
      precatorioId: documento?.precatorio_id || '',
    },
  });

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      setIsSaving(true);

      let novoDocumento: Documento;

      if (values.categoria === 'cliente' && values.clienteId) {
        novoDocumento = await criarNovoDocumento(
          values.nome,
          values.conteudo,
          values.tipo,
          values.clienteId
        );
      } else if (values.categoria === 'precatorio' && values.precatorioId) {
        novoDocumento = await criarNovoDocumento(
          values.nome,
          values.conteudo,
          values.tipo,
          undefined,
          values.precatorioId
        );
      } else {
        throw new Error('Categoria ou ID inválido');
      }

      // Notificar componente pai
      onDocumentoAdicionado(novoDocumento);

      // Resetar estado
      form.reset();
    } catch (error) {
      console.error('Erro ao criar documento:', error);
      toast.error('Erro ao criar documento');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="flex flex-col h-full">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="flex flex-col h-full">
          <div className="grid grid-cols-2 gap-4 mb-4">
            <FormField
              control={form.control}
              name="nome"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nome do Documento</FormLabel>
                  <FormControl>
                    <Input placeholder="Ex: Contrato de Prestação de Serviços" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="tipo"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tipo de Documento</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione o tipo de documento" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="contrato">Contrato</SelectItem>
                      <SelectItem value="peticao">Petição</SelectItem>
                      <SelectItem value="procuracao">Procuração</SelectItem>
                      <SelectItem value="declaracao">Declaração</SelectItem>
                      <SelectItem value="termo">Termo</SelectItem>
                      <SelectItem value="outros">Outros</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="grid grid-cols-2 gap-4 mb-4">
            <FormField
              control={form.control}
              name="categoria"
              render={({ field }) => (
                <FormItem className="space-y-1">
                  <FormLabel>Associar a</FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      className="flex space-x-4"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="cliente" id="cliente-radio" />
                        <Label htmlFor="cliente-radio">Cliente</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="precatorio" id="precatorio-radio" />
                        <Label htmlFor="precatorio-radio">Precatório</Label>
                      </div>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {form.watch('categoria') === 'cliente' ? (
              <FormField
                control={form.control}
                name="clienteId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Cliente</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione um cliente" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {clientes.map((cliente) => (
                          <SelectItem key={cliente.id} value={cliente.id}>
                            {cliente.nome}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            ) : (
              <FormField
                control={form.control}
                name="precatorioId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Precatório</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione um precatório" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {precatorios.map((precatorio) => (
                          <SelectItem key={precatorio.id} value={precatorio.id}>
                            {precatorio.numero_precatorio || precatorio.id}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
          </div>

          <FormField
            control={form.control}
            name="conteudo"
            render={({ field }) => (
              <FormItem className="flex-1 flex flex-col">
                <FormLabel>Conteúdo do Documento</FormLabel>
                <FormControl>
                  <RichTextEditor
                    initialContent={field.value}
                    onChange={field.onChange}
                    placeholder="Comece a escrever seu documento aqui..."
                    minHeight="400px"
                    className="border-input"
                  />
                </FormControl>
                <FormDescription>
                  Utilize as ferramentas de formatação para criar seu documento. Você pode adicionar títulos, listas, tabelas, imagens e muito mais.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex justify-end gap-2 pt-4 mt-auto">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isSaving}
            >
              Cancelar
            </Button>
            <Button
              type="submit"
              disabled={isSaving}
              className="gap-2"
            >
              {isSaving ? (
                <>
                  <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full" />
                  Salvando...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4" />
                  Salvar Documento
                </>
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
