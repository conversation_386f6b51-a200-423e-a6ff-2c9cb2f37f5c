import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Progress } from "@/components/ui/progress";
import { Loader2, Plus, CheckCircle2, Clock, AlertCircle, Calendar, User } from "lucide-react";
import { TaskManager } from "@/features/tasks/components/TaskManager";
import { fetchTasksByClient, fetchTasksByPrecatorio, fetchTasksByAssignee, Task } from '@/services/tasksService';
import { CHART_COLORS } from '@/constants/chartColors';

interface AssociatedTasksProps {
  entityId: string;
  entityType: 'cliente' | 'precatorio' | 'usuario';
  showHeader?: boolean;
  className?: string;
}

export function AssociatedTasks({
  entityId,
  entityType,
  showHeader = true,
  className = ""
}: AssociatedTasksProps) {
  const [activeTab, setActiveTab] = useState("lista");
  const [loading, setLoading] = useState(true);
  const [taskCount, setTaskCount] = useState({
    total: 0,
    pendentes: 0,
    emAndamento: 0,
    concluidas: 0,
    atrasadas: 0
  });

  // Carregar contagem de tarefas
  useEffect(() => {
    const loadTaskCount = async () => {
      try {
        setLoading(true);

        let tasks: Task[] = [];

        // Buscar tarefas com base no tipo de entidade
        if (entityType === 'cliente') {
          tasks = await fetchTasksByClient(entityId);
        } else if (entityType === 'precatorio') {
          tasks = await fetchTasksByPrecatorio(entityId);
        } else if (entityType === 'usuario') {
          tasks = await fetchTasksByAssignee(entityId);
        }

        // Calcular contagens
        const pendentes = tasks.filter(t => t.status === 'pendente').length;
        const emAndamento = tasks.filter(t => t.status === 'em_andamento').length;
        const concluidas = tasks.filter(t => t.status === 'concluida').length;

        // Verificar tarefas atrasadas (comparando due_date com a data atual)
        const hoje = new Date();
        const atrasadas = tasks.filter(t => {
          if (!t.due_date) return false;
          const prazo = new Date(t.due_date);
          return prazo < hoje && t.status !== 'concluida';
        }).length;

        setTaskCount({
          total: tasks.length,
          pendentes,
          emAndamento,
          concluidas,
          atrasadas
        });
      } catch (error) {
        console.error('Erro ao carregar contagem de tarefas:', error);
      } finally {
        setLoading(false);
      }
    };

    loadTaskCount();
  }, [entityId, entityType]);

  // Função para obter a cor do status
  const getStatusColor = (status: string) => {
    const statusMap: Record<string, string> = {
      'pendente': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300',
      'em_andamento': 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300',
      'concluida': 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',
      'atrasada': 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300',
    };

    return statusMap[status] || 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
  };

  return (
    <div className={className}>
      {showHeader && (
        <div className="mb-6">
          <h2 className="text-2xl font-bold mb-2">Tarefas</h2>
          <p className="text-muted-foreground">
            Gerencie as tarefas associadas a este {
              entityType === 'cliente' ? 'cliente' :
              entityType === 'precatorio' ? 'precatório' : 'usuário'
            }
          </p>
        </div>
      )}

      {/* Resumo de tarefas */}
      {loading ? (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
          <Card>
            <CardContent className="pt-6">
              <div className="flex flex-col items-center justify-center text-center">
                <div className="text-3xl font-bold">{taskCount.total}</div>
                <p className="text-sm text-muted-foreground mt-1">Total de Tarefas</p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex flex-col items-center justify-center text-center">
                <div className="text-3xl font-bold text-yellow-500">{taskCount.pendentes}</div>
                <p className="text-sm text-muted-foreground mt-1">Pendentes</p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex flex-col items-center justify-center text-center">
                <div className="text-3xl font-bold text-blue-500">{taskCount.emAndamento}</div>
                <p className="text-sm text-muted-foreground mt-1">Em Andamento</p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex flex-col items-center justify-center text-center">
                <div className="text-3xl font-bold text-green-500">{taskCount.concluidas}</div>
                <p className="text-sm text-muted-foreground mt-1">Concluídas</p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex flex-col items-center justify-center text-center">
                <div className="text-3xl font-bold text-red-500">{taskCount.atrasadas}</div>
                <p className="text-sm text-muted-foreground mt-1">Atrasadas</p>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Gerenciador de tarefas */}
      <Card className="border-neutral-200 dark:border-neutral-700 bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">Gerenciamento de Tarefas</CardTitle>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-auto">
              <TabsList className="bg-muted/50">
                <TabsTrigger value="lista">Lista</TabsTrigger>
                <TabsTrigger value="quadro">Quadro</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
          <CardDescription>
            Visualize e gerencie todas as tarefas associadas
          </CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          <div className="h-[500px] overflow-hidden">
            {entityType === 'cliente' && (
              <TaskManager
                clienteId={entityId}
                defaultView={activeTab === 'lista' ? 'lista' : 'quadro'}
                showHeader={false}
                showFilters={true}
              />
            )}

            {entityType === 'precatorio' && (
              <TaskManager
                processoId={entityId}
                defaultView={activeTab === 'lista' ? 'lista' : 'quadro'}
                showHeader={false}
                showFilters={true}
              />
            )}

            {entityType === 'usuario' && (
              <TaskManager
                usuarioId={entityId}
                defaultView={activeTab === 'lista' ? 'lista' : 'quadro'}
                showHeader={false}
                showFilters={true}
              />
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
