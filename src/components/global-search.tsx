import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  CommandDialog, 
  CommandEmpty, 
  CommandGroup, 
  CommandInput, 
  CommandItem, 
  CommandList, 
  CommandShortcut 
} from '@/components/ui/command';
import { 
  Search, 
  User, 
  FileText, 
  CheckSquare, 
  Loader2,
  FileSpreadsheet
} from 'lucide-react';
import { searchGlobal, SearchResult } from '@/services/searchService';
import { useDebounce } from '@/hooks/useDebounce';

export function GlobalSearch() {
  const [open, setOpen] = useState(false);
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const debouncedQuery = useDebounce(query, 300);

  useEffect(() => {
    const down = (e: KeyboardEvent) => {
      // Abrir com Cmd+K ou Ctrl+K
      if ((e.key === 'k' && (e.metaKey || e.ctrlKey))) {
        e.preventDefault();
        setOpen((open) => !open);
      }
    };

    document.addEventListener('keydown', down);
    return () => document.removeEventListener('keydown', down);
  }, []);

  useEffect(() => {
    const performSearch = async () => {
      if (debouncedQuery.length < 2) {
        setResults([]);
        return;
      }

      setLoading(true);
      try {
        const searchResults = await searchGlobal(debouncedQuery);
        setResults(searchResults);
      } catch (error) {
        console.error('Erro ao realizar pesquisa:', error);
      } finally {
        setLoading(false);
      }
    };

    performSearch();
  }, [debouncedQuery]);

  const handleSelect = (result: SearchResult) => {
    setOpen(false);
    navigate(result.url);
  };

  const getIcon = (type: string) => {
    switch (type) {
      case 'cliente':
        return <User className="h-4 w-4 mr-2" />;
      case 'precatorio':
        return <FileSpreadsheet className="h-4 w-4 mr-2" />;
      case 'rpv':
        return <FileSpreadsheet className="h-4 w-4 mr-2" />;
      case 'tarefa':
        return <CheckSquare className="h-4 w-4 mr-2" />;
      default:
        return <FileText className="h-4 w-4 mr-2" />;
    }
  };

  return (
    <>
      <button
        onClick={() => setOpen(true)}
        className="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2"
      >
        <Search className="mr-2 h-4 w-4" />
        <span className="hidden md:inline-flex">Pesquisar...</span>
        <kbd className="pointer-events-none ml-auto hidden h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium opacity-100 md:flex">
          <span className="text-xs">⌘</span>K
        </kbd>
      </button>

      <CommandDialog open={open} onOpenChange={setOpen}>
        <CommandInput
          placeholder="Pesquisar clientes, precatórios, RPVs, tarefas..."
          value={query}
          onValueChange={setQuery}
        />
        <CommandList>
          {loading && (
            <div className="py-6 text-center text-sm">
              <Loader2 className="h-6 w-6 animate-spin mx-auto mb-2" />
              <p>Buscando resultados...</p>
            </div>
          )}
          
          {!loading && query.length > 0 && results.length === 0 && (
            <CommandEmpty>Nenhum resultado encontrado.</CommandEmpty>
          )}

          {!loading && results.length > 0 && (
            <>
              {/* Clientes */}
              {results.filter(r => r.type === 'cliente').length > 0 && (
                <CommandGroup heading="Clientes">
                  {results
                    .filter(r => r.type === 'cliente')
                    .map((result) => (
                      <CommandItem
                        key={`${result.type}-${result.id}`}
                        onSelect={() => handleSelect(result)}
                      >
                        {getIcon(result.type)}
                        <div className="flex flex-col">
                          <span>{result.title}</span>
                          {result.subtitle && (
                            <span className="text-xs text-muted-foreground">{result.subtitle}</span>
                          )}
                        </div>
                      </CommandItem>
                    ))}
                </CommandGroup>
              )}

              {/* Precatórios */}
              {results.filter(r => r.type === 'precatorio').length > 0 && (
                <CommandGroup heading="Precatórios">
                  {results
                    .filter(r => r.type === 'precatorio')
                    .map((result) => (
                      <CommandItem
                        key={`${result.type}-${result.id}`}
                        onSelect={() => handleSelect(result)}
                      >
                        {getIcon(result.type)}
                        <div className="flex flex-col">
                          <span>{result.title}</span>
                          {result.subtitle && (
                            <span className="text-xs text-muted-foreground">{result.subtitle}</span>
                          )}
                        </div>
                      </CommandItem>
                    ))}
                </CommandGroup>
              )}

              {/* RPVs */}
              {results.filter(r => r.type === 'rpv').length > 0 && (
                <CommandGroup heading="RPVs">
                  {results
                    .filter(r => r.type === 'rpv')
                    .map((result) => (
                      <CommandItem
                        key={`${result.type}-${result.id}`}
                        onSelect={() => handleSelect(result)}
                      >
                        {getIcon(result.type)}
                        <div className="flex flex-col">
                          <span>{result.title}</span>
                          {result.subtitle && (
                            <span className="text-xs text-muted-foreground">{result.subtitle}</span>
                          )}
                        </div>
                      </CommandItem>
                    ))}
                </CommandGroup>
              )}

              {/* Tarefas */}
              {results.filter(r => r.type === 'tarefa').length > 0 && (
                <CommandGroup heading="Tarefas">
                  {results
                    .filter(r => r.type === 'tarefa')
                    .map((result) => (
                      <CommandItem
                        key={`${result.type}-${result.id}`}
                        onSelect={() => handleSelect(result)}
                      >
                        {getIcon(result.type)}
                        <div className="flex flex-col">
                          <span>{result.title}</span>
                          {result.subtitle && (
                            <span className="text-xs text-muted-foreground">{result.subtitle}</span>
                          )}
                        </div>
                      </CommandItem>
                    ))}
                </CommandGroup>
              )}
            </>
          )}

          <CommandGroup heading="Atalhos">
            <CommandItem onSelect={() => navigate('/dashboard')}>
              <span>Ir para Dashboard</span>
              <CommandShortcut>⌘D</CommandShortcut>
            </CommandItem>
            <CommandItem onSelect={() => navigate('/customers')}>
              <span>Ir para Clientes</span>
              <CommandShortcut>⌘C</CommandShortcut>
            </CommandItem>
            <CommandItem onSelect={() => navigate('/precatorios')}>
              <span>Ir para Precatórios</span>
              <CommandShortcut>⌘P</CommandShortcut>
            </CommandItem>
            <CommandItem onSelect={() => navigate('/tarefas')}>
              <span>Ir para Tarefas</span>
              <CommandShortcut>⌘T</CommandShortcut>
            </CommandItem>
          </CommandGroup>
        </CommandList>
      </CommandDialog>
    </>
  );
}
