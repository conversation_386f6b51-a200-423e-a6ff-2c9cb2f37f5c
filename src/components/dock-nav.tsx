import { Users, Settings, LayoutDashboard, UserSquare2, User, CheckSquare, Calendar, LogOut, KanbanSquare, FileSpreadsheet, FileText } from 'lucide-react';
import { Dock, DockIcon, DockItem, DockLabel } from '@/components/ui/dock';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

// Tipos para os dados do usuário
type UserRole =
  | "admin"  // Administrador (ADC)
  | "gerente_geral"  // Gerente Geral de Precatórios e RPVs
  | "gerente_precatorio"  // Gerente de Precatório
  | "gerente_rpv"  // Gerente de RPV
  | "captador"  // Captador
  | "operacional_precatorio"  // Funcionário Operacional de Precatório
  | "operacional_rpv"  // Funcionário Operacional de RPV
  | "operacional_completo";  // Funcionário Operacional Completo (Precatório + RPV)

interface UserProfile {
  id: string;
  email: string;
  nome?: string;
  role: UserRole;
  foto_url?: string;
  status?: string;
}

type PermissionType =
  // Permissões para clientes
  | 'criar_cliente'
  | 'editar_cliente'
  | 'excluir_cliente'
  | 'visualizar_cliente'

  // Permissões para Precatórios
  | 'criar_precatorio'
  | 'editar_precatorio'
  | 'excluir_precatorio'
  | 'visualizar_precatorio'

  // Permissões para RPVs
  | 'criar_rpv'
  | 'editar_rpv'
  | 'excluir_rpv'
  | 'visualizar_rpv'

  // Permissões para tarefas
  | 'criar_tarefa'
  | 'editar_tarefa'
  | 'excluir_tarefa'
  | 'visualizar_tarefa'
  | 'visualizar_todas_tarefas'

  // Permissões para documentos
  | 'criar_documento'
  | 'editar_documento'
  | 'excluir_documento'
  | 'visualizar_documento'

  // Permissões para automações
  | 'criar_automacao'
  | 'editar_automacao'
  | 'excluir_automacao'
  | 'visualizar_automacao'

  // Permissões para relatórios
  | 'visualizar_relatorio_precatorio'
  | 'visualizar_relatorio_rpv'
  | 'visualizar_relatorio_captacao'
  | 'visualizar_relatorio_completo'

  // Permissões para usuários e configurações
  | 'gerenciar_usuarios'
  | 'configurar_sistema';

// Mapeamento de permissões por papel
const PERMISSIONS_BY_ROLE: Record<UserRole, PermissionType[]> = {
  admin: [
    'criar_cliente', 'editar_cliente', 'excluir_cliente', 'visualizar_cliente',
    'criar_precatorio', 'editar_precatorio', 'excluir_precatorio', 'visualizar_precatorio',
    'criar_rpv', 'editar_rpv', 'excluir_rpv', 'visualizar_rpv',
    'criar_tarefa', 'editar_tarefa', 'excluir_tarefa', 'visualizar_tarefa', 'visualizar_todas_tarefas',
    'criar_documento', 'editar_documento', 'excluir_documento', 'visualizar_documento',
    'criar_automacao', 'editar_automacao', 'excluir_automacao', 'visualizar_automacao',
    'visualizar_relatorio_precatorio', 'visualizar_relatorio_rpv', 'visualizar_relatorio_captacao', 'visualizar_relatorio_completo',
    'gerenciar_usuarios', 'configurar_sistema'
  ],
  gerente_geral: [
    'criar_cliente', 'editar_cliente', 'excluir_cliente', 'visualizar_cliente',
    'criar_precatorio', 'editar_precatorio', 'excluir_precatorio', 'visualizar_precatorio',
    'criar_rpv', 'editar_rpv', 'excluir_rpv', 'visualizar_rpv',
    'criar_tarefa', 'editar_tarefa', 'excluir_tarefa', 'visualizar_tarefa', 'visualizar_todas_tarefas',
    'criar_documento', 'editar_documento', 'excluir_documento', 'visualizar_documento',
    'visualizar_relatorio_precatorio', 'visualizar_relatorio_rpv'
  ],
  gerente_precatorio: [
    'criar_cliente', 'editar_cliente', 'excluir_cliente', 'visualizar_cliente',
    'criar_precatorio', 'editar_precatorio', 'excluir_precatorio', 'visualizar_precatorio',
    'criar_tarefa', 'editar_tarefa', 'excluir_tarefa', 'visualizar_tarefa',
    'criar_documento', 'editar_documento', 'excluir_documento', 'visualizar_documento',
    'visualizar_relatorio_precatorio'
  ],
  gerente_rpv: [
    'criar_cliente', 'editar_cliente', 'excluir_cliente', 'visualizar_cliente',
    'criar_rpv', 'editar_rpv', 'excluir_rpv', 'visualizar_rpv',
    'criar_tarefa', 'editar_tarefa', 'excluir_tarefa', 'visualizar_tarefa',
    'criar_documento', 'editar_documento', 'excluir_documento', 'visualizar_documento',
    'visualizar_relatorio_rpv'
  ],
  captador: [
    'criar_cliente', 'editar_cliente', 'excluir_cliente', 'visualizar_cliente',
    'criar_precatorio', 'editar_precatorio', 'visualizar_precatorio',
    'criar_rpv', 'editar_rpv', 'visualizar_rpv',
    'visualizar_tarefa',
    'visualizar_documento',
    'visualizar_relatorio_captacao'
  ],
  operacional_precatorio: [
    'visualizar_cliente',
    'visualizar_precatorio', 'editar_precatorio',
    'criar_tarefa', 'editar_tarefa', 'visualizar_tarefa',
    'visualizar_documento',
    'visualizar_relatorio_precatorio'
  ],
  operacional_rpv: [
    'visualizar_cliente',
    'visualizar_rpv', 'editar_rpv',
    'criar_tarefa', 'editar_tarefa', 'visualizar_tarefa',
    'visualizar_documento',
    'visualizar_relatorio_rpv'
  ],
  operacional_completo: [
    'visualizar_cliente',
    'visualizar_precatorio', 'editar_precatorio',
    'visualizar_rpv', 'editar_rpv',
    'criar_tarefa', 'editar_tarefa', 'visualizar_tarefa',
    'visualizar_documento',
    'visualizar_relatorio_precatorio', 'visualizar_relatorio_rpv'
  ]
};

export function DockNav() {
  const location = useLocation();
  const navigate = useNavigate();
  const [user, setUser] = useState<UserProfile | null>(null);
  const [permissions, setPermissions] = useState<PermissionType[]>([]);

  // Carregar perfil do usuário do localStorage
  useEffect(() => {
    try {
      const userProfileStr = localStorage.getItem('userProfile');
      if (userProfileStr) {
        const userProfile = JSON.parse(userProfileStr);
        setUser(userProfile);

        // Carregar permissões com base no papel
        if (userProfile.role) {
          setPermissions(PERMISSIONS_BY_ROLE[userProfile.role] || []);
        }
      }
    } catch (error) {
      console.error("Erro ao carregar perfil do usuário:", error);
    }
  }, []);

  // Função para verificar permissão
  const hasPermission = (permission: PermissionType | null): boolean => {
    if (permission === null) return true;
    return permissions.includes(permission);
  };

  // Função de logout
  const handleLogout = () => {
    // Limpar dados de autenticação
    localStorage.removeItem('userProfile');
    localStorage.removeItem('supabase.auth.token');
    localStorage.removeItem('sb-ubwzukpsqcrwzfbppoux-auth-token');

    toast.success("Logout realizado com sucesso");
    navigate("/login");
  };

  // Todos os itens de navegação
  const menuItems = [
    {
      title: 'Dashboard',
      icon: <LayoutDashboard className='h-full w-full' />,
      href: '/dashboard',
      permission: null // Todos podem acessar
    },
    {
      title: 'Tarefas',
      icon: <CheckSquare className='h-full w-full' />,
      href: user?.role === 'operacional_precatorio' ? '/tarefas?area=PRECATORIO' :
            user?.role === 'operacional_rpv' ? '/tarefas?area=RPV' : '/tarefas',
      permission: 'visualizar_tarefa' as PermissionType
    },
    {
      title: 'Calendário',
      icon: <Calendar className='h-full w-full' />,
      href: '/calendar',
      permission: null // Todos podem acessar
    },
    {
      title: 'Clientes',
      icon: <UserSquare2 className='h-full w-full' />,
      href: '/customers',
      permission: 'visualizar_cliente' as PermissionType
    },
    {
      title: 'Documentos',
      icon: <FileText className='h-full w-full' />,
      href: '/documents',
      permission: 'visualizar_documento' as PermissionType
    },
    // Item de menu adaptado conforme o papel do usuário
    ...(user?.role === 'gerente_precatorio' || user?.role === 'operacional_precatorio' ? [
      {
        title: 'Kanban Precatórios',
        icon: <KanbanSquare className='h-full w-full' />,
        href: '/precatorios?tipo=PRECATORIO',
        permission: 'visualizar_precatorio' as PermissionType
      },
      {
        title: 'Precatórios',
        icon: <FileSpreadsheet className='h-full w-full' />,
        href: '/precatorios-table?tipo=PRECATORIO',
        permission: 'visualizar_precatorio' as PermissionType
      }
    ] :
    user?.role === 'gerente_rpv' || user?.role === 'operacional_rpv' ? [
      {
        title: 'Kanban RPVs',
        icon: <KanbanSquare className='h-full w-full' />,
        href: '/precatorios?tipo=RPV',
        permission: 'visualizar_rpv' as PermissionType
      },
      {
        title: 'RPVs',
        icon: <FileSpreadsheet className='h-full w-full' />,
        href: '/precatorios-table?tipo=RPV',
        permission: 'visualizar_rpv' as PermissionType
      }
    ] : [
      {
        title: 'Kanban Precatórios/RPVs',
        icon: <KanbanSquare className='h-full w-full' />,
        href: '/precatorios',
        permission: hasPermission('visualizar_precatorio') || hasPermission('visualizar_rpv') ? null : 'visualizar_precatorio' as PermissionType
      },
      {
        title: 'Precatórios/RPVs',
        icon: <FileSpreadsheet className='h-full w-full' />,
        href: '/precatorios-table',
        permission: hasPermission('visualizar_precatorio') || hasPermission('visualizar_rpv') ? null : 'visualizar_precatorio' as PermissionType
      }
    ]),
    // Restante dos itens comuns para todos os papéis autorizados
    {
      title: 'Usuários',
      icon: <Users className='h-full w-full' />,
      href: '/user-management',
      permission: 'gerenciar_usuarios' as PermissionType
    },
    {
      title: 'Configurações',
      icon: <Settings className='h-full w-full' />,
      href: '/settings',
      permission: 'configurar_sistema' as PermissionType
    },
    {
      title: 'Meu Perfil',
      icon: <User className='h-full w-full' />,
      href: `/usuario/${user?.id}`,
      permission: null // Todos podem acessar
    },
    {
      title: 'Sair',
      icon: <LogOut className='h-full w-full' />,
      onClick: handleLogout,
      permission: null // Todos podem acessar
    }
  ].flat(); // Flatten para lidar com o spread operator que retorna array

  // Filtrar itens baseado nas permissões
  const filteredItems = menuItems.filter(item =>
    item.permission === null || hasPermission(item.permission)
  );

  return (
    <div className='fixed bottom-8 left-1/2 max-w-full -translate-x-1/2 z-50'>
      <Dock className='items-end pb-3 shadow-lg'>
        {filteredItems.map((item, idx) => (
          item.onClick ? (
            <div key={idx} onClick={item.onClick}>
              <DockItem
                className="aspect-square rounded-full transition-colors bg-gray-200 dark:bg-neutral-800 hover:bg-gray-300 dark:hover:bg-neutral-700"
              >
                <DockLabel>{item.title}</DockLabel>
                <DockIcon>
                  <div className="text-neutral-600 dark:text-neutral-300">
                    {item.icon}
                  </div>
                </DockIcon>
              </DockItem>
            </div>
          ) : (
            <Link to={item.href} key={idx}>
              <DockItem
                className={
                  `aspect-square rounded-full transition-colors
                  ${location.pathname === item.href
                    ? 'bg-primary text-white dark:bg-white dark:text-black'
                    : 'bg-gray-200 dark:bg-neutral-800 hover:bg-gray-300 dark:hover:bg-neutral-700'
                  }`
                }
              >
                <DockLabel>{item.title}</DockLabel>
                <DockIcon>
                  <div className={
                    location.pathname === item.href
                      ? 'text-white dark:text-black font-bold'
                      : 'text-neutral-600 dark:text-neutral-300'
                  }>
                    {item.icon}
                  </div>
                </DockIcon>
              </DockItem>
            </Link>
          )
        ))}
      </Dock>
    </div>
  );
}