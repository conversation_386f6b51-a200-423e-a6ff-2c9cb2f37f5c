import React from 'react';
import { usePermissions } from '@/hooks/usePermissions';

interface ConditionalActionProps {
  action: string;
  resource: string;
  resourceId?: string;
  fallback?: React.ReactNode;
  children: React.ReactNode;
}

export function ConditionalAction({
  action,
  resource,
  resourceId,
  fallback = null,
  children
}: ConditionalActionProps) {
  const { canSee } = usePermissions();
  
  // Verificar se o usuário tem permissão para ver a ação
  const hasPermission = canSee(action, resource, resourceId);
  
  if (!hasPermission) {
    return <>{fallback}</>;
  }
  
  return <>{children}</>;
}
