import React, { useState, useEffect } from 'react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { Check, ChevronDown, Search } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import * as LucideIcons from 'lucide-react';

interface IconPickerProps {
  value: string;
  onChange: (icon: string) => void;
}

// Lista de ícones comuns para mostrar no seletor
const COMMON_ICONS = [
  'File', 'FileText', 'Folder', 'FolderOpen', 'User', 'Users', 'UserPlus',
  'Calendar', 'Clock', 'Timer', 'Bell', 'Mail', 'MessageSquare', 'Phone',
  'Home', 'Settings', 'LayoutGrid', 'List', 'Table', 'Tag', 'Tags',
  'Star', 'Heart', 'ThumbsUp', 'Check', 'X', 'AlertTriangle', 'Info',
  'Edit', 'Trash2', 'Copy', 'Download', 'Upload', 'Link', 'ExternalLink',
  'Search', 'Filter', 'Plus', 'Minus', 'MoreHorizontal', 'MoreVertical',
  'ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'ChevronUp', 'ChevronDown',
  'ChevronLeft', 'ChevronRight', 'Menu', 'BarChart', 'PieChart', 'LineChart',
  'DollarSign', 'Percent', 'CreditCard', 'ShoppingCart', 'Package', 'Truck',
  'Map', 'MapPin', 'Globe', 'Layers', 'Image', 'Camera', 'Video', 'Music',
  'Bookmark', 'Award', 'Flag', 'Shield', 'Lock', 'Unlock', 'Key', 'Cog',
  'Tool', 'Wrench', 'Sliders', 'Share', 'Wifi', 'Bluetooth', 'Battery',
  'Monitor', 'Smartphone', 'Tablet', 'Laptop', 'Printer', 'Tv', 'Headphones',
  'Speaker', 'Mic', 'Sun', 'Moon', 'Cloud', 'CloudRain', 'Thermometer',
  'Umbrella', 'Wind', 'Compass', 'Map', 'Navigation', 'Car', 'Plane', 'Train',
  'Zap', 'Activity', 'AlertCircle', 'Archive', 'AtSign', 'Briefcase',
  'Calendar', 'Coffee', 'Command', 'Cpu', 'Database', 'Eye', 'EyeOff',
  'Facebook', 'Github', 'Instagram', 'Linkedin', 'Twitter', 'Youtube',
];

export function IconPicker({ value, onChange }: IconPickerProps) {
  const [selectedIcon, setSelectedIcon] = useState(value || 'Tag');
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredIcons, setFilteredIcons] = useState(COMMON_ICONS);

  useEffect(() => {
    setSelectedIcon(value || 'Tag');
  }, [value]);

  useEffect(() => {
    if (!searchTerm) {
      setFilteredIcons(COMMON_ICONS);
      return;
    }

    const term = searchTerm.toLowerCase();
    const filtered = Object.keys(LucideIcons)
      .filter(iconName => 
        iconName !== 'createLucideIcon' && 
        typeof LucideIcons[iconName as keyof typeof LucideIcons] === 'function' &&
        iconName.toLowerCase().includes(term)
      )
      .slice(0, 100); // Limitar a 100 resultados para performance

    setFilteredIcons(filtered);
  }, [searchTerm]);

  const handleIconChange = (iconName: string) => {
    setSelectedIcon(iconName);
    onChange(iconName);
  };

  // Renderizar o ícone selecionado
  const SelectedIconComponent = LucideIcons[selectedIcon as keyof typeof LucideIcons] as React.FC<any>;

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="outline" className="w-full justify-between">
          <div className="flex items-center gap-2">
            {SelectedIconComponent && <SelectedIconComponent className="h-4 w-4" />}
            <span>{selectedIcon}</span>
          </div>
          <ChevronDown className="h-4 w-4 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-64">
        <div className="space-y-2">
          <div className="flex items-center gap-2 mb-2">
            <Search className="h-4 w-4 opacity-50" />
            <Input
              placeholder="Buscar ícone..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="h-8"
            />
          </div>
          <ScrollArea className="h-[300px]">
            <div className="grid grid-cols-4 gap-2">
              {filteredIcons.map((iconName) => {
                const IconComponent = LucideIcons[iconName as keyof typeof LucideIcons] as React.FC<any>;
                
                if (!IconComponent) return null;
                
                return (
                  <div
                    key={iconName}
                    className={`relative flex h-10 w-full cursor-pointer flex-col items-center justify-center rounded-md border p-1 hover:bg-muted ${
                      selectedIcon === iconName ? 'border-primary bg-primary/10' : 'border-transparent'
                    }`}
                    onClick={() => handleIconChange(iconName)}
                    title={iconName}
                  >
                    <IconComponent className="h-4 w-4" />
                    <span className="mt-1 text-[10px] text-muted-foreground truncate w-full text-center">
                      {iconName}
                    </span>
                    {selectedIcon === iconName && (
                      <div className="absolute right-1 top-1 h-2 w-2 rounded-full bg-primary" />
                    )}
                  </div>
                );
              })}
            </div>
          </ScrollArea>
        </div>
      </PopoverContent>
    </Popover>
  );
}
