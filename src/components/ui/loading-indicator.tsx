import React from 'react';
import { Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface LoadingIndicatorProps {
  /**
   * Tamanho do indicador de carregamento
   * @default "md"
   */
  size?: 'sm' | 'md' | 'lg' | 'xl';
  
  /**
   * Texto a ser exibido junto com o indicador
   */
  text?: string;
  
  /**
   * Se o indicador deve ser exibido em tela cheia
   * @default false
   */
  fullScreen?: boolean;
  
  /**
   * Classes CSS adicionais
   */
  className?: string;
  
  /**
   * Se o indicador deve ser centralizado
   * @default true
   */
  centered?: boolean;
  
  /**
   * Cor do indicador
   * @default "primary"
   */
  color?: 'primary' | 'secondary' | 'accent' | 'muted';
}

/**
 * Componente de indicador de carregamento
 */
export function LoadingIndicator({
  size = 'md',
  text,
  fullScreen = false,
  className,
  centered = true,
  color = 'primary'
}: LoadingIndicatorProps) {
  // Mapear tamanhos para classes
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8',
    xl: 'h-12 w-12'
  };
  
  // Mapear cores para classes
  const colorClasses = {
    primary: 'text-primary',
    secondary: 'text-secondary',
    accent: 'text-accent',
    muted: 'text-muted-foreground'
  };
  
  // Construir classes para o container
  const containerClasses = cn(
    'flex items-center gap-2',
    centered && 'justify-center',
    fullScreen && 'fixed inset-0 bg-background/80 backdrop-blur-sm z-50',
    className
  );
  
  // Construir classes para o spinner
  const spinnerClasses = cn(
    'animate-spin',
    sizeClasses[size],
    colorClasses[color]
  );
  
  // Construir classes para o texto
  const textClasses = cn(
    'font-medium',
    {
      'text-xs': size === 'sm',
      'text-sm': size === 'md',
      'text-base': size === 'lg',
      'text-lg': size === 'xl'
    },
    colorClasses[color]
  );
  
  return (
    <div className={containerClasses}>
      <Loader2 className={spinnerClasses} />
      {text && <span className={textClasses}>{text}</span>}
    </div>
  );
}

/**
 * Componente de indicador de carregamento em tela cheia
 */
export function FullScreenLoading({
  text = 'Carregando...',
  size = 'lg',
  color = 'primary'
}: Partial<LoadingIndicatorProps>) {
  return (
    <LoadingIndicator
      fullScreen
      text={text}
      size={size}
      color={color}
      className="flex-col gap-4"
    />
  );
}

/**
 * Componente de indicador de carregamento para seções
 */
export function SectionLoading({
  text = 'Carregando...',
  size = 'md',
  className
}: Partial<LoadingIndicatorProps>) {
  return (
    <LoadingIndicator
      text={text}
      size={size}
      className={cn('py-8', className)}
    />
  );
}

/**
 * Componente de indicador de carregamento inline
 */
export function InlineLoading({
  text,
  size = 'sm',
  className,
  centered = false
}: Partial<LoadingIndicatorProps>) {
  return (
    <LoadingIndicator
      text={text}
      size={size}
      centered={centered}
      className={className}
    />
  );
}
