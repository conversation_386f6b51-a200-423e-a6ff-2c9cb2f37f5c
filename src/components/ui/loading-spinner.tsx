import { Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";

export interface LoadingSpinnerProps {
  /**
   * Tamanho do spinner (pequeno, médio, grande)
   * @default "medium"
   */
  size?: "small" | "medium" | "large";
  
  /**
   * Texto a ser exibido abaixo do spinner
   */
  text?: string;
  
  /**
   * Texto secundário/descrição
   */
  description?: string;
  
  /**
   * Se deve ocupar a tela inteira
   * @default false
   */
  fullScreen?: boolean;
  
  /**
   * Se deve ter um fundo com gradiente
   * @default false
   */
  withGradient?: boolean;
  
  /**
   * Classes CSS adicionais
   */
  className?: string;
}

export function LoadingSpinner({
  size = "medium",
  text,
  description,
  fullScreen = false,
  withGradient = false,
  className,
}: LoadingSpinnerProps) {
  // Definir tamanhos do spinner
  const spinnerSizes = {
    small: "h-5 w-5",
    medium: "h-8 w-8",
    large: "h-12 w-12",
  };
  
  // Definir tamanhos do texto
  const textSizes = {
    small: "text-sm",
    medium: "text-lg",
    large: "text-2xl",
  };
  
  // Container principal
  const containerClasses = cn(
    "flex flex-col items-center justify-center",
    fullScreen ? "h-[calc(100vh-65px)] w-full" : "py-8",
    className
  );
  
  // Container do spinner
  const spinnerContainerClasses = cn(
    "relative",
    withGradient ? "p-1" : ""
  );
  
  // Efeito de gradiente
  const gradientClasses = cn(
    "absolute -inset-1 rounded-full bg-gradient-to-r from-primary to-primary/50 opacity-75 blur-sm",
    withGradient ? "block" : "hidden"
  );
  
  return (
    <div className={containerClasses}>
      <div className={spinnerContainerClasses}>
        {withGradient && <div className={gradientClasses}></div>}
        <Loader2 
          className={cn(
            "animate-spin text-primary relative", 
            spinnerSizes[size]
          )} 
        />
      </div>
      
      {text && (
        <span className={cn("font-medium text-muted-foreground mt-4", textSizes[size])}>
          {text}
        </span>
      )}
      
      {description && (
        <p className="text-sm text-muted-foreground mt-1">
          {description}
        </p>
      )}
    </div>
  );
}

/**
 * Componente para exibir um loading em tabelas
 */
export function TableLoading({ 
  colSpan = 1, 
  text = "Carregando dados...",
  size = "small"
}: { 
  colSpan?: number; 
  text?: string;
  size?: "small" | "medium";
}) {
  return (
    <tr>
      <td colSpan={colSpan} className="text-center py-4">
        <LoadingSpinner 
          size={size} 
          text={text} 
          className="mx-auto"
        />
      </td>
    </tr>
  );
}

/**
 * Componente para exibir um loading em cards
 */
export function CardLoading({
  text = "Carregando...",
  description,
  size = "medium",
  withGradient = true,
}: {
  text?: string;
  description?: string;
  size?: "small" | "medium" | "large";
  withGradient?: boolean;
}) {
  return (
    <div className="flex items-center justify-center p-8">
      <LoadingSpinner
        size={size}
        text={text}
        description={description}
        withGradient={withGradient}
      />
    </div>
  );
}

/**
 * Componente para exibir um loading em tela cheia
 */
export function FullScreenLoading({
  text = "Carregando...",
  description,
  withGradient = true,
}: {
  text?: string;
  description?: string;
  withGradient?: boolean;
}) {
  return (
    <div className="flex items-center justify-center h-[calc(100vh-65px)] w-full">
      <LoadingSpinner
        size="large"
        text={text}
        description={description}
        withGradient={withGradient}
        fullScreen
      />
    </div>
  );
}

/**
 * Componente para exibir um loading inline
 */
export function InlineLoading({
  text,
  size = "small",
}: {
  text?: string;
  size?: "small" | "medium";
}) {
  return (
    <div className="flex items-center gap-2 py-2">
      <Loader2 className={cn("animate-spin text-primary", size === "small" ? "h-4 w-4" : "h-5 w-5")} />
      {text && <span className="text-sm">{text}</span>}
    </div>
  );
}
