import { ReactNode } from "react";
import { ArrowRightIcon } from "@radix-ui/react-icons";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";

const BentoGrid = ({
  children,
  className,
}: {
  children: ReactNode;
  className?: string;
}) => {
  return (
    <div className={cn("grid w-full auto-rows-[22rem] grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4", className)}>
      {children}
    </div>
  );
};

const BentoCard = ({
  name,
  className,
  background,
  Icon,
  description,
  href,
  cta,
}: {
  name: string;
  className: string;
  background: string;
  Icon: any;
  description: string;
  href: string;
  cta: string;
}) => (
  <div
    key={name}
    className={cn(
      "group relative col-span-1 flex flex-col justify-between overflow-hidden rounded-xl",
      background,
      "border border-neutral-200/50 dark:border-neutral-800/50",
      "transform-gpu transition-all duration-300",
      "hover:shadow-xl hover:shadow-neutral-200/20 dark:hover:shadow-neutral-900/20",
      "backdrop-blur-sm bg-white/80 dark:bg-neutral-900/80",
      className,
    )}
  >
    <div className="pointer-events-none z-10 flex transform-gpu flex-col gap-2 p-6 transition-all duration-300 group-hover:-translate-y-10">
      <div className="flex items-center gap-4">
        <div className="p-2 rounded-lg bg-neutral-100/80 dark:bg-neutral-800/80 backdrop-blur-sm border border-neutral-200/50 dark:border-neutral-700/50">
          <Icon className="h-8 w-8 origin-left transform-gpu text-neutral-600 transition-all duration-300 ease-in-out group-hover:scale-90 dark:text-neutral-400" />
        </div>
      </div>
      <h3 className="text-2xl font-semibold text-neutral-800 dark:text-neutral-200 mt-2 bg-clip-text">
        {name}
      </h3>
      <p className="text-neutral-600 dark:text-neutral-400">{description}</p>
    </div>
    <div className="pointer-events-none absolute bottom-0 flex w-full translate-y-10 transform-gpu flex-row items-center p-4 opacity-0 transition-all duration-300 group-hover:translate-y-0 group-hover:opacity-100">
      <Button 
        variant="ghost" 
        asChild 
        size="sm" 
        className="pointer-events-auto bg-neutral-100/80 hover:bg-neutral-200/80 dark:bg-neutral-800/80 dark:hover:bg-neutral-700/80 backdrop-blur-sm text-neutral-700 dark:text-neutral-300 border border-neutral-200/50 dark:border-neutral-700/50"
      >
        <a href={href}>
          {cta}
          <ArrowRightIcon className="ml-2 h-4 w-4" />
        </a>
      </Button>
    </div>
    <div className="pointer-events-none absolute inset-0 transform-gpu transition-all duration-300 group-hover:bg-neutral-100/[.03] dark:group-hover:bg-neutral-200/[.03]" />
  </div>
);

export { BentoCard, BentoGrid };