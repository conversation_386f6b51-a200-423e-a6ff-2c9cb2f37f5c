import React from 'react';
import { Button, ButtonProps } from '@/components/ui/button';
import { PermissionTooltip } from '@/components/ui/permission-tooltip';
import { usePermissions } from '@/hooks/usePermissions';

interface PermissionButtonProps extends ButtonProps {
  action: string;
  resource: string;
  resourceId?: string;
  tooltipMessage?: string;
  showTooltip?: boolean;
  showIcon?: boolean;
  tooltipSide?: "top" | "right" | "bottom" | "left";
  tooltipAlign?: "start" | "center" | "end";
  fallback?: React.ReactNode;
}

/**
 * Botão que é habilitado apenas se o usuário tiver permissão para a ação especificada.
 * Se o usuário não tiver permissão, o botão é desabilitado e um tooltip é exibido.
 */
export function PermissionButton({
  action,
  resource,
  resourceId,
  tooltipMessage,
  showTooltip = true,
  showIcon = true,
  tooltipSide = "top",
  tooltipAlign = "center",
  fallback = null,
  children,
  ...props
}: PermissionButtonProps) {
  const { canSee } = usePermissions();
  
  // Verificar se o usuário tem permissão
  const hasPermission = canSee(action, resource, resourceId);
  
  // Se não tem permissão e há um fallback, renderizar o fallback
  if (!hasPermission && fallback) {
    return <>{fallback}</>;
  }
  
  // Se não tem permissão e não deve mostrar tooltip, renderizar botão desabilitado
  if (!hasPermission && !showTooltip) {
    return (
      <Button {...props} disabled>
        {children}
      </Button>
    );
  }
  
  // Se não tem permissão e deve mostrar tooltip, renderizar botão com tooltip
  if (!hasPermission && showTooltip) {
    return (
      <PermissionTooltip
        action={action}
        resource={resource}
        resourceId={resourceId}
        message={tooltipMessage}
        showIcon={showIcon}
        side={tooltipSide}
        align={tooltipAlign}
      >
        <Button {...props} disabled>
          {children}
        </Button>
      </PermissionTooltip>
    );
  }
  
  // Se tem permissão, renderizar botão normal
  return (
    <Button {...props}>
      {children}
    </Button>
  );
}
