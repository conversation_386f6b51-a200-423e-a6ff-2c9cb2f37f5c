import React from 'react';
import { Button, ButtonProps } from '@/components/ui/button';
import { usePermissions } from '@/hooks/usePermissions';

interface ConditionalButtonProps extends ButtonProps {
  action: string;
  resource: string;
  resourceId?: string;
  fallback?: React.ReactNode;
  showAlways?: boolean;
}

export function ConditionalButton({
  action,
  resource,
  resourceId,
  fallback = null,
  showAlways = false,
  children,
  ...props
}: ConditionalButtonProps) {
  const { canSee } = usePermissions();
  
  // Verificar se o usuário tem permissão para ver o botão
  const hasPermission = showAlways || canSee(action, resource, resourceId);
  
  if (!hasPermission) {
    return <>{fallback}</>;
  }
  
  return <Button {...props}>{children}</Button>;
}
