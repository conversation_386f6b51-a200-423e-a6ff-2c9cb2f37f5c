'use client';

import { Card } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import {
  DndContext,
  rectIntersection,
  useDraggable,
  useDroppable,
  DragOverlay,
  defaultDropAnimationSideEffects,
  DragStartEvent,
  DragOverEvent,
} from '@dnd-kit/core';
import type { DragEndEvent } from '@dnd-kit/core';
import type { ReactNode } from 'react';
import { createPortal } from 'react-dom';
import { useState, memo, useRef, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { MoreHorizontal, Filter, SortAsc, AlertCircle, ChevronLeft, ChevronRight, GripVertical } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

export type Status = {
  id: string;
  name: string;
  color: string;
};

export type Feature = {
  id: string;
  name: string;
  startAt: Date;
  endAt: Date;
  status: Status;
};

export type KanbanBoardProps = {
  id: Status['id'];
  children: ReactNode;
  className?: string;
};

export const KanbanBoard = ({ id, children, className }: KanbanBoardProps) => {
  const { isOver, setNodeRef } = useDroppable({ id });

  return (
    <div
      className={cn(
        'flex flex-col gap-1 rounded-lg border bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm p-2 text-xs transition-all w-[240px] shrink-0 min-h-[500px]',
        isOver ? 'ring-2 ring-primary ring-offset-2' : 'ring-0',
        className
      )}
      ref={setNodeRef}
    >
      {children}
    </div>
  );
};

export type KanbanCardProps = Pick<Feature, 'id' | 'name'> & {
  index: number;
  parent: string;
  children?: ReactNode;
  className?: string;
  onClick?: () => void;
};

export const KanbanCard = ({
  id,
  name,
  index,
  parent,
  children,
  className,
  onClick,
}: KanbanCardProps) => {
  const { attributes, listeners, setNodeRef, transform, isDragging } =
    useDraggable({
      id,
      data: { index, parent },
    });

  return (
    <Card
      ref={setNodeRef}
      {...listeners}
      {...attributes}
      onMouseDown={(e) => {
        listeners?.onMouseDown?.(e);
        e.stopPropagation();
      }}
      className={cn(
        'rounded-lg p-3 border border-border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow duration-200 relative group cursor-grab active:cursor-grabbing',
        isDragging ? 'opacity-75 scale-105 z-10 shadow-lg' : 'opacity-100 scale-100 z-0',
        className
      )}
      style={{
        transform: transform
          ? `translate3d(${transform.x}px, ${transform.y}px, 0) scale(${isDragging ? 1.05 : 1})`
          : undefined,
      }}
      onClick={onClick}
    >
      <div className="text-sm font-medium leading-none">
        {children ?? <p className="m-0">{name}</p>}
      </div>
    </Card>
  );
};

export const MemoizedKanbanCard = memo(KanbanCard);

export type KanbanCardsProps = {
  children: ReactNode;
  className?: string;
};

export const KanbanCards = ({ children, className }: KanbanCardsProps) => (
  <div className={cn('flex-1 flex flex-col gap-2 overflow-y-auto pr-1 min-h-0 max-h-full', className)}>
    {children}
  </div>
);

export type KanbanHeaderProps =
  | {
      children: ReactNode;
    }
  | {
      name: Status['name'];
      color: Status['color'];
      className?: string;
      count?: number;
      total?: number;
      onSort?: () => void;
      onFilter?: () => void;
      onConfig?: () => void;
      alerts?: number;
      isRecolhida?: boolean;
      onToggleRecolher?: () => void;
    };

export const KanbanHeader = (props: KanbanHeaderProps) =>
  'children' in props ? (
    props.children
  ) : (
    <TooltipProvider delayDuration={100}>
      <div className={cn('flex flex-col gap-2 sticky top-0 bg-background/90 dark:bg-neutral-900/90 backdrop-blur-sm pt-3 pb-2 px-2 z-10 border-b', props.className)}>
        {props.isRecolhida ? (
          <div className="flex flex-col h-full min-h-[500px]">
            <div className="flex items-center justify-between mb-2">
              <Tooltip>
                <TooltipTrigger asChild>
                  <div
                    className="h-3 w-3 rounded-full shrink-0"
                    style={{ backgroundColor: props.color }}
                  />
                </TooltipTrigger>
                <TooltipContent side="right">
                  <p>{props.name}</p>
                </TooltipContent>
              </Tooltip>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6 rounded-md"
                    onClick={props.onToggleRecolher}
                  >
                    <ChevronRight className="w-4 h-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="right">
                  <p>Expandir Coluna</p>
                </TooltipContent>
              </Tooltip>
            </div>
            <div className="flex-1 flex flex-col justify-between items-center py-4">
              <div className="flex-1 flex items-center justify-center">
                <div className="rotate-[-90deg] whitespace-nowrap origin-center flex items-center gap-2 translate-y-8">
                  <p className="m-0 font-semibold text-sm">{props.name}</p>
                  <span className="text-xs text-muted-foreground">({props.count || 0})</span>
                </div>
              </div>
              {props.alerts && props.alerts > 0 && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="mt-auto text-amber-500 p-1">
                      <AlertCircle className="w-4 h-4" />
                    </div>
                  </TooltipTrigger>
                  <TooltipContent side="bottom">
                    <p>{props.alerts} Alerta(s)</p>
                  </TooltipContent>
                </Tooltip>
              )}
            </div>
          </div>
        ) : (
          <>
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2 min-w-0">
                <Tooltip>
                   <TooltipTrigger asChild>
                    <div
                      className="h-3 w-3 rounded-full shrink-0"
                      style={{ backgroundColor: props.color }}
                    />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{props.name}</p>
                  </TooltipContent>
                </Tooltip>
                <p className="m-0 font-semibold text-base truncate">{props.name}</p>
                {props.alerts && props.alerts > 0 && (
                 <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="flex items-center gap-1 text-amber-500 cursor-default">
                        <AlertCircle className="w-4 h-4" />
                        <span className="text-xs font-medium">{props.alerts}</span>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{props.alerts} Alerta(s)</p>
                    </TooltipContent>
                  </Tooltip>
                )}
              </div>
              <div className="flex items-center gap-0.5">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-7 w-7 rounded-md"
                      onClick={props.onToggleRecolher}
                    >
                      <ChevronLeft className="w-4 h-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Recolher Coluna</p>
                  </TooltipContent>
                </Tooltip>
                <DropdownMenu>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-7 w-7 rounded-md">
                          <MoreHorizontal className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Opções</p>
                    </TooltipContent>
                  </Tooltip>
                  <DropdownMenuContent align="end" className="w-48">
                    <DropdownMenuLabel className="text-xs">Opções da Coluna</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem className="text-xs gap-2" onClick={props.onSort}>
                      <SortAsc className="w-4 h-4" />
                      Ordenar por Data
                    </DropdownMenuItem>
                    <DropdownMenuItem className="text-xs gap-2" onClick={props.onSort}>
                      <SortAsc className="w-4 h-4" />
                      Ordenar por Valor
                    </DropdownMenuItem>
                    <DropdownMenuItem className="text-xs gap-2" onClick={props.onFilter}>
                      <Filter className="w-4 h-4" />
                      Filtrar Cards
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem className="text-xs" onClick={props.onConfig}>
                      Configurar Coluna
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
            <div className="flex items-center justify-between px-1 text-xs text-muted-foreground mb-1">
              <div className="flex items-center gap-1.5">
                <span>{props.count || 0}</span>
                <span>cards</span>
              </div>
              {props.total && (
                <div>
                  {props.total.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' })}
                </div>
              )}
            </div>
          </>
        )}
      </div>
    </TooltipProvider>
  );

export type KanbanProviderProps = {
  children: ReactNode;
  onDragEnd: (event: DragEndEvent) => void;
  className?: string;
};

const dropAnimation = {
  sideEffects: defaultDropAnimationSideEffects({
    styles: {
      active: {
        opacity: '0.5',
      },
    },
  }),
};

export const KanbanProvider = ({
  children,
  onDragEnd,
  className,
}: KanbanProviderProps) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isPanning, setIsPanning] = useState(false);
  const [startX, setStartX] = useState(0);
  const [scrollLeftStart, setScrollLeftStart] = useState(0);

  const handleMouseDown = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target !== containerRef.current) return;
    
    if (containerRef.current) {
      setIsPanning(true);
      setStartX(e.pageX - containerRef.current.offsetLeft);
      setScrollLeftStart(containerRef.current.scrollLeft);
      containerRef.current.style.cursor = 'grabbing';
      containerRef.current.style.userSelect = 'none';
    }
  }, []);

  const handleMouseLeaveOrUp = useCallback(() => {
    if (!isPanning) return;
    setIsPanning(false);
    if (containerRef.current) {
      containerRef.current.style.cursor = 'grab';
      containerRef.current.style.userSelect = 'auto';
    }
  }, [isPanning]);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isPanning || !containerRef.current) return;
    e.preventDefault();
    const x = e.pageX - containerRef.current.offsetLeft;
    const walk = (x - startX);
    containerRef.current.scrollLeft = scrollLeftStart - walk;
  }, [isPanning, startX, scrollLeftStart]);

  useEffect(() => {
    if (isPanning) {
      window.addEventListener('mousemove', handleMouseMove);
      window.addEventListener('mouseup', handleMouseLeaveOrUp);
    }

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseLeaveOrUp);
    };
  }, [isPanning, handleMouseMove, handleMouseLeaveOrUp]);

  return (
    <DndContext 
      onDragEnd={onDragEnd}
    >
      <div
        ref={containerRef}
        className={cn('flex gap-4 min-w-full pb-20 overflow-x-auto cursor-grab', className)}
        onMouseDown={handleMouseDown}
        onMouseLeave={handleMouseLeaveOrUp}
      >
        {children}
      </div>
    </DndContext>
  );
}; 