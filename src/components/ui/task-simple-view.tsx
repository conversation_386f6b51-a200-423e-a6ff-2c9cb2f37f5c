import React from 'react';
import { CheckCircle2, Clock, Circle, AlertCircle, User, MessageSquare, Paperclip, ChevronRight } from 'lucide-react';
import { cn } from "@/lib/utils";
import { Badge } from './badge';
import { Avatar, AvatarFallback, AvatarImage } from './avatar';
import { Progress } from './progress';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface TaskSimpleViewProps {
  tasks: Array<{
    id: string;
    titulo: string;
    status: 'pendente' | 'em_andamento' | 'concluida';
    prioridade: 'baixa' | 'media' | 'alta';
    prazo: string;
    progresso: number;
    responsavel: {
      nome: string;
      avatar: string;
    };
    subtarefas: Array<{ concluida: boolean }>;
    comentarios: Array<any>;
    arquivosAnexos?: Array<any>;
    descricao: string;
  }>;
  onTaskClick: (taskId: string) => void;
  onStatusChange?: (taskId: string, newStatus: 'pendente' | 'em_andamento' | 'concluida') => void;
}

const statusIcons = {
  pendente: Circle,
  em_andamento: Clock,
  concluida: CheckCircle2,
};

const statusLabels = {
  pendente: 'Pendente',
  em_andamento: 'Em Andamento',
  concluida: 'Concluída'
};

const prioridadeClasses = {
  baixa: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
  media: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400',
  alta: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400',
};

const statusClasses = {
  pendente: 'text-yellow-500 dark:text-yellow-400',
  em_andamento: 'text-blue-500 dark:text-blue-400',
  concluida: 'text-green-500 dark:text-green-400',
};

export function TaskSimpleView({ tasks, onTaskClick, onStatusChange }: TaskSimpleViewProps) {
  const hoje = new Date();
  hoje.setHours(0, 0, 0, 0);

  // Agrupar tarefas por data
  const tarefasHoje = tasks.filter(task => {
    const dataTarefa = new Date(task.prazo);
    dataTarefa.setHours(0, 0, 0, 0);
    return dataTarefa.getTime() === hoje.getTime();
  });

  const tarefasProximas = tasks.filter(task => {
    const dataTarefa = new Date(task.prazo);
    dataTarefa.setHours(0, 0, 0, 0);
    return dataTarefa.getTime() > hoje.getTime();
  }).sort((a, b) => new Date(a.prazo).getTime() - new Date(b.prazo).getTime());

  const renderTaskItem = (task: TaskSimpleViewProps['tasks'][0]) => {
    const StatusIcon = statusIcons[task.status];
    const taskOverdue = new Date(task.prazo) < new Date() && task.status !== 'concluida';
    const subtaskProgress = task.subtarefas.length > 0 
      ? (task.subtarefas.filter(st => st.concluida).length / task.subtarefas.length) * 100
      : 0;
    
    return (
      <div
        key={task.id}
        onClick={() => onTaskClick(task.id)}
        className={cn(
          "flex items-center gap-3 px-3 py-2 rounded-lg",
          "bg-white/80 dark:bg-neutral-800/80 border border-neutral-200/50 dark:border-neutral-700/50",
          "hover:bg-neutral-50 dark:hover:bg-neutral-700/50 cursor-pointer",
          "transition-all duration-200",
          taskOverdue && task.status !== 'concluida' && "border-red-200 dark:border-red-800"
        )}
      >
        <DropdownMenu>
          <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
            <div className="cursor-pointer hover:bg-neutral-100 dark:hover:bg-neutral-700 rounded-full p-1 transition-colors">
              <StatusIcon className={cn("w-4 h-4 flex-shrink-0", statusClasses[task.status])} />
            </div>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start">
            <DropdownMenuItem onClick={(e) => {
              e.stopPropagation();
              onStatusChange?.(task.id, 'pendente');
            }}>
              <Circle className="w-4 h-4 mr-2 text-yellow-500" />
              Pendente
            </DropdownMenuItem>
            <DropdownMenuItem onClick={(e) => {
              e.stopPropagation();
              onStatusChange?.(task.id, 'em_andamento');
            }}>
              <Clock className="w-4 h-4 mr-2 text-blue-500" />
              Em Andamento
            </DropdownMenuItem>
            <DropdownMenuItem onClick={(e) => {
              e.stopPropagation();
              onStatusChange?.(task.id, 'concluida');
            }}>
              <CheckCircle2 className="w-4 h-4 mr-2 text-green-500" />
              Concluída
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2">
            <span className={cn(
              "text-sm font-medium truncate",
              task.status === 'concluida' && "line-through text-neutral-400"
            )}>
              {task.titulo}
            </span>
            {taskOverdue && task.status !== 'concluida' && (
              <AlertCircle className="w-3 h-3 text-red-500 flex-shrink-0" />
            )}
            {task.prioridade === 'alta' && (
              <span className="w-1.5 h-1.5 rounded-full bg-red-500 flex-shrink-0" />
            )}
          </div>

          <div className="flex items-center gap-3 mt-0.5">
            <div className="flex items-center gap-1.5">
              <Clock className="w-3 h-3 text-neutral-400" />
              <span className={cn(
                "text-xs text-neutral-500",
                taskOverdue && task.status !== 'concluida' && "text-red-500"
              )}>
                {new Date(task.prazo).toLocaleDateString('pt-BR')}
              </span>
            </div>

            {task.subtarefas.length > 0 && (
              <div className="flex items-center gap-1 text-xs text-neutral-500">
                <CheckCircle2 className="w-3 h-3" />
                {task.subtarefas.filter(st => st.concluida).length}/{task.subtarefas.length}
              </div>
            )}

            {(task.comentarios?.length > 0 || task.arquivosAnexos?.length) && (
              <div className="flex items-center gap-2">
                {task.comentarios?.length > 0 && (
                  <div className="flex items-center gap-1 text-xs text-neutral-500">
                    <MessageSquare className="w-3 h-3" />
                    {task.comentarios.length}
                  </div>
                )}
                {task.arquivosAnexos?.length > 0 && (
                  <div className="flex items-center gap-1 text-xs text-neutral-500">
                    <Paperclip className="w-3 h-3" />
                    {task.arquivosAnexos.length}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Avatar className="w-5 h-5">
            <AvatarImage src={task.responsavel.avatar} />
            <AvatarFallback>{task.responsavel.nome.split(' ').map(n => n[0]).join('')}</AvatarFallback>
          </Avatar>
          <ChevronRight className="w-4 h-4 text-neutral-400" />
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {tarefasHoje.length > 0 && (
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-neutral-600 dark:text-neutral-400 px-1">
            Hoje ({tarefasHoje.length})
          </h3>
          <div className="space-y-1">
            {tarefasHoje.map(renderTaskItem)}
          </div>
        </div>
      )}

      {tarefasProximas.length > 0 && (
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-neutral-600 dark:text-neutral-400 px-1">
            Próximos Dias ({tarefasProximas.length})
          </h3>
          <div className="space-y-1">
            {tarefasProximas.map(renderTaskItem)}
          </div>
        </div>
      )}
    </div>
  );
} 