import React from 'react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { usePermissions } from '@/hooks/usePermissions';
import { ShieldAlert } from 'lucide-react';

interface PermissionTooltipProps {
  action: string;
  resource: string;
  resourceId?: string;
  message?: string;
  children: React.ReactNode;
  showIcon?: boolean;
  side?: "top" | "right" | "bottom" | "left";
  align?: "start" | "center" | "end";
}

/**
 * Componente que exibe um tooltip explicando por que um elemento está desabilitado
 * quando o usuário não tem permissão para realizar uma ação.
 */
export function PermissionTooltip({
  action,
  resource,
  resourceId,
  message,
  children,
  showIcon = true,
  side = "top",
  align = "center"
}: PermissionTooltipProps) {
  const { canSee } = usePermissions();
  
  // Verificar se o usuário tem permissão
  const hasPermission = canSee(action, resource, resourceId);
  
  // Se tem permissão, apenas renderizar o conteúdo
  if (hasPermission) {
    return <>{children}</>;
  }
  
  // Mensagem padrão se não for fornecida
  const defaultMessage = `Você não tem permissão para ${action} ${resource}.`;
  const tooltipMessage = message || defaultMessage;
  
  // Renderizar o tooltip com o conteúdo
  return (
    <TooltipProvider>
      <Tooltip delayDuration={300}>
        <TooltipTrigger asChild>
          <div className="relative inline-flex">
            {children}
            {showIcon && (
              <div className="absolute -top-1 -right-1 bg-destructive text-destructive-foreground rounded-full p-0.5">
                <ShieldAlert className="h-3 w-3" />
              </div>
            )}
          </div>
        </TooltipTrigger>
        <TooltipContent side={side} align={align} className="bg-destructive text-destructive-foreground border-destructive">
          <div className="flex items-center gap-2">
            <ShieldAlert className="h-4 w-4" />
            <p>{tooltipMessage}</p>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
