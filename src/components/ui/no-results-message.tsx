import React from 'react';
import { <PERSON>ert<PERSON>ircle, Filter, FilterX, Refresh<PERSON>w, ShieldAlert } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface NoResultsMessageProps {
  /**
   * Title of the message
   */
  title?: string;
  /**
   * Description text
   */
  description?: string;
  /**
   * Icon to display
   */
  icon?: React.ReactNode;
  /**
   * Whether to show a clear filters button
   */
  showClearFiltersButton?: boolean;
  /**
   * Function to call when clear filters button is clicked
   */
  onClearFilters?: () => void;
  /**
   * Whether to show a refresh button
   */
  showRefreshButton?: boolean;
  /**
   * Function to call when refresh button is clicked
   */
  onRefresh?: () => void;
  /**
   * Whether this is an authentication error
   */
  isAuthError?: boolean;
  /**
   * Additional CSS classes
   */
  className?: string;
  /**
   * Additional CSS classes for the card
   */
  cardClassName?: string;
}

/**
 * A reusable component for displaying a "no results found" message
 */
export function NoResultsMessage({
  title = "Nenhum resultado encontrado",
  description = "Verifique se existem dados cadastrados ou se os filtros estão muito restritivos.",
  icon = <AlertCircle className="h-10 w-10 text-muted-foreground" />,
  showClearFiltersButton = false,
  onClearFilters,
  showRefreshButton = false,
  onRefresh,
  isAuthError = false,
  className,
  cardClassName,
}: NoResultsMessageProps) {
  // Se for um erro de autenticação, substituir os valores padrão
  const finalTitle = isAuthError
    ? "Erro de conexão"
    : title;

  const finalDescription = isAuthError
    ? "Não foi possível carregar os dados devido a um problema de autenticação. Tente recarregar a página ou fazer login novamente."
    : description;

  const finalIcon = isAuthError
    ? <ShieldAlert className="h-10 w-10 text-destructive" />
    : icon;

  // Verificar se há erros de autenticação no console para diagnóstico
  React.useEffect(() => {
    if (isAuthError) {
      console.log('[Auth] Exibindo mensagem de erro de autenticação');

      // Marcar elemento para diagnóstico
      const markAuthError = () => {
        document.body.setAttribute('data-auth-error', 'true');
      };

      // Tentar verificar a sessão atual
      const checkSession = async () => {
        try {
          const { supabase } = await import('@/lib/supabase');
          const { data } = await supabase.auth.getSession();

          if (!data.session) {
            console.warn('[Auth] Nenhuma sessão encontrada');
          } else {
            const expiryTime = new Date(data.session.expires_at * 1000);
            const now = new Date();
            const timeToExpiry = expiryTime.getTime() - now.getTime();
            const minutesToExpiry = Math.floor(timeToExpiry / 60000);

            console.log(`[Auth] Sessão encontrada, expira em ${minutesToExpiry} minutos`);

            // Verificar se a sessão é válida com uma consulta simples
            const { error: testError } = await supabase
              .from('profiles')
              .select('id')
              .limit(1);

            if (testError) {
              console.error('[Auth] Sessão inválida:', testError.message);
              markAuthError();
            }
          }
        } catch (error) {
          console.error('[Auth] Erro ao verificar sessão:', error);
          markAuthError();
        }
      };

      checkSession();
    }
  }, [isAuthError]);

  return (
    <div className={cn("w-full", className)}>
      <Card className={cn("border-muted/40 bg-background shadow-sm", isAuthError ? "border-destructive/30" : "", cardClassName)}>
        <CardContent className="flex flex-col items-center justify-center p-6 text-center">
          <div className="mb-4">
            {finalIcon}
          </div>
          <h3 className="text-lg font-medium mb-2">{finalTitle}</h3>
          <p className="text-sm text-muted-foreground max-w-md mb-4">
            {finalDescription}
          </p>
          <div className="flex gap-2">
            {showClearFiltersButton && onClearFilters && (
              <Button
                variant="outline"
                size="sm"
                onClick={onClearFilters}
                className="gap-2"
              >
                <FilterX className="h-4 w-4" />
                Limpar Filtros
              </Button>
            )}
            {(showRefreshButton || isAuthError) && (
              <Button
                variant={isAuthError ? "default" : "outline"}
                size="sm"
                onClick={async () => {
                  if (isAuthError) {
                    // Tentar atualizar a sessão antes de recarregar
                    try {
                      const { refreshSupabaseSession } = await import('@/lib/supabase');
                      const success = await refreshSupabaseSession();

                      if (success) {
                        console.log('[Auth] Sessão atualizada com sucesso, recarregando...');
                        // Aguardar um pouco para garantir que a sessão seja propagada
                        setTimeout(() => {
                          if (onRefresh) {
                            onRefresh();
                          } else {
                            window.location.reload();
                          }
                        }, 500);
                      } else {
                        console.warn('[Auth] Falha ao atualizar sessão, redirecionando para login...');
                        // Redirecionar para a página de login
                        window.location.href = '/login';
                      }
                    } catch (error) {
                      console.error('[Auth] Erro ao tentar atualizar sessão:', error);
                      // Recarregar a página normalmente
                      if (onRefresh) {
                        onRefresh();
                      } else {
                        window.location.reload();
                      }
                    }
                  } else {
                    // Comportamento normal para não-erros de autenticação
                    if (onRefresh) {
                      onRefresh();
                    } else {
                      window.location.reload();
                    }
                  }
                }}
                className="gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                {isAuthError ? "Reconectar" : "Recarregar"}
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

/**
 * A specialized version of NoResultsMessage for precatórios
 */
export function NoPrecatoriosMessage({
  showClearFiltersButton = false,
  onClearFilters,
  showRefreshButton = false,
  onRefresh,
  isAuthError = false,
  className,
  cardClassName,
}: Omit<NoResultsMessageProps, 'title' | 'description' | 'icon'>) {
  return (
    <NoResultsMessage
      title="Nenhum precatório encontrado"
      description="Verifique se existem precatórios cadastrados ou se os filtros estão muito restritivos."
      icon={<Filter className="h-10 w-10 text-muted-foreground" />}
      showClearFiltersButton={showClearFiltersButton}
      onClearFilters={onClearFilters}
      showRefreshButton={showRefreshButton}
      onRefresh={onRefresh}
      isAuthError={isAuthError}
      className={className}
      cardClassName={cardClassName}
    />
  );
}
