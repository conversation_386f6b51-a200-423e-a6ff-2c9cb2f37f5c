import React, { useState, useEffect } from 'react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { Check, ChevronDown } from 'lucide-react';

interface ColorPickerProps {
  color: string;
  onChange: (color: string) => void;
}

const PRESET_COLORS = [
  '#3b82f6', // blue
  '#10b981', // green
  '#f59e0b', // amber
  '#ef4444', // red
  '#8b5cf6', // purple
  '#6366f1', // indigo
  '#ec4899', // pink
  '#14b8a6', // teal
  '#f97316', // orange
  '#84cc16', // lime
  '#06b6d4', // cyan
  '#a855f7', // violet
  '#d946ef', // fuchsia
  '#f43f5e', // rose
  '#64748b', // slate
  '#0f172a', // slate-900
];

export function ColorPicker({ color, onChange }: ColorPickerProps) {
  const [selectedColor, setSelectedColor] = useState(color || PRESET_COLORS[0]);

  useEffect(() => {
    setSelectedColor(color || PRESET_COLORS[0]);
  }, [color]);

  const handleColorChange = (newColor: string) => {
    setSelectedColor(newColor);
    onChange(newColor);
  };

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className="w-full justify-between"
          style={{ backgroundColor: selectedColor, color: isLightColor(selectedColor) ? '#000' : '#fff' }}
        >
          <span>{selectedColor}</span>
          <ChevronDown className="h-4 w-4 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-64">
        <div className="grid grid-cols-5 gap-2">
          {PRESET_COLORS.map((presetColor) => (
            <div
              key={presetColor}
              className="relative flex h-8 w-8 cursor-pointer items-center justify-center rounded-md"
              style={{ backgroundColor: presetColor }}
              onClick={() => handleColorChange(presetColor)}
            >
              {selectedColor === presetColor && (
                <Check className="h-4 w-4" style={{ color: isLightColor(presetColor) ? '#000' : '#fff' }} />
              )}
            </div>
          ))}
        </div>
        <div className="mt-4">
          <input
            type="color"
            value={selectedColor}
            onChange={(e) => handleColorChange(e.target.value)}
            className="w-full h-8 cursor-pointer"
          />
        </div>
      </PopoverContent>
    </Popover>
  );
}

// Função para determinar se uma cor é clara (para escolher texto preto ou branco)
function isLightColor(color: string): boolean {
  // Converter hex para RGB
  let r, g, b;
  
  if (color.startsWith('#')) {
    const hex = color.substring(1);
    r = parseInt(hex.substring(0, 2), 16);
    g = parseInt(hex.substring(2, 4), 16);
    b = parseInt(hex.substring(4, 6), 16);
  } else {
    // Cor não está em formato hex, assumir escura
    return false;
  }
  
  // Calcular luminosidade
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
  
  // Retornar true se a cor for clara
  return luminance > 0.5;
}
