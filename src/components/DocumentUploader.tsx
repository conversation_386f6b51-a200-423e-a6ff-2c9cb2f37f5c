import { useState } from 'react';
import { Upload, File, X, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { 
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { uploadDocumentoCliente, uploadDocumentoPrecatorio, Documento } from '@/services/documentosService';
import { toast } from 'sonner';

interface DocumentUploaderProps {
  clientes: any[];
  precatorios: any[];
  onDocumentoAdicionado: (documento: Documento) => void;
  onCancel: () => void;
}

// Schema de validação
const formSchema = z.object({
  tipo: z.string().min(1, { message: 'Selecione o tipo de documento' }),
  descricao: z.string().optional(),
  categoria: z.enum(['cliente', 'precatorio'], { 
    required_error: 'Selecione a categoria' 
  }),
  clienteId: z.string().optional(),
  precatorioId: z.string().optional(),
}).refine(data => {
  if (data.categoria === 'cliente') {
    return !!data.clienteId;
  }
  if (data.categoria === 'precatorio') {
    return !!data.precatorioId;
  }
  return true;
}, {
  message: 'Selecione um cliente ou precatório',
  path: ['clienteId'],
});

export function DocumentUploader({ 
  clientes, 
  precatorios, 
  onDocumentoAdicionado,
  onCancel
}: DocumentUploaderProps) {
  const [file, setFile] = useState<File | null>(null);
  const [progress, setProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);

  // Inicializar formulário
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      tipo: '',
      descricao: '',
      categoria: 'cliente',
      clienteId: '',
      precatorioId: '',
    },
  });

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    const droppedFile = e.dataTransfer.files[0];
    if (droppedFile) setFile(droppedFile);
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) setFile(selectedFile);
  };

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    if (!file) {
      toast.error('Selecione um arquivo para upload');
      return;
    }

    try {
      setIsUploading(true);
      
      // Simular progresso
      const interval = setInterval(() => {
        setProgress(prev => {
          if (prev >= 90) {
            clearInterval(interval);
            return prev;
          }
          return prev + 10;
        });
      }, 300);

      let documento: Documento;

      if (values.categoria === 'cliente' && values.clienteId) {
        documento = await uploadDocumentoCliente(
          file, 
          values.clienteId, 
          values.tipo, 
          values.descricao
        );
      } else if (values.categoria === 'precatorio' && values.precatorioId) {
        documento = await uploadDocumentoPrecatorio(
          file, 
          values.precatorioId, 
          values.tipo, 
          values.descricao
        );
      } else {
        throw new Error('Categoria ou ID inválido');
      }

      clearInterval(interval);
      setProgress(100);
      
      // Notificar componente pai
      onDocumentoAdicionado(documento);
      
      // Resetar estado
      setTimeout(() => {
        setFile(null);
        setProgress(0);
        form.reset();
      }, 500);
    } catch (error) {
      console.error('Erro ao fazer upload:', error);
      toast.error('Erro ao fazer upload do documento');
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="space-y-4">
      <div
        onDrop={handleDrop}
        onDragOver={e => e.preventDefault()}
        className="border-2 border-dashed rounded-lg p-8 text-center hover:border-primary transition-colors"
      >
        {file ? (
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <File className="h-6 w-6 text-primary" />
              <span className="font-medium">{file.name}</span>
              <span className="text-xs text-muted-foreground">
                ({(file.size / 1024).toFixed(2)} KB)
              </span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setFile(null)}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        ) : (
          <div>
            <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <p className="text-lg font-medium mb-2">Arraste um arquivo ou clique para selecionar</p>
            <p className="text-sm text-muted-foreground mb-4">
              Suporta arquivos PDF, DOC, DOCX, JPG, PNG e outros formatos comuns
            </p>
            <input
              type="file"
              onChange={handleFileSelect}
              className="hidden"
              id="file-upload"
            />
            <Button
              variant="outline"
              onClick={() => document.getElementById('file-upload')?.click()}
            >
              Selecionar arquivo
            </Button>
          </div>
        )}
      </div>

      {file && (
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="categoria"
              render={({ field }) => (
                <FormItem className="space-y-1">
                  <FormLabel>Categoria</FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      className="flex flex-col space-y-1"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="cliente" id="cliente" />
                        <Label htmlFor="cliente">Cliente</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="precatorio" id="precatorio" />
                        <Label htmlFor="precatorio">Precatório</Label>
                      </div>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {form.watch('categoria') === 'cliente' && (
              <FormField
                control={form.control}
                name="clienteId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Cliente</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione um cliente" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {clientes.map((cliente) => (
                          <SelectItem key={cliente.id} value={cliente.id}>
                            {cliente.nome}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {form.watch('categoria') === 'precatorio' && (
              <FormField
                control={form.control}
                name="precatorioId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Precatório</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione um precatório" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {precatorios.map((precatorio) => (
                          <SelectItem key={precatorio.id} value={precatorio.id}>
                            {precatorio.numero_precatorio || precatorio.id}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <FormField
              control={form.control}
              name="tipo"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tipo de Documento</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione o tipo de documento" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="rg">RG</SelectItem>
                      <SelectItem value="cpf">CPF</SelectItem>
                      <SelectItem value="comprovante_residencia">Comprovante de Residência</SelectItem>
                      <SelectItem value="procuracao">Procuração</SelectItem>
                      <SelectItem value="peticao">Petição</SelectItem>
                      <SelectItem value="decisao">Decisão Judicial</SelectItem>
                      <SelectItem value="contrato">Contrato</SelectItem>
                      <SelectItem value="outros">Outros</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="descricao"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Descrição (opcional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Descreva o conteúdo do documento..."
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Uma breve descrição para facilitar a identificação do documento.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {progress > 0 && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Progresso</span>
                  <span>{progress}%</span>
                </div>
                <Progress value={progress} className="h-2" />
              </div>
            )}

            <div className="flex justify-end gap-2 pt-2">
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isUploading}
              >
                Cancelar
              </Button>
              <Button
                type="submit"
                disabled={isUploading}
                className="gap-2"
              >
                {isUploading ? (
                  <>
                    <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full" />
                    Enviando...
                  </>
                ) : (
                  <>
                    <Check className="h-4 w-4" />
                    Enviar Documento
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      )}
    </div>
  );
}
