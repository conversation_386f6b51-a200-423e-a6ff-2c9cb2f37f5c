import { useState, useEffect } from 'react';
import './RichTextEditor.css';
import { useEditor, EditorContent, BubbleMenu, FloatingMenu } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Placeholder from '@tiptap/extension-placeholder';
import Image from '@tiptap/extension-image';
import Link from '@tiptap/extension-link';
import Table from '@tiptap/extension-table';
import TableRow from '@tiptap/extension-table-row';
import TableCell from '@tiptap/extension-table-cell';
import TableHeader from '@tiptap/extension-table-header';
import Underline from '@tiptap/extension-underline';
import TextAlign from '@tiptap/extension-text-align';
import Highlight from '@tiptap/extension-highlight';
import TaskList from '@tiptap/extension-task-list';
import TaskItem from '@tiptap/extension-task-item';
import { Color } from '@tiptap/extension-color';
import TextStyle from '@tiptap/extension-text-style';

import {
  Bold,
  Italic,
  Underline as UnderlineIcon,
  Strikethrough,
  List,
  ListOrdered,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  Heading1,
  Heading2,
  Heading3,
  Image as ImageIcon,
  Link as LinkIcon,
  Table as TableIcon,
  CheckSquare,
  Code,
  Quote,
  Undo,
  Redo,
  Highlighter,
  Palette,
  Trash2,
  Save,
  FileText,
  Type,
  Minus,
  MoreHorizontal,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';

interface RichTextEditorProps {
  initialContent?: string;
  placeholder?: string;
  onChange?: (html: string) => void;
  onSave?: (html: string) => void;
  readOnly?: boolean;
  className?: string;
  minHeight?: string;
}

export function RichTextEditor({
  initialContent = '',
  placeholder = 'Comece a escrever...',
  onChange,
  onSave,
  readOnly = false,
  className,
  minHeight = '300px',
}: RichTextEditorProps) {
  const [linkUrl, setLinkUrl] = useState('');
  const [imageUrl, setImageUrl] = useState('');
  const [showLinkMenu, setShowLinkMenu] = useState(false);
  const [showImageMenu, setShowImageMenu] = useState(false);

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: {
          levels: [1, 2, 3],
        },
        bulletList: {
          HTMLAttributes: {
            class: 'bullet-list',
          },
        },
        orderedList: {
          HTMLAttributes: {
            class: 'ordered-list',
          },
        },
      }),
      Placeholder.configure({
        placeholder,
        showOnlyWhenEditable: true,
        showOnlyCurrent: false,
        emptyEditorClass: 'is-editor-empty',
      }),
      Image,
      Link.configure({
        openOnClick: true,
      }),
      Table.configure({
        resizable: true,
      }),
      TableRow,
      TableCell,
      TableHeader,
      Underline,
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      Highlight,
      Color,
      TextStyle,
      TaskList,
      TaskItem.configure({
        nested: true,
      }),
    ],
    content: initialContent,
    editable: !readOnly,
    autofocus: !readOnly,
    injectCSS: false,
    enablePasteRules: true,
    enableInputRules: true,
    editorProps: {
      attributes: {
        class: 'prose-editor-content',
        spellcheck: 'false',
      },
      handleClick: (view, pos, event) => {
        // Permitir cliques em qualquer lugar para posicionar o cursor
        const { state } = view;
        const { doc } = state;

        // Se clicar abaixo do último nó, adicionar um parágrafo vazio
        if (pos >= doc.content.size) {
          const tr = view.state.tr.insert(
            doc.content.size,
            view.state.schema.nodes.paragraph.create()
          );
          view.dispatch(tr);
        }

        return false; // Continuar com o comportamento padrão
      },
    },
    onUpdate: ({ editor }) => {
      onChange?.(editor.getHTML());
    },
  });

  useEffect(() => {
    if (editor && initialContent && !editor.isEmpty) {
      editor.commands.setContent(initialContent);
    }
  }, [editor, initialContent]);

  const handleSave = () => {
    if (editor && onSave) {
      onSave(editor.getHTML());
    }
  };

  const addImage = () => {
    if (editor && imageUrl) {
      editor.chain().focus().setImage({ src: imageUrl }).run();
      setImageUrl('');
      setShowImageMenu(false);
    }
  };

  const setLink = () => {
    if (editor && linkUrl) {
      editor
        .chain()
        .focus()
        .extendMarkRange('link')
        .setLink({ href: linkUrl })
        .run();
      setLinkUrl('');
      setShowLinkMenu(false);
    }
  };

  const addTable = () => {
    if (editor) {
      editor
        .chain()
        .focus()
        .insertTable({ rows: 3, cols: 3, withHeaderRow: true })
        .run();
    }
  };

  if (!editor) {
    return null;
  }

  return (
    <div className={cn('border rounded-md', className)}>
      {!readOnly && (
        <div className="flex flex-wrap items-center gap-1 p-2 border-b bg-white dark:bg-neutral-900">
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => editor.chain().focus().toggleBold().run()}
              className={editor.isActive('bold') ? 'bg-muted/50' : ''}
            >
              <Bold className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => editor.chain().focus().toggleItalic().run()}
              className={editor.isActive('italic') ? 'bg-muted/50' : ''}
            >
              <Italic className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => editor.chain().focus().toggleUnderline().run()}
              className={editor.isActive('underline') ? 'bg-muted/50' : ''}
            >
              <UnderlineIcon className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => editor.chain().focus().toggleStrike().run()}
              className={editor.isActive('strike') ? 'bg-muted/50' : ''}
            >
              <Strikethrough className="h-4 w-4" />
            </Button>
          </div>

          <Separator orientation="vertical" className="h-6 mx-1" />

          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => editor.chain().focus().toggleBulletList().run()}
              className={editor.isActive('bulletList') ? 'bg-muted/50' : ''}
            >
              <List className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => editor.chain().focus().toggleOrderedList().run()}
              className={editor.isActive('orderedList') ? 'bg-muted/50' : ''}
            >
              <ListOrdered className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => editor.chain().focus().toggleTaskList().run()}
              className={editor.isActive('taskList') ? 'bg-muted/50' : ''}
            >
              <CheckSquare className="h-4 w-4" />
            </Button>
          </div>

          <Separator orientation="vertical" className="h-6 mx-1" />

          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => editor.chain().focus().setTextAlign('left').run()}
              className={editor.isActive({ textAlign: 'left' }) ? 'bg-muted/50' : ''}
            >
              <AlignLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => editor.chain().focus().setTextAlign('center').run()}
              className={editor.isActive({ textAlign: 'center' }) ? 'bg-muted/50' : ''}
            >
              <AlignCenter className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => editor.chain().focus().setTextAlign('right').run()}
              className={editor.isActive({ textAlign: 'right' }) ? 'bg-muted/50' : ''}
            >
              <AlignRight className="h-4 w-4" />
            </Button>
          </div>

          <Separator orientation="vertical" className="h-6 mx-1" />

          <div className="flex items-center gap-1">
            <Popover open={showLinkMenu} onOpenChange={setShowLinkMenu}>
              <PopoverTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className={editor.isActive('link') ? 'bg-muted/50' : ''}
                >
                  <LinkIcon className="h-4 w-4" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-80" align="start">
                <div className="flex flex-col gap-2">
                  <h4 className="font-medium">Adicionar link</h4>
                  <Input
                    placeholder="https://exemplo.com"
                    value={linkUrl}
                    onChange={(e) => setLinkUrl(e.target.value)}
                  />
                  <div className="flex justify-end gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowLinkMenu(false)}
                    >
                      Cancelar
                    </Button>
                    <Button size="sm" onClick={setLink}>
                      Salvar
                    </Button>
                  </div>
                </div>
              </PopoverContent>
            </Popover>

            <Popover open={showImageMenu} onOpenChange={setShowImageMenu}>
              <PopoverTrigger asChild>
                <Button variant="ghost" size="icon">
                  <ImageIcon className="h-4 w-4" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-80" align="start">
                <div className="flex flex-col gap-2">
                  <h4 className="font-medium">Adicionar imagem</h4>
                  <Input
                    placeholder="https://exemplo.com/imagem.jpg"
                    value={imageUrl}
                    onChange={(e) => setImageUrl(e.target.value)}
                  />
                  <div className="flex justify-end gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowImageMenu(false)}
                    >
                      Cancelar
                    </Button>
                    <Button size="sm" onClick={addImage}>
                      Adicionar
                    </Button>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          </div>

          <div className="flex-1"></div>

          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => editor.chain().focus().undo().run()}
              disabled={!editor.can().undo()}
            >
              <Undo className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => editor.chain().focus().redo().run()}
              disabled={!editor.can().redo()}
            >
              <Redo className="h-4 w-4" />
            </Button>
          </div>

          <div className="text-xs text-muted-foreground px-2">
            Digite <kbd className="px-1 py-0.5 bg-muted rounded border">/</kbd> para comandos
          </div>
        </div>
      )}

      {editor && (
        <BubbleMenu
          editor={editor}
          tippyOptions={{ duration: 0, maxWidth: 'none', placement: 'top' }}
          className="bg-white dark:bg-neutral-800 rounded-md shadow-lg border-0 p-1 flex flex-wrap gap-1"
          updateDelay={0}
          shouldShow={({ state, from, to }) => {
            // Verificar se há texto selecionado
            const { doc, selection } = state;
            const { empty } = selection;

            // Não mostrar se a seleção estiver vazia
            if (empty) return false;

            // Verificar se a seleção tem conteúdo válido
            return from !== to && from >= 0 && to <= doc.content.size;
          }}
        >
          <div className="flex items-center gap-1 p-1">
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 rounded-sm"
              onClick={() => editor.chain().focus().toggleBold().run()}
              data-active={editor.isActive('bold')}
            >
              <Bold className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 rounded-sm"
              onClick={() => editor.chain().focus().toggleItalic().run()}
              data-active={editor.isActive('italic')}
            >
              <Italic className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 rounded-sm"
              onClick={() => editor.chain().focus().toggleUnderline().run()}
              data-active={editor.isActive('underline')}
            >
              <UnderlineIcon className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 rounded-sm"
              onClick={() => editor.chain().focus().toggleStrike().run()}
              data-active={editor.isActive('strike')}
            >
              <Strikethrough className="h-4 w-4" />
            </Button>
            <Separator orientation="vertical" className="h-6 mx-1" />
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 rounded-sm"
              onClick={() => editor.chain().focus().toggleHighlight().run()}
              data-active={editor.isActive('highlight')}
            >
              <Highlighter className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 rounded-sm"
              onClick={() => editor.chain().focus().toggleCode().run()}
              data-active={editor.isActive('code')}
            >
              <Code className="h-4 w-4" />
            </Button>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 rounded-sm"
                >
                  <Palette className="h-4 w-4" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-2" align="start" side="top">
                <div className="flex flex-wrap gap-1 max-w-[220px]">
                  {[
                    '#000000', '#434343', '#666666', '#999999', '#b7b7b7', '#cccccc', '#d9d9d9', '#efefef', '#f3f3f3', '#ffffff',
                    '#980000', '#ff0000', '#ff9900', '#ffff00', '#00ff00', '#00ffff', '#4a86e8', '#0000ff', '#9900ff', '#ff00ff',
                    '#e6b8af', '#f4cccc', '#fce5cd', '#fff2cc', '#d9ead3', '#d0e0e3', '#c9daf8', '#cfe2f3', '#d9d2e9', '#ead1dc',
                  ].map((color) => (
                    <Button
                      key={color}
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0 rounded-sm"
                      style={{ backgroundColor: color }}
                      onClick={() => {
                        editor
                          .chain()
                          .focus()
                          .setColor(color)
                          .run();
                      }}
                    />
                  ))}
                </div>
              </PopoverContent>
            </Popover>
            <Separator orientation="vertical" className="h-6 mx-1" />
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 rounded-sm"
              onClick={() => setShowLinkMenu(true)}
              data-active={editor.isActive('link')}
            >
              <LinkIcon className="h-4 w-4" />
            </Button>
          </div>
        </BubbleMenu>
      )}

      {editor && (
        <FloatingMenu
          editor={editor}
          tippyOptions={{ duration: 0, placement: 'bottom-start' }}
          className="bg-white dark:bg-neutral-800 rounded-md shadow-lg border-0 p-2"
          shouldShow={({ editor }) => {
            // Obter o texto atual
            const { state } = editor;
            const { selection } = state;
            const { empty, $head } = selection;

            // Verificar se o cursor está em uma posição válida
            if (!empty || !$head.parent.isTextblock) {
              return false;
            }

            // Obter o texto antes do cursor
            const textBeforeCursor = $head.parent.textContent.slice(0, $head.parentOffset);

            // Verificar se o último caractere é '/'
            return textBeforeCursor.endsWith('/');
          }}
        >
          <div className="flex flex-col gap-1 min-w-[220px]">
            <div className="px-2 py-1 text-xs text-muted-foreground">Comandos básicos</div>
            <Button
              variant="ghost"
              size="sm"
              className="flex items-center gap-2 w-full justify-start h-9 px-2"
              onClick={() => {
                editor.chain().focus().deleteRange({ from: editor.state.selection.from - 1, to: editor.state.selection.from }).toggleHeading({ level: 1 }).run();
              }}
            >
              <Heading1 className="h-4 w-4" />
              <div className="flex flex-col items-start">
                <span>Título</span>
                <span className="text-xs text-muted-foreground">Título grande</span>
              </div>
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="flex items-center gap-2 w-full justify-start h-9 px-2"
              onClick={() => {
                editor.chain().focus().deleteRange({ from: editor.state.selection.from - 1, to: editor.state.selection.from }).toggleHeading({ level: 2 }).run();
              }}
            >
              <Heading2 className="h-4 w-4" />
              <div className="flex flex-col items-start">
                <span>Subtítulo</span>
                <span className="text-xs text-muted-foreground">Título médio</span>
              </div>
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="flex items-center gap-2 w-full justify-start h-9 px-2"
              onClick={() => {
                editor.chain().focus().deleteRange({ from: editor.state.selection.from - 1, to: editor.state.selection.from }).toggleBulletList().run();
              }}
            >
              <List className="h-4 w-4" />
              <div className="flex flex-col items-start">
                <span>Lista com marcadores</span>
                <span className="text-xs text-muted-foreground">Cria uma lista com pontos</span>
              </div>
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="flex items-center gap-2 w-full justify-start h-9 px-2"
              onClick={() => {
                editor.chain().focus().deleteRange({ from: editor.state.selection.from - 1, to: editor.state.selection.from }).toggleOrderedList().run();
              }}
            >
              <ListOrdered className="h-4 w-4" />
              <div className="flex flex-col items-start">
                <span>Lista numerada</span>
                <span className="text-xs text-muted-foreground">Cria uma lista com números</span>
              </div>
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="flex items-center gap-2 w-full justify-start h-9 px-2"
              onClick={() => {
                editor.chain().focus().deleteRange({ from: editor.state.selection.from - 1, to: editor.state.selection.from }).toggleTaskList().run();
              }}
            >
              <CheckSquare className="h-4 w-4" />
              <div className="flex flex-col items-start">
                <span>Lista de tarefas</span>
                <span className="text-xs text-muted-foreground">Cria uma lista de checagem</span>
              </div>
            </Button>
            <Separator className="my-1" />
            <Button
              variant="ghost"
              size="sm"
              className="flex items-center gap-2 w-full justify-start h-9 px-2"
              onClick={() => {
                editor.chain().focus().deleteRange({ from: editor.state.selection.from - 1, to: editor.state.selection.from }).toggleBlockquote().run();
              }}
            >
              <Quote className="h-4 w-4" />
              <div className="flex flex-col items-start">
                <span>Citação</span>
                <span className="text-xs text-muted-foreground">Destaca uma citação</span>
              </div>
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="flex items-center gap-2 w-full justify-start h-9 px-2"
              onClick={() => {
                editor.chain().focus().deleteRange({ from: editor.state.selection.from - 1, to: editor.state.selection.from }).toggleCodeBlock().run();
              }}
            >
              <Code className="h-4 w-4" />
              <div className="flex flex-col items-start">
                <span>Bloco de código</span>
                <span className="text-xs text-muted-foreground">Adiciona um bloco de código</span>
              </div>
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="flex items-center gap-2 w-full justify-start h-9 px-2"
              onClick={() => {
                editor.chain().focus().deleteRange({ from: editor.state.selection.from - 1, to: editor.state.selection.from }).setHorizontalRule().run();
              }}
            >
              <Minus className="h-4 w-4" />
              <div className="flex flex-col items-start">
                <span>Linha horizontal</span>
                <span className="text-xs text-muted-foreground">Adiciona uma linha divisora</span>
              </div>
            </Button>
          </div>
        </FloatingMenu>
      )}

      <div className="editor-container relative w-full h-full">
        <EditorContent
          editor={editor}
          className={cn('prose dark:prose-invert max-w-none p-4 focus:outline-none w-full', {
            'min-h-[300px]': !minHeight,
          })}
          style={{
            minHeight,
            height: '100%',
            caretColor: 'black',
            outline: 'none',
            border: 'none',
            boxShadow: 'none'
          }}
        />

      </div>
    </div>
  );
}
