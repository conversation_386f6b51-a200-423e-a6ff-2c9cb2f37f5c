import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, AlertCircle, CheckCircle, RefreshCw, Database, Copy } from 'lucide-react';
import { toast } from 'sonner';

interface SupabaseDiagnosticProps {
  compact?: boolean;
}

export function SupabaseDiagnostic({ compact = false }: SupabaseDiagnosticProps) {
  const [loading, setLoading] = useState(true);
  const [connectionStatus, setConnectionStatus] = useState<'checking' | 'connected' | 'error'>('checking');
  const [authStatus, setAuthStatus] = useState<'checking' | 'authenticated' | 'error'>('checking');
  const [tablesStatus, setTablesStatus] = useState<'checking' | 'ok' | 'error'>('checking');
  const [errorDetails, setErrorDetails] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  const checkConnection = async () => {
    setLoading(true);
    setConnectionStatus('checking');
    setAuthStatus('checking');
    setTablesStatus('checking');
    setErrorDetails(null);

    try {
      // 1. Verificar conexão básica e autenticação simultaneamente
      console.log('Verificando conexão e autenticação...');
      const [connectionResult, authResult] = await Promise.allSettled([
        // Use a simple table query to test connection
        supabase.from('profiles').select('count').limit(1),
        supabase.auth.getSession()
      ]);

      // Verificar resultado da conexão
      if (connectionResult.status === 'rejected' || (connectionResult.status === 'fulfilled' && connectionResult.value.error)) {
        const error = connectionResult.status === 'rejected'
          ? connectionResult.reason
          : connectionResult.value.error;
        console.error('Erro na conexão com Supabase:', error);
        setConnectionStatus('error');
        setErrorDetails(`Erro de conexão: ${error.message}`);
      } else {
        setConnectionStatus('connected');
        console.log('Conexão com Supabase estabelecida');
      }

      // Verificar resultado da autenticação
      if (authResult.status === 'rejected' || (authResult.status === 'fulfilled' && authResult.value.error)) {
        const error = authResult.status === 'rejected'
          ? authResult.reason
          : authResult.value.error;
        console.error('Erro na autenticação:', error);
        setAuthStatus('error');
        setErrorDetails(`Erro de autenticação: ${error.message}`);
      } else if (authResult.status === 'fulfilled' && !authResult.value.data.session) {
        console.warn('Usuário não autenticado');
        setAuthStatus('error');
        setErrorDetails('Usuário não autenticado. Faça login novamente.');
      } else {
        setAuthStatus('authenticated');
        console.log('Usuário autenticado:', authResult.value.data.session.user.email);
      }

      // 3. Verificar tabelas principais simultaneamente
      if (connectionStatus !== 'error' && authStatus !== 'error') {
        console.log('Verificando tabelas...');
        const [precatoriosResult, clientesResult] = await Promise.allSettled([
          supabase.from('precatorios').select('count').limit(1),
          supabase.from('clientes').select('count').limit(1)
        ]);

        // Verificar resultado das tabelas
        if (
          (precatoriosResult.status === 'rejected' || (precatoriosResult.status === 'fulfilled' && precatoriosResult.value.error)) ||
          (clientesResult.status === 'rejected' || (clientesResult.status === 'fulfilled' && clientesResult.value.error))
        ) {
          let errorMessage = '';

          if (precatoriosResult.status === 'rejected' || (precatoriosResult.status === 'fulfilled' && precatoriosResult.value.error)) {
            const error = precatoriosResult.status === 'rejected'
              ? precatoriosResult.reason
              : precatoriosResult.value.error;
            errorMessage = `Erro ao acessar tabela de precatórios: ${error.message}`;
          } else if (clientesResult.status === 'rejected' || (clientesResult.status === 'fulfilled' && clientesResult.value.error)) {
            const error = clientesResult.status === 'rejected'
              ? clientesResult.reason
              : clientesResult.value.error;
            errorMessage = `Erro ao acessar tabela de clientes: ${error.message}`;
          }

          console.error('Erro ao acessar tabelas:', errorMessage);
          setTablesStatus('error');
          setErrorDetails(errorMessage);
        } else {
          setTablesStatus('ok');
          console.log('Tabelas verificadas com sucesso');
        }
      }
    } catch (error) {
      console.error('Erro durante diagnóstico:', error);
      setConnectionStatus('error');
      setErrorDetails(`Erro não tratado: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = () => {
    setRefreshing(true);
    checkConnection();
  };

  const handleCopyError = async () => {
    if (!errorDetails) return;

    try {
      await navigator.clipboard.writeText(errorDetails);
      toast.success('Mensagem de erro copiada para a área de transferência');
    } catch (error) {
      // Fallback para navegadores que não suportam clipboard API
      const textArea = document.createElement('textarea');
      textArea.value = errorDetails;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      toast.success('Mensagem de erro copiada para a área de transferência');
    }
  };

  const handleReset = async () => {
    try {
      setRefreshing(true);

      // Limpar tokens de autenticação
      await supabase.auth.signOut({ scope: 'local' });

      // Limpar localStorage
      localStorage.removeItem('sb-ubwzukpsqcrwzfbppoux-auth-token');

      toast.success('Autenticação resetada. Redirecionando para login...');

      // Redirecionar para a página de login após 2 segundos
      setTimeout(() => {
        window.location.href = '/login';
      }, 2000);

    } catch (error) {
      console.error('Erro ao resetar autenticação:', error);
      toast.error('Erro ao resetar autenticação');
      setRefreshing(false);
    }
  };

  useEffect(() => {
    checkConnection();
  }, []);

  if (compact) {
    return (
      <Card className="w-full">
        <CardHeader className="pb-2">
          <CardTitle className="text-lg flex items-center gap-2">
            <Database className="h-5 w-5 text-primary" />
            Diagnóstico de Conexão
            {loading && <Loader2 className="h-4 w-4 animate-spin ml-2" />}
          </CardTitle>
          <CardDescription>
            Verificação rápida da conexão com o banco de dados
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-2 pt-0">
          <div className="flex flex-col space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm">Conexão:</span>
              <div className="flex items-center">
                {connectionStatus === 'checking' && <Loader2 className="h-3 w-3 animate-spin text-muted-foreground" />}
                {connectionStatus === 'connected' && <CheckCircle className="h-3 w-3 text-green-500" />}
                {connectionStatus === 'error' && <AlertCircle className="h-3 w-3 text-red-500" />}
                <span className="ml-1 text-xs">
                  {connectionStatus === 'checking' && 'Verificando...'}
                  {connectionStatus === 'connected' && 'Conectado'}
                  {connectionStatus === 'error' && 'Erro'}
                </span>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-sm">Autenticação:</span>
              <div className="flex items-center">
                {authStatus === 'checking' && <Loader2 className="h-3 w-3 animate-spin text-muted-foreground" />}
                {authStatus === 'authenticated' && <CheckCircle className="h-3 w-3 text-green-500" />}
                {authStatus === 'error' && <AlertCircle className="h-3 w-3 text-red-500" />}
                <span className="ml-1 text-xs">
                  {authStatus === 'checking' && 'Verificando...'}
                  {authStatus === 'authenticated' && 'Autenticado'}
                  {authStatus === 'error' && 'Erro'}
                </span>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-sm">Tabelas:</span>
              <div className="flex items-center">
                {tablesStatus === 'checking' && <Loader2 className="h-3 w-3 animate-spin text-muted-foreground" />}
                {tablesStatus === 'ok' && <CheckCircle className="h-3 w-3 text-green-500" />}
                {tablesStatus === 'error' && <AlertCircle className="h-3 w-3 text-red-500" />}
                <span className="ml-1 text-xs">
                  {tablesStatus === 'checking' && 'Verificando...'}
                  {tablesStatus === 'ok' && 'OK'}
                  {tablesStatus === 'error' && 'Erro'}
                </span>
              </div>
            </div>
          </div>

          {errorDetails && (
            <div className="mt-2 p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md text-xs text-red-800 dark:text-red-300">
              <div className="flex items-start justify-between gap-2">
                <p className="flex-1">{errorDetails}</p>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleCopyError}
                  className="h-6 w-6 p-0 text-red-600 hover:text-red-700 hover:bg-red-100 dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-800/30"
                  title="Copiar mensagem de erro"
                >
                  <Copy className="h-3 w-3" />
                </Button>
              </div>
            </div>
          )}
        </CardContent>
        <CardFooter className="pt-0 flex justify-between">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={refreshing}
            className="text-xs"
          >
            {refreshing ? (
              <Loader2 className="h-3 w-3 mr-1 animate-spin" />
            ) : (
              <RefreshCw className="h-3 w-3 mr-1" />
            )}
            Atualizar
          </Button>
          <Button
            variant="destructive"
            size="sm"
            onClick={handleReset}
            disabled={refreshing}
            className="text-xs"
          >
            Resetar Autenticação
          </Button>
        </CardFooter>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          Diagnóstico do Supabase
          {loading && <Loader2 className="h-4 w-4 animate-spin" />}
        </CardTitle>
        <CardDescription>
          Verificando a conexão com o banco de dados
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <span>Conexão com Supabase:</span>
          <div className="flex items-center">
            {connectionStatus === 'checking' && <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />}
            {connectionStatus === 'connected' && <CheckCircle className="h-4 w-4 text-green-500" />}
            {connectionStatus === 'error' && <AlertCircle className="h-4 w-4 text-red-500" />}
            <span className="ml-2">
              {connectionStatus === 'checking' && 'Verificando...'}
              {connectionStatus === 'connected' && 'Conectado'}
              {connectionStatus === 'error' && 'Erro'}
            </span>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <span>Autenticação:</span>
          <div className="flex items-center">
            {authStatus === 'checking' && <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />}
            {authStatus === 'authenticated' && <CheckCircle className="h-4 w-4 text-green-500" />}
            {authStatus === 'error' && <AlertCircle className="h-4 w-4 text-red-500" />}
            <span className="ml-2">
              {authStatus === 'checking' && 'Verificando...'}
              {authStatus === 'authenticated' && 'Autenticado'}
              {authStatus === 'error' && 'Erro'}
            </span>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <span>Tabelas:</span>
          <div className="flex items-center">
            {tablesStatus === 'checking' && <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />}
            {tablesStatus === 'ok' && <CheckCircle className="h-4 w-4 text-green-500" />}
            {tablesStatus === 'error' && <AlertCircle className="h-4 w-4 text-red-500" />}
            <span className="ml-2">
              {tablesStatus === 'checking' && 'Verificando...'}
              {tablesStatus === 'ok' && 'OK'}
              {tablesStatus === 'error' && 'Erro'}
            </span>
          </div>
        </div>

        {errorDetails && (
          <div className="mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md text-sm text-red-800 dark:text-red-300">
            <div className="flex items-start justify-between gap-2 mb-2">
              <p className="font-medium">Detalhes do erro:</p>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCopyError}
                className="h-7 w-7 p-0 text-red-600 hover:text-red-700 hover:bg-red-100 dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-800/30"
                title="Copiar mensagem de erro"
              >
                <Copy className="h-4 w-4" />
              </Button>
            </div>
            <p className="break-words">{errorDetails}</p>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button
          variant="outline"
          onClick={handleRefresh}
          disabled={refreshing}
        >
          {refreshing ? (
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <RefreshCw className="h-4 w-4 mr-2" />
          )}
          Atualizar
        </Button>
        <Button
          variant="destructive"
          onClick={handleReset}
          disabled={refreshing}
        >
          Resetar Autenticação
        </Button>
      </CardFooter>
    </Card>
  );
}
