import React, { useState, useEffect } from 'react';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle,
  CardDescription,
  CardFooter 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { Tabs, Ta<PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Plus, 
  UserCog, 
  Shield, 
  Check, 
  X,
  Save,
  RefreshCw,
  AlertCircle
} from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { 
  fetchAllRoles, 
  createRole, 
  updateRole, 
  deleteRole, 
  getRoleDefaultPermissions,
  updateRoleDefaultPermission,
  CustomRole,
  RolePermission
} from '@/services/rolesService';

interface RoleManagerProps {
  onRolesUpdated?: () => void;
}

interface ResourcePermission {
  resourceType: string;
  resourceName: string;
  actions: {
    action: string;
    label: string;
    allowed: boolean;
  }[];
}

const defaultColors = [
  "#3b82f6", // blue-500
  "#10b981", // emerald-500
  "#f59e0b", // amber-500
  "#ef4444", // red-500
  "#8b5cf6", // violet-500
  "#ec4899", // pink-500
  "#06b6d4", // cyan-500
  "#f97316", // orange-500
];

export function RoleManager({ onRolesUpdated }: RoleManagerProps) {
  const { toast } = useToast();
  const [roles, setRoles] = useState<CustomRole[]>([]);
  const [loading, setLoading] = useState(true);
  const [isAddRoleOpen, setIsAddRoleOpen] = useState(false);
  const [isEditRoleOpen, setIsEditRoleOpen] = useState(false);
  const [isDeleteRoleOpen, setIsDeleteRoleOpen] = useState(false);
  const [isPermissionsOpen, setIsPermissionsOpen] = useState(false);
  const [selectedRole, setSelectedRole] = useState<CustomRole | null>(null);
  const [selectedColor, setSelectedColor] = useState(defaultColors[0]);
  const [resourcePermissions, setResourcePermissions] = useState<ResourcePermission[]>([]);
  const [savingPermissions, setSavingPermissions] = useState(false);
  
  const [newRole, setNewRole] = useState<{
    nome: string;
    descricao: string;
    cor: string;
    icone?: string;
  }>({
    nome: "",
    descricao: "",
    cor: defaultColors[0],
    icone: "UserCog"
  });

  // Carregar lista de cargos
  const loadRoles = async () => {
    try {
      setLoading(true);
      const rolesData = await fetchAllRoles();
      setRoles(rolesData);
    } catch (error) {
      console.error("Erro ao carregar cargos:", error);
      toast({
        title: "Erro ao carregar cargos",
        description: "Não foi possível carregar a lista de cargos. Tente novamente mais tarde.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  // Carregar permissões de um cargo
  const loadRolePermissions = async (roleId: string) => {
    try {
      setSavingPermissions(true);
      
      // Buscar permissões do cargo
      const permissions = await getRoleDefaultPermissions(roleId);
      
      // Configurar recursos e permissões
      const resources: ResourcePermission[] = [
        {
          resourceType: 'cliente',
          resourceName: 'Clientes',
          actions: [
            { action: 'view', label: 'Visualizar', allowed: false },
            { action: 'create', label: 'Criar', allowed: false },
            { action: 'edit', label: 'Editar', allowed: false },
            { action: 'delete', label: 'Excluir', allowed: false }
          ]
        },
        {
          resourceType: 'precatorio',
          resourceName: 'Precatórios',
          actions: [
            { action: 'view', label: 'Visualizar', allowed: false },
            { action: 'create', label: 'Criar', allowed: false },
            { action: 'edit', label: 'Editar', allowed: false },
            { action: 'delete', label: 'Excluir', allowed: false }
          ]
        },
        {
          resourceType: 'rpv',
          resourceName: 'RPVs',
          actions: [
            { action: 'view', label: 'Visualizar', allowed: false },
            { action: 'create', label: 'Criar', allowed: false },
            { action: 'edit', label: 'Editar', allowed: false },
            { action: 'delete', label: 'Excluir', allowed: false }
          ]
        },
        {
          resourceType: 'tarefa',
          resourceName: 'Tarefas',
          actions: [
            { action: 'view', label: 'Visualizar', allowed: false },
            { action: 'create', label: 'Criar', allowed: false },
            { action: 'edit', label: 'Editar', allowed: false },
            { action: 'delete', label: 'Excluir', allowed: false },
            { action: 'view_all', label: 'Ver Todas', allowed: false }
          ]
        },
        {
          resourceType: 'documento',
          resourceName: 'Documentos',
          actions: [
            { action: 'view', label: 'Visualizar', allowed: false },
            { action: 'create', label: 'Criar', allowed: false },
            { action: 'edit', label: 'Editar', allowed: false },
            { action: 'delete', label: 'Excluir', allowed: false }
          ]
        },
        {
          resourceType: 'relatorio',
          resourceName: 'Relatórios',
          actions: [
            { action: 'view_precatorio', label: 'Ver Precatórios', allowed: false },
            { action: 'view_rpv', label: 'Ver RPVs', allowed: false },
            { action: 'view_captacao', label: 'Ver Captação', allowed: false },
            { action: 'view_completo', label: 'Ver Completo', allowed: false }
          ]
        },
        {
          resourceType: 'user',
          resourceName: 'Usuários',
          actions: [
            { action: 'view', label: 'Visualizar', allowed: false },
            { action: 'create', label: 'Criar', allowed: false },
            { action: 'edit', label: 'Editar', allowed: false },
            { action: 'delete', label: 'Excluir', allowed: false }
          ]
        },
        {
          resourceType: 'system',
          resourceName: 'Sistema',
          actions: [
            { action: 'configure', label: 'Configurar', allowed: false },
            { action: 'manage_roles', label: 'Gerenciar Cargos', allowed: false }
          ]
        }
      ];
      
      // Marcar permissões existentes
      permissions.forEach(permission => {
        const resource = resources.find(r => r.resourceType === permission.resource_type);
        if (resource) {
          const action = resource.actions.find(a => a.action === permission.action);
          if (action) {
            action.allowed = permission.allowed;
          }
        }
      });
      
      setResourcePermissions(resources);
    } catch (error) {
      console.error("Erro ao carregar permissões do cargo:", error);
      toast({
        title: "Erro ao carregar permissões",
        description: "Não foi possível carregar as permissões do cargo. Tente novamente mais tarde.",
        variant: "destructive"
      });
    } finally {
      setSavingPermissions(false);
    }
  };

  // Carregar cargos ao montar o componente
  useEffect(() => {
    loadRoles();
  }, []);

  // Criar novo cargo
  const handleCreateRole = async () => {
    try {
      if (!newRole.nome.trim()) {
        toast({
          title: "Nome obrigatório",
          description: "O nome do cargo é obrigatório.",
          variant: "destructive"
        });
        return;
      }

      const roleData = {
        nome: newRole.nome.trim(),
        descricao: newRole.descricao.trim(),
        cor: newRole.cor,
        icone: newRole.icone,
        is_system: false
      };

      await createRole(roleData);
      
      toast({
        title: "Cargo criado",
        description: `O cargo "${roleData.nome}" foi criado com sucesso.`
      });
      
      setIsAddRoleOpen(false);
      setNewRole({
        nome: "",
        descricao: "",
        cor: defaultColors[0],
        icone: "UserCog"
      });
      
      loadRoles();
      
      if (onRolesUpdated) {
        onRolesUpdated();
      }
    } catch (error) {
      console.error("Erro ao criar cargo:", error);
      toast({
        title: "Erro ao criar cargo",
        description: "Não foi possível criar o cargo. Tente novamente mais tarde.",
        variant: "destructive"
      });
    }
  };

  // Atualizar cargo existente
  const handleUpdateRole = async () => {
    try {
      if (!selectedRole) return;
      
      if (!selectedRole.nome.trim()) {
        toast({
          title: "Nome obrigatório",
          description: "O nome do cargo é obrigatório.",
          variant: "destructive"
        });
        return;
      }

      const roleData = {
        nome: selectedRole.nome.trim(),
        descricao: selectedRole.descricao?.trim(),
        cor: selectedRole.cor
      };

      await updateRole(selectedRole.id, roleData);
      
      toast({
        title: "Cargo atualizado",
        description: `O cargo "${roleData.nome}" foi atualizado com sucesso.`
      });
      
      setIsEditRoleOpen(false);
      loadRoles();
      
      if (onRolesUpdated) {
        onRolesUpdated();
      }
    } catch (error) {
      console.error("Erro ao atualizar cargo:", error);
      toast({
        title: "Erro ao atualizar cargo",
        description: "Não foi possível atualizar o cargo. Tente novamente mais tarde.",
        variant: "destructive"
      });
    }
  };

  // Excluir cargo
  const handleDeleteRole = async () => {
    try {
      if (!selectedRole) return;
      
      if (selectedRole.is_system) {
        toast({
          title: "Operação não permitida",
          description: "Não é possível excluir cargos do sistema.",
          variant: "destructive"
        });
        return;
      }

      await deleteRole(selectedRole.id);
      
      toast({
        title: "Cargo excluído",
        description: `O cargo "${selectedRole.nome}" foi excluído com sucesso.`
      });
      
      setIsDeleteRoleOpen(false);
      loadRoles();
      
      if (onRolesUpdated) {
        onRolesUpdated();
      }
    } catch (error) {
      console.error("Erro ao excluir cargo:", error);
      toast({
        title: "Erro ao excluir cargo",
        description: "Não foi possível excluir o cargo. Tente novamente mais tarde.",
        variant: "destructive"
      });
    }
  };

  // Abrir modal de edição
  const handleOpenEditRole = (role: CustomRole) => {
    setSelectedRole(role);
    setIsEditRoleOpen(true);
  };

  // Abrir modal de exclusão
  const handleOpenDeleteRole = (role: CustomRole) => {
    setSelectedRole(role);
    setIsDeleteRoleOpen(true);
  };

  // Abrir modal de permissões
  const handleOpenPermissions = (role: CustomRole) => {
    setSelectedRole(role);
    loadRolePermissions(role.id);
    setIsPermissionsOpen(true);
  };

  // Atualizar permissão de recurso
  const handleResourcePermissionChange = (resourceType: string, action: string, allowed: boolean) => {
    setResourcePermissions(prev => 
      prev.map(resource => 
        resource.resourceType === resourceType
          ? {
              ...resource,
              actions: resource.actions.map(a => 
                a.action === action ? { ...a, allowed } : a
              )
            }
          : resource
      )
    );
  };

  // Salvar permissões
  const handleSavePermissions = async () => {
    try {
      if (!selectedRole) return;
      
      setSavingPermissions(true);
      
      // Salvar permissões de recursos
      for (const resource of resourcePermissions) {
        for (const action of resource.actions) {
          await updateRoleDefaultPermission(
            selectedRole.id,
            resource.resourceType,
            action.action,
            action.allowed
          );
        }
      }
      
      toast({
        title: "Permissões atualizadas",
        description: `As permissões do cargo "${selectedRole.nome}" foram atualizadas com sucesso.`
      });
      
      setIsPermissionsOpen(false);
      
      if (onRolesUpdated) {
        onRolesUpdated();
      }
    } catch (error) {
      console.error("Erro ao salvar permissões:", error);
      toast({
        title: "Erro ao salvar permissões",
        description: "Não foi possível salvar as permissões do cargo. Tente novamente mais tarde.",
        variant: "destructive"
      });
    } finally {
      setSavingPermissions(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Gerenciamento de Cargos</h2>
          <p className="text-muted-foreground">
            Crie e gerencie cargos e suas permissões padrão
          </p>
        </div>
        
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => loadRoles()}
            disabled={loading}
          >
            {loading ? (
              <RefreshCw className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Atualizar
          </Button>
          
          <Button 
            onClick={() => setIsAddRoleOpen(true)}
            size="sm"
          >
            <Plus className="h-4 w-4 mr-2" />
            Novo Cargo
          </Button>
        </div>
      </div>
      
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Nome</TableHead>
                <TableHead>Descrição</TableHead>
                <TableHead>Tipo</TableHead>
                <TableHead className="w-[100px]">Ações</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={4} className="text-center py-8">
                    <div className="flex justify-center items-center">
                      <RefreshCw className="h-6 w-6 animate-spin text-primary" />
                      <span className="ml-2">Carregando cargos...</span>
                    </div>
                  </TableCell>
                </TableRow>
              ) : roles.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={4} className="text-center py-8">
                    <div className="flex flex-col items-center justify-center text-muted-foreground">
                      <UserCog className="h-10 w-10 mb-2" />
                      <p>Nenhum cargo encontrado</p>
                      <Button 
                        variant="link" 
                        onClick={() => setIsAddRoleOpen(true)}
                        className="mt-2"
                      >
                        Criar novo cargo
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                roles.map((role) => (
                  <TableRow key={role.id}>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <div 
                          className="w-3 h-3 rounded-full" 
                          style={{ backgroundColor: role.cor }}
                        />
                        <span className="font-medium">{role.nome}</span>
                      </div>
                    </TableCell>
                    <TableCell className="text-muted-foreground">
                      {role.descricao || "Sem descrição"}
                    </TableCell>
                    <TableCell>
                      {role.is_system ? (
                        <Badge variant="secondary">Sistema</Badge>
                      ) : (
                        <Badge variant="outline">Personalizado</Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                            <span className="sr-only">Abrir menu</span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleOpenPermissions(role)}>
                            <Shield className="h-4 w-4 mr-2" />
                            Permissões
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleOpenEditRole(role)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Editar
                          </DropdownMenuItem>
                          {!role.is_system && (
                            <>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem 
                                onClick={() => handleOpenDeleteRole(role)}
                                className="text-destructive focus:text-destructive"
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Excluir
                              </DropdownMenuItem>
                            </>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
      
      {/* Modal de adicionar cargo */}
      <Dialog open={isAddRoleOpen} onOpenChange={setIsAddRoleOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Novo Cargo</DialogTitle>
            <DialogDescription>
              Crie um novo cargo e defina suas permissões padrão.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <Label htmlFor="nome">Nome do Cargo *</Label>
              <Input 
                id="nome" 
                value={newRole.nome} 
                onChange={(e) => setNewRole(prev => ({ ...prev, nome: e.target.value }))}
                placeholder="Ex: Gerente de Vendas"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="descricao">Descrição</Label>
              <Textarea 
                id="descricao" 
                value={newRole.descricao} 
                onChange={(e) => setNewRole(prev => ({ ...prev, descricao: e.target.value }))}
                placeholder="Descreva as responsabilidades deste cargo"
                rows={3}
              />
            </div>
            
            <div className="space-y-2">
              <Label>Cor</Label>
              <div className="flex flex-wrap gap-2">
                {defaultColors.map((color) => (
                  <button
                    key={color}
                    type="button"
                    className={`w-8 h-8 rounded-full transition-all ${
                      newRole.cor === color ? 'ring-2 ring-offset-2 ring-primary' : ''
                    }`}
                    style={{ backgroundColor: color }}
                    onClick={() => setNewRole(prev => ({ ...prev, cor: color }))}
                  />
                ))}
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddRoleOpen(false)}>
              Cancelar
            </Button>
            <Button onClick={handleCreateRole}>
              Criar Cargo
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Modal de editar cargo */}
      <Dialog open={isEditRoleOpen} onOpenChange={setIsEditRoleOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Editar Cargo</DialogTitle>
            <DialogDescription>
              Atualize as informações do cargo.
            </DialogDescription>
          </DialogHeader>
          
          {selectedRole && (
            <div className="space-y-4 py-2">
              <div className="space-y-2">
                <Label htmlFor="edit-nome">Nome do Cargo *</Label>
                <Input 
                  id="edit-nome" 
                  value={selectedRole.nome} 
                  onChange={(e) => setSelectedRole(prev => prev ? { ...prev, nome: e.target.value } : null)}
                  placeholder="Ex: Gerente de Vendas"
                  disabled={selectedRole.is_system}
                />
                {selectedRole.is_system && (
                  <p className="text-xs text-muted-foreground">
                    Cargos do sistema não podem ter o nome alterado.
                  </p>
                )}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="edit-descricao">Descrição</Label>
                <Textarea 
                  id="edit-descricao" 
                  value={selectedRole.descricao || ''} 
                  onChange={(e) => setSelectedRole(prev => prev ? { ...prev, descricao: e.target.value } : null)}
                  placeholder="Descreva as responsabilidades deste cargo"
                  rows={3}
                />
              </div>
              
              <div className="space-y-2">
                <Label>Cor</Label>
                <div className="flex flex-wrap gap-2">
                  {defaultColors.map((color) => (
                    <button
                      key={color}
                      type="button"
                      className={`w-8 h-8 rounded-full transition-all ${
                        selectedRole.cor === color ? 'ring-2 ring-offset-2 ring-primary' : ''
                      }`}
                      style={{ backgroundColor: color }}
                      onClick={() => setSelectedRole(prev => prev ? { ...prev, cor: color } : null)}
                    />
                  ))}
                </div>
              </div>
            </div>
          )}
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditRoleOpen(false)}>
              Cancelar
            </Button>
            <Button onClick={handleUpdateRole}>
              Salvar Alterações
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Modal de excluir cargo */}
      <Dialog open={isDeleteRoleOpen} onOpenChange={setIsDeleteRoleOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Excluir Cargo</DialogTitle>
            <DialogDescription>
              Tem certeza que deseja excluir este cargo? Esta ação não pode ser desfeita.
            </DialogDescription>
          </DialogHeader>
          
          {selectedRole && (
            <div className="py-4">
              <div className="flex items-center p-4 border rounded-lg bg-muted/50">
                <div 
                  className="w-4 h-4 rounded-full mr-3" 
                  style={{ backgroundColor: selectedRole.cor }}
                />
                <div>
                  <p className="font-medium">{selectedRole.nome}</p>
                  <p className="text-sm text-muted-foreground">
                    {selectedRole.descricao || "Sem descrição"}
                  </p>
                </div>
              </div>
              
              {selectedRole.is_system && (
                <div className="mt-4 p-4 border rounded-lg bg-destructive/10 text-destructive flex items-start">
                  <AlertCircle className="h-5 w-5 mr-2 mt-0.5 flex-shrink-0" />
                  <p className="text-sm">
                    Não é possível excluir cargos do sistema. Estes cargos são necessários para o funcionamento correto da aplicação.
                  </p>
                </div>
              )}
            </div>
          )}
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteRoleOpen(false)}>
              Cancelar
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleDeleteRole}
              disabled={selectedRole?.is_system}
            >
              Excluir Cargo
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Modal de permissões */}
      <Dialog open={isPermissionsOpen} onOpenChange={setIsPermissionsOpen}>
        <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Permissões do Cargo</DialogTitle>
            <DialogDescription>
              Configure as permissões padrão para o cargo {selectedRole?.nome}.
              Estas permissões serão aplicadas automaticamente a novos usuários com este cargo.
            </DialogDescription>
          </DialogHeader>
          
          {selectedRole && (
            <div className="py-2">
              <div className="flex items-center mb-4">
                <div 
                  className="w-4 h-4 rounded-full mr-3" 
                  style={{ backgroundColor: selectedRole.cor }}
                />
                <div>
                  <p className="font-medium">{selectedRole.nome}</p>
                  <p className="text-sm text-muted-foreground">
                    {selectedRole.descricao || "Sem descrição"}
                  </p>
                </div>
                
                {selectedRole.is_system && (
                  <Badge variant="secondary" className="ml-auto">
                    Cargo do Sistema
                  </Badge>
                )}
              </div>
              
              <Separator className="my-4" />
              
              <ScrollArea className="h-[400px] pr-4">
                <div className="space-y-6">
                  {resourcePermissions.map((resource) => (
                    <div key={resource.resourceType} className="space-y-2">
                      <h3 className="font-medium text-lg">{resource.resourceName}</h3>
                      <div className="space-y-2">
                        {resource.actions.map((action) => (
                          <div 
                            key={`${resource.resourceType}-${action.action}`}
                            className="flex items-center justify-between py-2 px-4 rounded-md bg-muted/50"
                          >
                            <span>{action.label}</span>
                            <Switch
                              checked={action.allowed}
                              onCheckedChange={(checked) => 
                                handleResourcePermissionChange(resource.resourceType, action.action, checked)
                              }
                            />
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </div>
          )}
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsPermissionsOpen(false)}>
              Cancelar
            </Button>
            <Button 
              onClick={handleSavePermissions}
              disabled={savingPermissions}
            >
              {savingPermissions ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Salvando...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Salvar Permissões
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
