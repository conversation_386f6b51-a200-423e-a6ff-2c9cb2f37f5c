import { useEffect, useState, useRef } from 'react';
import { initSessionPersistence, checkAndPersistSession, clearSessionData } from '@/lib/sessionPersistence';
import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';
import { useNavigate } from 'react-router-dom';

/**
 * Componente que gerencia a persistência de sessão
 * Este componente deve ser montado no nível raiz da aplicação
 */
export function SessionPersistenceHandler() {
  const navigate = useNavigate();
  const [isInitialized, setIsInitialized] = useState(false);
  const [isRecovering, setIsRecovering] = useState(false);
  const recoveryAttemptRef = useRef(0);
  const MAX_RECOVERY_ATTEMPTS = 3;

  // Inicializar o sistema de persistência de sessão
  useEffect(() => {
    if (!isInitialized) {
      console.log('[SessionHandler] Inicializando sistema de persistência de sessão');
      initSessionPersistence();
      setIsInitialized(true);
    }
  }, [isInitialized]);

  // Função para forçar o redirecionamento para a página de login
  const forceRedirectToLogin = (reason: string) => {
    console.log(`[SessionHandler] Forçando redirecionamento para login: ${reason}`);
    
    // Limpar dados de sessão
    clearSessionData();
    
    // Limpar outros dados de autenticação
    try {
      localStorage.removeItem("userProfile");
      sessionStorage.removeItem("userProfile");
      localStorage.removeItem("sb-ubwzukpsqcrwzfbppoux-auth-token");
      sessionStorage.removeItem("sb-ubwzukpsqcrwzfbppoux-auth-token");
      localStorage.removeItem("sb-ubwzukpsqcrwzfbppoux-auth-backup");
      sessionStorage.removeItem("sb-ubwzukpsqcrwzfbppoux-auth-backup");
      localStorage.removeItem("sb-ubwzukpsqcrwzfbppoux-last-refresh");
    } catch (error) {
      console.error("[SessionHandler] Erro ao limpar dados de autenticação:", error);
    }
    
    // Salvar a URL atual para retornar após o login
    try {
      if (window.location.pathname !== '/login' && 
          window.location.pathname !== '/register' && 
          window.location.pathname !== '/forgot-password') {
        sessionStorage.setItem('last_url', window.location.pathname);
      }
    } catch (e) {
      console.error("[SessionHandler] Erro ao salvar URL atual:", e);
    }
    
    // Redirecionar para login
    window.location.href = `/login?forced=true&reason=${encodeURIComponent(reason)}`;
  };

  // Função para recuperar a sessão
  const recoverSession = async () => {
    if (isRecovering) {
      console.log('[SessionHandler] Já está recuperando a sessão');
      return false;
    }
    
    setIsRecovering(true);
    
    try {
      console.log('[SessionHandler] Tentando recuperar sessão');
      
      // Incrementar contador de tentativas
      recoveryAttemptRef.current++;
      
      // Verificar se excedeu o número máximo de tentativas
      if (recoveryAttemptRef.current > MAX_RECOVERY_ATTEMPTS) {
        console.error('[SessionHandler] Número máximo de tentativas excedido');
        forceRedirectToLogin('Número máximo de tentativas de recuperação excedido');
        return false;
      }
      
      // Tentar verificar e persistir a sessão
      const success = await checkAndPersistSession();
      
      if (success) {
        console.log('[SessionHandler] Sessão recuperada com sucesso');
        
        // Resetar contador de tentativas
        recoveryAttemptRef.current = 0;
        
        // Mostrar toast de sucesso
        toast.success('Conexão restabelecida', {
          description: 'Sua sessão foi recuperada com sucesso'
        });
        
        // Disparar evento de reconexão
        const reconnectEvent = new CustomEvent('app-reconnected', {
          detail: {
            timestamp: Date.now(),
            source: 'session-persistence'
          }
        });
        document.dispatchEvent(reconnectEvent);
        
        return true;
      }
      
      console.warn('[SessionHandler] Falha ao recuperar sessão');
      
      // Se ainda temos tentativas, mostrar toast de aviso
      if (recoveryAttemptRef.current < MAX_RECOVERY_ATTEMPTS) {
        toast.warning('Verificando conexão', {
          description: 'Tentando reconectar ao servidor...'
        });
        
        // Tentar novamente após um atraso
        setTimeout(() => {
          setIsRecovering(false);
          recoverSession();
        }, 2000);
      } else {
        // Se excedeu o número máximo de tentativas, redirecionar para login
        forceRedirectToLogin('Falha ao recuperar sessão após várias tentativas');
      }
      
      return false;
    } catch (error) {
      console.error('[SessionHandler] Erro ao recuperar sessão:', error);
      
      // Se excedeu o número máximo de tentativas, redirecionar para login
      if (recoveryAttemptRef.current >= MAX_RECOVERY_ATTEMPTS) {
        forceRedirectToLogin('Erro ao recuperar sessão');
      } else {
        // Tentar novamente após um atraso
        setTimeout(() => {
          setIsRecovering(false);
          recoverSession();
        }, 2000);
      }
      
      return false;
    } finally {
      setIsRecovering(false);
    }
  };

  // Monitorar eventos de visibilidade
  useEffect(() => {
    const handleVisibilityChange = async () => {
      if (document.visibilityState === 'visible') {
        console.log('[SessionHandler] Página voltou a ficar visível, verificando sessão');
        
        // Verificar se temos uma sessão válida
        const { data, error } = await supabase.auth.getSession();
        
        if (error || !data.session) {
          console.warn('[SessionHandler] Sessão inválida após retorno de visibilidade, tentando recuperar');
          recoverSession();
        } else {
          console.log('[SessionHandler] Sessão válida após retorno de visibilidade');
        }
      }
    };
    
    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  // Monitorar eventos de foco
  useEffect(() => {
    const handleFocus = async () => {
      console.log('[SessionHandler] Janela recebeu foco, verificando sessão');
      
      // Verificar se temos uma sessão válida
      const { data, error } = await supabase.auth.getSession();
      
      if (error || !data.session) {
        console.warn('[SessionHandler] Sessão inválida após foco, tentando recuperar');
        recoverSession();
      } else {
        console.log('[SessionHandler] Sessão válida após foco');
      }
    };
    
    window.addEventListener('focus', handleFocus);
    
    return () => {
      window.removeEventListener('focus', handleFocus);
    };
  }, []);

  // Monitorar eventos de reconexão
  useEffect(() => {
    const handleReconnect = () => {
      console.log('[SessionHandler] Evento de reconexão recebido');
      
      // Resetar contador de tentativas
      recoveryAttemptRef.current = 0;
    };
    
    document.addEventListener('app-reconnected', handleReconnect);
    
    return () => {
      document.removeEventListener('app-reconnected', handleReconnect);
    };
  }, []);

  // Este componente não renderiza nada
  return null;
}

export default SessionPersistenceHandler;
