import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useToast } from '@/components/ui/use-toast';
import { DeletedItem, fetchDeletedItems, restoreDeletedItem, deletePermanently } from '@/services/deletedItemsService';
import { Loader2, Trash2, AlertTriangle, RotateCcw, Users, Info, Clock, FileText, Tag, LayoutGrid } from 'lucide-react';
import { format, formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface DeletedItemsManagerProps {
  onItemRestored?: () => void;
}

export function DeletedItemsManager({ onItemRestored }: DeletedItemsManagerProps) {
  const { toast } = useToast();
  const [deletedItems, setDeletedItems] = useState<DeletedItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [isRestoreDialogOpen, setIsRestoreDialogOpen] = useState(false);
  const [isDeletePermanentlyDialogOpen, setIsDeletePermanentlyDialogOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<DeletedItem | null>(null);
  const [activeTab, setActiveTab] = useState<'all' | 'precatorio' | 'tag' | 'coluna' | 'cliente'>('all');

  // Carregar itens excluídos
  useEffect(() => {
    loadDeletedItems();
  }, [activeTab]);

  const loadDeletedItems = async () => {
    try {
      setLoading(true);
      const itemType = activeTab === 'all' ? undefined : activeTab as any;
      const items = await fetchDeletedItems(itemType);
      setDeletedItems(items);
    } catch (error) {
      console.error('Erro ao carregar itens excluídos:', error);
      toast({
        title: 'Erro ao carregar itens excluídos',
        description: 'Não foi possível carregar os itens excluídos. Tente novamente mais tarde.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Abrir diálogo de restauração
  const handleOpenRestoreDialog = (item: DeletedItem) => {
    setSelectedItem(item);
    setIsRestoreDialogOpen(true);
  };

  // Abrir diálogo de exclusão permanente
  const handleOpenDeletePermanentlyDialog = (item: DeletedItem) => {
    setSelectedItem(item);
    setIsDeletePermanentlyDialogOpen(true);
  };

  // Restaurar item
  const handleRestoreItem = async () => {
    try {
      if (!selectedItem) return;

      const result = await restoreDeletedItem(selectedItem.id);
      
      if (result.success) {
        setDeletedItems(deletedItems.filter(item => item.id !== selectedItem.id));
        setIsRestoreDialogOpen(false);
        
        toast({
          title: 'Item restaurado',
          description: `O item foi restaurado com sucesso.`,
        });

        if (onItemRestored) onItemRestored();
      } else {
        toast({
          title: 'Erro ao restaurar item',
          description: result.message || 'Não foi possível restaurar o item.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Erro ao restaurar item:', error);
      toast({
        title: 'Erro ao restaurar item',
        description: 'Não foi possível restaurar o item. Tente novamente mais tarde.',
        variant: 'destructive',
      });
    }
  };

  // Excluir permanentemente
  const handleDeletePermanently = async () => {
    try {
      if (!selectedItem) return;

      await deletePermanently(selectedItem.id);
      setDeletedItems(deletedItems.filter(item => item.id !== selectedItem.id));
      setIsDeletePermanentlyDialogOpen(false);
      
      toast({
        title: 'Item excluído permanentemente',
        description: 'O item foi excluído permanentemente com sucesso.',
      });
    } catch (error) {
      console.error('Erro ao excluir permanentemente:', error);
      toast({
        title: 'Erro ao excluir permanentemente',
        description: 'Não foi possível excluir o item permanentemente. Tente novamente mais tarde.',
        variant: 'destructive',
      });
    }
  };

  // Obter ícone do tipo de item
  const getItemTypeIcon = (type: string) => {
    switch (type) {
      case 'precatorio':
        return <FileText className="w-4 h-4" />;
      case 'tag':
        return <Tag className="w-4 h-4" />;
      case 'coluna':
        return <LayoutGrid className="w-4 h-4" />;
      case 'cliente':
        return <Users className="w-4 h-4" />;
      default:
        return <FileText className="w-4 h-4" />;
    }
  };

  // Obter nome do tipo de item
  const getItemTypeName = (type: string) => {
    switch (type) {
      case 'precatorio':
        return 'Precatório';
      case 'tag':
        return 'Tag';
      case 'coluna':
        return 'Coluna';
      case 'cliente':
        return 'Cliente';
      default:
        return 'Item';
    }
  };

  // Obter nome do item
  const getItemName = (item: DeletedItem) => {
    const data = item.item_data;
    
    switch (item.item_type) {
      case 'precatorio':
        return data.numero_precatorio || 'Precatório sem número';
      case 'tag':
        return data.nome || 'Tag sem nome';
      case 'coluna':
        return data.nome || 'Coluna sem nome';
      case 'cliente':
        return data.nome || data.email || 'Cliente sem nome';
      default:
        return 'Item sem nome';
    }
  };

  // Renderizar tabela de itens excluídos
  const renderDeletedItemsTable = () => (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Tipo</TableHead>
          <TableHead>Nome</TableHead>
          <TableHead>Excluído por</TableHead>
          <TableHead>Data de exclusão</TableHead>
          <TableHead>Prazo para restauração</TableHead>
          <TableHead className="text-right">Ações</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {deletedItems.length === 0 ? (
          <TableRow>
            <TableCell colSpan={6} className="text-center py-8">
              <div className="flex flex-col items-center justify-center text-muted-foreground">
                <Trash2 className="w-8 h-8 mb-2" />
                <p>Nenhum item excluído encontrado</p>
              </div>
            </TableCell>
          </TableRow>
        ) : (
          deletedItems.map(item => {
            const canRestore = item.can_restore && (!item.restore_deadline || new Date(item.restore_deadline) > new Date());
            const deadlineDate = item.restore_deadline ? new Date(item.restore_deadline) : null;
            const timeLeft = deadlineDate ? formatDistanceToNow(deadlineDate, { locale: ptBR, addSuffix: true }) : 'Sem prazo';
            
            return (
              <TableRow key={item.id}>
                <TableCell>
                  <Badge variant="outline" className="flex items-center gap-1">
                    {getItemTypeIcon(item.item_type)}
                    <span>{getItemTypeName(item.item_type)}</span>
                  </Badge>
                </TableCell>
                <TableCell>{getItemName(item)}</TableCell>
                <TableCell>
                  {item.deleted_by_user ? (
                    <div className="flex items-center gap-2">
                      <div className="w-6 h-6 rounded-full bg-muted flex items-center justify-center">
                        {item.deleted_by_user.avatar_url ? (
                          <img
                            src={item.deleted_by_user.avatar_url}
                            alt={item.deleted_by_user.nome}
                            className="w-6 h-6 rounded-full"
                          />
                        ) : (
                          <Users className="w-3 h-3" />
                        )}
                      </div>
                      <span>{item.deleted_by_user.nome}</span>
                    </div>
                  ) : (
                    <span className="text-muted-foreground">Desconhecido</span>
                  )}
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-1">
                    <Clock className="w-3 h-3 text-muted-foreground" />
                    <span title={format(new Date(item.deleted_at), 'PPpp', { locale: ptBR })}>
                      {formatDistanceToNow(new Date(item.deleted_at), { locale: ptBR, addSuffix: true })}
                    </span>
                  </div>
                </TableCell>
                <TableCell>
                  {deadlineDate ? (
                    <div className="flex items-center gap-1">
                      <Clock className="w-3 h-3 text-muted-foreground" />
                      <span title={format(deadlineDate, 'PPpp', { locale: ptBR })}>
                        {timeLeft}
                      </span>
                    </div>
                  ) : (
                    <span className="text-muted-foreground">Sem prazo</span>
                  )}
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex items-center justify-end gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleOpenRestoreDialog(item)}
                      disabled={!canRestore}
                      title={canRestore ? 'Restaurar item' : 'Não é possível restaurar este item'}
                    >
                      <RotateCcw className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleOpenDeletePermanentlyDialog(item)}
                      className="text-destructive hover:text-destructive"
                      title="Excluir permanentemente"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            );
          })
        )}
      </TableBody>
    </Table>
  );

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold">Itens Excluídos</h2>
        <p className="text-muted-foreground">
          Gerencie e restaure itens que foram excluídos recentemente
        </p>
      </div>

      <Alert>
        <Info className="h-4 w-4" />
        <AlertTitle>Informação</AlertTitle>
        <AlertDescription>
          Os itens excluídos ficam disponíveis para restauração por 30 dias. Após esse período, eles são removidos permanentemente.
        </AlertDescription>
      </Alert>

      <Tabs defaultValue="all" value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
        <TabsList className="mb-4">
          <TabsTrigger value="all">Todos</TabsTrigger>
          <TabsTrigger value="precatorio">Precatórios</TabsTrigger>
          <TabsTrigger value="tag">Tags</TabsTrigger>
          <TabsTrigger value="coluna">Colunas</TabsTrigger>
          <TabsTrigger value="cliente">Clientes</TabsTrigger>
        </TabsList>
        
        <Card>
          <CardContent className="p-0">
            {loading ? (
              <div className="flex items-center justify-center p-8">
                <Loader2 className="w-8 h-8 animate-spin text-primary" />
              </div>
            ) : (
              <ScrollArea className="h-[500px]">
                {renderDeletedItemsTable()}
              </ScrollArea>
            )}
          </CardContent>
        </Card>
      </Tabs>

      {/* Diálogo de restauração */}
      <Dialog open={isRestoreDialogOpen} onOpenChange={setIsRestoreDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Restaurar Item</DialogTitle>
            <DialogDescription>
              Tem certeza que deseja restaurar este item?
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Alert>
              <Info className="h-4 w-4" />
              <AlertTitle>Informação</AlertTitle>
              <AlertDescription>
                Ao restaurar o item "{getItemName(selectedItem || {} as DeletedItem)}", ele voltará a estar disponível no sistema.
              </AlertDescription>
            </Alert>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsRestoreDialogOpen(false)}>
              Cancelar
            </Button>
            <Button onClick={handleRestoreItem}>
              Restaurar Item
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Diálogo de exclusão permanente */}
      <Dialog open={isDeletePermanentlyDialogOpen} onOpenChange={setIsDeletePermanentlyDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Excluir Permanentemente</DialogTitle>
            <DialogDescription>
              Tem certeza que deseja excluir permanentemente este item?
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Atenção</AlertTitle>
              <AlertDescription>
                Esta ação irá excluir permanentemente o item "{getItemName(selectedItem || {} as DeletedItem)}".
                Esta ação não pode ser desfeita.
              </AlertDescription>
            </Alert>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeletePermanentlyDialogOpen(false)}>
              Cancelar
            </Button>
            <Button variant="destructive" onClick={handleDeletePermanently}>
              Excluir Permanentemente
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
