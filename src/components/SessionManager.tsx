import { useEffect, useState } from 'react';
import { authManager } from '@/lib/authManager';
import { supabase } from '@/lib/supabase';
import { useToast } from '@/components/ui/use-toast';
import { executarMigracaoAutomatica } from '@/services/migracaoStatusService';

/**
 * Componente que gerencia a sessão do usuário
 * Este componente deve ser montado no layout principal da aplicação
 */
export function SessionManager() {
  const { toast } = useToast();
  const [reconnectCount, setReconnectCount] = useState(0);
  const [lastReconnect, setLastReconnect] = useState<Date | null>(null);

  useEffect(() => {
    console.log('[SessionManager] Iniciando gerenciador de sessão');

    // Iniciar o gerenciador de autenticação
    authManager.start();

    // Verificar se o usuário está logado
    const checkInitialSession = async () => {
      try {
        const { data } = await supabase.auth.getSession();
        if (data.session) {
          console.log('[SessionManager] Usuário já está logado');

          // Executar migração automática de status de precatórios
          try {
            console.log('[SessionManager] Iniciando migração automática de status de precatórios');
            await executarMigracaoAutomatica();
          } catch (migrationError) {
            console.error('[SessionManager] Erro na migração automática:', migrationError);
          }
        } else {
          console.warn('[SessionManager] Usuário não está logado');
        }
      } catch (error) {
        console.error('[SessionManager] Erro ao verificar sessão inicial:', error);
      }
    };

    checkInitialSession();

    // Adicionar listener para o evento de reconexão
    const handleReconnect = (event: Event) => {
      const customEvent = event as CustomEvent;
      console.log('[SessionManager] Evento de reconexão recebido:', customEvent.detail);

      setReconnectCount(prev => prev + 1);
      setLastReconnect(new Date());

      // Removido toast de conexão restaurada
    };

    document.addEventListener('app-reconnected', handleReconnect);

    // Adicionar listener para o evento de problema de sessão
    const handleSessionProblem = (event: Event) => {
      const customEvent = event as CustomEvent;
      console.warn('[SessionManager] Evento de problema de sessão recebido:', customEvent.detail);

      // Removido toast de problema de sessão
    };

    document.addEventListener('session-problem', handleSessionProblem);

    // Limpar ao desmontar
    return () => {
      console.log('[SessionManager] Parando gerenciador de sessão');
      authManager.stop();
      document.removeEventListener('app-reconnected', handleReconnect);
      document.removeEventListener('session-problem', handleSessionProblem);
    };
  }, [toast]);

  // Este componente não renderiza nada visível
  return null;
}

export default SessionManager;
