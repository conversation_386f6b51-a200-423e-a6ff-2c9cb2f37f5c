import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Shield,
  RefreshCw,
  CheckCircle2,
  XCircle,
  AlertCircle,
  Info
} from "lucide-react";
import { usePermissions } from '@/hooks/usePermissions';
import { useAuth } from '@/hooks/useAuth';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { getPermissionsCacheStats } from '@/services/permissionsService';

/**
 * Componente que exibe o status de permissões do usuário atual
 */
export function PermissionStatus() {
  const { user } = useAuth();
  const {
    permissions,
    refreshPermissions,
    loading,
    isAdmin
  } = usePermissions();
  const [cacheStats, setCacheStats] = useState<{
    totalEntries: number;
    validEntries: number;
    expiredEntries: number;
    averageAge: number;
  } | null>(null);

  // Carregar estatísticas do cache
  const loadCacheStats = () => {
    try {
      const stats = getPermissionsCacheStats();
      setCacheStats(stats);
    } catch (error) {
      console.error('Erro ao carregar estatísticas do cache:', error);
    }
  };

  // Carregar estatísticas ao montar o componente
  useEffect(() => {
    loadCacheStats();

    // Atualizar estatísticas a cada 30 segundos
    const interval = setInterval(() => {
      loadCacheStats();
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  // Formatar tempo em segundos
  const formatTime = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    if (seconds < 60) {
      return `${seconds}s`;
    }
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <Shield className="h-5 w-5 text-primary" />
            Status de Permissões
          </CardTitle>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => {
              refreshPermissions();
              loadCacheStats();
            }}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            <span className="sr-only">Atualizar</span>
          </Button>
        </div>
        <CardDescription>
          Informações sobre suas permissões no sistema
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Informações do usuário */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">Usuário:</span>
              <span className="text-sm">{user?.name || 'Não autenticado'}</span>
            </div>
            <Badge variant={isAdmin ? "default" : "outline"}>
              {isAdmin ? 'Administrador' : user?.role || 'Sem papel'}
            </Badge>
          </div>

          {/* Status do cache */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Status do Cache:</span>
              {loading ? (
                <Badge variant="outline" className="bg-muted">
                  <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                  Atualizando...
                </Badge>
              ) : permissions ? (
                <Badge variant="success" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                  <CheckCircle2 className="h-3 w-3 mr-1" />
                  Carregado
                </Badge>
              ) : (
                <Badge variant="destructive">
                  <XCircle className="h-3 w-3 mr-1" />
                  Não carregado
                </Badge>
              )}
            </div>

            {cacheStats && (
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div className="flex items-center justify-between p-2 bg-muted/50 rounded-md">
                  <span>Entradas no cache:</span>
                  <Badge variant="outline" className="ml-2">
                    {cacheStats.totalEntries}
                  </Badge>
                </div>
                <div className="flex items-center justify-between p-2 bg-muted/50 rounded-md">
                  <span>Entradas válidas:</span>
                  <Badge variant="outline" className="ml-2">
                    {cacheStats.validEntries}
                  </Badge>
                </div>
                <div className="flex items-center justify-between p-2 bg-muted/50 rounded-md">
                  <span>Entradas expiradas:</span>
                  <Badge variant="outline" className="ml-2">
                    {cacheStats.expiredEntries}
                  </Badge>
                </div>
                <div className="flex items-center justify-between p-2 bg-muted/50 rounded-md">
                  <span>Idade média:</span>
                  <Badge variant="outline" className="ml-2">
                    {formatTime(cacheStats.averageAge)}
                  </Badge>
                </div>
              </div>
            )}
          </div>

          {/* Permissões */}
          {permissions && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Permissões:</span>
                <Badge variant="outline">
                  {permissions?.role_permissions?.length || 0} permissões
                </Badge>
              </div>

              <div className="grid grid-cols-2 gap-2">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="flex items-center justify-between p-2 bg-muted/50 rounded-md">
                        <div className="flex items-center gap-1">
                          <CheckCircle2 className="h-3 w-3 text-green-600 dark:text-green-400" />
                          <span className="text-xs">Clientes</span>
                        </div>
                        <Info className="h-3 w-3 text-muted-foreground" />
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <div className="text-xs space-y-1">
                        <p className="font-medium">Permissões para Clientes:</p>
                        <ul className="list-disc list-inside">
                          {permissions?.role_permissions
                            ?.filter(p => p.resource_type === 'cliente')
                            .map(p => (
                              <li key={`cliente-${p.action}`} className="flex items-center gap-1">
                                {p.allowed ? (
                                  <CheckCircle2 className="h-3 w-3 text-green-600 dark:text-green-400" />
                                ) : (
                                  <XCircle className="h-3 w-3 text-red-600 dark:text-red-400" />
                                )}
                                <span>{p.action}</span>
                              </li>
                            )) || []}
                        </ul>
                      </div>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="flex items-center justify-between p-2 bg-muted/50 rounded-md">
                        <div className="flex items-center gap-1">
                          <CheckCircle2 className="h-3 w-3 text-green-600 dark:text-green-400" />
                          <span className="text-xs">Precatórios</span>
                        </div>
                        <Info className="h-3 w-3 text-muted-foreground" />
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <div className="text-xs space-y-1">
                        <p className="font-medium">Permissões para Precatórios:</p>
                        <ul className="list-disc list-inside">
                          {permissions?.role_permissions
                            ?.filter(p => p.resource_type === 'precatorio')
                            .map(p => (
                              <li key={`precatorio-${p.action}`} className="flex items-center gap-1">
                                {p.allowed ? (
                                  <CheckCircle2 className="h-3 w-3 text-green-600 dark:text-green-400" />
                                ) : (
                                  <XCircle className="h-3 w-3 text-red-600 dark:text-red-400" />
                                )}
                                <span>{p.action}</span>
                              </li>
                            )) || []}
                        </ul>
                      </div>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
          )}

          {/* Mensagem de ajuda */}
          <div className="flex items-start gap-2 p-2 bg-blue-50 dark:bg-blue-950/30 border border-blue-200 dark:border-blue-800 rounded-md text-xs text-blue-800 dark:text-blue-300">
            <AlertCircle className="h-4 w-4 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
            <div>
              <p className="font-medium">Sobre permissões</p>
              <p className="mt-1">
                Suas permissões determinam quais ações você pode realizar no sistema.
                Se você precisar de acesso a recursos adicionais, entre em contato com um administrador.
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
