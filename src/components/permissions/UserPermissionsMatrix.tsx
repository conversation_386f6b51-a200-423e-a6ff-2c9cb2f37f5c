import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Checkbox } from "@/components/ui/checkbox";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import {
  Filter,
  Save,
  Refresh<PERSON>w,
  FileText,
  Users,
  ClipboardList,
  LayoutDashboard,
  Settings,
  Search,
  Check,
  X,
  Info
} from "lucide-react";
import { useAuth } from '@/hooks/useAuth';
import { UserBasicInfo, PermissionMatrixItem } from '@/types/permissions';
import { getUserPermissions, updateUserPermission } from '@/services/advancedPermissionsService';

interface UserPermissionsMatrixProps {
  users: UserBasicInfo[];
}

// Resource types and actions for the matrix
const resourceTypes = [
  { type: 'cliente', name: 'Clientes', icon: <Users className="h-4 w-4" /> },
  { type: 'precatorio', name: 'Precatórios', icon: <FileText className="h-4 w-4" /> },
  { type: 'rpv', name: 'RPVs', icon: <FileText className="h-4 w-4" /> },
  { type: 'tarefa', name: 'Tarefas', icon: <ClipboardList className="h-4 w-4" /> },
  { type: 'documento', name: 'Documentos', icon: <FileText className="h-4 w-4" /> },
  { type: 'relatorio', name: 'Relatórios', icon: <LayoutDashboard className="h-4 w-4" /> },
  { type: 'system', name: 'Sistema', icon: <Settings className="h-4 w-4" /> }
];

const actions = [
  { action: 'view', label: 'Visualizar' },
  { action: 'create', label: 'Criar' },
  { action: 'edit', label: 'Editar' },
  { action: 'delete', label: 'Excluir' }
];

export function UserPermissionsMatrix({ users }: UserPermissionsMatrixProps) {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [matrixData, setMatrixData] = useState<PermissionMatrixItem[]>([]);
  const [filteredData, setFilteredData] = useState<PermissionMatrixItem[]>([]);
  const [selectedResourceType, setSelectedResourceType] = useState<string>('cliente');
  const [searchQuery, setSearchQuery] = useState('');
  const [changedPermissions, setChangedPermissions] = useState<{
    userId: string;
    resourceType: string;
    action: string;
    allowed: boolean;
  }[]>([]);

  // Carregar dados da matriz
  useEffect(() => {
    if (users.length > 0) {
      loadMatrixData();
    }
  }, [users]);

  // Filtrar dados quando o tipo de recurso ou a busca mudar
  useEffect(() => {
    filterMatrixData();
  }, [selectedResourceType, searchQuery, matrixData]);

  // Carregar dados de permissões para todos os usuários
  const loadMatrixData = async () => {
    try {
      setLoading(true);
      
      const matrixItems: PermissionMatrixItem[] = [];
      
      for (const user of users) {
        try {
          const permissions = await getUserPermissions(user.id);
          
          // Criar objeto de permissões para o usuário
          const permissionObj: { [resourceType: string]: { [action: string]: boolean } } = {};
          
          // Adicionar permissões de papel (role)
          permissions.role_permissions.forEach(perm => {
            if (!permissionObj[perm.resource_type]) {
              permissionObj[perm.resource_type] = {};
            }
            permissionObj[perm.resource_type][perm.action] = perm.allowed;
          });
          
          // Adicionar permissões específicas (sobrescrevem as de papel)
          permissions.specific_permissions.forEach(perm => {
            if (!perm.resource_id) { // Apenas permissões gerais, não específicas a um recurso
              if (!permissionObj[perm.resource_type]) {
                permissionObj[perm.resource_type] = {};
              }
              permissionObj[perm.resource_type][perm.action] = perm.allowed;
            }
          });
          
          matrixItems.push({
            userId: user.id,
            userName: user.name,
            userRole: user.role,
            permissions: permissionObj
          });
        } catch (error) {
          console.error(`Erro ao carregar permissões para ${user.name}:`, error);
        }
      }
      
      setMatrixData(matrixItems);
    } catch (error) {
      console.error('Erro ao carregar dados da matriz:', error);
      toast.error("Erro ao carregar permissões", {
        description: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    } finally {
      setLoading(false);
    }
  };

  // Filtrar dados da matriz
  const filterMatrixData = () => {
    if (!matrixData.length) {
      setFilteredData([]);
      return;
    }
    
    let filtered = [...matrixData];
    
    // Filtrar por busca
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(item => 
        item.userName.toLowerCase().includes(query) || 
        item.userRole?.toLowerCase().includes(query)
      );
    }
    
    setFilteredData(filtered);
  };

  // Alternar permissão
  const togglePermission = (userId: string, resourceType: string, action: string, currentValue: boolean) => {
    // Verificar se é o próprio usuário admin tentando remover suas próprias permissões
    if (userId === user?.id && resourceType === 'system' && action === 'manage_roles' && currentValue) {
      toast.error("Operação não permitida", {
        description: "Você não pode remover sua própria permissão de gerenciar papéis."
      });
      return;
    }
    
    // Atualizar estado local
    const newMatrixData = matrixData.map(item => {
      if (item.userId === userId) {
        return {
          ...item,
          permissions: {
            ...item.permissions,
            [resourceType]: {
              ...item.permissions[resourceType],
              [action]: !currentValue
            }
          }
        };
      }
      return item;
    });
    
    setMatrixData(newMatrixData);
    
    // Adicionar à lista de permissões alteradas
    const existingIndex = changedPermissions.findIndex(
      p => p.userId === userId && p.resourceType === resourceType && p.action === action
    );
    
    if (existingIndex >= 0) {
      // Atualizar permissão existente
      const newChangedPermissions = [...changedPermissions];
      newChangedPermissions[existingIndex].allowed = !currentValue;
      setChangedPermissions(newChangedPermissions);
    } else {
      // Adicionar nova permissão alterada
      setChangedPermissions([
        ...changedPermissions,
        {
          userId,
          resourceType,
          action,
          allowed: !currentValue
        }
      ]);
    }
  };

  // Salvar alterações
  const saveChanges = async () => {
    try {
      setSaving(true);
      
      for (const change of changedPermissions) {
        await updateUserPermission(
          user!.id,
          change.userId,
          change.resourceType,
          change.action,
          change.allowed
        );
      }
      
      toast.success("Permissões atualizadas", {
        description: `${changedPermissions.length} permissões foram atualizadas com sucesso.`
      });
      
      // Limpar lista de alterações
      setChangedPermissions([]);
    } catch (error) {
      console.error('Erro ao salvar permissões:', error);
      toast.error("Erro ao salvar permissões", {
        description: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    } finally {
      setSaving(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Matriz de Permissões</CardTitle>
            <CardDescription>
              Configure permissões por usuário e tipo de recurso
            </CardDescription>
          </div>
          
          <div className="flex items-center gap-2">
            <Select
              value={selectedResourceType}
              onValueChange={setSelectedResourceType}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Tipo de recurso" />
              </SelectTrigger>
              <SelectContent>
                {resourceTypes.map(resource => (
                  <SelectItem key={resource.type} value={resource.type}>
                    <div className="flex items-center">
                      {resource.icon}
                      <span className="ml-2">{resource.name}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => loadMatrixData()}
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Atualizar
            </Button>
            
            <Button
              size="sm"
              onClick={saveChanges}
              disabled={saving || changedPermissions.length === 0}
            >
              <Save className="h-4 w-4 mr-2" />
              Salvar Alterações
              {changedPermissions.length > 0 && (
                <Badge variant="secondary" className="ml-2">
                  {changedPermissions.length}
                </Badge>
              )}
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <ScrollArea className="h-[600px]">
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[250px]">Usuário</TableHead>
                  {actions.map(action => (
                    <TableHead key={action.action} className="text-center">
                      {action.label}
                    </TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={5} className="h-24 text-center">
                      <RefreshCw className="h-5 w-5 animate-spin mx-auto" />
                      <div className="mt-2">Carregando permissões...</div>
                    </TableCell>
                  </TableRow>
                ) : filteredData.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} className="h-24 text-center">
                      <div className="flex flex-col items-center justify-center text-muted-foreground">
                        <Info className="h-10 w-10 mb-2" />
                        <p>Nenhum usuário encontrado</p>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredData.map(item => (
                    <TableRow key={item.userId}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={users.find(u => u.id === item.userId)?.avatar_url} />
                            <AvatarFallback>
                              {item.userName.substring(0, 2).toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium">{item.userName}</div>
                            {item.userRole && (
                              <div className="text-xs text-muted-foreground">{item.userRole}</div>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      
                      {actions.map(action => {
                        const hasPermission = item.permissions[selectedResourceType]?.[action.action] || false;
                        const isChanged = changedPermissions.some(
                          p => p.userId === item.userId && 
                               p.resourceType === selectedResourceType && 
                               p.action === action.action
                        );
                        
                        return (
                          <TableCell key={action.action} className="text-center">
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Checkbox
                                    checked={hasPermission}
                                    onCheckedChange={() => togglePermission(
                                      item.userId,
                                      selectedResourceType,
                                      action.action,
                                      hasPermission
                                    )}
                                    className={isChanged ? 'border-primary' : ''}
                                  />
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>
                                    {hasPermission 
                                      ? `Permitir ${action.label.toLowerCase()} ${resourceTypes.find(r => r.type === selectedResourceType)?.name.toLowerCase()}` 
                                      : `Não permitir ${action.label.toLowerCase()} ${resourceTypes.find(r => r.type === selectedResourceType)?.name.toLowerCase()}`}
                                  </p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </TableCell>
                        );
                      })}
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
