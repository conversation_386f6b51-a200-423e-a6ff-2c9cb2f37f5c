import React, { useState, useEffect } from 'react';
import { useDataVisibility } from '@/hooks/useDataVisibility';
import { Loader2, EyeOff, Shield } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface DataVisibilityGuardProps {
  resourceType: string;
  resourceOwnerId?: string;
  resourceMetadata?: any;
  requireSensitiveData?: boolean;
  requireFinancialData?: boolean;
  requirePersonalData?: boolean;
  requireExportPermission?: boolean;
  fallback?: React.ReactNode;
  loadingFallback?: React.ReactNode;
  children: React.ReactNode;
}

/**
 * Componente que protege dados baseado nas configurações de visibilidade granular
 */
export function DataVisibilityGuard({
  resourceType,
  resourceOwnerId,
  resourceMetadata,
  requireSensitiveData = false,
  requireFinancialData = false,
  requirePersonalData = false,
  requireExportPermission = false,
  fallback,
  loadingFallback,
  children
}: DataVisibilityGuardProps) {
  const {
    canViewData,
    canViewSensitiveData,
    canViewFinancialData,
    canViewPersonalData,
    canExportData
  } = useDataVisibility();

  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkPermissions = async () => {
      try {
        setLoading(true);

        // Verificar permissão básica de visualização
        const canView = await canViewData(resourceType, resourceOwnerId, resourceMetadata);
        if (!canView) {
          setHasPermission(false);
          return;
        }

        // Verificar permissões específicas se necessário
        if (requireSensitiveData) {
          const canViewSensitive = await canViewSensitiveData(resourceType);
          if (!canViewSensitive) {
            setHasPermission(false);
            return;
          }
        }

        if (requireFinancialData) {
          const canViewFinancial = await canViewFinancialData(resourceType);
          if (!canViewFinancial) {
            setHasPermission(false);
            return;
          }
        }

        if (requirePersonalData) {
          const canViewPersonal = await canViewPersonalData(resourceType);
          if (!canViewPersonal) {
            setHasPermission(false);
            return;
          }
        }

        if (requireExportPermission) {
          const canExport = await canExportData(resourceType);
          if (!canExport) {
            setHasPermission(false);
            return;
          }
        }

        setHasPermission(true);
      } catch (error) {
        console.error('Erro ao verificar permissões de visibilidade:', error);
        setHasPermission(false);
      } finally {
        setLoading(false);
      }
    };

    checkPermissions();
  }, [
    resourceType,
    resourceOwnerId,
    resourceMetadata,
    requireSensitiveData,
    requireFinancialData,
    requirePersonalData,
    requireExportPermission,
    canViewData,
    canViewSensitiveData,
    canViewFinancialData,
    canViewPersonalData,
    canExportData
  ]);

  if (loading) {
    return loadingFallback || (
      <div className="flex items-center justify-center p-4">
        <Loader2 className="h-4 w-4 animate-spin mr-2" />
        <span className="text-sm text-muted-foreground">Verificando permissões...</span>
      </div>
    );
  }

  if (!hasPermission) {
    return fallback || (
      <Alert className="border-orange-200 bg-orange-50">
        <EyeOff className="h-4 w-4 text-orange-600" />
        <AlertDescription className="text-orange-800">
          Você não tem permissão para visualizar este conteúdo.
        </AlertDescription>
      </Alert>
    );
  }

  return <>{children}</>;
}

/**
 * Componente para mascarar dados sensíveis
 */
interface DataMaskProps {
  data: string | number;
  resourceType: string;
  maskType: 'sensitive' | 'financial' | 'personal';
  maskChar?: string;
  showLength?: number;
  children?: React.ReactNode;
}

export function DataMask({
  data,
  resourceType,
  maskType,
  maskChar = '*',
  showLength = 3,
  children
}: DataMaskProps) {
  const {
    canViewSensitiveData,
    canViewFinancialData,
    canViewPersonalData
  } = useDataVisibility();

  const [canView, setCanView] = useState<boolean | null>(null);

  useEffect(() => {
    const checkPermission = async () => {
      try {
        let hasPermission = false;

        switch (maskType) {
          case 'sensitive':
            hasPermission = await canViewSensitiveData(resourceType);
            break;
          case 'financial':
            hasPermission = await canViewFinancialData(resourceType);
            break;
          case 'personal':
            hasPermission = await canViewPersonalData(resourceType);
            break;
        }

        setCanView(hasPermission);
      } catch (error) {
        console.error('Erro ao verificar permissão de máscara:', error);
        setCanView(false);
      }
    };

    checkPermission();
  }, [resourceType, maskType, canViewSensitiveData, canViewFinancialData, canViewPersonalData]);

  if (canView === null) {
    return <Loader2 className="h-3 w-3 animate-spin inline" />;
  }

  if (canView) {
    return children || <span>{data}</span>;
  }

  // Mascarar dados
  const stringData = String(data);
  const maskedData = stringData.length > showLength
    ? stringData.substring(0, showLength) + maskChar.repeat(stringData.length - showLength)
    : maskChar.repeat(stringData.length);

  return (
    <span className="inline-flex items-center gap-1 text-muted-foreground">
      <Shield className="h-3 w-3" />
      {maskedData}
    </span>
  );
}

/**
 * Componente para botões condicionais baseados em permissões
 */
interface ConditionalButtonProps {
  resourceType: string;
  action: 'export' | 'view_sensitive' | 'view_financial' | 'view_personal';
  fallback?: React.ReactNode;
  children: React.ReactNode;
}

export function ConditionalButton({
  resourceType,
  action,
  fallback,
  children
}: ConditionalButtonProps) {
  const {
    canExportData,
    canViewSensitiveData,
    canViewFinancialData,
    canViewPersonalData
  } = useDataVisibility();

  const [hasPermission, setHasPermission] = useState<boolean | null>(null);

  useEffect(() => {
    const checkPermission = async () => {
      try {
        let permission = false;

        switch (action) {
          case 'export':
            permission = await canExportData(resourceType);
            break;
          case 'view_sensitive':
            permission = await canViewSensitiveData(resourceType);
            break;
          case 'view_financial':
            permission = await canViewFinancialData(resourceType);
            break;
          case 'view_personal':
            permission = await canViewPersonalData(resourceType);
            break;
        }

        setHasPermission(permission);
      } catch (error) {
        console.error('Erro ao verificar permissão do botão:', error);
        setHasPermission(false);
      }
    };

    checkPermission();
  }, [resourceType, action, canExportData, canViewSensitiveData, canViewFinancialData, canViewPersonalData]);

  if (hasPermission === null) {
    return <Loader2 className="h-4 w-4 animate-spin" />;
  }

  if (!hasPermission) {
    return fallback || null;
  }

  return <>{children}</>;
}

/**
 * Hook para filtrar listas de dados baseado em permissões
 */
export function useFilteredData<T extends { 
  id: string; 
  created_by?: string; 
  responsavel_id?: string; 
  user_id?: string;
}>(data: T[], resourceType: string) {
  const { filterVisibleData } = useDataVisibility();
  const [filteredData, setFilteredData] = useState<T[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const applyFilter = async () => {
      try {
        setLoading(true);
        const filtered = await filterVisibleData(data, resourceType);
        setFilteredData(filtered);
      } catch (error) {
        console.error('Erro ao filtrar dados:', error);
        setFilteredData([]);
      } finally {
        setLoading(false);
      }
    };

    applyFilter();
  }, [data, resourceType, filterVisibleData]);

  return { filteredData, loading };
}
