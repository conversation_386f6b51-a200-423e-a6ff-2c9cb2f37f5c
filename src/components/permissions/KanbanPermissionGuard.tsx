import React from 'react';
import { usePermissions } from '@/hooks/usePermissions';
import { KanbanColuna } from '@/components/Precatorios/types';

interface KanbanPermissionGuardProps {
  action: 'view' | 'create' | 'edit' | 'delete' | 'move';
  resourceType: 'precatorio' | 'rpv' | 'kanban_column';
  resourceId?: string;
  columnId?: string;
  fallback?: React.ReactNode;
  children: React.ReactNode;
}

/**
 * Componente especializado para verificar permissões no contexto do Kanban
 * Verifica permissões específicas para visualização, criação, edição, exclusão e movimentação de precatórios
 */
export function KanbanPermissionGuard({
  action,
  resourceType,
  resourceId,
  columnId,
  fallback = null,
  children
}: KanbanPermissionGuardProps) {
  const { canSee, can } = usePermissions();
  
  // Verificar permissão básica
  let hasPermission = canSee(action, resourceType, resourceId);
  
  // Verificações adicionais específicas para o Kanban
  if (hasPermission && action === 'move' && columnId) {
    // Verificar se o usuário tem permissão para mover para esta coluna específica
    hasPermission = can('edit', 'kanban_column', columnId);
  }
  
  if (!hasPermission) {
    return <>{fallback}</>;
  }
  
  return <>{children}</>;
}

interface KanbanColumnPermissionGuardProps {
  column: KanbanColuna;
  fallback?: React.ReactNode;
  children: React.ReactNode;
}

/**
 * Componente para verificar permissões específicas para colunas do Kanban
 */
export function KanbanColumnPermissionGuard({
  column,
  fallback = null,
  children
}: KanbanColumnPermissionGuardProps) {
  const { canSee } = usePermissions();
  
  // Verificar se o usuário tem permissão para ver esta coluna específica
  const hasPermission = canSee('view', 'kanban_column', column.id);
  
  if (!hasPermission) {
    return <>{fallback}</>;
  }
  
  return <>{children}</>;
}

interface KanbanViewPermissionGuardProps {
  viewId: string;
  fallback?: React.ReactNode;
  children: React.ReactNode;
}

/**
 * Componente para verificar permissões específicas para visualizações personalizadas do Kanban
 */
export function KanbanViewPermissionGuard({
  viewId,
  fallback = null,
  children
}: KanbanViewPermissionGuardProps) {
  const { canSee } = usePermissions();
  
  // Verificar se o usuário tem permissão para ver esta visualização específica
  const hasPermission = canSee('view', 'custom_view', viewId);
  
  if (!hasPermission) {
    return <>{fallback}</>;
  }
  
  return <>{children}</>;
}

interface KanbanFilterPermissionGuardProps {
  filterId?: string;
  fallback?: React.ReactNode;
  children: React.ReactNode;
}

/**
 * Componente para verificar permissões específicas para filtros do Kanban
 */
export function KanbanFilterPermissionGuard({
  filterId,
  fallback = null,
  children
}: KanbanFilterPermissionGuardProps) {
  const { canSee } = usePermissions();
  
  // Verificar se o usuário tem permissão para usar filtros
  // Se filterId for fornecido, verifica permissão para este filtro específico
  const hasPermission = filterId 
    ? canSee('view', 'kanban_filter', filterId)
    : canSee('view', 'kanban_filter');
  
  if (!hasPermission) {
    return <>{fallback}</>;
  }
  
  return <>{children}</>;
}
