import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON>, 
  DialogContent, 
  DialogDescription, 
  Di<PERSON>Footer, 
  DialogHeader, 
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "sonner";
import { 
  LayoutGrid, 
  List, 
  Table as TableIcon, 
  Plus, 
  Edit, 
  Trash2, 
  Star, 
  Eye, 
  EyeOff, 
  Users, 
  User, 
  UserPlus, 
  UserMinus,
  Save,
  Loader2
} from "lucide-react";
import { useAuth } from '@/hooks/useAuth';
import { useKanbanPermissions } from '@/hooks/useKanbanPermissions';
import { CustomView, KanbanColuna } from '@/components/Precatorios/types';
import { UserBasicInfo } from '@/types/permissions';
import { 
  manageCustomView, 
  assignViewToUser 
} from '@/services/advancedPermissionsService';
import { ColorPicker } from '@/components/ui/color-picker';

interface KanbanViewsManagerProps {
  views: CustomView[];
  columns: KanbanColuna[];
  users: UserBasicInfo[];
  onViewsChange?: () => void;
}

export function KanbanViewsManager({ 
  views, 
  columns, 
  users,
  onViewsChange 
}: KanbanViewsManagerProps) {
  const { user } = useAuth();
  const { 
    canUseCustomView, 
    canEditCustomView, 
    filterVisibleColumns 
  } = useKanbanPermissions();
  
  const [loading, setLoading] = useState(false);
  const [selectedView, setSelectedView] = useState<CustomView | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isAssignDialogOpen, setIsAssignDialogOpen] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  
  // Filtrar colunas visíveis para o usuário
  const visibleColumns = filterVisibleColumns(columns);
  
  // Filtrar visualizações que o usuário pode ver
  const visibleViews = views.filter(view => canUseCustomView(view));
  
  // Estado do formulário
  const [formData, setFormData] = useState<{
    nome: string;
    descricao: string;
    is_public: boolean;
    layout: 'kanban' | 'lista' | 'tabela';
    icone: string;
    cor: string;
    colunas_selecionadas: string[];
    filtros: any;
  }>({
    nome: '',
    descricao: '',
    is_public: false,
    layout: 'kanban',
    icone: 'layout',
    cor: '#3b82f6',
    colunas_selecionadas: [],
    filtros: {},
  });
  
  // Resetar formulário quando o diálogo for aberto
  useEffect(() => {
    if (isCreateDialogOpen) {
      setFormData({
        nome: '',
        descricao: '',
        is_public: false,
        layout: 'kanban',
        icone: 'layout',
        cor: '#3b82f6',
        colunas_selecionadas: visibleColumns.map(col => col.id),
        filtros: {},
      });
    }
  }, [isCreateDialogOpen, visibleColumns]);
  
  // Preencher formulário quando uma visualização for selecionada para edição
  useEffect(() => {
    if (selectedView && isEditDialogOpen) {
      setFormData({
        nome: selectedView.nome,
        descricao: selectedView.descricao || '',
        is_public: selectedView.is_public,
        layout: selectedView.layout || 'kanban',
        icone: selectedView.icone || 'layout',
        cor: selectedView.cor || '#3b82f6',
        colunas_selecionadas: selectedView.colunas_selecionadas || visibleColumns.map(col => col.id),
        filtros: selectedView.filtros || {},
      });
    }
  }, [selectedView, isEditDialogOpen, visibleColumns]);
  
  // Criar nova visualização
  const handleCreateView = async () => {
    try {
      if (!formData.nome.trim()) {
        toast.error("Nome obrigatório", {
          description: "O nome da visualização é obrigatório."
        });
        return;
      }
      
      setLoading(true);
      
      const viewData = {
        name: formData.nome.trim(),
        description: formData.descricao.trim(),
        viewType: formData.layout,
        configuration: {
          icone: formData.icone,
          cor: formData.cor,
          colunas_selecionadas: formData.colunas_selecionadas,
          filtros: formData.filtros,
          is_public: formData.is_public
        },
        isDefault: false
      };
      
      await manageCustomView(user!.id, 'create', viewData);
      
      toast.success("Visualização criada", {
        description: `A visualização "${formData.nome}" foi criada com sucesso.`
      });
      
      setIsCreateDialogOpen(false);
      
      if (onViewsChange) {
        onViewsChange();
      }
    } catch (error) {
      console.error('Erro ao criar visualização:', error);
      toast.error("Erro ao criar visualização", {
        description: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    } finally {
      setLoading(false);
    }
  };
  
  // Editar visualização existente
  const handleEditView = async () => {
    try {
      if (!selectedView) return;
      
      if (!formData.nome.trim()) {
        toast.error("Nome obrigatório", {
          description: "O nome da visualização é obrigatório."
        });
        return;
      }
      
      setLoading(true);
      
      const viewData = {
        id: selectedView.id,
        name: formData.nome.trim(),
        description: formData.descricao.trim(),
        viewType: formData.layout,
        configuration: {
          icone: formData.icone,
          cor: formData.cor,
          colunas_selecionadas: formData.colunas_selecionadas,
          filtros: formData.filtros,
          is_public: formData.is_public
        },
        isDefault: selectedView.is_default
      };
      
      await manageCustomView(user!.id, 'update', viewData);
      
      toast.success("Visualização atualizada", {
        description: `A visualização "${formData.nome}" foi atualizada com sucesso.`
      });
      
      setIsEditDialogOpen(false);
      
      if (onViewsChange) {
        onViewsChange();
      }
    } catch (error) {
      console.error('Erro ao atualizar visualização:', error);
      toast.error("Erro ao atualizar visualização", {
        description: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    } finally {
      setLoading(false);
    }
  };
  
  // Excluir visualização
  const handleDeleteView = async () => {
    try {
      if (!selectedView) return;
      
      setLoading(true);
      
      const viewData = {
        id: selectedView.id,
        name: selectedView.nome,
        viewType: selectedView.layout || 'kanban',
        configuration: {},
        isDefault: selectedView.is_default
      };
      
      await manageCustomView(user!.id, 'delete', viewData);
      
      toast.success("Visualização excluída", {
        description: `A visualização "${selectedView.nome}" foi excluída com sucesso.`
      });
      
      setIsDeleteDialogOpen(false);
      
      if (onViewsChange) {
        onViewsChange();
      }
    } catch (error) {
      console.error('Erro ao excluir visualização:', error);
      toast.error("Erro ao excluir visualização", {
        description: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    } finally {
      setLoading(false);
    }
  };
  
  // Atribuir visualização a usuários
  const handleAssignView = async () => {
    try {
      if (!selectedView) return;
      
      setLoading(true);
      
      // Atribuir visualização a cada usuário selecionado
      for (const userId of selectedUsers) {
        await assignViewToUser(user!.id, userId, selectedView.id, true);
      }
      
      toast.success("Visualização atribuída", {
        description: `A visualização foi atribuída a ${selectedUsers.length} usuário(s) com sucesso.`
      });
      
      setIsAssignDialogOpen(false);
      setSelectedUsers([]);
    } catch (error) {
      console.error('Erro ao atribuir visualização:', error);
      toast.error("Erro ao atribuir visualização", {
        description: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    } finally {
      setLoading(false);
    }
  };
  
  // Renderizar ícone de layout
  const renderLayoutIcon = (layout: string) => {
    switch (layout) {
      case 'kanban':
        return <LayoutGrid className="w-4 h-4" />;
      case 'lista':
        return <List className="w-4 h-4" />;
      case 'tabela':
        return <TableIcon className="w-4 h-4" />;
      default:
        return <LayoutGrid className="w-4 h-4" />;
    }
  };
  
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">Visualizações Personalizadas</h2>
        <Button onClick={() => setIsCreateDialogOpen(true)}>
          <Plus className="w-4 h-4 mr-2" />
          Nova Visualização
        </Button>
      </div>
      
      {visibleViews.length === 0 ? (
        <Card>
          <CardContent className="p-8 flex flex-col items-center justify-center text-center">
            <LayoutGrid className="w-12 h-12 text-muted-foreground mb-4" />
            <h3 className="text-xl font-medium mb-2">Nenhuma visualização personalizada</h3>
            <p className="text-muted-foreground mb-4">
              Crie visualizações personalizadas para organizar seu quadro Kanban
            </p>
            <Button onClick={() => setIsCreateDialogOpen(true)}>
              <Plus className="w-4 h-4 mr-2" />
              Criar Visualização
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4">
          {visibleViews.map(view => {
            const layoutIcon = renderLayoutIcon(view.layout || 'kanban');
            const canEdit = canEditCustomView(view);
            
            return (
              <div
                key={view.id}
                className="flex items-center justify-between p-3 rounded-lg border border-border hover:bg-accent/50 transition-colors"
              >
                <div className="flex items-center gap-3">
                  <div
                    className="w-8 h-8 rounded-md flex items-center justify-center"
                    style={{ backgroundColor: view.cor || '#3b82f6' }}
                  >
                    {layoutIcon}
                  </div>
                  <div>
                    <div className="flex items-center gap-2">
                      <h3 className="font-medium">{view.nome}</h3>
                      {view.is_default && (
                        <Badge variant="secondary">Padrão</Badge>
                      )}
                      {view.is_public && (
                        <Badge variant="outline">Pública</Badge>
                      )}
                      {view.is_favorite && (
                        <Star className="w-4 h-4 text-amber-500 fill-amber-500" />
                      )}
                    </div>
                    {view.descricao && (
                      <p className="text-sm text-muted-foreground">{view.descricao}</p>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  {canEdit && (
                    <>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => {
                          setSelectedView(view);
                          setIsAssignDialogOpen(true);
                        }}
                      >
                        <UserPlus className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => {
                          setSelectedView(view);
                          setIsEditDialogOpen(true);
                        }}
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => {
                          setSelectedView(view);
                          setIsDeleteDialogOpen(true);
                        }}
                      >
                        <Trash2 className="w-4 h-4 text-destructive" />
                      </Button>
                    </>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      )}
      
      {/* Diálogos para criar, editar, excluir e atribuir visualizações */}
      {/* Implementação dos diálogos omitida por brevidade */}
    </div>
  );
}
