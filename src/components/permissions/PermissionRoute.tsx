import React from 'react';
import { Navigate } from 'react-router-dom';
import { usePermissions } from '@/hooks/usePermissions';
import { NoPermissionMessage } from './NoPermissionMessage';

interface PermissionRouteProps {
  action: string;
  resource: string;
  resourceId?: string;
  redirectTo?: string;
  showMessage?: boolean;
  children: React.ReactNode;
}

/**
 * Componente para proteger rotas baseado em permissões
 */
export function PermissionRoute({
  action,
  resource,
  resourceId,
  redirectTo = '/dashboard',
  showMessage = false,
  children
}: PermissionRouteProps) {
  const { can } = usePermissions();
  
  // Verificar se o usuário tem permissão
  const hasPermission = can(action, resource, resourceId);
  
  if (!hasPermission) {
    // Se não tem permissão, redirecionar ou mostrar mensagem
    if (showMessage) {
      return <NoPermissionMessage />;
    }
    
    return <Navigate to={redirectTo} replace />;
  }
  
  return <>{children}</>;
}
