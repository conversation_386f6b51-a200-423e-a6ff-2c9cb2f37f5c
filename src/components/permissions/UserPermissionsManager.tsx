import React, { useState, useEffect } from 'react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  Card<PERSON>ooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { 
  RefreshCw, 
  Save, 
  Shield, 
  User, 
  FileText, 
  LayoutDashboard,
  Users,
  Calendar,
  ClipboardList,
  BarChart4,
  Settings,
  AlertCircle
} from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { 
  getUserPermissions, 
  updateUserPermission, 
  updateTaskVisibilitySettings, 
  updatePageAccess,
  UserPermissionsData
} from '@/services/permissionsService';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { getUserRole } from '@/services/userService';
import { fetchAllRoles } from '@/services/rolesService';

interface UserPermissionsManagerProps {
  userId: string;
  userName?: string;
  onPermissionsUpdated?: () => void;
}

interface ResourcePermission {
  resourceType: string;
  resourceName: string;
  icon: React.ReactNode;
  actions: {
    action: string;
    label: string;
    allowed: boolean;
  }[];
}

interface PageAccess {
  pagePath: string;
  pageLabel: string;
  icon: React.ReactNode;
  canAccess: boolean;
}

export function UserPermissionsManager({ 
  userId, 
  userName = "Usuário", 
  onPermissionsUpdated 
}: UserPermissionsManagerProps) {
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [userRole, setUserRole] = useState<string | null>(null);
  const [availableRoles, setAvailableRoles] = useState<any[]>([]);
  const [permissions, setPermissions] = useState<UserPermissionsData | null>(null);
  const [resourcePermissions, setResourcePermissions] = useState<ResourcePermission[]>([]);
  const [pageAccess, setPageAccess] = useState<PageAccess[]>([]);
  const [taskVisibility, setTaskVisibility] = useState({
    canSeeOwnTasks: true,
    canSeeTeamTasks: false,
    canSeeAllTasks: false,
    visibleUserIds: [] as string[]
  });
  const [activeTab, setActiveTab] = useState("recursos");

  // Carregar permissões do usuário
  const loadUserPermissions = async () => {
    try {
      setLoading(true);
      
      // Carregar papel do usuário
      const role = await getUserRole(userId);
      setUserRole(role);
      
      // Carregar cargos disponíveis
      const roles = await fetchAllRoles();
      setAvailableRoles(roles);
      
      // Carregar permissões
      const userPermissions = await getUserPermissions(userId);
      setPermissions(userPermissions);
      
      // Configurar recursos e permissões
      const resources: ResourcePermission[] = [
        {
          resourceType: 'cliente',
          resourceName: 'Clientes',
          icon: <Users className="h-4 w-4" />,
          actions: [
            { action: 'view', label: 'Visualizar', allowed: false },
            { action: 'create', label: 'Criar', allowed: false },
            { action: 'edit', label: 'Editar', allowed: false },
            { action: 'delete', label: 'Excluir', allowed: false }
          ]
        },
        {
          resourceType: 'precatorio',
          resourceName: 'Precatórios',
          icon: <FileText className="h-4 w-4" />,
          actions: [
            { action: 'view', label: 'Visualizar', allowed: false },
            { action: 'create', label: 'Criar', allowed: false },
            { action: 'edit', label: 'Editar', allowed: false },
            { action: 'delete', label: 'Excluir', allowed: false }
          ]
        },
        {
          resourceType: 'rpv',
          resourceName: 'RPVs',
          icon: <FileText className="h-4 w-4" />,
          actions: [
            { action: 'view', label: 'Visualizar', allowed: false },
            { action: 'create', label: 'Criar', allowed: false },
            { action: 'edit', label: 'Editar', allowed: false },
            { action: 'delete', label: 'Excluir', allowed: false }
          ]
        },
        {
          resourceType: 'tarefa',
          resourceName: 'Tarefas',
          icon: <ClipboardList className="h-4 w-4" />,
          actions: [
            { action: 'view', label: 'Visualizar', allowed: false },
            { action: 'create', label: 'Criar', allowed: false },
            { action: 'edit', label: 'Editar', allowed: false },
            { action: 'delete', label: 'Excluir', allowed: false },
            { action: 'view_all', label: 'Ver Todas', allowed: false }
          ]
        },
        {
          resourceType: 'documento',
          resourceName: 'Documentos',
          icon: <FileText className="h-4 w-4" />,
          actions: [
            { action: 'view', label: 'Visualizar', allowed: false },
            { action: 'create', label: 'Criar', allowed: false },
            { action: 'edit', label: 'Editar', allowed: false },
            { action: 'delete', label: 'Excluir', allowed: false }
          ]
        },
        {
          resourceType: 'relatorio',
          resourceName: 'Relatórios',
          icon: <BarChart4 className="h-4 w-4" />,
          actions: [
            { action: 'view_precatorio', label: 'Ver Precatórios', allowed: false },
            { action: 'view_rpv', label: 'Ver RPVs', allowed: false },
            { action: 'view_captacao', label: 'Ver Captação', allowed: false },
            { action: 'view_completo', label: 'Ver Completo', allowed: false }
          ]
        },
        {
          resourceType: 'user',
          resourceName: 'Usuários',
          icon: <Users className="h-4 w-4" />,
          actions: [
            { action: 'view', label: 'Visualizar', allowed: false },
            { action: 'create', label: 'Criar', allowed: false },
            { action: 'edit', label: 'Editar', allowed: false },
            { action: 'delete', label: 'Excluir', allowed: false }
          ]
        },
        {
          resourceType: 'system',
          resourceName: 'Sistema',
          icon: <Settings className="h-4 w-4" />,
          actions: [
            { action: 'configure', label: 'Configurar', allowed: false },
            { action: 'manage_roles', label: 'Gerenciar Cargos', allowed: false }
          ]
        }
      ];
      
      // Configurar páginas
      const pages: PageAccess[] = [
        { pagePath: '/dashboard', pageLabel: 'Dashboard', icon: <LayoutDashboard className="h-4 w-4" />, canAccess: true },
        { pagePath: '/customers', pageLabel: 'Clientes', icon: <Users className="h-4 w-4" />, canAccess: false },
        { pagePath: '/precatorios', pageLabel: 'Precatórios', icon: <FileText className="h-4 w-4" />, canAccess: false },
        { pagePath: '/rpv', pageLabel: 'RPVs', icon: <FileText className="h-4 w-4" />, canAccess: false },
        { pagePath: '/tasks', pageLabel: 'Tarefas', icon: <ClipboardList className="h-4 w-4" />, canAccess: false },
        { pagePath: '/calendar', pageLabel: 'Calendário', icon: <Calendar className="h-4 w-4" />, canAccess: false },
        { pagePath: '/documents', pageLabel: 'Documentos', icon: <FileText className="h-4 w-4" />, canAccess: false },
        { pagePath: '/reports', pageLabel: 'Relatórios', icon: <BarChart4 className="h-4 w-4" />, canAccess: false },
        { pagePath: '/users', pageLabel: 'Usuários', icon: <Users className="h-4 w-4" />, canAccess: false },
        { pagePath: '/settings', pageLabel: 'Configurações', icon: <Settings className="h-4 w-4" />, canAccess: false },
        { pagePath: '/roles', pageLabel: 'Cargos', icon: <Shield className="h-4 w-4" />, canAccess: false }
      ];
      
      // Marcar permissões existentes
      if (userPermissions?.role_permissions) {
        userPermissions.role_permissions.forEach(permission => {
          const resource = resources.find(r => r.resourceType === permission.resource_type);
          if (resource) {
            const action = resource.actions.find(a => a.action === permission.action);
            if (action) {
              action.allowed = permission.allowed;
            }
          }
        });
      }
      
      // Marcar acesso a páginas
      if (userPermissions?.page_access) {
        userPermissions.page_access.forEach(access => {
          const page = pages.find(p => p.pagePath === access.page_path);
          if (page) {
            page.canAccess = access.can_access;
          }
        });
      }
      
      // Configurar visibilidade de tarefas
      if (userPermissions?.task_visibility) {
        setTaskVisibility({
          canSeeOwnTasks: userPermissions.task_visibility.can_see_own_tasks,
          canSeeTeamTasks: userPermissions.task_visibility.can_see_team_tasks,
          canSeeAllTasks: userPermissions.task_visibility.can_see_all_tasks,
          visibleUserIds: userPermissions.task_visibility.visible_user_ids || []
        });
      }
      
      setResourcePermissions(resources);
      setPageAccess(pages);
    } catch (error) {
      console.error('Erro ao carregar permissões:', error);
      toast({
        title: "Erro ao carregar permissões",
        description: "Não foi possível carregar as permissões do usuário. Tente novamente mais tarde.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  // Carregar permissões ao montar o componente
  useEffect(() => {
    loadUserPermissions();
  }, [userId]);

  // Atualizar permissão de recurso
  const handleResourcePermissionChange = (resourceType: string, action: string, allowed: boolean) => {
    setResourcePermissions(prev => 
      prev.map(resource => 
        resource.resourceType === resourceType
          ? {
              ...resource,
              actions: resource.actions.map(a => 
                a.action === action ? { ...a, allowed } : a
              )
            }
          : resource
      )
    );
  };

  // Atualizar acesso a página
  const handlePageAccessChange = (pagePath: string, canAccess: boolean) => {
    setPageAccess(prev => 
      prev.map(page => 
        page.pagePath === pagePath
          ? { ...page, canAccess }
          : page
      )
    );
  };

  // Atualizar visibilidade de tarefas
  const handleTaskVisibilityChange = (field: keyof typeof taskVisibility, value: any) => {
    setTaskVisibility(prev => ({ ...prev, [field]: value }));
  };

  // Salvar permissões
  const handleSavePermissions = async () => {
    try {
      setSaving(true);
      
      // Salvar permissões de recursos
      for (const resource of resourcePermissions) {
        for (const action of resource.actions) {
          await updateUserPermission(
            userId,
            resource.resourceType,
            action.action,
            action.allowed
          );
        }
      }
      
      // Salvar acesso a páginas
      for (const page of pageAccess) {
        await updatePageAccess(
          userId,
          page.pagePath,
          page.canAccess
        );
      }
      
      // Salvar visibilidade de tarefas
      await updateTaskVisibilitySettings(
        userId,
        taskVisibility.canSeeOwnTasks,
        taskVisibility.canSeeTeamTasks,
        taskVisibility.canSeeAllTasks,
        taskVisibility.visibleUserIds
      );
      
      toast({
        title: "Permissões atualizadas",
        description: `As permissões de ${userName} foram atualizadas com sucesso.`
      });
      
      if (onPermissionsUpdated) {
        onPermissionsUpdated();
      }
    } catch (error) {
      console.error('Erro ao salvar permissões:', error);
      toast({
        title: "Erro ao salvar permissões",
        description: "Não foi possível salvar as permissões do usuário. Tente novamente mais tarde.",
        variant: "destructive"
      });
    } finally {
      setSaving(false);
    }
  };

  // Aplicar permissões de cargo
  const handleApplyRolePermissions = async (roleId: string) => {
    try {
      setSaving(true);
      
      // Chamar função para aplicar permissões padrão do cargo
      const { data, error } = await fetch('/api/apply-role-permissions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          roleId
        }),
      }).then(res => res.json());
      
      if (error) {
        throw new Error(error);
      }
      
      toast({
        title: "Permissões aplicadas",
        description: `As permissões padrão do cargo foram aplicadas com sucesso.`
      });
      
      // Recarregar permissões
      await loadUserPermissions();
      
      if (onPermissionsUpdated) {
        onPermissionsUpdated();
      }
    } catch (error) {
      console.error('Erro ao aplicar permissões do cargo:', error);
      toast({
        title: "Erro ao aplicar permissões",
        description: "Não foi possível aplicar as permissões padrão do cargo. Tente novamente mais tarde.",
        variant: "destructive"
      });
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Permissões do Usuário</h2>
          <p className="text-muted-foreground">
            Gerencie as permissões de {userName}
          </p>
        </div>
        
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => loadUserPermissions()}
            disabled={loading || saving}
          >
            {loading ? (
              <RefreshCw className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Atualizar
          </Button>
          
          <Button 
            onClick={handleSavePermissions}
            size="sm"
            disabled={loading || saving}
          >
            {saving ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            Salvar Permissões
          </Button>
        </div>
      </div>
      
      {loading ? (
        <Card>
          <CardContent className="p-6">
            <div className="space-y-4">
              <Skeleton className="h-8 w-1/3" />
              <Skeleton className="h-4 w-1/2" />
              <div className="space-y-2">
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
              </div>
            </div>
          </CardContent>
        </Card>
      ) : (
        <>
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">Informações do Usuário</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-4">
                <div className="bg-primary/10 p-3 rounded-full">
                  <User className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <p className="font-medium">{userName}</p>
                  <div className="flex items-center gap-2 mt-1">
                    <Badge variant="outline" className="text-xs">
                      ID: {userId}
                    </Badge>
                    {userRole && (
                      <Badge className="text-xs">
                        Cargo: {userRole}
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
              
              {availableRoles.length > 0 && (
                <div className="mt-4 pt-4 border-t">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Aplicar Permissões de Cargo</p>
                      <p className="text-sm text-muted-foreground">
                        Aplique as permissões padrão de um cargo a este usuário
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Select onValueChange={handleApplyRolePermissions}>
                        <SelectTrigger className="w-[200px]">
                          <SelectValue placeholder="Selecionar cargo" />
                        </SelectTrigger>
                        <SelectContent>
                          {availableRoles.map((role) => (
                            <SelectItem key={role.id} value={role.id}>
                              {role.nome}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="mt-2 p-2 bg-amber-50 dark:bg-amber-950/30 border border-amber-200 dark:border-amber-800 rounded-md text-sm flex items-start">
                    <AlertCircle className="h-4 w-4 text-amber-600 dark:text-amber-400 mt-0.5 mr-2 flex-shrink-0" />
                    <p className="text-amber-800 dark:text-amber-300">
                      Isso substituirá todas as permissões atuais pelas permissões padrão do cargo selecionado.
                    </p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-0">
              <Tabs defaultValue="recursos" value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="grid grid-cols-3">
                  <TabsTrigger value="recursos" className="flex items-center gap-2">
                    <Shield className="h-4 w-4" />
                    Recursos
                  </TabsTrigger>
                  <TabsTrigger value="paginas" className="flex items-center gap-2">
                    <LayoutDashboard className="h-4 w-4" />
                    Páginas
                  </TabsTrigger>
                  <TabsTrigger value="tarefas" className="flex items-center gap-2">
                    <ClipboardList className="h-4 w-4" />
                    Tarefas
                  </TabsTrigger>
                </TabsList>
              </Tabs>
            </CardHeader>
            <CardContent className="pt-6">
              <TabsContent value="recursos" className="mt-0">
                <ScrollArea className="h-[400px] pr-4">
                  <div className="space-y-6">
                    {resourcePermissions.map((resource) => (
                      <div key={resource.resourceType} className="space-y-2">
                        <div className="flex items-center gap-2">
                          <div className="bg-primary/10 p-1.5 rounded-md">
                            {resource.icon}
                          </div>
                          <h3 className="font-medium text-lg">{resource.resourceName}</h3>
                        </div>
                        <div className="space-y-2 pl-8">
                          {resource.actions.map((action) => (
                            <div 
                              key={`${resource.resourceType}-${action.action}`}
                              className="flex items-center justify-between py-2 px-4 rounded-md bg-muted/50"
                            >
                              <span>{action.label}</span>
                              <Switch
                                checked={action.allowed}
                                onCheckedChange={(checked) => 
                                  handleResourcePermissionChange(resource.resourceType, action.action, checked)
                                }
                              />
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </TabsContent>
              
              <TabsContent value="paginas" className="mt-0">
                <ScrollArea className="h-[400px] pr-4">
                  <div className="space-y-2">
                    {pageAccess.map((page) => (
                      <div 
                        key={page.pagePath}
                        className="flex items-center justify-between py-3 px-4 rounded-md bg-muted/50"
                      >
                        <div className="flex items-center gap-3">
                          <div className="bg-primary/10 p-1.5 rounded-md">
                            {page.icon}
                          </div>
                          <div>
                            <p className="font-medium">{page.pageLabel}</p>
                            <p className="text-xs text-muted-foreground">{page.pagePath}</p>
                          </div>
                        </div>
                        <Switch
                          checked={page.canAccess}
                          onCheckedChange={(checked) => 
                            handlePageAccessChange(page.pagePath, checked)
                          }
                        />
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </TabsContent>
              
              <TabsContent value="tarefas" className="mt-0">
                <div className="space-y-6">
                  <div>
                    <h3 className="font-medium text-lg mb-3">Visibilidade de Tarefas</h3>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between py-2 px-4 rounded-md bg-muted/50">
                        <div>
                          <p>Ver tarefas próprias</p>
                          <p className="text-xs text-muted-foreground">O usuário pode ver suas próprias tarefas</p>
                        </div>
                        <Switch
                          checked={taskVisibility.canSeeOwnTasks}
                          onCheckedChange={(checked) => 
                            handleTaskVisibilityChange('canSeeOwnTasks', checked)
                          }
                        />
                      </div>
                      
                      <div className="flex items-center justify-between py-2 px-4 rounded-md bg-muted/50">
                        <div>
                          <p>Ver tarefas da equipe</p>
                          <p className="text-xs text-muted-foreground">O usuário pode ver tarefas de sua equipe</p>
                        </div>
                        <Switch
                          checked={taskVisibility.canSeeTeamTasks}
                          onCheckedChange={(checked) => 
                            handleTaskVisibilityChange('canSeeTeamTasks', checked)
                          }
                        />
                      </div>
                      
                      <div className="flex items-center justify-between py-2 px-4 rounded-md bg-muted/50">
                        <div>
                          <p>Ver todas as tarefas</p>
                          <p className="text-xs text-muted-foreground">O usuário pode ver todas as tarefas do sistema</p>
                        </div>
                        <Switch
                          checked={taskVisibility.canSeeAllTasks}
                          onCheckedChange={(checked) => 
                            handleTaskVisibilityChange('canSeeAllTasks', checked)
                          }
                        />
                      </div>
                    </div>
                  </div>
                  
                  {/* Aqui poderia ter um seletor de usuários específicos cujas tarefas podem ser vistas */}
                </div>
              </TabsContent>
            </CardContent>
            <CardFooter className="border-t pt-6">
              <Button 
                onClick={handleSavePermissions}
                className="ml-auto"
                disabled={saving}
              >
                {saving ? (
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Save className="h-4 w-4 mr-2" />
                )}
                Salvar Permissões
              </Button>
            </CardFooter>
          </Card>
        </>
      )}
    </div>
  );
}
