import React from 'react';
import { DataMask, DataVisibilityGuard } from './DataVisibilityGuard';
import { Shield, Eye, DollarSign, User } from 'lucide-react';

interface ProtectedDataFieldProps {
  data: string | number | null | undefined;
  resourceType: string;
  dataType: 'sensitive' | 'financial' | 'personal' | 'normal';
  fallback?: React.ReactNode;
  showIcon?: boolean;
  className?: string;
  children?: React.ReactNode;
}

/**
 * Componente para proteger campos de dados específicos
 */
export function ProtectedDataField({
  data,
  resourceType,
  dataType,
  fallback,
  showIcon = false,
  className = '',
  children
}: ProtectedDataFieldProps) {
  if (!data && data !== 0) {
    return <span className={`text-muted-foreground ${className}`}>-</span>;
  }

  const getIcon = () => {
    switch (dataType) {
      case 'sensitive':
        return <Shield className="h-3 w-3" />;
      case 'financial':
        return <DollarSign className="h-3 w-3" />;
      case 'personal':
        return <User className="h-3 w-3" />;
      default:
        return <Eye className="h-3 w-3" />;
    }
  };

  const getRequiredPermission = () => {
    switch (dataType) {
      case 'sensitive':
        return { requireSensitiveData: true };
      case 'financial':
        return { requireFinancialData: true };
      case 'personal':
        return { requirePersonalData: true };
      default:
        return {};
    }
  };

  if (dataType === 'normal') {
    return (
      <span className={className}>
        {showIcon && getIcon()}
        {children || data}
      </span>
    );
  }

  return (
    <DataVisibilityGuard
      resourceType={resourceType}
      {...getRequiredPermission()}
      fallback={
        fallback || (
          <DataMask
            data={String(data)}
            resourceType={resourceType}
            maskType={dataType}
          />
        )
      }
    >
      <span className={className}>
        {showIcon && getIcon()}
        {children || data}
      </span>
    </DataVisibilityGuard>
  );
}

/**
 * Componente específico para valores monetários
 */
interface ProtectedCurrencyProps {
  value: number | null | undefined;
  resourceType: string;
  className?: string;
  showIcon?: boolean;
}

export function ProtectedCurrency({
  value,
  resourceType,
  className = '',
  showIcon = false
}: ProtectedCurrencyProps) {
  if (!value && value !== 0) {
    return <span className={`text-muted-foreground ${className}`}>-</span>;
  }

  const formattedValue = new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(value);

  return (
    <ProtectedDataField
      data={formattedValue}
      resourceType={resourceType}
      dataType="financial"
      showIcon={showIcon}
      className={className}
    />
  );
}

/**
 * Componente específico para documentos (CPF, CNPJ, etc.)
 */
interface ProtectedDocumentProps {
  document: string | null | undefined;
  resourceType: string;
  className?: string;
  showIcon?: boolean;
}

export function ProtectedDocument({
  document,
  resourceType,
  className = '',
  showIcon = false
}: ProtectedDocumentProps) {
  if (!document) {
    return <span className={`text-muted-foreground ${className}`}>-</span>;
  }

  return (
    <ProtectedDataField
      data={document}
      resourceType={resourceType}
      dataType="personal"
      showIcon={showIcon}
      className={className}
    />
  );
}

/**
 * Componente específico para informações de contato
 */
interface ProtectedContactProps {
  contact: string | null | undefined;
  resourceType: string;
  type: 'email' | 'phone' | 'address';
  className?: string;
  showIcon?: boolean;
}

export function ProtectedContact({
  contact,
  resourceType,
  type,
  className = '',
  showIcon = false
}: ProtectedContactProps) {
  if (!contact) {
    return <span className={`text-muted-foreground ${className}`}>-</span>;
  }

  return (
    <ProtectedDataField
      data={contact}
      resourceType={resourceType}
      dataType="personal"
      showIcon={showIcon}
      className={className}
    />
  );
}

/**
 * Componente para proteger seções inteiras de dados
 */
interface ProtectedSectionProps {
  resourceType: string;
  sectionType: 'sensitive' | 'financial' | 'personal';
  title?: string;
  fallbackMessage?: string;
  children: React.ReactNode;
  className?: string;
}

export function ProtectedSection({
  resourceType,
  sectionType,
  title,
  fallbackMessage,
  children,
  className = ''
}: ProtectedSectionProps) {
  const getRequiredPermission = () => {
    switch (sectionType) {
      case 'sensitive':
        return { requireSensitiveData: true };
      case 'financial':
        return { requireFinancialData: true };
      case 'personal':
        return { requirePersonalData: true };
      default:
        return {};
    }
  };

  const getDefaultFallback = () => {
    const message = fallbackMessage || `Você não tem permissão para visualizar ${
      sectionType === 'sensitive' ? 'dados sensíveis' :
      sectionType === 'financial' ? 'dados financeiros' :
      sectionType === 'personal' ? 'dados pessoais' : 'estes dados'
    }.`;

    return (
      <div className={`p-4 border border-dashed border-muted-foreground/30 rounded-lg text-center ${className}`}>
        <Shield className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
        <p className="text-sm text-muted-foreground">{message}</p>
      </div>
    );
  };

  return (
    <DataVisibilityGuard
      resourceType={resourceType}
      {...getRequiredPermission()}
      fallback={getDefaultFallback()}
    >
      <div className={className}>
        {title && (
          <h3 className="text-sm font-medium mb-2 flex items-center gap-2">
            {sectionType === 'sensitive' && <Shield className="h-4 w-4" />}
            {sectionType === 'financial' && <DollarSign className="h-4 w-4" />}
            {sectionType === 'personal' && <User className="h-4 w-4" />}
            {title}
          </h3>
        )}
        {children}
      </div>
    </DataVisibilityGuard>
  );
}

/**
 * Hook para verificar múltiplas permissões de uma vez
 */
export function useDataPermissions(resourceType: string) {
  const [permissions, setPermissions] = React.useState({
    canViewSensitive: false,
    canViewFinancial: false,
    canViewPersonal: false,
    canExport: false,
    loading: true
  });

  React.useEffect(() => {
    const checkPermissions = async () => {
      try {
        // Importar dinamicamente para evitar dependência circular
        const { DataVisibilityService } = await import('@/services/dataVisibilityService');
        const { useAuth } = await import('@/contexts/AuthContext');
        
        // Obter usuário atual
        const user = useAuth().user;
        if (!user) {
          setPermissions({
            canViewSensitive: false,
            canViewFinancial: false,
            canViewPersonal: false,
            canExport: false,
            loading: false
          });
          return;
        }

        const [canViewSensitive, canViewFinancial, canViewPersonal, canExport] = await Promise.all([
          DataVisibilityService.canUserViewSensitiveData(user.id, resourceType),
          DataVisibilityService.canUserViewFinancialData(user.id, resourceType),
          DataVisibilityService.canUserViewPersonalData(user.id, resourceType),
          DataVisibilityService.canUserExportData(user.id, resourceType)
        ]);

        setPermissions({
          canViewSensitive,
          canViewFinancial,
          canViewPersonal,
          canExport,
          loading: false
        });
      } catch (error) {
        console.error('Erro ao verificar permissões:', error);
        setPermissions({
          canViewSensitive: false,
          canViewFinancial: false,
          canViewPersonal: false,
          canExport: false,
          loading: false
        });
      }
    };

    checkPermissions();
  }, [resourceType]);

  return permissions;
}
