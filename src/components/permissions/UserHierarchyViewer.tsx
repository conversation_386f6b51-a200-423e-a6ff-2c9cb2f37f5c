import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { toast } from "sonner";
import {
  RefreshCw,
  Users,
  UserPlus,
  UserMinus,
  ChevronRight,
  ChevronDown,
  Info
} from "lucide-react";
import { useAuth } from '@/hooks/useAuth';
import { UserBasicInfo, UserHierarchyNode, UserRelationship } from '@/types/permissions';
import { supabase } from '@/lib/supabase';
import { manageUserRelationship } from '@/services/advancedPermissionsService';

interface UserHierarchyViewerProps {
  users: UserBasicInfo[];
}

export function UserHierarchyViewer({ users }: UserHierarchyViewerProps) {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [relationships, setRelationships] = useState<UserRelationship[]>([]);
  const [hierarchyData, setHierarchyData] = useState<UserHierarchyNode[]>([]);
  const [expandedNodes, setExpandedNodes] = useState<string[]>([]);
  const [isAddRelationshipOpen, setIsAddRelationshipOpen] = useState(false);
  const [selectedSupervisor, setSelectedSupervisor] = useState<string | null>(null);
  const [selectedSubordinate, setSelectedSubordinate] = useState<string | null>(null);

  // Carregar relacionamentos
  useEffect(() => {
    if (users.length > 0) {
      loadRelationships();
    }
  }, [users]);

  // Construir hierarquia quando relacionamentos mudarem
  useEffect(() => {
    buildHierarchy();
  }, [relationships, users]);

  // Carregar relacionamentos do banco de dados
  const loadRelationships = async () => {
    try {
      setLoading(true);
      
      const { data, error } = await supabase
        .from('user_relationships')
        .select('*');
      
      if (error) {
        console.error('Erro ao carregar relacionamentos:', error);
        throw error;
      }
      
      setRelationships(data || []);
    } catch (error) {
      console.error('Erro ao carregar relacionamentos:', error);
      toast.error("Erro ao carregar hierarquia", {
        description: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    } finally {
      setLoading(false);
    }
  };

  // Construir hierarquia de usuários
  const buildHierarchy = () => {
    if (!users.length || !relationships.length) {
      // Se não há relacionamentos, mostrar todos os usuários no nível raiz
      const rootNodes: UserHierarchyNode[] = users.map(user => ({
        id: user.id,
        name: user.name,
        role: user.role,
        avatar_url: user.avatar_url,
        children: []
      }));
      
      setHierarchyData(rootNodes);
      return;
    }
    
    // Identificar usuários que são supervisores
    const supervisorIds = new Set(relationships.map(rel => rel.supervisor_id));
    
    // Identificar usuários que são subordinados
    const subordinateIds = new Set(relationships.map(rel => rel.subordinate_id));
    
    // Encontrar usuários raiz (supervisores que não são subordinados de ninguém)
    const rootUserIds = [...supervisorIds].filter(id => !subordinateIds.has(id));
    
    // Se não houver usuários raiz, usar todos os supervisores
    const rootIds = rootUserIds.length > 0 ? rootUserIds : [...supervisorIds];
    
    // Construir nós para usuários raiz
    const rootNodes: UserHierarchyNode[] = rootIds.map(id => {
      const userInfo = users.find(u => u.id === id);
      return {
        id,
        name: userInfo?.name || 'Usuário desconhecido',
        role: userInfo?.role,
        avatar_url: userInfo?.avatar_url,
        children: buildChildrenNodes(id)
      };
    });
    
    // Adicionar usuários que não estão em nenhum relacionamento
    const usersInRelationships = new Set([...supervisorIds, ...subordinateIds]);
    const standaloneUsers = users.filter(u => !usersInRelationships.has(u.id));
    
    const standaloneNodes: UserHierarchyNode[] = standaloneUsers.map(user => ({
      id: user.id,
      name: user.name,
      role: user.role,
      avatar_url: user.avatar_url,
      children: []
    }));
    
    setHierarchyData([...rootNodes, ...standaloneNodes]);
  };

  // Construir nós filhos recursivamente
  const buildChildrenNodes = (parentId: string): UserHierarchyNode[] => {
    // Encontrar todos os subordinados diretos deste supervisor
    const childRelationships = relationships.filter(rel => rel.supervisor_id === parentId);
    
    if (childRelationships.length === 0) {
      return [];
    }
    
    // Construir nós para cada subordinado
    return childRelationships.map(rel => {
      const userInfo = users.find(u => u.id === rel.subordinate_id);
      return {
        id: rel.subordinate_id,
        name: userInfo?.name || 'Usuário desconhecido',
        role: userInfo?.role,
        avatar_url: userInfo?.avatar_url,
        children: buildChildrenNodes(rel.subordinate_id)
      };
    });
  };

  // Alternar expansão de um nó
  const toggleNodeExpansion = (nodeId: string) => {
    setExpandedNodes(prev => 
      prev.includes(nodeId)
        ? prev.filter(id => id !== nodeId)
        : [...prev, nodeId]
    );
  };

  // Adicionar relacionamento
  const handleAddRelationship = async () => {
    if (!selectedSupervisor || !selectedSubordinate) {
      toast.error("Seleção incompleta", {
        description: "Selecione um supervisor e um subordinado."
      });
      return;
    }
    
    if (selectedSupervisor === selectedSubordinate) {
      toast.error("Seleção inválida", {
        description: "Um usuário não pode ser supervisor de si mesmo."
      });
      return;
    }
    
    // Verificar se já existe este relacionamento
    const existingRelationship = relationships.find(
      rel => rel.supervisor_id === selectedSupervisor && rel.subordinate_id === selectedSubordinate
    );
    
    if (existingRelationship) {
      toast.error("Relacionamento existente", {
        description: "Este relacionamento já existe."
      });
      return;
    }
    
    // Verificar ciclos na hierarquia
    if (wouldCreateCycle(selectedSupervisor, selectedSubordinate)) {
      toast.error("Ciclo detectado", {
        description: "Esta relação criaria um ciclo na hierarquia."
      });
      return;
    }
    
    try {
      await manageUserRelationship(
        user!.id,
        selectedSupervisor,
        selectedSubordinate,
        'create'
      );
      
      // Adicionar ao estado local
      setRelationships(prev => [
        ...prev,
        {
          id: `temp-${Date.now()}`,
          supervisor_id: selectedSupervisor,
          subordinate_id: selectedSubordinate,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      ]);
      
      toast.success("Relacionamento adicionado", {
        description: "O relacionamento foi adicionado com sucesso."
      });
      
      // Fechar modal e limpar seleção
      setIsAddRelationshipOpen(false);
      setSelectedSupervisor(null);
      setSelectedSubordinate(null);
    } catch (error) {
      console.error('Erro ao adicionar relacionamento:', error);
      toast.error("Erro ao adicionar relacionamento", {
        description: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  };

  // Remover relacionamento
  const handleRemoveRelationship = async (supervisorId: string, subordinateId: string) => {
    try {
      await manageUserRelationship(
        user!.id,
        supervisorId,
        subordinateId,
        'delete'
      );
      
      // Remover do estado local
      setRelationships(prev => 
        prev.filter(rel => 
          !(rel.supervisor_id === supervisorId && rel.subordinate_id === subordinateId)
        )
      );
      
      toast.success("Relacionamento removido", {
        description: "O relacionamento foi removido com sucesso."
      });
    } catch (error) {
      console.error('Erro ao remover relacionamento:', error);
      toast.error("Erro ao remover relacionamento", {
        description: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  };

  // Verificar se adicionar um relacionamento criaria um ciclo
  const wouldCreateCycle = (supervisorId: string, subordinateId: string): boolean => {
    // Verificar se o subordinado é supervisor do supervisor (diretamente)
    const directCycle = relationships.some(
      rel => rel.supervisor_id === subordinateId && rel.subordinate_id === supervisorId
    );
    
    if (directCycle) return true;
    
    // Verificar ciclos mais longos (subordinado é supervisor indireto do supervisor)
    const isIndirectSupervisor = (currentId: string, targetId: string, visited = new Set<string>()): boolean => {
      if (visited.has(currentId)) return false; // Evitar loops infinitos
      visited.add(currentId);
      
      // Verificar subordinados diretos
      const directSubordinates = relationships
        .filter(rel => rel.supervisor_id === currentId)
        .map(rel => rel.subordinate_id);
      
      // Se o alvo é um subordinado direto, há um ciclo
      if (directSubordinates.includes(targetId)) return true;
      
      // Verificar recursivamente para cada subordinado
      return directSubordinates.some(subId => isIndirectSupervisor(subId, targetId, visited));
    };
    
    return isIndirectSupervisor(subordinateId, supervisorId);
  };

  // Renderizar nó da hierarquia
  const renderHierarchyNode = (node: UserHierarchyNode, level = 0) => {
    const hasChildren = node.children && node.children.length > 0;
    const isExpanded = expandedNodes.includes(node.id);
    
    return (
      <div key={node.id} className="user-hierarchy-node">
        <div 
          className={`flex items-center py-2 px-2 rounded-md hover:bg-accent ${level > 0 ? 'ml-6' : ''}`}
          style={{ paddingLeft: `${level * 16 + 8}px` }}
        >
          {hasChildren ? (
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 p-0 mr-1"
              onClick={() => toggleNodeExpansion(node.id)}
            >
              {isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
            </Button>
          ) : (
            <div className="w-7"></div>
          )}
          
          <Avatar className="h-8 w-8 mr-2">
            <AvatarImage src={node.avatar_url} />
            <AvatarFallback>{node.name.substring(0, 2).toUpperCase()}</AvatarFallback>
          </Avatar>
          
          <div className="flex-1">
            <div className="font-medium">{node.name}</div>
            {node.role && (
              <div className="text-xs text-muted-foreground">{node.role}</div>
            )}
          </div>
          
          {level > 0 && (
            <Button
              variant="ghost"
              size="icon"
              className="h-7 w-7"
              onClick={() => {
                // Encontrar o supervisor deste nó
                const supervisor = findSupervisor(node.id);
                if (supervisor) {
                  handleRemoveRelationship(supervisor.id, node.id);
                }
              }}
            >
              <UserMinus className="h-4 w-4 text-destructive" />
            </Button>
          )}
        </div>
        
        {hasChildren && isExpanded && (
          <div className="pl-4 border-l-2 border-border ml-4">
            {node.children!.map(child => renderHierarchyNode(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  // Encontrar o supervisor de um nó
  const findSupervisor = (nodeId: string): UserHierarchyNode | null => {
    const relationship = relationships.find(rel => rel.subordinate_id === nodeId);
    if (!relationship) return null;
    
    const supervisorId = relationship.supervisor_id;
    const supervisorInfo = users.find(u => u.id === supervisorId);
    
    if (!supervisorInfo) return null;
    
    return {
      id: supervisorInfo.id,
      name: supervisorInfo.name,
      role: supervisorInfo.role,
      avatar_url: supervisorInfo.avatar_url
    };
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Hierarquia de Usuários</CardTitle>
            <CardDescription>
              Gerencie relacionamentos hierárquicos entre usuários
            </CardDescription>
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => loadRelationships()}
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Atualizar
            </Button>
            
            <Button
              size="sm"
              onClick={() => setIsAddRelationshipOpen(true)}
            >
              <UserPlus className="h-4 w-4 mr-2" />
              Adicionar Relacionamento
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <ScrollArea className="h-[600px] pr-4">
          {loading ? (
            <div className="flex flex-col items-center justify-center h-40">
              <RefreshCw className="h-8 w-8 animate-spin mb-4" />
              <div>Carregando hierarquia...</div>
            </div>
          ) : hierarchyData.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-40 text-muted-foreground">
              <Info className="h-10 w-10 mb-2" />
              <p>Nenhum relacionamento encontrado</p>
              <Button
                variant="outline"
                size="sm"
                className="mt-4"
                onClick={() => setIsAddRelationshipOpen(true)}
              >
                <UserPlus className="h-4 w-4 mr-2" />
                Adicionar Relacionamento
              </Button>
            </div>
          ) : (
            <div className="space-y-1">
              {hierarchyData.map(node => renderHierarchyNode(node))}
            </div>
          )}
        </ScrollArea>
      </CardContent>
      
      {/* Modal para adicionar relacionamento */}
      <Dialog open={isAddRelationshipOpen} onOpenChange={setIsAddRelationshipOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Adicionar Relacionamento</DialogTitle>
            <DialogDescription>
              Selecione um supervisor e um subordinado para criar um relacionamento hierárquico.
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <label htmlFor="supervisor" className="text-sm font-medium">
                Supervisor
              </label>
              <Select
                value={selectedSupervisor || ''}
                onValueChange={setSelectedSupervisor}
              >
                <SelectTrigger id="supervisor">
                  <SelectValue placeholder="Selecione um supervisor" />
                </SelectTrigger>
                <SelectContent>
                  {users.map(user => (
                    <SelectItem key={`sup-${user.id}`} value={user.id}>
                      <div className="flex items-center">
                        <Avatar className="h-6 w-6 mr-2">
                          <AvatarImage src={user.avatar_url} />
                          <AvatarFallback>{user.name.substring(0, 2).toUpperCase()}</AvatarFallback>
                        </Avatar>
                        <span>{user.name}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="grid gap-2">
              <label htmlFor="subordinate" className="text-sm font-medium">
                Subordinado
              </label>
              <Select
                value={selectedSubordinate || ''}
                onValueChange={setSelectedSubordinate}
              >
                <SelectTrigger id="subordinate">
                  <SelectValue placeholder="Selecione um subordinado" />
                </SelectTrigger>
                <SelectContent>
                  {users.map(user => (
                    <SelectItem key={`sub-${user.id}`} value={user.id}>
                      <div className="flex items-center">
                        <Avatar className="h-6 w-6 mr-2">
                          <AvatarImage src={user.avatar_url} />
                          <AvatarFallback>{user.name.substring(0, 2).toUpperCase()}</AvatarFallback>
                        </Avatar>
                        <span>{user.name}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddRelationshipOpen(false)}>
              Cancelar
            </Button>
            <Button onClick={handleAddRelationship}>
              Adicionar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
}
