import React from 'react';
import { usePermissions } from '@/hooks/usePermissions';

interface PermissionGuardProps {
  action: string;
  resource: string;
  resourceId?: string;
  fallback?: React.ReactNode;
  children: React.ReactNode;
}

/**
 * Componente que renderiza seu conteúdo apenas se o usuário tiver a permissão especificada
 */
export function PermissionGuard({
  action,
  resource,
  resourceId,
  fallback = null,
  children
}: PermissionGuardProps) {
  const { canSee } = usePermissions();
  
  // Verificar se o usuário tem permissão
  const hasPermission = canSee(action, resource, resourceId);
  
  if (!hasPermission) {
    return <>{fallback}</>;
  }
  
  return <>{children}</>;
}
