import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Shield, 
  Users, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  RefreshCw, 
  Download,
  Eye,
  EyeOff,
  Settings,
  BarChart3
} from "lucide-react";
import { toast } from "sonner";
import { PermissionAuditService, UserPermissionSummary } from "@/services/permissionAuditService";
import { useAuth } from "@/contexts/AuthContext";

interface PermissionAuditDashboardProps {
  className?: string;
}

export function PermissionAuditDashboard({ className = '' }: PermissionAuditDashboardProps) {
  const { getAllUsers, user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [auditData, setAuditData] = useState<{
    summary: {
      totalUsers: number;
      usersWithIssues: number;
      averageScore: number;
      commonIssues: string[];
    };
    userSummaries: UserPermissionSummary[];
  } | null>(null);
  const [selectedUser, setSelectedUser] = useState<UserPermissionSummary | null>(null);

  // Verificar se o usuário é admin
  const isAdmin = user?.role === 'admin';

  useEffect(() => {
    if (isAdmin) {
      runAudit();
    }
  }, [isAdmin]);

  const runAudit = async () => {
    if (!isAdmin) {
      toast.error("Apenas administradores podem executar auditoria de permissões");
      return;
    }

    try {
      setLoading(true);
      
      // Obter todos os usuários
      const { data: users, error } = await getAllUsers();
      if (error || !users) {
        toast.error("Erro ao obter usuários: " + (error || "Dados não encontrados"));
        return;
      }

      // Executar auditoria
      const auditResult = await PermissionAuditService.generateAuditReport(users);
      setAuditData(auditResult);
      
      toast.success(`Auditoria concluída! ${auditResult.summary.totalUsers} usuários analisados.`);
    } catch (error) {
      console.error('Erro na auditoria:', error);
      toast.error("Erro ao executar auditoria de permissões");
    } finally {
      setLoading(false);
    }
  };

  const applyFixes = async (userId: string, userRole: string) => {
    try {
      const { applied, failed } = await PermissionAuditService.applyAutomaticFixes(userId, userRole);
      
      if (applied.length > 0) {
        toast.success(`Correções aplicadas: ${applied.join(', ')}`);
      }
      
      if (failed.length > 0) {
        toast.error(`Falhas: ${failed.join(', ')}`);
      }

      // Reexecutar auditoria
      await runAudit();
    } catch (error) {
      console.error('Erro ao aplicar correções:', error);
      toast.error("Erro ao aplicar correções automáticas");
    }
  };

  const exportReport = () => {
    if (!auditData) return;

    const reportData = {
      timestamp: new Date().toISOString(),
      summary: auditData.summary,
      users: auditData.userSummaries.map(user => ({
        email: user.userEmail,
        name: user.userName,
        role: user.role,
        score: user.overallScore,
        issues: user.criticalIssues,
        permissions: user.pagePermissions.map(p => ({
          page: p.page,
          hasAccess: p.hasAccess,
          canView: p.canView,
          canExport: p.canExport,
          issues: p.issues
        }))
      }))
    };

    const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `permission-audit-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast.success("Relatório exportado com sucesso!");
  };

  if (!isAdmin) {
    return (
      <Alert className="border-orange-200 bg-orange-50">
        <Shield className="h-4 w-4 text-orange-600" />
        <AlertDescription className="text-orange-800">
          Apenas administradores podem acessar a auditoria de permissões.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Shield className="h-6 w-6" />
            Auditoria de Permissões
          </h2>
          <p className="text-muted-foreground">
            Análise completa das permissões de usuários no sistema
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={runAudit}
            disabled={loading}
            variant="outline"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            {loading ? 'Auditando...' : 'Executar Auditoria'}
          </Button>
          {auditData && (
            <Button onClick={exportReport} variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Exportar Relatório
            </Button>
          )}
        </div>
      </div>

      {/* Resumo Geral */}
      {auditData && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total de Usuários</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{auditData.summary.totalUsers}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Usuários com Problemas</CardTitle>
              <AlertTriangle className="h-4 w-4 text-orange-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">
                {auditData.summary.usersWithIssues}
              </div>
              <p className="text-xs text-muted-foreground">
                {Math.round((auditData.summary.usersWithIssues / auditData.summary.totalUsers) * 100)}% do total
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Score Médio</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{auditData.summary.averageScore}%</div>
              <Progress value={auditData.summary.averageScore} className="mt-2" />
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Status Geral</CardTitle>
              {auditData.summary.averageScore >= 80 ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <XCircle className="h-4 w-4 text-red-500" />
              )}
            </CardHeader>
            <CardContent>
              <div className={`text-sm font-medium ${
                auditData.summary.averageScore >= 80 ? 'text-green-600' : 'text-red-600'
              }`}>
                {auditData.summary.averageScore >= 80 ? 'Bom' : 'Requer Atenção'}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Problemas Comuns */}
      {auditData && auditData.summary.commonIssues.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-orange-500" />
              Problemas Mais Comuns
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {auditData.summary.commonIssues.map((issue, index) => (
                <Alert key={index} className="border-orange-200 bg-orange-50">
                  <AlertDescription className="text-orange-800">
                    {issue}
                  </AlertDescription>
                </Alert>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Detalhes por Usuário */}
      {auditData && (
        <Tabs defaultValue="overview" className="w-full">
          <TabsList>
            <TabsTrigger value="overview">Visão Geral</TabsTrigger>
            <TabsTrigger value="details">Detalhes por Usuário</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid gap-4">
              {auditData.userSummaries.map((userSummary) => (
                <Card key={userSummary.userId} className="cursor-pointer hover:shadow-md transition-shadow"
                      onClick={() => setSelectedUser(userSummary)}>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className={`w-3 h-3 rounded-full ${
                          userSummary.overallScore >= 80 ? 'bg-green-500' :
                          userSummary.overallScore >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                        }`} />
                        <div>
                          <p className="font-medium">{userSummary.userName}</p>
                          <p className="text-sm text-muted-foreground">{userSummary.userEmail}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <Badge variant={userSummary.role === 'admin' ? 'default' : 'secondary'}>
                          {userSummary.role}
                        </Badge>
                        <div className="text-right">
                          <p className="font-medium">{userSummary.overallScore}%</p>
                          {userSummary.criticalIssues.length > 0 && (
                            <p className="text-sm text-red-600">
                              {userSummary.criticalIssues.length} problema(s)
                            </p>
                          )}
                        </div>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={(e) => {
                            e.stopPropagation();
                            applyFixes(userSummary.userId, userSummary.role);
                          }}
                        >
                          <Settings className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="details">
            {selectedUser ? (
              <Card>
                <CardHeader>
                  <CardTitle>
                    Detalhes de Permissões - {selectedUser.userName}
                  </CardTitle>
                  <div className="flex gap-2">
                    <Badge variant={selectedUser.role === 'admin' ? 'default' : 'secondary'}>
                      {selectedUser.role}
                    </Badge>
                    <Badge variant={selectedUser.overallScore >= 80 ? 'default' : 'destructive'}>
                      Score: {selectedUser.overallScore}%
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {selectedUser.pagePermissions.map((permission) => (
                      <div key={permission.page} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium capitalize">{permission.page}</h4>
                          <div className="flex gap-2">
                            {permission.hasAccess ? (
                              <CheckCircle className="h-4 w-4 text-green-500" />
                            ) : (
                              <XCircle className="h-4 w-4 text-red-500" />
                            )}
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
                          <div className="flex items-center gap-1">
                            <Eye className="h-3 w-3" />
                            <span className={permission.canView ? 'text-green-600' : 'text-red-600'}>
                              Visualizar
                            </span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Download className="h-3 w-3" />
                            <span className={permission.canExport ? 'text-green-600' : 'text-red-600'}>
                              Exportar
                            </span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Shield className="h-3 w-3" />
                            <span className={permission.canViewSensitive ? 'text-green-600' : 'text-red-600'}>
                              Dados Sensíveis
                            </span>
                          </div>
                          <div className="flex items-center gap-1">
                            <BarChart3 className="h-3 w-3" />
                            <span className={permission.canViewFinancial ? 'text-green-600' : 'text-red-600'}>
                              Dados Financeiros
                            </span>
                          </div>
                        </div>

                        {permission.issues.length > 0 && (
                          <div className="mt-2 space-y-1">
                            {permission.issues.map((issue, index) => (
                              <Alert key={index} className="border-red-200 bg-red-50">
                                <AlertDescription className="text-red-800 text-sm">
                                  {issue}
                                </AlertDescription>
                              </Alert>
                            ))}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="p-8 text-center">
                  <EyeOff className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <p className="text-muted-foreground">
                    Selecione um usuário na aba "Visão Geral" para ver os detalhes das permissões
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
}
