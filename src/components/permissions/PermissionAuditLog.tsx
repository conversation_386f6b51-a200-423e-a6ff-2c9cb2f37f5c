import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { toast } from "sonner";
import {
  Search,
  RefreshCw,
  Filter,
  Clock,
  User,
  ShieldAlert,
  Loader2,
  FileText,
  ArrowUpDown,
  Calendar,
  Eye
} from "lucide-react";
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/lib/supabase';
import { UserBasicInfo } from '@/types/permissions';
import { format, parseISO } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface PermissionAuditLogProps {
  users: UserBasicInfo[];
}

interface PermissionLog {
  id: string;
  admin_id: string;
  user_id: string;
  action: string;
  resource_type: string;
  resource_id?: string;
  old_value?: any;
  new_value?: any;
  created_at: string;
}

export function PermissionAuditLog({ users }: PermissionAuditLogProps) {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [logs, setLogs] = useState<PermissionLog[]>([]);
  const [filteredLogs, setFilteredLogs] = useState<PermissionLog[]>([]);
  const [selectedLog, setSelectedLog] = useState<PermissionLog | null>(null);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterAction, setFilterAction] = useState<string>('all');
  const [filterResourceType, setFilterResourceType] = useState<string>('all');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // Carregar logs de auditoria
  useEffect(() => {
    loadAuditLogs();
  }, []);

  // Aplicar filtros quando os critérios mudarem
  useEffect(() => {
    applyFilters();
  }, [logs, searchQuery, filterAction, filterResourceType, sortDirection]);

  // Carregar logs de auditoria
  const loadAuditLogs = async () => {
    try {
      setLoading(true);
      
      const { data, error } = await supabase
        .from('permission_logs')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (error) {
        console.error('Erro ao carregar logs de auditoria:', error);
        toast.error("Erro ao carregar logs", {
          description: error.message
        });
        return;
      }
      
      setLogs(data || []);
    } catch (error) {
      console.error('Erro ao carregar logs de auditoria:', error);
      toast.error("Erro ao carregar logs", {
        description: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    } finally {
      setLoading(false);
    }
  };

  // Aplicar filtros aos logs
  const applyFilters = () => {
    let filtered = [...logs];
    
    // Aplicar filtro de busca
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(log => {
        const adminName = users.find(u => u.id === log.admin_id)?.name.toLowerCase() || '';
        const userName = users.find(u => u.id === log.user_id)?.name.toLowerCase() || '';
        
        return adminName.includes(query) ||
               userName.includes(query) ||
               log.action.toLowerCase().includes(query) ||
               log.resource_type.toLowerCase().includes(query) ||
               (log.resource_id && log.resource_id.toLowerCase().includes(query));
      });
    }
    
    // Aplicar filtro de ação
    if (filterAction !== 'all') {
      filtered = filtered.filter(log => log.action === filterAction);
    }
    
    // Aplicar filtro de tipo de recurso
    if (filterResourceType !== 'all') {
      filtered = filtered.filter(log => log.resource_type === filterResourceType);
    }
    
    // Aplicar ordenação
    filtered.sort((a, b) => {
      const dateA = new Date(a.created_at).getTime();
      const dateB = new Date(b.created_at).getTime();
      
      return sortDirection === 'asc' ? dateA - dateB : dateB - dateA;
    });
    
    setFilteredLogs(filtered);
  };

  // Formatar data
  const formatDate = (dateString: string) => {
    try {
      return format(parseISO(dateString), "dd/MM/yyyy 'às' HH:mm", { locale: ptBR });
    } catch (error) {
      return dateString;
    }
  };

  // Formatar ação para exibição
  const formatAction = (action: string) => {
    switch (action) {
      case 'update_kanban_column_permission':
        return 'Atualizar permissão de coluna';
      case 'assign_kanban_view':
        return 'Atribuir visualização';
      case 'unassign_kanban_view':
        return 'Remover visualização';
      case 'create_custom_view':
        return 'Criar visualização';
      case 'update_custom_view':
        return 'Atualizar visualização';
      case 'delete_custom_view':
        return 'Excluir visualização';
      default:
        return action.replace(/_/g, ' ');
    }
  };

  // Formatar tipo de recurso para exibição
  const formatResourceType = (resourceType: string) => {
    switch (resourceType) {
      case 'kanban_column':
        return 'Coluna do Kanban';
      case 'kanban_custom_view':
        return 'Visualização do Kanban';
      case 'precatorio':
        return 'Precatório';
      case 'rpv':
        return 'RPV';
      default:
        return resourceType.replace(/_/g, ' ');
    }
  };

  // Obter ações únicas para o filtro
  const getUniqueActions = () => {
    const actions = new Set<string>();
    logs.forEach(log => actions.add(log.action));
    return Array.from(actions);
  };

  // Obter tipos de recursos únicos para o filtro
  const getUniqueResourceTypes = () => {
    const types = new Set<string>();
    logs.forEach(log => types.add(log.resource_type));
    return Array.from(types);
  };

  // Atualizar manualmente
  const handleRefresh = async () => {
    setRefreshing(true);
    await loadAuditLogs();
    setRefreshing(false);
  };

  // Alternar direção de ordenação
  const toggleSortDirection = () => {
    setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc');
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Logs de Auditoria de Permissões</CardTitle>
            <CardDescription>
              Visualize o histórico de alterações de permissões no sistema
            </CardDescription>
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={refreshing}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              Atualizar
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="flex items-center justify-between mb-4 gap-2">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Buscar logs..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          
          <Select
            value={filterAction}
            onValueChange={setFilterAction}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filtrar por ação" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Todas as ações</SelectItem>
              {getUniqueActions().map(action => (
                <SelectItem key={action} value={action}>
                  {formatAction(action)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Select
            value={filterResourceType}
            onValueChange={setFilterResourceType}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filtrar por recurso" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Todos os recursos</SelectItem>
              {getUniqueResourceTypes().map(type => (
                <SelectItem key={type} value={type}>
                  {formatResourceType(type)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        {loading ? (
          <div className="flex flex-col items-center justify-center h-40">
            <Loader2 className="h-8 w-8 animate-spin mb-4" />
            <div>Carregando logs de auditoria...</div>
          </div>
        ) : filteredLogs.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-40 text-muted-foreground">
            <ShieldAlert className="h-10 w-10 mb-2" />
            <p>Nenhum log de auditoria encontrado</p>
          </div>
        ) : (
          <ScrollArea className="h-[500px]">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[180px]">
                    <div className="flex items-center gap-1 cursor-pointer" onClick={toggleSortDirection}>
                      <Calendar className="h-4 w-4" />
                      Data/Hora
                      <ArrowUpDown className="h-3 w-3 ml-1" />
                    </div>
                  </TableHead>
                  <TableHead>Administrador</TableHead>
                  <TableHead>Usuário</TableHead>
                  <TableHead>Ação</TableHead>
                  <TableHead>Recurso</TableHead>
                  <TableHead className="text-right">Detalhes</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredLogs.map(log => {
                  const admin = users.find(u => u.id === log.admin_id);
                  const targetUser = users.find(u => u.id === log.user_id);
                  
                  return (
                    <TableRow key={log.id}>
                      <TableCell className="font-mono text-xs">
                        {formatDate(log.created_at)}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4 text-primary" />
                          <span>{admin?.name || log.admin_id}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4 text-muted-foreground" />
                          <span>{targetUser?.name || log.user_id}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {formatAction(log.action)}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant="secondary">
                          {formatResourceType(log.resource_type)}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setSelectedLog(log);
                            setIsDetailsDialogOpen(true);
                          }}
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          Detalhes
                        </Button>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </ScrollArea>
        )}
      </CardContent>
      
      {/* Diálogo de detalhes do log */}
      <Dialog open={isDetailsDialogOpen} onOpenChange={setIsDetailsDialogOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Detalhes do Log de Auditoria</DialogTitle>
            <DialogDescription>
              Informações detalhadas sobre a alteração de permissão.
            </DialogDescription>
          </DialogHeader>
          
          {selectedLog && (
            <div className="py-4">
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div className="space-y-1">
                  <p className="text-sm font-medium">Data e Hora</p>
                  <p className="text-sm">{formatDate(selectedLog.created_at)}</p>
                </div>
                
                <div className="space-y-1">
                  <p className="text-sm font-medium">Ação</p>
                  <Badge variant="outline">
                    {formatAction(selectedLog.action)}
                  </Badge>
                </div>
                
                <div className="space-y-1">
                  <p className="text-sm font-medium">Administrador</p>
                  <p className="text-sm">
                    {users.find(u => u.id === selectedLog.admin_id)?.name || selectedLog.admin_id}
                  </p>
                </div>
                
                <div className="space-y-1">
                  <p className="text-sm font-medium">Usuário Afetado</p>
                  <p className="text-sm">
                    {users.find(u => u.id === selectedLog.user_id)?.name || selectedLog.user_id}
                  </p>
                </div>
                
                <div className="space-y-1">
                  <p className="text-sm font-medium">Tipo de Recurso</p>
                  <Badge variant="secondary">
                    {formatResourceType(selectedLog.resource_type)}
                  </Badge>
                </div>
                
                <div className="space-y-1">
                  <p className="text-sm font-medium">ID do Recurso</p>
                  <p className="text-sm font-mono">{selectedLog.resource_id || 'N/A'}</p>
                </div>
              </div>
              
              <div className="space-y-4">
                <div className="space-y-2">
                  <p className="text-sm font-medium">Valor Anterior</p>
                  <div className="bg-muted p-3 rounded-md overflow-auto max-h-[200px]">
                    <pre className="text-xs">
                      {selectedLog.old_value ? JSON.stringify(selectedLog.old_value, null, 2) : 'N/A'}
                    </pre>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <p className="text-sm font-medium">Novo Valor</p>
                  <div className="bg-muted p-3 rounded-md overflow-auto max-h-[200px]">
                    <pre className="text-xs">
                      {selectedLog.new_value ? JSON.stringify(selectedLog.new_value, null, 2) : 'N/A'}
                    </pre>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          <DialogFooter>
            <Button onClick={() => setIsDetailsDialogOpen(false)}>
              Fechar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
}
