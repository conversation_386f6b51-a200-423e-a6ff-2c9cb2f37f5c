import React from 'react';
import { usePermissions } from '@/hooks/usePermissions';
import { NoPermissionMessage } from './NoPermissionMessage';

interface PermissionSectionProps {
  action: string;
  resource: string;
  resourceId?: string;
  fallback?: React.ReactNode;
  showMessage?: boolean;
  messageTitle?: string;
  messageDescription?: string;
  children: React.ReactNode;
}

/**
 * Componente que renderiza uma seção apenas se o usuário tiver a permissão especificada
 */
export function PermissionSection({
  action,
  resource,
  resourceId,
  fallback,
  showMessage = false,
  messageTitle,
  messageDescription,
  children
}: PermissionSectionProps) {
  const { canSee } = usePermissions();
  
  // Verificar se o usuário tem permissão
  const hasPermission = canSee(action, resource, resourceId);
  
  if (!hasPermission) {
    // Se não tem permissão, mostrar mensagem ou fallback
    if (showMessage) {
      return (
        <NoPermissionMessage
          title={messageTitle}
          description={messageDescription}
        />
      );
    }
    
    return fallback ? <>{fallback}</> : null;
  }
  
  return <>{children}</>;
}
