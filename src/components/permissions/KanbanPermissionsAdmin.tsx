import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, Di<PERSON><PERSON>ooter, Di<PERSON>Header, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTrigger } from "@/components/ui/dialog";
import { toast } from "sonner";
import {
  Users,
  Columns,
  Eye,
  Edit,
  Trash2,
  Plus,
  Search,
  RefreshCw,
  Filter,
  LayoutDashboard,
  Check,
  X,
  ArrowRight,
  Save,
  Loader2,
  UserPlus,
  UserMinus,
  ShieldAlert,
  Lock
} from "lucide-react";
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/lib/supabase';
import { UserBasicInfo } from '@/types/permissions';
import { KanbanColuna } from '@/components/Precatorios/types';

interface KanbanPermissionsAdminProps {
  users: UserBasicInfo[];
}

export function KanbanPermissionsAdmin({ users }: KanbanPermissionsAdminProps) {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('columns');
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [columns, setColumns] = useState<KanbanColuna[]>([]);
  const [selectedColumn, setSelectedColumn] = useState<KanbanColuna | null>(null);
  const [isPermissionsDialogOpen, setIsPermissionsDialogOpen] = useState(false);
  const [columnPermissions, setColumnPermissions] = useState<{
    id: string;
    column_id: string;
    user_id: string;
    can_view: boolean;
    can_edit: boolean;
    can_delete: boolean;
    can_move_cards: boolean;
  }[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredUsers, setFilteredUsers] = useState<UserBasicInfo[]>([]);

  // Carregar colunas do Kanban
  useEffect(() => {
    loadKanbanColumns();
  }, []);

  // Filtrar usuários quando a busca mudar
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredUsers(users);
      return;
    }
    
    const query = searchQuery.toLowerCase();
    const filtered = users.filter(user => 
      user.name.toLowerCase().includes(query) || 
      user.email?.toLowerCase().includes(query) ||
      user.role?.toLowerCase().includes(query)
    );
    
    setFilteredUsers(filtered);
  }, [searchQuery, users]);

  // Carregar colunas do Kanban
  const loadKanbanColumns = async () => {
    try {
      setLoading(true);
      
      const { data, error } = await supabase
        .from('kanban_colunas')
        .select('*')
        .order('ordem');
      
      if (error) {
        console.error('Erro ao carregar colunas do Kanban:', error);
        toast.error("Erro ao carregar colunas", {
          description: error.message
        });
        return;
      }
      
      setColumns(data || []);
    } catch (error) {
      console.error('Erro ao carregar colunas do Kanban:', error);
      toast.error("Erro ao carregar colunas", {
        description: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    } finally {
      setLoading(false);
    }
  };

  // Carregar permissões de uma coluna específica
  const loadColumnPermissions = async (columnId: string) => {
    try {
      setLoading(true);
      
      const { data, error } = await supabase
        .from('kanban_column_permissions')
        .select('*')
        .eq('column_id', columnId);
      
      if (error) {
        console.error('Erro ao carregar permissões da coluna:', error);
        toast.error("Erro ao carregar permissões", {
          description: error.message
        });
        return;
      }
      
      setColumnPermissions(data || []);
    } catch (error) {
      console.error('Erro ao carregar permissões da coluna:', error);
      toast.error("Erro ao carregar permissões", {
        description: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    } finally {
      setLoading(false);
    }
  };

  // Abrir diálogo de permissões para uma coluna
  const handleOpenPermissionsDialog = (column: KanbanColuna) => {
    setSelectedColumn(column);
    loadColumnPermissions(column.id);
    setIsPermissionsDialogOpen(true);
  };

  // Atualizar permissão de usuário
  const handleUpdatePermission = async (userId: string, permission: 'can_view' | 'can_edit' | 'can_delete' | 'can_move_cards', value: boolean) => {
    try {
      if (!selectedColumn) return;
      
      // Encontrar permissões existentes para este usuário
      const existingPermission = columnPermissions.find(p => p.user_id === userId);
      
      // Preparar dados para atualização
      const permissionData = {
        can_view: existingPermission?.can_view || false,
        can_edit: existingPermission?.can_edit || false,
        can_delete: existingPermission?.can_delete || false,
        can_move_cards: existingPermission?.can_move_cards || false,
        [permission]: value
      };
      
      // Chamar função RPC para atualizar permissão
      const { data, error } = await supabase.rpc('update_kanban_column_permission', {
        p_admin_id: user!.id,
        p_column_id: selectedColumn.id,
        p_user_id: userId,
        p_can_view: permissionData.can_view,
        p_can_edit: permissionData.can_edit,
        p_can_delete: permissionData.can_delete,
        p_can_move_cards: permissionData.can_move_cards
      });
      
      if (error) {
        console.error('Erro ao atualizar permissão:', error);
        toast.error("Erro ao atualizar permissão", {
          description: error.message
        });
        return;
      }
      
      // Atualizar estado local
      setColumnPermissions(prev => {
        const index = prev.findIndex(p => p.user_id === userId);
        if (index >= 0) {
          // Atualizar permissão existente
          const updated = [...prev];
          updated[index] = {
            ...updated[index],
            [permission]: value
          };
          return updated;
        } else {
          // Adicionar nova permissão
          return [...prev, {
            id: `temp-${Date.now()}`,
            column_id: selectedColumn.id,
            user_id: userId,
            can_view: permission === 'can_view' ? value : false,
            can_edit: permission === 'can_edit' ? value : false,
            can_delete: permission === 'can_delete' ? value : false,
            can_move_cards: permission === 'can_move_cards' ? value : false
          }];
        }
      });
      
      toast.success("Permissão atualizada", {
        description: "A permissão foi atualizada com sucesso."
      });
    } catch (error) {
      console.error('Erro ao atualizar permissão:', error);
      toast.error("Erro ao atualizar permissão", {
        description: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  };

  // Atualizar manualmente
  const handleRefresh = async () => {
    setRefreshing(true);
    await loadKanbanColumns();
    setRefreshing(false);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Permissões do Kanban</CardTitle>
            <CardDescription>
              Gerencie permissões para colunas e visualizações do Kanban
            </CardDescription>
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={refreshing}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              Atualizar
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-2 mb-6">
            <TabsTrigger value="columns">
              <Columns className="h-4 w-4 mr-2" />
              Colunas do Kanban
            </TabsTrigger>
            <TabsTrigger value="views">
              <LayoutDashboard className="h-4 w-4 mr-2" />
              Visualizações Personalizadas
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="columns" className="space-y-4">
            {loading ? (
              <div className="flex flex-col items-center justify-center h-40">
                <Loader2 className="h-8 w-8 animate-spin mb-4" />
                <div>Carregando colunas...</div>
              </div>
            ) : columns.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-40 text-muted-foreground">
                <ShieldAlert className="h-10 w-10 mb-2" />
                <p>Nenhuma coluna encontrada</p>
              </div>
            ) : (
              <div className="space-y-2">
                {columns.map(column => (
                  <div
                    key={column.id}
                    className="flex items-center justify-between p-3 rounded-lg border border-border hover:bg-accent/50 transition-colors"
                  >
                    <div className="flex items-center gap-3">
                      <div
                        className="w-8 h-8 rounded-md flex items-center justify-center"
                        style={{ backgroundColor: column.cor || '#3b82f6' }}
                      >
                        {column.icone && <span>{column.icone}</span>}
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <h3 className="font-medium">{column.nome}</h3>
                          {column.is_default && (
                            <Badge variant="secondary">Padrão</Badge>
                          )}
                          {column.is_system && (
                            <Badge variant="outline">Sistema</Badge>
                          )}
                        </div>
                        {column.descricao && (
                          <p className="text-sm text-muted-foreground">{column.descricao}</p>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleOpenPermissionsDialog(column)}
                      >
                        <Users className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="views" className="space-y-4">
            {/* Conteúdo para visualizações personalizadas */}
            <div className="flex flex-col items-center justify-center h-40 text-muted-foreground">
              <LayoutDashboard className="h-10 w-10 mb-2" />
              <p>Gerenciamento de visualizações personalizadas</p>
              <p className="text-sm">Em desenvolvimento</p>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      
      {/* Diálogo de permissões de coluna */}
      <Dialog open={isPermissionsDialogOpen} onOpenChange={setIsPermissionsDialogOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>
              Permissões da Coluna: {selectedColumn?.nome}
            </DialogTitle>
            <DialogDescription>
              Configure quais usuários podem visualizar, editar, excluir ou mover cards nesta coluna.
            </DialogDescription>
          </DialogHeader>
          
          <div className="py-4">
            <div className="flex items-center justify-between mb-4">
              <div className="relative w-64">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Buscar usuários..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
            
            <ScrollArea className="h-[400px]">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Usuário</TableHead>
                    <TableHead className="w-[80px] text-center">Visualizar</TableHead>
                    <TableHead className="w-[80px] text-center">Editar</TableHead>
                    <TableHead className="w-[80px] text-center">Excluir</TableHead>
                    <TableHead className="w-[80px] text-center">Mover Cards</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredUsers.map(user => {
                    const permission = columnPermissions.find(p => p.user_id === user.id);
                    const isAdmin = user.role === 'admin';
                    const isSystemColumn = selectedColumn?.is_system;
                    const isDefaultColumn = selectedColumn?.is_default;
                    
                    return (
                      <TableRow key={user.id}>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={user.avatar_url} />
                              <AvatarFallback>
                                {user.name.substring(0, 2).toUpperCase()}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="font-medium">{user.name}</div>
                              {user.role && (
                                <div className="text-xs text-muted-foreground">{user.role}</div>
                              )}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="text-center">
                          {isAdmin ? (
                            <Check className="w-4 h-4 mx-auto text-green-500" />
                          ) : (
                            <Switch
                              checked={permission?.can_view || false}
                              onCheckedChange={(checked) =>
                                handleUpdatePermission(user.id, 'can_view', checked)
                              }
                            />
                          )}
                        </TableCell>
                        <TableCell className="text-center">
                          {isAdmin ? (
                            <Check className="w-4 h-4 mx-auto text-green-500" />
                          ) : isSystemColumn ? (
                            <X className="w-4 h-4 mx-auto text-red-500" />
                          ) : (
                            <Switch
                              checked={permission?.can_edit || false}
                              onCheckedChange={(checked) =>
                                handleUpdatePermission(user.id, 'can_edit', checked)
                              }
                            />
                          )}
                        </TableCell>
                        <TableCell className="text-center">
                          {isAdmin ? (
                            isSystemColumn || isDefaultColumn ? (
                              <X className="w-4 h-4 mx-auto text-red-500" />
                            ) : (
                              <Check className="w-4 h-4 mx-auto text-green-500" />
                            )
                          ) : isSystemColumn || isDefaultColumn ? (
                            <X className="w-4 h-4 mx-auto text-red-500" />
                          ) : (
                            <Switch
                              checked={permission?.can_delete || false}
                              onCheckedChange={(checked) =>
                                handleUpdatePermission(user.id, 'can_delete', checked)
                              }
                            />
                          )}
                        </TableCell>
                        <TableCell className="text-center">
                          {isAdmin ? (
                            <Check className="w-4 h-4 mx-auto text-green-500" />
                          ) : (
                            <Switch
                              checked={permission?.can_move_cards || false}
                              onCheckedChange={(checked) =>
                                handleUpdatePermission(user.id, 'can_move_cards', checked)
                              }
                            />
                          )}
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </ScrollArea>
          </div>
          
          <DialogFooter>
            <Button onClick={() => setIsPermissionsDialogOpen(false)}>
              Fechar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
}
