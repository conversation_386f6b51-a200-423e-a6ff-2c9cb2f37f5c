import React from 'react';
import { Alert<PERSON>ircle, ShieldAlert } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

interface NoPermissionMessageProps {
  title?: string;
  description?: string;
  icon?: React.ReactNode;
  className?: string;
}

/**
 * Componente para exibir uma mensagem quando o usuário não tem permissão
 */
export function NoPermissionMessage({
  title = "Acesso Restrito",
  description = "Você não tem permissão para acessar este recurso.",
  icon = <ShieldAlert className="h-12 w-12 text-destructive" />,
  className = ""
}: NoPermissionMessageProps) {
  return (
    <Card className={`w-full ${className}`}>
      <CardHeader className="flex flex-row items-center gap-4 pb-2">
        {icon}
        <div>
          <CardTitle>{title}</CardTitle>
          <CardDescription>{description}</CardDescription>
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <AlertCircle className="h-4 w-4" />
          <p>
            Se você acredita que deveria ter acesso a este recurso, entre em contato com um administrador.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
