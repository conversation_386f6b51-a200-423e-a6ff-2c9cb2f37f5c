import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { toast } from "sonner";
import {
  LayoutGrid,
  List,
  Table as TableIcon,
  Plus,
  Edit,
  Trash2,
  Star,
  Eye,
  EyeOff,
  Users,
  User,
  UserPlus,
  UserMinus,
  Save,
  Loader2,
  RefreshCw,
  Search,
  Filter,
  LayoutDashboard,
  ShieldAlert
} from "lucide-react";
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/lib/supabase';
import { UserBasicInfo } from '@/types/permissions';
import { KanbanColuna } from '@/components/Precatorios/types';

interface KanbanViewsAdminProps {
  users: UserBasicInfo[];
}

interface KanbanCustomView {
  id: string;
  nome: string;
  descricao?: string;
  user_id: string;
  is_public: boolean;
  is_default: boolean;
  is_favorite?: boolean;
  is_admin_created?: boolean;
  is_system?: boolean;
  layout?: string;
  icone?: string;
  cor?: string;
  colunas_selecionadas?: any;
  filtros?: any;
  tags_selecionadas?: any;
  created_at?: string;
  updated_at?: string;
}

export function KanbanViewsAdmin({ users }: KanbanViewsAdminProps) {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [views, setViews] = useState<KanbanCustomView[]>([]);
  const [columns, setColumns] = useState<KanbanColuna[]>([]);
  const [selectedView, setSelectedView] = useState<KanbanCustomView | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isAssignDialogOpen, setIsAssignDialogOpen] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredViews, setFilteredViews] = useState<KanbanCustomView[]>([]);

  // Estado do formulário
  const [formData, setFormData] = useState<{
    nome: string;
    descricao: string;
    is_public: boolean;
    is_default: boolean;
    layout: string;
    icone: string;
    cor: string;
    colunas_selecionadas: string[];
    filtros: any;
    tags_selecionadas: string[];
  }>({
    nome: '',
    descricao: '',
    is_public: false,
    is_default: false,
    layout: 'kanban',
    icone: 'layout',
    cor: '#3b82f6',
    colunas_selecionadas: [],
    filtros: {},
    tags_selecionadas: []
  });

  // Carregar visualizações e colunas
  useEffect(() => {
    loadViews();
    loadColumns();
  }, []);

  // Filtrar visualizações quando a busca mudar
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredViews(views);
      return;
    }

    const query = searchQuery.toLowerCase();
    const filtered = views.filter(view =>
      view.nome.toLowerCase().includes(query) ||
      view.descricao?.toLowerCase().includes(query) ||
      users.find(u => u.id === view.user_id)?.name.toLowerCase().includes(query)
    );

    setFilteredViews(filtered);
  }, [searchQuery, views, users]);

  // Resetar formulário quando o diálogo for aberto
  useEffect(() => {
    if (isCreateDialogOpen) {
      setFormData({
        nome: '',
        descricao: '',
        is_public: false,
        is_default: false,
        layout: 'kanban',
        icone: 'layout',
        cor: '#3b82f6',
        colunas_selecionadas: columns.map(col => col.id),
        filtros: {},
        tags_selecionadas: []
      });
    }
  }, [isCreateDialogOpen, columns]);

  // Preencher formulário quando uma visualização for selecionada para edição
  useEffect(() => {
    if (selectedView && isEditDialogOpen) {
      setFormData({
        nome: selectedView.nome,
        descricao: selectedView.descricao || '',
        is_public: selectedView.is_public,
        is_default: selectedView.is_default,
        layout: selectedView.layout || 'kanban',
        icone: selectedView.icone || 'layout',
        cor: selectedView.cor || '#3b82f6',
        colunas_selecionadas: selectedView.colunas_selecionadas || columns.map(col => col.id),
        filtros: selectedView.filtros || {},
        tags_selecionadas: selectedView.tags_selecionadas || []
      });
    }
  }, [selectedView, isEditDialogOpen, columns]);

  // Carregar visualizações personalizadas
  const loadViews = async () => {
    try {
      setLoading(true);

      const { data, error } = await supabase
        .from('kanban_custom_views')
        .select('*')
        .order('nome');

      if (error) {
        console.error('Erro ao carregar visualizações:', error);
        toast.error("Erro ao carregar visualizações", {
          description: error.message
        });
        return;
      }

      setViews(data || []);
      setFilteredViews(data || []);
    } catch (error) {
      console.error('Erro ao carregar visualizações:', error);
      toast.error("Erro ao carregar visualizações", {
        description: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    } finally {
      setLoading(false);
    }
  };

  // Carregar colunas do Kanban
  const loadColumns = async () => {
    try {
      const { data, error } = await supabase
        .from('kanban_colunas')
        .select('*')
        .order('ordem');

      if (error) {
        console.error('Erro ao carregar colunas:', error);
        toast.error("Erro ao carregar colunas", {
          description: error.message
        });
        return;
      }

      setColumns(data || []);
    } catch (error) {
      console.error('Erro ao carregar colunas:', error);
      toast.error("Erro ao carregar colunas", {
        description: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  };

  // Criar nova visualização
  const handleCreateView = async () => {
    try {
      if (!formData.nome.trim()) {
        toast.error("Nome obrigatório", {
          description: "O nome da visualização é obrigatório."
        });
        return;
      }

      setLoading(true);

      const { data, error } = await supabase.rpc('manage_kanban_custom_view', {
        p_user_id: user!.id,
        p_view_id: null,
        p_nome: formData.nome.trim(),
        p_descricao: formData.descricao.trim(),
        p_is_public: formData.is_public,
        p_is_default: formData.is_default,
        p_is_favorite: false,
        p_layout: formData.layout,
        p_icone: formData.icone,
        p_cor: formData.cor,
        p_colunas_selecionadas: formData.colunas_selecionadas,
        p_filtros: formData.filtros,
        p_tags_selecionadas: formData.tags_selecionadas,
        p_operation: 'create'
      });

      if (error) {
        console.error('Erro ao criar visualização:', error);
        toast.error("Erro ao criar visualização", {
          description: error.message
        });
        return;
      }

      toast.success("Visualização criada", {
        description: `A visualização "${formData.nome}" foi criada com sucesso.`
      });

      setIsCreateDialogOpen(false);
      loadViews();
    } catch (error) {
      console.error('Erro ao criar visualização:', error);
      toast.error("Erro ao criar visualização", {
        description: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    } finally {
      setLoading(false);
    }
  };

  // Editar visualização existente
  const handleEditView = async () => {
    try {
      if (!selectedView) return;

      if (!formData.nome.trim()) {
        toast.error("Nome obrigatório", {
          description: "O nome da visualização é obrigatório."
        });
        return;
      }

      setLoading(true);

      const { data, error } = await supabase.rpc('manage_kanban_custom_view', {
        p_user_id: user!.id,
        p_view_id: selectedView.id,
        p_nome: formData.nome.trim(),
        p_descricao: formData.descricao.trim(),
        p_is_public: formData.is_public,
        p_is_default: formData.is_default,
        p_is_favorite: selectedView.is_favorite || false,
        p_layout: formData.layout,
        p_icone: formData.icone,
        p_cor: formData.cor,
        p_colunas_selecionadas: formData.colunas_selecionadas,
        p_filtros: formData.filtros,
        p_tags_selecionadas: formData.tags_selecionadas,
        p_operation: 'update'
      });

      if (error) {
        console.error('Erro ao atualizar visualização:', error);
        toast.error("Erro ao atualizar visualização", {
          description: error.message
        });
        return;
      }

      toast.success("Visualização atualizada", {
        description: `A visualização "${formData.nome}" foi atualizada com sucesso.`
      });

      setIsEditDialogOpen(false);
      loadViews();
    } catch (error) {
      console.error('Erro ao atualizar visualização:', error);
      toast.error("Erro ao atualizar visualização", {
        description: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    } finally {
      setLoading(false);
    }
  };

  // Excluir visualização
  const handleDeleteView = async () => {
    try {
      if (!selectedView) return;

      setLoading(true);

      const { data, error } = await supabase.rpc('manage_kanban_custom_view', {
        p_user_id: user!.id,
        p_view_id: selectedView.id,
        p_nome: selectedView.nome,
        p_descricao: selectedView.descricao || '',
        p_is_public: selectedView.is_public,
        p_is_default: selectedView.is_default,
        p_is_favorite: selectedView.is_favorite || false,
        p_layout: selectedView.layout || 'kanban',
        p_icone: selectedView.icone || 'layout',
        p_cor: selectedView.cor || '#3b82f6',
        p_colunas_selecionadas: selectedView.colunas_selecionadas || [],
        p_filtros: selectedView.filtros || {},
        p_tags_selecionadas: selectedView.tags_selecionadas || [],
        p_operation: 'delete'
      });

      if (error) {
        console.error('Erro ao excluir visualização:', error);
        toast.error("Erro ao excluir visualização", {
          description: error.message
        });
        return;
      }

      toast.success("Visualização excluída", {
        description: `A visualização "${selectedView.nome}" foi excluída com sucesso.`
      });

      setIsDeleteDialogOpen(false);
      loadViews();
    } catch (error) {
      console.error('Erro ao excluir visualização:', error);
      toast.error("Erro ao excluir visualização", {
        description: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    } finally {
      setLoading(false);
    }
  };

  // Atribuir visualização a usuários
  const handleAssignView = async () => {
    try {
      if (!selectedView || selectedUsers.length === 0) return;

      setLoading(true);

      // Atribuir visualização a cada usuário selecionado
      for (const userId of selectedUsers) {
        const { error } = await supabase.rpc('assign_kanban_view_to_user', {
          p_admin_id: user!.id,
          p_view_id: selectedView.id,
          p_user_id: userId,
          p_assign: true
        });

        if (error) {
          console.error(`Erro ao atribuir visualização ao usuário ${userId}:`, error);
          toast.error("Erro ao atribuir visualização", {
            description: error.message
          });
        }
      }

      toast.success("Visualização atribuída", {
        description: `A visualização foi atribuída a ${selectedUsers.length} usuário(s) com sucesso.`
      });

      setIsAssignDialogOpen(false);
      setSelectedUsers([]);
    } catch (error) {
      console.error('Erro ao atribuir visualização:', error);
      toast.error("Erro ao atribuir visualização", {
        description: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    } finally {
      setLoading(false);
    }
  };

  // Atualizar manualmente
  const handleRefresh = async () => {
    setRefreshing(true);
    await loadViews();
    setRefreshing(false);
  };

  // Renderizar ícone de layout
  const renderLayoutIcon = (layout: string) => {
    switch (layout) {
      case 'kanban':
        return <LayoutGrid className="w-4 h-4" />;
      case 'lista':
        return <List className="w-4 h-4" />;
      case 'tabela':
        return <TableIcon className="w-4 h-4" />;
      default:
        return <LayoutGrid className="w-4 h-4" />;
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Visualizações Personalizadas do Kanban</CardTitle>
            <CardDescription>
              Gerencie visualizações personalizadas e atribua a usuários
            </CardDescription>
          </div>

          <div className="flex items-center gap-2">
            <div className="relative w-64">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Buscar visualizações..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={refreshing}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              Atualizar
            </Button>

            <Button
              size="sm"
              onClick={() => setIsCreateDialogOpen(true)}
            >
              <Plus className="h-4 w-4 mr-2" />
              Nova Visualização
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        {loading ? (
          <div className="flex flex-col items-center justify-center h-40">
            <Loader2 className="h-8 w-8 animate-spin mb-4" />
            <div>Carregando visualizações...</div>
          </div>
        ) : filteredViews.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-40 text-muted-foreground">
            <ShieldAlert className="h-10 w-10 mb-2" />
            <p>Nenhuma visualização encontrada</p>
            <Button
              variant="outline"
              size="sm"
              className="mt-4"
              onClick={() => setIsCreateDialogOpen(true)}
            >
              <Plus className="h-4 w-4 mr-2" />
              Criar Visualização
            </Button>
          </div>
        ) : (
          <ScrollArea className="h-[500px]">
            <div className="space-y-2">
              {filteredViews.map(view => {
                const layoutIcon = renderLayoutIcon(view.layout || 'kanban');
                const createdBy = users.find(u => u.id === view.user_id);

                return (
                  <div
                    key={view.id}
                    className="flex items-center justify-between p-3 rounded-lg border border-border hover:bg-accent/50 transition-colors"
                  >
                    <div className="flex items-center gap-3">
                      <div
                        className="w-8 h-8 rounded-md flex items-center justify-center"
                        style={{ backgroundColor: view.cor || '#3b82f6' }}
                      >
                        {layoutIcon}
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <h3 className="font-medium">{view.nome}</h3>
                          {view.is_default && (
                            <Badge variant="secondary">Padrão</Badge>
                          )}
                          {view.is_public && (
                            <Badge variant="outline">Pública</Badge>
                          )}
                          {view.is_favorite && (
                            <Star className="w-4 h-4 text-amber-500 fill-amber-500" />
                          )}
                        </div>
                        <div className="flex items-center gap-2 text-xs text-muted-foreground">
                          {view.descricao && (
                            <span className="mr-2">{view.descricao}</span>
                          )}
                          {createdBy && (
                            <span className="flex items-center">
                              <User className="w-3 h-3 mr-1" />
                              {createdBy.name}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => {
                          setSelectedView(view);
                          setIsAssignDialogOpen(true);
                        }}
                      >
                        <UserPlus className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => {
                          setSelectedView(view);
                          setIsEditDialogOpen(true);
                        }}
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => {
                          setSelectedView(view);
                          setIsDeleteDialogOpen(true);
                        }}
                      >
                        <Trash2 className="w-4 h-4 text-destructive" />
                      </Button>
                    </div>
                  </div>
                );
              })}
            </div>
          </ScrollArea>
        )}
      </CardContent>

      {/* Diálogo para criar visualização */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Nova Visualização</DialogTitle>
            <DialogDescription>
              Crie uma nova visualização personalizada para o Kanban.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="nome">Nome</Label>
              <Input
                id="nome"
                value={formData.nome}
                onChange={(e) => setFormData({ ...formData, nome: e.target.value })}
                placeholder="Nome da visualização"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="descricao">Descrição</Label>
              <Textarea
                id="descricao"
                value={formData.descricao}
                onChange={(e) => setFormData({ ...formData, descricao: e.target.value })}
                placeholder="Descrição opcional"
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="layout">Layout</Label>
              <Select
                value={formData.layout}
                onValueChange={(value) => setFormData({ ...formData, layout: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione um layout" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="kanban">
                    <div className="flex items-center">
                      <LayoutGrid className="w-4 h-4 mr-2" />
                      Kanban
                    </div>
                  </SelectItem>
                  <SelectItem value="lista">
                    <div className="flex items-center">
                      <List className="w-4 h-4 mr-2" />
                      Lista
                    </div>
                  </SelectItem>
                  <SelectItem value="tabela">
                    <div className="flex items-center">
                      <TableIcon className="w-4 h-4 mr-2" />
                      Tabela
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="cor">Cor</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="cor"
                  type="color"
                  value={formData.cor}
                  onChange={(e) => setFormData({ ...formData, cor: e.target.value })}
                  className="w-12 h-8 p-1"
                />
                <Input
                  value={formData.cor}
                  onChange={(e) => setFormData({ ...formData, cor: e.target.value })}
                  placeholder="#3b82f6"
                  className="flex-1"
                />
              </div>
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="is_public">Visualização Pública</Label>
              <Switch
                id="is_public"
                checked={formData.is_public}
                onCheckedChange={(checked) => setFormData({ ...formData, is_public: checked })}
              />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="is_default">Visualização Padrão</Label>
              <Switch
                id="is_default"
                checked={formData.is_default}
                onCheckedChange={(checked) => setFormData({ ...formData, is_default: checked })}
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
              Cancelar
            </Button>
            <Button onClick={handleCreateView} disabled={loading}>
              {loading ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <Save className="h-4 w-4 mr-2" />}
              Criar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Diálogo para editar visualização */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Editar Visualização</DialogTitle>
            <DialogDescription>
              Edite a visualização personalizada.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="edit-nome">Nome</Label>
              <Input
                id="edit-nome"
                value={formData.nome}
                onChange={(e) => setFormData({ ...formData, nome: e.target.value })}
                placeholder="Nome da visualização"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-descricao">Descrição</Label>
              <Textarea
                id="edit-descricao"
                value={formData.descricao}
                onChange={(e) => setFormData({ ...formData, descricao: e.target.value })}
                placeholder="Descrição opcional"
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-layout">Layout</Label>
              <Select
                value={formData.layout}
                onValueChange={(value) => setFormData({ ...formData, layout: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione um layout" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="kanban">
                    <div className="flex items-center">
                      <LayoutGrid className="w-4 h-4 mr-2" />
                      Kanban
                    </div>
                  </SelectItem>
                  <SelectItem value="lista">
                    <div className="flex items-center">
                      <List className="w-4 h-4 mr-2" />
                      Lista
                    </div>
                  </SelectItem>
                  <SelectItem value="tabela">
                    <div className="flex items-center">
                      <TableIcon className="w-4 h-4 mr-2" />
                      Tabela
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-cor">Cor</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="edit-cor"
                  type="color"
                  value={formData.cor}
                  onChange={(e) => setFormData({ ...formData, cor: e.target.value })}
                  className="w-12 h-8 p-1"
                />
                <Input
                  value={formData.cor}
                  onChange={(e) => setFormData({ ...formData, cor: e.target.value })}
                  placeholder="#3b82f6"
                  className="flex-1"
                />
              </div>
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="edit-is_public">Visualização Pública</Label>
              <Switch
                id="edit-is_public"
                checked={formData.is_public}
                onCheckedChange={(checked) => setFormData({ ...formData, is_public: checked })}
              />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="edit-is_default">Visualização Padrão</Label>
              <Switch
                id="edit-is_default"
                checked={formData.is_default}
                onCheckedChange={(checked) => setFormData({ ...formData, is_default: checked })}
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancelar
            </Button>
            <Button onClick={handleEditView} disabled={loading}>
              {loading ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <Save className="h-4 w-4 mr-2" />}
              Salvar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Diálogo para excluir visualização */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Excluir Visualização</DialogTitle>
            <DialogDescription>
              Tem certeza que deseja excluir esta visualização? Esta ação não pode ser desfeita.
            </DialogDescription>
          </DialogHeader>

          {selectedView && (
            <div className="py-4">
              <div className="flex items-center gap-3 p-3 rounded-lg border border-destructive/20 bg-destructive/5">
                <div
                  className="w-8 h-8 rounded-md flex items-center justify-center"
                  style={{ backgroundColor: selectedView.cor || '#3b82f6' }}
                >
                  {renderLayoutIcon(selectedView.layout || 'kanban')}
                </div>
                <div>
                  <h3 className="font-medium">{selectedView.nome}</h3>
                  {selectedView.descricao && (
                    <p className="text-sm text-muted-foreground">{selectedView.descricao}</p>
                  )}
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancelar
            </Button>
            <Button variant="destructive" onClick={handleDeleteView} disabled={loading}>
              {loading ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <Trash2 className="h-4 w-4 mr-2" />}
              Excluir
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Diálogo para atribuir visualização a usuários */}
      <Dialog open={isAssignDialogOpen} onOpenChange={setIsAssignDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Atribuir Visualização a Usuários</DialogTitle>
            <DialogDescription>
              Selecione os usuários que terão acesso a esta visualização.
            </DialogDescription>
          </DialogHeader>

          {selectedView && (
            <div className="py-4">
              <div className="flex items-center gap-3 p-3 rounded-lg border mb-4">
                <div
                  className="w-8 h-8 rounded-md flex items-center justify-center"
                  style={{ backgroundColor: selectedView.cor || '#3b82f6' }}
                >
                  {renderLayoutIcon(selectedView.layout || 'kanban')}
                </div>
                <div>
                  <h3 className="font-medium">{selectedView.nome}</h3>
                  {selectedView.descricao && (
                    <p className="text-sm text-muted-foreground">{selectedView.descricao}</p>
                  )}
                </div>
              </div>

              <div className="relative mb-4">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Buscar usuários..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>

              <ScrollArea className="h-[300px] border rounded-md p-2">
                <div className="space-y-2">
                  {filteredUsers.map(user => (
                    <div key={user.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={`user-${user.id}`}
                        checked={selectedUsers.includes(user.id)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setSelectedUsers([...selectedUsers, user.id]);
                          } else {
                            setSelectedUsers(selectedUsers.filter(id => id !== user.id));
                          }
                        }}
                      />
                      <Label
                        htmlFor={`user-${user.id}`}
                        className="flex items-center gap-2 cursor-pointer py-1"
                      >
                        <Avatar className="h-6 w-6">
                          <AvatarImage src={user.avatar_url} />
                          <AvatarFallback>
                            {user.name.substring(0, 2).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex flex-col">
                          <span className="text-sm font-medium">{user.name}</span>
                          {user.role && (
                            <span className="text-xs text-muted-foreground">{user.role}</span>
                          )}
                        </div>
                      </Label>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAssignDialogOpen(false)}>
              Cancelar
            </Button>
            <Button
              onClick={handleAssignView}
              disabled={loading || selectedUsers.length === 0}
            >
              {loading ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <UserPlus className="h-4 w-4 mr-2" />}
              Atribuir ({selectedUsers.length})
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
}
