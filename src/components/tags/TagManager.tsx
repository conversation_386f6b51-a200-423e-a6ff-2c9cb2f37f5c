import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useToast } from '@/components/ui/use-toast';
import { Tag, TagPermission, fetchAllTags, createTag, updateTag, deleteTag, getTagPermissions, updateTagPermission } from '@/services/tagsService';
import { Loader2, Plus, Trash2, Edit, AlertTriangle, Tag as TagIcon, Check, X, Users, Settings, Info } from 'lucide-react';
import { ColorPicker } from '@/components/ui/color-picker';
import { IconPicker } from '@/components/ui/icon-picker';
import { supabase } from '@/lib/supabase';

interface User {
  id: string;
  nome: string;
  email: string;
  avatar_url?: string;
  role?: string;
}

interface TagManagerProps {
  onTagsChange?: () => void;
}

export function TagManager({ onTagsChange }: TagManagerProps) {
  const { toast } = useToast();
  const [tags, setTags] = useState<Tag[]>([]);
  const [loading, setLoading] = useState(true);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isPermissionsDialogOpen, setIsPermissionsDialogOpen] = useState(false);
  const [selectedTag, setSelectedTag] = useState<Tag | null>(null);
  const [tagPermissions, setTagPermissions] = useState<TagPermission[]>([]);
  const [permissionsLoading, setPermissionsLoading] = useState(false);
  const [allUsers, setAllUsers] = useState<User[]>([]);
  const [usersLoading, setUsersLoading] = useState(false);

  // Form states
  const [formData, setFormData] = useState<{
    nome: string;
    cor: string;
    descricao: string;
    icone: string;
  }>({
    nome: '',
    cor: '#3b82f6',
    descricao: '',
    icone: 'tag',
  });

  // Carregar tags
  useEffect(() => {
    loadTags();
  }, []);

  const loadTags = async () => {
    try {
      setLoading(true);
      const tagsData = await fetchAllTags();
      setTags(tagsData);
    } catch (error) {
      console.error('Erro ao carregar tags:', error);
      toast({
        title: 'Erro ao carregar tags',
        description: 'Não foi possível carregar as tags. Tente novamente mais tarde.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Carregar usuários
  const loadUsers = async () => {
    try {
      setUsersLoading(true);
      const { data, error } = await supabase
        .from('profiles')
        .select('id, nome, email, avatar_url, role')
        .order('nome');

      if (error) throw error;
      setAllUsers(data || []);
    } catch (error) {
      console.error('Erro ao carregar usuários:', error);
      toast({
        title: 'Erro ao carregar usuários',
        description: 'Não foi possível carregar a lista de usuários.',
        variant: 'destructive',
      });
    } finally {
      setUsersLoading(false);
    }
  };

  // Carregar permissões de uma tag
  const loadTagPermissions = async (tagId: string) => {
    try {
      setPermissionsLoading(true);
      const permissions = await getTagPermissions(tagId);
      setTagPermissions(permissions);
    } catch (error) {
      console.error('Erro ao carregar permissões da tag:', error);
      toast({
        title: 'Erro ao carregar permissões',
        description: 'Não foi possível carregar as permissões da tag.',
        variant: 'destructive',
      });
    } finally {
      setPermissionsLoading(false);
    }
  };

  // Abrir diálogo de criação
  const handleOpenCreateDialog = () => {
    setFormData({
      nome: '',
      cor: '#3b82f6',
      descricao: '',
      icone: 'tag',
    });
    setIsCreateDialogOpen(true);
  };

  // Abrir diálogo de edição
  const handleOpenEditDialog = (tag: Tag) => {
    setSelectedTag(tag);
    setFormData({
      nome: tag.nome,
      cor: tag.cor,
      descricao: tag.descricao || '',
      icone: tag.icone || 'tag',
    });
    setIsEditDialogOpen(true);
  };

  // Abrir diálogo de exclusão
  const handleOpenDeleteDialog = (tag: Tag) => {
    setSelectedTag(tag);
    setIsDeleteDialogOpen(true);
  };

  // Abrir diálogo de permissões
  const handleOpenPermissionsDialog = async (tag: Tag) => {
    setSelectedTag(tag);
    await loadUsers();
    await loadTagPermissions(tag.id);
    setIsPermissionsDialogOpen(true);
  };

  // Criar tag
  const handleCreateTag = async () => {
    try {
      if (!formData.nome.trim()) {
        toast({
          title: 'Nome obrigatório',
          description: 'O nome da tag é obrigatório.',
          variant: 'destructive',
        });
        return;
      }

      const newTag = await createTag({
        nome: formData.nome,
        cor: formData.cor,
        descricao: formData.descricao,
        icone: formData.icone,
      });

      setTags([...tags, newTag]);
      setIsCreateDialogOpen(false);
      
      toast({
        title: 'Tag criada',
        description: 'A tag foi criada com sucesso.',
      });

      if (onTagsChange) onTagsChange();
    } catch (error) {
      console.error('Erro ao criar tag:', error);
      toast({
        title: 'Erro ao criar tag',
        description: 'Não foi possível criar a tag. Tente novamente mais tarde.',
        variant: 'destructive',
      });
    }
  };

  // Atualizar tag
  const handleUpdateTag = async () => {
    try {
      if (!selectedTag) return;
      
      if (!formData.nome.trim()) {
        toast({
          title: 'Nome obrigatório',
          description: 'O nome da tag é obrigatório.',
          variant: 'destructive',
        });
        return;
      }

      const updatedTag = await updateTag(selectedTag.id, {
        nome: formData.nome,
        cor: formData.cor,
        descricao: formData.descricao,
        icone: formData.icone,
      });

      setTags(tags.map(tag => tag.id === updatedTag.id ? updatedTag : tag));
      setIsEditDialogOpen(false);
      
      toast({
        title: 'Tag atualizada',
        description: 'A tag foi atualizada com sucesso.',
      });

      if (onTagsChange) onTagsChange();
    } catch (error) {
      console.error('Erro ao atualizar tag:', error);
      toast({
        title: 'Erro ao atualizar tag',
        description: 'Não foi possível atualizar a tag. Tente novamente mais tarde.',
        variant: 'destructive',
      });
    }
  };

  // Excluir tag
  const handleDeleteTag = async () => {
    try {
      if (!selectedTag) return;

      await deleteTag(selectedTag.id);
      setTags(tags.filter(tag => tag.id !== selectedTag.id));
      setIsDeleteDialogOpen(false);
      
      toast({
        title: 'Tag excluída',
        description: 'A tag foi excluída com sucesso.',
      });

      if (onTagsChange) onTagsChange();
    } catch (error) {
      console.error('Erro ao excluir tag:', error);
      toast({
        title: 'Erro ao excluir tag',
        description: 'Não foi possível excluir a tag. Tente novamente mais tarde.',
        variant: 'destructive',
      });
    }
  };

  // Atualizar permissão de usuário
  const handleUpdatePermission = async (userId: string, permission: 'can_view' | 'can_edit' | 'can_delete', value: boolean) => {
    try {
      if (!selectedTag) return;

      await updateTagPermission(selectedTag.id, userId, {
        [permission]: value,
      });

      // Atualizar estado local
      setTagPermissions(tagPermissions.map(p => {
        if (p.user_id === userId) {
          return { ...p, [permission]: value };
        }
        return p;
      }));

      toast({
        title: 'Permissão atualizada',
        description: 'A permissão foi atualizada com sucesso.',
      });
    } catch (error) {
      console.error('Erro ao atualizar permissão:', error);
      toast({
        title: 'Erro ao atualizar permissão',
        description: 'Não foi possível atualizar a permissão. Tente novamente mais tarde.',
        variant: 'destructive',
      });
    }
  };

  // Renderizar tag
  const renderTag = (tag: Tag) => (
    <div
      className="flex items-center justify-between p-3 rounded-lg border border-neutral-200 dark:border-neutral-700 hover:bg-neutral-50 dark:hover:bg-neutral-800 transition-colors"
      key={tag.id}
    >
      <div className="flex items-center gap-3">
        <div
          className="w-8 h-8 rounded-md flex items-center justify-center"
          style={{ backgroundColor: tag.cor }}
        >
          <TagIcon className="w-4 h-4 text-white" />
        </div>
        <div>
          <h3 className="font-medium">{tag.nome}</h3>
          {tag.descricao && (
            <p className="text-sm text-muted-foreground">{tag.descricao}</p>
          )}
        </div>
      </div>
      <div className="flex items-center gap-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleOpenPermissionsDialog(tag)}
        >
          <Users className="w-4 h-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleOpenEditDialog(tag)}
        >
          <Edit className="w-4 h-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleOpenDeleteDialog(tag)}
        >
          <Trash2 className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Gerenciamento de Tags</h2>
          <p className="text-muted-foreground">
            Crie e gerencie tags para organizar seus precatórios
          </p>
        </div>
        <Button onClick={handleOpenCreateDialog}>
          <Plus className="w-4 h-4 mr-2" />
          Nova Tag
        </Button>
      </div>

      {loading ? (
        <div className="flex items-center justify-center p-8">
          <Loader2 className="w-8 h-8 animate-spin text-primary" />
        </div>
      ) : tags.length === 0 ? (
        <Card>
          <CardContent className="p-8 flex flex-col items-center justify-center text-center">
            <TagIcon className="w-12 h-12 text-muted-foreground mb-4" />
            <h3 className="text-xl font-medium mb-2">Nenhuma tag encontrada</h3>
            <p className="text-muted-foreground mb-4">
              Crie tags para organizar seus precatórios e RPVs
            </p>
            <Button onClick={handleOpenCreateDialog}>
              <Plus className="w-4 h-4 mr-2" />
              Criar Tag
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {tags.map(renderTag)}
        </div>
      )}

      {/* Diálogo de criação */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Criar Nova Tag</DialogTitle>
            <DialogDescription>
              Crie uma nova tag para organizar seus precatórios
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="nome">Nome</Label>
              <Input
                id="nome"
                value={formData.nome}
                onChange={(e) => setFormData({ ...formData, nome: e.target.value })}
                placeholder="Ex: Precatório Federal"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="descricao">Descrição (opcional)</Label>
              <Textarea
                id="descricao"
                value={formData.descricao}
                onChange={(e) => setFormData({ ...formData, descricao: e.target.value })}
                placeholder="Descreva o propósito desta tag"
                rows={3}
              />
            </div>
            <div className="space-y-2">
              <Label>Cor</Label>
              <ColorPicker
                color={formData.cor}
                onChange={(color) => setFormData({ ...formData, cor: color })}
              />
            </div>
            <div className="space-y-2">
              <Label>Ícone</Label>
              <IconPicker
                value={formData.icone}
                onChange={(icon) => setFormData({ ...formData, icone: icon })}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
              Cancelar
            </Button>
            <Button onClick={handleCreateTag}>
              Criar Tag
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Diálogo de edição */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Editar Tag</DialogTitle>
            <DialogDescription>
              Atualize as informações da tag
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="edit-nome">Nome</Label>
              <Input
                id="edit-nome"
                value={formData.nome}
                onChange={(e) => setFormData({ ...formData, nome: e.target.value })}
                placeholder="Ex: Precatório Federal"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-descricao">Descrição (opcional)</Label>
              <Textarea
                id="edit-descricao"
                value={formData.descricao}
                onChange={(e) => setFormData({ ...formData, descricao: e.target.value })}
                placeholder="Descreva o propósito desta tag"
                rows={3}
              />
            </div>
            <div className="space-y-2">
              <Label>Cor</Label>
              <ColorPicker
                color={formData.cor}
                onChange={(color) => setFormData({ ...formData, cor: color })}
              />
            </div>
            <div className="space-y-2">
              <Label>Ícone</Label>
              <IconPicker
                value={formData.icone}
                onChange={(icon) => setFormData({ ...formData, icone: icon })}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancelar
            </Button>
            <Button onClick={handleUpdateTag}>
              Salvar Alterações
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Diálogo de exclusão */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Excluir Tag</DialogTitle>
            <DialogDescription>
              Tem certeza que deseja excluir esta tag?
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Atenção</AlertTitle>
              <AlertDescription>
                Esta ação irá remover a tag "{selectedTag?.nome}" de todos os precatórios associados.
                Esta ação pode ser revertida na seção de itens excluídos.
              </AlertDescription>
            </Alert>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancelar
            </Button>
            <Button variant="destructive" onClick={handleDeleteTag}>
              Excluir Tag
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Diálogo de permissões */}
      <Dialog open={isPermissionsDialogOpen} onOpenChange={setIsPermissionsDialogOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Permissões da Tag</DialogTitle>
            <DialogDescription>
              Gerencie quem pode ver, editar e excluir a tag "{selectedTag?.nome}"
            </DialogDescription>
          </DialogHeader>
          
          {permissionsLoading || usersLoading ? (
            <div className="flex items-center justify-center p-8">
              <Loader2 className="w-8 h-8 animate-spin text-primary" />
            </div>
          ) : (
            <div className="py-4">
              <Alert className="mb-4">
                <Info className="h-4 w-4" />
                <AlertTitle>Informação</AlertTitle>
                <AlertDescription>
                  O criador da tag sempre tem todas as permissões. Administradores também têm acesso completo a todas as tags.
                </AlertDescription>
              </Alert>
              
              <ScrollArea className="h-[400px]">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Usuário</TableHead>
                      <TableHead className="w-[100px] text-center">Visualizar</TableHead>
                      <TableHead className="w-[100px] text-center">Editar</TableHead>
                      <TableHead className="w-[100px] text-center">Excluir</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {allUsers.map(user => {
                      const permission = tagPermissions.find(p => p.user_id === user.id);
                      const isCreator = selectedTag?.criado_por === user.id;
                      const isAdmin = user.role === 'admin';
                      
                      return (
                        <TableRow key={user.id}>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <div className="w-8 h-8 rounded-full bg-muted flex items-center justify-center">
                                {user.avatar_url ? (
                                  <img
                                    src={user.avatar_url}
                                    alt={user.nome}
                                    className="w-8 h-8 rounded-full"
                                  />
                                ) : (
                                  <Users className="w-4 h-4" />
                                )}
                              </div>
                              <div>
                                <p className="font-medium">{user.nome}</p>
                                <p className="text-xs text-muted-foreground">{user.email}</p>
                              </div>
                              {isCreator && (
                                <Badge variant="outline" className="ml-2">Criador</Badge>
                              )}
                              {isAdmin && (
                                <Badge variant="default" className="ml-2">Admin</Badge>
                              )}
                            </div>
                          </TableCell>
                          <TableCell className="text-center">
                            {isCreator || isAdmin ? (
                              <Check className="w-4 h-4 mx-auto text-green-500" />
                            ) : (
                              <Switch
                                checked={permission?.can_view || false}
                                onCheckedChange={(checked) => 
                                  handleUpdatePermission(user.id, 'can_view', checked)
                                }
                              />
                            )}
                          </TableCell>
                          <TableCell className="text-center">
                            {isCreator || isAdmin ? (
                              <Check className="w-4 h-4 mx-auto text-green-500" />
                            ) : (
                              <Switch
                                checked={permission?.can_edit || false}
                                onCheckedChange={(checked) => 
                                  handleUpdatePermission(user.id, 'can_edit', checked)
                                }
                              />
                            )}
                          </TableCell>
                          <TableCell className="text-center">
                            {isCreator || isAdmin ? (
                              <Check className="w-4 h-4 mx-auto text-green-500" />
                            ) : (
                              <Switch
                                checked={permission?.can_delete || false}
                                onCheckedChange={(checked) => 
                                  handleUpdatePermission(user.id, 'can_delete', checked)
                                }
                              />
                            )}
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </ScrollArea>
            </div>
          )}
          
          <DialogFooter>
            <Button onClick={() => setIsPermissionsDialogOpen(false)}>
              Fechar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
