import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useToast } from '@/components/ui/use-toast';
import { CustomView, fetchUserViews, createCustomView, updateCustomView, deleteCustomView } from '@/services/kanbanService';
import { Tag, fetchAllTags } from '@/services/tagsService';
import { KanbanColuna, KanbanColunaPersonalizada, fetchAllColunas, fetchAllColunasPersonalizadas } from '@/services/kanbanService';
import { Loader2, Plus, Trash2, Edit, AlertTriangle, LayoutGrid, Check, X, Users, Settings, Info, Star, Eye, EyeOff, Layout, List, Table as TableIcon } from 'lucide-react';
import { ColorPicker } from '@/components/ui/color-picker';
import { IconPicker } from '@/components/ui/icon-picker';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

interface CustomViewManagerProps {
  onViewsChange?: () => void;
}

export function CustomViewManager({ onViewsChange }: CustomViewManagerProps) {
  const { toast } = useToast();
  const [views, setViews] = useState<CustomView[]>([]);
  const [loading, setLoading] = useState(true);
  const [tags, setTags] = useState<Tag[]>([]);
  const [columns, setColumns] = useState<KanbanColunaPersonalizada[]>([]);
  const [tagsLoading, setTagsLoading] = useState(false);
  const [columnsLoading, setColumnsLoading] = useState(false);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedView, setSelectedView] = useState<CustomView | null>(null);

  // Form states
  const [formData, setFormData] = useState<{
    nome: string;
    descricao: string;
    is_public: boolean;
    layout: 'kanban' | 'lista' | 'tabela';
    icone: string;
    cor: string;
    tags_selecionadas: string[];
    colunas_selecionadas: string[];
    filtros: any;
  }>({
    nome: '',
    descricao: '',
    is_public: false,
    layout: 'kanban',
    icone: 'layout',
    cor: '#3b82f6',
    tags_selecionadas: [],
    colunas_selecionadas: [],
    filtros: {},
  });

  // Carregar visualizações
  useEffect(() => {
    loadViews();
  }, []);

  const loadViews = async () => {
    try {
      setLoading(true);
      const viewsData = await fetchUserViews();
      setViews(viewsData);
    } catch (error) {
      console.error('Erro ao carregar visualizações:', error);
      toast({
        title: 'Erro ao carregar visualizações',
        description: 'Não foi possível carregar as visualizações personalizadas. Tente novamente mais tarde.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Carregar tags e colunas
  const loadTagsAndColumns = async () => {
    try {
      setTagsLoading(true);
      setColumnsLoading(true);

      const [tagsData, columnsData] = await Promise.all([
        fetchAllTags(),
        fetchAllColunasPersonalizadas()
      ]);

      setTags(tagsData);
      setColumns(columnsData);

      console.log('Tags carregadas:', tagsData.length);
      console.log('Colunas personalizadas carregadas:', columnsData.length);
    } catch (error) {
      console.error('Erro ao carregar tags e colunas:', error);
      toast({
        title: 'Erro ao carregar dados',
        description: 'Não foi possível carregar as tags e colunas. Tente novamente mais tarde.',
        variant: 'destructive',
      });
    } finally {
      setTagsLoading(false);
      setColumnsLoading(false);
    }
  };

  // Abrir diálogo de criação
  const handleOpenCreateDialog = async () => {
    await loadTagsAndColumns();
    setFormData({
      nome: '',
      descricao: '',
      is_public: false,
      layout: 'kanban',
      icone: 'layout',
      cor: '#3b82f6',
      tags_selecionadas: [],
      colunas_selecionadas: [],
      filtros: {},
    });
    setIsCreateDialogOpen(true);
  };

  // Abrir diálogo de edição
  const handleOpenEditDialog = async (view: CustomView) => {
    await loadTagsAndColumns();
    setSelectedView(view);
    setFormData({
      nome: view.nome,
      descricao: view.descricao || '',
      is_public: view.is_public,
      layout: view.layout || 'kanban',
      icone: view.icone || 'layout',
      cor: view.cor || '#3b82f6',
      tags_selecionadas: view.tags_selecionadas || [],
      colunas_selecionadas: view.colunas_selecionadas || [],
      filtros: view.filtros || {},
    });
    setIsEditDialogOpen(true);
  };

  // Abrir diálogo de exclusão
  const handleOpenDeleteDialog = (view: CustomView) => {
    setSelectedView(view);
    setIsDeleteDialogOpen(true);
  };

  // Alternar favorito
  const handleToggleFavorite = async (view: CustomView) => {
    try {
      const updatedView = await updateCustomView(view.id, {
        is_favorite: !view.is_favorite
      });

      setViews(views.map(v => v.id === updatedView.id ? updatedView : v));

      toast({
        title: updatedView.is_favorite ? 'Adicionado aos favoritos' : 'Removido dos favoritos',
        description: `A visualização "${updatedView.nome}" foi ${updatedView.is_favorite ? 'adicionada aos' : 'removida dos'} favoritos.`,
      });

      if (onViewsChange) onViewsChange();
    } catch (error) {
      console.error('Erro ao atualizar favorito:', error);
      toast({
        title: 'Erro ao atualizar favorito',
        description: 'Não foi possível atualizar o status de favorito. Tente novamente mais tarde.',
        variant: 'destructive',
      });
    }
  };

  // Criar visualização
  const handleCreateView = async () => {
    try {
      if (!formData.nome.trim()) {
        toast({
          title: 'Nome obrigatório',
          description: 'O nome da visualização é obrigatório.',
          variant: 'destructive',
        });
        return;
      }

      // Verificar se há pelo menos uma coluna selecionada
      if (!formData.colunas_selecionadas || formData.colunas_selecionadas.length === 0) {
        toast({
          title: 'Colunas obrigatórias',
          description: 'Selecione pelo menos uma coluna para a visualização.',
          variant: 'destructive',
        });
        return;
      }

      console.log('Criando visualização com dados:', {
        ...formData,
        tags_selecionadas: formData.tags_selecionadas,
        colunas_selecionadas: formData.colunas_selecionadas
      });

      const newView = await createCustomView({
        nome: formData.nome,
        descricao: formData.descricao,
        is_public: formData.is_public,
        layout: formData.layout,
        icone: formData.icone,
        cor: formData.cor,
        tags_selecionadas: formData.tags_selecionadas,
        colunas_selecionadas: formData.colunas_selecionadas,
        filtros: formData.filtros,
        is_default: false,
        is_favorite: false,
        user_id: '', // Será preenchido pelo backend
      });

      console.log('Visualização criada com sucesso:', newView);

      setViews([...views, newView]);
      setIsCreateDialogOpen(false);

      toast({
        title: 'Visualização criada',
        description: 'A visualização personalizada foi criada com sucesso.',
      });

      if (onViewsChange) onViewsChange();
    } catch (error) {
      console.error('Erro ao criar visualização:', error);
      toast({
        title: 'Erro ao criar visualização',
        description: 'Não foi possível criar a visualização. Tente novamente mais tarde.',
        variant: 'destructive',
      });
    }
  };

  // Atualizar visualização
  const handleUpdateView = async () => {
    try {
      if (!selectedView) return;

      if (!formData.nome.trim()) {
        toast({
          title: 'Nome obrigatório',
          description: 'O nome da visualização é obrigatório.',
          variant: 'destructive',
        });
        return;
      }

      // Verificar se há pelo menos uma coluna selecionada
      if (!formData.colunas_selecionadas || formData.colunas_selecionadas.length === 0) {
        toast({
          title: 'Colunas obrigatórias',
          description: 'Selecione pelo menos uma coluna para a visualização.',
          variant: 'destructive',
        });
        return;
      }

      // Verificar se é uma visualização padrão
      if (selectedView.is_default) {
        toast({
          title: 'Operação não permitida',
          description: 'Não é possível modificar visualizações padrão.',
          variant: 'destructive',
        });
        return;
      }

      console.log('Atualizando visualização com dados:', {
        id: selectedView.id,
        ...formData,
        tags_selecionadas: formData.tags_selecionadas,
        colunas_selecionadas: formData.colunas_selecionadas
      });

      const updatedView = await updateCustomView(selectedView.id, {
        nome: formData.nome,
        descricao: formData.descricao,
        is_public: formData.is_public,
        layout: formData.layout,
        icone: formData.icone,
        cor: formData.cor,
        tags_selecionadas: formData.tags_selecionadas,
        colunas_selecionadas: formData.colunas_selecionadas,
        filtros: formData.filtros,
      });

      console.log('Visualização atualizada com sucesso:', updatedView);

      setViews(views.map(view => view.id === updatedView.id ? updatedView : view));
      setIsEditDialogOpen(false);

      toast({
        title: 'Visualização atualizada',
        description: 'A visualização personalizada foi atualizada com sucesso.',
      });

      if (onViewsChange) onViewsChange();
    } catch (error) {
      console.error('Erro ao atualizar visualização:', error);
      toast({
        title: 'Erro ao atualizar visualização',
        description: 'Não foi possível atualizar a visualização. Tente novamente mais tarde.',
        variant: 'destructive',
      });
    }
  };

  // Excluir visualização
  const handleDeleteView = async () => {
    try {
      if (!selectedView) return;

      // Verificar se é uma visualização padrão
      if (selectedView.is_default) {
        toast({
          title: 'Operação não permitida',
          description: 'Não é possível excluir visualizações padrão.',
          variant: 'destructive',
        });
        return;
      }

      await deleteCustomView(selectedView.id);
      setViews(views.filter(view => view.id !== selectedView.id));
      setIsDeleteDialogOpen(false);

      toast({
        title: 'Visualização excluída',
        description: 'A visualização personalizada foi excluída com sucesso.',
      });

      if (onViewsChange) onViewsChange();
    } catch (error) {
      console.error('Erro ao excluir visualização:', error);
      toast({
        title: 'Erro ao excluir visualização',
        description: 'Não foi possível excluir a visualização. Tente novamente mais tarde.',
        variant: 'destructive',
      });
    }
  };

  // Renderizar visualização
  const renderView = (view: CustomView) => {
    const layoutIcon =
      view.layout === 'kanban' ? <LayoutGrid className="w-4 h-4" /> :
      view.layout === 'lista' ? <List className="w-4 h-4" /> :
      <TableIcon className="w-4 h-4" />;

    return (
      <div
        className="flex items-center justify-between p-3 rounded-lg border border-neutral-200 dark:border-neutral-700 hover:bg-neutral-50 dark:hover:bg-neutral-800 transition-colors"
        key={view.id}
      >
        <div className="flex items-center gap-3">
          <div
            className="w-8 h-8 rounded-md flex items-center justify-center"
            style={{ backgroundColor: view.cor || '#3b82f6' }}
          >
            {layoutIcon}
          </div>
          <div>
            <div className="flex items-center gap-2">
              <h3 className="font-medium">{view.nome}</h3>
              {view.is_default && (
                <Badge variant="secondary">Padrão</Badge>
              )}
              {view.is_public && (
                <Badge variant="outline">Pública</Badge>
              )}
              {view.is_favorite && (
                <Star className="w-4 h-4 text-amber-500 fill-amber-500" />
              )}
            </div>
            {view.descricao && (
              <p className="text-sm text-muted-foreground">{view.descricao}</p>
            )}
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleToggleFavorite(view)}
            title={view.is_favorite ? "Remover dos favoritos" : "Adicionar aos favoritos"}
          >
            <Star className={`w-4 h-4 ${view.is_favorite ? 'text-amber-500 fill-amber-500' : ''}`} />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleOpenEditDialog(view)}
            disabled={view.is_default}
          >
            <Edit className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleOpenDeleteDialog(view)}
            disabled={view.is_default}
          >
            <Trash2 className="w-4 h-4" />
          </Button>
        </div>
      </div>
    );
  };

  // Renderizar formulário de visualização
  const renderViewForm = () => (
    <div className="space-y-4 py-4">
      <div className="space-y-2">
        <Label htmlFor="nome">Nome</Label>
        <Input
          id="nome"
          value={formData.nome}
          onChange={(e) => setFormData({ ...formData, nome: e.target.value })}
          placeholder="Ex: Meus Precatórios Federais"
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="descricao">Descrição (opcional)</Label>
        <Textarea
          id="descricao"
          value={formData.descricao}
          onChange={(e) => setFormData({ ...formData, descricao: e.target.value })}
          placeholder="Descreva o propósito desta visualização"
          rows={2}
        />
      </div>
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label>Cor</Label>
          <ColorPicker
            color={formData.cor}
            onChange={(color) => setFormData({ ...formData, cor: color })}
          />
        </div>
        <div className="space-y-2">
          <Label>Ícone</Label>
          <IconPicker
            value={formData.icone}
            onChange={(icon) => setFormData({ ...formData, icone: icon })}
          />
        </div>
      </div>
      <div className="space-y-2">
        <Label>Layout</Label>
        <Select
          value={formData.layout}
          onValueChange={(value: 'kanban' | 'lista' | 'tabela') =>
            setFormData({ ...formData, layout: value })
          }
        >
          <SelectTrigger>
            <SelectValue placeholder="Selecione o layout" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="kanban">
              <div className="flex items-center gap-2">
                <LayoutGrid className="w-4 h-4" />
                <span>Kanban</span>
              </div>
            </SelectItem>
            <SelectItem value="lista">
              <div className="flex items-center gap-2">
                <List className="w-4 h-4" />
                <span>Lista</span>
              </div>
            </SelectItem>
            <SelectItem value="tabela">
              <div className="flex items-center gap-2">
                <TableIcon className="w-4 h-4" />
                <span>Tabela</span>
              </div>
            </SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div className="flex items-center space-x-2">
        <Checkbox
          id="is_public"
          checked={formData.is_public}
          onCheckedChange={(checked) =>
            setFormData({ ...formData, is_public: checked as boolean })
          }
        />
        <Label htmlFor="is_public">Tornar esta visualização pública para todos os usuários</Label>
      </div>

      <Separator />

      <div className="space-y-4">
        <h3 className="text-sm font-medium">Tags</h3>
        {tagsLoading ? (
          <div className="flex items-center justify-center p-4">
            <Loader2 className="w-4 h-4 animate-spin text-primary mr-2" />
            <span>Carregando tags...</span>
          </div>
        ) : (
          <div className="grid grid-cols-2 gap-2">
            {tags.map(tag => (
              <div key={tag.id} className="flex items-center space-x-2">
                <Checkbox
                  id={`tag-${tag.id}`}
                  checked={formData.tags_selecionadas.includes(tag.id)}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setFormData({
                        ...formData,
                        tags_selecionadas: [...formData.tags_selecionadas, tag.id]
                      });
                    } else {
                      setFormData({
                        ...formData,
                        tags_selecionadas: formData.tags_selecionadas.filter(id => id !== tag.id)
                      });
                    }
                  }}
                />
                <Label htmlFor={`tag-${tag.id}`} className="flex items-center gap-2">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: tag.cor }}
                  ></div>
                  <span>{tag.nome}</span>
                </Label>
              </div>
            ))}
          </div>
        )}
      </div>

      <div className="space-y-4">
        <h3 className="text-sm font-medium">Colunas</h3>
        {columnsLoading ? (
          <div className="flex items-center justify-center p-4">
            <Loader2 className="w-4 h-4 animate-spin text-primary mr-2" />
            <span>Carregando colunas...</span>
          </div>
        ) : (
          <div className="grid grid-cols-2 gap-2">
            {columns.map(column => (
              <div key={column.id} className="flex items-center space-x-2">
                <Checkbox
                  id={`column-${column.id}`}
                  checked={formData.colunas_selecionadas.includes(column.id)}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setFormData({
                        ...formData,
                        colunas_selecionadas: [...formData.colunas_selecionadas, column.id]
                      });
                    } else {
                      setFormData({
                        ...formData,
                        colunas_selecionadas: formData.colunas_selecionadas.filter(id => id !== column.id)
                      });
                    }
                  }}
                />
                <Label htmlFor={`column-${column.id}`} className="flex items-center gap-2">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: column.cor }}
                  ></div>
                  <span>{column.nome}</span>
                </Label>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Visualizações Personalizadas</h2>
          <p className="text-muted-foreground">
            Crie e gerencie visualizações personalizadas para o quadro Kanban
          </p>
        </div>
        <Button onClick={handleOpenCreateDialog}>
          <Plus className="w-4 h-4 mr-2" />
          Nova Visualização
        </Button>
      </div>

      {loading ? (
        <div className="flex items-center justify-center p-8">
          <Loader2 className="w-8 h-8 animate-spin text-primary" />
        </div>
      ) : views.length === 0 ? (
        <Card>
          <CardContent className="p-8 flex flex-col items-center justify-center text-center">
            <Layout className="w-12 h-12 text-muted-foreground mb-4" />
            <h3 className="text-xl font-medium mb-2">Nenhuma visualização personalizada</h3>
            <p className="text-muted-foreground mb-4">
              Crie visualizações personalizadas para organizar seu quadro Kanban
            </p>
            <Button onClick={handleOpenCreateDialog}>
              <Plus className="w-4 h-4 mr-2" />
              Criar Visualização
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {/* Visualizações favoritas */}
          {views.some(view => view.is_favorite) && (
            <div className="space-y-2">
              <h3 className="text-lg font-medium flex items-center gap-2">
                <Star className="w-4 h-4 text-amber-500 fill-amber-500" />
                Favoritas
              </h3>
              <div className="grid grid-cols-1 gap-2">
                {views
                  .filter(view => view.is_favorite)
                  .map(renderView)}
              </div>
            </div>
          )}

          {/* Visualizações padrão */}
          {views.some(view => view.is_default) && (
            <div className="space-y-2">
              <h3 className="text-lg font-medium">Padrão</h3>
              <div className="grid grid-cols-1 gap-2">
                {views
                  .filter(view => view.is_default)
                  .map(renderView)}
              </div>
            </div>
          )}

          {/* Outras visualizações */}
          <div className="space-y-2">
            <h3 className="text-lg font-medium">Minhas Visualizações</h3>
            <div className="grid grid-cols-1 gap-2">
              {views
                .filter(view => !view.is_default && !view.is_favorite)
                .map(renderView)}
            </div>
          </div>
        </div>
      )}

      {/* Diálogo de criação */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Criar Nova Visualização</DialogTitle>
            <DialogDescription>
              Crie uma visualização personalizada para o quadro Kanban
            </DialogDescription>
          </DialogHeader>
          {renderViewForm()}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
              Cancelar
            </Button>
            <Button onClick={handleCreateView}>
              Criar Visualização
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Diálogo de edição */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Editar Visualização</DialogTitle>
            <DialogDescription>
              Atualize as configurações da visualização personalizada
            </DialogDescription>
          </DialogHeader>
          {renderViewForm()}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancelar
            </Button>
            <Button onClick={handleUpdateView}>
              Salvar Alterações
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Diálogo de exclusão */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Excluir Visualização</DialogTitle>
            <DialogDescription>
              Tem certeza que deseja excluir esta visualização?
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Atenção</AlertTitle>
              <AlertDescription>
                Esta ação irá excluir permanentemente a visualização "{selectedView?.nome}".
                Esta ação não pode ser desfeita.
              </AlertDescription>
            </Alert>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancelar
            </Button>
            <Button variant="destructive" onClick={handleDeleteView}>
              Excluir Visualização
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
