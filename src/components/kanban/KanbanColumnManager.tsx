import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useToast } from '@/components/ui/use-toast';
import { KanbanColuna, KanbanColunaPersonalizada, ColunaPermission, fetchAllColunas, createColuna, updateColuna, deleteColuna, getColunaPermissions, updateColunaPermission, reordenarColunas } from '@/services/kanbanService';
import { Loader2, Plus, Trash2, Edit, AlertTriangle, LayoutGrid, Check, X, Users, Settings, Info, GripVertical, MoveVertical } from 'lucide-react';
import { ColorPicker } from '@/components/ui/color-picker';
import { IconPicker } from '@/components/ui/icon-picker';
import { supabase } from '@/lib/supabase';
import { DragDropContext, Droppable, Draggable, DropResult } from 'react-beautiful-dnd';

interface User {
  id: string;
  nome: string;
  email: string;
  avatar_url?: string;
  role?: string;
}

interface KanbanColumnManagerProps {
  onColumnsChange?: () => void;
}

export function KanbanColumnManager({ onColumnsChange }: KanbanColumnManagerProps) {
  const { toast } = useToast();
  const [columns, setColumns] = useState<KanbanColuna[]>([]);
  const [loading, setLoading] = useState(true);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isPermissionsDialogOpen, setIsPermissionsDialogOpen] = useState(false);
  const [selectedColumn, setSelectedColumn] = useState<KanbanColuna | null>(null);
  const [columnPermissions, setColumnPermissions] = useState<ColunaPermission[]>([]);
  const [permissionsLoading, setPermissionsLoading] = useState(false);
  const [allUsers, setAllUsers] = useState<User[]>([]);
  const [usersLoading, setUsersLoading] = useState(false);
  const [reordering, setReordering] = useState(false);

  // Form states
  const [formData, setFormData] = useState<{
    nome: string;
    cor: string;
    descricao: string;
    icone: string;
  }>({
    nome: '',
    cor: '#3b82f6',
    descricao: '',
    icone: 'layout',
  });

  // Carregar colunas
  useEffect(() => {
    loadColumns();
  }, []);

  const loadColumns = async () => {
    try {
      setLoading(true);
      const columnsData = await fetchAllColunas();
      setColumns(columnsData);
    } catch (error) {
      console.error('Erro ao carregar colunas:', error);
      toast({
        title: 'Erro ao carregar colunas',
        description: 'Não foi possível carregar as colunas do Kanban. Tente novamente mais tarde.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Carregar usuários
  const loadUsers = async () => {
    try {
      setUsersLoading(true);
      const { data, error } = await supabase
        .from('profiles')
        .select('id, nome, email, avatar_url, role')
        .order('nome');

      if (error) throw error;
      setAllUsers(data || []);
    } catch (error) {
      console.error('Erro ao carregar usuários:', error);
      toast({
        title: 'Erro ao carregar usuários',
        description: 'Não foi possível carregar a lista de usuários.',
        variant: 'destructive',
      });
    } finally {
      setUsersLoading(false);
    }
  };

  // Carregar permissões de uma coluna
  const loadColumnPermissions = async (columnId: string) => {
    try {
      setPermissionsLoading(true);
      const permissions = await getColunaPermissions(columnId);
      setColumnPermissions(permissions);
    } catch (error) {
      console.error('Erro ao carregar permissões da coluna:', error);
      toast({
        title: 'Erro ao carregar permissões',
        description: 'Não foi possível carregar as permissões da coluna.',
        variant: 'destructive',
      });
    } finally {
      setPermissionsLoading(false);
    }
  };

  // Abrir diálogo de criação
  const handleOpenCreateDialog = () => {
    setFormData({
      nome: '',
      cor: '#3b82f6',
      descricao: '',
      icone: 'layout',
    });
    setIsCreateDialogOpen(true);
  };

  // Abrir diálogo de edição
  const handleOpenEditDialog = (column: KanbanColuna) => {
    setSelectedColumn(column);
    setFormData({
      nome: column.nome,
      cor: column.cor,
      descricao: column.descricao || '',
      icone: column.icone || 'layout',
    });
    setIsEditDialogOpen(true);
  };

  // Abrir diálogo de exclusão
  const handleOpenDeleteDialog = (column: KanbanColuna) => {
    setSelectedColumn(column);
    setIsDeleteDialogOpen(true);
  };

  // Abrir diálogo de permissões
  const handleOpenPermissionsDialog = async (column: KanbanColuna) => {
    setSelectedColumn(column);
    await loadUsers();
    await loadColumnPermissions(column.id);
    setIsPermissionsDialogOpen(true);
  };

  // Criar coluna
  const handleCreateColumn = async () => {
    try {
      if (!formData.nome.trim()) {
        toast({
          title: 'Nome obrigatório',
          description: 'O nome da coluna é obrigatório.',
          variant: 'destructive',
        });
        return;
      }

      const newColumn = await createColuna({
        nome: formData.nome,
        cor: formData.cor,
        descricao: formData.descricao,
        icone: formData.icone,
        ordem: columns.length + 1,
        is_default: false,
        is_system: false,
      });

      setColumns([...columns, newColumn]);
      setIsCreateDialogOpen(false);

      toast({
        title: 'Coluna criada',
        description: 'A coluna foi criada com sucesso.',
      });

      if (onColumnsChange) onColumnsChange();
    } catch (error) {
      console.error('Erro ao criar coluna:', error);
      toast({
        title: 'Erro ao criar coluna',
        description: 'Não foi possível criar a coluna. Tente novamente mais tarde.',
        variant: 'destructive',
      });
    }
  };

  // Atualizar coluna
  const handleUpdateColumn = async () => {
    try {
      if (!selectedColumn) return;

      if (!formData.nome.trim()) {
        toast({
          title: 'Nome obrigatório',
          description: 'O nome da coluna é obrigatório.',
          variant: 'destructive',
        });
        return;
      }

      // Verificar se é uma coluna do sistema
      if (selectedColumn.is_system) {
        toast({
          title: 'Operação não permitida',
          description: 'Não é possível modificar colunas do sistema.',
          variant: 'destructive',
        });
        return;
      }

      const updatedColumn = await updateColuna(selectedColumn.id, {
        nome: formData.nome,
        cor: formData.cor,
        descricao: formData.descricao,
        icone: formData.icone,
      });

      setColumns(columns.map(column => column.id === updatedColumn.id ? updatedColumn : column));
      setIsEditDialogOpen(false);

      toast({
        title: 'Coluna atualizada',
        description: 'A coluna foi atualizada com sucesso.',
      });

      if (onColumnsChange) onColumnsChange();
    } catch (error) {
      console.error('Erro ao atualizar coluna:', error);
      toast({
        title: 'Erro ao atualizar coluna',
        description: 'Não foi possível atualizar a coluna. Tente novamente mais tarde.',
        variant: 'destructive',
      });
    }
  };

  // Excluir coluna
  const handleDeleteColumn = async () => {
    try {
      if (!selectedColumn) return;

      // Verificar se é uma coluna do sistema ou padrão
      if (selectedColumn.is_system || selectedColumn.is_default) {
        toast({
          title: 'Operação não permitida',
          description: 'Não é possível excluir colunas do sistema ou padrão.',
          variant: 'destructive',
        });
        return;
      }

      await deleteColuna(selectedColumn.id);
      setColumns(columns.filter(column => column.id !== selectedColumn.id));
      setIsDeleteDialogOpen(false);

      toast({
        title: 'Coluna excluída',
        description: 'A coluna foi excluída com sucesso.',
      });

      if (onColumnsChange) onColumnsChange();
    } catch (error) {
      console.error('Erro ao excluir coluna:', error);
      toast({
        title: 'Erro ao excluir coluna',
        description: 'Não foi possível excluir a coluna. Tente novamente mais tarde.',
        variant: 'destructive',
      });
    }
  };

  // Atualizar permissão de usuário
  const handleUpdatePermission = async (userId: string, permission: 'can_view' | 'can_edit' | 'can_delete' | 'can_move_cards', value: boolean) => {
    try {
      if (!selectedColumn) return;

      await updateColunaPermission(selectedColumn.id, userId, {
        [permission]: value,
      });

      // Atualizar estado local
      setColumnPermissions(columnPermissions.map(p => {
        if (p.user_id === userId) {
          return { ...p, [permission]: value };
        }
        return p;
      }));

      toast({
        title: 'Permissão atualizada',
        description: 'A permissão foi atualizada com sucesso.',
      });
    } catch (error) {
      console.error('Erro ao atualizar permissão:', error);
      toast({
        title: 'Erro ao atualizar permissão',
        description: 'Não foi possível atualizar a permissão. Tente novamente mais tarde.',
        variant: 'destructive',
      });
    }
  };

  // Reordenar colunas
  const handleDragEnd = async (result: DropResult) => {
    if (!result.destination) return;

    const items = Array.from(columns);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    // Atualizar ordens
    const updatedItems = items.map((item, index) => ({
      ...item,
      ordem: index + 1
    }));

    setColumns(updatedItems);

    try {
      setReordering(true);
      await reordenarColunas(updatedItems.map(item => ({ id: item.id, ordem: item.ordem })));

      toast({
        title: 'Colunas reordenadas',
        description: 'A ordem das colunas foi atualizada com sucesso.',
      });

      if (onColumnsChange) onColumnsChange();
    } catch (error) {
      console.error('Erro ao reordenar colunas:', error);
      toast({
        title: 'Erro ao reordenar colunas',
        description: 'Não foi possível atualizar a ordem das colunas. Tente novamente mais tarde.',
        variant: 'destructive',
      });

      // Reverter para a ordem original
      loadColumns();
    } finally {
      setReordering(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Gerenciamento de Colunas do Kanban</h2>
          <p className="text-muted-foreground">
            Crie e gerencie colunas para o quadro Kanban
          </p>
        </div>
        <Button onClick={handleOpenCreateDialog}>
          <Plus className="w-4 h-4 mr-2" />
          Nova Coluna
        </Button>
      </div>

      {loading ? (
        <div className="flex items-center justify-center p-8">
          <Loader2 className="w-8 h-8 animate-spin text-primary" />
        </div>
      ) : columns.length === 0 ? (
        <Card>
          <CardContent className="p-8 flex flex-col items-center justify-center text-center">
            <LayoutGrid className="w-12 h-12 text-muted-foreground mb-4" />
            <h3 className="text-xl font-medium mb-2">Nenhuma coluna encontrada</h3>
            <p className="text-muted-foreground mb-4">
              Crie colunas para organizar seu quadro Kanban
            </p>
            <Button onClick={handleOpenCreateDialog}>
              <Plus className="w-4 h-4 mr-2" />
              Criar Coluna
            </Button>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>Colunas do Kanban</CardTitle>
            <CardDescription>
              Arraste para reordenar as colunas. As colunas aparecerão no quadro Kanban na ordem definida aqui.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <DragDropContext onDragEnd={handleDragEnd}>
              <Droppable droppableId="columns">
                {(provided) => (
                  <div
                    {...provided.droppableProps}
                    ref={provided.innerRef}
                    className="space-y-2"
                  >
                    {columns.map((column, index) => (
                      <Draggable
                        key={column.id}
                        draggableId={column.id}
                        index={index}
                        isDragDisabled={column.is_system || reordering}
                      >
                        {(provided) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            className="flex items-center justify-between p-3 rounded-lg border border-neutral-200 dark:border-neutral-700 hover:bg-neutral-50 dark:hover:bg-neutral-800 transition-colors"
                          >
                            <div className="flex items-center gap-3">
                              <div {...provided.dragHandleProps} className="cursor-grab">
                                <GripVertical className="w-5 h-5 text-muted-foreground" />
                              </div>
                              <div
                                className="w-8 h-8 rounded-md flex items-center justify-center"
                                style={{ backgroundColor: column.cor }}
                              >
                                <LayoutGrid className="w-4 h-4 text-white" />
                              </div>
                              <div>
                                <div className="flex items-center gap-2">
                                  <h3 className="font-medium">{column.nome}</h3>
                                  {column.is_system && (
                                    <Badge variant="outline">Sistema</Badge>
                                  )}
                                  {column.is_default && (
                                    <Badge variant="secondary">Padrão</Badge>
                                  )}
                                </div>
                                {column.descricao && (
                                  <p className="text-sm text-muted-foreground">{column.descricao}</p>
                                )}
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleOpenPermissionsDialog(column)}
                                disabled={reordering}
                              >
                                <Users className="w-4 h-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleOpenEditDialog(column)}
                                disabled={column.is_system || reordering}
                              >
                                <Edit className="w-4 h-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleOpenDeleteDialog(column)}
                                disabled={(column.is_system || column.is_default) || reordering}
                              >
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            </div>
                          </div>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </DragDropContext>
          </CardContent>
        </Card>
      )}

      {/* Diálogo de criação */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Criar Nova Coluna</DialogTitle>
            <DialogDescription>
              Crie uma nova coluna para o quadro Kanban
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="nome">Nome</Label>
              <Input
                id="nome"
                value={formData.nome}
                onChange={(e) => setFormData({ ...formData, nome: e.target.value })}
                placeholder="Ex: Em Andamento"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="descricao">Descrição (opcional)</Label>
              <Textarea
                id="descricao"
                value={formData.descricao}
                onChange={(e) => setFormData({ ...formData, descricao: e.target.value })}
                placeholder="Descreva o propósito desta coluna"
                rows={3}
              />
            </div>
            <div className="space-y-2">
              <Label>Cor</Label>
              <ColorPicker
                color={formData.cor}
                onChange={(color) => setFormData({ ...formData, cor: color })}
              />
            </div>
            <div className="space-y-2">
              <Label>Ícone</Label>
              <IconPicker
                value={formData.icone}
                onChange={(icon) => setFormData({ ...formData, icone: icon })}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
              Cancelar
            </Button>
            <Button onClick={handleCreateColumn}>
              Criar Coluna
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Diálogo de edição */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Editar Coluna</DialogTitle>
            <DialogDescription>
              Atualize as informações da coluna
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="edit-nome">Nome</Label>
              <Input
                id="edit-nome"
                value={formData.nome}
                onChange={(e) => setFormData({ ...formData, nome: e.target.value })}
                placeholder="Ex: Em Andamento"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-descricao">Descrição (opcional)</Label>
              <Textarea
                id="edit-descricao"
                value={formData.descricao}
                onChange={(e) => setFormData({ ...formData, descricao: e.target.value })}
                placeholder="Descreva o propósito desta coluna"
                rows={3}
              />
            </div>
            <div className="space-y-2">
              <Label>Cor</Label>
              <ColorPicker
                color={formData.cor}
                onChange={(color) => setFormData({ ...formData, cor: color })}
              />
            </div>
            <div className="space-y-2">
              <Label>Ícone</Label>
              <IconPicker
                value={formData.icone}
                onChange={(icon) => setFormData({ ...formData, icone: icon })}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancelar
            </Button>
            <Button onClick={handleUpdateColumn}>
              Salvar Alterações
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Diálogo de exclusão */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Excluir Coluna</DialogTitle>
            <DialogDescription>
              Tem certeza que deseja excluir esta coluna?
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Atenção</AlertTitle>
              <AlertDescription>
                Esta ação irá remover a coluna "{selectedColumn?.nome}" e mover todos os precatórios associados para a coluna padrão.
                Esta ação pode ser revertida na seção de itens excluídos.
              </AlertDescription>
            </Alert>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancelar
            </Button>
            <Button variant="destructive" onClick={handleDeleteColumn}>
              Excluir Coluna
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Diálogo de permissões */}
      <Dialog open={isPermissionsDialogOpen} onOpenChange={setIsPermissionsDialogOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Permissões da Coluna</DialogTitle>
            <DialogDescription>
              Gerencie quem pode ver, editar, excluir e mover cards na coluna "{selectedColumn?.nome}"
            </DialogDescription>
          </DialogHeader>

          {permissionsLoading || usersLoading ? (
            <div className="flex items-center justify-center p-8">
              <Loader2 className="w-8 h-8 animate-spin text-primary" />
            </div>
          ) : (
            <div className="py-4">
              <Alert className="mb-4">
                <Info className="h-4 w-4" />
                <AlertTitle>Informação</AlertTitle>
                <AlertDescription>
                  O criador da coluna sempre tem todas as permissões. Administradores também têm acesso completo a todas as colunas.
                  Colunas do sistema e padrão têm regras especiais de permissão.
                </AlertDescription>
              </Alert>

              <ScrollArea className="h-[400px]">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Usuário</TableHead>
                      <TableHead className="w-[80px] text-center">Visualizar</TableHead>
                      <TableHead className="w-[80px] text-center">Editar</TableHead>
                      <TableHead className="w-[80px] text-center">Excluir</TableHead>
                      <TableHead className="w-[80px] text-center">Mover Cards</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {allUsers.map(user => {
                      const permission = columnPermissions.find(p => p.user_id === user.id);
                      const isCreator = selectedColumn?.criado_por === user.id;
                      const isAdmin = user.role === 'admin';
                      const isSystemColumn = selectedColumn?.is_system;
                      const isDefaultColumn = selectedColumn?.is_default;

                      return (
                        <TableRow key={user.id}>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <div className="w-8 h-8 rounded-full bg-muted flex items-center justify-center">
                                {user.avatar_url ? (
                                  <img
                                    src={user.avatar_url}
                                    alt={user.nome}
                                    className="w-8 h-8 rounded-full"
                                  />
                                ) : (
                                  <Users className="w-4 h-4" />
                                )}
                              </div>
                              <div>
                                <p className="font-medium">{user.nome}</p>
                                <p className="text-xs text-muted-foreground">{user.email}</p>
                              </div>
                              {isCreator && (
                                <Badge variant="outline" className="ml-2">Criador</Badge>
                              )}
                              {isAdmin && (
                                <Badge variant="default" className="ml-2">Admin</Badge>
                              )}
                            </div>
                          </TableCell>
                          <TableCell className="text-center">
                            {isCreator || isAdmin || (isDefaultColumn && !isSystemColumn) ? (
                              <Check className="w-4 h-4 mx-auto text-green-500" />
                            ) : (
                              <Switch
                                checked={permission?.can_view || false}
                                onCheckedChange={(checked) =>
                                  handleUpdatePermission(user.id, 'can_view', checked)
                                }
                              />
                            )}
                          </TableCell>
                          <TableCell className="text-center">
                            {isCreator || isAdmin ? (
                              <Check className="w-4 h-4 mx-auto text-green-500" />
                            ) : isSystemColumn ? (
                              <X className="w-4 h-4 mx-auto text-red-500" />
                            ) : (
                              <Switch
                                checked={permission?.can_edit || false}
                                onCheckedChange={(checked) =>
                                  handleUpdatePermission(user.id, 'can_edit', checked)
                                }
                              />
                            )}
                          </TableCell>
                          <TableCell className="text-center">
                            {isCreator || isAdmin ? (
                              isSystemColumn || isDefaultColumn ? (
                                <X className="w-4 h-4 mx-auto text-red-500" />
                              ) : (
                                <Check className="w-4 h-4 mx-auto text-green-500" />
                              )
                            ) : isSystemColumn || isDefaultColumn ? (
                              <X className="w-4 h-4 mx-auto text-red-500" />
                            ) : (
                              <Switch
                                checked={permission?.can_delete || false}
                                onCheckedChange={(checked) =>
                                  handleUpdatePermission(user.id, 'can_delete', checked)
                                }
                              />
                            )}
                          </TableCell>
                          <TableCell className="text-center">
                            {isCreator || isAdmin ? (
                              <Check className="w-4 h-4 mx-auto text-green-500" />
                            ) : (
                              <Switch
                                checked={permission?.can_move_cards || false}
                                onCheckedChange={(checked) =>
                                  handleUpdatePermission(user.id, 'can_move_cards', checked)
                                }
                              />
                            )}
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </ScrollArea>
            </div>
          )}

          <DialogFooter>
            <Button onClick={() => setIsPermissionsDialogOpen(false)}>
              Fechar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
