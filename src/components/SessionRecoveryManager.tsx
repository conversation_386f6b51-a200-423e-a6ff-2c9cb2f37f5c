import { useEffect, useState, useCallback } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { supabase, refreshSupabaseSession, SUPABASE_STORAGE_KEY, SUPABASE_BACKUP_KEY } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { RefreshCw, AlertCircle, CheckCircle2 } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';

/**
 * Componente avançado para gerenciar a recuperação de sessão
 * - Monitora eventos de visibilidade e foco
 * - Detecta problemas de sessão e tenta recuperar automaticamente
 * - Fornece um botão de reconexão manual como último recurso
 * - Mostra notificações claras sobre o estado da sessão
 */
export function SessionRecoveryManager() {
  const { toast } = useToast();
  const navigate = useNavigate();
  const { refreshSession } = useAuth();
  const [isRecovering, setIsRecovering] = useState(false);
  const [recoveryAttempts, setRecoveryAttempts] = useState(0);
  const [lastRecoveryTime, setLastRecoveryTime] = useState(0);
  const [showRecoveryButton, setShowRecoveryButton] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'online' | 'offline' | 'recovering'>('online');

  // Função para verificar a sessão
  const checkSession = useCallback(async () => {
    try {
      // Evitar verificações muito frequentes
      const now = Date.now();
      if (now - lastRecoveryTime < 60000) { // Aumentado para 60 segundos para reduzir frequência
        return;
      }

      console.log('[SessionRecovery] Verificando sessão...');
      const { data } = await supabase.auth.getSession();

      if (!data.session) {
        console.warn('[SessionRecovery] Sessão não encontrada, iniciando recuperação');
        handleSessionRecovery();
      } else {
        // Verificar se a sessão está prestes a expirar
        const expiryTime = new Date(data.session.expires_at * 1000);
        const now = new Date();
        const timeToExpiry = expiryTime.getTime() - now.getTime();

        if (timeToExpiry < 5 * 60 * 1000) { // 5 minutos
          console.warn(`[SessionRecovery] Sessão expira em ${Math.floor(timeToExpiry / 60000)} minutos, atualizando`);
          handleSessionRecovery();
        } else {
          console.log(`[SessionRecovery] Sessão válida, expira em ${Math.floor(timeToExpiry / 60000)} minutos`);
          setConnectionStatus('online');
          setShowRecoveryButton(false);
        }
      }
    } catch (error) {
      console.error('[SessionRecovery] Erro ao verificar sessão:', error);
      handleSessionRecovery();
    }
  }, [lastRecoveryTime]);

  // Função para recuperar a sessão
  const handleSessionRecovery = useCallback(async () => {
    if (isRecovering) return;

    try {
      setIsRecovering(true);
      setConnectionStatus('recovering');
      setLastRecoveryTime(Date.now());
      setRecoveryAttempts(prev => prev + 1);

      console.log('[SessionRecovery] Tentando recuperar sessão...');

      // Estratégia 1: Tentar recuperar a sessão diretamente do localStorage
      try {
        console.log('[SessionRecovery] Tentando recuperar sessão do localStorage...');
        const tokenStr = localStorage.getItem(SUPABASE_STORAGE_KEY);

        if (tokenStr) {
          const tokenData = JSON.parse(tokenStr);

          if (tokenData && tokenData.access_token) {
            console.log('[SessionRecovery] Token encontrado no localStorage, tentando definir sessão...');

            // Tentar definir a sessão com o token do localStorage
            const { data: setData, error: setError } = await supabase.auth.setSession({
              access_token: tokenData.access_token,
              refresh_token: tokenData.refresh_token || '',
            });

            if (!setError && setData.session) {
              console.log('[SessionRecovery] Sessão recuperada com sucesso do localStorage');

              // Reconexão bem-sucedida
              setConnectionStatus('online');
              setShowRecoveryButton(false);
              setRecoveryAttempts(0);

              // Removido toast de conexão restaurada para evitar notificações excessivas

              // Disparar evento de reconexão
              const reconnectEvent = new CustomEvent('app-reconnected', {
                detail: {
                  timestamp: Date.now(),
                  source: 'session-recovery-localstorage'
                }
              });
              document.dispatchEvent(reconnectEvent);

              return;
            }
          }
        }
      } catch (localStorageError) {
        console.error('[SessionRecovery] Erro ao recuperar sessão do localStorage:', localStorageError);
      }

      // Estratégia 2: Tentar atualizar a sessão usando o mecanismo padrão
      console.log('[SessionRecovery] Tentando atualizar sessão via refreshSupabaseSession...');
      const success = await refreshSupabaseSession();

      if (success) {
        console.log('[SessionRecovery] Sessão recuperada com sucesso');
        setConnectionStatus('online');
        setShowRecoveryButton(false);
        setRecoveryAttempts(0);

        // Removido toast de conexão restaurada para evitar notificações excessivas

        // Disparar evento de reconexão
        const reconnectEvent = new CustomEvent('app-reconnected', {
          detail: {
            timestamp: Date.now(),
            source: 'session-recovery-refresh'
          }
        });
        document.dispatchEvent(reconnectEvent);
      } else {
        // Estratégia 3: Tentar usar o authManager para recuperar a sessão
        console.log('[SessionRecovery] Tentando recuperar sessão via authManager...');
        const authManagerSuccess = await refreshSession();

        if (authManagerSuccess) {
          console.log('[SessionRecovery] Sessão recuperada com sucesso via authManager');
          setConnectionStatus('online');
          setShowRecoveryButton(false);
          setRecoveryAttempts(0);

          // Removido toast de conexão restaurada para evitar notificações excessivas

          // Disparar evento de reconexão
          const reconnectEvent = new CustomEvent('app-reconnected', {
            detail: {
              timestamp: Date.now(),
              source: 'session-recovery-auth-manager'
            }
          });
          document.dispatchEvent(reconnectEvent);
        } else {
          console.warn('[SessionRecovery] Falha ao recuperar sessão automaticamente');
          setConnectionStatus('offline');
          setShowRecoveryButton(true);

          // Removido toasts de problemas de conexão para evitar notificações excessivas
        }
      }
    } catch (error) {
      console.error('[SessionRecovery] Erro ao recuperar sessão:', error);
      setConnectionStatus('offline');
      setShowRecoveryButton(true);
    } finally {
      setIsRecovering(false);
    }
  }, [isRecovering, recoveryAttempts, toast, navigate, refreshSession]);

  // Função para reconexão manual
  const handleManualReconnect = useCallback(async () => {
    try {
      setIsRecovering(true);
      setConnectionStatus('recovering');

      console.log('[SessionRecovery] Tentando reconexão manual...');

      // Estratégia 1: Tentar recuperar a sessão diretamente do localStorage
      try {
        console.log('[SessionRecovery] Tentando recuperar sessão do localStorage...');
        const tokenStr = localStorage.getItem(SUPABASE_STORAGE_KEY);

        if (tokenStr) {
          const tokenData = JSON.parse(tokenStr);

          if (tokenData && tokenData.access_token) {
            console.log('[SessionRecovery] Token encontrado no localStorage, tentando definir sessão...');

            // Tentar definir a sessão com o token do localStorage
            const { data: setData, error: setError } = await supabase.auth.setSession({
              access_token: tokenData.access_token,
              refresh_token: tokenData.refresh_token || '',
            });

            if (!setError && setData.session) {
              console.log('[SessionRecovery] Sessão recuperada com sucesso do localStorage');

              // Reconexão bem-sucedida
              setConnectionStatus('online');
              setShowRecoveryButton(false);
              setRecoveryAttempts(0);

              // Removido toast de conexão restaurada para evitar notificações excessivas

              // Disparar evento de reconexão
              const reconnectEvent = new CustomEvent('app-reconnected', {
                detail: {
                  timestamp: Date.now(),
                  source: 'manual-reconnect-localstorage'
                }
              });
              document.dispatchEvent(reconnectEvent);

              return;
            }
          }
        }
      } catch (localStorageError) {
        console.error('[SessionRecovery] Erro ao recuperar sessão do localStorage:', localStorageError);
      }

      // Estratégia 2: Tentar usar o refreshSession do contexto de autenticação
      console.log('[SessionRecovery] Tentando reconexão via refreshSession do contexto...');
      const success = await refreshSession();

      if (success) {
        console.log('[SessionRecovery] Reconexão manual bem-sucedida');
        setConnectionStatus('online');
        setShowRecoveryButton(false);
        setRecoveryAttempts(0);

        // Removido toast de conexão restaurada

        // Disparar evento de reconexão
        const reconnectEvent = new CustomEvent('app-reconnected', {
          detail: {
            timestamp: Date.now(),
            source: 'manual-reconnect'
          }
        });
        document.dispatchEvent(reconnectEvent);
        return;
      }

      // Estratégia 3: Tentar atualizar a sessão diretamente com o Supabase
      console.log('[SessionRecovery] Tentando atualizar sessão diretamente com o Supabase...');
      const { data, error } = await supabase.auth.refreshSession();

      if (!error && data.session) {
        console.log('[SessionRecovery] Sessão atualizada com sucesso via Supabase');

        // Reconexão bem-sucedida
        setConnectionStatus('online');
        setShowRecoveryButton(false);
        setRecoveryAttempts(0);

        // Removido toast de conexão restaurada para evitar notificações excessivas

        // Disparar evento de reconexão
        const reconnectEvent = new CustomEvent('app-reconnected', {
          detail: {
            timestamp: Date.now(),
            source: 'manual-reconnect-direct'
          }
        });
        document.dispatchEvent(reconnectEvent);
        return;
      }

      // Se todas as estratégias falharam
      console.warn('[SessionRecovery] Falha na reconexão manual após todas as tentativas');
      setConnectionStatus('offline');

      // Removido toast de falha na reconexão
    } catch (error) {
      console.error('[SessionRecovery] Erro na reconexão manual:', error);
      setConnectionStatus('offline');

      // Removido toast de erro na reconexão
    } finally {
      setIsRecovering(false);
    }
  }, [refreshSession, toast, navigate]);

  // Monitorar eventos de visibilidade
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        console.log('[SessionRecovery] Página voltou a ficar visível, verificando sessão');
        checkSession();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [checkSession]);

  // Monitorar eventos de foco
  useEffect(() => {
    const handleFocus = () => {
      console.log('[SessionRecovery] Janela recebeu foco, verificando sessão');
      checkSession();
    };

    window.addEventListener('focus', handleFocus);

    return () => {
      window.removeEventListener('focus', handleFocus);
    };
  }, [checkSession]);

  // Monitorar eventos de sessão
  useEffect(() => {
    const handleSessionProblem = (event: Event) => {
      const customEvent = event as CustomEvent;
      console.log('[SessionRecovery] Evento de problema de sessão recebido:', customEvent.detail);
      handleSessionRecovery();
    };

    const handleSessionRefreshed = (event: Event) => {
      const customEvent = event as CustomEvent;
      console.log('[SessionRecovery] Evento de sessão atualizada recebido:', customEvent.detail);
      setConnectionStatus('online');
      setShowRecoveryButton(false);
      setRecoveryAttempts(0);
    };

    const handleSessionRecovered = (event: Event) => {
      const customEvent = event as CustomEvent;
      console.log('[SessionRecovery] Evento de sessão recuperada recebido:', customEvent.detail);
      setConnectionStatus('online');
      setShowRecoveryButton(false);
      setRecoveryAttempts(0);

      // Disparar evento de reconexão para atualizar os componentes
      const reconnectEvent = new CustomEvent('app-reconnected', {
        detail: {
          timestamp: Date.now(),
          source: 'session-recovered-event'
        }
      });
      document.dispatchEvent(reconnectEvent);
    };

    const handleAppReconnected = (event: Event) => {
      const customEvent = event as CustomEvent;
      console.log('[SessionRecovery] Evento de reconexão recebido:', customEvent.detail);
      setConnectionStatus('online');
      setShowRecoveryButton(false);
      setRecoveryAttempts(0);
    };

    // Registrar listeners para todos os eventos relacionados à sessão
    document.addEventListener('session-problem', handleSessionProblem);
    document.addEventListener('supabase-session-refreshed', handleSessionRefreshed);
    document.addEventListener('supabase-session-recovered', handleSessionRecovered);
    document.addEventListener('auth-session-recovered', handleSessionRecovered);
    document.addEventListener('auth-session-refreshed', handleSessionRefreshed);
    document.addEventListener('app-reconnected', handleAppReconnected);

    return () => {
      // Remover todos os listeners ao desmontar
      document.removeEventListener('session-problem', handleSessionProblem);
      document.removeEventListener('supabase-session-refreshed', handleSessionRefreshed);
      document.removeEventListener('supabase-session-recovered', handleSessionRecovered);
      document.removeEventListener('auth-session-recovered', handleSessionRecovered);
      document.removeEventListener('auth-session-refreshed', handleSessionRefreshed);
      document.removeEventListener('app-reconnected', handleAppReconnected);
    };
  }, [handleSessionRecovery]);

  // Verificar sessão ao montar o componente
  useEffect(() => {
    checkSession();

    // Verificar a cada 10 minutos (aumentado para reduzir frequência)
    const interval = setInterval(checkSession, 10 * 60 * 1000);

    return () => {
      clearInterval(interval);
    };
  }, [checkSession]);

  // Renderizar botão de reconexão apenas quando necessário
  if (!showRecoveryButton) {
    return null;
  }

  // Tentar reconectar automaticamente sem mostrar UI
  if (connectionStatus === 'offline' && !isRecovering && showRecoveryButton) {
    setTimeout(() => {
      handleManualReconnect();
    }, 5000); // Tentar reconectar a cada 5 segundos
  }

  // Não exibir botão para evitar poluição visual
  return null;
}

export default SessionRecoveryManager;
