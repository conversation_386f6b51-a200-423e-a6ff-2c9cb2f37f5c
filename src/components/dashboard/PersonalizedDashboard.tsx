import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  LayoutDashboard, 
  Users, 
  FileText, 
  CheckSquare, 
  TrendingUp, 
  DollarSign,
  Clock,
  AlertTriangle,
  Eye,
  EyeOff,
  Settings
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { usePermissions } from '@/hooks/usePermissions';
import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';

interface DashboardMetrics {
  totalPrecatorios: number;
  precatoriosConcluidos: number;
  precatoriosEmAndamento: number;
  totalClientes: number;
  clientesAtivos: number;
  totalTarefas: number;
  tarefasConcluidas: number;
  tarefasPendentes: number;
  valorTotalPrecatorios: number;
  faturamentoMensal: number;
}

interface DashboardWidget {
  id: string;
  title: string;
  icon: React.ReactNode;
  value: string | number;
  subtitle?: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  permission: {
    resource: string;
    action: string;
  };
  visible: boolean;
}

export function PersonalizedDashboard() {
  const { user } = useAuth();
  const { hasPermission, canSeeData, isAdmin, isManager, loading: permissionsLoading } = usePermissions();
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [widgets, setWidgets] = useState<DashboardWidget[]>([]);

  useEffect(() => {
    if (!permissionsLoading && user) {
      loadDashboardData();
      setupWidgets();
    }
  }, [user, permissionsLoading, hasPermission, canSeeData]);

  const loadDashboardData = async () => {
    if (!user) return;

    try {
      setLoading(true);

      // Carregar métricas baseadas nas permissões do usuário
      let metricsData: DashboardMetrics = {
        totalPrecatorios: 0,
        precatoriosConcluidos: 0,
        precatoriosEmAndamento: 0,
        totalClientes: 0,
        clientesAtivos: 0,
        totalTarefas: 0,
        tarefasConcluidas: 0,
        tarefasPendentes: 0,
        valorTotalPrecatorios: 0,
        faturamentoMensal: 0
      };

      // Carregar dados de precatórios se tiver permissão
      if (hasPermission('precatorios', 'view')) {
        const precatoriosQuery = supabase
          .from('precatorios')
          .select('id, valor_total, status, responsavel_id, created_by')
          .eq('is_deleted', false);

        // Filtrar dados baseado na visibilidade
        if (!canSeeData('all')) {
          if (canSeeData('own')) {
            precatoriosQuery.or(`responsavel_id.eq.${user.id},created_by.eq.${user.id}`);
          }
        }

        const { data: precatorios } = await precatoriosQuery;

        if (precatorios) {
          metricsData.totalPrecatorios = precatorios.length;
          metricsData.precatoriosConcluidos = precatorios.filter(p => p.status === 'concluido').length;
          metricsData.precatoriosEmAndamento = metricsData.totalPrecatorios - metricsData.precatoriosConcluidos;
          metricsData.valorTotalPrecatorios = precatorios.reduce((sum, p) => sum + (Number(p.valor_total) || 0), 0);
        }
      }

      // Carregar dados de clientes se tiver permissão
      if (hasPermission('clientes', 'view')) {
        const clientesQuery = supabase
          .from('clientes')
          .select('id, status, responsavel_id, created_by');

        // Filtrar dados baseado na visibilidade
        if (!canSeeData('all')) {
          if (canSeeData('own')) {
            clientesQuery.or(`responsavel_id.eq.${user.id},created_by.eq.${user.id}`);
          }
        }

        const { data: clientes } = await clientesQuery;

        if (clientes) {
          metricsData.totalClientes = clientes.length;
          metricsData.clientesAtivos = clientes.filter(c => c.status === 'ativo').length;
        }
      }

      // Carregar dados de tarefas se tiver permissão
      if (hasPermission('tasks', 'view')) {
        const tasksQuery = supabase
          .from('tasks')
          .select('id, status, assignee_id, created_by');

        // Filtrar dados baseado na visibilidade
        if (!canSeeData('all')) {
          if (canSeeData('own')) {
            tasksQuery.or(`assignee_id.eq.${user.id},created_by.eq.${user.id}`);
          }
        }

        const { data: tasks } = await tasksQuery;

        if (tasks) {
          metricsData.totalTarefas = tasks.length;
          metricsData.tarefasConcluidas = tasks.filter(t => t.status === 'concluida').length;
          metricsData.tarefasPendentes = metricsData.totalTarefas - metricsData.tarefasConcluidas;
        }
      }

      setMetrics(metricsData);
    } catch (error) {
      console.error('Erro ao carregar dados do dashboard:', error);
      toast.error('Erro ao carregar dados do dashboard');
    } finally {
      setLoading(false);
    }
  };

  const setupWidgets = () => {
    const allWidgets: DashboardWidget[] = [
      {
        id: 'total-precatorios',
        title: 'Total de Precatórios',
        icon: <FileText className="h-4 w-4" />,
        value: metrics?.totalPrecatorios || 0,
        subtitle: `${metrics?.precatoriosConcluidos || 0} concluídos`,
        permission: { resource: 'precatorios', action: 'view' },
        visible: hasPermission('precatorios', 'view')
      },
      {
        id: 'valor-precatorios',
        title: 'Valor Total',
        icon: <DollarSign className="h-4 w-4" />,
        value: `R$ ${(metrics?.valorTotalPrecatorios || 0).toLocaleString('pt-BR')}`,
        subtitle: 'Em precatórios',
        permission: { resource: 'dashboard', action: 'view_financial' },
        visible: hasPermission('dashboard', 'view_financial')
      },
      {
        id: 'total-clientes',
        title: 'Total de Clientes',
        icon: <Users className="h-4 w-4" />,
        value: metrics?.totalClientes || 0,
        subtitle: `${metrics?.clientesAtivos || 0} ativos`,
        permission: { resource: 'clientes', action: 'view' },
        visible: hasPermission('clientes', 'view')
      },
      {
        id: 'total-tarefas',
        title: 'Minhas Tarefas',
        icon: <CheckSquare className="h-4 w-4" />,
        value: metrics?.totalTarefas || 0,
        subtitle: `${metrics?.tarefasPendentes || 0} pendentes`,
        permission: { resource: 'tasks', action: 'view' },
        visible: hasPermission('tasks', 'view')
      },
      {
        id: 'performance',
        title: 'Performance da Equipe',
        icon: <TrendingUp className="h-4 w-4" />,
        value: '85%',
        subtitle: 'Meta mensal',
        trend: { value: 5, isPositive: true },
        permission: { resource: 'dashboard', action: 'view_team_performance' },
        visible: hasPermission('dashboard', 'view_team_performance')
      },
      {
        id: 'alertas',
        title: 'Alertas',
        icon: <AlertTriangle className="h-4 w-4" />,
        value: 3,
        subtitle: 'Requerem atenção',
        permission: { resource: 'dashboard', action: 'view' },
        visible: hasPermission('dashboard', 'view')
      }
    ];

    setWidgets(allWidgets.filter(widget => widget.visible));
  };

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Bom dia';
    if (hour < 18) return 'Boa tarde';
    return 'Boa noite';
  };

  const getRoleDisplayName = (role: string) => {
    const roleNames = {
      admin: 'Administrador',
      gerente_precatorio: 'Gerente de Precatório',
      gerente_operacional: 'Gerente Operacional',
      assistente: 'Assistente'
    };
    return roleNames[role as keyof typeof roleNames] || role;
  };

  if (loading || permissionsLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-muted rounded w-1/3 mb-2"></div>
          <div className="h-4 bg-muted rounded w-1/2"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-muted rounded w-1/2 mb-2"></div>
                <div className="h-8 bg-muted rounded w-1/3"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header personalizado */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">
            {getGreeting()}, {user?.nome || user?.email}!
          </h1>
          <div className="flex items-center gap-2 mt-2">
            <Badge variant="outline">{getRoleDisplayName(user?.role || '')}</Badge>
            {isAdmin && <Badge variant="destructive">Admin</Badge>}
            {isManager && <Badge variant="default">Gerente</Badge>}
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={loadDashboardData}>
            <TrendingUp className="h-4 w-4 mr-2" />
            Atualizar
          </Button>
        </div>
      </div>

      {/* Widgets baseados em permissões */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {widgets.map((widget) => (
          <Card key={widget.id} className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{widget.title}</CardTitle>
              {widget.icon}
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{widget.value}</div>
              {widget.subtitle && (
                <p className="text-xs text-muted-foreground">{widget.subtitle}</p>
              )}
              {widget.trend && (
                <div className={`flex items-center text-xs ${
                  widget.trend.isPositive ? 'text-green-600' : 'text-red-600'
                }`}>
                  <TrendingUp className={`h-3 w-3 mr-1 ${
                    widget.trend.isPositive ? '' : 'rotate-180'
                  }`} />
                  {widget.trend.value}% vs mês anterior
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Seção de acesso rápido baseada em permissões */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <LayoutDashboard className="h-5 w-5" />
            Acesso Rápido
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {hasPermission('precatorios', 'create') && (
              <Button variant="outline" className="h-20 flex flex-col gap-2">
                <FileText className="h-6 w-6" />
                <span className="text-sm">Novo Precatório</span>
              </Button>
            )}
            {hasPermission('clientes', 'create') && (
              <Button variant="outline" className="h-20 flex flex-col gap-2">
                <Users className="h-6 w-6" />
                <span className="text-sm">Novo Cliente</span>
              </Button>
            )}
            {hasPermission('tasks', 'create') && (
              <Button variant="outline" className="h-20 flex flex-col gap-2">
                <CheckSquare className="h-6 w-6" />
                <span className="text-sm">Nova Tarefa</span>
              </Button>
            )}
            {hasPermission('reports', 'view') && (
              <Button variant="outline" className="h-20 flex flex-col gap-2">
                <TrendingUp className="h-6 w-6" />
                <span className="text-sm">Relatórios</span>
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Informações de visibilidade de dados */}
      {!canSeeData('all') && (
        <Card className="border-amber-200 bg-amber-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 text-amber-800">
              <Eye className="h-4 w-4" />
              <span className="text-sm font-medium">
                Visualização limitada: Você está vendo apenas {canSeeData('team') ? 'dados da sua equipe' : 'seus próprios dados'}
              </span>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
