import { useEffect, useState } from 'react';
import { initTabSessionManager, forceSessionCheck } from '@/lib/tabSessionManager';
import { toast } from 'sonner';

interface TabSessionHandlerProps {
  onSessionRecovered?: () => void;
  onSessionLost?: () => void;
}

/**
 * Component that handles tab session management
 * This component should be mounted at the application root level
 */
export function TabSessionHandler({ onSessionRecovered, onSessionLost }: TabSessionHandlerProps) {
  const [isInitialized, setIsInitialized] = useState(false);
  const [isRecovering, setIsRecovering] = useState(false);

  // Initialize the tab session manager
  useEffect(() => {
    if (!isInitialized) {
      console.log('[TabSessionHandler] Initializing tab session manager');
      initTabSessionManager();
      setIsInitialized(true);
    }
  }, [isInitialized]);

  // Listen for session events
  useEffect(() => {
    const handleReconnect = (event: Event) => {
      const customEvent = event as CustomEvent;
      console.log('[TabSessionHandler] Reconnect event received:', customEvent.detail);
      
      if (onSessionRecovered) {
        onSessionRecovered();
      }
    };

    const handleSessionRefreshed = (event: Event) => {
      const customEvent = event as CustomEvent;
      console.log('[TabSessionHandler] Session refreshed event received:', customEvent.detail);
      
      if (customEvent.detail?.success && onSessionRecovered) {
        onSessionRecovered();
      }
    };

    const handleSessionExpired = (event: Event) => {
      const customEvent = event as CustomEvent;
      console.log('[TabSessionHandler] Session expired event received:', customEvent.detail);
      
      if (onSessionLost) {
        onSessionLost();
      }
    };

    const handleVisibilityChange = async () => {
      if (document.visibilityState === 'visible') {
        console.log('[TabSessionHandler] Tab became visible, checking session');
        
        // Avoid multiple simultaneous recovery attempts
        if (isRecovering) {
          console.log('[TabSessionHandler] Already recovering session, skipping');
          return;
        }
        
        setIsRecovering(true);
        
        try {
          // Add a small delay to allow the browser to stabilize
          await new Promise(resolve => setTimeout(resolve, 500));
          
          // Force a session check
          const success = await forceSessionCheck();
          
          if (success) {
            console.log('[TabSessionHandler] Session check successful after visibility change');
            
            if (onSessionRecovered) {
              onSessionRecovered();
            }
          } else {
            console.warn('[TabSessionHandler] Session check failed after visibility change');
            
            // Show a toast notification
            toast.warning('Verificando conexão', {
              description: 'Tentando reconectar ao servidor...',
              duration: 3000
            });
            
            // Try one more time after a delay
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            const retrySuccess = await forceSessionCheck();
            
            if (retrySuccess) {
              console.log('[TabSessionHandler] Session recovery successful on retry');
              
              if (onSessionRecovered) {
                onSessionRecovered();
              }
              
              toast.success('Conexão restabelecida', {
                description: 'Sua sessão foi recuperada com sucesso',
                duration: 3000
              });
            } else {
              console.error('[TabSessionHandler] Session recovery failed on retry');
              
              if (onSessionLost) {
                onSessionLost();
              }
              
              toast.error('Problema de conexão', {
                description: 'Não foi possível reconectar. Tente recarregar a página.',
                duration: 5000
              });
            }
          }
        } catch (error) {
          console.error('[TabSessionHandler] Error during visibility change session check:', error);
        } finally {
          setIsRecovering(false);
        }
      }
    };

    // Register event listeners
    document.addEventListener('app-reconnected', handleReconnect);
    document.addEventListener('supabase-session-refreshed', handleSessionRefreshed);
    document.addEventListener('session-expired', handleSessionExpired);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Clean up event listeners on unmount
    return () => {
      document.removeEventListener('app-reconnected', handleReconnect);
      document.removeEventListener('supabase-session-refreshed', handleSessionRefreshed);
      document.removeEventListener('session-expired', handleSessionExpired);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [onSessionRecovered, onSessionLost, isRecovering]);

  // This component doesn't render anything
  return null;
}

export default TabSessionHandler;
