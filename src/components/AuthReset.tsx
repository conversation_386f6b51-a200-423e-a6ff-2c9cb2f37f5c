import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertCircle, RefreshCw, Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { supabase } from '@/lib/supabase';

export function AuthReset() {
  const [isResetting, setIsResetting] = useState(false);

  const handleReset = async () => {
    try {
      setIsResetting(true);
      
      // 1. Mostrar toast informativo
      toast.info('Iniciando processo de reset de autenticação...');
      
      // 2. Fazer logout do Supabase
      await supabase.auth.signOut({ scope: 'global' });
      
      // 3. Limpar localStorage
      const keysToRemove = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && (
          key.startsWith('sb-') || 
          key.includes('supabase') || 
          key.includes('auth') ||
          key.includes('user') ||
          key.includes('profile')
        )) {
          keysToRemove.push(key);
        }
      }
      
      // Remover as chaves em um loop separado para evitar problemas com o índice
      keysToRemove.forEach(key => {
        console.log(`Removendo chave do localStorage: ${key}`);
        localStorage.removeItem(key);
      });
      
      // 4. Limpar sessionStorage
      const sessionKeysToRemove = [];
      for (let i = 0; i < sessionStorage.length; i++) {
        const key = sessionStorage.key(i);
        if (key && (
          key.startsWith('sb-') || 
          key.includes('supabase') || 
          key.includes('auth') ||
          key.includes('user') ||
          key.includes('profile')
        )) {
          sessionKeysToRemove.push(key);
        }
      }
      
      sessionKeysToRemove.forEach(key => {
        console.log(`Removendo chave do sessionStorage: ${key}`);
        sessionStorage.removeItem(key);
      });
      
      // 5. Limpar cookies relacionados ao Supabase
      document.cookie.split(';').forEach(cookie => {
        const [name] = cookie.trim().split('=');
        if (name && (
          name.startsWith('sb-') || 
          name.includes('supabase') || 
          name.includes('auth')
        )) {
          console.log(`Removendo cookie: ${name}`);
          document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
        }
      });
      
      // 6. Mostrar toast de sucesso
      toast.success('Autenticação resetada com sucesso! Redirecionando para login...');
      
      // 7. Aguardar um momento antes de redirecionar
      setTimeout(() => {
        // 8. Forçar recarregamento completo da página para limpar o estado da aplicação
        window.location.href = '/login';
      }, 1500);
      
    } catch (error) {
      console.error('Erro ao resetar autenticação:', error);
      toast.error('Erro ao resetar autenticação. Tente novamente.');
      setIsResetting(false);
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto border-red-200 dark:border-red-800">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center gap-2 text-red-600 dark:text-red-400">
          <AlertCircle className="h-5 w-5" />
          Problemas de Conexão
        </CardTitle>
        <CardDescription>
          Se você está enfrentando problemas para carregar dados ou acessar o sistema, tente resetar sua autenticação.
        </CardDescription>
      </CardHeader>
      <CardContent className="pb-3">
        <p className="text-sm text-muted-foreground mb-4">
          Esta ação irá desconectar você do sistema e limpar todos os dados de autenticação armazenados localmente. 
          Você precisará fazer login novamente após o reset.
        </p>
        <div className="bg-amber-50 dark:bg-amber-950/30 border border-amber-200 dark:border-amber-800 rounded-md p-3">
          <p className="text-sm text-amber-800 dark:text-amber-300">
            <strong>Nota:</strong> Use esta opção apenas se você estiver enfrentando problemas de carregamento 
            ou erros persistentes de autenticação.
          </p>
        </div>
      </CardContent>
      <CardFooter>
        <Button 
          variant="destructive" 
          className="w-full"
          onClick={handleReset}
          disabled={isResetting}
        >
          {isResetting ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Resetando Autenticação...
            </>
          ) : (
            <>
              <RefreshCw className="h-4 w-4 mr-2" />
              Resetar Autenticação e Fazer Logout
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}
