import React, { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { RefreshCw, AlertCircle, CheckCircle2 } from 'lucide-react';
import { LoadingIndicator } from '@/components/ui/loading-indicator';
import { toast } from 'sonner';
import { authManager } from '@/lib/authManager';
import { forceSessionCheck } from '@/lib/sessionHeartbeat';
import { supabase, SUPABASE_STORAGE_KEY } from '@/lib/supabase';

interface AutoReconnectProps {
  /**
   * Função a ser executada após a reconexão bem-sucedida
   */
  onReconnect?: () => void;

  /**
   * Função a ser executada quando a reconexão falhar após várias tentativas
   */
  onReconnectFailure?: () => void;

  /**
   * Se o componente deve ser exibido como um botão flutuante
   * @default false
   */
  floating?: boolean;

  /**
   * Se o componente deve tentar reconectar automaticamente
   * @default true
   */
  autoReconnect?: boolean;

  /**
   * Intervalo entre tentativas de reconexão automática (em ms)
   * @default 30000 (30 segundos)
   */
  reconnectInterval?: number;

  /**
   * Número máximo de tentativas de reconexão antes de chamar onReconnectFailure
   * @default 3
   */
  maxReconnectAttempts?: number;
}

/**
 * Componente que detecta problemas de conexão e oferece opção de reconexão
 */
export function AutoReconnect({
  onReconnect,
  onReconnectFailure,
  floating = false,
  autoReconnect = true,
  reconnectInterval = 30000,
  maxReconnectAttempts = 3
}: AutoReconnectProps) {
  const { refreshSession, resetAuth } = useAuth();
  const [isReconnecting, setIsReconnecting] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'online' | 'offline' | 'checking'>('online');
  const [lastReconnectAttempt, setLastReconnectAttempt] = useState(0);
  const [reconnectAttemptCount, setReconnectAttemptCount] = useState(0);

  // Verificar status de conexão
  useEffect(() => {
    const handleOnline = () => {
      setConnectionStatus('online');
      // Removido toast de conexão restaurada
    };

    const handleOffline = () => {
      setConnectionStatus('offline');
      // Removido toast de conexão perdida
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Verificar status inicial
    setConnectionStatus(navigator.onLine ? 'online' : 'offline');

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Tentar reconectar automaticamente
  useEffect(() => {
    // Não tentar reconectar se estiver na página de login
    if (window.location.pathname === "/login") return;

    // Não tentar reconexão automática se desativada ou se já excedeu o número máximo de tentativas
    if (!autoReconnect || reconnectAttemptCount >= maxReconnectAttempts) return;

    // Não tentar reconectar se estiver offline (deixar para o usuário iniciar manualmente)
    if (connectionStatus === 'offline') return;

    const now = Date.now();
    if (now - lastReconnectAttempt < reconnectInterval) return;

    let isMounted = true;

    // Adicionar listener para o evento de reconexão
    const handleReconnectEvent = (event: Event) => {
      const customEvent = event as CustomEvent;
      console.log('[AutoReconnect] Evento de reconexão recebido:', customEvent.detail);

      if (isMounted) {
        setConnectionStatus('online');
        setReconnectAttemptCount(0); // Resetar contador de tentativas

        if (onReconnect) onReconnect();
      }
    };

    document.addEventListener('app-reconnected', handleReconnectEvent);

    // Adicionar listener para o evento de problema de sessão
    const handleSessionProblem = (event: Event) => {
      const customEvent = event as CustomEvent;
      console.warn('[AutoReconnect] Evento de problema de sessão recebido:', customEvent.detail);

      if (isMounted) {
        setConnectionStatus('offline');
      }
    };

    document.addEventListener('session-problem', handleSessionProblem);

    const checkConnection = async () => {
      try {
        // Verificar novamente se estamos na página de login
        if (window.location.pathname === "/login") return;

        if (isMounted) setConnectionStatus('checking');

        // Usar o sistema de heartbeat para verificar a sessão
        console.log('[AutoReconnect] Verificando conexão usando o sistema de heartbeat...');
        const sessionValid = await forceSessionCheck();

        if (sessionValid) {
          if (isMounted) {
            setConnectionStatus('online');
            setReconnectAttemptCount(0); // Resetar contador de tentativas
          }
        } else {
          // Se o heartbeat falhar, tentar usar o refreshSession do contexto
          console.log('[AutoReconnect] Heartbeat falhou, tentando refreshSession do contexto...');
          const success = await refreshSession();

          if (success) {
            if (isMounted) {
              setConnectionStatus('online');
              setReconnectAttemptCount(0); // Resetar contador de tentativas
            }
            if (onReconnect && isMounted) onReconnect();
          } else {
            if (isMounted) {
              setConnectionStatus('offline');
              // Incrementar contador de tentativas
              const newAttemptCount = reconnectAttemptCount + 1;
              setReconnectAttemptCount(newAttemptCount);

              // Se excedeu o número máximo de tentativas
              if (newAttemptCount >= maxReconnectAttempts && onReconnectFailure) {
                console.warn(`Excedido número máximo de tentativas automáticas (${maxReconnectAttempts})`);
                // Removido toast de falha persistente de conexão
              }
            }
          }
        }
      } catch (error) {
        console.error('Erro ao verificar conexão:', error);
        if (isMounted) {
          setConnectionStatus('offline');
          // Incrementar contador de tentativas
          setReconnectAttemptCount(prev => prev + 1);
        }
      }
    };

    const timer = setTimeout(() => {
      if (isMounted) setLastReconnectAttempt(Date.now());
      checkConnection();
    }, reconnectInterval);

    return () => {
      isMounted = false;
      clearTimeout(timer);
      document.removeEventListener('app-reconnected', handleReconnectEvent);
      document.removeEventListener('session-problem', handleSessionProblem);
    };
  }, [autoReconnect, connectionStatus, lastReconnectAttempt, onReconnect, onReconnectFailure, reconnectAttemptCount, reconnectInterval, refreshSession, maxReconnectAttempts]);

  // Função para tentar reconectar manualmente
  const handleReconnect = async () => {
    // Não tentar reconectar se estiver na página de login
    if (window.location.pathname === "/login") {
      // Removido toast de já está na página de login
      return;
    }

    try {
      // Incrementar contador de tentativas
      const newAttemptCount = reconnectAttemptCount + 1;
      setReconnectAttemptCount(newAttemptCount);

      setIsReconnecting(true);
      setConnectionStatus('checking');

      // Verificar se excedeu o número máximo de tentativas
      if (newAttemptCount > maxReconnectAttempts) {
        console.warn(`Excedido número máximo de tentativas (${maxReconnectAttempts})`);

        // Chamar callback de falha se existir
        if (onReconnectFailure) {
          toast.error('Falha persistente de conexão', {
            description: 'Redirecionando para página de login...'
          });
          onReconnectFailure();
          return;
        }
      }

      console.log('[AutoReconnect] Iniciando processo de reconexão...');

      // Estratégia 1: Tentar recuperar a sessão diretamente do localStorage
      try {
        console.log('[AutoReconnect] Tentando recuperar sessão do localStorage...');
        const tokenStr = localStorage.getItem(SUPABASE_STORAGE_KEY);

        if (tokenStr) {
          const tokenData = JSON.parse(tokenStr);

          if (tokenData && tokenData.access_token) {
            console.log('[AutoReconnect] Token encontrado no localStorage, tentando definir sessão...');

            // Tentar definir a sessão com o token do localStorage
            const { data: setData, error: setError } = await supabase.auth.setSession({
              access_token: tokenData.access_token,
              refresh_token: tokenData.refresh_token || '',
            });

            if (!setError && setData.session) {
              console.log('[AutoReconnect] Sessão recuperada com sucesso do localStorage');

              // Reconexão bem-sucedida
              setConnectionStatus('online');
              setReconnectAttemptCount(0); // Resetar contador de tentativas

              // Removido toast de reconectado com sucesso

              // Disparar evento de reconexão para atualizar os componentes
              const reconnectEvent = new CustomEvent('app-reconnected', {
                detail: {
                  timestamp: Date.now(),
                  source: 'manual-reconnect-localstorage'
                }
              });
              document.dispatchEvent(reconnectEvent);

              if (onReconnect) onReconnect();
              return;
            }
          }
        }
      } catch (localStorageError) {
        console.error('[AutoReconnect] Erro ao recuperar sessão do localStorage:', localStorageError);
      }

      // Estratégia 2: Tentar usar o authManager para verificar a sessão
      console.log('[AutoReconnect] Tentando reconectar usando o authManager...');
      const sessionValid = await authManager.checkSession();

      if (sessionValid) {
        // Reconexão bem-sucedida
        setConnectionStatus('online');
        setReconnectAttemptCount(0); // Resetar contador de tentativas

        // Removido toast de reconectado com sucesso

        // Disparar evento de reconexão para atualizar os componentes
        const reconnectEvent = new CustomEvent('app-reconnected', {
          detail: {
            timestamp: Date.now(),
            source: 'manual-reconnect'
          }
        });
        document.dispatchEvent(reconnectEvent);

        if (onReconnect) onReconnect();
        return;
      }

      // Estratégia 3: Tentar usar o refreshSession do contexto de autenticação
      console.log('[AutoReconnect] Tentando reconectar usando o refreshSession do contexto...');
      const success = await refreshSession();

      if (success) {
        // Reconexão bem-sucedida
        setConnectionStatus('online');
        setReconnectAttemptCount(0); // Resetar contador de tentativas

        // Removido toast de reconectado com sucesso

        // Disparar evento de reconexão para atualizar os componentes
        const reconnectEvent = new CustomEvent('app-reconnected', {
          detail: {
            timestamp: Date.now(),
            source: 'manual-reconnect-fallback'
          }
        });
        document.dispatchEvent(reconnectEvent);

        if (onReconnect) onReconnect();
      } else {
        // Estratégia 4: Tentar atualizar a sessão diretamente com o Supabase
        console.log('[AutoReconnect] Tentando atualizar sessão diretamente com o Supabase...');
        const { data, error } = await supabase.auth.refreshSession();

        if (!error && data.session) {
          console.log('[AutoReconnect] Sessão atualizada com sucesso via Supabase');

          // Reconexão bem-sucedida
          setConnectionStatus('online');
          setReconnectAttemptCount(0); // Resetar contador de tentativas

          // Removido toast de reconectado com sucesso

          // Disparar evento de reconexão para atualizar os componentes
          const reconnectEvent = new CustomEvent('app-reconnected', {
            detail: {
              timestamp: Date.now(),
              source: 'manual-reconnect-direct'
            }
          });
          document.dispatchEvent(reconnectEvent);

          if (onReconnect) onReconnect();
          return;
        }

        // Falha na reconexão após todas as tentativas
        setConnectionStatus('offline');

        // Removido toasts de falha de conexão

        // Chamar callback de falha se existir e excedeu o número máximo de tentativas
        if (newAttemptCount >= maxReconnectAttempts && onReconnectFailure) {
          setTimeout(() => onReconnectFailure(), 2000);
        }
      }
    } catch (error) {
      console.error('Erro ao reconectar:', error);
      setConnectionStatus('offline');

      // Incrementar contador de tentativas
      const newAttemptCount = reconnectAttemptCount + 1;
      setReconnectAttemptCount(newAttemptCount);

      // Verificar se é um erro de autenticação
      const isAuthError = error instanceof Error &&
                         (error.message.includes('authentication') ||
                          error.message.includes('auth') ||
                          error.message.includes('token') ||
                          error.message.includes('session'));

      if (isAuthError) {
        // Removido toast de erro de autenticação

        // Limpar dados locais
        localStorage.removeItem("userProfile");

        // Redirecionar para login após um breve delay
        setTimeout(() => {
          window.location.href = '/login';
        }, 1500);

        return;
      }

      // Removido toast de erro ao reconectar

      // Se excedeu o número máximo de tentativas
      if (newAttemptCount >= maxReconnectAttempts && onReconnectFailure) {
        setTimeout(() => onReconnectFailure(), 2000);
      }
    } finally {
      setIsReconnecting(false);
      setLastReconnectAttempt(Date.now());
    }
  };

  // Função para resetar a autenticação
  const handleReset = async () => {
    try {
      setIsReconnecting(true);

      // Limpar localStorage antes de resetar a autenticação
      localStorage.removeItem("userProfile");

      // Resetar a autenticação
      await resetAuth();

      // Removido toast de autenticação resetada

      // Redirecionar para login imediatamente
      window.location.href = '/login';
    } catch (error) {
      console.error('Erro ao resetar autenticação:', error);
      // Removido toast de erro ao resetar autenticação

      // Em caso de erro, ainda tentar redirecionar para login
      localStorage.removeItem("userProfile");
      window.location.href = '/login';
    } finally {
      setIsReconnecting(false);
    }
  };

  // Se estiver online e não estiver verificando, não exibir nada
  if (connectionStatus === 'online' && !isReconnecting) {
    return null;
  }

  // Não exibir botão flutuante para evitar poluição visual
  if (floating) {
    // Tentar reconectar automaticamente sem mostrar UI
    if (connectionStatus === 'offline' && !isReconnecting) {
      setTimeout(() => {
        handleReconnect();
      }, 5000); // Tentar reconectar a cada 5 segundos
    }
    return null; // Não exibir nada
  }

  // Renderizar componente normal
  return (
    <div className="flex flex-col items-center justify-center gap-4 p-4 rounded-lg border bg-card text-card-foreground shadow-sm">
      <div className="flex items-center gap-2">
        {connectionStatus === 'offline' ? (
          <AlertCircle className="h-5 w-5 text-destructive" />
        ) : connectionStatus === 'checking' ? (
          <RefreshCw className="h-5 w-5 text-muted-foreground animate-spin" />
        ) : (
          <CheckCircle2 className="h-5 w-5 text-primary" />
        )}
        <span className="font-medium">
          {connectionStatus === 'offline'
            ? 'Problemas de conexão detectados'
            : connectionStatus === 'checking'
            ? 'Verificando conexão...'
            : 'Conexão restaurada'}
        </span>
      </div>

      <div className="flex gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={handleReconnect}
          disabled={isReconnecting}
        >
          {isReconnecting ? (
            <LoadingIndicator size="sm" text="Reconectando..." />
          ) : (
            <>
              <RefreshCw className="h-4 w-4 mr-2" />
              Reconectar
            </>
          )}
        </Button>

        <Button
          variant="destructive"
          size="sm"
          onClick={handleReset}
          disabled={isReconnecting}
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Resetar Autenticação
        </Button>
      </div>
    </div>
  );
}
