import { useEffect, useState, useRef } from 'react';
import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';
import { useNavigate } from 'react-router-dom';

/**
 * Componente que lida com problemas de visibilidade de abas
 * Implementa uma solução radical para o problema de carregamento infinito
 */
export function TabVisibilityHandler() {
  const navigate = useNavigate();
  const [isVisible, setIsVisible] = useState(document.visibilityState === 'visible');
  const lastVisibleTimeRef = useRef(Date.now());
  const timeoutRef = useRef<number | null>(null);
  const recoveryAttemptRef = useRef(0);
  const isRecoveringRef = useRef(false);

  // Função para forçar o redirecionamento para a página de login
  const forceRedirectToLogin = (reason: string) => {
    console.log(`[TabVisibility] Forçando redirecionamento para login: ${reason}`);

    // Limpar completamente o estado de autenticação
    try {
      localStorage.removeItem("userProfile");
      sessionStorage.removeItem("userProfile");
      localStorage.removeItem("sb-ubwzukpsqcrwzfbppoux-auth-token");
      sessionStorage.removeItem("sb-ubwzukpsqcrwzfbppoux-auth-token");
      localStorage.removeItem("sb-ubwzukpsqcrwzfbppoux-auth-backup");
      sessionStorage.removeItem("sb-ubwzukpsqcrwzfbppoux-auth-backup");
      localStorage.removeItem("sb-ubwzukpsqcrwzfbppoux-last-refresh");
    } catch (error) {
      console.error("[TabVisibility] Erro ao limpar estado de autenticação:", error);
    }

    // Redirecionar para login com parâmetro forced=true
    window.location.href = `/login?forced=true&reason=${encodeURIComponent(reason)}`;
  };

  // Função para verificar a sessão
  const checkSession = async () => {
    if (isRecoveringRef.current) {
      console.log("[TabVisibility] Já está recuperando a sessão, ignorando");
      return;
    }

    isRecoveringRef.current = true;

    try {
      console.log("[TabVisibility] Verificando sessão após mudança de visibilidade");

      // SOLUÇÃO RADICAL: Forçar recarregamento da página se estiver em uma página protegida
      // e não for a página de login
      if (window.location.pathname !== '/login' &&
          window.location.pathname !== '/register' &&
          window.location.pathname !== '/forgot-password') {

        // Verificar se temos uma sessão válida
        const { data, error } = await supabase.auth.getSession();

        if (error) {
          console.error("[TabVisibility] Erro ao verificar sessão:", error);

          // Tentar uma vez mais antes de redirecionar
          if (recoveryAttemptRef.current < 1) {
            recoveryAttemptRef.current++;

            // Mostrar toast de aviso
            toast.warning("Verificando conexão", {
              description: "Tentando reconectar ao servidor..."
            });

            // Tentar novamente após um pequeno atraso
            setTimeout(() => {
              isRecoveringRef.current = false;
              checkSession();
            }, 1000);

            return false;
          }

          // Em vez de recarregar a página, redirecionar para login
          console.log("[TabVisibility] Redirecionando para login devido a erro de sessão");

          // Salvar a URL atual para retornar após o login
          try {
            sessionStorage.setItem('last_url', window.location.pathname);
          } catch (e) {
            console.error("[TabVisibility] Erro ao salvar URL atual:", e);
          }

          // Redirecionar para login
          forceRedirectToLogin("Erro ao verificar sessão");
          return false;
        }

        if (!data.session) {
          console.warn("[TabVisibility] Nenhuma sessão encontrada");

          // Tentar uma vez mais antes de redirecionar
          if (recoveryAttemptRef.current < 1) {
            recoveryAttemptRef.current++;

            // Mostrar toast de aviso
            toast.warning("Verificando autenticação", {
              description: "Tentando recuperar sua sessão..."
            });

            // Tentar novamente após um pequeno atraso
            setTimeout(() => {
              isRecoveringRef.current = false;
              checkSession();
            }, 1000);

            return false;
          }

          // Se ainda falhar, forçar redirecionamento para login
          forceRedirectToLogin("Sessão expirada ou inválida");
          return false;
        }

        // Verificar se a sessão está prestes a expirar
        const expiryTime = new Date(data.session.expires_at * 1000);
        const now = new Date();
        const timeToExpiry = expiryTime.getTime() - now.getTime();

        console.log(`[TabVisibility] Sessão válida, expira em ${Math.floor(timeToExpiry / 60000)} minutos`);

        // Se a sessão estiver prestes a expirar (menos de 5 minutos), tentar atualizá-la
        if (timeToExpiry < 5 * 60 * 1000) {
          console.log("[TabVisibility] Sessão está prestes a expirar, atualizando...");

          // Atualizar a sessão
          const { data: refreshData, error: refreshError } = await supabase.auth.refreshSession();

          if (refreshError) {
            console.error("[TabVisibility] Erro ao atualizar sessão:", refreshError);

            // Redirecionar para login
            console.log("[TabVisibility] Redirecionando para login devido a erro ao atualizar sessão");
            forceRedirectToLogin("Erro ao atualizar sessão");
            return false;
          }

          if (!refreshData.session) {
            console.warn("[TabVisibility] Nenhuma sessão retornada após atualização");

            // Redirecionar para login
            console.log("[TabVisibility] Redirecionando para login devido a sessão inválida após atualização");
            forceRedirectToLogin("Sessão inválida após atualização");
            return false;
          }

          console.log("[TabVisibility] Sessão atualizada com sucesso");
        }

        // Sessão está válida, resetar contador de tentativas
        recoveryAttemptRef.current = 0;

        // Disparar evento de reconexão
        const reconnectEvent = new CustomEvent('app-reconnected', {
          detail: {
            timestamp: Date.now(),
            source: 'tab-visibility'
          }
        });
        document.dispatchEvent(reconnectEvent);

        // Mostrar toast de sucesso
        toast.success("Conexão restabelecida", {
          description: "Sua sessão foi recuperada com sucesso"
        });

        return true;
      }

      return true;
    } catch (error) {
      console.error("[TabVisibility] Erro ao verificar/atualizar sessão:", error);

      // Incrementar contador de tentativas
      recoveryAttemptRef.current++;

      // Se excedeu o número máximo de tentativas, forçar redirecionamento para login
      if (recoveryAttemptRef.current >= 2) {
        forceRedirectToLogin("Falha ao recuperar sessão após várias tentativas");
        return false;
      }

      // Mostrar toast de erro
      toast.error("Problema de conexão", {
        description: "Tentando reconectar..."
      });

      return false;
    } finally {
      isRecoveringRef.current = false;
    }
  };

  // Efeito para lidar com mudanças de visibilidade
  useEffect(() => {
    const handleVisibilityChange = async () => {
      const isNowVisible = document.visibilityState === 'visible';
      const now = Date.now();

      // Atualizar estado de visibilidade
      setIsVisible(isNowVisible);

      if (isNowVisible) {
        // A aba se tornou visível
        const timeHidden = now - lastVisibleTimeRef.current;
        console.log(`[TabVisibility] Aba se tornou visível após ${Math.round(timeHidden / 1000)} segundos`);

        // Se ficou oculta por mais de 5 segundos, verificar a sessão
        if (timeHidden > 5000) {
          // Limpar qualquer timeout pendente
          if (timeoutRef.current) {
            window.clearTimeout(timeoutRef.current);
          }

          // Definir um timeout para verificar a sessão após um pequeno atraso
          timeoutRef.current = window.setTimeout(async () => {
            // Verificar se ainda estamos visíveis
            if (document.visibilityState === 'visible') {
              await checkSession();
            }
          }, 500);
        }
      } else {
        // A aba se tornou oculta
        console.log("[TabVisibility] Aba se tornou oculta");
        lastVisibleTimeRef.current = now;

        // Limpar qualquer timeout pendente
        if (timeoutRef.current) {
          window.clearTimeout(timeoutRef.current);
          timeoutRef.current = null;
        }
      }
    };

    // Registrar listener para evento de visibilidade
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Registrar listener para evento de foco
    const handleFocus = () => {
      console.log("[TabVisibility] Janela recebeu foco");

      // Se a aba estiver visível, verificar a sessão
      if (document.visibilityState === 'visible') {
        // Limpar qualquer timeout pendente
        if (timeoutRef.current) {
          window.clearTimeout(timeoutRef.current);
        }

        // Definir um timeout para verificar a sessão após um pequeno atraso
        timeoutRef.current = window.setTimeout(async () => {
          // Verificar se ainda estamos visíveis e com foco
          if (document.visibilityState === 'visible' && document.hasFocus()) {
            await checkSession();
          }
        }, 500);
      }
    };

    window.addEventListener('focus', handleFocus);

    // Cleanup
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);

      if (timeoutRef.current) {
        window.clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
    };
  }, []);

  // Este componente não renderiza nada
  return null;
}

export default TabVisibilityHandler;
