import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { CalendarIcon, X, Search, Check, UserX, Loader2, Plus, RefreshCw } from "lucide-react";
import { cn } from "@/lib/utils";
import { Toolt<PERSON>, <PERSON><PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TooltipTrigger } from "@/components/ui/tooltip";
import { supabase } from "@/lib/supabase";
import { toast } from "sonner";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";

interface Cliente {
  id: string;
  nome: string;
  cpf_cnpj: string;
  email?: string;
  telefone?: string;
  endereco?: string;
}

interface SharedPrecatorioFormProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (precatorio: any) => void;
  initialData?: any;
  modoEdicao?: boolean;
}

export function SharedPrecatorioForm(props: SharedPrecatorioFormProps) {
  const { isOpen, onOpenChange, onSave, initialData, modoEdicao = false } = props;

  // Estado para o formulário de precatório
  const [formValues, setFormValues] = useState({
    numero_precatorio: "",
    tribunal: { id: "", nome: "" },
    status: "Novo",
    valor_total: "",
    prioridade: "normal",
    entidade_devedora: "",
    natureza: "",
    data_previsao_pagamento: "",
    tipo: "PRECATORIO", // Padrão para precatório
    observacoes: "",
    desconto: "",
    prazo_atual: "",
    processo_id: "",
    responsavel_id: "",
    tags: []
  });

  // Estado para erros de validação
  const [formErrors, setFormErrors] = useState<{
    numero_precatorio?: string;
    tribunal?: string;
    valor_total?: string;
    desconto?: string;
    data_previsao_pagamento?: string;
  }>({});

  // Estado para busca de cliente
  const [clienteSearch, setClienteSearch] = useState("");
  const [clientesEncontrados, setClientesEncontrados] = useState<Cliente[]>([]);
  const [clienteSelecionado, setClienteSelecionado] = useState<Cliente | null>(null);
  const [buscandoClientes, setBuscandoClientes] = useState(false);
  const [criandoPrecatorio, setCriandoPrecatorio] = useState(false);
  const [tribunais, setTribunais] = useState<{id: string, nome: string, sigla?: string}[]>([]);
  const [statusOptions, setStatusOptions] = useState<{id: number, nome: string, cor: string, status_id: string}[]>([]);

  // Função para carregar status do kanban
  const carregarStatusKanban = async () => {
    try {
      console.log('Carregando status do kanban...');
      const { data, error } = await supabase
        .from('kanban_colunas')
        .select('id, nome, cor, status_id')
        .eq('ativo', true)
        .order('ordem');

      if (error) throw error;

      console.log('Status do kanban carregados:', data);
      setStatusOptions(data || []);

      // Se houver status, atualizar o status padrão no formulário
      if (data && data.length > 0) {
        setFormValues(prev => ({
          ...prev,
          status: data[0].status_id // Usar o status_id do primeiro status como padrão
        }));
      }
    } catch (error) {
      console.error('Erro ao carregar status do kanban:', error);
      toast.error('Erro ao carregar status');
    }
  };

  // Carregar dados iniciais se estiver em modo de edição
  useEffect(() => {
    if (modoEdicao && initialData) {
      setFormValues({
        ...formValues,
        ...initialData,
        numero_precatorio: initialData.numero_precatorio || initialData.numero,
        valor_total: initialData.valor_total?.toString() || initialData.valor?.toString() || "",
      });

      if (initialData.cliente) {
        setClienteSelecionado({
          id: initialData.beneficiario_id || initialData.cliente.id,
          nome: initialData.cliente.nome,
          cpf_cnpj: initialData.cliente.cpf_cnpj || "",
          email: initialData.cliente.email,
          telefone: initialData.cliente.telefone,
        });
      }
    }

    // Carregar dados iniciais
    carregarTribunais();
    carregarStatusKanban();
  }, [modoEdicao, initialData]);

  // Função para carregar tribunais
  const carregarTribunais = async () => {
    try {
      console.log('Carregando tribunais...');
      const { data, error } = await supabase
        .from('tribunais')
        .select('id, nome, sigla')
        .order('nome');

      if (error) throw error;

      console.log('Tribunais carregados:', data);
      setTribunais(data || []);

      // Se não houver tribunais, criar alguns padrão
      if (!data || data.length === 0) {
        console.log('Nenhum tribunal encontrado, criando tribunais padrão...');
        const tribunaisPadrao = [
          { nome: 'Tribunal de Justiça de São Paulo', sigla: 'TJSP', estado: 'SP' },
          { nome: 'Tribunal de Justiça do Rio de Janeiro', sigla: 'TJRJ', estado: 'RJ' },
          { nome: 'Tribunal Regional Federal da 3ª Região', sigla: 'TRF3', estado: 'SP' },
          { nome: 'Tribunal Regional Federal da 2ª Região', sigla: 'TRF2', estado: 'RJ' },
          { nome: 'Tribunal Superior do Trabalho', sigla: 'TST', estado: 'DF' }
        ];

        // Inserir tribunais padrão
        const { data: novosTribunais, error: erroInsercao } = await supabase
          .from('tribunais')
          .insert(tribunaisPadrao)
          .select('id, nome, sigla');

        if (erroInsercao) {
          console.error('Erro ao criar tribunais padrão:', erroInsercao);
          throw erroInsercao;
        }

        console.log('Tribunais padrão criados:', novosTribunais);
        setTribunais(novosTribunais || []);
      }
    } catch (error) {
      console.error('Erro ao carregar tribunais:', error);
      toast.error('Erro ao carregar tribunais');
    }
  };

  // Função para buscar clientes
  const buscarClientes = async () => {
    if (!clienteSearch.trim()) return;

    try {
      setBuscandoClientes(true);
      console.log('Buscando clientes com termo:', clienteSearch);

      // Primeiro, tente buscar por nome
      const { data: dataNome, error: errorNome } = await supabase
        .from('clientes')
        .select('id, nome, cpf_cnpj, email, telefone, endereco')
        .ilike('nome', `%${clienteSearch}%`)
        .limit(5);

      if (errorNome) {
        console.error('Erro ao buscar clientes por nome:', errorNome);
        throw errorNome;
      }

      // Se não encontrou por nome, tente por CPF/CNPJ
      if (!dataNome || dataNome.length === 0) {
        console.log('Nenhum cliente encontrado por nome, tentando por CPF/CNPJ');
        const { data: dataCpf, error: errorCpf } = await supabase
          .from('clientes')
          .select('id, nome, cpf_cnpj, email, telefone, endereco')
          .ilike('cpf_cnpj', `%${clienteSearch}%`)
          .limit(5);

        if (errorCpf) {
          console.error('Erro ao buscar clientes por CPF/CNPJ:', errorCpf);
          throw errorCpf;
        }

        setClientesEncontrados(dataCpf || []);
        console.log('Clientes encontrados por CPF/CNPJ:', dataCpf?.length || 0);
      } else {
        setClientesEncontrados(dataNome);
        console.log('Clientes encontrados por nome:', dataNome.length);
      }
    } catch (error) {
      console.error('Erro ao buscar clientes:', error);
      toast.error('Erro ao buscar clientes');
      setClientesEncontrados([]);
    } finally {
      setBuscandoClientes(false);
    }
  };

  // Função para selecionar um cliente
  const selecionarCliente = (cliente: Cliente) => {
    setClienteSelecionado(cliente);
    setClientesEncontrados([]);
    setClienteSearch("");
  };

  // Função para remover o cliente selecionado
  const removerClienteSelecionado = () => {
    setClienteSelecionado(null);
  };

  // Função para atualizar valores do formulário
  const handleInputChange = (field: string, value: any) => {
    setFormValues(prev => ({
      ...prev,
      [field]: value
    }));

    // Limpar erro do campo se existir
    if (formErrors[field as keyof typeof formErrors]) {
      setFormErrors(prev => ({
        ...prev,
        [field]: undefined
      }));
    }
  };

  // Função para criar um novo precatório
  const criarPrecatorio = async () => {
    // Resetar erros anteriores
    setFormErrors({});

    // Objeto para armazenar erros
    const errors: {[key: string]: string} = {};
    let hasErrors = false;

    // Validar campos obrigatórios
    if (!formValues.numero_precatorio) {
      errors.numero_precatorio = "Número do precatório é obrigatório";
      hasErrors = true;
    }

    if (!formValues.tribunal.id) {
      errors.tribunal = "Tribunal é obrigatório";
      hasErrors = true;
    }

    if (!clienteSelecionado) {
      toast.error('Selecione um cliente para continuar.');
      // Mudar para a aba de cliente
      document.querySelector('[value="cliente"]')?.click();
      hasErrors = true;
    }

    if (!formValues.valor_total) {
      errors.valor_total = "Valor total é obrigatório";
      hasErrors = true;
    } else if (isNaN(parseFloat(formValues.valor_total))) {
      errors.valor_total = "Valor total deve ser um número";
      hasErrors = true;
    }

    if (formValues.desconto && isNaN(parseFloat(formValues.desconto))) {
      errors.desconto = "Desconto deve ser um número";
      hasErrors = true;
    }

    if (hasErrors) {
      setFormErrors(errors);
      return;
    }

    try {
      setCriandoPrecatorio(true);

      // Preparar dados para salvar no banco
      const currentDate = new Date().toISOString();
      const precatorioData = {
        numero_precatorio: formValues.numero_precatorio,
        beneficiario_id: clienteSelecionado.id,
        tribunal_id: formValues.tribunal.id, // Usando o ID correto do tribunal
        status: formValues.status,
        valor_total: parseFloat(formValues.valor_total),
        prioridade: formValues.prioridade || 'normal',
        entidade_devedora: formValues.entidade_devedora || '',
        natureza: formValues.natureza || '',
        data_previsao_pagamento: formValues.data_previsao_pagamento || null,
        tipo: formValues.tipo || 'PRECATORIO',
        categoria: formValues.tipo || 'PRECATORIO',
        observacoes: formValues.observacoes || '',
        desconto: formValues.desconto ? parseFloat(formValues.desconto) : null,
        prazo_atual: formValues.prazo_atual || null,
        processo_id: formValues.processo_id || null,
        responsavel_id: formValues.responsavel_id || null,
        tags: formValues.tags || [],
        data_entrada: currentDate, // Campo obrigatório que estava faltando
        created_at: currentDate,
        updated_at: currentDate,
        is_deleted: false
      };

      console.log('Dados do precatório a serem salvos:', precatorioData);

      let data;
      let error;

      try {
        if (modoEdicao && initialData?.id) {
          // Atualizar precatório existente
          console.log('Atualizando precatório existente:', initialData.id);
          const result = await supabase
            .from('precatorios')
            .update(precatorioData)
            .eq('id', initialData.id)
            .select();

          data = result.data;
          error = result.error;

          if (error) {
            console.error('Erro ao atualizar precatório:', error);
            throw error;
          }
        } else {
          // Criar novo precatório
          console.log('Criando novo precatório');
          const result = await supabase
            .from('precatorios')
            .insert([precatorioData])
            .select();

          console.log('Resultado da criação:', result);
          data = result.data;
          error = result.error;

          if (error) {
            console.error('Erro ao criar precatório:', error);
            throw error;
          }
        }

        if (data && data.length > 0) {
          console.log('Precatório salvo com sucesso:', data[0]);
          toast.success(`Precatório ${modoEdicao ? 'atualizado' : 'criado'} com sucesso!`);
          onSave(data[0]);
          onOpenChange(false);

          // Limpar formulário
          setFormValues({
            numero_precatorio: "",
            tribunal: { id: "", nome: "" },
            status: "Novo",
            valor_total: "",
            prioridade: "normal",
            entidade_devedora: "",
            natureza: "",
            data_previsao_pagamento: "",
            tipo: "PRECATORIO",
            observacoes: "",
            desconto: "",
            prazo_atual: "",
            processo_id: "",
            responsavel_id: "",
            tags: []
          });
          setClienteSelecionado(null);
        } else {
          console.error('Nenhum dado retornado após salvar precatório');
          toast.error('Erro ao salvar precatório. Nenhum dado retornado.');
        }
      } catch (saveError) {
        console.error(`Erro ao ${modoEdicao ? 'atualizar' : 'criar'} precatório:`, saveError);
        toast.error(`Erro ao ${modoEdicao ? 'atualizar' : 'criar'} precatório: ${saveError.message || 'Erro desconhecido'}`);
      }
    } catch (err) {
      console.error(`Erro ao ${modoEdicao ? 'atualizar' : 'criar'} precatório:`, err);
      toast.error(`Erro ao ${modoEdicao ? 'atualizar' : 'criar'} precatório. Tente novamente.`);
    } finally {
      setCriandoPrecatorio(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{modoEdicao ? 'Editar' : 'Novo'} Precatório/RPV</DialogTitle>
          <DialogDescription>
            {modoEdicao ? 'Atualize as' : 'Preencha as'} informações do {modoEdicao ? '' : 'novo'} precatório ou RPV.
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="cliente" className="w-full">
          <TabsList className="grid grid-cols-2 mb-4">
            <TabsTrigger value="cliente">Dados do Cliente</TabsTrigger>
            <TabsTrigger value="precatorio">Dados do Precatório</TabsTrigger>
          </TabsList>

          <TabsContent value="cliente" className="space-y-4">
            {clienteSelecionado ? (
              <Card>
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-center">
                    <CardTitle className="text-lg">Cliente Selecionado</CardTitle>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={removerClienteSelecionado}
                      className="h-8 w-8"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <span className="font-semibold">Nome:</span>
                      <span>{clienteSelecionado.nome}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="font-semibold">CPF/CNPJ:</span>
                      <span>{clienteSelecionado.cpf_cnpj}</span>
                    </div>
                    {clienteSelecionado.email && (
                      <div className="flex items-center gap-2">
                        <span className="font-semibold">Email:</span>
                        <span>{clienteSelecionado.email}</span>
                      </div>
                    )}
                    {clienteSelecionado.telefone && (
                      <div className="flex items-center gap-2">
                        <span className="font-semibold">Telefone:</span>
                        <span>{clienteSelecionado.telefone}</span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-4">
                <div className="space-y-2">
                  <div className="flex gap-2">
                    <div className="flex-1">
                      <Input
                        placeholder="Buscar cliente por nome ou CPF/CNPJ"
                        value={clienteSearch}
                        onChange={(e) => setClienteSearch(e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            buscarClientes();
                          }
                        }}
                      />
                    </div>
                    <Button
                      onClick={buscarClientes}
                      disabled={buscandoClientes || !clienteSearch.trim()}
                    >
                      {buscandoClientes ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <Search className="h-4 w-4" />
                      )}
                    </Button>
                  </div>

                  <div className="flex justify-between">
                    <Button
                      variant="outline"
                      size="sm"
                      className="text-xs"
                      onClick={() => {
                        setClienteSearch("");
                        setClientesEncontrados([]);
                      }}
                    >
                      Limpar
                    </Button>

                    <Button
                      variant="outline"
                      size="sm"
                      className="text-xs flex items-center gap-1"
                      onClick={() => {
                        if (clienteSearch.trim()) {
                          buscarClientes();
                        } else {
                          toast.info('Digite um termo para buscar');
                        }
                      }}
                    >
                      <RefreshCw className="h-3 w-3" />
                      Atualizar
                    </Button>
                  </div>
                </div>

                {clientesEncontrados.length > 0 ? (
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm">Clientes Encontrados</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ScrollArea className="h-[200px]">
                        <div className="space-y-2">
                          {clientesEncontrados.map((cliente) => (
                            <div
                              key={cliente.id}
                              className="p-2 border rounded-md hover:bg-accent cursor-pointer flex justify-between items-center"
                              onClick={() => selecionarCliente(cliente)}
                            >
                              <div>
                                <div className="font-medium">{cliente.nome}</div>
                                <div className="text-sm text-muted-foreground">{cliente.cpf_cnpj}</div>
                              </div>
                              <Button variant="ghost" size="icon" className="h-8 w-8">
                                <Check className="h-4 w-4" />
                              </Button>
                            </div>
                          ))}
                        </div>
                      </ScrollArea>
                    </CardContent>
                  </Card>
                ) : clienteSearch && !buscandoClientes ? (
                  <div className="text-center p-4 border rounded-md bg-muted/50">
                    <UserX className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                    <p className="text-sm text-muted-foreground">Nenhum cliente encontrado com este termo.</p>
                    <Button
                      variant="outline"
                      className="mt-2 w-full"
                      onClick={() => {
                        // Abrir a página de clientes em uma nova aba
                        window.open('/clientes/novo', '_blank');
                        toast.info('Após criar o cliente, volte aqui e faça a busca novamente');
                      }}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Criar novo cliente
                    </Button>
                  </div>
                ) : null}
              </div>
            )}
          </TabsContent>

          <TabsContent value="precatorio" className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Número do Precatório *</label>
                <Input
                  placeholder="Número do precatório"
                  value={formValues.numero_precatorio}
                  onChange={(e) => handleInputChange('numero_precatorio', e.target.value)}
                  className={formErrors.numero_precatorio ? "border-red-500" : ""}
                />
                {formErrors.numero_precatorio && (
                  <p className="text-xs text-red-500">{formErrors.numero_precatorio}</p>
                )}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Tribunal *</label>
                <Select
                  value={formValues.tribunal.id || ''}
                  onValueChange={(value) => {
                    const tribunalSelecionado = tribunais.find(t => t.id === value);
                    if (tribunalSelecionado) {
                      console.log('Tribunal selecionado:', tribunalSelecionado);
                      handleInputChange('tribunal', {
                        id: tribunalSelecionado.id,
                        nome: tribunalSelecionado.nome
                      });
                    }
                  }}
                >
                  <SelectTrigger className={formErrors.tribunal ? "border-red-500" : ""}>
                    <SelectValue placeholder="Selecione um tribunal" />
                  </SelectTrigger>
                  <SelectContent>
                    {tribunais.length === 0 ? (
                      <SelectItem value="carregando" disabled>
                        Carregando tribunais...
                      </SelectItem>
                    ) : (
                      tribunais.map((tribunal) => (
                        <SelectItem key={tribunal.id} value={tribunal.id}>
                          {tribunal.sigla ? `${tribunal.sigla} - ${tribunal.nome}` : tribunal.nome}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
                {formErrors.tribunal && (
                  <p className="text-xs text-red-500">{formErrors.tribunal}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Valor Total *</label>
                <Input
                  type="number"
                  placeholder="Valor total"
                  value={formValues.valor_total}
                  onChange={(e) => handleInputChange('valor_total', e.target.value)}
                  className={formErrors.valor_total ? "border-red-500" : ""}
                />
                {formErrors.valor_total && (
                  <p className="text-xs text-red-500">{formErrors.valor_total}</p>
                )}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Desconto (%)</label>
                <Input
                  type="number"
                  placeholder="Desconto em percentual"
                  value={formValues.desconto}
                  onChange={(e) => handleInputChange('desconto', e.target.value)}
                  className={formErrors.desconto ? "border-red-500" : ""}
                />
                {formErrors.desconto && (
                  <p className="text-xs text-red-500">{formErrors.desconto}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Tipo</label>
                <Select
                  value={formValues.tipo}
                  onValueChange={(value) => handleInputChange('tipo', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione o tipo" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="PRECATORIO">Precatório</SelectItem>
                    <SelectItem value="RPV">RPV</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Status</label>
                <Select
                  value={formValues.status}
                  onValueChange={(value) => {
                    // Encontrar o status selecionado para obter o status_id
                    const statusSelecionado = statusOptions.find(s => s.status_id === value || s.nome === value);
                    if (statusSelecionado) {
                      console.log('Status selecionado:', statusSelecionado);
                      // Usar o status_id em vez do nome
                      handleInputChange('status', statusSelecionado.status_id);
                    } else {
                      handleInputChange('status', value);
                    }
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione o status">
                      {formValues.status && statusOptions.length > 0 && (
                        (() => {
                          const statusSelecionado = statusOptions.find(s => s.status_id === formValues.status);
                          if (statusSelecionado) {
                            return (
                              <div className="flex items-center gap-2">
                                <div
                                  className="w-3 h-3 rounded-full"
                                  style={{ backgroundColor: statusSelecionado.cor }}
                                />
                                {statusSelecionado.nome}
                              </div>
                            );
                          }
                          return formValues.status;
                        })()
                      )}
                    </SelectValue>
                  </SelectTrigger>
                  <SelectContent>
                    {statusOptions.length === 0 ? (
                      <SelectItem value="carregando" disabled>
                        Carregando status...
                      </SelectItem>
                    ) : (
                      statusOptions.map((status) => (
                        <SelectItem key={status.id} value={status.status_id}>
                          <div className="flex items-center gap-2">
                            <div
                              className="w-3 h-3 rounded-full"
                              style={{ backgroundColor: status.cor }}
                            />
                            {status.nome}
                          </div>
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Prioridade</label>
                <Select
                  value={formValues.prioridade}
                  onValueChange={(value) => handleInputChange('prioridade', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione a prioridade" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="baixa">Baixa</SelectItem>
                    <SelectItem value="normal">Normal</SelectItem>
                    <SelectItem value="alta">Alta</SelectItem>
                    <SelectItem value="urgente">Urgente</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Data Previsão Pagamento</label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !formValues.data_previsao_pagamento && "text-muted-foreground",
                        formErrors.data_previsao_pagamento && "border-red-500"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {formValues.data_previsao_pagamento ? (
                        format(new Date(formValues.data_previsao_pagamento), "PPP", { locale: ptBR })
                      ) : (
                        <span>Selecione uma data</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={formValues.data_previsao_pagamento ? new Date(formValues.data_previsao_pagamento) : undefined}
                      onSelect={(date) => handleInputChange('data_previsao_pagamento', date ? date.toISOString() : "")}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                {formErrors.data_previsao_pagamento && (
                  <p className="text-xs text-red-500">{formErrors.data_previsao_pagamento}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Natureza</label>
              <Input
                placeholder="Natureza do precatório"
                value={formValues.natureza}
                onChange={(e) => handleInputChange('natureza', e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Entidade Devedora</label>
              <Input
                placeholder="Entidade devedora"
                value={formValues.entidade_devedora}
                onChange={(e) => handleInputChange('entidade_devedora', e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Observações</label>
              <Textarea
                placeholder="Observações sobre o precatório"
                value={formValues.observacoes}
                onChange={(e) => handleInputChange('observacoes', e.target.value)}
                rows={3}
              />
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter className="mt-4 gap-2">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
          >
            Cancelar
          </Button>
          <Button
            onClick={criarPrecatorio}
            disabled={
              !formValues.numero_precatorio ||
              !formValues.tribunal.id ||
              !clienteSelecionado ||
              !formValues.valor_total ||
              criandoPrecatorio
            }
          >
            {criandoPrecatorio ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                {modoEdicao ? 'Atualizando...' : 'Criando...'}
              </>
            ) : (
              <>
                <Plus className="w-4 h-4 mr-2" />
                {modoEdicao ? 'Atualizar' : 'Criar'} Precatório
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
