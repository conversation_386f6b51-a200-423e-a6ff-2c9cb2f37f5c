import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  FileText,
  CircleDollarSign,
  MoreHorizontal,
  ArrowUpRight,
  Download,
  Calendar,
  Building,
  User,
} from "lucide-react";
import { formatarMoeda } from "@/lib/utils";
import { CHART_COLORS, getStatusColor } from "@/constants/chartColors";
import { format, parseISO } from "date-fns";
import { ptBR } from "date-fns/locale";

interface Precatorio {
  id: string;
  numero: string;
  tribunal: string;
  valor: number;
  status: string;
  tipo: 'PRECATORIO' | 'RPV';
  cliente: {
    id: string;
    nome: string;
  };
  responsavel?: {
    id: string;
    nome: string;
  };
  dataCriacao: string;
  dataAtualizacao: string;
}

interface PrecatorioCardViewProps {
  precatorio: Precatorio;
  onClick: (precatorioId: string) => void;
}

export function PrecatorioCardView({ precatorio, onClick }: PrecatorioCardViewProps) {
  const [isHovered, setIsHovered] = useState(false);

  // Função para obter a cor do status como classe CSS
  const getStatusColorClass = (status: string) => {
    const color = getStatusColor(status);

    // Mapear cores para classes CSS
    const colorClassMap: Record<string, string> = {
      [CHART_COLORS.primary.main]: 'bg-blue-100 text-blue-800 dark:bg-blue-950/70 dark:text-blue-200',
      [CHART_COLORS.primary.light]: 'bg-blue-100 text-blue-800 dark:bg-blue-950/70 dark:text-blue-200',
      [CHART_COLORS.status.warning]: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-950/70 dark:text-yellow-200',
      [CHART_COLORS.status.success]: 'bg-green-100 text-green-800 dark:bg-green-950/70 dark:text-green-200',
      [CHART_COLORS.status.error]: 'bg-red-100 text-red-800 dark:bg-red-950/70 dark:text-red-200',
      [CHART_COLORS.status.pending]: 'bg-purple-100 text-purple-800 dark:bg-purple-950/70 dark:text-purple-200',
      [CHART_COLORS.status.info]: 'bg-indigo-100 text-indigo-800 dark:bg-indigo-950/70 dark:text-indigo-200',
      [CHART_COLORS.secondary.main]: 'bg-gray-100 text-gray-800 dark:bg-gray-950/70 dark:text-gray-200',
    };

    return colorClassMap[color] || 'bg-gray-100 text-gray-800 dark:bg-gray-950/70 dark:text-gray-200';
  };

  // Função para formatar o status para exibição
  const formatarStatus = (status: string) => {
    const statusMap: Record<string, string> = {
      'NOVO': 'Novo',
      'EM_ANALISE': 'Em Análise',
      'APROVADO': 'Aprovado',
      'REJEITADO': 'Rejeitado',
      'EM_PROCESSAMENTO': 'Em Processamento',
      'CONCLUIDO': 'Concluído',
      'CANCELADO': 'Cancelado',
      'analise': 'Análise',
      'proposta_tmj': 'Proposta TMJ',
      'proposta_btg': 'Proposta BTG',
      'negociacao': 'Negociação',
      'documentacao': 'Documentação',
      'pagamento': 'Pagamento',
      'concluido': 'Concluído',
      'cancelado': 'Cancelado',
    };

    return statusMap[status] || status;
  };

  const formatarData = (dataString: string) => {
    try {
      return format(parseISO(dataString), "dd/MM/yyyy", { locale: ptBR });
    } catch (error) {
      return "Data inválida";
    }
  };

  // Calcular o progresso com base no status
  const calcularProgresso = () => {
    const statusProgressMap: Record<string, number> = {
      'NOVO': 10,
      'EM_ANALISE': 30,
      'APROVADO': 50,
      'EM_PROCESSAMENTO': 70,
      'CONCLUIDO': 100,
      'CANCELADO': 0,
      'REJEITADO': 0,
      'analise': 20,
      'proposta_tmj': 40,
      'proposta_btg': 40,
      'negociacao': 60,
      'documentacao': 80,
      'pagamento': 90,
      'concluido': 100,
      'cancelado': 0,
    };

    return statusProgressMap[precatorio.status] || 0;
  };

  // Obter a cor do progresso com base no status
  const getProgressColor = () => {
    const progress = calcularProgresso();

    if (progress === 0) return CHART_COLORS.status.error;
    if (progress < 30) return CHART_COLORS.status.warning;
    if (progress < 70) return CHART_COLORS.primary.main;
    if (progress < 100) return CHART_COLORS.status.info;
    return CHART_COLORS.status.success;
  };

  return (
    <Card
      className={`overflow-hidden transition-all duration-200 ${
        isHovered ? "shadow-md" : ""
      }`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <CardContent className="p-6">
        <div className="flex justify-between items-start mb-4">
          <div className="flex items-center gap-3">
            <Avatar className="h-12 w-12">
              <AvatarImage src={`/avatars/precatorio_${precatorio.id.substring(0, 8)}.jpg`} />
              <AvatarFallback>
                <FileText className="h-6 w-6" />
              </AvatarFallback>
            </Avatar>
            <div>
              <h3 className="font-medium text-lg">{precatorio.numero}</h3>
              <p className="text-sm text-muted-foreground">
                {precatorio.tipo === 'PRECATORIO' ? 'Precatório' : 'RPV'}
              </p>
            </div>
          </div>
          <Badge className={getStatusColorClass(precatorio.status)}>
            {formatarStatus(precatorio.status)}
          </Badge>
        </div>

        <div className="grid grid-cols-2 gap-3 mb-4">
          <div className="flex items-center gap-2">
            <User className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm truncate" title={precatorio.cliente.nome}>
              {precatorio.cliente.nome}
            </span>
          </div>
          <div className="flex items-center gap-2">
            <Building className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm">{precatorio.tribunal}</span>
          </div>
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm">{formatarData(precatorio.dataCriacao)}</span>
          </div>
          <div className="flex items-center gap-2">
            <CircleDollarSign className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium">{formatarMoeda(precatorio.valor)}</span>
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex justify-between items-center text-sm">
            <span>Progresso</span>
            <span>{calcularProgresso()}%</span>
          </div>
          <Progress value={calcularProgresso()} className="h-2" style={{ backgroundColor: `${CHART_COLORS.secondary.ultraLight}` }}>
            <div
              className="h-full transition-all"
              style={{
                width: `${calcularProgresso()}%`,
                backgroundColor: getProgressColor()
              }}
            />
          </Progress>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between p-4 pt-0 border-t mt-4">
        <Button variant="outline" size="sm" onClick={() => onClick(precatorio.id)}>
          <ArrowUpRight className="mr-2 h-4 w-4" />
          Detalhes
        </Button>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <MoreHorizontal className="h-4 w-4" />
              <span className="sr-only">Abrir menu</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={(e) => {
              e.stopPropagation();
              onClick(precatorio.id);
            }}>
              <ArrowUpRight className="mr-2 h-4 w-4" />
              <span>Ver detalhes</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={(e) => e.stopPropagation()}>
              <Download className="mr-2 h-4 w-4" />
              <span>Exportar dados</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </CardFooter>
    </Card>
  );
}
