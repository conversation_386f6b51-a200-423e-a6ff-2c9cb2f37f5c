import { useState, useEffect } from "react";
import { KanbanColuna } from "../types";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { toast } from "sonner";
import { HexColorPicker } from "react-colorful";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";

interface ColunaFormProps {
  coluna?: Omit<KanbanColuna, 'alerts'>;
  onSave: (coluna: Omit<KanbanColuna, 'alerts'>) => void;
  onCancel: () => void;
  statusOptions: { id: string; nome: string }[];
}

export function ColunaForm({ coluna, onSave, onCancel, statusOptions }: ColunaFormProps) {
  const [formData, setFormData] = useState<Omit<KanbanColuna, 'alerts'>>({
    id: coluna?.id || '',
    name: coluna?.name || '',
    color: coluna?.color || '#3b82f6',
    tipo: coluna?.tipo || 'AMBOS',
    ordem: coluna?.ordem || 0,
    status_id: coluna?.status_id || '',
    ativo: coluna?.ativo !== undefined ? coluna.ativo : true,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // Verificar se é modo de edição
  const isEditMode = !!coluna?.id;
  
  useEffect(() => {
    console.log('ColunaForm inicializado com:', { coluna, isEditMode });
    
    // Garantir que os dados do formulário sejam atualizados quando a coluna muda
    if (coluna) {
      setFormData({
        id: coluna.id || '',
        name: coluna.name || '',
        color: coluna.color || '#3b82f6',
        tipo: coluna.tipo || 'AMBOS',
        ordem: coluna.ordem ?? 0,
        status_id: coluna.status_id || '',
        ativo: coluna.ativo !== undefined ? coluna.ativo : true,
      });
    }
  }, [coluna]);

  // Validar o formulário
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'O nome da coluna é obrigatório';
    }

    if (!formData.status_id) {
      newErrors.status_id = 'O status é obrigatório';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Manipular envio do formulário
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error('Por favor, corrija os erros no formulário');
      return;
    }

    onSave(formData);
  };

  // Manipular mudanças nos campos
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Manipular mudança no tipo (RadioGroup)
  const handleTipoChange = (value: string) => {
    setFormData(prev => ({ ...prev, tipo: value as 'PRECATORIO' | 'RPV' | 'AMBOS' }));
  };

  // Manipular mudança no status (Select)
  const handleStatusChange = (value: string) => {
    setFormData(prev => ({ ...prev, status_id: value }));
  };

  // Manipular mudança no switch de ativo
  const handleAtivoChange = (checked: boolean) => {
    setFormData(prev => ({ ...prev, ativo: checked }));
  };

  // Manipular mudança na cor
  const handleColorChange = (color: string) => {
    console.log('Mudando cor para:', color);
    setFormData(prev => ({ ...prev, color }));
  };

  // Selecionar cor predefinida e fechar popover
  const handleSelectColor = (color: string, closePopover: () => void) => {
    handleColorChange(color);
    closePopover();
  };
  
  // Gerar status_id a partir do nome se estiver criando uma nova coluna
  useEffect(() => {
    if (!isEditMode && formData.name && !formData.status_id) {
      // Converter nome para formato de status_id (minúsculo, sem acentos, com underscores)
      const statusId = formData.name
        .toLowerCase()
        .normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '')
        .replace(/\s+/g, '_')
        .replace(/[^a-z0-9_]/g, '');
      
      setFormData(prev => ({ ...prev, status_id: statusId }));
    }
  }, [formData.name, isEditMode]);

  return (
    <form onSubmit={handleSubmit} className="space-y-4" autoComplete="off">
      <div className="space-y-2">
        <Label htmlFor="name">Nome da Coluna</Label>
        <Input
          id="name"
          name="name"
          value={formData.name}
          onChange={handleChange}
          placeholder="Ex: Análise"
          className={errors.name ? "border-red-500" : ""}
        />
        {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
      </div>

      <div className="space-y-2">
        <Label>Cor da Coluna</Label>
        <div className="flex flex-wrap gap-2 mt-1">
          {[
            {cor: '#3b82f6', nome: 'Azul'},
            {cor: '#8b5cf6', nome: 'Roxo'},
            {cor: '#ec4899', nome: 'Rosa'},
            {cor: '#f59e0b', nome: 'Laranja'},
            {cor: '#10b981', nome: 'Verde'},
            {cor: '#6366f1', nome: 'Índigo'},
            {cor: '#22c55e', nome: 'Verde Claro'},
            {cor: '#ef4444', nome: 'Vermelho'}
          ].map((item) => (
            <div 
              key={item.cor}
              className={`flex items-center justify-center w-10 h-10 rounded-full cursor-pointer border-2 ${formData.color === item.cor ? 'border-black' : 'border-transparent'} hover:border-gray-400`}
              style={{ backgroundColor: item.cor }}
              onClick={() => handleColorChange(item.cor)}
              title={item.nome}
            >
              {formData.color === item.cor && (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              )}
            </div>
          ))}
        </div>
        <div className="flex items-center space-x-2 mt-2">
          <div 
            className="w-8 h-8 border border-gray-300"
            style={{ backgroundColor: formData.color || '#3b82f6' }}
          />
          <Input
            name="color"
            value={formData.color || '#3b82f6'}
            onChange={handleChange}
            placeholder="#000000"
            className="w-[120px]"
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label>Tipo de Visualização</Label>
        <RadioGroup
          value={formData.tipo}
          onValueChange={handleTipoChange}
          className="flex flex-col space-y-1"
        >
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="PRECATORIO" id="precatorio" />
            <Label htmlFor="precatorio">Precatórios</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="RPV" id="rpv" />
            <Label htmlFor="rpv">RPVs</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="AMBOS" id="ambos" />
            <Label htmlFor="ambos">Ambos</Label>
          </div>
        </RadioGroup>
      </div>

      <div className="space-y-2">
        <Label htmlFor="status_id">Status</Label>
        {isEditMode ? (
          <Input
            id="status_id"
            value={formData.status_id}
            disabled={true}
            className="bg-muted"
          />
        ) : (
          <div className="space-y-2">
            <Input
              id="status_id"
              name="status_id"
              value={formData.status_id}
              onChange={handleChange}
              placeholder="Ex: analise"
              className={errors.status_id ? "border-red-500" : ""}
            />
            <p className="text-xs text-muted-foreground">O status é gerado automaticamente a partir do nome, mas pode ser editado</p>
            {errors.status_id && (
              <p className="text-sm text-red-500">{errors.status_id}</p>
            )}
          </div>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="ordem">Ordem</Label>
        <Input
          id="ordem"
          name="ordem"
          type="number"
          value={formData.ordem.toString()}
          onChange={e => setFormData(prev => ({ ...prev, ordem: parseInt(e.target.value) || 0 }))}
          min="0"
        />
      </div>

      <div className="flex items-center space-x-2">
        <Switch
          id="ativo"
          checked={formData.ativo}
          onCheckedChange={handleAtivoChange}
        />
        <Label htmlFor="ativo">Coluna Ativa</Label>
      </div>

      <div className="flex justify-end space-x-2 pt-4">
        <Button 
          type="button" 
          variant="outline" 
          onClick={onCancel}
          className="min-w-[120px]"
        >
          Cancelar
        </Button>
        <Button 
          type="submit"
          className="min-w-[150px]"
          disabled={!formData.name || !formData.status_id}
        >
          {isEditMode ? "Atualizar" : "Criar"} Coluna
        </Button>
      </div>
      
      {isEditMode && (
        <div className="mt-2 text-xs text-muted-foreground">
          <p>ID: {formData.id}</p>
          <p>Status ID: {formData.status_id}</p>
        </div>
      )}
    </form>
  );
}
