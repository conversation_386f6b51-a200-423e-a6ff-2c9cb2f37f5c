import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import {
  FileText,
  X,
  User,
  Calendar,
  FileCheck,
  ClipboardList,
  AlertTriangle,
  Info,
  MessageSquare,
  History,
  ArrowDownRight,
} from "lucide-react";
import { Precatorio } from "./types";

interface DetalhesModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  precatorio: Precatorio | null;
  onEdit: (precatorio: Precatorio) => void;
}

export function DetalhesModal({ isOpen, onOpenChange, precatorio, onEdit }: DetalhesModalProps) {
  if (!precatorio) return null;

  // Função para formatar datas
  const formatarData = (data: string | Date | undefined) => {
    if (!data) return "N/A";
    try {
      const dataObj = new Date(data);
      return format(dataObj, "dd 'de' MMMM 'de' yyyy", { locale: ptBR });
    } catch (e) {
      return "Data inválida";
    }
  };

  // Função para formatar valores monetários
  const formatarValor = (valor: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(valor);
  };

  // Calcular dias restantes até o vencimento
  const calcularDiasRestantes = () => {
    if (!precatorio.dataVencimento) return null;

    const hoje = new Date();
    const vencimento = new Date(precatorio.dataVencimento);
    const diffTime = vencimento.getTime() - hoje.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return diffDays;
  };

  const diasRestantes = calcularDiasRestantes();

  // Determinar cor do status
  const getStatusColor = (status: string) => {
    const statusLower = status.toLowerCase();
    if (statusLower.includes('conclu') || statusLower.includes('pago')) return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
    if (statusLower.includes('pend') || statusLower.includes('aguard')) return "bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200";
    if (statusLower.includes('cancel') || statusLower.includes('rejeit')) return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
    if (statusLower.includes('process')) return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
    return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
  };

  const handleClose = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[85vh] overflow-hidden p-0" aria-describedby="precatorio-detail-description">
        {/* Cabeçalho com informações principais */}
        <div className="bg-gradient-to-r from-primary/20 to-primary/10 dark:from-primary/30 dark:to-primary/20 p-6 relative shadow-sm">
          <button onClick={handleClose} className="absolute right-4 top-4 hover:bg-black/10 p-1 rounded-full transition-colors">
            <X className="h-4 w-4" />
          </button>

          <div className="flex items-start justify-between">
            <div>
              <Badge className={getStatusColor(precatorio.status)} variant="outline">
                {precatorio.status}
              </Badge>
              <h2 className="text-2xl font-bold mt-2">{precatorio.numero}</h2>
              <p className="text-muted-foreground mt-1">{precatorio.tribunal || 'Tribunal'} • {precatorio.natureza || 'Natureza'}</p>
            </div>

            <div className="text-right">
              <p className="text-sm text-muted-foreground">Valor Total</p>
              <p className="text-2xl font-bold">{formatarValor(precatorio.valor)}</p>

              {diasRestantes !== null && (
                <Badge variant={diasRestantes < 0 ? "destructive" : diasRestantes < 30 ? "default" : "outline"} className="mt-2">
                  {diasRestantes < 0
                    ? `Vencido há ${Math.abs(diasRestantes)} dias`
                    : `Vence em ${diasRestantes} dias`}
                </Badge>
              )}
            </div>
          </div>
        </div>

        {/* Conteúdo principal com abas */}
        <Tabs defaultValue="detalhes" className="w-full bg-card">
          <div className="px-6 border-b">
            <TabsList className="w-full justify-start h-12">
              <TabsTrigger value="detalhes" className="data-[state=active]:bg-background">
                <Info className="h-4 w-4 mr-2" />
                Detalhes
              </TabsTrigger>
              <TabsTrigger value="documentos" className="data-[state=active]:bg-background">
                <FileText className="h-4 w-4 mr-2" />
                Documentos
              </TabsTrigger>
              <TabsTrigger value="historico" className="data-[state=active]:bg-background">
                <History className="h-4 w-4 mr-2" />
                Histórico
              </TabsTrigger>
              <TabsTrigger value="comentarios" className="data-[state=active]:bg-background">
                <MessageSquare className="h-4 w-4 mr-2" />
                Comentários
              </TabsTrigger>
            </TabsList>
          </div>

          <ScrollArea className="h-[calc(85vh-220px)] px-6 py-6">
            <TabsContent value="detalhes" className="m-0">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {/* Coluna 1 - Informações Gerais */}
                <div className="space-y-6">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base font-medium flex items-center">
                        <User className="h-4 w-4 mr-2 text-primary" />
                        Informações Gerais
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <p className="text-xs text-muted-foreground">Número</p>
                          <p className="font-medium">{precatorio.numero}</p>
                        </div>
                        <div>
                          <p className="text-xs text-muted-foreground">Valor</p>
                          <p className="font-medium">{formatarValor(precatorio.valor)}</p>
                        </div>
                        <div>
                          <p className="text-xs text-muted-foreground">Tribunal</p>
                          <p className="font-medium">{precatorio.tribunal || 'N/A'}</p>
                        </div>
                        <div>
                          <p className="text-xs text-muted-foreground">Natureza</p>
                          <p className="font-medium">{precatorio.natureza || 'N/A'}</p>
                        </div>
                        <div>
                          <p className="text-xs text-muted-foreground">Prioridade</p>
                          <Badge variant={precatorio.prioridade === "alta" ? "destructive" : precatorio.prioridade === "media" ? "secondary" : "outline"}>
                            {precatorio.prioridade}
                          </Badge>
                        </div>
                        <div>
                          <p className="text-xs text-muted-foreground">Responsável</p>
                          <div className="flex items-center gap-2 mt-1">
                            <Avatar className="h-5 w-5">
                              <AvatarFallback className="text-xs">{precatorio.responsavel?.nome ? precatorio.responsavel.nome.substring(0, 2).toUpperCase() : "--"}</AvatarFallback>
                            </Avatar>
                            <span className="text-sm">{precatorio.responsavel?.nome || "Não atribuído"}</span>
                          </div>
                        </div>
                      </div>

                      {precatorio.tags && precatorio.tags.length > 0 && (
                        <div>
                          <p className="text-xs text-muted-foreground mb-2">Tags</p>
                          <div className="flex flex-wrap gap-2">
                            {precatorio.tags.map((tag, index) => (
                              <Badge key={index} variant="outline">{tag}</Badge>
                            ))}
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base font-medium flex items-center">
                        <FileCheck className="h-4 w-4 mr-2 text-primary" />
                        Descrição
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm">
                        {precatorio.observacoes || "Sem descrição detalhada para este precatório."}
                      </p>
                    </CardContent>
                  </Card>
                </div>

                {/* Coluna 2 - Datas e Observações */}
                <div className="space-y-6">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base font-medium flex items-center">
                        <Calendar className="h-4 w-4 mr-2 text-primary" />
                        Datas Importantes
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-1 gap-4">
                        <div>
                          <p className="text-xs text-muted-foreground">Data de Entrada</p>
                          <p className="font-medium">{formatarData(precatorio.dataCriacao)}</p>
                        </div>
                        <div>
                          <p className="text-xs text-muted-foreground">Data de Vencimento</p>
                          <div className="flex items-center gap-2">
                            <p className="font-medium">{formatarData(precatorio.dataVencimento)}</p>
                            {diasRestantes !== null && diasRestantes < 30 && diasRestantes > 0 && (
                              <Badge variant="outline" className="bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200">
                                {diasRestantes} dias restantes
                              </Badge>
                            )}
                            {diasRestantes !== null && diasRestantes < 0 && (
                              <Badge variant="destructive">
                                Vencido
                              </Badge>
                            )}
                          </div>
                        </div>
                        <div>
                          <p className="text-xs text-muted-foreground">Data de Pagamento</p>
                          <p className="font-medium">N/A</p>
                        </div>
                        <div>
                          <p className="text-xs text-muted-foreground">Última Atualização</p>
                          <p className="font-medium">{formatarData(precatorio.dataAtualizacao)}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base font-medium flex items-center">
                        <ClipboardList className="h-4 w-4 mr-2 text-primary" />
                        Observações
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm">
                        {precatorio.observacoes || "Sem observações adicionais."}
                      </p>
                    </CardContent>
                  </Card>

                  {/* Alertas e Avisos */}
                  {(diasRestantes !== null && diasRestantes < 0) && (
                    <Card className="border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-950/30">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-base font-medium flex items-center text-red-700 dark:text-red-400">
                          <AlertTriangle className="h-4 w-4 mr-2" />
                          Alerta de Vencimento
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-red-700 dark:text-red-400">
                          Este precatório está vencido há {Math.abs(diasRestantes)} dias. É recomendado verificar a situação atual e tomar as medidas cabíveis.
                        </p>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="documentos" className="m-0">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Documentos Anexados</CardTitle>
                  <CardDescription>Documentos relacionados a este precatório</CardDescription>
                </CardHeader>
                <CardContent>
                  {precatorio.documentos && precatorio.documentos.length > 0 ? (
                    <div className="space-y-3">
                      {precatorio.documentos.map((doc, index) => (
                        <div key={index} className="flex items-center gap-3 p-3 border rounded-lg hover:bg-muted/50 transition-colors">
                          <div className="p-2 bg-primary/10 rounded-md">
                            <FileText className="h-5 w-5 text-primary" />
                          </div>
                          <div className="flex-1">
                            <p className="font-medium">{doc.nome}</p>
                            <p className="text-xs text-muted-foreground">{doc.tipo} • Adicionado em {formatarData(doc.data)}</p>
                          </div>
                          <Badge variant="outline">{doc.tipo}</Badge>
                          <Button variant="ghost" size="sm">
                            <ArrowDownRight className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center py-8 text-center">
                      <FileText className="h-12 w-12 text-muted-foreground/50 mb-3" />
                      <h3 className="text-lg font-medium">Nenhum documento anexado</h3>
                      <p className="text-sm text-muted-foreground mt-1 max-w-md">
                        Não há documentos anexados a este precatório. Documentos importantes podem ser adicionados ao editar o precatório.
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="historico" className="m-0">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Histórico de Alterações</CardTitle>
                  <CardDescription>Registro de mudanças e atualizações</CardDescription>
                </CardHeader>
                <CardContent>
                  {precatorio.historico && precatorio.historico.length > 0 ? (
                    <div className="relative pl-6 border-l space-y-6">
                      {precatorio.historico.map((evento, index) => (
                        <div key={index} className="relative">
                          <div className="absolute -left-[25px] p-1 bg-background border rounded-full">
                            <div className="w-3 h-3 rounded-full bg-primary" />
                          </div>
                          <div className="space-y-1">
                            <p className="text-sm font-medium">{evento.acao}</p>
                            <div className="flex items-center gap-2">
                              <Avatar className="h-5 w-5">
                                <AvatarFallback className="text-xs">{evento.usuario ? evento.usuario.substring(0, 2).toUpperCase() : "--"}</AvatarFallback>
                              </Avatar>
                              <p className="text-xs text-muted-foreground">{evento.usuario} • {formatarData(evento.data)}</p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center py-8 text-center">
                      <History className="h-12 w-12 text-muted-foreground/50 mb-3" />
                      <h3 className="text-lg font-medium">Sem histórico de alterações</h3>
                      <p className="text-sm text-muted-foreground mt-1 max-w-md">
                        Não há registros de alterações para este precatório. O histórico será atualizado automaticamente quando houver mudanças.
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="comentarios" className="m-0">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Comentários</CardTitle>
                  <CardDescription>Discussões e notas sobre este precatório</CardDescription>
                </CardHeader>
                <CardContent>
                  {false ? (
                    <div className="space-y-4">
                      {/* Comentários serão implementados em uma versão futura */}
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center py-8 text-center">
                      <MessageSquare className="h-12 w-12 text-muted-foreground/50 mb-3" />
                      <h3 className="text-lg font-medium">Sem comentários</h3>
                      <p className="text-sm text-muted-foreground mt-1 max-w-md">
                        Não há comentários para este precatório. Comentários podem ser adicionados para facilitar a comunicação entre a equipe.
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </ScrollArea>
        </Tabs>

        <DialogFooter className="px-6 py-4 border-t">
          <Button variant="outline" onClick={handleClose}>Fechar</Button>
          <Button onClick={() => onEdit(precatorio)} className="gap-2">
            <FileText className="h-4 w-4" />
            Editar Precatório
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}