import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { CalendarIcon, X, Search, Check, UserX, Loader2, Plus } from "lucide-react";
import { cn } from "@/lib/utils";
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { supabase } from "@/lib/supabase";
import { toast } from "sonner";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";

interface NovoPrecatorioFormProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (precatorio: any) => void;
}

interface Cliente {
  id: string;
  nome: string;
  cpf_cnpj: string;
  email?: string;
  telefone?: string;
  endereco?: string;
}

export function NovoPrecatorioForm(props: NovoPrecatorioFormProps) {
  const { isOpen, onOpenChange, onSave } = props;
  // Estado para o formulário de precatório
  const [formValues, setFormValues] = useState({
    numero_precatorio: "",
    tribunal: { nome: "" },
    status: "Novo",
    valor_total: "",
    prioridade: "normal",
    entidade_devedora: "",
    natureza: "",
    data_previsao_pagamento: "",
    tipo: "PRECATORIO", // Padrão para precatório
    observacoes: "",
    desconto: "",
    prazo_atual: "",
    processo_id: "",
    responsavel_id: "",
    tags: []
  });

  // Estado para erros de validação
  const [formErrors, setFormErrors] = useState<{
    numero_precatorio?: string;
    tribunal?: string;
    valor_total?: string;
    desconto?: string;
    data_previsao_pagamento?: string;
  }>({});

  // Estados para busca e seleção de cliente
  const [clienteSearch, setClienteSearch] = useState("");
  const [clientesEncontrados, setClientesEncontrados] = useState<Cliente[]>([]);
  const [clienteSelecionado, setClienteSelecionado] = useState<Cliente | null>(null);
  const [loadingClientes, setLoadingClientes] = useState(false);
  const [criandoPrecatorio, setCriandoPrecatorio] = useState(false);

  // Estado para novo cliente
  const [novoCliente, setNovoCliente] = useState({
    nome: "",
    cpf_cnpj: "",
    email: "",
    telefone: "",
    endereco: ""
  });

  // Estado para nova tag
  const [novaTag, setNovaTag] = useState("");

  // Função para buscar clientes no Supabase
  const buscarClientes = async () => {
    if (!clienteSearch.trim()) return;

    try {
      setLoadingClientes(true);

      const { data, error } = await supabase
        .from('clientes')
        .select('*')
        .or(`nome.ilike.%${clienteSearch}%,cpf_cnpj.ilike.%${clienteSearch}%,email.ilike.%${clienteSearch}%`)
        .limit(10);

      if (error) {
        console.error('Erro ao buscar clientes:', error);
        toast.error('Erro ao buscar clientes. Tente novamente.');
        return;
      }

      setClientesEncontrados(Array.isArray(data) ? data : []);
    } catch (err) {
      console.error('Erro ao buscar clientes:', err);
      toast.error('Erro ao buscar clientes. Tente novamente.');
    } finally {
      setLoadingClientes(false);
    }
  };

  // Função para selecionar um cliente
  const selecionarCliente = (cliente: Cliente) => {
    setClienteSelecionado(cliente);
  };

  // Função para criar um novo cliente
  const criarNovoCliente = async () => {
    if (!novoCliente.nome || !novoCliente.cpf_cnpj) {
      toast.error('Nome e CPF/CNPJ são obrigatórios.');
      return;
    }

    try {
      setLoadingClientes(true);

      // Verificar se já existe um cliente com o mesmo CPF/CNPJ
      const { data: clienteExistente } = await supabase
        .from('clientes')
        .select('*')
        .eq('cpf_cnpj', novoCliente.cpf_cnpj)
        .maybeSingle();

      if (clienteExistente) {
        toast.error('Já existe um cliente com este CPF/CNPJ.');
        setLoadingClientes(false);
        return;
      }

      // Criar novo cliente
      const { data, error } = await supabase
        .from('clientes')
        .insert([novoCliente])
        .select();

      if (error) {
        console.error('Erro ao criar cliente:', error);
        toast.error('Erro ao criar cliente. Tente novamente.');
        return;
      }

      if (data && data.length > 0) {
        toast.success('Cliente criado com sucesso!');
        setClienteSelecionado(data[0]);

        // Limpar formulário de novo cliente
        setNovoCliente({
          nome: "",
          cpf_cnpj: "",
          email: "",
          telefone: "",
          endereco: ""
        });

        // Mudar para a aba de busca
        document.querySelector('[data-value="buscar"]')?.click();
      }
    } catch (err) {
      console.error('Erro ao criar cliente:', err);
      toast.error('Erro ao criar cliente. Tente novamente.');
    } finally {
      setLoadingClientes(false);
    }
  };

  // Função para lidar com mudanças no formulário de precatório
  const handleFormChange = (field: string, value: any) => {
    // Limpar erro do campo quando o usuário o modifica
    if (formErrors[field as keyof typeof formErrors]) {
      setFormErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field as keyof typeof formErrors];
        return newErrors;
      });
    }

    setFormValues(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Função para lidar com mudanças no formulário de novo cliente
  const handleNovoClienteChange = (field: string, value: any) => {
    setNovoCliente(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Função para adicionar uma nova tag
  const adicionarTag = () => {
    if (!novaTag.trim() || formValues.tags.includes(novaTag.trim())) {
      return;
    }

    handleFormChange('tags', [...formValues.tags, novaTag.trim()]);
    setNovaTag("");
  };

  // Função para remover uma tag
  const removerTag = (tag: string) => {
    handleFormChange('tags', formValues.tags.filter(t => t !== tag));
  };

  // Função para criar um novo precatório
  const criarPrecatorio = async () => {
    // Resetar erros anteriores
    setFormErrors({});

    // Objeto para armazenar erros
    const errors: {[key: string]: string} = {};
    let hasErrors = false;

    // Validar campos obrigatórios
    if (!formValues.numero_precatorio) {
      errors.numero_precatorio = "Número do precatório é obrigatório";
      hasErrors = true;
    }

    if (!formValues.tribunal.nome) {
      errors.tribunal = "Tribunal é obrigatório";
      hasErrors = true;
    }

    if (!clienteSelecionado) {
      toast.error('Selecione um cliente para continuar.');
      // Mudar para a aba de cliente
      document.querySelector('[value="cliente"]')?.click();
      hasErrors = true;
    }

    if (!formValues.valor_total) {
      errors.valor_total = "Valor total é obrigatório";
      hasErrors = true;
    } else if (isNaN(parseFloat(formValues.valor_total)) || parseFloat(formValues.valor_total) <= 0) {
      errors.valor_total = "Valor total deve ser um número positivo";
      hasErrors = true;
    }

    // Validar campos opcionais
    if (formValues.desconto && (isNaN(parseFloat(formValues.desconto)) || parseFloat(formValues.desconto) < 0 || parseFloat(formValues.desconto) > 100)) {
      errors.desconto = "Desconto deve ser um percentual entre 0 e 100";
      hasErrors = true;
    }

    if (formValues.data_previsao_pagamento) {
      const dataPrevisao = new Date(formValues.data_previsao_pagamento);
      if (isNaN(dataPrevisao.getTime())) {
        errors.data_previsao_pagamento = "Data inválida";
        hasErrors = true;
      }
    }

    // Se houver erros, atualizar o estado e retornar
    if (hasErrors) {
      setFormErrors(errors);
      if (Object.keys(errors).length > 0) {
        toast.error('Verifique os campos destacados em vermelho.');
      }
      return;
    }

    try {
      setCriandoPrecatorio(true);

      // Criar novo precatório
      const { data, error } = await supabase
        .from('precatorios')
        .insert([
          {
            numero_precatorio: formValues.numero_precatorio,
            beneficiario_id: clienteSelecionado.id,
            tribunal_id: formValues.tribunal.nome, // Idealmente, isso seria um ID, não o nome
            status: formValues.status,
            valor_total: parseFloat(formValues.valor_total),
            prioridade: formValues.prioridade,
            entidade_devedora: formValues.entidade_devedora,
            natureza: formValues.natureza,
            data_previsao_pagamento: formValues.data_previsao_pagamento || null,
            tipo: formValues.tipo,
            categoria: formValues.tipo,
            observacoes: formValues.observacoes,
            desconto: formValues.desconto ? parseFloat(formValues.desconto) : null,
            prazo_atual: formValues.prazo_atual || null,
            processo_id: formValues.processo_id || null,
            responsavel_id: formValues.responsavel_id || null,
            tags: formValues.tags.length > 0 ? formValues.tags : null,
            data_entrada: new Date().toISOString()
          }
        ])
        .select();

      if (error) {
        console.error('Erro ao criar precatório:', error);
        toast.error('Erro ao criar precatório. Tente novamente.');
        return;
      }

      if (data && data.length > 0) {
        toast.success('Precatório criado com sucesso!');
        onSave(data[0]);
        onOpenChange(false);

        // Limpar formulário
        setFormValues({
          numero_precatorio: "",
          tribunal: { nome: "" },
          status: "Novo",
          valor_total: "",
          prioridade: "normal",
          entidade_devedora: "",
          natureza: "",
          data_previsao_pagamento: "",
          tipo: "PRECATORIO",
          observacoes: "",
          desconto: "",
          prazo_atual: "",
          processo_id: "",
          responsavel_id: "",
          tags: []
        });
        setClienteSelecionado(null);
      }
    } catch (err) {
      console.error('Erro ao criar precatório:', err);
      toast.error('Erro ao criar precatório. Tente novamente.');
    } finally {
      setCriandoPrecatorio(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Novo Precatório/RPV</DialogTitle>
          <DialogDescription>
            Preencha as informações do novo precatório ou RPV.
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="cliente" className="w-full">
          <TabsList className="grid grid-cols-2 mb-4">
            <TabsTrigger value="cliente">Dados do Cliente</TabsTrigger>
            <TabsTrigger value="precatorio">Dados do Precatório</TabsTrigger>
          </TabsList>

          {/* Aba de Cliente */}
          <TabsContent value="cliente" className="space-y-4">
            <Tabs defaultValue="buscar">
              <TabsList className="w-full">
                <TabsTrigger value="buscar" className="flex-1" data-value="buscar">Buscar Cliente</TabsTrigger>
                <TabsTrigger value="criar" className="flex-1" data-value="criar">Criar Novo Cliente</TabsTrigger>
              </TabsList>

              <TabsContent value="buscar" className="mt-4 space-y-4">
                <div className="flex items-center gap-2">
                  <div className="flex-1">
                    <Input
                      placeholder="Buscar por nome, CPF/CNPJ ou email..."
                      value={clienteSearch}
                      onChange={e => setClienteSearch(e.target.value)}
                      className="w-full"
                      onKeyDown={e => {
                        if (e.key === 'Enter') {
                          buscarClientes();
                        }
                      }}
                    />
                  </div>
                  <Button
                    variant="secondary"
                    size="icon"
                    onClick={buscarClientes}
                  >
                    <Search className="w-4 h-4" />
                  </Button>
                </div>

                <div className="border rounded-md overflow-hidden">
                  {loadingClientes ? (
                    <div className="flex justify-center p-4">
                      <Loader2 className="w-6 h-6 text-primary animate-spin" />
                    </div>
                  ) : clientesEncontrados.length > 0 ? (
                    <ScrollArea className="h-[300px]">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Nome</TableHead>
                            <TableHead>CPF/CNPJ</TableHead>
                            <TableHead>Contato</TableHead>
                            <TableHead className="text-right">Ações</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {clientesEncontrados.map((cliente: Cliente) => (
                            <TableRow key={cliente.id} className={cliente.id === clienteSelecionado?.id ? "bg-muted" : ""}>
                              <TableCell className="font-medium">{cliente.nome}</TableCell>
                              <TableCell>{cliente.cpf_cnpj}</TableCell>
                              <TableCell>
                                <div className="flex flex-col text-xs">
                                  <span>{cliente.email}</span>
                                  <span>{cliente.telefone}</span>
                                </div>
                              </TableCell>
                              <TableCell className="text-right">
                                <Button
                                  variant={cliente.id === clienteSelecionado?.id ? "default" : "ghost"}
                                  size="sm"
                                  onClick={() => selecionarCliente(cliente)}
                                >
                                  {cliente.id === clienteSelecionado?.id ? (
                                    <>
                                      <Check className="w-4 h-4 mr-1" />
                                      Selecionado
                                    </>
                                  ) : (
                                    "Selecionar"
                                  )}
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </ScrollArea>
                  ) : clienteSearch && !loadingClientes ? (
                    <div className="text-center p-6 space-y-2">
                      <UserX className="w-8 h-8 text-muted-foreground mx-auto" />
                      <p className="text-lg font-medium">Nenhum cliente encontrado</p>
                      <p className="text-sm text-muted-foreground mb-2">Tente com outro termo ou crie um novo cliente.</p>
                      <Button variant="outline" onClick={() => document.querySelector('[data-value="criar"]')?.click()}>
                        Criar Novo Cliente
                      </Button>
                    </div>
                  ) : (
                    <div className="text-center p-6 space-y-2">
                      <Search className="w-8 h-8 text-muted-foreground mx-auto" />
                      <p className="text-lg font-medium">Busque por um cliente</p>
                      <p className="text-sm text-muted-foreground">Digite um nome, CPF/CNPJ ou email para buscar clientes existentes.</p>
                    </div>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="criar" className="mt-4 space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Nome *</label>
                    <Input
                      placeholder="Nome completo"
                      value={novoCliente.nome}
                      onChange={(e) => handleNovoClienteChange('nome', e.target.value)}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">CPF/CNPJ *</label>
                    <Input
                      placeholder="CPF ou CNPJ"
                      value={novoCliente.cpf_cnpj}
                      onChange={(e) => handleNovoClienteChange('cpf_cnpj', e.target.value)}
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Email</label>
                    <Input
                      type="email"
                      placeholder="Email"
                      value={novoCliente.email}
                      onChange={(e) => handleNovoClienteChange('email', e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Telefone</label>
                    <Input
                      placeholder="Telefone"
                      value={novoCliente.telefone}
                      onChange={(e) => handleNovoClienteChange('telefone', e.target.value)}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Endereço</label>
                  <Input
                    placeholder="Endereço completo"
                    value={novoCliente.endereco}
                    onChange={(e) => handleNovoClienteChange('endereco', e.target.value)}
                  />
                </div>

                <Button
                  className="w-full"
                  onClick={criarNovoCliente}
                  disabled={!novoCliente.nome || !novoCliente.cpf_cnpj || loadingClientes}
                >
                  {loadingClientes ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Criando...
                    </>
                  ) : (
                    <>
                      <Plus className="w-4 h-4 mr-2" />
                      Criar Cliente
                    </>
                  )}
                </Button>
              </TabsContent>
            </Tabs>

            {clienteSelecionado && (
              <Card className="mt-2">
                <CardHeader className="py-3">
                  <CardTitle className="text-md flex items-center gap-2">
                    Cliente Selecionado
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0 pb-3">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground mb-1">Nome do Cliente</p>
                      <p className="text-md font-medium">{clienteSelecionado.nome}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground mb-1">CPF/CNPJ</p>
                      <p className="text-md font-medium">{clienteSelecionado.cpf_cnpj}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Aba de Precatório */}
          <TabsContent value="precatorio" className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex items-center gap-1">
                  <label className="text-sm font-medium">Número do Precatório *</label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger className="cursor-help">
                        <span className="text-xs text-muted-foreground hover:text-primary">(?)</span>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="max-w-xs">Insira o número completo do precatório conforme consta no documento oficial.</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <div className="space-y-1">
                  <Input
                    placeholder="Número do precatório"
                    value={formValues.numero_precatorio}
                    onChange={(e) => handleFormChange('numero_precatorio', e.target.value)}
                    required
                    className={`${formErrors.numero_precatorio ? 'border-red-500 focus-visible:ring-red-500' : ''}`}
                    aria-invalid={!!formErrors.numero_precatorio}
                    aria-describedby={formErrors.numero_precatorio ? "numero_precatorio-error" : undefined}
                  />
                  {formErrors.numero_precatorio && (
                    <p id="numero_precatorio-error" className="text-sm font-medium text-red-500">{formErrors.numero_precatorio}</p>
                  )}
                </div>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Tribunal *</label>
                <div className="space-y-1">
                  <Input
                    placeholder="Nome do tribunal"
                    value={formValues.tribunal.nome}
                    onChange={(e) => handleFormChange('tribunal', { nome: e.target.value })}
                    required
                    className={`${formErrors.tribunal ? 'border-red-500 focus-visible:ring-red-500' : ''}`}
                    aria-invalid={!!formErrors.tribunal}
                    aria-describedby={formErrors.tribunal ? "tribunal-error" : undefined}
                  />
                  {formErrors.tribunal && (
                    <p id="tribunal-error" className="text-sm font-medium text-red-500">{formErrors.tribunal}</p>
                  )}
                </div>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex items-center gap-1">
                  <label className="text-sm font-medium">Valor Total *</label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger className="cursor-help">
                        <span className="text-xs text-muted-foreground hover:text-primary">(?)</span>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="max-w-xs">Valor total do precatório em reais, sem considerar descontos.</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <div className="space-y-1">
                  <Input
                    type="number"
                    placeholder="Valor total"
                    value={formValues.valor_total}
                    onChange={(e) => handleFormChange('valor_total', e.target.value)}
                    required
                    className={`${formErrors.valor_total ? 'border-red-500 focus-visible:ring-red-500' : ''}`}
                    aria-invalid={!!formErrors.valor_total}
                    aria-describedby={formErrors.valor_total ? "valor_total-error" : undefined}
                  />
                  {formErrors.valor_total && (
                    <p id="valor_total-error" className="text-sm font-medium text-red-500">{formErrors.valor_total}</p>
                  )}
                </div>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Tipo</label>
                <Select
                  value={formValues.tipo}
                  onValueChange={(value) => handleFormChange('tipo', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione o tipo" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="PRECATORIO">Precatório</SelectItem>
                    <SelectItem value="RPV">RPV</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Status</label>
                <Select
                  value={formValues.status}
                  onValueChange={(value) => handleFormChange('status', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione o status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Novo">Novo</SelectItem>
                    <SelectItem value="Em Análise">Em Análise</SelectItem>
                    <SelectItem value="Em Andamento">Em Andamento</SelectItem>
                    <SelectItem value="Aguardando">Aguardando</SelectItem>
                    <SelectItem value="Concluído">Concluído</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Prioridade</label>
                <Select
                  value={formValues.prioridade}
                  onValueChange={(value) => handleFormChange('prioridade', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione a prioridade" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="baixa">Baixa</SelectItem>
                    <SelectItem value="normal">Normal</SelectItem>
                    <SelectItem value="alta">Alta</SelectItem>
                    <SelectItem value="urgente">Urgente</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Entidade Devedora</label>
                <Input
                  placeholder="Entidade devedora"
                  value={formValues.entidade_devedora}
                  onChange={(e) => handleFormChange('entidade_devedora', e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Natureza</label>
                <Input
                  placeholder="Natureza do precatório"
                  value={formValues.natureza}
                  onChange={(e) => handleFormChange('natureza', e.target.value)}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex items-center gap-1">
                  <label className="text-sm font-medium">Desconto (%)</label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger className="cursor-help">
                        <span className="text-xs text-muted-foreground hover:text-primary">(?)</span>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="max-w-xs">Percentual de desconto aplicado ao valor total. Deve ser um valor entre 0 e 100.</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <div className="space-y-1">
                  <Input
                    type="number"
                    placeholder="Percentual de desconto"
                    value={formValues.desconto}
                    onChange={(e) => handleFormChange('desconto', e.target.value)}
                    className={`${formErrors.desconto ? 'border-red-500 focus-visible:ring-red-500' : ''}`}
                    aria-invalid={!!formErrors.desconto}
                    aria-describedby={formErrors.desconto ? "desconto-error" : undefined}
                  />
                  {formErrors.desconto && (
                    <p id="desconto-error" className="text-sm font-medium text-red-500">{formErrors.desconto}</p>
                  )}
                </div>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Prazo Atual</label>
                <Input
                  placeholder="Prazo atual"
                  value={formValues.prazo_atual}
                  onChange={(e) => handleFormChange('prazo_atual', e.target.value)}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">ID do Processo</label>
                <Input
                  placeholder="ID do processo relacionado"
                  value={formValues.processo_id}
                  onChange={(e) => handleFormChange('processo_id', e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">ID do Responsável</label>
                <Input
                  placeholder="ID do responsável"
                  value={formValues.responsavel_id}
                  onChange={(e) => handleFormChange('responsavel_id', e.target.value)}
                />
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-1">
                <label className="text-sm font-medium">Data de Previsão de Pagamento</label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger className="cursor-help">
                      <span className="text-xs text-muted-foreground hover:text-primary">(?)</span>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs">Data prevista para o pagamento do precatório. Utilizada para cálculos de prazos e notificações.</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <div className="space-y-1">
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !formValues.data_previsao_pagamento && "text-muted-foreground",
                        formErrors.data_previsao_pagamento && "border-red-500 text-red-500"
                      )}
                      aria-invalid={!!formErrors.data_previsao_pagamento}
                      aria-describedby={formErrors.data_previsao_pagamento ? "data_previsao_pagamento-error" : undefined}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {formValues.data_previsao_pagamento ? (
                        format(new Date(formValues.data_previsao_pagamento), "dd/MM/yyyy", { locale: ptBR })
                      ) : (
                        <span>Selecione uma data</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={formValues.data_previsao_pagamento ? new Date(formValues.data_previsao_pagamento) : undefined}
                      onSelect={(date) => handleFormChange('data_previsao_pagamento', date ? date.toISOString() : null)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                {formErrors.data_previsao_pagamento && (
                  <p id="data_previsao_pagamento-error" className="text-sm font-medium text-red-500">{formErrors.data_previsao_pagamento}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-1">
                <label className="text-sm font-medium">Tags</label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger className="cursor-help">
                      <span className="text-xs text-muted-foreground hover:text-primary">(?)</span>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs">Adicione palavras-chave para facilitar a busca e organização dos precatórios. Pressione Enter após digitar cada tag.</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <div className="flex flex-wrap gap-2 mb-2">
                {formValues.tags.map((tag, index) => (
                  <div
                    key={index}
                    className="bg-primary/10 text-primary px-3 py-1 rounded-full flex items-center gap-1"
                  >
                    <span>{tag}</span>
                    <button
                      type="button"
                      onClick={() => removerTag(tag)}
                      className="text-primary hover:text-primary/80 focus:outline-none"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </div>
                ))}
              </div>
              <div className="flex gap-2">
                <Input
                  placeholder="Nova tag"
                  value={novaTag}
                  onChange={(e) => setNovaTag(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      adicionarTag();
                    }
                  }}
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={adicionarTag}
                  disabled={!novaTag.trim()}
                >
                  Adicionar
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Observações</label>
              <Textarea
                placeholder="Observações adicionais"
                value={formValues.observacoes}
                onChange={(e) => handleFormChange('observacoes', e.target.value)}
                rows={4}
              />
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter className="mt-4 gap-2">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
          >
            Cancelar
          </Button>
          <Button
            onClick={criarPrecatorio}
            disabled={
              !formValues.numero_precatorio ||
              !formValues.tribunal.nome ||
              !clienteSelecionado ||
              !formValues.valor_total ||
              criandoPrecatorio
            }
          >
            {criandoPrecatorio ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Criando...
              </>
            ) : (
              <>
                <Plus className="w-4 h-4 mr-2" />
                Criar Precatório
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
