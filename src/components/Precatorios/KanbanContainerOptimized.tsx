import { useState, useCallback, useRef, useEffect } from "react";
import {
  DndContext,
  DragEndEvent,
  DragOverEvent,
  DragStartEvent,
  DragOverlay,
  useSensor,
  useSensors,
  PointerSensor,
  KeyboardSensor,
  closestCorners,
  MeasuringStrategy,
} from "@dnd-kit/core";
import { sortableKeyboardCoordinates } from "@dnd-kit/sortable";
import { Precatorio, KanbanColuna } from "./types";
import { KanbanColunaOptimized } from "./KanbanColunaOptimized";
import { PrecatorioCardOptimized } from "./PrecatorioCardOptimized";
import { SharedPrecatorioForm } from "./SharedPrecatorioForm";
import { DetalhesModal } from "./DetalhesModal";
import { useKanbanPermissionsSimple } from "@/hooks/useKanbanPermissionsSimple";
import { toast } from "sonner";

interface KanbanContainerOptimizedProps {
  precatorios: Precatorio[];
  colunas: KanbanC<PERSON>una[];
  onSavePrecatorio: (precatorio: Precatorio) => void;
  onDeletePrecatorio: (id: string) => void;
  onMovePrecatorio: (precatorioId: string, novoStatusId: string) => void;
}

export function KanbanContainerOptimized({
  precatorios,
  colunas,
  onSavePrecatorio,
  onDeletePrecatorio,
  onMovePrecatorio,
}: KanbanContainerOptimizedProps) {
  // Estados para modais
  const [precatorioSelecionado, setPrecatorioSelecionado] = useState<Precatorio | null>(null);
  const [isDetalhesModalOpen, setIsDetalhesModalOpen] = useState(false);
  const [isFormModalOpen, setIsFormModalOpen] = useState(false);
  const [modoEdicao, setModoEdicao] = useState(false);
  const [activePrecatorio, setActivePrecatorio] = useState<Precatorio | null>(null);

  // Estados para scroll horizontal
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [isDraggingView, setIsDraggingView] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, scrollLeft: 0 });

  // Hooks de permissões
  const {
    canCreatePrecatorio,
    canEditPrecatorio,
    canDeletePrecatorio,
    canMovePrecatorioToColumn,
    filterVisibleColumns,
  } = useKanbanPermissionsSimple();

  // Sensores para drag and drop
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Handlers para ações
  const handleVerDetalhes = useCallback((precatorio: Precatorio) => {
    setPrecatorioSelecionado(precatorio);
    setIsDetalhesModalOpen(true);
  }, []);

  const handleEditarPrecatorio = useCallback((precatorio: Precatorio) => {
    if (!canEditPrecatorio(precatorio)) {
      toast.error("Você não tem permissão para editar este precatório");
      return;
    }
    setPrecatorioSelecionado(precatorio);
    setModoEdicao(true);
    setIsFormModalOpen(true);
  }, [canEditPrecatorio]);

  const handleNovoPrecatorio = useCallback(() => {
    if (!canCreatePrecatorio()) {
      toast.error("Você não tem permissão para criar precatórios");
      return;
    }
    setPrecatorioSelecionado(null);
    setModoEdicao(false);
    setIsFormModalOpen(true);
  }, [canCreatePrecatorio]);

  const handleDeletePrecatorio = useCallback((id: string) => {
    const precatorio = precatorios.find(p => p.id === id);
    if (!precatorio || !canDeletePrecatorio(precatorio)) {
      toast.error("Você não tem permissão para excluir este precatório");
      return;
    }
    onDeletePrecatorio(id);
  }, [precatorios, canDeletePrecatorio, onDeletePrecatorio]);

  // Handlers para scroll horizontal
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    // Verificar se clicou em um card ou elemento interativo
    const target = e.target as HTMLElement;
    if (target.closest('[data-draggable="card"]') || target.closest('button') || target.closest('[role="menuitem"]')) {
      return; // Não iniciar scroll se clicou em card ou botão
    }

    if (scrollContainerRef.current) {
      setIsDraggingView(true);
      setDragStart({
        x: e.pageX,
        scrollLeft: scrollContainerRef.current.scrollLeft
      });
    }
  }, []);

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (!isDraggingView || !scrollContainerRef.current) return;

    e.preventDefault();
    const x = e.pageX;
    const walk = (x - dragStart.x) * 2; // Multiplicar por 2 para scroll mais rápido
    scrollContainerRef.current.scrollLeft = dragStart.scrollLeft - walk;
  }, [isDraggingView, dragStart]);

  const handleMouseUp = useCallback(() => {
    setIsDraggingView(false);
  }, []);

  const handleMouseLeave = useCallback(() => {
    setIsDraggingView(false);
  }, []);

  // Adicionar listeners globais para mouse up
  useEffect(() => {
    if (isDraggingView) {
      const handleGlobalMouseUp = () => setIsDraggingView(false);
      const handleGlobalMouseMove = (e: MouseEvent) => {
        if (!scrollContainerRef.current) return;
        e.preventDefault();
        const walk = (e.pageX - dragStart.x) * 2;
        scrollContainerRef.current.scrollLeft = dragStart.scrollLeft - walk;
      };

      document.addEventListener('mouseup', handleGlobalMouseUp);
      document.addEventListener('mousemove', handleGlobalMouseMove);

      return () => {
        document.removeEventListener('mouseup', handleGlobalMouseUp);
        document.removeEventListener('mousemove', handleGlobalMouseMove);
      };
    }
  }, [isDraggingView, dragStart]);

  // Drag and drop handlers
  const handleDragStart = useCallback((event: DragStartEvent) => {
    const { active } = event;
    const precatorio = active.data.current?.precatorio;

    if (precatorio) {
      setActivePrecatorio(precatorio);
    }
  }, []);

  const handleDragOver = useCallback((event: DragOverEvent) => {
    // Feedback visual durante o drag
  }, []);

  const handleDragEnd = useCallback((event: DragEndEvent) => {
    const { active, over } = event;

    setActivePrecatorio(null);

    if (!over) return;

    const precatorio = active.data.current?.precatorio;
    const targetColumnId = over.id as string;

    if (!precatorio || !targetColumnId) return;

    // Verificar permissões
    if (!canMovePrecatorioToColumn(targetColumnId, precatorio)) {
      toast.error("Você não tem permissão para mover este precatório");
      return;
    }

    // Se já está na coluna correta, não fazer nada
    if (precatorio.status_id === targetColumnId) return;

    // Mover precatório
    onMovePrecatorio(precatorio.id, targetColumnId);
  }, [canMovePrecatorioToColumn, onMovePrecatorio]);

  // Filtrar colunas visíveis
  const colunasVisiveis = filterVisibleColumns(colunas);

  if (colunasVisiveis.length === 0) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center p-8">
          <h3 className="text-lg font-semibold mb-2">Nenhuma coluna disponível</h3>
          <p className="text-muted-foreground">
            Configure as colunas do Kanban para começar a usar.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full w-full overflow-hidden">
      {/* Estilos customizados para scrollbar e texto vertical */}
      <style jsx>{`
        .kanban-scroll::-webkit-scrollbar {
          height: 8px;
        }
        .kanban-scroll::-webkit-scrollbar-track {
          background: transparent;
        }
        .kanban-scroll::-webkit-scrollbar-thumb {
          background: rgba(0, 0, 0, 0.2);
          border-radius: 4px;
        }
        .kanban-scroll::-webkit-scrollbar-thumb:hover {
          background: rgba(0, 0, 0, 0.3);
        }
        .writing-mode-vertical {
          writing-mode: vertical-rl;
          text-orientation: mixed;
        }
      `}</style>

      <DndContext
        sensors={sensors}
        collisionDetection={closestCorners}
        onDragStart={handleDragStart}
        onDragOver={handleDragOver}
        onDragEnd={handleDragEnd}
        measuring={{
          droppable: {
            strategy: MeasuringStrategy.Always,
          },
        }}
      >
        {/* Container das colunas com scroll horizontal e drag */}
        <div
          ref={scrollContainerRef}
          className={`flex gap-3 overflow-x-auto overflow-y-hidden px-4 pt-16 pb-6 kanban-scroll ${
            isDraggingView ? 'cursor-grabbing' : 'cursor-grab'
          } select-none`}
          style={{
            height: '100%', // Usar toda a altura disponível
            scrollbarWidth: 'thin' // Firefox
          }}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseLeave}
        >
          {colunasVisiveis.map((coluna) => (
            <div key={coluna.id} className="flex-shrink-0">
              <KanbanColunaOptimized
                coluna={coluna}
                precatorios={precatorios}
                onVerDetalhes={handleVerDetalhes}
                onEdit={handleEditarPrecatorio}
                onDelete={handleDeletePrecatorio}
                onNovoPrecatorio={handleNovoPrecatorio}
                canCreate={canCreatePrecatorio()}
                canEdit={true} // Verificação feita dentro do handler
                canDelete={true} // Verificação feita dentro do handler
              />
            </div>
          ))}
        </div>

        {/* Overlay para drag and drop */}
        <DragOverlay>
          {activePrecatorio ? (
            <PrecatorioCardOptimized
              precatorio={activePrecatorio}
              onVerDetalhes={() => {}}
              onEdit={() => {}}
              onDelete={() => {}}
              isOverlay={true}
            />
          ) : null}
        </DragOverlay>
      </DndContext>

      {/* Modais */}
      {isDetalhesModalOpen && precatorioSelecionado && (
        <DetalhesModal
          precatorio={precatorioSelecionado}
          isOpen={isDetalhesModalOpen}
          onOpenChange={setIsDetalhesModalOpen}
          onEdit={() => {
            setIsDetalhesModalOpen(false);
            handleEditarPrecatorio(precatorioSelecionado);
          }}
        />
      )}

      {isFormModalOpen && (
        <SharedPrecatorioForm
          isOpen={isFormModalOpen}
          onOpenChange={setIsFormModalOpen}
          initialData={precatorioSelecionado}
          modoEdicao={modoEdicao}
          onSave={(precatorio) => {
            onSavePrecatorio(precatorio);
            setIsFormModalOpen(false);
          }}
        />
      )}
    </div>
  );
}
