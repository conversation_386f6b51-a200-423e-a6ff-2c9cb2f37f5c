import { useState, useEffect, useMemo, useRef, useCallback } from "react";
import { KanbanColuna } from "./KanbanColuna";
import { DetalhesModal } from "./DetalhesModal";
import { FormularioPrecatorio } from "./FormularioPrecatorio";
import { EstatisticasModal } from "./EstatisticasModal";
import { FiltrosModal } from "./FiltrosModal";
import { Precatorio, KanbanColuna as KanbanColunaType, FiltrosAvancados, DashboardStats, CORES_STATUS } from "./types";
import {
  DndContext,
  DragOverlay,
  useSensors,
  useSensor,
  PointerSensor,
  KeyboardSensor,
  defaultDropAnimationSideEffects,
  DragStartEvent,
  DragEndEvent,
  DragOverEvent,
  MeasuringStrategy,
  DropAnimation,
  Modifier,
  closestCorners
} from "@dnd-kit/core";
import { sortableKeyboardCoordinates } from '@dnd-kit/sortable';
import { restrictToWindowEdges } from "@dnd-kit/modifiers";
import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { PrecatorioCard } from "./PrecatorioCard";
import {
  FilterX,
  Plus,
  Filter,
  BarChart,
  Search,
  SlidersHorizontal,
  BarChart2,
  X,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { cn } from "@/lib/utils";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface KanbanContainerProps {
  todosPrecatorios: Precatorio[];
  colunas: KanbanColunaType[];
  onSavePrecatorio: (precatorio: Precatorio) => void;
  onDeletePrecatorio: (id: string) => void;
  onMovePrecatorio: (precatorioId: string, novoStatus: string) => void;
}

// Configuração simples para a animação de drop
const dropAnimation: DropAnimation = {
  sideEffects: defaultDropAnimationSideEffects({
    styles: {
      active: {
        opacity: '0.3',  // Card original fica semi-transparente
      },
    }
  }),
  duration: 150,
  easing: 'ease-out',
};

// Restringir apenas às bordas da janela, sem modificadores personalizados
const modifiers: Modifier[] = [restrictToWindowEdges];

export function KanbanContainer({
  todosPrecatorios,
  colunas,
  onSavePrecatorio,
  onDeletePrecatorio,
  onMovePrecatorio,
}: KanbanContainerProps) {
  // Estados para os modais
  const [precatorioSelecionado, setPrecatorioSelecionado] = useState<Precatorio | null>(null);
  const [isDetalhesModalOpen, setIsDetalhesModalOpen] = useState(false);
  const [isFormModalOpen, setIsFormModalOpen] = useState(false);
  const [isFiltrosModalOpen, setIsFiltrosModalOpen] = useState(false);
  const [isEstatisticasModalOpen, setIsEstatisticasModalOpen] = useState(false);
  const [isConfirmDeleteOpen, setIsConfirmDeleteOpen] = useState(false);
  const [precatorioIdToDelete, setPrecatorioIdToDelete] = useState<string | null>(null);
  const [modoEdicao, setModoEdicao] = useState(false);

  // Estados para filtros e busca
  const [filtrosAvancados, setFiltrosAvancados] = useState<FiltrosAvancados>({
    prioridade: "",
    tribunal: "",
    natureza: "",
    valorMin: undefined,
    valorMax: undefined,
    dataVencimentoInicio: undefined,
    dataVencimentoFim: undefined,
    tags: [],
    responsavel: "",
  });
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredPrecatorios, setFilteredPrecatorios] = useState<Precatorio[]>(todosPrecatorios);

  // Estado para drag and drop
  const [activePrecatorio, setActivePrecatorio] = useState<Precatorio | null>(null);

  // Sensores para drag and drop (ativando auto-scroll do dnd-kit)
  const sensors = useSensors(
    // Usar PointerSensor com configuração para arrasto imediato
    useSensor(PointerSensor, {
      // Configuração para arrasto imediato
      activationConstraint: {
        distance: 1, // Distância mínima para ativar o arrasto (valor mínimo para arrasto imediato)
        delay: 0, // Sem delay para iniciar o arrasto
        tolerance: 3, // Tolerância mínima para movimentos acidentais
      },
    }),
    // Manter KeyboardSensor para acessibilidade
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  );

  const containerRef = useRef<HTMLDivElement>(null);

  // Estado para controlar o arrasto do scroll lateral
  const [isDraggingScroll, setIsDraggingScroll] = useState(false);
  const [scrollStartX, setScrollStartX] = useState(0);
  const [scrollStartScrollLeft, setScrollStartScrollLeft] = useState(0);

  // Novo estado para controlar o modo responsivo
  const [isMobileView, setIsMobileView] = useState(false);
  
  // Detectar se está em dispositivo móvel
  useEffect(() => {
    const checkMobileView = () => {
      setIsMobileView(window.innerWidth < 768);
    };
    
    // Verificar no carregamento inicial
    checkMobileView();
    
    // Adicionar listener para redimensionamento
    window.addEventListener('resize', checkMobileView);
    
    // Limpar listener quando o componente for desmontado
    return () => {
      window.removeEventListener('resize', checkMobileView);
    };
  }, []);

  // Funções para permitir arrastar o scroll lateral clicando em espaços vazios
  const handleMouseDownOnContainer = (e: React.MouseEvent<HTMLDivElement>) => {
    // Verificar se o clique foi em um espaço vazio (não em um card ou coluna)
    // Verificar se o elemento clicado ou seus pais têm a classe data-draggable="card"
    const target = e.target as HTMLElement;

    // Verificar se o clique foi em um card ou em um elemento dentro de um card
    let currentElement: HTMLElement | null = target;
    while (currentElement) {
      if (currentElement.getAttribute('data-draggable') === 'card' ||
          currentElement.classList.contains('precatorio-card')) {
        // Se for um card, não iniciar o arrasto do scroll
        return;
      }
      currentElement = currentElement.parentElement;
    }

    // Se chegou aqui, não é um card, então verificar se é um espaço válido para arrastar o scroll
    if (target.classList.contains('kanban-container') ||
        target.classList.contains('flex') ||
        target.classList.contains('p-3') ||
        target.classList.contains('gap-4')) {
      setIsDraggingScroll(true);
      setScrollStartX(e.clientX);
      setScrollStartScrollLeft(containerRef.current?.scrollLeft || 0);
      // Mudar o cursor para indicar que o scroll pode ser arrastado
      document.body.classList.add('scrolling');
    }
  };

  const handleMouseMoveOnContainer = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!isDraggingScroll) return;

    // Calcular a distância percorrida
    const dx = e.clientX - scrollStartX;

    // Aplicar o scroll
    if (containerRef.current) {
      containerRef.current.scrollLeft = scrollStartScrollLeft - dx;
    }
  };

  const handleMouseUpOnContainer = () => {
    setIsDraggingScroll(false);
    document.body.classList.remove('scrolling');
  };

  // Adicionar e remover event listeners globais para capturar o mouse up mesmo fora do container
  useEffect(() => {
    const handleGlobalMouseUp = () => {
      setIsDraggingScroll(false);
      document.body.classList.remove('scrolling');
    };

    document.addEventListener('mouseup', handleGlobalMouseUp);

    return () => {
      document.removeEventListener('mouseup', handleGlobalMouseUp);
    };
  }, []);

  // Filtrar precatórios
  useEffect(() => {
    let filtered = [...todosPrecatorios];

    // Aplicar filtro de busca textual
    if (searchTerm.trim()) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(p =>
        p.numero.toLowerCase().includes(searchLower) ||
        p.natureza?.toLowerCase().includes(searchLower) ||
        p.observacoes?.toLowerCase().includes(searchLower) ||
        p.cliente?.nome.toLowerCase().includes(searchLower) ||
        p.tribunal?.toLowerCase().includes(searchLower) ||
        p.responsavel?.nome.toLowerCase().includes(searchLower)
      );
    }

    // Aplicar filtros avançados
    if (filtrosAvancados.prioridade) {
      filtered = filtered.filter(p => p.prioridade === filtrosAvancados.prioridade);
    }

    if (filtrosAvancados.tribunal) {
      filtered = filtered.filter(p => p.tribunal === filtrosAvancados.tribunal);
    }

    if (filtrosAvancados.natureza) {
      filtered = filtered.filter(p => p.natureza === filtrosAvancados.natureza);
    }

    if (filtrosAvancados.valorMin !== undefined) {
      filtered = filtered.filter(p => p.valor >= filtrosAvancados.valorMin);
    }

    if (filtrosAvancados.valorMax !== undefined) {
      filtered = filtered.filter(p => p.valor <= filtrosAvancados.valorMax);
    }

    if (filtrosAvancados.responsavel) {
      filtered = filtered.filter(p => p.responsavel?.nome === filtrosAvancados.responsavel);
    }

    if (filtrosAvancados.tags.length > 0) {
      filtered = filtered.filter(p =>
        filtrosAvancados.tags.some(tag => p.tags?.includes(tag))
      );
    }

    setFilteredPrecatorios(filtered);
  }, [todosPrecatorios, searchTerm, filtrosAvancados]);

  // Calcular estatísticas
  const dashboardStats = useMemo(() => {
    // Ajustar para comparar com os valores corretos do banco de dados
    const totalPrecatorios = todosPrecatorios.length;
    const concluidos = todosPrecatorios.filter(p => p.status === "concluido").length;

    const stats: DashboardStats = {
      total: totalPrecatorios,
      valorTotal: todosPrecatorios.reduce((sum, p) => sum + p.valor, 0),
      emAndamento: todosPrecatorios.filter(p => p.status !== "concluido" && p.status !== "cancelado").length,
      concluidos: concluidos,
      taxaConclusao: totalPrecatorios > 0 ? (concluidos / totalPrecatorios) * 100 : 0,
      porStatus: [],
      porPrioridade: [],
      valoresPorTribunal: [],
    };

    // Agrupar por status
    const statusMap = new Map<string, number>();
    todosPrecatorios.forEach(p => {
      statusMap.set(p.status, (statusMap.get(p.status) || 0) + 1);
    });

    stats.porStatus = Array.from(statusMap.entries()).map(([status, valor]) => ({
      status,
      valor,
      cor: CORES_STATUS[status as keyof typeof CORES_STATUS] || "gray",
    }));

    // Agrupar por prioridade
    const prioridadeMap = new Map<string, number>();
    todosPrecatorios.forEach(p => {
      prioridadeMap.set(p.prioridade, (prioridadeMap.get(p.prioridade) || 0) + 1);
    });

    stats.porPrioridade = Array.from(prioridadeMap.entries()).map(([prioridade, valor]) => ({
      prioridade,
      valor,
    }));

    // Agrupar valores por tribunal
    const tribunalMap = new Map<string, number>();
    todosPrecatorios.forEach(p => {
      tribunalMap.set(p.tribunal, (tribunalMap.get(p.tribunal) || 0) + p.valor);
    });

    stats.valoresPorTribunal = Array.from(tribunalMap.entries()).map(([tribunal, valor]) => ({
      tribunal,
      valor,
    }));

    return stats;
  }, [todosPrecatorios]);

  // Ações dos botões e handlers
  const handleVerDetalhes = (precatorio: Precatorio) => {
    setPrecatorioSelecionado(precatorio);
    setIsDetalhesModalOpen(true);
  };

  const handleEditarPrecatorio = (precatorio: Precatorio) => {
    setPrecatorioSelecionado(precatorio);
    setModoEdicao(true);
    setIsFormModalOpen(true);
  };

  const handleNovoPrecatorio = useCallback(() => {
    setPrecatorioSelecionado(null);
    setModoEdicao(false);
    setIsFormModalOpen(true);
  }, []);

  const handleDeletePrecatorio = (id: string) => {
    setPrecatorioIdToDelete(id);
    setIsConfirmDeleteOpen(true);
  };

  const confirmDeletePrecatorio = () => {
    if (precatorioIdToDelete) {
      onDeletePrecatorio(precatorioIdToDelete);
      setIsConfirmDeleteOpen(false);
      setPrecatorioIdToDelete(null);
    }
  };

  // Função de arrastar para iniciar o drag
  const handleDragStart = useCallback((event: DragStartEvent) => {
    const { active } = event;
    const precatorio = active.data.current?.precatorio;

    if (precatorio) {
      console.log(`Iniciando arrasto do precatório ${precatorio.id}`);
      setActivePrecatorio(precatorio);

      // Adicionar classe ao body para evitar seleção de texto durante o arrasto
      document.body.classList.add('dragging');

      // Desativar o arrasto do scroll lateral quando estiver arrastando um card
      setIsDraggingScroll(false);
      document.body.classList.remove('scrolling');
    }
  }, []);

  // Função para quando o item é movido sobre uma coluna
  const handleDragOver = useCallback((event: DragOverEvent) => {
    const { active, over } = event;
    if (!over) return;

    // Verificar se o card está próximo à borda para fazer scroll lateral mais rápido
    const containerElement = containerRef.current;
    if (containerElement) {
      const containerRect = containerElement.getBoundingClientRect();
      // Obter a posição do mouse a partir do evento
      const mouseX = (event.activatorEvent as MouseEvent)?.clientX ||
                    (event.activatorEvent as TouchEvent)?.touches?.[0]?.clientX || 0;

      // Calcular a distância da borda
      const distanceFromLeft = mouseX - containerRect.left;
      const distanceFromRight = containerRect.right - mouseX;

      // Se estiver próximo à borda esquerda, fazer scroll para a esquerda
      if (distanceFromLeft < 100) {
        const scrollAmount = Math.max(30, 100 - distanceFromLeft);
        containerElement.scrollLeft -= scrollAmount / 5;
      }

      // Se estiver próximo à borda direita, fazer scroll para a direita
      if (distanceFromRight < 100) {
        const scrollAmount = Math.max(30, 100 - distanceFromRight);
        containerElement.scrollLeft += scrollAmount / 5;
      }
    }

    // Lógica de movimentação (já existente)
    const activeId = active.id as string;
    const activeData = active.data.current;
    const overContainerId = over.data.current?.coluna?.id || over.id;

    // Verificar se temos dados válidos
    if (activeId && activeData && overContainerId) {
      // Não fazer nada se o precatório já está na coluna de destino
      const precatorio = activeData.precatorio;
      if (precatorio && precatorio.status !== overContainerId) {
        console.log(`Movendo precatório ${activeId} para coluna ${overContainerId}`);
      }
    }
  }, []);

  // Função para quando o item é movido para fora de uma coluna
  const handleDragEnd = useCallback((event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      const precatorioId = active.id as string;
      const novoStatus = over.id as string;

      // Verificar se temos dados válidos
      if (precatorioId && novoStatus) {
        console.log(`Finalizando movimento do precatório ${precatorioId} para coluna ${novoStatus}`);

        // Chama a API para atualizar no servidor
        onMovePrecatorio(precatorioId, novoStatus);

        // Atualiza o estado local
        setFilteredPrecatorios(prev =>
          prev.map(p => p.id === precatorioId ? { ...p, status: novoStatus } : p)
        );
      }
    }

    setActivePrecatorio(null);

    // Remover classe do body
    document.body.classList.remove('dragging');
  }, [onMovePrecatorio]);

  // Função para quando o arrasto é cancelado
  const handleDragCancel = useCallback(() => {
    console.log('Arrasto cancelado');
    setActivePrecatorio(null);

    // Remover classe do body
    document.body.classList.remove('dragging');
  }, []);

  // Agrupamento de precatórios por status usando colunas dinâmicas do banco de dados
  const precatoriosPorStatus = useMemo(() => {
    const result: Record<string, Precatorio[]> = {};

    // Criar mapeamento de status para colunas
    const statusParaColuna = new Map<string, string>();
    colunas.forEach(coluna => {
      statusParaColuna.set(coluna.status_id, coluna.id);
      // Também mapear o status_id para ele mesmo para garantir compatibilidade
      statusParaColuna.set(coluna.id, coluna.id);
    });

    // Inicializar todas as colunas com arrays vazios
    colunas.forEach(coluna => {
      // Usar o ID da coluna como chave no resultado
      result[coluna.id] = [];
    });

    // Distribuir precatórios nas colunas correspondentes
    filteredPrecatorios.forEach(precatorio => {
      // Verificar se o status está definido
      if (!precatorio.status) {
        console.warn(`Precatório sem status definido:`, precatorio);
        return;
      }

      // Normalizar o status (substituir 'ativo' por 'analise' se necessário)
      // Convertemos para string para garantir compatibilidade entre valores do banco de dados e do frontend
      const statusNormalizado = precatorio.status === 'ativo' ? 'analise' : precatorio.status.toString();

      // Encontrar a coluna correspondente usando o mapeamento
      const colunaId = statusParaColuna.get(statusNormalizado);

      if (colunaId && result[colunaId]) {
        // Se encontrou uma coluna correspondente, adicionar o precatório
        result[colunaId].push(precatorio);
      } else {
        // Se não encontrou, usar a primeira coluna como fallback
        console.warn(`Coluna não encontrada para status "${statusNormalizado}", usando fallback:`, precatorio);
        const primeiraColuna = colunas[0]?.id;
        if (primeiraColuna && result[primeiraColuna]) {
          result[primeiraColuna].push(precatorio);
        }
      }
    });

    return result;
  }, [filteredPrecatorios, colunas]);

  return (
    <div className="flex flex-col h-full">
      {/* Barra de navegação e controles - design melhorado */}
      <div className={cn(
        "sticky top-0 z-10",
        "py-3 px-4 border-b",
        "bg-gradient-to-r from-background via-background/95 to-background/90 backdrop-blur-md",
        "transition-all duration-300 ease-in-out",
      )}>
        {/* Cabeçalho principal com título */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-3">
            <h2 className="text-xl font-semibold tracking-tight">
              Kanban de Precatórios
            </h2>
            <Badge variant="outline" className="font-medium">
              {filteredPrecatorios.length} precatórios
            </Badge>
          </div>
          
          {/* Botão de adicionar em destaque */}
          <Button
            size="sm"
            className="gap-1 bg-primary/90 hover:bg-primary shadow-sm"
            onClick={() => {
              setPrecatorioSelecionado(null);
              setModoEdicao(false);
              setIsFormModalOpen(true);
            }}
          >
            <Plus className="h-4 w-4" />
            <span>Novo Precatório</span>
          </Button>
        </div>

        {/* Barra de ferramentas secundária */}
        <div className="flex flex-wrap items-center gap-3 justify-between">
          <div className="flex items-center gap-2 flex-grow md:flex-grow-0">
            {/* Input de busca - agora mais responsivo e estilizado */}
            <div className="relative w-full max-w-sm">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Buscar precatório..."
                className="pl-8 h-9 md:w-[220px] lg:w-[300px] bg-background/60 ring-1 ring-border/40 focus-visible:ring-primary/30"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              {searchTerm && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-9 w-9 rounded-l-none"
                  onClick={() => setSearchTerm("")}
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>

          {/* Botões de ação - agora com visual aprimorado */}
          <div className="flex items-center gap-2 flex-wrap">
            <TooltipProvider>
              {/* Botão de Filtros com indicador visual */}
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={Object.values(filtrosAvancados).some(v => v !== "" && v !== undefined && (Array.isArray(v) ? v.length > 0 : true)) ? "secondary" : "outline"}
                    size="sm"
                    className={cn(
                      "h-9 gap-1 transition-all shadow-sm",
                      Object.values(filtrosAvancados).some(v => v !== "" && v !== undefined && (Array.isArray(v) ? v.length > 0 : true)) && "bg-secondary/90"
                    )}
                    onClick={() => setIsFiltrosModalOpen(true)}
                  >
                    <Filter className="h-4 w-4" />
                    <span className="hidden sm:inline">Filtros</span>
                    {Object.values(filtrosAvancados).some(v => v !== "" && v !== undefined && (Array.isArray(v) ? v.length > 0 : true)) && (
                      <Badge variant={Object.values(filtrosAvancados).some(v => v !== "" && v !== undefined && (Array.isArray(v) ? v.length > 0 : true)) ? "default" : "secondary"} className="ml-1 h-5 px-1">
                        {Object.values(filtrosAvancados).filter(v => v !== "" && v !== undefined && (Array.isArray(v) ? v.length > 0 : true)).length}
                      </Badge>
                    )}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Filtros avançados</p>
                </TooltipContent>
              </Tooltip>

              {/* Botão de Estatísticas */}
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-9 gap-1 shadow-sm"
                    onClick={() => setIsEstatisticasModalOpen(true)}
                  >
                    <BarChart2 className="h-4 w-4" />
                    <span className="hidden sm:inline">Estatísticas</span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Ver estatísticas</p>
                </TooltipContent>
              </Tooltip>

              {/* Botão para Limpar Filtros - mostrar apenas se houver filtros ativos */}
              {Object.values(filtrosAvancados).some(v => v !== "" && v !== undefined && (Array.isArray(v) ? v.length > 0 : true)) && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-9 gap-1"
                      onClick={() => {
                        setFiltrosAvancados({
                          prioridade: "",
                          tribunal: "",
                          natureza: "",
                          valorMin: undefined,
                          valorMax: undefined,
                          dataVencimentoInicio: undefined,
                          dataVencimentoFim: undefined,
                          tags: [],
                          responsavel: "",
                        });
                      }}
                    >
                      <FilterX className="h-4 w-4" />
                      <span className="hidden sm:inline">Limpar filtros</span>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Limpar filtros aplicados</p>
                  </TooltipContent>
                </Tooltip>
              )}
            </TooltipProvider>
          </div>
        </div>
      </div>

      {/* Exibir resultados de busca ou filtros - estilo melhorado */}
      {(searchTerm || Object.values(filtrosAvancados).some(v => v !== "" && v !== undefined && (Array.isArray(v) ? v.length > 0 : true))) && (
        <div className="bg-muted/20 py-2 px-4 text-sm flex items-center justify-between border-b">
          <div className="flex items-center gap-2">
            <span className="text-muted-foreground">Resultados:</span>
            <span className="font-medium">{filteredPrecatorios.length}</span> 
            {filteredPrecatorios.length === 1 ? "precatório encontrado" : "precatórios encontrados"}
            {searchTerm && <span className="text-muted-foreground ml-1">para <span className="italic font-medium">"{searchTerm}"</span></span>}
          </div>
          
          <Badge variant="outline" className="ml-2">
            {Object.values(filtrosAvancados).some(v => v !== "" && v !== undefined && (Array.isArray(v) ? v.length > 0 : true))
              ? `${Object.values(filtrosAvancados).filter(v => v !== "" && v !== undefined && (Array.isArray(v) ? v.length > 0 : true)).length} filtros aplicados`
              : "Sem filtros"}
          </Badge>
        </div>
      )}

      {/* Container principal do Kanban com DndContext */}
      <DndContext
        sensors={sensors}
        collisionDetection={closestCorners}
        onDragStart={handleDragStart}
        onDragOver={handleDragOver}
        onDragEnd={handleDragEnd}
        measuring={{
          droppable: {
            strategy: MeasuringStrategy.Always,
          },
        }}
        modifiers={modifiers}
      >
        {/* Container scrollable para as colunas */}
        <div
          ref={containerRef}
          className={cn(
            "flex-1 flex items-start h-full overflow-x-auto overflow-y-hidden",
            "scrollbar-thin scrollbar-track-transparent",
            "scrollbar-thumb-neutral-300 dark:scrollbar-thumb-neutral-600",
            "p-3 md:p-4 gap-4 relative",  // Aumentar padding em telas maiores
            isDraggingScroll && "cursor-grabbing",
            isMobileView ? "snap-x snap-mandatory" : ""  // Adicionar snap para mobile
          )}
          onMouseDown={handleMouseDownOnContainer}
          onMouseMove={handleMouseMoveOnContainer}
          onMouseUp={handleMouseUpOnContainer}
        >
          {/* Mensagem quando não há colunas */}
          {colunas.length === 0 && (
            <div className="w-full h-full flex items-center justify-center text-muted-foreground">
              <div className="text-center">
                <p className="mb-2">Nenhuma coluna configurada.</p>
                <Button variant="outline" size="sm">
                  Configurar colunas
                </Button>
              </div>
            </div>
          )}

          {/* Renderizar colunas */}
          {colunas.map((coluna) => {
            // Encontrar precatórios desta coluna
            const precatoriosDaColuna = filteredPrecatorios.filter(
              (p) => p.status === coluna.status_id
            );

            return (
              <div 
                key={coluna.id} 
                className={cn(
                  "h-full", 
                  "transition-all duration-200",
                  isMobileView ? "snap-start min-w-[90vw] flex justify-center" : ""  // Colunas ocupam 90% da largura em mobile com snap
                )}
              >
                <KanbanColuna
                  coluna={coluna}
                  precatorios={precatoriosDaColuna}
                  onVerDetalhes={handleVerDetalhes}
                  onEdit={handleEditarPrecatorio}
                  onDelete={handleDeletePrecatorio}
                  onNovoPrecatorio={() => {
                    setPrecatorioSelecionado(null);
                    setModoEdicao(false);
                    setIsFormModalOpen(true);
                  }}
                />
              </div>
            );
          })}
        </div>

        <DragOverlay dropAnimation={dropAnimation}>
          {activePrecatorio ? (
            <PrecatorioCard
              precatorio={activePrecatorio}
              onVerDetalhes={() => {}}
              onEdit={() => {}}
              onDelete={() => {}}
              isOverlay={true}
            />
          ) : null}
        </DragOverlay>
      </DndContext>

      {/* Modais */}
      {isDetalhesModalOpen && precatorioSelecionado && (
        <DetalhesModal
          precatorio={precatorioSelecionado}
          isOpen={isDetalhesModalOpen}
          onOpenChange={setIsDetalhesModalOpen}
          onEdit={() => {
            setIsDetalhesModalOpen(false);
            handleEditarPrecatorio(precatorioSelecionado);
          }}
        />
      )}

      {isFormModalOpen && (
        <FormularioPrecatorio
          precatorio={modoEdicao ? precatorioSelecionado : undefined}
          isOpen={isFormModalOpen}
          onOpenChange={setIsFormModalOpen}
          modoEdicao={modoEdicao}
          onSave={(precatorio) => {
            onSavePrecatorio(precatorio);
            setIsFormModalOpen(false);
          }}
        />
      )}

      {isFiltrosModalOpen && (
        <FiltrosModal
          filtros={filtrosAvancados}
          isOpen={isFiltrosModalOpen}
          onOpenChange={setIsFiltrosModalOpen}
          onAplicarFiltros={(novosFiltros) => {
            setFiltrosAvancados(novosFiltros);
            setIsFiltrosModalOpen(false);
          }}
          onLimparFiltros={() => {
            setFiltrosAvancados({
              prioridade: "",
              tribunal: "",
              natureza: "",
              valorMin: undefined,
              valorMax: undefined,
              dataVencimentoInicio: undefined,
              dataVencimentoFim: undefined,
              tags: [],
              responsavel: "",
            });
          }}
        />
      )}

      {isEstatisticasModalOpen && (
        <EstatisticasModal
          stats={dashboardStats}
          isOpen={isEstatisticasModalOpen}
          onOpenChange={setIsEstatisticasModalOpen}
        />
      )}

      <AlertDialog open={isConfirmDeleteOpen} onOpenChange={setIsConfirmDeleteOpen}>
        <AlertDialogContent aria-describedby="alert-dialog-description">
          <AlertDialogHeader>
            <AlertDialogTitle>Confirmar exclusão</AlertDialogTitle>
            <AlertDialogDescription id="alert-dialog-description">
              Tem certeza que deseja excluir este precatório? Esta ação não pode ser desfeita.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDeletePrecatorio} className="bg-red-600 hover:bg-red-700">
              Excluir
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}