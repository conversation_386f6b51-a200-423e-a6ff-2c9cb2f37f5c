import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { toast } from "sonner";

interface NovoPrecatorioFormSimplesProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (precatorio: any) => void;
}

export function NovoPrecatorioFormSimples(props: NovoPrecatorioFormSimplesProps) {
  const { isOpen, onOpenChange, onSave } = props;
  
  const [numero, setNumero] = useState("");
  const [valor, setValor] = useState("");
  
  const handleSave = () => {
    if (!numero || !valor) {
      toast.error("Preencha todos os campos obrigatórios");
      return;
    }
    
    const novoPrecatorio = {
      numero_precatorio: numero,
      valor_total: parseFloat(valor),
      status: "Novo",
      tipo: "PRECATORIO"
    };
    
    onSave(novoPrecatorio);
    onOpenChange(false);
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Novo Precatório</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Número do Precatório *</label>
            <Input
              placeholder="Número do precatório"
              value={numero}
              onChange={(e) => setNumero(e.target.value)}
            />
          </div>
          
          <div className="space-y-2">
            <label className="text-sm font-medium">Valor Total *</label>
            <Input
              type="number"
              placeholder="Valor total"
              value={valor}
              onChange={(e) => setValor(e.target.value)}
            />
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button onClick={handleSave}>
            Criar Precatório
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
