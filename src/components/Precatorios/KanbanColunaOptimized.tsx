import React, { useMemo, useState } from "react";
import { useVirtualizer } from "@tanstack/react-virtual";
import { useDroppable } from "@dnd-kit/core";
import { Precatorio, KanbanColuna as KanbanColunaType } from "./types";
import { PrecatorioCardOptimized } from "./PrecatorioCardOptimized";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import { Plus, MoreVertical, ChevronLeft, ChevronRight, Edit3, Trash2, ArrowUp, ArrowDown, Copy, Archive } from "lucide-react";
import { cn } from "@/lib/utils";

interface KanbanColunaOptimizedProps {
  coluna: KanbanColunaType;
  precatorios: Precatorio[];
  onVerDetalhes: (precatorio: Precatorio) => void;
  onEdit: (precatorio: Precatorio) => void;
  onDelete: (id: string) => void;
  onNovoPrecatorio: () => void;
  canCreate?: boolean;
  canEdit?: boolean;
  canDelete?: boolean;
}

export function KanbanColunaOptimized({
  coluna,
  precatorios,
  onVerDetalhes,
  onEdit,
  onDelete,
  onNovoPrecatorio,
  canCreate = true,
  canEdit = true,
  canDelete = true,
}: KanbanColunaOptimizedProps) {
  // Estado para modo colapsado (como no Trello)
  const [isCollapsed, setIsCollapsed] = useState(false);
  // Estado para modal de opções da coluna
  const [isOptionsModalOpen, setIsOptionsModalOpen] = useState(false);

  const { setNodeRef, isOver } = useDroppable({
    id: coluna.id,
    data: {
      type: "column",
      coluna,
    },
  });

  // Filtrar precatórios desta coluna
  const precatoriosDaColuna = useMemo(() => {
    return precatorios.filter(p => p.status_id === coluna.id);
  }, [precatorios, coluna.id]);

  // Configurar virtualizador para lista de precatórios
  const parentRef = React.useRef<HTMLDivElement>(null);

  // Calcular altura dinâmica baseada no número de cards e altura disponível
  const cardHeight = 120; // Altura de cada card
  const headerHeight = 80; // Altura do header da coluna
  const padding = 40; // Padding total
  const topNavHeight = 120; // Altura do TopNav + filtros
  const topPadding = 64; // Padding superior do container (pt-16 = 64px)
  const bottomPadding = 24; // Padding inferior do container (pb-6 = 24px)
  const availableHeight = window.innerHeight - topNavHeight - topPadding - bottomPadding; // Altura disponível real

  // Altura baseada no conteúdo, mas limitada pela altura disponível
  const contentHeight = precatoriosDaColuna.length * cardHeight + padding;
  const minHeight = 300; // Altura mínima da coluna
  const maxHeight = availableHeight; // Máximo da altura disponível

  const colunaHeight = Math.max(minHeight, Math.min(maxHeight, contentHeight));

  const virtualizer = useVirtualizer({
    count: precatoriosDaColuna.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => cardHeight,
    overscan: 3, // Reduzir overscan para melhor performance
  });

  // Renderizar versão colapsada (como no Trello)
  if (isCollapsed) {
    return (
      <Card
        ref={setNodeRef}
        className={cn(
          "flex flex-col w-12 bg-card/50 backdrop-blur-sm cursor-pointer",
          "border-t-4 transition-all duration-300 hover:w-14",
          isOver ? "ring-2 ring-primary/60 shadow-lg" : "shadow-sm",
          "hover:shadow-md"
        )}
        style={{
          borderTopColor: coluna.cor || coluna.color || "#3b82f6",
          height: `${colunaHeight}px`
        }}
        onClick={() => setIsCollapsed(false)}
      >
        <div className="p-2 flex flex-col items-center h-full">
          <div className="text-sm font-medium text-center writing-mode-vertical-rl text-orientation-mixed mt-8 mb-4" style={{ writingMode: 'vertical-rl', textOrientation: 'mixed' }}>
            {(coluna.nome || coluna.name).substring(0, 12)}
          </div>
          <Badge variant="secondary" className="text-xs px-1 h-4 mt-2">
            {precatoriosDaColuna.length}
          </Badge>
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0 mt-auto mb-2"
            onClick={(e) => {
              e.stopPropagation();
              setIsCollapsed(false);
            }}
          >
            <ChevronRight className="h-3 w-3" />
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <>
      <Card
        ref={setNodeRef}
        className={cn(
          "flex flex-col w-[280px] bg-card/50 backdrop-blur-sm", // Mais estreita como no Trello
          "border-t-4 transition-all duration-200",
          isOver ? "ring-2 ring-primary/60 scale-[1.02] shadow-lg" : "shadow-sm",
          "hover:shadow-md"
        )}
        style={{
          borderTopColor: coluna.cor || coluna.color || "#3b82f6",
          height: `${colunaHeight}px`
        }}
      >
        {/* Header da coluna */}
        <CardHeader className="pb-3 px-4 py-3 border-b bg-card/80">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: coluna.cor || coluna.color }}
              />
              <h3 className="font-medium text-sm truncate max-w-[180px]">
                {coluna.nome || coluna.name}
              </h3>
              <Badge variant="secondary" className="text-xs px-1.5 h-5">
                {precatoriosDaColuna.length}
              </Badge>
            </div>

            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="sm"
                className="h-7 w-7 p-0"
                onClick={() => setIsCollapsed(true)}
                title="Colapsar coluna"
              >
                <ChevronLeft className="h-3.5 w-3.5" />
              </Button>
              {canCreate && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-7 w-7 p-0"
                  onClick={onNovoPrecatorio}
                >
                  <Plus className="h-3.5 w-3.5" />
                </Button>
              )}
              <Button
                variant="ghost"
                size="sm"
                className="h-7 w-7 p-0"
                onClick={() => setIsOptionsModalOpen(true)}
              >
                <MoreVertical className="h-3.5 w-3.5" />
              </Button>
            </div>
          </div>
        </CardHeader>

        {/* Lista virtualizada de precatórios */}
        <CardContent className="flex-1 p-0 overflow-hidden flex flex-col">
          {precatoriosDaColuna.length === 0 ? (
            <div className="flex flex-col items-center justify-center flex-1 text-muted-foreground">
              <div className="text-center p-4">
                <p className="text-sm mb-3">Nenhum precatório</p>
                {canCreate && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={onNovoPrecatorio}
                    className="text-xs"
                  >
                    <Plus className="h-3 w-3 mr-1" />
                    Adicionar
                  </Button>
                )}
              </div>
            </div>
          ) : (
            <div className="flex-1 overflow-hidden">
              <ScrollArea className="h-full">
                <div
                  ref={parentRef}
                  className="h-full overflow-auto"
                  style={{
                    height: `${colunaHeight - headerHeight - 60}px`, // Altura dinâmica menos header e botão inferior
                  }}
                >
                <div
                  style={{
                    height: `${virtualizer.getTotalSize()}px`,
                    width: "100%",
                    position: "relative",
                  }}
                >
                  {virtualizer.getVirtualItems().map((virtualItem) => {
                    const precatorio = precatoriosDaColuna[virtualItem.index];

                    return (
                      <div
                        key={virtualItem.key}
                        style={{
                          position: "absolute",
                          top: 0,
                          left: 0,
                          width: "100%",
                          height: `${virtualItem.size}px`,
                          transform: `translateY(${virtualItem.start}px)`,
                        }}
                      >
                        <div className="p-2">
                          <PrecatorioCardOptimized
                            precatorio={precatorio}
                            onVerDetalhes={onVerDetalhes}
                            onEdit={canEdit ? onEdit : undefined}
                            onDelete={canDelete ? onDelete : undefined}
                            compact={true}
                          />
                        </div>
                      </div>
                    );
                  })}
                </div>
                </div>
              </ScrollArea>
            </div>
          )}

          {/* Botão "Adicionar cartão" na parte inferior (como no Trello) */}
          {canCreate && (
            <div className="px-2 py-0.5 border-t bg-card/60">
              <Button
                variant="ghost"
                size="sm"
                className="w-full justify-start text-muted-foreground hover:text-foreground h-5 text-xs py-1"
                onClick={onNovoPrecatorio}
              >
                <Plus className="h-3 w-3 mr-1" />
                Adicionar um cartão
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Modal de opções da coluna */}
      <Dialog open={isOptionsModalOpen} onOpenChange={setIsOptionsModalOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <div
                className="w-4 h-4 rounded-full"
                style={{ backgroundColor: coluna.cor || coluna.color }}
              />
              Opções da Coluna: {coluna.nome || coluna.name}
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-2">
            {/* Ações de edição */}
            <div className="space-y-1">
              <Button
                variant="ghost"
                className="w-full justify-start h-9"
                onClick={() => {
                  setIsOptionsModalOpen(false);
                  // TODO: Implementar edição da coluna
                }}
              >
                <Edit3 className="h-4 w-4 mr-3" />
                Editar coluna
              </Button>

              <Button
                variant="ghost"
                className="w-full justify-start h-9"
                onClick={() => {
                  setIsOptionsModalOpen(false);
                  // TODO: Implementar duplicação da coluna
                }}
              >
                <Copy className="h-4 w-4 mr-3" />
                Duplicar coluna
              </Button>
            </div>

            <Separator />

            {/* Ações de ordenação */}
            <div className="space-y-1">
              <Button
                variant="ghost"
                className="w-full justify-start h-9"
                onClick={() => {
                  setIsOptionsModalOpen(false);
                  // TODO: Implementar ordenação por data
                }}
              >
                <ArrowUp className="h-4 w-4 mr-3" />
                Ordenar por data (mais recente)
              </Button>

              <Button
                variant="ghost"
                className="w-full justify-start h-9"
                onClick={() => {
                  setIsOptionsModalOpen(false);
                  // TODO: Implementar ordenação por valor
                }}
              >
                <ArrowDown className="h-4 w-4 mr-3" />
                Ordenar por valor (maior)
              </Button>
            </div>

            <Separator />

            {/* Ações de arquivamento */}
            <div className="space-y-1">
              <Button
                variant="ghost"
                className="w-full justify-start h-9"
                onClick={() => {
                  setIsOptionsModalOpen(false);
                  // TODO: Implementar arquivamento de todos os cartões
                }}
              >
                <Archive className="h-4 w-4 mr-3" />
                Arquivar todos os cartões
              </Button>

              <Button
                variant="ghost"
                className="w-full justify-start h-9 text-destructive hover:text-destructive"
                onClick={() => {
                  setIsOptionsModalOpen(false);
                  // TODO: Implementar exclusão da coluna
                }}
              >
                <Trash2 className="h-4 w-4 mr-3" />
                Excluir coluna
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
