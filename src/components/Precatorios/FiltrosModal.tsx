import { useState } from "react";
import { FiltrosAvancados } from "./types";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { CalendarIcon, X } from "lucide-react";
import { cn } from "@/lib/utils";

interface FiltrosModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  filtros: FiltrosAvancados;
  onAplicarFiltros: (filtros: FiltrosAvancados) => void;
  onLimparFiltros: () => void;
}

export function FiltrosModal({ 
  isOpen, 
  onOpenChange, 
  filtros, 
  onAplicarFiltros, 
  onLimparFiltros 
}: FiltrosModalProps) {
  const [filtrosTemp, setFiltrosTemp] = useState<FiltrosAvancados>({ ...filtros });
  
  const handleChange = (campo: keyof FiltrosAvancados, valor: any) => {
    const valorFinal = valor === "qualquer" ? "" : valor;
    
    setFiltrosTemp(prev => ({
      ...prev,
      [campo]: valorFinal
    }));
  };
  
  const handleResetarFiltros = () => {
    setFiltrosTemp({
      prioridade: "",
      tribunal: "",
      natureza: "",
      valorMin: undefined,
      valorMax: undefined,
      dataInicio: undefined,
      dataFim: undefined,
      tags: [],
      responsavel: ""
    });
    onLimparFiltros();
  };
  
  const handleAplicarFiltros = () => {
    onAplicarFiltros(filtrosTemp);
    onOpenChange(false);
  };
  
  // Conta quantos filtros estão ativos
  const filtrosAtivos = Object.entries(filtrosTemp).filter(([_, valor]) => {
    if (Array.isArray(valor)) return valor.length > 0;
    return valor !== undefined && valor !== "";
  }).length;
  
  const adicionarTag = (tag: string) => {
    if (tag.trim() === "" || filtrosTemp.tags?.includes(tag)) return;
    
    setFiltrosTemp(prev => ({
      ...prev,
      tags: [...(prev.tags || []), tag]
    }));
  };
  
  const removerTag = (tag: string) => {
    setFiltrosTemp(prev => ({
      ...prev,
      tags: prev.tags?.filter(t => t !== tag) || []
    }));
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]" aria-describedby="filtros-description">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>Filtros Avançados</span>
            {filtrosAtivos > 0 && (
              <Badge variant="secondary" className="ml-2">
                {filtrosAtivos} filtro{filtrosAtivos !== 1 ? 's' : ''} ativo{filtrosAtivos !== 1 ? 's' : ''}
              </Badge>
            )}
          </DialogTitle>
          <DialogDescription id="filtros-description">
            Configure os filtros para encontrar precatórios com critérios específicos
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Prioridade</label>
              <Select
                value={filtrosTemp.prioridade || "qualquer"}
                onValueChange={(value) => handleChange("prioridade", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Qualquer" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="qualquer">Qualquer</SelectItem>
                  <SelectItem value="Alta">Alta</SelectItem>
                  <SelectItem value="Média">Média</SelectItem>
                  <SelectItem value="Baixa">Baixa</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">Tribunal</label>
              <Select
                value={filtrosTemp.tribunal || "qualquer"}
                onValueChange={(value) => handleChange("tribunal", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Qualquer" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="qualquer">Qualquer</SelectItem>
                  <SelectItem value="TRF1">TRF1</SelectItem>
                  <SelectItem value="TRF2">TRF2</SelectItem>
                  <SelectItem value="TRF3">TRF3</SelectItem>
                  <SelectItem value="TRF4">TRF4</SelectItem>
                  <SelectItem value="TRF5">TRF5</SelectItem>
                  <SelectItem value="TRF6">TRF6</SelectItem>
                  <SelectItem value="STF">STF</SelectItem>
                  <SelectItem value="STJ">STJ</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div className="space-y-2">
            <label className="text-sm font-medium">Natureza</label>
            <Select
              value={filtrosTemp.natureza || "qualquer"}
              onValueChange={(value) => handleChange("natureza", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Qualquer" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="qualquer">Qualquer</SelectItem>
                <SelectItem value="Alimentar">Alimentar</SelectItem>
                <SelectItem value="Comum">Comum</SelectItem>
                <SelectItem value="Fiscal">Fiscal</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Valor Mínimo</label>
              <Input
                type="number"
                placeholder="R$ 0,00"
                value={filtrosTemp.valorMin || ""}
                onChange={(e) => handleChange("valorMin", e.target.value ? Number(e.target.value) : undefined)}
              />
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">Valor Máximo</label>
              <Input
                type="number"
                placeholder="R$ 0,00"
                value={filtrosTemp.valorMax || ""}
                onChange={(e) => handleChange("valorMax", e.target.value ? Number(e.target.value) : undefined)}
              />
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Vencimento De</label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !filtrosTemp.dataInicio && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {filtrosTemp.dataInicio ? (
                      format(filtrosTemp.dataInicio, "dd/MM/yyyy", { locale: ptBR })
                    ) : (
                      <span>Selecionar data</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={filtrosTemp.dataInicio}
                    onSelect={(date) => handleChange("dataInicio", date)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">Vencimento Até</label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !filtrosTemp.dataFim && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {filtrosTemp.dataFim ? (
                      format(filtrosTemp.dataFim, "dd/MM/yyyy", { locale: ptBR })
                    ) : (
                      <span>Selecionar data</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={filtrosTemp.dataFim}
                    onSelect={(date) => handleChange("dataFim", date)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>
          
          <div className="space-y-2">
            <label className="text-sm font-medium">Responsável</label>
            <Input
              placeholder="Nome do responsável"
              value={filtrosTemp.responsavel || ""}
              onChange={(e) => handleChange("responsavel", e.target.value)}
            />
          </div>
          
          <div className="space-y-2">
            <label className="text-sm font-medium">Tags</label>
            <div className="flex flex-wrap gap-2 mb-2">
              {filtrosTemp.tags?.map((tag) => (
                <Badge key={tag} variant="secondary" className="gap-1">
                  {tag}
                  <X 
                    className="h-3 w-3 cursor-pointer" 
                    onClick={() => removerTag(tag)} 
                  />
                </Badge>
              ))}
            </div>
            <div className="flex gap-2">
              <Input
                placeholder="Nova tag"
                id="nova-tag"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    adicionarTag((e.target as HTMLInputElement).value);
                    (e.target as HTMLInputElement).value = '';
                  }
                }}
              />
              <Button 
                variant="outline" 
                type="button"
                onClick={() => {
                  const input = document.getElementById('nova-tag') as HTMLInputElement;
                  adicionarTag(input.value);
                  input.value = '';
                }}
              >
                Adicionar
              </Button>
            </div>
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={handleResetarFiltros}>
            Limpar Filtros
          </Button>
          <Button onClick={handleAplicarFiltros}>Aplicar Filtros</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 