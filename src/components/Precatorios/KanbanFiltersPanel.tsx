import { useState, useMemo } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import {
  ChevronDown,
  ChevronUp,
  X,
  Filter,
  Calendar,
  User,
  Building,
  Tag,
  AlertTriangle,
} from "lucide-react";
import { Precatorio } from "./types";
import { cn } from "@/lib/utils";

interface KanbanFiltersPanelProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  precatorios: Precatorio[];
  filtros: {
    prioridade: string;
    tribunal: string;
    responsavel: string;
    tipo: string;
    tags: string[];
  };
  onFiltrosChange: (filtros: any) => void;
  onLimparFiltros: () => void;
}

export function KanbanFiltersPanel({
  isOpen,
  onOpenChange,
  precatorios,
  filtros,
  onFiltrosChange,
  onLimparFiltros,
}: KanbanFiltersPanelProps) {
  const [expandedSections, setExpandedSections] = useState({
    geral: true,
    datas: false,
    avancado: false,
  });

  // Extrair opções únicas dos precatórios
  const opcoes = useMemo(() => {
    const tribunais = [...new Set(precatorios.map(p => p.tribunal).filter(Boolean))];
    const responsaveis = [...new Set(precatorios.map(p => p.responsavel?.nome).filter(Boolean))];
    const tags = [...new Set(precatorios.flatMap(p => p.tags || []))];
    
    return {
      tribunais: tribunais.sort(),
      responsaveis: responsaveis.sort(),
      tags: tags.sort(),
    };
  }, [precatorios]);

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const handleFiltroChange = (key: string, value: any) => {
    onFiltrosChange({
      ...filtros,
      [key]: value
    });
  };

  const handleTagToggle = (tag: string) => {
    const newTags = filtros.tags.includes(tag)
      ? filtros.tags.filter(t => t !== tag)
      : [...filtros.tags, tag];
    
    handleFiltroChange('tags', newTags);
  };

  const contarFiltrosAtivos = () => {
    let count = 0;
    if (filtros.prioridade) count++;
    if (filtros.tribunal) count++;
    if (filtros.responsavel) count++;
    if (filtros.tipo) count++;
    if (filtros.tags.length > 0) count++;
    return count;
  };

  const filtrosAtivos = contarFiltrosAtivos();

  if (!isOpen) return null;

  return (
    <Card className="border-t-0 rounded-t-none shadow-lg">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base flex items-center gap-2">
            <Filter className="h-4 w-4" />
            Filtros Avançados
            {filtrosAtivos > 0 && (
              <Badge variant="secondary" className="ml-2">
                {filtrosAtivos}
              </Badge>
            )}
          </CardTitle>
          <div className="flex items-center gap-2">
            {filtrosAtivos > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={onLimparFiltros}
                className="h-7"
              >
                <X className="h-3 w-3 mr-1" />
                Limpar
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onOpenChange(false)}
              className="h-7 w-7 p-0"
            >
              <ChevronUp className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Filtros Gerais */}
        <Collapsible
          open={expandedSections.geral}
          onOpenChange={() => toggleSection('geral')}
        >
          <CollapsibleTrigger asChild>
            <Button variant="ghost" className="w-full justify-between p-2 h-auto">
              <span className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4" />
                Filtros Gerais
              </span>
              {expandedSections.geral ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="space-y-3 pt-2">
            <div className="grid grid-cols-2 gap-3">
              {/* Prioridade */}
              <div className="space-y-1">
                <Label htmlFor="prioridade" className="text-xs">Prioridade</Label>
                <Select
                  value={filtros.prioridade}
                  onValueChange={(value) => handleFiltroChange('prioridade', value)}
                >
                  <SelectTrigger className="h-8">
                    <SelectValue placeholder="Todas" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Todas</SelectItem>
                    <SelectItem value="alta">Alta</SelectItem>
                    <SelectItem value="media">Média</SelectItem>
                    <SelectItem value="baixa">Baixa</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Tipo */}
              <div className="space-y-1">
                <Label htmlFor="tipo" className="text-xs">Tipo</Label>
                <Select
                  value={filtros.tipo}
                  onValueChange={(value) => handleFiltroChange('tipo', value)}
                >
                  <SelectTrigger className="h-8">
                    <SelectValue placeholder="Todos" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Todos</SelectItem>
                    <SelectItem value="PRECATORIO">Precatório</SelectItem>
                    <SelectItem value="RPV">RPV</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CollapsibleContent>
        </Collapsible>

        <Separator />

        {/* Filtros por Entidade */}
        <Collapsible
          open={expandedSections.avancado}
          onOpenChange={() => toggleSection('avancado')}
        >
          <CollapsibleTrigger asChild>
            <Button variant="ghost" className="w-full justify-between p-2 h-auto">
              <span className="flex items-center gap-2">
                <Building className="h-4 w-4" />
                Por Entidade
              </span>
              {expandedSections.avancado ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="space-y-3 pt-2">
            <div className="grid grid-cols-1 gap-3">
              {/* Tribunal */}
              <div className="space-y-1">
                <Label htmlFor="tribunal" className="text-xs">Tribunal</Label>
                <Select
                  value={filtros.tribunal}
                  onValueChange={(value) => handleFiltroChange('tribunal', value)}
                >
                  <SelectTrigger className="h-8">
                    <SelectValue placeholder="Todos os tribunais" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Todos os tribunais</SelectItem>
                    {opcoes.tribunais.map((tribunal) => (
                      <SelectItem key={tribunal} value={tribunal}>
                        {tribunal}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Responsável */}
              <div className="space-y-1">
                <Label htmlFor="responsavel" className="text-xs">Responsável</Label>
                <Select
                  value={filtros.responsavel}
                  onValueChange={(value) => handleFiltroChange('responsavel', value)}
                >
                  <SelectTrigger className="h-8">
                    <SelectValue placeholder="Todos os responsáveis" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Todos os responsáveis</SelectItem>
                    {opcoes.responsaveis.map((responsavel) => (
                      <SelectItem key={responsavel} value={responsavel}>
                        {responsavel}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CollapsibleContent>
        </Collapsible>

        {/* Tags */}
        {opcoes.tags.length > 0 && (
          <>
            <Separator />
            <div className="space-y-2">
              <Label className="text-xs flex items-center gap-2">
                <Tag className="h-3 w-3" />
                Tags
              </Label>
              <div className="flex flex-wrap gap-1">
                {opcoes.tags.map((tag) => (
                  <Badge
                    key={tag}
                    variant={filtros.tags.includes(tag) ? "default" : "outline"}
                    className={cn(
                      "cursor-pointer text-xs",
                      filtros.tags.includes(tag) && "bg-primary text-primary-foreground"
                    )}
                    onClick={() => handleTagToggle(tag)}
                  >
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}
