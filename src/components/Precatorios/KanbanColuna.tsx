import { Precatorio, KanbanColuna as KanbanColunaType, CORES_STATUS } from "./types";
import { PrecatorioCard } from "./PrecatorioCard";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { useDroppable } from "@dnd-kit/core";
import { AlertTriangle, PlusCircle, AlertCircle, ChevronLeft, ChevronRight, Clock, Filter, MoreVertical, MoveHorizontal, GripVertical, ShieldAlert, Lock } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useState, useEffect, useRef, memo, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { useAuth } from "@/hooks/useAuth";
import { useKanbanPermissions } from "@/hooks/useKanbanPermissions";
import { KanbanPermissionGuard } from "@/components/permissions/KanbanPermissionGuard";

interface KanbanColunaProps {
  coluna: KanbanColunaType;
  precatorios: Precatorio[];
  onVerDetalhes: (precatorio: Precatorio) => void;
  onEdit: (precatorio: Precatorio) => void;
  onDelete: (id: string) => void;
  onNovoPrecatorio: () => void;
}

function KanbanColunaComponent({
  coluna,
  precatorios,
  onVerDetalhes,
  onEdit,
  onDelete,
  onNovoPrecatorio,
}: KanbanColunaProps) {
  const [compacto, setCompacto] = useState(false);
  const navigate = useNavigate();
  const { user } = useAuth();
  const {
    canViewColumn,
    canEditColumn,
    canMovePrecatorioToColumn,
    canViewPrecatorio,
    canEditPrecatorio,
    canDeletePrecatorio,
    canCreatePrecatorio
  } = useKanbanPermissions();

  // Estado local da coluna com a contagem de precatórios e normalização de dados
  const [colunaLocal, setColunaLocal] = useState(() => {
    // Agora o id da coluna é o UUID do status e status_id é o código
    const statusId = coluna.id;
    const statusCodigo = coluna.status_id;

    // Log para debug
    console.log(`Inicializando coluna ${coluna.nome}:`, {
      id: coluna.id,
      status_id: coluna.status_id,
      precatorios: precatorios.length
    });

    return {
      ...coluna,
      id: statusId,
      status_id: statusCodigo,
      count: precatorios.length,
      alerts: coluna.alerts || []  // Garantir que alerts existe
    };
  });

  // Referência para rastrear se a coluna foi montada/desmontada
  const colunaMontada = useRef(true);

  useEffect(() => {
    // Marcamos que a coluna está montada
    colunaMontada.current = true;

    return () => {
      // Se o componente for desmontado, marcamos
      colunaMontada.current = false;
    };
  }, []);

  // Atualizar dados da coluna quando mudar - considerando colunas dinâmicas
  useEffect(() => {
    if (!colunaMontada.current) return;

    try {
      // Agora o id da coluna é o UUID do status e status_id é o código
      const statusId = coluna.id;
      const statusCodigo = coluna.status_id;

      // Verificar se a coluna tem dados válidos
      if (!coluna) {
        console.warn('KanbanColuna: Dados da coluna inválidos');
        return;
      }

      // Verificar se precatorios é um array válido
      if (!Array.isArray(precatorios)) {
        console.warn('KanbanColuna: Lista de precatórios inválida');
        return;
      }

      setColunaLocal(prev => ({
        ...prev,
        ...coluna,
        id: statusId,
        status_id: statusCodigo,
        count: precatorios.length,
        alerts: coluna.alerts || prev.alerts || []
      }));

      // Log para debug da atualização da coluna
      console.log(`KanbanColuna ${coluna.name || coluna.nome} (${coluna.id}) atualizada:`, {
        qtdPrecatorios: precatorios.length
      });
    } catch (error) {
      console.error('Erro ao atualizar dados da coluna:', error);
    }
  }, [coluna, precatorios]);

  const { setNodeRef, isOver } = useDroppable({
    id: coluna.id,
    data: {
      type: "column",
      coluna,
    },
  });

  const getAlertColor = (tipo: string) => {
    switch (tipo) {
      case "atrasado":
        return "text-red-500";
      case "vencendo":
        return "text-amber-500";
      case "importante":
        return "text-blue-500";
      default:
        return "text-neutral-500";
    }
  };

  const getAlertIcon = (tipo: string) => {
    switch (tipo) {
      case "atrasado":
      case "vencendo":
        return <AlertTriangle className="w-4 h-4 mr-1" />;
      case "importante":
        return <AlertCircle className="w-4 h-4 mr-1" />;
      default:
        return null;
    }
  };

  const toggleCompacto = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    setCompacto(!compacto);
  }, [compacto]);

  return (
    <Card
      ref={setNodeRef}
      className={cn(
        "flex flex-col shrink-0 rounded-lg border transition-all duration-300 overflow-hidden",
        "shadow-md hover:shadow-lg",
        "bg-gradient-to-b from-card/95 to-card/80 backdrop-blur-sm",
        isOver ? "ring-2 ring-primary/60 ring-inset scale-[1.01] shadow-xl" : "",
        // Borda superior colorida em vez de lateral para um visual mais moderno
        `border-t-4 ${coluna.color ? `border-t-[${coluna.color}]` : coluna.cor ? `border-t-[${coluna.cor}]` : 'border-t-primary'}`,
        compacto ? "w-[50px]" : "w-[300px] md:w-[340px]", // Colunas um pouco mais largas
        "max-h-[calc(100vh-4rem)]"
      )}
    >
      <div className={cn(
        "flex items-center justify-between px-4 py-3",
        "border-b border-border/30 bg-gradient-to-r from-card/90 to-card/70",
        compacto && "border-b-0 justify-center p-2",
      )}>
        {/* Cabeçalho da coluna */}
        <div className="flex items-center gap-2">
          {/* Cor e nome da coluna */}
          <div className="flex items-center gap-2">
            <div
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: coluna.color || coluna.cor }}
            />

            {!compacto && (
              <h3 className="font-medium text-sm tracking-tight">
                {colunaLocal.name || colunaLocal.nome}
              </h3>
            )}
          </div>

          {/* Contador de precatórios */}
          {!compacto && (
            <Badge
              variant="secondary"
              className="text-xs font-normal px-1.5 h-5 bg-primary/10 text-primary"
            >
              {precatorios.length}
            </Badge>
          )}
        </div>

        {/* Botão de toggle compacto aprimorado */}
        <Button
          variant="ghost"
          size="sm"
          className={cn(
            "p-0 size-7 hover:bg-primary/10 hover:text-primary",
            compacto && "mt-1"
          )}
          onClick={toggleCompacto}
          title={compacto ? "Expandir coluna" : "Comprimir coluna"}
        >
          {compacto ? (
            <ChevronRight className="size-4" />
          ) : (
            <ChevronLeft className="size-4" />
          )}
        </Button>
      </div>

      {/* Conteúdo para modo compacto */}
      {compacto && (
        <div className="p-1 flex flex-col items-center gap-1">
          <div className="text-xs font-semibold tracking-tight text-center rotate-90 origin-center h-32 flex items-center">
            {colunaLocal.name || colunaLocal.nome}
          </div>

          <Badge className="mt-1 text-xs bg-primary/10 text-primary">
            {precatorios.length}
          </Badge>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="p-0 size-7 mt-1 hover:bg-primary/10 text-muted-foreground hover:text-primary"
                  onClick={(e) => {
                    e.stopPropagation();
                    onNovoPrecatorio();
                  }}
                >
                  <PlusCircle className="size-3.5" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="right">
                <p className="text-xs">Adicionar precatório</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      )}

      {/* Alertas e conteúdo apenas para o modo expandido */}
      {!compacto && (
        <>
          {colunaLocal.alerts && colunaLocal.alerts.length > 0 && (
            <div className="px-3 pt-1.5 pb-1 bg-muted/20">
              {colunaLocal.alerts.map((alerta, index) => (
                <div
                  key={index}
                  className={cn(
                    "flex items-center text-xs p-1.5 rounded-md mb-1.5",
                    "bg-card border text-xs",
                    getAlertColor(alerta.tipo)
                  )}
                >
                  {getAlertIcon(alerta.tipo)}
                  <span className="font-medium mr-1 text-[11px]">{alerta.tipo}:</span>
                  <span className="text-[11px] line-clamp-1">{alerta.mensagem}</span>
                </div>
              ))}
            </div>
          )}

          <ScrollArea
            className={cn(
              'flex-1 overflow-y-auto p-3 pt-2',
              'scrollbar-thin scrollbar-track-transparent scrollbar-thumb-neutral-200 dark:scrollbar-thumb-neutral-700',
              'hover:scrollbar-thumb-neutral-300 dark:hover:scrollbar-thumb-neutral-600',
              'transition-all duration-300',
              'relative max-h-[calc(100vh-180px)]',
              'rounded-md',
              '[&>*:not(:first-child)]:mt-3',
              isOver && 'bg-primary/5 shadow-inner'
            )}
          >
            <div className="space-y-3 py-1">
              {precatorios.map((precatorio, index) => (
                <div
                  key={precatorio.id}
                  className={cn(
                    "transition-all duration-300 w-full",
                    "hover:translate-y-[-2px]",
                    "motion-safe:animate-in motion-safe:fade-in-50 motion-safe:slide-in-from-left-3",
                    { "motion-safe:delay-100": index === 1 },
                    { "motion-safe:delay-150": index === 2 },
                    { "motion-safe:delay-200": index === 3 },
                  )}
                >
                  <PrecatorioCard
                    precatorio={precatorio}
                    onVerDetalhes={onVerDetalhes}
                    onEdit={onEdit}
                    onDelete={onDelete}
                  />
                </div>
              ))}
              {/* Espaço vazio quando não há precatórios */}
              {precatorios.length === 0 && (
                <div className="w-full h-10 flex items-center justify-center text-muted-foreground text-xs bg-muted/10 rounded-sm">
                  Sem precatórios
                </div>
              )}
            </div>
          </ScrollArea>

          <CardFooter
            className={cn(
              "flex justify-between items-center p-2 pt-2 border-t border-border/30 bg-card/90",
              "mt-auto"
            )}
          >
            <div className="flex items-center gap-2">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Badge
                      variant="outline"
                      className={cn(
                        "px-2 py-0 h-6",
                        "flex items-center gap-1 text-xs font-normal"
                      )}
                    >
                      <span className="w-2 h-2 rounded-full" style={{ backgroundColor: coluna.color || coluna.cor }}></span>
                      <span className="text-[10px] truncate max-w-[60px]" title={coluna.status_id}>{coluna.status_id}</span>
                    </Badge>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="text-xs">Status ID: {coluna.status_id}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              {precatorios.length > 0 && (
                <span className="text-[10px] text-muted-foreground">
                  {precatorios.length} {precatorios.length === 1 ? 'item' : 'itens'}
                </span>
              )}
            </div>

            {/* Espaço para informações adicionais da coluna */}
            <span className="text-[10px] text-muted-foreground opacity-70 flex items-center">
              <Clock className="h-3 w-3 mr-1" />
              Atualizado
            </span>
          </CardFooter>
        </>
      )}
    </Card>
  );
}

// Wrap with React.memo for performance optimization
export const KanbanColuna = memo(KanbanColunaComponent);