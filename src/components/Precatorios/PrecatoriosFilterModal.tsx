import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { CalendarIcon, Filter, X } from "lucide-react";
import { cn } from "@/lib/utils";

export interface PrecatoriosFiltros {
  status: string[];
  tribunal: string[];
  valorMin?: number;
  valorMax?: number;
  dataInicio?: Date;
  dataFim?: Date;
}

interface PrecatoriosFilterModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  filtros: PrecatoriosFiltros;
  onAplicarFiltros: (filtros: PrecatoriosFiltros) => void;
  onLimparFiltros: () => void;
  estatisticas: {
    total: number;
    valorTotal: number;
    emAndamento: number;
    concluidos: number;
    mediaPrazo: number;
    statusCount: Record<string, number>;
    tribunalCount: Record<string, number>;
  };
  tipoVisualizacao: 'PRECATORIO' | 'RPV';
}

export function PrecatoriosFilterModal({
  isOpen,
  onOpenChange,
  filtros,
  onAplicarFiltros,
  onLimparFiltros,
  estatisticas,
  tipoVisualizacao,
}: PrecatoriosFilterModalProps) {
  const [filtrosTemp, setFiltrosTemp] = useState<PrecatoriosFiltros>({ ...filtros });

  // Reset filtros temporários quando o modal abrir
  useEffect(() => {
    setFiltrosTemp({ ...filtros });
  }, [filtros, isOpen]);

  const handleStatusChange = (status: string, checked: boolean) => {
    if (checked) {
      setFiltrosTemp((prev) => ({
        ...prev,
        status: [...prev.status, status],
      }));
    } else {
      setFiltrosTemp((prev) => ({
        ...prev,
        status: prev.status.filter((s) => s !== status),
      }));
    }
  };

  const handleTribunalChange = (tribunal: string, checked: boolean) => {
    if (checked) {
      setFiltrosTemp((prev) => ({
        ...prev,
        tribunal: [...prev.tribunal, tribunal],
      }));
    } else {
      setFiltrosTemp((prev) => ({
        ...prev,
        tribunal: prev.tribunal.filter((t) => t !== tribunal),
      }));
    }
  };

  const handleInputChange = (campo: keyof PrecatoriosFiltros, valor: any) => {
    setFiltrosTemp((prev) => ({
      ...prev,
      [campo]: valor,
    }));
  };

  const handleResetarFiltros = () => {
    const filtrosVazios: PrecatoriosFiltros = {
      status: [],
      tribunal: [],
      valorMin: undefined,
      valorMax: undefined,
      dataInicio: undefined,
      dataFim: undefined,
    };
    setFiltrosTemp(filtrosVazios);
    onLimparFiltros();
  };

  const handleAplicarFiltros = () => {
    onAplicarFiltros(filtrosTemp);
    onOpenChange(false);
  };

  // Obter lista de status disponíveis
  const statusDisponiveis = Object.keys(estatisticas.statusCount || {}).sort();

  // Obter lista de tribunais disponíveis
  const tribunaisDisponiveis = Object.keys(estatisticas.tribunalCount || {}).sort();

  // Função para formatar o status para exibição
  const formatarStatus = (status: string) => {
    const statusMap: Record<string, string> = {
      'NOVO': 'Novo',
      'EM_ANALISE': 'Em Análise',
      'APROVADO': 'Aprovado',
      'REJEITADO': 'Rejeitado',
      'EM_PROCESSAMENTO': 'Em Processamento',
      'CONCLUIDO': 'Concluído',
      'CANCELADO': 'Cancelado',
      'analise': 'Análise',
      'proposta_tmj': 'Proposta TMJ',
      'proposta_btg': 'Proposta BTG',
      'negociacao': 'Negociação',
      'documentacao': 'Documentação',
      'pagamento': 'Pagamento',
      'concluido': 'Concluído',
      'cancelado': 'Cancelado',
    };

    return statusMap[status] || status;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filtros Avançados
          </DialogTitle>
          <DialogDescription>
            Filtre os {tipoVisualizacao === 'PRECATORIO' ? 'precatórios' : 'RPVs'} por diferentes critérios para encontrar exatamente o que precisa.
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-6 py-4">
          {/* Status */}
          <div className="space-y-2">
            <h3 className="text-sm font-medium">Status</h3>
            <div className="grid grid-cols-2 gap-2">
              {statusDisponiveis.map(status => (
                <div key={status} className="flex items-center space-x-2">
                  <Checkbox
                    id={`status-${status}`}
                    checked={filtrosTemp.status.includes(status)}
                    onCheckedChange={(checked) => handleStatusChange(status, !!checked)}
                  />
                  <Label htmlFor={`status-${status}`} className="flex items-center justify-between flex-1">
                    <span>{formatarStatus(status)}</span>
                    <Badge variant="outline">{estatisticas.statusCount[status] || 0}</Badge>
                  </Label>
                </div>
              ))}
            </div>
          </div>

          <Separator />

          {/* Tribunal */}
          <div className="space-y-2">
            <h3 className="text-sm font-medium">Tribunal</h3>
            <div className="grid grid-cols-2 gap-2">
              {tribunaisDisponiveis.map(tribunal => (
                <div key={tribunal} className="flex items-center space-x-2">
                  <Checkbox
                    id={`tribunal-${tribunal}`}
                    checked={filtrosTemp.tribunal.includes(tribunal)}
                    onCheckedChange={(checked) => handleTribunalChange(tribunal, !!checked)}
                  />
                  <Label htmlFor={`tribunal-${tribunal}`} className="flex items-center justify-between flex-1">
                    <span>{tribunal}</span>
                    <Badge variant="outline">{estatisticas.tribunalCount[tribunal] || 0}</Badge>
                  </Label>
                </div>
              ))}
            </div>
          </div>

          <Separator />

          {/* Valor */}
          <div className="space-y-2">
            <h3 className="text-sm font-medium">Valor</h3>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="valor-min">Valor Mínimo</Label>
                <Input
                  id="valor-min"
                  type="number"
                  placeholder="R$ 0,00"
                  value={filtrosTemp.valorMin || ""}
                  onChange={(e) => handleInputChange("valorMin", e.target.value ? Number(e.target.value) : undefined)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="valor-max">Valor Máximo</Label>
                <Input
                  id="valor-max"
                  type="number"
                  placeholder="R$ 1.000.000,00"
                  value={filtrosTemp.valorMax || ""}
                  onChange={(e) => handleInputChange("valorMax", e.target.value ? Number(e.target.value) : undefined)}
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* Data de Cadastro */}
          <div className="space-y-2">
            <h3 className="text-sm font-medium">Data de Cadastro</h3>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="data-inicio">Data Inicial</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      id="data-inicio"
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !filtrosTemp.dataInicio && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {filtrosTemp.dataInicio ? (
                        format(filtrosTemp.dataInicio, "dd/MM/yyyy", { locale: ptBR })
                      ) : (
                        <span>Selecione uma data</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={filtrosTemp.dataInicio}
                      onSelect={(date) => handleInputChange("dataInicio", date)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
              <div className="space-y-2">
                <Label htmlFor="data-fim">Data Final</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      id="data-fim"
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !filtrosTemp.dataFim && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {filtrosTemp.dataFim ? (
                        format(filtrosTemp.dataFim, "dd/MM/yyyy", { locale: ptBR })
                      ) : (
                        <span>Selecione uma data</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={filtrosTemp.dataFim}
                      onSelect={(date) => handleInputChange("dataFim", date)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter className="flex justify-between">
          <Button variant="outline" onClick={handleResetarFiltros} className="gap-1">
            <X className="h-4 w-4" />
            Limpar Filtros
          </Button>
          <Button onClick={handleAplicarFiltros} className="gap-1">
            <Filter className="h-4 w-4" />
            Aplicar Filtros
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
