import { useState, useEffect, useMemo, useRef, useCallback, memo } from "react";
import { KanbanColuna } from "./KanbanColuna";
import { DetalhesModal } from "./DetalhesModal";

import { SharedPrecatorioForm } from "./SharedPrecatorioForm";
import { EstatisticasModal } from "./EstatisticasModal";
import { FiltrosModal } from "./FiltrosModal";
import { Precatorio, KanbanColuna as KanbanColunaType, FiltrosAvancados, DashboardStats, CORES_STATUS, CustomView } from "./types";
import { useDebounce } from "@/hooks/useDebounce";
import { kanbanLogger } from "@/lib/logger";
import {
  DndContext,
  DragOverlay,
  useSensors,
  useSensor,
  PointerSensor,
  KeyboardSensor,
  defaultDropAnimationSideEffects,
  DragStartEvent,
  DragEndEvent,
  DragOverEvent,
  MeasuringStrategy,
  DropAnimation,
  Modifier,
  closestCorners
} from "@dnd-kit/core";
import { sortableKeyboardCoordinates } from '@dnd-kit/sortable';
import { restrictToWindowEdges } from "@dnd-kit/modifiers";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { PrecatorioCard } from "./PrecatorioCard";
import {
  FilterX,
  Plus,
  Filter,
  BarChart2,
  Search,
  X,
  SlidersHorizontal,
  MoreVertical,
  PlusCircle
} from "lucide-react";
import { Input } from "@/components/ui/input";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { cn } from "@/lib/utils";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { toast } from "sonner";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from "@/components/ui/dropdown-menu";
import { Card } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { NoPrecatoriosMessage } from "@/components/ui/no-results-message";
import { useAuth } from "@/hooks/useAuth";
import { useKanbanPermissions } from "@/hooks/useKanbanPermissions";
import { KanbanPermissionGuard, KanbanColumnPermissionGuard, KanbanFilterPermissionGuard } from "@/components/permissions/KanbanPermissionGuard";

interface KanbanContainerProps {
  todosPrecatorios: Precatorio[];
  colunas: KanbanColunaType[];
  onSavePrecatorio: (precatorio: Precatorio) => void;
  onDeletePrecatorio: (id: string) => void;
  onMovePrecatorio: (precatorioId: string, novoStatus: string) => void;
}

// Configuração para a animação de drop
const dropAnimation: DropAnimation = {
  sideEffects: defaultDropAnimationSideEffects({
    styles: {
      active: {
        opacity: '0.3',
      },
    }
  }),
  duration: 150,
  easing: 'ease-out',
};

const modifiers: Modifier[] = [restrictToWindowEdges];

function KanbanContainerComponent({
  todosPrecatorios,
  colunas,
  onSavePrecatorio,
  onDeletePrecatorio,
  onMovePrecatorio,
}: KanbanContainerProps) {
  // Hooks para autenticação e permissões
  const { user } = useAuth();
  const {
    canViewColumn,
    canEditColumn,
    canMovePrecatorioToColumn,
    canViewPrecatorio,
    canEditPrecatorio,
    canDeletePrecatorio,
    canCreatePrecatorio,
    filterVisibleColumns,
    filterVisiblePrecatorios
  } = useKanbanPermissions();

  // Estados para os modais
  const [precatorioSelecionado, setPrecatorioSelecionado] = useState<Precatorio | null>(null);
  const [isDetalhesModalOpen, setIsDetalhesModalOpen] = useState(false);
  const [isFormModalOpen, setIsFormModalOpen] = useState(false);
  const [isFiltrosModalOpen, setIsFiltrosModalOpen] = useState(false);
  const [isEstatisticasModalOpen, setIsEstatisticasModalOpen] = useState(false);
  const [isConfirmDeleteOpen, setIsConfirmDeleteOpen] = useState(false);
  const [precatorioIdToDelete, setPrecatorioIdToDelete] = useState<string | null>(null);
  const [modoEdicao, setModoEdicao] = useState(false);

  // Remover estado de admin para depuração - não é mais necessário

  // Estado para indicar carregamento
  const [loading, setLoading] = useState(false);

  // Estado para filtros e busca
  const [filtrosAvancados, setFiltrosAvancados] = useState<FiltrosAvancados>({
    prioridade: "",
    tribunal: "",
    natureza: "",
    valorMin: undefined,
    valorMax: undefined,
    dataVencimentoInicio: undefined,
    dataVencimentoFim: undefined,
    tags: [],
    responsavel: "",
  });
  const [searchTerm, setSearchTerm] = useState("");
  const debouncedSearchTerm = useDebounce(searchTerm, 300); // 300ms debounce for better performance
  const [filteredPrecatorios, setFilteredPrecatorios] = useState<Precatorio[]>(todosPrecatorios);

  // Estado para drag and drop
  const [activePrecatorio, setActivePrecatorio] = useState<Precatorio | null>(null);

  // Sensores para drag and drop - otimizados para melhor desempenho e experiência
  const sensors = useSensors(
    useSensor(PointerSensor, {
      // Configuração otimizada para melhor detecção de arrasto
      activationConstraint: {
        distance: 3, // Pequena distância para evitar cliques acidentais
        delay: 0, // Sem delay para iniciar o arrasto
        tolerance: 5, // Tolerância para movimentos acidentais
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  );

  const containerRef = useRef<HTMLDivElement>(null);

  // Estados para scroll e responsividade
  const [isDraggingScroll, setIsDraggingScroll] = useState(false);
  const [scrollStartX, setScrollStartX] = useState(0);
  const [scrollStartScrollLeft, setScrollStartScrollLeft] = useState(0);
  const [isMobileView, setIsMobileView] = useState(false);



  // Detectar se está em dispositivo móvel
  useEffect(() => {
    const checkMobileView = () => {
      setIsMobileView(window.innerWidth < 768);
    };

    checkMobileView();
    window.addEventListener('resize', checkMobileView);

    return () => {
      window.removeEventListener('resize', checkMobileView);
    };
  }, []);

  // Adicionar listeners para eventos personalizados
  useEffect(() => {
    // Evento para abrir o modal de novo precatório
    const handleOpenNewPrecatorioModal = () => {
      setPrecatorioSelecionado(null);
      setModoEdicao(false);
      setIsFormModalOpen(true);
    };

    // Evento para abrir/fechar o modal de filtros
    const handleToggleFiltrosModal = () => {
      setIsFiltrosModalOpen(prev => !prev);
    };

    document.addEventListener('open-new-precatorio-modal', handleOpenNewPrecatorioModal);
    document.addEventListener('toggle-filtros-modal', handleToggleFiltrosModal);

    return () => {
      document.removeEventListener('open-new-precatorio-modal', handleOpenNewPrecatorioModal);
      document.removeEventListener('toggle-filtros-modal', handleToggleFiltrosModal);
    };
  }, []);

  // Funções para arrastar o scroll lateral
  const handleMouseDownOnContainer = (e: React.MouseEvent<HTMLDivElement>) => {
    const target = e.target as HTMLElement;
    let currentElement: HTMLElement | null = target;

    while (currentElement) {
      if (currentElement.getAttribute('data-draggable') === 'card' ||
          currentElement.classList.contains('precatorio-card')) {
        return;
      }
      currentElement = currentElement.parentElement;
    }

    if (target.classList.contains('kanban-scroll-area') ||
        target.classList.contains('flex') ||
        target.classList.contains('p-3') ||
        target.classList.contains('gap-4')) {
      setIsDraggingScroll(true);
      setScrollStartX(e.clientX);
      setScrollStartScrollLeft(containerRef.current?.scrollLeft || 0);
      document.body.classList.add('scrolling');
    }
  };

  const handleMouseMoveOnContainer = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!isDraggingScroll) return;
    const dx = e.clientX - scrollStartX;
    if (containerRef.current) {
      containerRef.current.scrollLeft = scrollStartScrollLeft - dx;
    }
  };

  const handleMouseUpOnContainer = () => {
    setIsDraggingScroll(false);
    document.body.classList.remove('scrolling');
  };

  useEffect(() => {
    const handleGlobalMouseUp = () => {
      setIsDraggingScroll(false);
      document.body.classList.remove('scrolling');
    };

    document.addEventListener('mouseup', handleGlobalMouseUp);
    return () => {
      document.removeEventListener('mouseup', handleGlobalMouseUp);
    };
  }, []);

  // Estado para controlar erros de autenticação
  const [authError, setAuthError] = useState(false);

  // Verificar se há erros de autenticação - simplificado para evitar loops
  useEffect(() => {
    // Verificar status de autenticação apenas se não houver precatórios
    if (todosPrecatorios.length === 0 && colunas.length === 0) {
      console.log('[KanbanContainer] Sem dados, possível problema de autenticação');

      // Verificar se há marcadores de erro de autenticação no DOM
      if (document.querySelectorAll('*[data-auth-error="true"]').length > 0) {
        console.log('[KanbanContainer] Erro de autenticação detectado via DOM');
        setAuthError(true);
        return;
      }

      // Não fazer verificações adicionais para evitar loops de carregamento
    } else {
      // Precatórios ou colunas recebidos com sucesso
      setAuthError(false);
    }
  }, [todosPrecatorios, colunas]);

  // Filtrar precatórios com base em permissões, busca e filtros
  useEffect(() => {
    // Garantir que todosPrecatorios é sempre um array
    const precatoriosArray = Array.isArray(todosPrecatorios) ? todosPrecatorios : [];

    // Verificar se temos colunas
    if (colunas.length === 0) {
      // Nenhuma coluna disponível
      console.log('[KanbanContainer] Nenhuma coluna disponível');
    }

    // Manter o estado anterior se não houver precatórios ou se o array for muito pequeno
    // (isso evita que os precatórios desapareçam durante reconexões ou recarregamentos parciais)
    if ((precatoriosArray.length === 0 || precatoriosArray.length < filteredPrecatorios.length / 2) &&
        filteredPrecatorios.length > 0) {
      console.log(`[KanbanContainer] Mantendo precatórios anteriores (${filteredPrecatorios.length}) para evitar que desapareçam. Novos: ${precatoriosArray.length}`);
      return;
    }

    // Primeiro, filtrar por permissões - para admin, mostrar todos os precatórios
    let permissionFilteredPrecatorios = precatoriosArray;
    try {
      permissionFilteredPrecatorios = filterVisiblePrecatorios(precatoriosArray);
    } catch (error) {
      console.error('[KanbanContainer] Erro ao filtrar precatórios por permissões:', error);
      // Em caso de erro, manter todos os precatórios para evitar tela vazia
      permissionFilteredPrecatorios = precatoriosArray;
    }

    // CORREÇÃO: Verificar se os precatórios têm status_id definido
    // Se não tiverem, atribuir um status_id baseado no status
    const precatoriosComStatusId = permissionFilteredPrecatorios.map(precatorio => {
      // Verificar se o precatório já tem um status_id válido
      if (precatorio.status_id) {
        // Verificar se o status_id corresponde a alguma coluna existente
        const colunaExistente = colunas.find(c => c.id === precatorio.status_id);
        if (colunaExistente) {
          // Status_id válido, manter como está
          return precatorio;
        }
      }

      // Se chegou aqui, o precatório não tem status_id ou tem um status_id inválido
      // Tentar encontrar uma coluna correspondente pelo status
      if (precatorio.status) {
        const statusNormalizado = String(precatorio.status).toLowerCase()
          .normalize('NFD').replace(/[\u0300-\u036f]/g, '')
          .replace(/\s+/g, '_');

        console.log(`⚠️ Precatório ${precatorio.id} precisa de status_id. Status atual: ${precatorio.status}`);

        // Buscar correspondência por status
        const colunaCorrespondente = colunas.find(c =>
          (c.status_id && c.status_id.toLowerCase() === precatorio.status?.toLowerCase()) ||
          (c.codigo && c.codigo.toLowerCase() === precatorio.status?.toLowerCase()) ||
          (c.nome && c.nome.toLowerCase() === precatorio.status?.toLowerCase())
        );

        // Se encontrou uma coluna correspondente, usar o ID dela como status_id
        if (colunaCorrespondente) {
          console.log(`✅ Encontrada coluna correspondente: ${colunaCorrespondente.nome || colunaCorrespondente.name}`);

          // Atualizar no banco de dados em segundo plano
          import('@/services/precatoriosServiceSimples').then(({ atualizarStatusPrecatorio }) => {
            atualizarStatusPrecatorio(precatorio.id, colunaCorrespondente.id)
              .catch(err => console.error(`Erro ao atualizar status_id do precatório ${precatorio.id}:`, err));
          });

          return {
            ...precatorio,
            status_id: colunaCorrespondente.id
          };
        }

        // Se não encontrou correspondência e temos colunas, usar a primeira coluna
        if (colunas.length > 0) {
          const primeiraColuna = colunas[0];
          console.log(`⚠️ Usando primeira coluna (${primeiraColuna.nome || primeiraColuna.name}) para precatório ${precatorio.id}`);

          // Atualizar no banco de dados em segundo plano
          import('@/services/precatoriosServiceSimples').then(({ atualizarStatusPrecatorio }) => {
            atualizarStatusPrecatorio(precatorio.id, primeiraColuna.id)
              .catch(err => console.error(`Erro ao atualizar status_id do precatório ${precatorio.id}:`, err));
          });

          return {
            ...precatorio,
            status_id: primeiraColuna.id,
            status: primeiraColuna.status_id || primeiraColuna.codigo || 'novo'
          };
        }

        // Fallback: usar o status normalizado como status_id
        return {
          ...precatorio,
          status_id: statusNormalizado
        };
      }

      // Se não tem status, usar a primeira coluna se disponível
      if (colunas.length > 0) {
        const primeiraColuna = colunas[0];
        console.log(`⚠️ Precatório ${precatorio.id} sem status. Usando primeira coluna.`);

        return {
          ...precatorio,
          status_id: primeiraColuna.id,
          status: primeiraColuna.status_id || primeiraColuna.codigo || 'novo'
        };
      }

      // Último caso: manter como está
      return precatorio;
    });

    // Usar o array com status_id atribuídos
    let filtered = [...precatoriosComStatusId];

    // Aplicar filtro de busca textual com debounce
    if (debouncedSearchTerm.trim()) {
      const searchLower = debouncedSearchTerm.toLowerCase();
      filtered = filtered.filter(p =>
        (p.numero_precatorio?.toLowerCase() || p.numero?.toLowerCase() || '').includes(searchLower) ||
        (p.natureza?.toLowerCase() || '').includes(searchLower) ||
        (p.observacoes?.toLowerCase() || '').includes(searchLower) ||
        (p.cliente?.nome?.toLowerCase() || '').includes(searchLower) ||
        (p.tribunal?.toLowerCase() || '').includes(searchLower) ||
        (p.responsavel?.nome?.toLowerCase() || '').includes(searchLower)
      );
    }

    // Aplicar filtros avançados
    if (filtrosAvancados.prioridade) {
      filtered = filtered.filter(p => p.prioridade === filtrosAvancados.prioridade);
    }

    if (filtrosAvancados.tribunal) {
      filtered = filtered.filter(p => p.tribunal === filtrosAvancados.tribunal);
    }

    if (filtrosAvancados.natureza) {
      filtered = filtered.filter(p => p.natureza === filtrosAvancados.natureza);
    }

    if (filtrosAvancados.valorMin !== undefined) {
      filtered = filtered.filter(p => p.valor >= filtrosAvancados.valorMin);
    }

    if (filtrosAvancados.valorMax !== undefined) {
      filtered = filtered.filter(p => p.valor <= filtrosAvancados.valorMax);
    }

    if (filtrosAvancados.responsavel) {
      filtered = filtered.filter(p => p.responsavel?.nome === filtrosAvancados.responsavel);
    }

    if (filtrosAvancados.tags.length > 0) {
      filtered = filtered.filter(p =>
        filtrosAvancados.tags.some(tag => p.tags?.includes(tag))
      );
    }

    setFilteredPrecatorios(filtered);
  }, [todosPrecatorios, debouncedSearchTerm, filtrosAvancados]);

  // Calcular estatísticas
  const dashboardStats = useMemo(() => {
    // Garantir que todosPrecatorios é sempre um array
    const precatoriosArray = Array.isArray(todosPrecatorios) ? todosPrecatorios : [];

    const totalPrecatorios = precatoriosArray.length;
    const concluidos = precatoriosArray.filter(p => p.status === "concluido").length;

    const stats: DashboardStats = {
      total: totalPrecatorios,
      valorTotal: precatoriosArray.reduce((sum, p) => sum + (p.valor || 0), 0),
      emAndamento: precatoriosArray.filter(p => p.status !== "concluido" && p.status !== "cancelado").length,
      concluidos: concluidos,
      taxaConclusao: totalPrecatorios > 0 ? (concluidos / totalPrecatorios) * 100 : 0,
      porStatus: [],
      porPrioridade: [],
      valoresPorTribunal: [],
    };

    // Agrupar por status
    const statusMap = new Map<string, number>();
    precatoriosArray.forEach(p => {
      statusMap.set(p.status, (statusMap.get(p.status) || 0) + 1);
    });

    stats.porStatus = Array.from(statusMap.entries()).map(([status, valor]) => ({
      status,
      valor,
      cor: CORES_STATUS[status as keyof typeof CORES_STATUS] || "gray",
    }));

    // Agrupar por prioridade
    const prioridadeMap = new Map<string, number>();
    precatoriosArray.forEach(p => {
      prioridadeMap.set(p.prioridade, (prioridadeMap.get(p.prioridade) || 0) + 1);
    });

    stats.porPrioridade = Array.from(prioridadeMap.entries()).map(([prioridade, valor]) => ({
      prioridade,
      valor,
    }));

    // Agrupar valores por tribunal
    const tribunalMap = new Map<string, number>();
    precatoriosArray.forEach(p => {
      tribunalMap.set(p.tribunal, (tribunalMap.get(p.tribunal) || 0) + (p.valor || 0));
    });

    stats.valoresPorTribunal = Array.from(tribunalMap.entries()).map(([tribunal, valor]) => ({
      tribunal,
      valor,
    }));

    return stats;
  }, [todosPrecatorios]);

  // Ações dos botões e handlers - memoizados para melhor performance
  const handleVerDetalhes = useCallback((precatorio: Precatorio) => {
    setPrecatorioSelecionado(precatorio);
    setIsDetalhesModalOpen(true);
  }, []);

  const handleEditarPrecatorio = useCallback((precatorio: Precatorio) => {
    setPrecatorioSelecionado(precatorio);
    setModoEdicao(true);
    setIsFormModalOpen(true);
  }, []);

  const handleNovoPrecatorio = useCallback(() => {
    setPrecatorioSelecionado(null);
    setModoEdicao(false);
    setIsFormModalOpen(true);
  }, []);

  const handleDeletePrecatorio = useCallback((id: string) => {
    setPrecatorioIdToDelete(id);
    setIsConfirmDeleteOpen(true);
  }, []);

  const confirmDeletePrecatorio = useCallback(() => {
    if (precatorioIdToDelete) {
      onDeletePrecatorio(precatorioIdToDelete);
      setIsConfirmDeleteOpen(false);
      setPrecatorioIdToDelete(null);
    }
  }, [precatorioIdToDelete, onDeletePrecatorio]);

  // Função de arrastar para iniciar o drag - otimizada para melhor feedback visual
  const handleDragStart = useCallback((event: DragStartEvent) => {
    const { active } = event;
    const precatorio = active.data.current?.precatorio;

    if (precatorio) {
      console.log(`Iniciando arrasto do precatório ${precatorio.id}`);

      // Verificar permissões para mover o precatório
      const colunaAtual = colunas.find(c => c.id === precatorio.status_id);
      if (colunaAtual && !canMovePrecatorioToColumn(colunaAtual.id, precatorio.id)) {
        // Se não tiver permissão, cancelar o arrasto
        console.log(`Sem permissão para mover o precatório ${precatorio.id} da coluna ${colunaAtual.nome}`);
        toast.error("Você não tem permissão para mover este precatório", {
          duration: 2000,
          position: 'bottom-right',
          className: 'kanban-toast'
        });
        return;
      }

      setActivePrecatorio(precatorio);

      // Adicionar classes para feedback visual
      document.body.classList.add('dragging');
      document.body.style.cursor = 'grabbing';

      // Desativar o arrasto do scroll lateral quando estiver arrastando um card
      setIsDraggingScroll(false);
      document.body.classList.remove('scrolling');

      // Adicionar classe de destaque ao card original para melhor feedback visual
      const cardElement = document.querySelector(`[data-precatorio-id="${precatorio.id}"]`);
      if (cardElement) {
        cardElement.classList.add('card-dragging');
      }

      // Adicionar efeito de vibração sutil para feedback tátil em dispositivos móveis
      if ('vibrate' in navigator) {
        try {
          navigator.vibrate(50); // Vibração sutil de 50ms
        } catch (e) {
          // Ignorar erros de vibração
        }
      }
    }
  }, []);

  // Função para quando o item é movido sobre uma coluna - com feedback visual aprimorado
  const handleDragOver = useCallback((event: DragOverEvent) => {
    const { over } = event;
    if (!over) return;

    // Adicionar efeito visual na coluna alvo
    const targetColumn = document.querySelector(`[data-column-id="${over.id}"]`);
    if (targetColumn) {
      // Remover destaque de todas as colunas
      document.querySelectorAll('[data-column-id]').forEach(col => {
        col.classList.remove('column-highlight');
      });

      // Adicionar destaque à coluna alvo
      targetColumn.classList.add('column-highlight');
    }

    // Auto-scroll horizontal quando próximo às bordas - com velocidade adaptativa
    const containerElement = containerRef.current;
    if (containerElement) {
      const containerRect = containerElement.getBoundingClientRect();
      const mouseX = (event.activatorEvent as MouseEvent)?.clientX ||
                    (event.activatorEvent as TouchEvent)?.touches?.[0]?.clientX || 0;

      const distanceFromLeft = mouseX - containerRect.left;
      const distanceFromRight = containerRect.right - mouseX;

      // Zona de auto-scroll mais ampla (120px) e velocidade adaptativa
      const scrollThreshold = 120;

      if (distanceFromLeft < scrollThreshold) {
        // Velocidade adaptativa - mais rápido quanto mais próximo da borda
        const scrollSpeed = Math.max(5, 20 * (1 - distanceFromLeft / scrollThreshold));
        containerElement.scrollLeft -= scrollSpeed;
      }

      if (distanceFromRight < scrollThreshold) {
        // Velocidade adaptativa - mais rápido quanto mais próximo da borda
        const scrollSpeed = Math.max(5, 20 * (1 - distanceFromRight / scrollThreshold));
        containerElement.scrollLeft += scrollSpeed;
      }
    }
  }, []);

  // Função para quando o arrasto termina - com animação e feedback aprimorados
  const handleDragEnd = useCallback((event: DragEndEvent) => {
    const { active, over } = event;

    // Remover destaque de todas as colunas
    document.querySelectorAll('[data-column-id]').forEach(col => {
      col.classList.remove('column-highlight');
    });

    // Remover classe de destaque de todos os cards
    document.querySelectorAll('.card-dragging').forEach(card => {
      card.classList.remove('card-dragging');
    });

    if (over && active.id !== over.id) {
      const precatorioId = active.id as string;
      const novoStatus = over.id as string;

      if (precatorioId && novoStatus) {
        // Encontrar a coluna de destino
        const colunaDestino = colunas.find(c => c.id === novoStatus);
        if (colunaDestino) {
          // Verificar permissões para mover para a coluna de destino
          if (!canMovePrecatorioToColumn(colunaDestino.id, precatorioId)) {
            console.log(`Sem permissão para mover o precatório ${precatorioId} para a coluna ${colunaDestino.nome}`);
            toast.error("Você não tem permissão para mover precatórios para esta coluna", {
              duration: 2000,
              position: 'bottom-right',
              className: 'kanban-toast'
            });
            return;
          }

          // Agora o id da coluna é o UUID do status
          const novoStatusId = colunaDestino.id;
          const novoStatusCodigo = colunaDestino.status_id;

          console.log(`Finalizando movimento do precatório ${precatorioId} para coluna ${colunaDestino.nome} (${novoStatus})`);

          // Atualizar o estado local imediatamente para feedback visual instantâneo
          setFilteredPrecatorios(prev =>
            prev.map(p =>
              p.id === precatorioId
                ? {
                    ...p,
                    status: novoStatusCodigo,
                    status_id: novoStatusId
                  }
                : p
            )
          );

          // Adicionar efeito visual de sucesso
          toast.success(`Precatório movido para ${colunaDestino.nome || colunaDestino.name}`, {
            duration: 2000,
            position: 'bottom-right',
            className: 'kanban-toast'
          });

          // Adicionar efeito de vibração para feedback tátil em dispositivos móveis
          if ('vibrate' in navigator) {
            try {
              navigator.vibrate([50, 50, 50]); // Padrão de vibração para sucesso
            } catch (e) {
              // Ignorar erros de vibração
            }
          }
        }

        // Chamar o callback para atualizar o status no banco de dados
        onMovePrecatorio(precatorioId, novoStatus);
      }
    }

    setActivePrecatorio(null);
    document.body.classList.remove('dragging');
    document.body.style.cursor = '';
  }, [onMovePrecatorio, colunas, toast]);

  // Limpar todos os filtros - memoizado para melhor performance
  const handleLimparFiltros = useCallback(() => {
    setFiltrosAvancados({
      prioridade: "",
      tribunal: "",
      natureza: "",
      valorMin: undefined,
      valorMax: undefined,
      dataVencimentoInicio: undefined,
      dataVencimentoFim: undefined,
      tags: [],
      responsavel: "",
    });
    setSearchTerm("");
  }, []);

  // Verificar se há filtros ativos
  const temFiltrosAtivos = useMemo(() => {
    return searchTerm.trim() !== "" ||
    Object.values(filtrosAvancados).some(v =>
      v !== "" && v !== undefined && (Array.isArray(v) ? v.length > 0 : true)
    );
  }, [searchTerm, filtrosAvancados]);

  return (
    <>
      {/* Área superior do Kanban */}
      <div className="z-20 w-full bg-background pt-2 flex items-center justify-end gap-2 px-4">
        <Button
          variant="outline"
          size="sm"
          className="h-8"
          onClick={() => setIsEstatisticasModalOpen(true)}
        >
          <BarChart2 className="h-3.5 w-3.5 mr-1.5" />
          Estatísticas
        </Button>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              className="h-8"
            >
              <MoreVertical className="h-3.5 w-3.5 mr-1.5" />
              Ações
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => setIsEstatisticasModalOpen(true)}>
              <BarChart2 className="mr-2 h-4 w-4" />
              <span>Estatísticas</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Campo de pesquisa */}
      <div className="flex items-center gap-3 mt-2 px-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Buscar por número, cliente, tribunal..."
            className="pl-9 pr-9 border-muted-foreground/20 bg-background shadow-sm"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          {searchTerm && (
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-1 top-1 h-8 w-8"
              onClick={() => setSearchTerm("")}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>

        <Button
          variant="outline"
          size="sm"
          className="h-8"
          onClick={() => setIsFiltrosModalOpen(true)}
        >
          <SlidersHorizontal className="h-3.5 w-3.5 mr-1.5" />
          Filtros Avançados
          {temFiltrosAtivos && (
            <Badge variant="secondary" className="ml-2 bg-primary/10 text-primary">
              {Object.values(filtrosAvancados).filter(v =>
                v !== "" && v !== undefined && (Array.isArray(v) ? v.length > 0 : true)
              ).length}
            </Badge>
          )}
        </Button>

        {temFiltrosAtivos && (
          <Button
            variant="ghost"
            size="sm"
            className="h-9 text-destructive hover:text-destructive hover:bg-destructive/10 whitespace-nowrap"
            onClick={handleLimparFiltros}
          >
            <FilterX className="h-3.5 w-3.5 mr-1.5" />
            Limpar Filtros
          </Button>
        )}
      </div>

      {/* Contador de resultados - só exibir quando houver precatórios */}
      {filteredPrecatorios.length > 0 && (
        <div className="text-xs text-muted-foreground">
          Mostrando <span className="font-medium text-foreground">{filteredPrecatorios.length}</span> de <span className="font-medium text-foreground">{Array.isArray(todosPrecatorios) ? todosPrecatorios.length : 0}</span> precatórios
        </div>
      )}

      {/* Container principal do Kanban com DndContext - Agora é a área de scroll principal */}
      <DndContext
        sensors={sensors}
        collisionDetection={closestCorners}
        onDragStart={handleDragStart}
        onDragOver={handleDragOver}
        onDragEnd={handleDragEnd}
        measuring={{
          droppable: {
            strategy: MeasuringStrategy.Always,
          },
        }}
        modifiers={modifiers}
      >
        {/* Container scrollable das colunas - Ocupa toda a tela e tem padding superior */}
        <div
          ref={containerRef}
          className={cn(
            "kanban-scroll-area",
            "h-[calc(100vh-120px)] w-full overflow-auto", // Ajustado para considerar a barra de filtros
            "scrollbar-thin scrollbar-track-transparent",
            "scrollbar-thumb-neutral-300 dark:scrollbar-thumb-neutral-600",
            "flex items-start gap-6 relative", // Aumentado o gap entre colunas
            "p-6 pt-4", // Ajustado padding para melhor espaçamento
            "transition-all duration-200 ease-in-out", // Adicionado transição suave
            isDraggingScroll && "cursor-grabbing",
            isMobileView ? "snap-x snap-mandatory" : ""
          )}
          onMouseDown={handleMouseDownOnContainer}
          onMouseMove={handleMouseMoveOnContainer}
          onMouseUp={handleMouseUpOnContainer}
        >
          {/* Mensagem quando não há colunas */}
          {colunas.length === 0 && (
            <div className="w-full h-full flex items-center justify-center text-muted-foreground">
              <div className="text-center p-8 bg-card rounded-lg border shadow-sm max-w-md">
                <h3 className="text-xl font-semibold mb-3">Configuração do Kanban</h3>
                <p className="mb-4">Este quadro Kanban ainda não possui colunas configuradas.</p>
                <p className="mb-4 text-sm text-muted-foreground">Um administrador precisa configurar as colunas para que o quadro funcione corretamente.</p>
                <div className="flex flex-col gap-2">
                  <Button
                    variant="default"
                    size="sm"
                    onClick={async () => {
                      try {
                        const { criarColunasPersonalizadasPadrao } = await import('@/services/kanbanService');
                        await criarColunasPersonalizadasPadrao();
                        window.location.reload();
                      } catch (error) {
                        console.error('Erro ao criar colunas padrão:', error);
                      }
                    }}
                  >
                    Criar Colunas Padrão
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => window.location.href = '/dashboard'}>
                    Voltar para Dashboard
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Área para adicionar novo precatório quando não há resultados */}
          {colunas.length > 0 && filteredPrecatorios.length === 0 && !loading && (
            <div className="w-full h-full flex items-center justify-center p-8">
              <div className="max-w-md w-full">
                {temFiltrosAtivos ? (
                  <Button
                    variant="outline"
                    size="lg"
                    className="w-full h-16 border border-dashed border-muted-foreground/30 hover:border-primary/30 hover:bg-primary/5"
                    onClick={handleLimparFiltros}
                  >
                    <FilterX className="h-5 w-5 mr-2" />
                    <span>Limpar filtros para ver precatórios</span>
                  </Button>
                ) : (
                  <Button
                    variant="outline"
                    size="lg"
                    className="w-full h-16 border border-dashed border-muted-foreground/30 hover:border-primary/30 hover:bg-primary/5"
                    onClick={handleNovoPrecatorio}
                  >
                    <PlusCircle className="h-5 w-5 mr-2" />
                    <span>Adicionar novo precatório</span>
                  </Button>
                )}
              </div>
            </div>
          )}

          {/* CORREÇÃO: Verificar se algum precatório foi atribuído a alguma coluna */}
          {(() => {
            // Só executar se houver precatórios e colunas
            if (filteredPrecatorios.length > 0 && colunas.length > 0) {
              // Verificar se algum precatório tem correspondência com alguma coluna
              let precatoriosComMatch = 0;

              // Função simplificada para verificar correspondência
              const checkMatch = (precatorio: Precatorio, coluna: KanbanColunaType): boolean => {
                if (!precatorio) return false;

                // Verificar correspondência direta por status_id (UUID)
                if (precatorio.status_id && coluna.id && precatorio.status_id === coluna.id) return true;

                // Verificar correspondência por status_uuid
                if (precatorio.status_id && coluna.status_uuid && precatorio.status_id === coluna.status_uuid) return true;

                // Verificar correspondência por código
                if (precatorio.status && coluna.status_id && precatorio.status === coluna.status_id) return true;
                if (precatorio.status && coluna.codigo && precatorio.status === coluna.codigo) return true;

                // Verificar correspondência por nome
                const colunaNomeSemContagem = (coluna.nome || coluna.name || '').replace(/\s*\(\d+\)$/, '');
                if (precatorio.status && colunaNomeSemContagem &&
                    precatorio.status.toLowerCase() === colunaNomeSemContagem.toLowerCase()) return true;

                return false;
              };

              // Verificar quantos precatórios têm correspondência com alguma coluna
              for (const precatorio of filteredPrecatorios) {
                let temMatch = false;
                for (const coluna of colunas) {
                  if (checkMatch(precatorio, coluna)) {
                    temMatch = true;
                    break;
                  }
                }
                if (temMatch) {
                  precatoriosComMatch++;
                }
              }

              console.log(`Precatórios com correspondência: ${precatoriosComMatch} de ${filteredPrecatorios.length}`);

              // Se menos da metade dos precatórios tiver correspondência, atribuir todos à primeira coluna
              if (precatoriosComMatch < filteredPrecatorios.length / 2) {
                console.log("⚠️ CORREÇÃO GLOBAL: Menos da metade dos precatórios tem correspondência. Atribuindo todos à primeira coluna.");

                // Encontrar a coluna "Análise" ou a primeira coluna
                let colunaAnalise = colunas.find(c =>
                  (c.nome && c.nome.toLowerCase().includes('análise')) ||
                  (c.name && c.name.toLowerCase().includes('análise')) ||
                  (c.codigo === 'analise') ||
                  (c.status_id === 'analise')
                );

                if (!colunaAnalise && colunas.length > 0) {
                  colunaAnalise = colunas[0];
                }

                if (colunaAnalise) {
                  // Atribuir todos os precatórios sem correspondência à coluna de análise
                  filteredPrecatorios.forEach(precatorio => {
                    let temMatch = false;
                    for (const coluna of colunas) {
                      if (checkMatch(precatorio, coluna)) {
                        temMatch = true;
                        break;
                      }
                    }

                    if (!temMatch) {
                      console.log(`✅ Atribuindo precatório ${precatorio.id} à coluna ${colunaAnalise.nome || colunaAnalise.name}`);
                      precatorio.status_id = colunaAnalise.id;
                      precatorio.status = colunaAnalise.codigo || 'analise';
                    }
                  });
                }
              }

              // Verificar se há precatórios em cada coluna
              let colunasVazias = 0;
              for (const coluna of colunas) {
                let precatoriosNaColuna = 0;
                for (const precatorio of filteredPrecatorios) {
                  if (checkMatch(precatorio, coluna)) {
                    precatoriosNaColuna++;
                  }
                }

                if (precatoriosNaColuna === 0) {
                  colunasVazias++;
                }
              }

              console.log(`Colunas vazias: ${colunasVazias} de ${colunas.length}`);
            }

            return null;
          })()}

          {/* Filtrar colunas com base nas permissões do usuário */}
          {(() => {
            // Filtrar colunas que o usuário tem permissão para ver
            const colunasVisiveis = filterVisibleColumns(colunas);

            if (colunas.length > 0 && colunasVisiveis.length === 0) {
              return (
                <div className="w-full h-full flex items-center justify-center text-muted-foreground">
                  <div className="text-center p-8 bg-card rounded-lg border shadow-sm max-w-md">
                    <h3 className="text-xl font-semibold mb-3">Sem permissão</h3>
                    <p className="mb-4">Você não tem permissão para visualizar nenhuma coluna do Kanban.</p>
                    <p className="mb-4 text-sm text-muted-foreground">Entre em contato com um administrador para solicitar acesso.</p>
                  </div>
                </div>
              );
            }

            return null;
          })()}

          {/* Renderizar colunas */}
          {filterVisibleColumns(colunas).map((coluna) => {
            // Função de correspondência aprimorada entre precatórios e colunas
            const matchPrecatorioToColumn = (precatorio: Precatorio, coluna: KanbanColunaType): boolean => {
              // Verificar se o precatório é válido
              if (!precatorio) return false;

              // Verificar se a coluna é válida
              if (!coluna) return false;

              try {
                // VERIFICAÇÃO PRELIMINAR: Se o precatório não tem status_id, atribuir um baseado no status
                if (!precatorio.status_id && precatorio.status) {
                  // Tentar encontrar uma coluna correspondente pelo status
                  const colunaCorrespondente = colunas.find(c =>
                    (c.status_id && c.status_id === precatorio.status) ||
                    (c.codigo && c.codigo === precatorio.status) ||
                    (c.nome && c.nome.toLowerCase() === precatorio.status.toLowerCase()) ||
                    (c.name && c.name.toLowerCase() === precatorio.status.toLowerCase())
                  );

                  if (colunaCorrespondente) {
                    // Atribuir status_id da coluna correspondente
                    precatorio.status_id = colunaCorrespondente.id;

                    // Atualizar no banco de dados em segundo plano
                    import('@/services/precatoriosServiceSimples').then(({ atualizarStatusPrecatorio }) => {
                      atualizarStatusPrecatorio(precatorio.id, colunaCorrespondente.id)
                        .catch(err => console.error(`Erro ao atualizar status_id do precatório ${precatorio.id}:`, err));
                    });
                  }
                }

                // VERIFICAÇÃO 1: Correspondência direta por status_id com id da coluna (mais prioritária)
                if (precatorio.status_id && coluna.id && precatorio.status_id === coluna.id) {
                  return true;
                }

                // VERIFICAÇÃO 2: Correspondência por status_id com status_uuid da coluna
                if (precatorio.status_id && coluna.status_uuid && precatorio.status_id === coluna.status_uuid) {
                  return true;
                }

                // VERIFICAÇÃO 3: Correspondência por código de status
                if (precatorio.status && coluna.status_id && precatorio.status === coluna.status_id) {
                  return true;
                }

                // VERIFICAÇÃO 4: Correspondência por código explícito da coluna
                if (precatorio.status && coluna.codigo && precatorio.status === coluna.codigo) {
                  return true;
                }

                // VERIFICAÇÃO 5: Correspondência por nome normalizado
                // Remover a parte do nome da coluna que contém a contagem entre parênteses
                const colunaNomeSemContagem = (coluna.nome || coluna.name || '').replace(/\s*\(\d+\)$/, '');

                if (precatorio.status && colunaNomeSemContagem) {
                  // Normalizar o status do precatório e o nome da coluna (remover acentos, converter para minúsculas)
                  const normalizar = (str: string) => {
                    return str.normalize('NFD').replace(/[\u0300-\u036f]/g, '').toLowerCase().replace(/\s+/g, '_');
                  };

                  const statusNormalizado = normalizar(String(precatorio.status));
                  const colunaNomeNormalizado = normalizar(String(colunaNomeSemContagem));

                  // Verificar correspondência exata
                  if (statusNormalizado === colunaNomeNormalizado) {
                    // Atualizar o status_id para manter consistência
                    if (precatorio.status_id !== coluna.id) {
                      precatorio.status_id = coluna.id;

                      // Atualizar no banco de dados em segundo plano
                      import('@/services/precatoriosServiceSimples').then(({ atualizarStatusPrecatorio }) => {
                        atualizarStatusPrecatorio(precatorio.id, coluna.id)
                          .catch(err => console.error(`Erro ao atualizar status_id do precatório ${precatorio.id}:`, err));
                      });
                    }
                    return true;
                  }
                }

                // VERIFICAÇÃO 6: Mapeamento específico de status para colunas
                // Mapeamento simplificado e mais abrangente
                const statusToColumnIdMap: Record<string, string[]> = {
                  'analise': ['analise', 'em_analise', 'novo', 'análise', 'em análise'],
                  'em_andamento': ['em_andamento', 'andamento', 'processando', 'em andamento'],
                  'aguardando': ['aguardando', 'espera', 'pendente', 'aguardando resposta'],
                  'proposta': ['proposta', 'proposta_tmj', 'proposta_btg', 'proposta tmj', 'proposta btg'],
                  'negociacao': ['negociacao', 'negociando', 'negociação', 'em negociação'],
                  'documentacao': ['documentacao', 'documentos', 'documentação'],
                  'concluido': ['concluido', 'finalizado', 'completo', 'concluído'],
                  'pagamento': ['pagamento', 'pago', 'em pagamento'],
                  'cancelado': ['cancelado', 'arquivado', 'cancelamento']
                };

                // Verificar se o status do precatório está no mapa
                if (precatorio.status) {
                  // Normalizar o status para busca no mapa
                  const statusNormalizado = String(precatorio.status).toLowerCase()
                    .normalize('NFD').replace(/[\u0300-\u036f]/g, '')
                    .replace(/\s+/g, '_');

                  // Verificar cada chave no mapa
                  for (const [key, values] of Object.entries(statusToColumnIdMap)) {
                    // Se o status normalizado está nos valores ou é igual à chave
                    if (statusNormalizado === key || values.includes(statusNormalizado)) {
                      // Verificar se o nome ou código da coluna corresponde a algum dos valores
                      const colunaNome = (coluna.nome || coluna.name || '').toLowerCase();
                      const colunaCodigo = (coluna.codigo || '').toLowerCase();

                      if (colunaNome.includes(key) || colunaCodigo.includes(key) ||
                          values.some(v => colunaNome.includes(v) || colunaCodigo.includes(v))) {

                        // Atualizar o status_id para manter consistência
                        if (precatorio.status_id !== coluna.id) {
                          precatorio.status_id = coluna.id;

                          // Atualizar no banco de dados em segundo plano
                          import('@/services/precatoriosServiceSimples').then(({ atualizarStatusPrecatorio }) => {
                            atualizarStatusPrecatorio(precatorio.id, coluna.id)
                              .catch(err => console.error(`Erro ao atualizar status_id do precatório ${precatorio.id}:`, err));
                          });
                        }
                        return true;
                      }
                    }
                  }
                }

                // VERIFICAÇÃO 7: Verificar se o precatório tem um status que corresponde a qualquer coluna
                // Se não, atribuir à primeira coluna
                if (coluna.ordem === 0 || coluna.ordem === 1 || coluna.id === colunas[0]?.id) {
                  // Verificar se o precatório já tem correspondência com outra coluna
                  const temMatch = colunas.some(c => {
                    if (c.id === coluna.id) return false; // Pular a coluna atual

                    // Verificar correspondência com outras colunas
                    return (
                      (precatorio.status_id && c.id && precatorio.status_id === c.id) ||
                      (precatorio.status_id && c.status_uuid && precatorio.status_id === c.status_uuid) ||
                      (precatorio.status && c.status_id && precatorio.status === c.status_id) ||
                      (precatorio.status && c.codigo && precatorio.status === c.codigo)
                    );
                  });

                  // Se não tem correspondência com nenhuma coluna, atribuir à primeira
                  if (!temMatch) {
                    console.log(`✅ Atribuindo precatório ${precatorio.id} à coluna ${coluna.nome || coluna.name} (primeira coluna)`);

                    // Atualizar o status_id e status do precatório para corresponder a esta coluna
                    precatorio.status_id = coluna.id;
                    if (coluna.status_id) {
                      precatorio.status = coluna.status_id;
                    }

                    // Atualizar no banco de dados em segundo plano
                    import('@/services/precatoriosServiceSimples').then(({ atualizarStatusPrecatorio }) => {
                      atualizarStatusPrecatorio(precatorio.id, coluna.id)
                        .catch(err => console.error(`Erro ao atualizar status_id do precatório ${precatorio.id}:`, err));
                    });

                    return true;
                  }
                }

                // VERIFICAÇÃO 8: Fallback final - se o precatório não tem status_id definido
                // e não conseguimos encontrar uma correspondência, atribuir à coluna atual
                // se ela for uma das primeiras colunas
                if (!precatorio.status_id && (coluna.ordem === 0 || coluna.ordem === 1 || coluna.id === colunas[0]?.id)) {
                  console.log(`⚠️ FALLBACK: Precatório ${precatorio.id} sem status_id atribuído à coluna ${coluna.nome || coluna.name}`);

                  // Atribuir à coluna atual
                  precatorio.status_id = coluna.id;
                  if (coluna.status_id) {
                    precatorio.status = coluna.status_id;
                  }

                  // Atualizar no banco de dados em segundo plano
                  import('@/services/precatoriosServiceSimples').then(({ atualizarStatusPrecatorio }) => {
                    atualizarStatusPrecatorio(precatorio.id, coluna.id)
                      .catch(err => console.error(`Erro ao atualizar status_id do precatório ${precatorio.id}:`, err));
                  });

                  return true;
                }

                return false;
              } catch (error) {
                console.error(`Erro ao verificar correspondência para precatório ${precatorio.id}:`, error);
                return false;
              }
            };

            // Filtrar precatórios para esta coluna
            const precatoriosDaColuna = filteredPrecatorios.filter(p => matchPrecatorioToColumn(p, coluna));

            // Verificar se todos os precatórios foram atribuídos a alguma coluna
            // Isso é importante para garantir que não haja precatórios "perdidos"
            if (coluna.ordem === 1 || coluna.ordem === colunas[0].ordem) {
              // Na primeira coluna, verificar quantos precatórios foram atribuídos a todas as colunas
              const allAssignedPrecatorios = new Set<string>();

              // Verificar precatórios já atribuídos a esta coluna
              precatoriosDaColuna.forEach(p => allAssignedPrecatorios.add(p.id));

              // Verificar se há precatórios não atribuídos
              const unassignedPrecatorios = filteredPrecatorios.filter(p => {
                // Verificar se este precatório já foi atribuído a alguma coluna
                const isAssigned = colunas.some(c => {
                  if (c.id === coluna.id) return false; // Pular a coluna atual
                  return matchPrecatorioToColumn(p, c);
                });

                // Se não foi atribuído e não está na lista de atribuídos, adicionar
                return !isAssigned && !allAssignedPrecatorios.has(p.id);
              });

              // Se houver precatórios não atribuídos, adicionar à primeira coluna
              if (unassignedPrecatorios.length > 0) {
                console.log(`⚠️ Encontrados ${unassignedPrecatorios.length} precatórios não atribuídos a nenhuma coluna. Atribuindo à primeira coluna: ${coluna.nome || coluna.name}`);

                // Atualizar os precatórios não atribuídos para terem o status desta coluna
                unassignedPrecatorios.forEach(p => {
                  // Atualizar o status_id e status do precatório para corresponder a esta coluna
                  if (coluna.status_uuid) {
                    p.status_id = coluna.status_uuid;
                  }
                  if (coluna.status_id) {
                    p.status = coluna.status_id;
                  }

                  // Tentar salvar a atualização no banco de dados (em segundo plano)
                  if (p.id) {
                    const updateData: { status_id?: string; status?: string } = {};
                    if (coluna.status_uuid) updateData.status_id = coluna.status_uuid;
                    if (coluna.status_id) updateData.status = coluna.status_id;

                    // Atualizar no banco de dados sem aguardar
                    import('@/services/precatoriosServiceSimples').then(({ atualizarStatusPrecatorio }) => {
                      // A função atualizarStatusPrecatorio espera (id, novoStatus)
                      // onde novoStatus pode ser o UUID ou o código
                      atualizarStatusPrecatorio(p.id, coluna.status_uuid || coluna.id)
                        .catch(err => console.error(`Erro ao atualizar status do precatório ${p.id}:`, err));
                    });
                  }
                });

                // Retornar os precatórios desta coluna + os não atribuídos
                return [...precatoriosDaColuna, ...unassignedPrecatorios];
              }
            }



            return (
              <div
                key={coluna.id}
                className={cn(
                  "transition-all duration-200 flex-shrink-0",
                  isMobileView ? "snap-start min-w-[87vw]" : ""
                )}
              >
                <KanbanColuna
                  coluna={coluna}
                  precatorios={precatoriosDaColuna}
                  onVerDetalhes={handleVerDetalhes}
                  onEdit={handleEditarPrecatorio}
                  onDelete={handleDeletePrecatorio}
                  onNovoPrecatorio={handleNovoPrecatorio}
                />
              </div>
            );
          })}
        </div>

        <DragOverlay dropAnimation={dropAnimation}>
          {activePrecatorio ? (
            <PrecatorioCard
              precatorio={activePrecatorio}
              onVerDetalhes={() => {}}
              onEdit={() => {}}
              onDelete={() => {}}
              isOverlay={true}
            />
          ) : null}
        </DragOverlay>
      </DndContext>

      {/* Modais (mantidos fora do fluxo principal) */}
      {isDetalhesModalOpen && precatorioSelecionado && (
        <DetalhesModal
          precatorio={precatorioSelecionado}
          isOpen={isDetalhesModalOpen}
          onOpenChange={setIsDetalhesModalOpen}
          onEdit={() => {
            setIsDetalhesModalOpen(false);
            handleEditarPrecatorio(precatorioSelecionado);
          }}
        />
      )}

      {isFormModalOpen && (
        <SharedPrecatorioForm
          isOpen={isFormModalOpen}
          onOpenChange={setIsFormModalOpen}
          initialData={precatorioSelecionado}
          modoEdicao={modoEdicao}
          onSave={(precatorio) => {
            onSavePrecatorio(precatorio);
            setIsFormModalOpen(false);
          }}
        />
      )}

      {isFiltrosModalOpen && (
        <FiltrosModal
          filtros={filtrosAvancados}
          isOpen={isFiltrosModalOpen}
          onOpenChange={setIsFiltrosModalOpen}
          onAplicarFiltros={(novosFiltros) => {
            setFiltrosAvancados(novosFiltros);
            setIsFiltrosModalOpen(false);
          }}
          onLimparFiltros={handleLimparFiltros}
        />
      )}

      {isEstatisticasModalOpen && (
        <EstatisticasModal
          stats={dashboardStats}
          isOpen={isEstatisticasModalOpen}
          onOpenChange={setIsEstatisticasModalOpen}
        />
      )}

      <AlertDialog open={isConfirmDeleteOpen} onOpenChange={setIsConfirmDeleteOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirmar exclusão</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir este precatório? Esta ação não pode ser desfeita.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDeletePrecatorio} className="bg-red-600 hover:bg-red-700">
              Excluir
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

// Wrap with React.memo for performance optimization
export const KanbanContainer = memo(KanbanContainerComponent);