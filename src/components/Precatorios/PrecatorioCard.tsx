import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Calendar,
  Clock,
  MoreHorizontal,
  AlertTriangle,
  ClipboardList,
  FileText,
  User,
  Tag,
  DollarSign,
  BarChart,
  Briefcase,
  Landmark,
  AlertCircle,
  CheckCircle2,
  GripVertical,
  Move, // Ícone para o botão de arrasto
  Lock,
  ShieldAlert,
  Eye,
  Pencil,
  Trash2
} from "lucide-react";
import { useAuth } from "@/hooks/useAuth";
import { useKanbanPermissions } from "@/hooks/useKanbanPermissions";
import { KanbanPermissionGuard } from "@/components/permissions/KanbanPermissionGuard";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { format, isAfter, isBefore, addDays } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Precatorio, CORES_STATUS } from "./types";
import { useDraggable } from "@dnd-kit/core";

import { cn } from "@/lib/utils";
import { memo, useState, useRef, useEffect } from "react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Progress } from "@/components/ui/progress";

interface PrecatorioCardProps {
  precatorio: Precatorio;
  onVerDetalhes: (precatorio: Precatorio) => void;
  onEdit?: (precatorio: Precatorio) => void;
  onDelete?: (id: string) => void;
  isOverlay?: boolean;
  compact?: boolean; // Nova prop para versão compacta
}

// Usando memo para evitar re-renderizações desnecessárias
export const PrecatorioCard = memo(function PrecatorioCard({
  precatorio,
  onVerDetalhes,
  onEdit,
  onDelete,
  isOverlay = false,
  compact = false,
}: PrecatorioCardProps) {
  const [isHovered, setIsHovered] = useState(false);
  const { user } = useAuth();
  const {
    canViewPrecatorio,
    canEditPrecatorio,
    canDeletePrecatorio
  } = useKanbanPermissions();

  // Variável para controlar o modo de arrasto
  const [dragMode, setDragMode] = useState(false);

  // Configurar o drag and drop com useDraggable - só ativar quando estiver em modo de arrasto
  const {
    attributes,
    listeners: originalListeners,
    setNodeRef,
    isDragging,
    transform,
  } = useDraggable({
    id: precatorio.id,
    data: {
      type: "precatorio",
      precatorio,
    },
    // Só permitir arrasto quando estiver em modo de arrasto
    disabled: !dragMode,
  });

  // Timer para ativar o modo de arrasto
  const dragTimerRef = useRef<number | null>(null);

  // Efeito para limpar o timer quando o componente for desmontado
  useEffect(() => {
    return () => {
      if (dragTimerRef.current !== null) {
        window.clearTimeout(dragTimerRef.current);
      }
    };
  }, []);

  // Função para iniciar o modo de arrasto após segurar o mouse
  const handleMouseDown = (e: React.MouseEvent) => {
    // Verificar se o clique foi em um botão ou menu dropdown
    if (
      e.target instanceof HTMLElement &&
      (e.target.closest('button') ||
       e.target.closest('[role="menuitem"]') ||
       e.target.closest('[data-state="open"]') ||
       e.target.closest('.drag-handle')) // Verificar se clicou no botão de arrasto
    ) {
      // Se clicou no botão de arrasto, ativar o modo de arrasto imediatamente
      if (e.target instanceof HTMLElement && e.target.closest('.drag-handle')) {
        setDragMode(true);
        // Chamar o listener original do dnd-kit para iniciar o arrasto
        if (originalListeners?.onMouseDown) {
          originalListeners.onMouseDown(e);
        }
        return;
      }
      return; // Ignorar cliques em outros elementos interativos
    }

    // Iniciar timer para ativar o modo de arrasto após 1 segundo
    if (dragTimerRef.current !== null) {
      window.clearTimeout(dragTimerRef.current);
    }

    dragTimerRef.current = window.setTimeout(() => {
      setDragMode(true); // Ativar modo de arrasto
      // Chamar o listener original do dnd-kit para iniciar o arrasto
      if (originalListeners?.onMouseDown) {
        originalListeners.onMouseDown(e);
      }
    }, 1000); // Aumentar para 1 segundo
  };

  // Função para cancelar o modo de arrasto e processar cliques
  const handleMouseUp = (e: React.MouseEvent) => {
    // Limpar o timer
    if (dragTimerRef.current !== null) {
      window.clearTimeout(dragTimerRef.current);
      dragTimerRef.current = null;
    }

    // Se não estamos em modo de arrasto, é um clique
    if (!dragMode && !isDragging) {
      handleCardClick(e);
    }

    // Resetar o modo de arrasto
    setDragMode(false);

    // Chamar o listener original se estiver arrastando
    if (dragMode && originalListeners?.onMouseUp) {
      originalListeners.onMouseUp(e);
    }
  };

  // Função para cancelar o modo de arrasto quando o mouse sai do card
  const handleMouseLeave = (e: React.MouseEvent) => {
    if (dragTimerRef.current !== null) {
      window.clearTimeout(dragTimerRef.current);
      dragTimerRef.current = null;
    }

    // Chamar o listener original se estiver arrastando
    if (dragMode && originalListeners?.onMouseLeave) {
      originalListeners.onMouseLeave(e);
    }
  };

  // Combinar os listeners personalizados com os originais
  const listeners = {
    onMouseDown: handleMouseDown,
    onMouseUp: handleMouseUp,
    onMouseLeave: handleMouseLeave,
    // Manter outros listeners originais
    ...(dragMode ? originalListeners : {}),
  };

  // Função para abrir os detalhes ao clicar no card
  const handleCardClick = (e: React.MouseEvent) => {
    console.log('Clique detectado no card:', precatorio.numero);

    // Verificar se o clique foi em um botão ou menu dropdown (evitar abrir detalhes)
    if (
      e.target instanceof HTMLElement &&
      (e.target.closest('button') ||
       e.target.closest('[role="menuitem"]') ||
       e.target.closest('[data-state="open"]'))
    ) {
      console.log('Clique em elemento interativo, ignorando');
      return;
    }

    // Abrir detalhes do precatório
    console.log('Abrindo detalhes do precatório:', precatorio.numero);
    onVerDetalhes(precatorio);
  };

  const formatarValor = (valor: number) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(valor);
  };

  // Função segura para formatar datas
  const formatarDataSegura = (data: Date | string | undefined) => {
    if (!data) return "N/A";
    try {
      const dataObj = data instanceof Date ? data : new Date(data);
      if (isNaN(dataObj.getTime())) return "Data inválida";
      return format(dataObj, "dd/MM/yyyy", { locale: ptBR });
    } catch (error) {
      console.error("Erro ao formatar data:", error);
      return "Data inválida";
    }
  };

  const formattedDate = precatorio.dataVencimento
    ? formatarDataSegura(precatorio.dataVencimento)
    : "";

  // Verificar se a data de vencimento está próxima ou já passou
  const verificarUrgencia = () => {
    if (!precatorio.dataVencimento) return { urgente: false, vencido: false };
    try {
      const dataVencimento = new Date(precatorio.dataVencimento);
      const hoje = new Date();
      const limitePrazo = addDays(hoje, 15); // 15 dias

      return {
        urgente: isAfter(dataVencimento, hoje) && isBefore(dataVencimento, limitePrazo),
        vencido: isBefore(dataVencimento, hoje)
      };
    } catch (e) {
      return { urgente: false, vencido: false };
    }
  };

  const { urgente, vencido } = verificarUrgencia();

  // Estilo simplificado para o card
  // Estilo para o card durante o arrasto - otimizado para performance
  const style = !isOverlay && transform ? {
    // Aplicar transform APENAS no card original, não no overlay
    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
    transition: 'none', // Remover transição durante o arrasto para evitar lag
    zIndex: 50, // Elevar o card durante o arrasto
    willChange: 'transform', // Otimização de performance
  } : isOverlay ? {
    // Estilos para o card no overlay (ex: rotação leve)
    transform: 'rotate(1deg) scale(1.03)', // Rotação mais sutil
    boxShadow: '0px 10px 25px rgba(0, 0, 0, 0.2)',
    border: '2px solid var(--primary)',
    pointerEvents: 'none' as const, // Garantir que o overlay não interfira com eventos
    willChange: 'transform', // Otimização de performance
  } : undefined;

  const corStatus = CORES_STATUS[precatorio.status as keyof typeof CORES_STATUS] || "gray";

  const getBadgeVariantForPrioridade = (prioridade: string) => {
    switch (prioridade) {
      case "alta":
        return "destructive";
      case "media":
        return "secondary";
      case "baixa":
        return "outline";
      default:
        return "secondary";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case "pendente":
        return <Clock className="h-3.5 w-3.5" />;
      case "processando":
        return <BarChart className="h-3.5 w-3.5" />;
      case "concluido":
        return <CheckCircle2 className="h-3.5 w-3.5" />;
      case "cancelado":
        return <AlertCircle className="h-3.5 w-3.5" />;
      default:
        return <Clock className="h-3.5 w-3.5" />;
    }
  };

  // Calcular a proporção para a barra de progresso (simulação)
  const calcularProgresso = () => {
    switch(precatorio.status.toLowerCase()) {
      case "pendente": return 20;
      case "processando": return 60;
      case "concluido": return 100;
      case "cancelado": return 0;
      default: return 30;
    }
  };

  return (
    <div
      ref={setNodeRef}
      {...listeners}
      {...attributes}
      data-draggable="card"
      data-drag-mode={dragMode ? "true" : "false"}
      data-precatorio-id={precatorio.id}
      className={cn(
        "precatorio-card bg-card border rounded-lg p-3.5 shadow-md",
        "relative",
        // Controle de cursor baseado no estado
        dragMode ? "cursor-grab" : "cursor-pointer", // Cursor pointer por padrão, grab quando segurar
        isDragging && "cursor-grabbing", // Cursor grabbing quando arrastando
        "transition-all duration-300 ease-in-out",
        "hover:shadow-lg hover:border-primary/30 hover:bg-gradient-to-b hover:from-card/95 hover:to-card/80",
        "dark:hover:border-primary/40 dark:hover:bg-gradient-to-b dark:hover:from-card/95 dark:hover:to-card/80",
        "select-none touch-manipulation", // Evitar seleção de texto
        "card-transition", // Classe de transição adicionada no CSS global
        isDragging && "dragging", // Adicionar classe para identificar arrasto
        isDragging && !isOverlay && "opacity-30", // Opacidade apenas no card original
        isOverlay && "z-50 scale-105 shadow-xl", // Elevar o card do overlay e aumentar levemente
        vencido ? "border-l-4 border-l-red-500" :
        urgente ? "border-l-4 border-l-amber-500" : "",
        // Adicionar efeito de brilho sutil quando o card está sendo arrastado
        isDragging && isOverlay && "ring-2 ring-primary/50 ring-offset-1",
      )}
      style={style}
      // Adicionar onClick como backup para garantir que o card seja clicável
      onClick={(e) => {
        // Só processar o clique se não estiver em modo de arrasto
        if (!dragMode && !isDragging) {
          handleCardClick(e);
        }
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Indicador de arrasto - visível apenas durante o arrasto */}
      {isDragging && (
        <div className="absolute inset-0 bg-primary/5 rounded-lg flex items-center justify-center z-20 pointer-events-none">
          <div className="animate-pulse text-primary/70 flex flex-col items-center">
            <Move size={24} className="mb-1" />
            <span className="text-xs font-medium">Movendo...</span>
          </div>
        </div>
      )}

      {/* Botão de arrasto */}
      <div className="absolute top-2 right-2 drag-handle cursor-grab p-1 rounded-md hover:bg-primary/10 transition-colors z-10">
        <Move size={16} className="text-muted-foreground" />
      </div>

      {/* Cabeçalho do card com design melhorado */}
      <div className="flex justify-between items-start mb-3">
        <div className="flex flex-col">
          <h3 className="font-semibold text-sm tracking-tight line-clamp-1 bg-primary/5 dark:bg-primary/10 px-2 py-0.5 rounded-md inline-block">{precatorio.numero_precatorio || precatorio.numero || 'Sem número'}</h3>
          <div className="flex items-center gap-1 mt-1.5">
            <Landmark className="h-3 w-3 text-muted-foreground shrink-0" />
            <p className="text-xs text-muted-foreground truncate max-w-[140px] hover:text-foreground transition-colors">{precatorio.tribunal}</p>
          </div>
        </div>

        {/* Indicador visual de arraste com animação - visível apenas ao passar o mouse */}
        <div
          className={cn(
            "absolute top-2 right-10 p-1 text-muted-foreground rounded-md transition-all duration-300",
            isHovered ? "opacity-70 scale-110" : "opacity-0 scale-90",
            "hover:opacity-100 hover:text-foreground hover:bg-muted/50"
          )}
          onClick={(e) => e.stopPropagation()}
        >
          <GripVertical className="h-5 w-5" />
        </div>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="h-7 w-7 rounded-full p-0 hover:bg-muted transition-all duration-200 hover:scale-110"
              aria-label="Ações"
              onClick={(e) => e.stopPropagation()}
            >
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            align="end"
            onClick={(e) => e.stopPropagation()}
            className="w-48 animate-in fade-in-50 zoom-in-95 duration-200 shadow-lg"
          >
            <DropdownMenuLabel className="font-semibold text-primary/80">Ações</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <KanbanPermissionGuard
              action="view"
              resourceType={precatorio.tipo === 'RPV' ? 'rpv' : 'precatorio'}
              resourceId={precatorio.id}
            >
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation();
                  onVerDetalhes(precatorio);
                }}
                className="transition-colors duration-200 hover:bg-primary/5 dark:hover:bg-primary/10 cursor-pointer"
              >
                <Eye className="mr-2 h-4 w-4" />
                Ver Detalhes
              </DropdownMenuItem>
            </KanbanPermissionGuard>

            <KanbanPermissionGuard
              action="edit"
              resourceType={precatorio.tipo === 'RPV' ? 'rpv' : 'precatorio'}
              resourceId={precatorio.id}
            >
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation();
                  onEdit(precatorio);
                }}
                className="transition-colors duration-200 hover:bg-primary/5 dark:hover:bg-primary/10 cursor-pointer"
              >
                <Pencil className="mr-2 h-4 w-4" />
                Editar
              </DropdownMenuItem>
            </KanbanPermissionGuard>

            <DropdownMenuSeparator />

            <KanbanPermissionGuard
              action="delete"
              resourceType={precatorio.tipo === 'RPV' ? 'rpv' : 'precatorio'}
              resourceId={precatorio.id}
            >
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation();
                  onDelete(precatorio.id);
                }}
                className="text-destructive focus:text-destructive transition-colors duration-200 hover:bg-red-50 dark:hover:bg-red-900/20 cursor-pointer"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Excluir
              </DropdownMenuItem>
            </KanbanPermissionGuard>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Conteúdo principal do card - com design melhorado */}
      <div className="grid grid-cols-2 gap-x-2 gap-y-2 mb-3">
        {/* Cliente */}
        {precatorio.cliente && (
          <div className="col-span-2 bg-muted/30 rounded-md px-2 py-1 hover:bg-muted/50 transition-colors">
            <div className="flex items-center gap-1.5">
              <User className="h-3.5 w-3.5 text-primary/70" />
              <span className="text-xs font-medium line-clamp-1">
                {precatorio.cliente.nome}
              </span>
            </div>
          </div>
        )}

        {/* Natureza e Valor */}
        <div className="col-span-1">
          <div className="flex items-center gap-1.5 hover:translate-x-0.5 transition-transform">
            <Briefcase className="h-3.5 w-3.5 text-muted-foreground" />
            <span className="text-xs line-clamp-1 hover:text-foreground transition-colors" title={precatorio.natureza}>
              {precatorio.natureza || "N/A"}
            </span>
          </div>
        </div>

        <div className="col-span-1">
          <div className="flex items-center gap-1.5 hover:translate-x-0.5 transition-transform">
            <DollarSign className="h-3.5 w-3.5 text-green-500" />
            <span className="text-xs font-medium text-green-600 dark:text-green-400">
              {formatarValor(precatorio.valor_total || precatorio.valor || 0)}
            </span>
          </div>
        </div>

        {/* Data de vencimento - com indicador de urgência */}
        {formattedDate && (
          <div className="col-span-2">
            <div className="flex items-center gap-1.5">
              <Calendar className={cn(
                "h-3.5 w-3.5",
                vencido ? "text-red-500" :
                urgente ? "text-amber-500" :
                "text-muted-foreground"
              )} />
              <span
                className={cn(
                  "text-xs",
                  vencido ? "text-red-500 font-medium" :
                  urgente ? "text-amber-500 font-medium" : ""
                )}
              >
                {vencido && <AlertTriangle className="inline h-3 w-3 mr-1" />}
                {formattedDate}
              </span>
            </div>
          </div>
        )}

        {/* Responsável - se existir */}
        {precatorio.responsavel && precatorio.responsavel.nome && precatorio.responsavel.nome !== 'Não atribuído' && (
          <div className="col-span-2">
            <div className="flex items-center gap-1.5">
              <User className="h-3.5 w-3.5 text-muted-foreground" />
              <div className="flex items-center gap-1">
                <span className="text-xs text-muted-foreground">Resp:</span>
                <span className="text-xs font-medium">{precatorio.responsavel.nome}</span>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Rodapé do card - tags, prioridade e status com design melhorado */}
      <div className="mt-auto pt-1 border-t border-border/30 flex justify-between items-center">
        {/* Tags - renderizar até 2 tags, mostrar badge com contagem se houver mais */}
        <div className="flex items-center gap-1.5 flex-wrap overflow-hidden">
          {precatorio.tags && precatorio.tags.length > 0 ? (
            <>
              {precatorio.tags.slice(0, 2).map((tag, index) => (
                <Badge
                  key={index}
                  variant="outline"
                  className="px-1.5 py-0 h-5 text-[10px] rounded-sm hover:bg-primary/10 hover:border-primary/30 transition-colors cursor-default"
                >
                  {tag}
                </Badge>
              ))}
              {precatorio.tags.length > 2 && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Badge
                        variant="secondary"
                        className="px-1.5 py-0 h-5 text-[10px] rounded-sm hover:bg-secondary-foreground/10 transition-colors cursor-default"
                      >
                        +{precatorio.tags.length - 2}
                      </Badge>
                    </TooltipTrigger>
                    <TooltipContent>
                      <div className="text-xs">
                        {precatorio.tags.slice(2).join(', ')}
                      </div>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </>
          ) : (
            <span className="text-[10px] text-muted-foreground italic">Sem tags</span>
          )}
        </div>

        {/* Prioridade e Status com efeitos visuais */}
        <div className="flex items-center gap-1.5">
          {/* Badge de prioridade com efeito hover */}
          {precatorio.prioridade && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Badge
                    variant={getBadgeVariantForPrioridade(precatorio.prioridade)}
                    className="px-1.5 py-0 h-5 text-[10px] rounded-sm hover:opacity-80 transition-all hover:scale-105 cursor-default"
                  >
                    {precatorio.prioridade}
                  </Badge>
                </TooltipTrigger>
                <TooltipContent>
                  <div className="text-xs">Prioridade {precatorio.prioridade}</div>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          {/* Status com ícone e efeito de vidro */}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div
                  className="flex items-center gap-1 px-1.5 py-0.5 rounded-sm text-[10px] font-medium shadow-sm hover:shadow transition-all hover:scale-105 cursor-default"
                  style={{
                    backgroundColor: `${corStatus}20`,
                    color: corStatus,
                    backdropFilter: 'blur(8px)',
                  }}
                >
                  {getStatusIcon(precatorio.status)}
                  <span>{precatorio.status}</span>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <div className="text-xs">Status: {precatorio.status}</div>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      {/* Barra de progresso com efeito de brilho */}
      <Progress
        value={calcularProgresso()}
        className={cn(
          "h-1.5 mt-3 bg-muted/50 rounded-full overflow-hidden",
          corStatus === "green" ? "[&>div]:bg-gradient-to-r [&>div]:from-green-500 [&>div]:to-green-400 [&>div]:animate-pulse" :
          corStatus === "blue" ? "[&>div]:bg-gradient-to-r [&>div]:from-blue-500 [&>div]:to-blue-400 [&>div]:animate-pulse" :
          corStatus === "red" ? "[&>div]:bg-gradient-to-r [&>div]:from-red-500 [&>div]:to-red-400 [&>div]:animate-pulse" :
          corStatus === "amber" ? "[&>div]:bg-gradient-to-r [&>div]:from-amber-500 [&>div]:to-amber-400 [&>div]:animate-pulse" :
          "[&>div]:bg-gradient-to-r [&>div]:from-primary [&>div]:to-primary/80 [&>div]:animate-pulse"
        )}
      />

      {/* Indicador de atualização - aparece brevemente quando o card é atualizado */}
      <div className="absolute -bottom-1 left-0 right-0 h-1 bg-primary/50 scale-x-0 origin-left transition-transform duration-500 rounded-b-lg opacity-70"
           style={{ transform: isDragging ? 'scaleX(1)' : 'scaleX(0)' }} />
    </div>
  );
});