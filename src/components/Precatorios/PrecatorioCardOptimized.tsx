import { memo } from "react";
import { useDraggable } from "@dnd-kit/core";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  Calendar,
  DollarSign,
  User,
  Landmark,
  MoreHorizontal,
  Eye,
  Pencil,
  Trash2,
  GripVertical,
  AlertTriangle,
  Clock,
  CheckCircle2,
  AlertCircle,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { format, isAfter, isBefore, addDays } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Precatorio } from "./types";
import { cn } from "@/lib/utils";

interface PrecatorioCardOptimizedProps {
  precatorio: Precatorio;
  onVerDetalhes: (precatorio: Precatorio) => void;
  onEdit?: (precatorio: Precatorio) => void;
  onDelete?: (id: string) => void;
  isOverlay?: boolean;
  compact?: boolean;
}

export const PrecatorioCardOptimized = memo(function PrecatorioCardOptimized({
  precatorio,
  onVerDetalhes,
  onEdit,
  onDelete,
  isOverlay = false,
  compact = false,
}: PrecatorioCardOptimizedProps) {

  // Configurar drag and drop
  const {
    attributes,
    listeners,
    setNodeRef,
    isDragging,
    transform,
  } = useDraggable({
    id: precatorio.id,
    data: {
      type: "precatorio",
      precatorio,
    },
  });

  // Formatação de valores
  const formatarValor = (valor: number) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(valor);
  };

  const formatarDataSegura = (data: Date | string | undefined) => {
    if (!data) return "N/A";
    try {
      const dataObj = data instanceof Date ? data : new Date(data);
      if (isNaN(dataObj.getTime())) return "Data inválida";
      return format(dataObj, "dd/MM/yyyy", { locale: ptBR });
    } catch (error) {
      return "Data inválida";
    }
  };

  // Verificar urgência
  const verificarUrgencia = () => {
    if (!precatorio.dataVencimento) return { urgente: false, vencido: false };
    try {
      const dataVencimento = new Date(precatorio.dataVencimento);
      const hoje = new Date();
      const limitePrazo = addDays(hoje, 15);

      return {
        urgente: isAfter(dataVencimento, hoje) && isBefore(dataVencimento, limitePrazo),
        vencido: isBefore(dataVencimento, hoje)
      };
    } catch (e) {
      return { urgente: false, vencido: false };
    }
  };

  const { urgente, vencido } = verificarUrgencia();

  // Ícone do status
  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case "novo":
        return <Clock className="h-3 w-3" />;
      case "analise":
        return <AlertTriangle className="h-3 w-3" />;
      case "concluido":
        return <CheckCircle2 className="h-3 w-3" />;
      case "cancelado":
        return <AlertCircle className="h-3 w-3" />;
      default:
        return <Clock className="h-3 w-3" />;
    }
  };

  // Cor da prioridade
  const getPrioridadeCor = (prioridade: string) => {
    switch (prioridade) {
      case "alta":
        return "destructive";
      case "media":
        return "secondary";
      case "baixa":
        return "outline";
      default:
        return "secondary";
    }
  };

  // Estilo para drag and drop
  const style = transform ? {
    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
    zIndex: isDragging ? 50 : 1,
  } : undefined;

  const handleCardClick = (e: React.MouseEvent) => {
    // Evitar abrir detalhes se clicou em botão
    if (e.target instanceof HTMLElement && e.target.closest('button')) {
      return;
    }
    onVerDetalhes(precatorio);
  };

  return (
    <Card
      ref={setNodeRef}
      {...listeners}
      {...attributes}
      data-draggable="card"
      style={style}
      className={cn(
        "cursor-pointer transition-all duration-200 hover:shadow-md",
        "border-l-4",
        vencido ? "border-l-red-500" : urgente ? "border-l-amber-500" : "border-l-primary",
        isDragging && !isOverlay && "opacity-50",
        isOverlay && "shadow-xl scale-105 rotate-1",
        compact ? "p-2" : "p-3"
      )}
      onClick={handleCardClick}
    >
      <CardContent className={cn("p-0", compact ? "space-y-1" : "space-y-2")}>
        {/* Header */}
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <h4 className={cn(
              "font-medium truncate",
              compact ? "text-xs" : "text-sm"
            )}>
              {precatorio.numero_precatorio || precatorio.numero || 'Sem número'}
            </h4>
            {!compact && (
              <div className="flex items-center gap-1 mt-1">
                <Landmark className="h-3 w-3 text-muted-foreground" />
                <span className="text-xs text-muted-foreground truncate">
                  {precatorio.tribunal}
                </span>
              </div>
            )}
          </div>

          <div className="flex items-center gap-1">
            {/* Ícone de drag */}
            <GripVertical className="h-4 w-4 text-muted-foreground opacity-50 hover:opacity-100" />

            {/* Menu de ações */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0"
                  onClick={(e) => e.stopPropagation()}
                >
                  <MoreHorizontal className="h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-40">
                <DropdownMenuLabel>Ações</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => onVerDetalhes(precatorio)}>
                  <Eye className="mr-2 h-3 w-3" />
                  Ver Detalhes
                </DropdownMenuItem>
                {onEdit && (
                  <DropdownMenuItem onClick={() => onEdit(precatorio)}>
                    <Pencil className="mr-2 h-3 w-3" />
                    Editar
                  </DropdownMenuItem>
                )}
                {onDelete && (
                  <>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      onClick={() => onDelete(precatorio.id)}
                      className="text-destructive"
                    >
                      <Trash2 className="mr-2 h-3 w-3" />
                      Excluir
                    </DropdownMenuItem>
                  </>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Cliente */}
        {precatorio.cliente && (
          <div className="flex items-center gap-1">
            <User className="h-3 w-3 text-muted-foreground" />
            <span className={cn(
              "text-muted-foreground truncate",
              compact ? "text-xs" : "text-sm"
            )}>
              {precatorio.cliente.nome}
            </span>
          </div>
        )}

        {/* Valor */}
        <div className="flex items-center gap-1">
          <DollarSign className="h-3 w-3 text-green-500" />
          <span className={cn(
            "font-medium text-green-600 dark:text-green-400",
            compact ? "text-xs" : "text-sm"
          )}>
            {formatarValor(precatorio.valor_total || precatorio.valor || 0)}
          </span>
        </div>

        {/* Data de vencimento */}
        {precatorio.dataVencimento && (
          <div className="flex items-center gap-1">
            <Calendar className={cn(
              "h-3 w-3",
              vencido ? "text-red-500" : urgente ? "text-amber-500" : "text-muted-foreground"
            )} />
            <span className={cn(
              compact ? "text-xs" : "text-sm",
              vencido ? "text-red-600" : urgente ? "text-amber-600" : "text-muted-foreground"
            )}>
              {formatarDataSegura(precatorio.dataVencimento)}
            </span>
          </div>
        )}

        {/* Footer com status e prioridade */}
        <div className="flex items-center justify-between pt-1">
          <div className="flex items-center gap-1">
            {getStatusIcon(precatorio.status)}
            <span className={cn(
              "text-muted-foreground capitalize",
              compact ? "text-xs" : "text-sm"
            )}>
              {precatorio.status}
            </span>
          </div>

          {precatorio.prioridade && (
            <Badge
              variant={getPrioridadeCor(precatorio.prioridade)}
              className={compact ? "text-xs px-1 py-0" : "text-xs"}
            >
              {precatorio.prioridade}
            </Badge>
          )}
        </div>
      </CardContent>
    </Card>
  );
});
