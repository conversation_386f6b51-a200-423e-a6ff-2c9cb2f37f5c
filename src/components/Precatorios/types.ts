export interface Precatorio {
  id: string;
  numero?: string;
  numero_precatorio?: string;
  valor?: number;
  valor_total?: number;
  desconto?: number;
  // Tornar o tipo status mais flexível para lidar com valores dinâmicos do banco de dados
  status: string;  // Aceita qualquer string para maior flexibilidade com o banco
  status_id?: string;  // ID da coluna do kanban correspondente ao status
  beneficiario_id?: string;
  tribunal_id?: string;
  cliente?: {
    nome: string;
    avatar?: string;
    email?: string;
    telefone?: string;
  };
  responsavel?: {
    nome: string;
    avatar?: string;
    cargo?: string;
  };
  responsavel_id?: string;
  dataCriacao?: string;
  dataAtualizacao?: string;
  dataVencimento?: string;
  data_entrada?: string;
  created_at?: string;
  updated_at?: string;
  data_previsao_pagamento?: string;
  tribunal?: string;
  natureza?: string;
  entidade_devedora?: string;
  tipo?: string;
  categoria?: string;
  documentos?: {
    nome: string;
    tipo: string;
    status: "pendente" | "aprovado" | "rejeitado";
    data: string;
  }[];
  historico?: {
    acao: string;
    data: string;
    usuario: string;
    detalhes: string;
  }[];
  observacoes?: string;
  tags?: string[];
  prioridade?: string;
  is_deleted?: boolean;
}

export interface FiltrosAvancados {
  prioridade: string;
  tribunal: string;
  natureza: string;
  valorMin?: number;
  valorMax?: number;
  dataInicio?: Date;
  dataFim?: Date;
  dataVencimentoInicio?: Date;
  dataVencimentoFim?: Date;
  tags: string[];
  responsavel: string;
}

export interface KanbanColuna {
  id: string;
  nome: string;
  name?: string; // Para compatibilidade com código existente
  cor: string;
  color?: string; // Para compatibilidade com código existente
  tipo?: 'PRECATORIO' | 'RPV' | 'AMBOS'; // Tipo de visualização a que pertence
  ordem: number; // Ordem de exibição na visualização
  status_id?: string; // Código do status correspondente no banco de dados (ex: 'analise')
  status_uuid?: string; // UUID do status correspondente na tabela status_precatorios
  status_ref_id?: string; // Referência adicional para o ID do status (usado em algumas consultas)
  ativo?: boolean; // Se a coluna está ativa ou não
  count?: number; // Número de precatórios/RPVs na coluna (para exibição)
  is_default?: boolean; // Se é uma coluna padrão
  is_system?: boolean; // Se é uma coluna do sistema
  codigo?: string; // Código da coluna (usado para correspondência com status)
  status?: { // Informações do status relacionado
    id: string;
    nome: string;
    codigo: string;
    cor: string;
  };
  alerts?: {
    tipo: string;
    mensagem: string;
  }[];
}

export interface DashboardStats {
  total: number;
  valorTotal: number;
  concluidos: number;
  emAndamento: number;
  taxaConclusao: number;
  porStatus: {
    status: string;
    valor: number;
    cor: string;
  }[];
  porPrioridade: {
    prioridade: string;
    valor: number;
  }[];
  valoresPorTribunal: {
    tribunal: string;
    valor: number;
  }[];
}

export const CORES_STATUS = {
  analise: "#3b82f6",
  proposta_tmj: "#8b5cf6",
  proposta_btg: "#ec4899",
  negociacao: "#f59e0b",
  documentacao: "#10b981",
  pagamento: "#6366f1",
  concluido: "#22c55e",
  cancelado: "#ef4444",
};

export const COLORS_CHART = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#A020F0', '#20B2AA'];

export interface CustomView {
  id: string;
  nome: string;
  descricao?: string;
  user_id: string;
  is_public: boolean;
  is_default: boolean;
  is_favorite?: boolean;
  is_admin_created?: boolean;
  is_system?: boolean;
  layout?: 'kanban' | 'lista' | 'tabela';
  icone?: string;
  cor?: string;
  colunas_selecionadas?: string[];
  filtros?: FiltrosAvancados;
  tags_selecionadas?: string[];
  created_at?: string;
  updated_at?: string;
}