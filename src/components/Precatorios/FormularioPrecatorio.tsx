import { useState, useEffect } from "react";
import { Precatorio } from "./types";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { CalendarIcon, X, UploadCloud, Trash2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { supabase } from "@/lib/supabase";

interface FormularioPrecatorioProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  precatorio: Partial<Precatorio> | null;
  onSave: (precatorio: Precatorio) => void;
  modoEdicao: boolean;
}

export function FormularioPrecatorio({
  isOpen,
  onOpenChange,
  precatorio,
  onSave,
  modoEdicao,
}: FormularioPrecatorioProps) {
  const [form, setForm] = useState<Partial<Precatorio>>({
    id: "",
    numero: "",
    status: "analise",
    status_id: "",
    valor: 0,
    dataVencimento: undefined,
    prioridade: "media",
    tribunal: "",
    natureza: "",
    desconto: 0,
    cliente: {
      nome: "",
      email: "",
      telefone: "",
      avatar: "",
    },
    documentos: [],
    tags: [],
    responsavel: {
      nome: "",
      avatar: "",
      cargo: "",
    },
    observacoes: "",
    dataAtualizacao: new Date().toISOString(),
    dataCriacao: new Date().toISOString(),
  });

  const [novaTag, setNovaTag] = useState("");
  const [novoDocumento, setNovoDocumento] = useState<{
    nome: string;
    tipo: string;
    status: "pendente" | "aprovado" | "rejeitado";
    data: string;
  }>({
    nome: "",
    tipo: "",
    status: "pendente",
    data: new Date().toISOString()
  });
  const [statusOptions, setStatusOptions] = useState<Array<{id: string, codigo: string, nome: string, cor: string}>>([]);

  // Fetch status options from the database
  useEffect(() => {
    const fetchStatusOptions = async () => {
      try {
        const { data, error } = await supabase
          .from('status_precatorios')
          .select('id, codigo, nome, cor')
          .eq('ativo', true)
          .order('ordem', { ascending: true });

        if (error) {
          console.error('Error fetching status options:', error);
          return;
        }

        if (data && data.length > 0) {
          setStatusOptions(data);
        }
      } catch (error) {
        console.error('Error fetching status options:', error);
      }
    };

    fetchStatusOptions();
  }, []);

  useEffect(() => {
    if (precatorio) {
      setForm({
        ...form,
        ...precatorio,
      });
    }
  }, [precatorio]);

  const handleChange = (campo: string, valor: string | number | Date | boolean | string[]) => {
    if (campo.includes(".")) {
      const [parent, child] = campo.split(".");
      setForm((prev) => {
        const parentObj = prev[parent as keyof typeof prev];
        if (parentObj && typeof parentObj === 'object' && !Array.isArray(parentObj)) {
          return {
            ...prev,
            [parent as keyof typeof prev]: {
              ...(parentObj as Record<string, unknown>),
              [child as string]: valor,
            },
          };
        }
        // If parent doesn't exist or isn't an object, create it
        return {
          ...prev,
          [parent as keyof typeof prev]: {
            [child as string]: valor,
          },
        };
      });
    } else {
      setForm((prev) => ({
        ...prev,
        [campo as keyof typeof prev]: valor,
      }));
    }
  };

  const adicionarTag = () => {
    if (novaTag.trim() === "" || form.tags?.includes(novaTag)) {
      return;
    }

    setForm((prev) => ({
      ...prev,
      tags: [...(prev.tags || []), novaTag],
    }));
    setNovaTag("");
  };

  const removerTag = (tag: string) => {
    setForm((prev) => ({
      ...prev,
      tags: prev.tags?.filter((t) => t !== tag) || [],
    }));
  };

  const adicionarDocumento = () => {
    if (!novoDocumento.nome || !novoDocumento.status) {
      return;
    }

    setForm((prev) => ({
      ...prev,
      documentos: [
        ...(prev.documentos || []),
        { ...novoDocumento },
      ],
    }));

    setNovoDocumento({
      nome: "",
      tipo: "",
      status: "pendente",
      data: new Date().toISOString()
    });
  };

  const removerDocumento = (nome: string) => {
    setForm((prev) => ({
      ...prev,
      documentos: prev.documentos?.filter((doc) => doc.nome !== nome) || [],
    }));
  };

  const handleSubmit = () => {
    if (!form.numero || !form.valor || !form.tribunal) {
      alert("Por favor, preencha os campos obrigatórios: Número, Valor e Tribunal");
      return;
    }

    // Ensure we have a status_id
    if (!form.status_id && form.status && statusOptions.length > 0) {
      // Try to find the status_id based on the status code
      const matchingStatus = statusOptions.find(s => s.codigo === form.status);
      if (matchingStatus) {
        form.status_id = matchingStatus.id;
      }
    }

    onSave({
      ...form,
      id: form.id || Date.now().toString(),
      dataUltimaAtualizacao: new Date(),
    } as Precatorio);

    onOpenChange(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto" aria-describedby="formulario-precatorio-description">
        <DialogHeader>
          <DialogTitle>
            {modoEdicao ? "Editar Precatório" : "Novo Precatório"}
          </DialogTitle>
          <DialogDescription id="formulario-precatorio-description">
            {modoEdicao
              ? "Atualize as informações do precatório utilizando o formulário abaixo."
              : "Preencha as informações do novo precatório utilizando o formulário abaixo."}
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="basico" className="w-full">
          <TabsList className="grid grid-cols-4 mb-4">
            <TabsTrigger value="basico">Informações Básicas</TabsTrigger>
            <TabsTrigger value="cliente">Dados do Cliente</TabsTrigger>
            <TabsTrigger value="documentos">Documentos</TabsTrigger>
            <TabsTrigger value="outros">Observações/Tags</TabsTrigger>
          </TabsList>

          <TabsContent value="basico" className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Número *</label>
                <Input
                  placeholder="Número do precatório"
                  value={form.numero || ""}
                  onChange={(e) => handleChange("numero", e.target.value)}
                  required
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Valor *</label>
                <Input
                  type="number"
                  placeholder="Valor do precatório"
                  value={form.valor || ""}
                  onChange={(e) => handleChange("valor", Number(e.target.value))}
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Status</label>
                <Select
                  value={form.status_id || ""}
                  onValueChange={(value) => {
                    // Find the corresponding status code
                    const selectedStatus = statusOptions.find(status => status.id === value);
                    if (selectedStatus) {
                      handleChange("status", selectedStatus.codigo);
                      handleChange("status_id", selectedStatus.id);
                    }
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione o status" />
                  </SelectTrigger>
                  <SelectContent>
                    {statusOptions.map(status => (
                      <SelectItem key={status.id} value={status.id}>
                        <div className="flex items-center">
                          <div
                            className="w-3 h-3 rounded-full mr-2"
                            style={{ backgroundColor: status.cor || '#888888' }}
                          />
                          {status.nome}
                        </div>
                      </SelectItem>
                    ))}
                    {statusOptions.length === 0 && (
                      <SelectItem value="loading" disabled>Carregando status...</SelectItem>
                    )}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Prioridade</label>
                <Select
                  value={form.prioridade || "Média"}
                  onValueChange={(value) => handleChange("prioridade", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione a prioridade" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Alta">Alta</SelectItem>
                    <SelectItem value="Média">Média</SelectItem>
                    <SelectItem value="Baixa">Baixa</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Tribunal *</label>
                <Select
                  value={form.tribunal || "nenhum"}
                  onValueChange={(value) => handleChange("tribunal", value === "nenhum" ? "" : value)}
                  required
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione o tribunal" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="nenhum">Selecione...</SelectItem>
                    <SelectItem value="TRF1">TRF1</SelectItem>
                    <SelectItem value="TRF2">TRF2</SelectItem>
                    <SelectItem value="TRF3">TRF3</SelectItem>
                    <SelectItem value="TRF4">TRF4</SelectItem>
                    <SelectItem value="TRF5">TRF5</SelectItem>
                    <SelectItem value="TRF6">TRF6</SelectItem>
                    <SelectItem value="STF">STF</SelectItem>
                    <SelectItem value="STJ">STJ</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Natureza</label>
                <Select
                  value={form.natureza || "nenhum"}
                  onValueChange={(value) => handleChange("natureza", value === "nenhum" ? "" : value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione a natureza" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="nenhum">Selecione...</SelectItem>
                    <SelectItem value="Alimentar">Alimentar</SelectItem>
                    <SelectItem value="Comum">Comum</SelectItem>
                    <SelectItem value="Fiscal">Fiscal</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Data de Vencimento</label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !form.dataVencimento && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {form.dataVencimento ? (
                        typeof form.dataVencimento === 'string' ? form.dataVencimento : format(form.dataVencimento, "dd/MM/yyyy", { locale: ptBR })
                      ) : (
                        <span>Selecione uma data</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={typeof form.dataVencimento === 'string' ? new Date(form.dataVencimento) : form.dataVencimento}
                      onSelect={(date) => handleChange("dataVencimento", date ? date.toISOString() : "")}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Desconto (%)</label>
                <Input
                  type="number"
                  placeholder="Desconto em percentual"
                  value={form.desconto || ""}
                  onChange={(e) => handleChange("desconto", Number(e.target.value))}
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Responsável</label>
              <Input
                placeholder="Nome do responsável"
                value={form.responsavel?.nome || ""}
                onChange={(e) => {
                  const responsavel = {
                    nome: e.target.value,
                    avatar: form.responsavel?.avatar || '',
                    cargo: form.responsavel?.cargo || ''
                  };
                  setForm(prev => ({...prev, responsavel}));
                }}
              />
            </div>
          </TabsContent>

          <TabsContent value="cliente" className="space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Nome do Cliente</label>
              <Input
                placeholder="Nome completo"
                value={form.cliente?.nome || ""}
                onChange={(e) => handleChange("cliente.nome", e.target.value)}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Email</label>
                <Input
                  type="email"
                  placeholder="Email de contato"
                  value={form.cliente?.email || ""}
                  onChange={(e) => handleChange("cliente.email", e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Telefone</label>
                <Input
                  placeholder="Telefone de contato"
                  value={form.cliente?.telefone || ""}
                  onChange={(e) => handleChange("cliente.telefone", e.target.value)}
                />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="documentos" className="space-y-4">
            <div className="space-y-2">
              <div className="flex flex-col space-y-4">
                <div className="grid grid-cols-12 mb-2 font-medium text-sm">
                  <div className="col-span-5">Nome do Documento</div>
                  <div className="col-span-5">Status</div>
                  <div className="col-span-2">Ações</div>
                </div>

                {form.documentos && form.documentos.length > 0 ? (
                  form.documentos.map((doc) => (
                    <div key={doc.nome} className="grid grid-cols-12 items-center gap-2">
                      <div className="col-span-5">{doc.nome}</div>
                      <div className="col-span-5">
                        <Badge
                          variant={
                            doc.status === "aprovado"
                              ? "outline"
                              : doc.status === "pendente"
                              ? "secondary"
                              : "destructive"
                          }
                        >
                          {doc.status}
                        </Badge>
                      </div>
                      <div className="col-span-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => removerDocumento(doc.nome)}
                        >
                          <Trash2 className="h-4 w-4 text-red-500" />
                        </Button>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-4 text-muted-foreground">
                    Nenhum documento adicionado
                  </div>
                )}
              </div>

              <div className="pt-4 border-t">
                <h4 className="text-sm font-medium mb-2">Adicionar Documento</h4>
                <div className="grid grid-cols-12 gap-2">
                  <div className="col-span-5">
                    <Input
                      placeholder="Nome do documento"
                      value={novoDocumento.nome}
                      onChange={(e) =>
                        setNovoDocumento({ ...novoDocumento, nome: e.target.value })
                      }
                    />
                  </div>
                  <div className="col-span-5">
                    <Select
                      value={novoDocumento.status}
                      onValueChange={(value) =>
                        setNovoDocumento({ ...novoDocumento, status: value as "pendente" | "aprovado" | "rejeitado" })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Status do documento" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="pendente">Pendente</SelectItem>
                        <SelectItem value="aprovado">Aprovado</SelectItem>
                        <SelectItem value="rejeitado">Rejeitado</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="col-span-2">
                    <Button
                      type="button"
                      onClick={adicionarDocumento}
                      variant="secondary"
                      className="w-full"
                    >
                      <UploadCloud className="h-4 w-4 mr-1" />
                      Adicionar
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="outros" className="space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Observações</label>
              <Textarea
                placeholder="Observações adicionais sobre o precatório"
                className="min-h-[100px]"
                value={form.observacoes || ""}
                onChange={(e) => handleChange("observacoes", e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Tags</label>
              <div className="flex flex-wrap gap-2 mb-2">
                {form.tags?.map((tag) => (
                  <Badge key={tag} variant="secondary" className="gap-1">
                    {tag}
                    <X
                      className="h-3 w-3 cursor-pointer"
                      onClick={() => removerTag(tag)}
                    />
                  </Badge>
                ))}
              </div>
              <div className="flex gap-2">
                <Input
                  placeholder="Nova tag"
                  value={novaTag}
                  onChange={(e) => setNovaTag(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      e.preventDefault();
                      adicionarTag();
                    }
                  }}
                />
                <Button
                  variant="outline"
                  type="button"
                  onClick={adicionarTag}
                >
                  Adicionar
                </Button>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button onClick={handleSubmit}>
            {modoEdicao ? "Atualizar" : "Salvar"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}