import { useState } from 'react';
import { Upload, File, X } from 'lucide-react';
import { Button } from './ui/button';
import { Progress } from './ui/progress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';

interface FileUploaderProps {
  onUpload: (file: File, tipo: string) => Promise<void>;
}

export function FileUploader({ onUpload }: FileUploaderProps) {
  const [file, setFile] = useState<File | null>(null);
  const [tipo, setTipo] = useState<string>('');
  const [progress, setProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    const droppedFile = e.dataTransfer.files[0];
    if (droppedFile) setFile(droppedFile);
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) setFile(selectedFile);
  };

  const handleUpload = async () => {
    if (!file || !tipo) return;

    try {
      setIsUploading(true);
      // Simular progresso
      const interval = setInterval(() => {
        setProgress(prev => Math.min(prev + 10, 90));
      }, 100);

      await onUpload(file, tipo);
      
      setProgress(100);
      setTimeout(() => {
        setFile(null);
        setTipo('');
        setProgress(0);
      }, 500);

      clearInterval(interval);
    } catch (error) {
      console.error(error);
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="space-y-4">
      <div
        onDrop={handleDrop}
        onDragOver={e => e.preventDefault()}
        className="border-2 border-dashed rounded-lg p-8 text-center hover:border-primary transition-colors"
      >
        {file ? (
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <File className="h-6 w-6" />
              <span>{file.name}</span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setFile(null)}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        ) : (
          <div>
            <Upload className="h-8 w-8 mx-auto mb-2" />
            <p>Arraste um arquivo ou clique para selecionar</p>
            <input
              type="file"
              onChange={handleFileSelect}
              className="hidden"
              id="file-upload"
            />
            <Button
              variant="ghost"
              onClick={() => document.getElementById('file-upload')?.click()}
            >
              Selecionar arquivo
            </Button>
          </div>
        )}
      </div>

      <Select value={tipo} onValueChange={setTipo}>
        <SelectTrigger>
          <SelectValue placeholder="Selecione o tipo de documento" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="rg">RG</SelectItem>
          <SelectItem value="cpf">CPF</SelectItem>
          <SelectItem value="comprovante_residencia">Comprovante de Residência</SelectItem>
          <SelectItem value="procuracao">Procuração</SelectItem>
          <SelectItem value="outros">Outros</SelectItem>
        </SelectContent>
      </Select>

      {progress > 0 && (
        <Progress value={progress} className="w-full" />
      )}

      <Button
        onClick={handleUpload}
        disabled={!file || !tipo || isUploading}
        className="w-full"
      >
        {isUploading ? 'Enviando...' : 'Enviar documento'}
      </Button>
    </div>
  );
}