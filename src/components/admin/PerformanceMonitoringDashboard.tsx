/**
 * Performance Monitoring Dashboard for Admins
 * Provides real-time insights into cache performance, memory usage, and system metrics
 */

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Activity,
  Database,
  Clock,
  Zap,
  BarChart3,
  RefreshCw,
  Trash2,
  TrendingUp,
  Server,
  HardDrive
} from 'lucide-react';
import { getCacheMetrics, getCacheInfo, cleanupExpiredCache, clearCache } from '@/services/cacheService';
import { warmupApplicationCache, DEFAULT_WARMUP_CONFIG } from '@/services/cacheWarmupService';
import { cacheLogger } from '@/lib/logger';

interface CacheMetrics {
  totalItems: number;
  activeItems: number;
  expiredItems: number;
  totalHits: number;
  averageHitsPerItem: number;
  itemsByType: Record<string, number>;
  memoryUsage: number;
}

interface PerformanceStats {
  cacheHitRatio: number;
  averageResponseTime: number;
  memoryEfficiency: number;
  systemHealth: 'excellent' | 'good' | 'warning' | 'critical';
}

export function PerformanceMonitoringDashboard() {
  const [metrics, setMetrics] = useState<CacheMetrics | null>(null);
  const [performanceStats, setPerformanceStats] = useState<PerformanceStats | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isWarmingUp, setIsWarmingUp] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  // Load metrics on component mount and set up auto-refresh
  useEffect(() => {
    loadMetrics();

    // Auto-refresh every 30 seconds
    const interval = setInterval(loadMetrics, 30000);
    return () => clearInterval(interval);
  }, []);

  const loadMetrics = useCallback(async () => {
    try {
      const cacheMetrics = getCacheMetrics();
      const cacheInfo = getCacheInfo();

      setMetrics(cacheMetrics);

      // Calculate performance stats
      const hitRatio = cacheMetrics.totalHits > 0
        ? (cacheMetrics.totalHits / (cacheMetrics.totalHits + cacheMetrics.totalItems)) * 100
        : 0;

      const memoryEfficiencyMB = cacheMetrics.memoryUsage / (1024 * 1024);
      const systemHealth = getSystemHealth(hitRatio, memoryEfficiencyMB, cacheMetrics.expiredItems);

      setPerformanceStats({
        cacheHitRatio: hitRatio,
        averageResponseTime: 150, // Simulated - would come from real metrics
        memoryEfficiency: memoryEfficiencyMB,
        systemHealth
      });

      setLastUpdated(new Date());
    } catch (error) {
      cacheLogger.error('Erro ao carregar métricas de performance:', error);
    }
  }, []);

  const getSystemHealth = (hitRatio: number, memoryMB: number, expiredItems: number): PerformanceStats['systemHealth'] => {
    if (hitRatio > 80 && memoryMB < 50 && expiredItems < 10) return 'excellent';
    if (hitRatio > 60 && memoryMB < 100 && expiredItems < 25) return 'good';
    if (hitRatio > 40 && memoryMB < 200 && expiredItems < 50) return 'warning';
    return 'critical';
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await loadMetrics();
    setIsRefreshing(false);
  };

  const handleCleanupCache = async () => {
    try {
      const removedCount = cleanupExpiredCache();
      cacheLogger.info(`Limpeza manual: ${removedCount} itens expirados removidos`);
      await loadMetrics();
    } catch (error) {
      cacheLogger.error('Erro na limpeza do cache:', error);
    }
  };

  const handleClearAllCache = async () => {
    try {
      clearCache();
      cacheLogger.info('Todo o cache foi limpo manualmente');
      await loadMetrics();
    } catch (error) {
      cacheLogger.error('Erro ao limpar todo o cache:', error);
    }
  };

  const handleWarmupCache = async () => {
    setIsWarmingUp(true);
    try {
      await warmupApplicationCache(DEFAULT_WARMUP_CONFIG);
      await loadMetrics();
    } catch (error) {
      cacheLogger.error('Erro no aquecimento do cache:', error);
    } finally {
      setIsWarmingUp(false);
    }
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getHealthColor = (health: PerformanceStats['systemHealth']) => {
    switch (health) {
      case 'excellent': return 'text-green-600 bg-green-100';
      case 'good': return 'text-blue-600 bg-blue-100';
      case 'warning': return 'text-yellow-600 bg-yellow-100';
      case 'critical': return 'text-red-600 bg-red-100';
    }
  };

  const getHealthLabel = (health: PerformanceStats['systemHealth']) => {
    switch (health) {
      case 'excellent': return 'Excelente';
      case 'good': return 'Bom';
      case 'warning': return 'Atenção';
      case 'critical': return 'Crítico';
    }
  };

  if (!metrics || !performanceStats) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Monitoramento de Performance</h2>
          <p className="text-muted-foreground">
            Última atualização: {lastUpdated.toLocaleTimeString()}
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={isRefreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            Atualizar
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleWarmupCache}
            disabled={isWarmingUp}
          >
            <Zap className={`h-4 w-4 mr-2 ${isWarmingUp ? 'animate-pulse' : ''}`} />
            Aquecer Cache
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Taxa de Acerto</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{performanceStats.cacheHitRatio.toFixed(1)}%</div>
            <Progress value={performanceStats.cacheHitRatio} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Itens em Cache</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.activeItems}</div>
            <p className="text-xs text-muted-foreground">
              {metrics.expiredItems} expirados
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Uso de Memória</CardTitle>
            <HardDrive className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatBytes(metrics.memoryUsage)}</div>
            <p className="text-xs text-muted-foreground">
              {performanceStats.memoryEfficiency.toFixed(1)} MB
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Status do Sistema</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <Badge className={getHealthColor(performanceStats.systemHealth)}>
              {getHealthLabel(performanceStats.systemHealth)}
            </Badge>
            <p className="text-xs text-muted-foreground mt-2">
              {metrics.totalHits} acessos totais
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Tabs */}
      <Tabs defaultValue="cache" className="space-y-4">
        <TabsList>
          <TabsTrigger value="cache">Cache</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="actions">Ações</TabsTrigger>
        </TabsList>

        <TabsContent value="cache" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Distribuição por Tipo de Dados</CardTitle>
              <CardDescription>
                Quantidade de itens em cache por categoria
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {Object.entries(metrics.itemsByType).map(([type, count]) => (
                  <div key={type} className="flex items-center justify-between">
                    <span className="text-sm font-medium capitalize">{type}</span>
                    <div className="flex items-center gap-2">
                      <div className="w-24 bg-secondary rounded-full h-2">
                        <div
                          className="bg-primary h-2 rounded-full"
                          style={{ width: `${(count / metrics.totalItems) * 100}%` }}
                        />
                      </div>
                      <span className="text-sm text-muted-foreground w-8 text-right">{count}</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Métricas de Acesso</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span>Total de Acessos:</span>
                  <span className="font-medium">{metrics.totalHits}</span>
                </div>
                <div className="flex justify-between">
                  <span>Média por Item:</span>
                  <span className="font-medium">{metrics.averageHitsPerItem.toFixed(1)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Tempo de Resposta:</span>
                  <span className="font-medium">{performanceStats.averageResponseTime}ms</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Eficiência de Memória</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span>Uso Total:</span>
                  <span className="font-medium">{formatBytes(metrics.memoryUsage)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Por Item:</span>
                  <span className="font-medium">
                    {formatBytes(metrics.memoryUsage / metrics.totalItems)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Eficiência:</span>
                  <span className="font-medium">
                    {((metrics.activeItems / metrics.totalItems) * 100).toFixed(1)}%
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="actions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Ações de Manutenção</CardTitle>
              <CardDescription>
                Ferramentas para otimizar e gerenciar o cache do sistema
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <Button
                  variant="outline"
                  onClick={handleCleanupCache}
                  className="justify-start"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Limpar Itens Expirados
                </Button>

                <Button
                  variant="outline"
                  onClick={handleClearAllCache}
                  className="justify-start"
                >
                  <Database className="h-4 w-4 mr-2" />
                  Limpar Todo o Cache
                </Button>

                <Button
                  variant="outline"
                  onClick={handleWarmupCache}
                  disabled={isWarmingUp}
                  className="justify-start"
                >
                  <Zap className={`h-4 w-4 mr-2 ${isWarmingUp ? 'animate-pulse' : ''}`} />
                  Aquecer Cache
                </Button>

                <Button
                  variant="outline"
                  onClick={handleRefresh}
                  disabled={isRefreshing}
                  className="justify-start"
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
                  Atualizar Métricas
                </Button>
              </div>

              {metrics.expiredItems > 0 && (
                <div className="mt-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                  <p className="text-sm text-yellow-800 dark:text-yellow-200">
                    <strong>Atenção:</strong> Existem {metrics.expiredItems} itens expirados no cache.
                    Considere executar uma limpeza para otimizar a performance.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
