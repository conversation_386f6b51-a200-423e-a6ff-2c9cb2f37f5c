import { Link, useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import { <PERSON>, User, <PERSON>, Sun, Plus } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useAuth } from "@/contexts/AuthContext";
import { useTheme } from "next-themes";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { GlobalSearch } from "@/components/global-search";

interface TopNavProps {
  title: string;
  icon?: React.ReactNode;
  children?: React.ReactNode;
  actions?: React.ReactNode;
}

export function TopNav({ title, icon, children, actions }: TopNavProps) {
  const location = useLocation();
  const topNavHeight = "65px";
  const { user } = useAuth();
  const { theme, setTheme } = useTheme();

  const toggleTheme = () => {
    setTheme(theme === "dark" ? "light" : "dark");
  };

  return (
    <motion.header
      initial={{ y: -10, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.3 }}
      className="fixed top-0 left-0 right-0 z-50 bg-background/95 backdrop-blur-sm border-b shadow-sm flex items-center px-2 sm:px-4 md:px-6 glass-effect"
      style={{ height: topNavHeight }}
    >
      <div className="flex items-center gap-2 md:gap-4 mr-auto min-w-0 flex-1">
        <Link to="/dashboard" className="flex items-center gap-1 md:gap-2 mr-2 md:mr-4 group flex-shrink-0">
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="transition-colors"
          >
            {icon}
          </motion.div>
          <span className="font-semibold text-sm md:text-lg hidden sm:inline group-hover:text-primary transition-colors truncate">{title}</span>
        </Link>
        <div className="min-w-0 flex-1">
          {children}
        </div>
      </div>

      {/* Ações customizadas ou ações rápidas padrão */}
      <div className="hidden lg:flex items-center mx-2 gap-2">
        {actions || (
          <>
            <Link to="/precatorios/novo">
              <Button variant="outline" size="sm" className="bg-primary/10 hover:bg-primary/20 border-primary/20">
                <Plus className="h-4 w-4 mr-2" />
                Novo Precatório
              </Button>
            </Link>
            <Link to="/clientes/novo">
              <Button variant="outline" size="sm" className="bg-primary/5 hover:bg-primary/10 border-primary/10">
                <Plus className="h-4 w-4 mr-2" />
                Novo Cliente
              </Button>
            </Link>
          </>
        )}
      </div>

      {/* Barra de pesquisa global - apenas em telas grandes */}
      <div className="hidden lg:flex items-center mx-2 relative max-w-sm flex-1">
        <GlobalSearch />
      </div>

      {/* Ícones de ação */}
      <div className="flex items-center gap-1 md:gap-2">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="rounded-full hover:bg-muted h-8 w-8 md:h-10 md:w-10"
                onClick={toggleTheme}
              >
                {theme === "dark" ? (
                  <Sun className="h-4 w-4 md:h-5 md:w-5" />
                ) : (
                  <Moon className="h-4 w-4 md:h-5 md:w-5" />
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent side="bottom">
              <p>{theme === "dark" ? "Modo claro" : "Modo escuro"}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        {/* Notificações - ocultar em telas muito pequenas */}
        <div className="hidden sm:block">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" className="rounded-full hover:bg-muted relative h-8 w-8 md:h-10 md:w-10">
                  <Bell className="h-4 w-4 md:h-5 md:w-5" />
                  <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-3 w-3 md:h-4 md:w-4 flex items-center justify-center text-[10px] md:text-xs">3</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom">
                <p>Notificações</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>

        <Link to="/profile" className="flex items-center gap-2 ml-1 md:ml-2">
          <Avatar className="h-7 w-7 md:h-8 md:w-8 transition-transform hover:scale-110">
            <AvatarImage src={user?.foto_url || ""} />
            <AvatarFallback className="bg-primary text-primary-foreground text-xs md:text-sm">
              {user?.nome ? user.nome.substring(0, 2).toUpperCase() : "U"}
            </AvatarFallback>
          </Avatar>
        </Link>
      </div>
    </motion.header>
  );
}
