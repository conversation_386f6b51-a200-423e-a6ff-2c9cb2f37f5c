import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Users,
  CircleDollarSign,
  FileText,
  CheckCircle2,
  TrendingUp,
  TrendingDown,
  Bar<PERSON>hart as BarChartIcon,
  PieChart as PieChartIcon,
  AreaChart as AreaChartIcon,
  LineChart as LineChartIcon,
} from "lucide-react";
import { formatarMoeda } from "@/lib/utils";
import { CHART_COLORS, getStatusColor } from "@/constants/chartColors";
import {
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  ResponsiveContainer,
  XAxis,
  YAxis,
  Tooltip,
  Legend,
  CartesianGrid,
  AreaChart,
  Area,
  LineChart,
  Line,
} from "recharts";
import { useState } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { But<PERSON> } from "@/components/ui/button";

interface ClientesEstatisticas {
  total: number;
  ativos: number;
  inativos: number;
  pendentes: number;
  valorTotal: number;
  precatoriosTotal: number;
  precatoriosConcluidos: number;
  precatoriosEmAndamento: number;
}

interface ClientsStatisticsProps {
  estatisticas: ClientesEstatisticas;
  className?: string;
}

export function ClientsStatistics({ estatisticas, className = "" }: ClientsStatisticsProps) {
  // Estados para controlar o tipo de gráfico
  const [statusChartType, setStatusChartType] = useState<"pie" | "donut" | "bar">("donut");
  const [precatoriosChartType, setPrecatoriosChartType] = useState<"bar" | "area" | "line">("area");

  // Dados para o gráfico de status
  const statusData = [
    { name: "Ativos", value: estatisticas.ativos, color: CHART_COLORS.primary.main },
    { name: "Inativos", value: estatisticas.inativos, color: CHART_COLORS.secondary.main },
    { name: "Pendentes", value: estatisticas.pendentes, color: CHART_COLORS.primary.light },
  ];

  // Dados para o gráfico de precatórios
  const precatoriosData = [
    { name: "Concluídos", value: estatisticas.precatoriosConcluidos, color: CHART_COLORS.primary.dark },
    { name: "Em Andamento", value: estatisticas.precatoriosEmAndamento, color: CHART_COLORS.primary.main },
  ];

  // Dados para gráfico de área/linha (mockado por enquanto)
  const historicoClientes = [
    { mes: "Jan", ativos: 42, inativos: 12, pendentes: 5 },
    { mes: "Fev", ativos: 48, inativos: 10, pendentes: 8 },
    { mes: "Mar", ativos: 55, inativos: 15, pendentes: 10 },
    { mes: "Abr", ativos: 62, inativos: 18, pendentes: 12 },
    { mes: "Mai", ativos: 70, inativos: 20, pendentes: 15 },
    { mes: "Jun", ativos: 78, inativos: 22, pendentes: 18 },
  ];

  // Calcular variação (mockado por enquanto)
  const variacao = {
    clientes: 5.2,
    valor: 12.8,
    precatorios: -3.5,
    concluidos: 8.7,
  };

  return (
    <div className={`grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4 ${className}`}>
      {/* Total de Clientes */}
      <Card>
        <CardContent className="flex flex-row items-center justify-between p-6">
          <div className="flex flex-col gap-1">
            <p className="text-sm text-muted-foreground">Total de Clientes</p>
            <div className="flex items-center gap-2">
              <p className="text-2xl font-bold">{estatisticas.total}</p>
              {variacao.clientes !== 0 && (
                <Badge
                  variant="outline"
                  className={`text-xs ${
                    variacao.clientes > 0
                      ? "text-green-600 dark:text-green-400"
                      : "text-red-600 dark:text-red-400"
                  }`}
                >
                  {variacao.clientes > 0 ? (
                    <TrendingUp className="mr-1 h-3 w-3" />
                  ) : (
                    <TrendingDown className="mr-1 h-3 w-3" />
                  )}
                  {Math.abs(variacao.clientes)}%
                </Badge>
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              {estatisticas.ativos} ativos
            </p>
          </div>
          <div className="rounded-full bg-muted p-3">
            <Users className="w-4 h-4" />
          </div>
        </CardContent>
      </Card>

      {/* Valor Total */}
      <Card>
        <CardContent className="flex flex-row items-center justify-between p-6">
          <div className="flex flex-col gap-1">
            <p className="text-sm text-muted-foreground">Valor Total</p>
            <div className="flex items-center gap-2">
              <p className="text-2xl font-bold">
                {formatarMoeda(estatisticas.valorTotal)}
              </p>
              {variacao.valor !== 0 && (
                <Badge
                  variant="outline"
                  className={`text-xs ${
                    variacao.valor > 0
                      ? "text-green-600 dark:text-green-400"
                      : "text-red-600 dark:text-red-400"
                  }`}
                >
                  {variacao.valor > 0 ? (
                    <TrendingUp className="mr-1 h-3 w-3" />
                  ) : (
                    <TrendingDown className="mr-1 h-3 w-3" />
                  )}
                  {Math.abs(variacao.valor)}%
                </Badge>
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              Em precatórios
            </p>
          </div>
          <div className="rounded-full bg-muted p-3">
            <CircleDollarSign className="w-4 h-4" />
          </div>
        </CardContent>
      </Card>

      {/* Precatórios */}
      <Card>
        <CardContent className="flex flex-row items-center justify-between p-6">
          <div className="flex flex-col gap-1">
            <p className="text-sm text-muted-foreground">Precatórios</p>
            <div className="flex items-center gap-2">
              <p className="text-2xl font-bold">{estatisticas.precatoriosTotal}</p>
              {variacao.precatorios !== 0 && (
                <Badge
                  variant="outline"
                  className={`text-xs ${
                    variacao.precatorios > 0
                      ? "text-green-600 dark:text-green-400"
                      : "text-red-600 dark:text-red-400"
                  }`}
                >
                  {variacao.precatorios > 0 ? (
                    <TrendingUp className="mr-1 h-3 w-3" />
                  ) : (
                    <TrendingDown className="mr-1 h-3 w-3" />
                  )}
                  {Math.abs(variacao.precatorios)}%
                </Badge>
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              {estatisticas.precatoriosEmAndamento} em andamento
            </p>
          </div>
          <div className="rounded-full bg-muted p-3">
            <FileText className="w-4 h-4" />
          </div>
        </CardContent>
      </Card>

      {/* Concluídos */}
      <Card>
        <CardContent className="flex flex-row items-center justify-between p-6">
          <div className="flex flex-col gap-1">
            <p className="text-sm text-muted-foreground">Concluídos</p>
            <div className="flex items-center gap-2">
              <p className="text-2xl font-bold">{estatisticas.precatoriosConcluidos}</p>
              <Badge variant="default" className="text-xs">
                {estatisticas.precatoriosTotal > 0
                  ? Math.round((estatisticas.precatoriosConcluidos / estatisticas.precatoriosTotal) * 100)
                  : 0}%
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground">
              Taxa de conclusão
            </p>
          </div>
          <div className="rounded-full bg-muted p-3">
            <CheckCircle2 className="w-4 h-4" />
          </div>
        </CardContent>
      </Card>

      {/* Gráficos */}
      <Card className="col-span-1 md:col-span-2">
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <div className="space-y-1">
            <CardTitle className="text-lg">Distribuição por Status</CardTitle>
            <CardDescription>
              Visualização dos clientes por status
            </CardDescription>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="ml-auto h-8 gap-1">
                {statusChartType === "pie" && <PieChartIcon className="h-4 w-4" />}
                {statusChartType === "donut" && <PieChartIcon className="h-4 w-4" />}
                {statusChartType === "bar" && <BarChartIcon className="h-4 w-4" />}
                Tipo
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => setStatusChartType("pie")}>
                <PieChartIcon className="h-4 w-4 mr-2" />
                Gráfico de Pizza
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setStatusChartType("donut")}>
                <PieChartIcon className="h-4 w-4 mr-2" />
                Gráfico de Rosca
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setStatusChartType("bar")}>
                <BarChartIcon className="h-4 w-4 mr-2" />
                Gráfico de Barras
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </CardHeader>
        <CardContent>
          <div className="h-[250px]">
            {statusChartType === "pie" && (
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={statusData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={90}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  >
                    {statusData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip
                    formatter={(value) => [`${value} clientes`, "Quantidade"]}
                  />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            )}

            {statusChartType === "donut" && (
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={statusData}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={90}
                    paddingAngle={5}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  >
                    {statusData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip
                    formatter={(value) => [`${value} clientes`, "Quantidade"]}
                  />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            )}

            {statusChartType === "bar" && (
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={statusData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip
                    formatter={(value) => [`${value} clientes`, "Quantidade"]}
                  />
                  <Legend />
                  <Bar dataKey="value" name="Quantidade">
                    {statusData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            )}
          </div>
        </CardContent>
      </Card>

      <Card className="col-span-1 md:col-span-2">
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <div className="space-y-1">
            <CardTitle className="text-lg">Evolução de Precatórios</CardTitle>
            <CardDescription>
              Acompanhamento dos precatórios por status
            </CardDescription>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="ml-auto h-8 gap-1">
                {precatoriosChartType === "area" && <AreaChartIcon className="h-4 w-4" />}
                {precatoriosChartType === "line" && <LineChartIcon className="h-4 w-4" />}
                {precatoriosChartType === "bar" && <BarChartIcon className="h-4 w-4" />}
                Tipo
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => setPrecatoriosChartType("area")}>
                <AreaChartIcon className="h-4 w-4 mr-2" />
                Gráfico de Área
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setPrecatoriosChartType("line")}>
                <LineChartIcon className="h-4 w-4 mr-2" />
                Gráfico de Linha
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setPrecatoriosChartType("bar")}>
                <BarChartIcon className="h-4 w-4 mr-2" />
                Gráfico de Barras
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </CardHeader>
        <CardContent>
          <div className="h-[250px]">
            {precatoriosChartType === "area" && (
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart data={historicoClientes}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="mes" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Area
                    type="monotone"
                    dataKey="ativos"
                    name="Ativos"
                    stackId="1"
                    stroke={CHART_COLORS.primary.main}
                    fill={CHART_COLORS.primary.main}
                    fillOpacity={0.6}
                  />
                  <Area
                    type="monotone"
                    dataKey="pendentes"
                    name="Pendentes"
                    stackId="1"
                    stroke={CHART_COLORS.primary.light}
                    fill={CHART_COLORS.primary.light}
                    fillOpacity={0.6}
                  />
                  <Area
                    type="monotone"
                    dataKey="inativos"
                    name="Inativos"
                    stackId="1"
                    stroke={CHART_COLORS.secondary.main}
                    fill={CHART_COLORS.secondary.main}
                    fillOpacity={0.6}
                  />
                </AreaChart>
              </ResponsiveContainer>
            )}

            {precatoriosChartType === "line" && (
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={historicoClientes}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="mes" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="ativos"
                    name="Ativos"
                    stroke={CHART_COLORS.primary.main}
                    strokeWidth={2}
                    dot={{ r: 4 }}
                    activeDot={{ r: 6 }}
                  />
                  <Line
                    type="monotone"
                    dataKey="pendentes"
                    name="Pendentes"
                    stroke={CHART_COLORS.primary.light}
                    strokeWidth={2}
                    dot={{ r: 4 }}
                    activeDot={{ r: 6 }}
                  />
                  <Line
                    type="monotone"
                    dataKey="inativos"
                    name="Inativos"
                    stroke={CHART_COLORS.secondary.main}
                    strokeWidth={2}
                    dot={{ r: 4 }}
                    activeDot={{ r: 6 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            )}

            {precatoriosChartType === "bar" && (
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={historicoClientes}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="mes" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="ativos" name="Ativos" fill={CHART_COLORS.primary.main} />
                  <Bar dataKey="pendentes" name="Pendentes" fill={CHART_COLORS.primary.light} />
                  <Bar dataKey="inativos" name="Inativos" fill={CHART_COLORS.secondary.main} />
                </BarChart>
              </ResponsiveContainer>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
