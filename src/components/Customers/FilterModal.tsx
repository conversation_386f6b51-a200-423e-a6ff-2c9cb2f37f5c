import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { CalendarIcon, Filter, X } from "lucide-react";
import { cn } from "@/lib/utils";

export interface ClientesFiltros {
  status: string[];
  tipo: string[];
  valorMin?: number;
  valorMax?: number;
  dataInicio?: Date;
  dataFim?: Date;
  precatoriosMin?: number;
  precatoriosMax?: number;
}

interface FilterModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  filtros: ClientesFiltros;
  onAplicarFiltros: (filtros: ClientesFiltros) => void;
  onLimparFiltros: () => void;
  estatisticas: {
    total: number;
    ativos: number;
    inativos: number;
    pendentes: number;
    valorTotal: number;
    precatoriosTotal: number;
  };
}

export function FilterModal({
  isOpen,
  onOpenChange,
  filtros,
  onAplicarFiltros,
  onLimparFiltros,
  estatisticas,
}: FilterModalProps) {
  const [filtrosTemp, setFiltrosTemp] = useState<ClientesFiltros>({ ...filtros });

  // Reset filtros temporários quando o modal abrir
  useEffect(() => {
    setFiltrosTemp({ ...filtros });
  }, [filtros, isOpen]);

  const handleStatusChange = (status: string, checked: boolean) => {
    if (checked) {
      setFiltrosTemp((prev) => ({
        ...prev,
        status: [...prev.status, status],
      }));
    } else {
      setFiltrosTemp((prev) => ({
        ...prev,
        status: prev.status.filter((s) => s !== status),
      }));
    }
  };

  const handleTipoChange = (tipo: string, checked: boolean) => {
    if (checked) {
      setFiltrosTemp((prev) => ({
        ...prev,
        tipo: [...prev.tipo, tipo],
      }));
    } else {
      setFiltrosTemp((prev) => ({
        ...prev,
        tipo: prev.tipo.filter((t) => t !== tipo),
      }));
    }
  };

  const handleInputChange = (campo: keyof ClientesFiltros, valor: any) => {
    setFiltrosTemp((prev) => ({
      ...prev,
      [campo]: valor,
    }));
  };

  const handleResetarFiltros = () => {
    const filtrosVazios: ClientesFiltros = {
      status: [],
      tipo: [],
      valorMin: undefined,
      valorMax: undefined,
      dataInicio: undefined,
      dataFim: undefined,
      precatoriosMin: undefined,
      precatoriosMax: undefined,
    };
    setFiltrosTemp(filtrosVazios);
    onLimparFiltros();
  };

  const handleAplicarFiltros = () => {
    onAplicarFiltros(filtrosTemp);
    onOpenChange(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filtros Avançados
          </DialogTitle>
          <DialogDescription>
            Filtre os clientes por diferentes critérios para encontrar exatamente o que precisa.
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-6 py-4">
          {/* Status */}
          <div className="space-y-2">
            <h3 className="text-sm font-medium">Status do Cliente</h3>
            <div className="grid grid-cols-2 gap-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="status-ativo"
                  checked={filtrosTemp.status.includes("ativo")}
                  onCheckedChange={(checked) => handleStatusChange("ativo", !!checked)}
                />
                <Label htmlFor="status-ativo" className="flex items-center justify-between flex-1">
                  <span>Ativos</span>
                  <Badge variant="outline">{estatisticas.ativos}</Badge>
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="status-inativo"
                  checked={filtrosTemp.status.includes("inativo")}
                  onCheckedChange={(checked) => handleStatusChange("inativo", !!checked)}
                />
                <Label htmlFor="status-inativo" className="flex items-center justify-between flex-1">
                  <span>Inativos</span>
                  <Badge variant="outline">{estatisticas.inativos}</Badge>
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="status-pendente"
                  checked={filtrosTemp.status.includes("pendente")}
                  onCheckedChange={(checked) => handleStatusChange("pendente", !!checked)}
                />
                <Label htmlFor="status-pendente" className="flex items-center justify-between flex-1">
                  <span>Pendentes</span>
                  <Badge variant="outline">{estatisticas.pendentes}</Badge>
                </Label>
              </div>
            </div>
          </div>

          <Separator />

          {/* Tipo */}
          <div className="space-y-2">
            <h3 className="text-sm font-medium">Tipo de Cliente</h3>
            <div className="grid grid-cols-2 gap-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="tipo-pf"
                  checked={filtrosTemp.tipo.includes("pessoa_fisica")}
                  onCheckedChange={(checked) => handleTipoChange("pessoa_fisica", !!checked)}
                />
                <Label htmlFor="tipo-pf">Pessoa Física</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="tipo-pj"
                  checked={filtrosTemp.tipo.includes("pessoa_juridica")}
                  onCheckedChange={(checked) => handleTipoChange("pessoa_juridica", !!checked)}
                />
                <Label htmlFor="tipo-pj">Pessoa Jurídica</Label>
              </div>
            </div>
          </div>

          <Separator />

          {/* Valor */}
          <div className="space-y-2">
            <h3 className="text-sm font-medium">Valor Total em Precatórios</h3>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="valor-min">Valor Mínimo</Label>
                <Input
                  id="valor-min"
                  type="number"
                  placeholder="R$ 0,00"
                  value={filtrosTemp.valorMin || ""}
                  onChange={(e) => handleInputChange("valorMin", e.target.value ? Number(e.target.value) : undefined)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="valor-max">Valor Máximo</Label>
                <Input
                  id="valor-max"
                  type="number"
                  placeholder="R$ 1.000.000,00"
                  value={filtrosTemp.valorMax || ""}
                  onChange={(e) => handleInputChange("valorMax", e.target.value ? Number(e.target.value) : undefined)}
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* Quantidade de Precatórios */}
          <div className="space-y-2">
            <h3 className="text-sm font-medium">Quantidade de Precatórios</h3>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="precatorios-min">Mínimo</Label>
                <Input
                  id="precatorios-min"
                  type="number"
                  placeholder="0"
                  value={filtrosTemp.precatoriosMin || ""}
                  onChange={(e) => handleInputChange("precatoriosMin", e.target.value ? Number(e.target.value) : undefined)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="precatorios-max">Máximo</Label>
                <Input
                  id="precatorios-max"
                  type="number"
                  placeholder="100"
                  value={filtrosTemp.precatoriosMax || ""}
                  onChange={(e) => handleInputChange("precatoriosMax", e.target.value ? Number(e.target.value) : undefined)}
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* Data de Cadastro */}
          <div className="space-y-2">
            <h3 className="text-sm font-medium">Data de Cadastro</h3>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="data-inicio">Data Inicial</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      id="data-inicio"
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !filtrosTemp.dataInicio && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {filtrosTemp.dataInicio ? (
                        format(filtrosTemp.dataInicio, "dd/MM/yyyy", { locale: ptBR })
                      ) : (
                        <span>Selecione uma data</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={filtrosTemp.dataInicio}
                      onSelect={(date) => handleInputChange("dataInicio", date)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
              <div className="space-y-2">
                <Label htmlFor="data-fim">Data Final</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      id="data-fim"
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !filtrosTemp.dataFim && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {filtrosTemp.dataFim ? (
                        format(filtrosTemp.dataFim, "dd/MM/yyyy", { locale: ptBR })
                      ) : (
                        <span>Selecione uma data</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={filtrosTemp.dataFim}
                      onSelect={(date) => handleInputChange("dataFim", date)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter className="flex justify-between">
          <Button variant="outline" onClick={handleResetarFiltros} className="gap-1">
            <X className="h-4 w-4" />
            Limpar Filtros
          </Button>
          <Button onClick={handleAplicarFiltros} className="gap-1">
            <Filter className="h-4 w-4" />
            Aplicar Filtros
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
