import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Mail,
  Phone,
  FileText,
  CircleDollarSign,
  MoreHorizontal,
  ArrowUpRight,
  Download,
  Upload,
  Calendar,
} from "lucide-react";
import { formatarMoeda } from "@/lib/utils";
import { format, parseISO } from "date-fns";
import { ptBR } from "date-fns/locale";

interface Cliente {
  id: string;
  nome: string;
  email: string;
  telefone: string;
  documento: string;
  tipo: "pessoa_fisica" | "pessoa_juridica";
  status: "ativo" | "inativo" | "pendente";
  dataCriacao: string;
  dataAtualizacao: string;
  precatorios?: number;
  valorTotal?: number;
}

interface ClientCardProps {
  cliente: Cliente;
  onClick: (clienteId: string) => void;
}

export function ClientCard({ cliente, onClick }: ClientCardProps) {
  const [isHovered, setIsHovered] = useState(false);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "ativo":
        return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400";
      case "inativo":
        return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400";
      case "pendente":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-400";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "ativo":
        return "Ativo";
      case "inativo":
        return "Inativo";
      case "pendente":
        return "Pendente";
      default:
        return status;
    }
  };

  const formatarData = (dataString: string) => {
    try {
      return format(parseISO(dataString), "dd/MM/yyyy", { locale: ptBR });
    } catch (error) {
      return "Data inválida";
    }
  };

  return (
    <Card
      className={`overflow-hidden transition-all duration-200 ${
        isHovered ? "shadow-md" : ""
      }`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <CardContent className="p-6">
        <div className="flex justify-between items-start mb-4">
          <div className="flex items-center gap-3">
            <Avatar className="h-12 w-12">
              <AvatarImage src={`/avatars/${cliente.nome.toLowerCase().replace(" ", "_")}.jpg`} />
              <AvatarFallback>{cliente.nome.split(" ").map((n) => n[0]).join("")}</AvatarFallback>
            </Avatar>
            <div>
              <h3 className="font-medium text-lg">{cliente.nome}</h3>
              <p className="text-sm text-muted-foreground">{cliente.documento}</p>
            </div>
          </div>
          <Badge className={getStatusColor(cliente.status)}>{getStatusText(cliente.status)}</Badge>
        </div>

        <div className="grid grid-cols-2 gap-3 mb-4">
          <div className="flex items-center gap-2">
            <Mail className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm truncate" title={cliente.email}>
              {cliente.email}
            </span>
          </div>
          <div className="flex items-center gap-2">
            <Phone className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm">{cliente.telefone}</span>
          </div>
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm">{formatarData(cliente.dataCriacao)}</span>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline">
              {cliente.tipo === "pessoa_fisica" ? "Pessoa Física" : "Pessoa Jurídica"}
            </Badge>
          </div>
        </div>

        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <FileText className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">Precatórios:</span>
            </div>
            <span className="text-sm font-medium">{cliente.precatorios || 0}</span>
          </div>
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <CircleDollarSign className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">Valor Total:</span>
            </div>
            <span className="text-sm font-medium">{formatarMoeda(cliente.valorTotal || 0)}</span>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between p-4 pt-0 border-t mt-4">
        <Button variant="outline" size="sm" onClick={() => onClick(cliente.id)}>
          <ArrowUpRight className="mr-2 h-4 w-4" />
          Detalhes
        </Button>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <MoreHorizontal className="h-4 w-4" />
              <span className="sr-only">Abrir menu</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={(e) => {
              e.stopPropagation();
              onClick(cliente.id);
            }}>
              <ArrowUpRight className="mr-2 h-4 w-4" />
              <span>Ver detalhes</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={(e) => e.stopPropagation()}>
              <Download className="mr-2 h-4 w-4" />
              <span>Exportar dados</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={(e) => e.stopPropagation()}>
              <Upload className="mr-2 h-4 w-4" />
              <span>Importar precatórios</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </CardFooter>
    </Card>
  );
}
