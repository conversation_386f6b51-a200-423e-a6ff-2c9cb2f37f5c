import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogDescription, Di<PERSON>Footer, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Loader2, Save, X } from "lucide-react";
import { toast } from "sonner";
import { useAuth } from "@/contexts/AuthContext";

type UserRole =
  | "admin"
  | "gerente_geral"
  | "gerente_precatorio"
  | "gerente_rpv"
  | "captador"
  | "operacional_precatorio"
  | "operacional_rpv"
  | "operacional_completo";

interface UserProfile {
  id: string;
  email: string;
  nome?: string;
  role: UserRole;
  foto_url?: string;
  status?: string;
  created_at?: string;
  updated_at?: string;
  cargo?: string;
  departamento?: string;
  telefone?: string;
  data_entrada?: string;
  custom_role_id?: string;
}

interface EditUserModalProps {
  isOpen: boolean;
  onClose: () => void;
  user: UserProfile | null;
  onUserUpdated?: (updatedUser: UserProfile) => void;
}

export function EditUserModal({ isOpen, onClose, user, onUserUpdated }: EditUserModalProps) {
  const { updateUser } = useAuth();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<Partial<UserProfile>>({});
  const [formErrors, setFormErrors] = useState<{[key: string]: string}>({});

  // Resetar formulário quando o usuário muda
  useEffect(() => {
    if (user) {
      setFormData({
        nome: user.nome || '',
        email: user.email || '',
        role: user.role,
        cargo: user.cargo || '',
        departamento: user.departamento || '',
        telefone: user.telefone || '',
        data_entrada: user.data_entrada ? user.data_entrada.split('T')[0] : '',
        foto_url: user.foto_url || '',
        status: user.status || 'ativo'
      });
    }
    setFormErrors({});
  }, [user]);

  const roleDisplayName = (role: UserRole): string => {
    const roleNames: Record<UserRole, string> = {
      admin: "Administrador",
      gerente_geral: "Gerente Geral",
      gerente_precatorio: "Gerente de Precatório",
      gerente_rpv: "Gerente de RPV",
      captador: "Captador",
      operacional_precatorio: "Operacional - Precatório",
      operacional_rpv: "Operacional - RPV",
      operacional_completo: "Operacional - Completo"
    };
    return roleNames[role] || role;
  };

  const handleSubmit = async () => {
    if (!user) return;

    // Resetar erros
    setFormErrors({});

    // Validações
    const errors: {[key: string]: string} = {};
    
    if (!formData.nome?.trim()) {
      errors.nome = "Nome é obrigatório";
    }

    if (!formData.email?.trim()) {
      errors.email = "E-mail é obrigatório";
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = "Formato de e-mail inválido";
    }

    if (!formData.role) {
      errors.role = "Cargo é obrigatório";
    }

    if (formData.telefone && !/^[\d\s\(\)\-\+]+$/.test(formData.telefone)) {
      errors.telefone = "Formato de telefone inválido";
    }

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    try {
      setLoading(true);

      const { error, user: updatedUser } = await updateUser(user.id, formData);

      if (error) {
        toast.error("Erro ao atualizar usuário", {
          description: error
        });
        return;
      }

      if (!updatedUser) {
        toast.error("Erro ao atualizar usuário", {
          description: "Não foi possível obter os dados atualizados."
        });
        return;
      }

      toast.success("Usuário atualizado com sucesso!", {
        description: `${formData.nome} foi atualizado.`
      });

      onUserUpdated?.(updatedUser);
      onClose();
    } catch (error: any) {
      console.error("Erro ao atualizar usuário:", error);
      toast.error("Erro ao atualizar usuário", {
        description: error instanceof Error ? error.message : "Ocorreu um erro desconhecido"
      });
    } finally {
      setLoading(false);
    }
  };

  if (!user) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <Avatar className="h-10 w-10">
              <AvatarImage src={formData.foto_url || user.foto_url || ""} />
              <AvatarFallback>
                {(formData.nome || user.nome || "U").slice(0, 2).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div>
              <h2 className="text-xl font-semibold">Editar Usuário</h2>
              <p className="text-sm text-muted-foreground">{user.email}</p>
            </div>
          </DialogTitle>
          <DialogDescription>
            Edite as informações do usuário. Campos marcados com * são obrigatórios.
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          {/* Nome */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="nome" className="text-right">
              Nome <span className="text-red-500">*</span>
            </Label>
            <div className="col-span-3 space-y-1">
              <Input
                id="nome"
                value={formData.nome || ''}
                onChange={(e) => {
                  setFormData({ ...formData, nome: e.target.value });
                  if (formErrors.nome) {
                    setFormErrors({ ...formErrors, nome: '' });
                  }
                }}
                className={formErrors.nome ? "border-red-500" : ""}
              />
              {formErrors.nome && (
                <p className="text-sm text-red-500">{formErrors.nome}</p>
              )}
            </div>
          </div>

          {/* E-mail */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="email" className="text-right">
              E-mail <span className="text-red-500">*</span>
            </Label>
            <div className="col-span-3 space-y-1">
              <Input
                id="email"
                type="email"
                value={formData.email || ''}
                onChange={(e) => {
                  setFormData({ ...formData, email: e.target.value });
                  if (formErrors.email) {
                    setFormErrors({ ...formErrors, email: '' });
                  }
                }}
                className={formErrors.email ? "border-red-500" : ""}
              />
              {formErrors.email && (
                <p className="text-sm text-red-500">{formErrors.email}</p>
              )}
            </div>
          </div>

          {/* Cargo/Role */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="role" className="text-right">
              Cargo <span className="text-red-500">*</span>
            </Label>
            <div className="col-span-3 space-y-1">
              <Select
                value={formData.role || ''}
                onValueChange={(value: UserRole) => {
                  setFormData({ ...formData, role: value });
                  if (formErrors.role) {
                    setFormErrors({ ...formErrors, role: '' });
                  }
                }}
              >
                <SelectTrigger className={formErrors.role ? "border-red-500" : ""}>
                  <SelectValue placeholder="Selecione um cargo" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="admin">Administrador</SelectItem>
                  <SelectItem value="gerente_geral">Gerente Geral</SelectItem>
                  <SelectItem value="gerente_precatorio">Gerente de Precatório</SelectItem>
                  <SelectItem value="gerente_rpv">Gerente de RPV</SelectItem>
                  <SelectItem value="captador">Captador</SelectItem>
                  <SelectItem value="operacional_precatorio">Operacional - Precatório</SelectItem>
                  <SelectItem value="operacional_rpv">Operacional - RPV</SelectItem>
                  <SelectItem value="operacional_completo">Operacional - Completo</SelectItem>
                </SelectContent>
              </Select>
              {formErrors.role && (
                <p className="text-sm text-red-500">{formErrors.role}</p>
              )}
            </div>
          </div>

          {/* Cargo (função) */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="cargo" className="text-right">
              Função
            </Label>
            <Input
              id="cargo"
              value={formData.cargo || ''}
              onChange={(e) => setFormData({ ...formData, cargo: e.target.value })}
              className="col-span-3"
              placeholder="Ex: Analista Sênior"
            />
          </div>

          {/* Departamento */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="departamento" className="text-right">
              Departamento
            </Label>
            <Input
              id="departamento"
              value={formData.departamento || ''}
              onChange={(e) => setFormData({ ...formData, departamento: e.target.value })}
              className="col-span-3"
              placeholder="Ex: Jurídico"
            />
          </div>

          {/* Telefone */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="telefone" className="text-right">
              Telefone
            </Label>
            <div className="col-span-3 space-y-1">
              <Input
                id="telefone"
                value={formData.telefone || ''}
                onChange={(e) => {
                  setFormData({ ...formData, telefone: e.target.value });
                  if (formErrors.telefone) {
                    setFormErrors({ ...formErrors, telefone: '' });
                  }
                }}
                className={formErrors.telefone ? "border-red-500" : ""}
                placeholder="(11) 99999-9999"
              />
              {formErrors.telefone && (
                <p className="text-sm text-red-500">{formErrors.telefone}</p>
              )}
            </div>
          </div>

          {/* Data de Entrada */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="data_entrada" className="text-right">
              Data de Entrada
            </Label>
            <Input
              id="data_entrada"
              type="date"
              value={formData.data_entrada || ''}
              onChange={(e) => setFormData({ ...formData, data_entrada: e.target.value })}
              className="col-span-3"
            />
          </div>

          {/* Status */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="status" className="text-right">
              Status
            </Label>
            <Select
              value={formData.status || 'ativo'}
              onValueChange={(value) => setFormData({ ...formData, status: value })}
            >
              <SelectTrigger className="col-span-3">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ativo">Ativo</SelectItem>
                <SelectItem value="inativo">Inativo</SelectItem>
                <SelectItem value="suspenso">Suspenso</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={loading}>
            <X className="h-4 w-4 mr-2" />
            Cancelar
          </Button>
          <Button onClick={handleSubmit} disabled={loading}>
            {loading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Salvando...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Salvar Alterações
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
