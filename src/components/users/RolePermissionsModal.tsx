import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON>le, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  Shield, 
  Users, 
  FileText,
  Calendar,
  BarChart3,
  UserCheck,
  Settings,
  Save,
  X
} from "lucide-react";
import { toast } from "sonner";
import { supabase } from "@/lib/supabase";

interface CustomRole {
  id: string;
  nome: string;
  descricao?: string;
  cor: string;
  icone?: string;
  is_system: boolean;
}

interface RolePermission {
  id?: string;
  role_id: string;
  resource_type: string;
  action: string;
  allowed: boolean;
}

interface RolePermissionsModalProps {
  role: CustomRole | null;
  isOpen: boolean;
  onClose: () => void;
  onPermissionsUpdated?: () => void;
}

const RESOURCE_TYPES = [
  { value: 'cliente', label: 'Clientes', icon: <Users className="h-4 w-4" /> },
  { value: 'precatorio', label: 'Precatórios', icon: <FileText className="h-4 w-4" /> },
  { value: 'rpv', label: 'RPVs', icon: <FileText className="h-4 w-4" /> },
  { value: 'documento', label: 'Documentos', icon: <FileText className="h-4 w-4" /> },
  { value: 'tarefa', label: 'Tarefas', icon: <Calendar className="h-4 w-4" /> },
  { value: 'relatorio', label: 'Relatórios', icon: <BarChart3 className="h-4 w-4" /> },
  { value: 'usuario', label: 'Usuários', icon: <UserCheck className="h-4 w-4" /> },
  { value: 'sistema', label: 'Sistema', icon: <Settings className="h-4 w-4" /> },
];

const ACTIONS = [
  { value: 'view', label: 'Visualizar' },
  { value: 'create', label: 'Criar' },
  { value: 'edit', label: 'Editar' },
  { value: 'delete', label: 'Excluir' },
  { value: 'export', label: 'Exportar' },
  { value: 'import', label: 'Importar' },
];

export function RolePermissionsModal({ role, isOpen, onClose, onPermissionsUpdated }: RolePermissionsModalProps) {
  const [permissions, setPermissions] = useState<RolePermission[]>([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (role && isOpen) {
      loadRolePermissions();
    }
  }, [role, isOpen]);

  const loadRolePermissions = async () => {
    if (!role) return;

    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('role_default_permissions')
        .select('*')
        .eq('role_id', role.id);

      if (error) throw error;
      setPermissions(data || []);
    } catch (error) {
      console.error('Erro ao carregar permissões do role:', error);
      toast.error("Erro ao carregar permissões do tipo de usuário");
    } finally {
      setLoading(false);
    }
  };

  const updatePermission = async (resourceType: string, action: string, allowed: boolean) => {
    if (!role) return;

    try {
      const { error } = await supabase
        .from('role_default_permissions')
        .upsert({
          role_id: role.id,
          resource_type: resourceType,
          action: action,
          allowed: allowed
        });

      if (error) throw error;

      // Atualizar estado local
      setPermissions(prev => {
        const existing = prev.find(p => 
          p.resource_type === resourceType && 
          p.action === action
        );

        if (existing) {
          return prev.map(p => 
            p.resource_type === resourceType && 
            p.action === action
              ? { ...p, allowed }
              : p
          );
        } else {
          return [...prev, { role_id: role.id, resource_type: resourceType, action: action, allowed }];
        }
      });

      toast.success("Permissão atualizada!");
    } catch (error) {
      console.error('Erro ao atualizar permissão:', error);
      toast.error("Erro ao atualizar permissão");
    }
  };

  const getPermission = (resourceType: string, action: string): boolean => {
    const permission = permissions.find(p => 
      p.resource_type === resourceType && 
      p.action === action
    );
    return permission?.allowed || false;
  };

  const saveAllPermissions = async () => {
    if (!role) return;

    try {
      setSaving(true);
      
      // Criar todas as combinações de permissões
      const allPermissions = [];
      for (const resource of RESOURCE_TYPES) {
        for (const action of ACTIONS) {
          const allowed = getPermission(resource.value, action.value);
          allPermissions.push({
            role_id: role.id,
            resource_type: resource.value,
            action: action.value,
            allowed: allowed
          });
        }
      }

      // Deletar permissões existentes e inserir novas
      const { error: deleteError } = await supabase
        .from('role_default_permissions')
        .delete()
        .eq('role_id', role.id);

      if (deleteError) throw deleteError;

      const { error: insertError } = await supabase
        .from('role_default_permissions')
        .insert(allPermissions);

      if (insertError) throw insertError;

      toast.success("Permissões salvas com sucesso!");
      onPermissionsUpdated?.();
      onClose();
    } catch (error) {
      console.error('Erro ao salvar permissões:', error);
      toast.error("Erro ao salvar permissões");
    } finally {
      setSaving(false);
    }
  };

  if (!role) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <div 
              className="w-4 h-4 rounded-full"
              style={{ backgroundColor: role.cor }}
            />
            <Shield className="h-5 w-5" />
            Permissões - {role.nome}
          </DialogTitle>
          <div className="flex items-center gap-2 mt-2">
            <Badge variant="outline">
              {role.descricao || "Sem descrição"}
            </Badge>
            {role.is_system && (
              <Badge variant="secondary">Sistema</Badge>
            )}
          </div>
        </DialogHeader>

        <div className="space-y-6">
          <div className="text-sm text-muted-foreground">
            <p>Configure as permissões para o tipo de usuário <strong>{role.nome}</strong>.</p>
            <p>Essas permissões serão aplicadas a todos os usuários deste tipo.</p>
          </div>

          {loading ? (
            <div className="flex justify-center items-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : (
            <ScrollArea className="h-[500px]">
              <div className="space-y-6">
                {RESOURCE_TYPES.map((resource) => (
                  <Card key={resource.value}>
                    <CardHeader className="pb-3">
                      <CardTitle className="flex items-center gap-2 text-base">
                        {resource.icon}
                        {resource.label}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                        {ACTIONS.map((action) => {
                          const hasPermission = getPermission(resource.value, action.value);
                          
                          return (
                            <div key={action.value} className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors">
                              <span className="text-sm font-medium">{action.label}</span>
                              <Switch
                                checked={hasPermission}
                                onCheckedChange={(checked) => 
                                  updatePermission(resource.value, action.value, checked)
                                }
                                disabled={role.is_system && role.nome === 'Administrador'}
                              />
                            </div>
                          );
                        })}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </ScrollArea>
          )}
        </div>

        <DialogFooter className="flex gap-2">
          <Button variant="outline" onClick={onClose} disabled={saving}>
            <X className="h-4 w-4 mr-2" />
            Cancelar
          </Button>
          <Button onClick={saveAllPermissions} disabled={saving || loading}>
            {saving ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            Salvar Permissões
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
