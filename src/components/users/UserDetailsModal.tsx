import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { 
  User, 
  Mail, 
  Phone, 
  Calendar, 
  Building, 
  UserCheck, 
  Clock,
  Shield,
  Activity
} from "lucide-react";

interface UserProfile {
  id: string;
  email: string;
  nome?: string;
  role: string;
  foto_url?: string;
  status?: string;
  created_at?: string;
  cargo?: string;
  departamento?: string;
  telefone?: string;
  data_entrada?: string;
  custom_role_id?: string;
}

interface UserDetailsModalProps {
  user: UserProfile | null;
  isOpen: boolean;
  onClose: () => void;
}

export function UserDetailsModal({ user, isOpen, onClose }: UserDetailsModalProps) {
  if (!user) return null;

  const formatDate = (dateString?: string) => {
    if (!dateString) return "Não informado";
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const formatDateTime = (dateString?: string) => {
    if (!dateString) return "Não informado";
    return new Date(dateString).toLocaleString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getRoleDisplayName = (role: string) => {
    const displayNames: Record<string, string> = {
      admin: "Administrador (ADC)",
      gerente_geral: "Gerente Geral",
      gerente_precatorio: "Gerente de Precatório",
      gerente_rpv: "Gerente de RPV",
      captador: "Captador",
      operacional_precatorio: "Operacional - Precatório",
      operacional_rpv: "Operacional - RPV",
      operacional_completo: "Operacional - Completo"
    };
    return displayNames[role] || role;
  };

  const getRoleBadgeColor = (role: string) => {
    const colors: Record<string, string> = {
      admin: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300",
      gerente_geral: "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300",
      gerente_precatorio: "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300",
      gerente_rpv: "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300",
      captador: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300",
      operacional_precatorio: "bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-300",
      operacional_rpv: "bg-pink-100 text-pink-800 dark:bg-pink-900/30 dark:text-pink-300",
      operacional_completo: "bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-300"
    };
    return colors[role] || "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300";
  };

  const getStatusBadgeVariant = (status?: string) => {
    switch (status) {
      case "ativo":
        return "default";
      case "inativo":
        return "secondary";
      case "suspenso":
        return "destructive";
      default:
        return "outline";
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <Avatar className="h-10 w-10">
              <AvatarImage src={user.foto_url || ""} />
              <AvatarFallback>
                {user.nome ? user.nome.slice(0, 2).toUpperCase() : "U"}
              </AvatarFallback>
            </Avatar>
            <div>
              <h2 className="text-xl font-semibold">{user.nome || "Usuário sem nome"}</h2>
              <p className="text-sm text-muted-foreground">{user.email}</p>
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Informações Básicas */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Informações Básicas
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Nome Completo</label>
                  <p className="text-sm">{user.nome || "Não informado"}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">E-mail</label>
                  <p className="text-sm flex items-center gap-2">
                    <Mail className="h-4 w-4" />
                    {user.email}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Telefone</label>
                  <p className="text-sm flex items-center gap-2">
                    <Phone className="h-4 w-4" />
                    {user.telefone || "Não informado"}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Status</label>
                  <div>
                    <Badge variant={getStatusBadgeVariant(user.status)}>
                      {user.status || "Desconhecido"}
                    </Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Informações Profissionais */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building className="h-5 w-5" />
                Informações Profissionais
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Cargo/Função</label>
                  <p className="text-sm">{user.cargo || "Não informado"}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Departamento</label>
                  <p className="text-sm">{user.departamento || "Não informado"}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Tipo de Usuário</label>
                  <div>
                    <Badge className={getRoleBadgeColor(user.role)}>
                      <Shield className="h-3 w-3 mr-1" />
                      {getRoleDisplayName(user.role)}
                    </Badge>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Data de Entrada</label>
                  <p className="text-sm flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    {formatDate(user.data_entrada)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Informações do Sistema */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Informações do Sistema
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">ID do Usuário</label>
                  <p className="text-sm font-mono text-xs bg-muted p-2 rounded">
                    {user.id}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Data de Criação</label>
                  <p className="text-sm flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    {formatDateTime(user.created_at)}
                  </p>
                </div>
                {user.custom_role_id && (
                  <div className="col-span-2">
                    <label className="text-sm font-medium text-muted-foreground">Role Personalizado</label>
                    <p className="text-sm font-mono text-xs bg-muted p-2 rounded">
                      {user.custom_role_id}
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Estatísticas de Atividade */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <UserCheck className="h-5 w-5" />
                Atividade Recente
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Estatísticas de atividade em desenvolvimento</p>
                <p className="text-xs">
                  Em breve: último login, ações recentes, estatísticas de uso
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
}
