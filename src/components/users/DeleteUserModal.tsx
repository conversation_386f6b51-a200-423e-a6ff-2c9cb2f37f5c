import React, { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogDescription, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, Trash2, X, AlertTriangle } from "lucide-react";
import { toast } from "sonner";
import { useAuth } from "@/contexts/AuthContext";

interface UserProfile {
  id: string;
  email: string;
  nome?: string;
  role: string;
  foto_url?: string;
  status?: string;
  created_at?: string;
  updated_at?: string;
  cargo?: string;
  departamento?: string;
  telefone?: string;
  data_entrada?: string;
  custom_role_id?: string;
}

interface DeleteUserModalProps {
  isOpen: boolean;
  onClose: () => void;
  user: UserProfile | null;
  onUserDeleted?: (deletedUserId: string) => void;
}

export function DeleteUserModal({ isOpen, onClose, user, onUserDeleted }: DeleteUserModalProps) {
  const { deleteUser, user: currentUser } = useAuth();
  const [loading, setLoading] = useState(false);

  const handleDelete = async () => {
    if (!user) return;

    try {
      setLoading(true);

      const { error, success } = await deleteUser(user.id);

      if (error) {
        toast.error("Erro ao excluir usuário", {
          description: error
        });
        return;
      }

      if (success) {
        toast.success("Usuário excluído com sucesso!", {
          description: `${user.nome || user.email} foi removido do sistema.`
        });

        // Forçar atualização da página para refletir as mudanças
        setTimeout(() => {
          window.location.reload();
        }, 1000);

        onUserDeleted?.(user.id);
        onClose();
      }
    } catch (error: any) {
      console.error("Erro ao excluir usuário:", error);
      toast.error("Erro ao excluir usuário", {
        description: error instanceof Error ? error.message : "Ocorreu um erro desconhecido"
      });
    } finally {
      setLoading(false);
    }
  };

  if (!user) return null;

  const isCurrentUser = currentUser?.id === user.id;
  const isAdmin = user.role === 'admin';

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3 text-red-600">
            <AlertTriangle className="h-6 w-6" />
            Confirmar Exclusão
          </DialogTitle>
          <DialogDescription>
            Esta ação não pode ser desfeita. O usuário será removido permanentemente do sistema.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Informações do usuário */}
          <div className="flex items-center gap-4 p-4 border rounded-lg bg-muted/50">
            <Avatar className="h-12 w-12">
              <AvatarImage src={user.foto_url || ""} />
              <AvatarFallback>
                {(user.nome || user.email).slice(0, 2).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <h3 className="font-semibold">{user.nome || "Usuário sem nome"}</h3>
              <p className="text-sm text-muted-foreground">{user.email}</p>
              <p className="text-sm text-muted-foreground">
                Cargo: {user.role === 'admin' ? 'Administrador' :
                       user.role === 'gerente_geral' ? 'Gerente Geral' :
                       user.role === 'gerente_precatorio' ? 'Gerente de Precatório' :
                       user.role === 'gerente_rpv' ? 'Gerente de RPV' :
                       user.role === 'captador' ? 'Captador' :
                       user.role === 'operacional_precatorio' ? 'Operacional - Precatório' :
                       user.role === 'operacional_rpv' ? 'Operacional - RPV' :
                       user.role === 'operacional_completo' ? 'Operacional - Completo' :
                       user.role}
              </p>
            </div>
          </div>

          {/* Avisos especiais */}
          {isCurrentUser && (
            <Alert className="border-red-200 bg-red-50">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-800">
                <strong>Atenção:</strong> Você não pode excluir sua própria conta.
              </AlertDescription>
            </Alert>
          )}

          {isAdmin && !isCurrentUser && (
            <Alert className="border-orange-200 bg-orange-50">
              <AlertTriangle className="h-4 w-4 text-orange-600" />
              <AlertDescription className="text-orange-800">
                <strong>Cuidado:</strong> Você está prestes a excluir um usuário administrador.
                Certifique-se de que há outros administradores no sistema.
              </AlertDescription>
            </Alert>
          )}

          <Alert className="border-red-200 bg-red-50">
            <AlertTriangle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">
              <strong>Esta ação irá:</strong>
              <ul className="mt-2 list-disc list-inside space-y-1">
                <li>Remover o usuário permanentemente do sistema</li>
                <li>Excluir todas as permissões específicas do usuário</li>
                <li>Remover o acesso a todas as páginas e funcionalidades</li>
                <li>Desativar a conta de autenticação</li>
              </ul>
            </AlertDescription>
          </Alert>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={loading}>
            <X className="h-4 w-4 mr-2" />
            Cancelar
          </Button>
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={loading || isCurrentUser}
          >
            {loading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Excluindo...
              </>
            ) : (
              <>
                <Trash2 className="h-4 w-4 mr-2" />
                Confirmar Exclusão
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
