import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, Save, Eye, Database, Users, Shield, Info } from "lucide-react";
import { toast } from "sonner";
import { supabase } from "@/lib/supabase";

interface UserProfile {
  id: string;
  email: string;
  nome?: string;
  role: string;
  departamento?: string;
}

interface DataVisibilityConfig {
  resource_type: string;
  visibility_type: 'own_only' | 'team_only' | 'department_only' | 'role_based' | 'specific_users' | 'all_data' | 'custom_filter';
  allowed_user_ids: string[];
  allowed_roles: string[];
  allowed_departments: string[];
  can_view_sensitive_data: boolean;
  can_export_data: boolean;
  can_view_financial_data: boolean;
  can_view_personal_data: boolean;
}

interface DataVisibilityManagerProps {
  isOpen: boolean;
  onClose: () => void;
  user: UserProfile | null;
  onConfigUpdated?: () => void;
}

const RESOURCE_TYPES = [
  { value: 'clientes', label: 'Clientes', icon: Users },
  { value: 'precatorios', label: 'Precatórios', icon: Database },
  { value: 'rpv', label: 'RPV', icon: Database },
  { value: 'tarefas', label: 'Tarefas', icon: Shield },
  { value: 'documentos', label: 'Documentos', icon: Eye }
];

const VISIBILITY_TYPES = [
  { value: 'own_only', label: 'Apenas próprios dados', description: 'Usuário vê apenas dados criados por ele' },
  { value: 'team_only', label: 'Dados da equipe', description: 'Usuário vê dados de sua equipe/departamento' },
  { value: 'department_only', label: 'Dados do departamento', description: 'Usuário vê dados de departamentos específicos' },
  { value: 'role_based', label: 'Baseado no cargo', description: 'Usuário vê dados de cargos específicos' },
  { value: 'specific_users', label: 'Usuários específicos', description: 'Usuário vê dados de usuários selecionados' },
  { value: 'all_data', label: 'Todos os dados', description: 'Usuário vê todos os dados do sistema' },
  { value: 'custom_filter', label: 'Filtro customizado', description: 'Regras personalizadas de visibilidade' }
];

export function DataVisibilityManager({ isOpen, onClose, user, onConfigUpdated }: DataVisibilityManagerProps) {
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [configs, setConfigs] = useState<DataVisibilityConfig[]>([]);
  const [selectedResource, setSelectedResource] = useState<string>('');
  const [availableUsers, setAvailableUsers] = useState<UserProfile[]>([]);

  // Carregar configurações existentes
  useEffect(() => {
    if (user && isOpen) {
      loadUserDataVisibility();
      loadAvailableUsers();
    }
  }, [user, isOpen]);

  const loadUserDataVisibility = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const { data, error } = await supabase.rpc('get_user_data_visibility', {
        p_user_id: user.id
      });

      if (error) {
        console.error('Erro ao carregar configurações de visibilidade:', error);
        toast.error("Erro ao carregar configurações");
        return;
      }

      setConfigs(data || []);
    } catch (error) {
      console.error('Erro ao carregar configurações:', error);
      toast.error("Erro ao carregar configurações");
    } finally {
      setLoading(false);
    }
  };

  const loadAvailableUsers = async () => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('id, email, nome, role, departamento')
        .neq('id', user?.id)
        .neq('status', 'excluido')
        .order('nome');

      if (error) {
        console.error('Erro ao carregar usuários:', error);
        return;
      }

      setAvailableUsers(data || []);
    } catch (error) {
      console.error('Erro ao carregar usuários:', error);
    }
  };

  const updateConfig = (resourceType: string, field: string, value: any) => {
    setConfigs(prev => {
      const existingIndex = prev.findIndex(c => c.resource_type === resourceType);

      if (existingIndex >= 0) {
        const updated = [...prev];
        updated[existingIndex] = { ...updated[existingIndex], [field]: value };
        return updated;
      } else {
        // Criar nova configuração
        const newConfig: DataVisibilityConfig = {
          resource_type: resourceType,
          visibility_type: 'own_only',
          allowed_user_ids: [],
          allowed_roles: [],
          allowed_departments: [],
          can_view_sensitive_data: false,
          can_export_data: false,
          can_view_financial_data: false,
          can_view_personal_data: false,
          [field]: value
        };
        return [...prev, newConfig];
      }
    });
  };

  const getConfig = (resourceType: string): DataVisibilityConfig => {
    return configs.find(c => c.resource_type === resourceType) || {
      resource_type: resourceType,
      visibility_type: 'own_only',
      allowed_user_ids: [],
      allowed_roles: [],
      allowed_departments: [],
      can_view_sensitive_data: false,
      can_export_data: false,
      can_view_financial_data: false,
      can_view_personal_data: false
    };
  };

  const saveConfigurations = async () => {
    if (!user) return;

    try {
      setSaving(true);

      // Salvar cada configuração
      for (const config of configs) {
        const { error } = await supabase
          .from('user_data_visibility')
          .upsert({
            user_id: user.id,
            resource_type: config.resource_type,
            visibility_type: config.visibility_type,
            allowed_user_ids: config.allowed_user_ids,
            allowed_roles: config.allowed_roles,
            allowed_departments: config.allowed_departments,
            can_view_sensitive_data: config.can_view_sensitive_data,
            can_export_data: config.can_export_data,
            can_view_financial_data: config.can_view_financial_data,
            can_view_personal_data: config.can_view_personal_data,
            updated_at: new Date().toISOString()
          }, {
            onConflict: 'user_id,resource_type'
          });

        if (error) {
          console.error('Erro ao salvar configuração:', error);
          toast.error(`Erro ao salvar configuração para ${config.resource_type}`);
          return;
        }
      }

      toast.success("Configurações de visibilidade salvas com sucesso!");
      onConfigUpdated?.();
      onClose();
    } catch (error) {
      console.error('Erro ao salvar configurações:', error);
      toast.error("Erro ao salvar configurações");
    } finally {
      setSaving(false);
    }
  };

  if (!user) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Configurações de Visibilidade de Dados
          </DialogTitle>
          <DialogDescription>
            Configure quais dados {user.nome || user.email} pode visualizar em cada seção do sistema.
          </DialogDescription>
        </DialogHeader>

        {loading ? (
          <div className="flex justify-center items-center h-32">
            <Loader2 className="h-6 w-6 animate-spin" />
          </div>
        ) : (
          <div className="space-y-6">
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                Estas configurações controlam granularmente quais dados o usuário pode ver.
                Configurações mais restritivas sempre têm prioridade.
              </AlertDescription>
            </Alert>

            {RESOURCE_TYPES.map((resource) => {
              const config = getConfig(resource.value);
              const IconComponent = resource.icon;

              return (
                <Card key={resource.value}>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <IconComponent className="h-5 w-5" />
                      {resource.label}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Tipo de Visibilidade */}
                    <div className="space-y-2">
                      <Label>Tipo de Visibilidade</Label>
                      <Select
                        value={config.visibility_type}
                        onValueChange={(value) => updateConfig(resource.value, 'visibility_type', value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {VISIBILITY_TYPES.map((type) => (
                            <SelectItem key={type.value} value={type.value}>
                              <div>
                                <div className="font-medium">{type.label}</div>
                                <div className="text-xs text-muted-foreground">{type.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Configurações Específicas */}
                    {config.visibility_type === 'specific_users' && (
                      <div className="space-y-3">
                        <Label>Usuários Permitidos</Label>
                        <div className="text-sm text-muted-foreground">
                          Selecione quais usuários este usuário pode ver dados de:
                        </div>

                        {/* Seletor de usuários */}
                        <Select
                          onValueChange={(userId) => {
                            if (!config.allowed_user_ids.includes(userId)) {
                              updateConfig(resource.value, 'allowed_user_ids', [...config.allowed_user_ids, userId]);
                            }
                          }}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Adicionar usuário..." />
                          </SelectTrigger>
                          <SelectContent>
                            {availableUsers
                              .filter(u => !config.allowed_user_ids.includes(u.id))
                              .map((availableUser) => (
                                <SelectItem key={availableUser.id} value={availableUser.id}>
                                  <div className="flex items-center gap-2">
                                    <div>
                                      <div className="font-medium">{availableUser.nome || availableUser.email}</div>
                                      <div className="text-xs text-muted-foreground">{availableUser.role}</div>
                                    </div>
                                  </div>
                                </SelectItem>
                              ))}
                          </SelectContent>
                        </Select>

                        {/* Lista de usuários selecionados */}
                        <div className="flex flex-wrap gap-2">
                          {config.allowed_user_ids.map((userId) => {
                            const allowedUser = availableUsers.find(u => u.id === userId);
                            return allowedUser ? (
                              <Badge key={userId} variant="secondary" className="flex items-center gap-1">
                                {allowedUser.nome || allowedUser.email}
                                <button
                                  onClick={() => {
                                    updateConfig(
                                      resource.value,
                                      'allowed_user_ids',
                                      config.allowed_user_ids.filter(id => id !== userId)
                                    );
                                  }}
                                  className="ml-1 hover:bg-red-100 rounded-full p-0.5"
                                >
                                  ×
                                </button>
                              </Badge>
                            ) : null;
                          })}
                        </div>
                      </div>
                    )}

                    {/* Configuração por Roles */}
                    {config.visibility_type === 'role_based' && (
                      <div className="space-y-3">
                        <Label>Roles Permitidos</Label>
                        <div className="text-sm text-muted-foreground">
                          Selecione quais roles este usuário pode ver dados de:
                        </div>

                        <div className="grid grid-cols-2 gap-2">
                          {['admin', 'gerente_geral', 'gerente_precatorio', 'gerente_rpv', 'captador', 'assistente'].map((role) => (
                            <div key={role} className="flex items-center space-x-2">
                              <input
                                type="checkbox"
                                id={`role-${role}-${resource.value}`}
                                checked={config.allowed_roles.includes(role)}
                                onChange={(e) => {
                                  if (e.target.checked) {
                                    updateConfig(resource.value, 'allowed_roles', [...config.allowed_roles, role]);
                                  } else {
                                    updateConfig(resource.value, 'allowed_roles', config.allowed_roles.filter(r => r !== role));
                                  }
                                }}
                                className="rounded border-gray-300"
                              />
                              <Label htmlFor={`role-${role}-${resource.value}`} className="text-sm">
                                {role.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                              </Label>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Configuração por Departamentos */}
                    {config.visibility_type === 'department_only' && (
                      <div className="space-y-3">
                        <Label>Departamentos Permitidos</Label>
                        <div className="text-sm text-muted-foreground">
                          Selecione quais departamentos este usuário pode ver dados de:
                        </div>

                        <div className="grid grid-cols-2 gap-2">
                          {['Jurídico', 'Financeiro', 'Administrativo', 'Comercial', 'TI'].map((dept) => (
                            <div key={dept} className="flex items-center space-x-2">
                              <input
                                type="checkbox"
                                id={`dept-${dept}-${resource.value}`}
                                checked={config.allowed_departments.includes(dept)}
                                onChange={(e) => {
                                  if (e.target.checked) {
                                    updateConfig(resource.value, 'allowed_departments', [...config.allowed_departments, dept]);
                                  } else {
                                    updateConfig(resource.value, 'allowed_departments', config.allowed_departments.filter(d => d !== dept));
                                  }
                                }}
                                className="rounded border-gray-300"
                              />
                              <Label htmlFor={`dept-${dept}-${resource.value}`} className="text-sm">
                                {dept}
                              </Label>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Permissões Adicionais */}
                    <div className="grid grid-cols-2 gap-4">
                      <div className="flex items-center justify-between">
                        <Label htmlFor={`sensitive-${resource.value}`}>Dados Sensíveis</Label>
                        <Switch
                          id={`sensitive-${resource.value}`}
                          checked={config.can_view_sensitive_data}
                          onCheckedChange={(checked) =>
                            updateConfig(resource.value, 'can_view_sensitive_data', checked)
                          }
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <Label htmlFor={`export-${resource.value}`}>Exportar Dados</Label>
                        <Switch
                          id={`export-${resource.value}`}
                          checked={config.can_export_data}
                          onCheckedChange={(checked) =>
                            updateConfig(resource.value, 'can_export_data', checked)
                          }
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <Label htmlFor={`financial-${resource.value}`}>Dados Financeiros</Label>
                        <Switch
                          id={`financial-${resource.value}`}
                          checked={config.can_view_financial_data}
                          onCheckedChange={(checked) =>
                            updateConfig(resource.value, 'can_view_financial_data', checked)
                          }
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <Label htmlFor={`personal-${resource.value}`}>Dados Pessoais</Label>
                        <Switch
                          id={`personal-${resource.value}`}
                          checked={config.can_view_personal_data}
                          onCheckedChange={(checked) =>
                            updateConfig(resource.value, 'can_view_personal_data', checked)
                          }
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={saving}>
            Cancelar
          </Button>
          <Button onClick={saveConfigurations} disabled={saving || loading}>
            {saving ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Salvando...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Salvar Configurações
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
