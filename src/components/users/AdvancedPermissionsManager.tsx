import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import {
  Plus,
  Edit,
  Trash2,
  Shield,
  Settings
} from "lucide-react";
import { toast } from "sonner";
import { supabase } from "@/lib/supabase";
import { RolePermissionsModal } from "./RolePermissionsModal";

interface CustomRole {
  id: string;
  nome: string;
  descricao?: string;
  cor: string;
  icone?: string;
  is_system: boolean;
  created_at: string;
}

interface AdvancedPermissionsManagerProps {
  onClose?: () => void;
}

export function AdvancedPermissionsManager({ onClose }: AdvancedPermissionsManagerProps) {
  const [customRoles, setCustomRoles] = useState<CustomRole[]>([]);
  const [loading, setLoading] = useState(true);
  const [isCreateRoleOpen, setIsCreateRoleOpen] = useState(false);
  const [isEditRoleOpen, setIsEditRoleOpen] = useState(false);
  const [isPermissionsModalOpen, setIsPermissionsModalOpen] = useState(false);
  const [selectedRole, setSelectedRole] = useState<CustomRole | null>(null);
  const [newRole, setNewRole] = useState({
    nome: "",
    descricao: "",
    cor: "#3b82f6",
    icone: "user"
  });

  useEffect(() => {
    loadCustomRoles();
  }, []);

  const loadCustomRoles = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('custom_roles')
        .select('*')
        .eq('is_deleted', false)
        .order('nome');

      if (error) throw error;
      setCustomRoles(data || []);
    } catch (error) {
      console.error('Erro ao carregar roles:', error);
      toast.error("Erro ao carregar tipos de usuário");
    } finally {
      setLoading(false);
    }
  };

  const createRole = async () => {
    try {
      const { data, error } = await supabase
        .from('custom_roles')
        .insert([{
          nome: newRole.nome,
          descricao: newRole.descricao,
          cor: newRole.cor,
          icone: newRole.icone,
          is_system: false
        }])
        .select()
        .single();

      if (error) throw error;

      setCustomRoles(prev => [...prev, data]);
      setNewRole({ nome: "", descricao: "", cor: "#3b82f6", icone: "user" });
      setIsCreateRoleOpen(false);
      toast.success("Tipo de usuário criado com sucesso!");
    } catch (error) {
      console.error('Erro ao criar role:', error);
      toast.error("Erro ao criar tipo de usuário");
    }
  };

  const updateRole = async () => {
    if (!selectedRole) return;

    try {
      const { error } = await supabase
        .from('custom_roles')
        .update({
          nome: newRole.nome,
          descricao: newRole.descricao,
          cor: newRole.cor,
          icone: newRole.icone
        })
        .eq('id', selectedRole.id);

      if (error) throw error;

      setCustomRoles(prev => prev.map(role =>
        role.id === selectedRole.id
          ? { ...role, ...newRole }
          : role
      ));
      setIsEditRoleOpen(false);
      setSelectedRole(null);
      toast.success("Tipo de usuário atualizado com sucesso!");
    } catch (error) {
      console.error('Erro ao atualizar role:', error);
      toast.error("Erro ao atualizar tipo de usuário");
    }
  };

  const deleteRole = async (roleId: string) => {
    try {
      const { error } = await supabase
        .from('custom_roles')
        .update({ is_deleted: true, deleted_at: new Date().toISOString() })
        .eq('id', roleId);

      if (error) throw error;

      setCustomRoles(prev => prev.filter(role => role.id !== roleId));
      toast.success("Tipo de usuário excluído com sucesso!");
    } catch (error) {
      console.error('Erro ao excluir role:', error);
      toast.error("Erro ao excluir tipo de usuário");
    }
  };



  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Gerenciamento de Permissões</h2>
          <p className="text-muted-foreground">Configure tipos de usuário e suas permissões</p>
        </div>
        <Button onClick={onClose} variant="outline">
          Fechar
        </Button>
      </div>

      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold">Tipos de Usuário e Permissões</h3>
          <Button onClick={() => setIsCreateRoleOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Novo Tipo
          </Button>
        </div>

        <div className="grid gap-4">
          {customRoles.map((role) => (
            <Card key={role.id} className="hover:shadow-md transition-shadow cursor-pointer">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div
                    className="flex items-center space-x-3 flex-1 cursor-pointer"
                    onClick={() => {
                      setSelectedRole(role);
                      setIsPermissionsModalOpen(true);
                    }}
                  >
                    <div
                      className="w-4 h-4 rounded-full"
                      style={{ backgroundColor: role.cor }}
                    />
                    <div className="flex-1">
                      <h4 className="font-medium">{role.nome}</h4>
                      {role.descricao && (
                        <p className="text-sm text-muted-foreground">{role.descricao}</p>
                      )}
                    </div>
                    {role.is_system && (
                      <Badge variant="secondary">Sistema</Badge>
                    )}
                    <Button variant="outline" size="sm">
                      <Settings className="h-4 w-4 mr-2" />
                      Configurar Permissões
                    </Button>
                  </div>
                  <div className="flex space-x-2 ml-4">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedRole(role);
                        setNewRole({
                          nome: role.nome,
                          descricao: role.descricao || "",
                          cor: role.cor,
                          icone: role.icone || "user"
                        });
                        setIsEditRoleOpen(true);
                      }}
                      disabled={role.is_system}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        deleteRole(role.id);
                      }}
                      disabled={role.is_system}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Modal de criar role */}
      <Dialog open={isCreateRoleOpen} onOpenChange={setIsCreateRoleOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Criar Novo Tipo de Usuário</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="nome">Nome</Label>
              <Input
                id="nome"
                value={newRole.nome}
                onChange={(e) => setNewRole(prev => ({ ...prev, nome: e.target.value }))}
                placeholder="Ex: Gerente Regional"
              />
            </div>
            <div>
              <Label htmlFor="descricao">Descrição</Label>
              <Input
                id="descricao"
                value={newRole.descricao}
                onChange={(e) => setNewRole(prev => ({ ...prev, descricao: e.target.value }))}
                placeholder="Descrição do tipo de usuário"
              />
            </div>
            <div>
              <Label htmlFor="cor">Cor</Label>
              <Input
                id="cor"
                type="color"
                value={newRole.cor}
                onChange={(e) => setNewRole(prev => ({ ...prev, cor: e.target.value }))}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateRoleOpen(false)}>
              Cancelar
            </Button>
            <Button onClick={createRole} disabled={!newRole.nome}>
              Criar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Modal de editar role */}
      <Dialog open={isEditRoleOpen} onOpenChange={setIsEditRoleOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Editar Tipo de Usuário</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="edit-nome">Nome</Label>
              <Input
                id="edit-nome"
                value={newRole.nome}
                onChange={(e) => setNewRole(prev => ({ ...prev, nome: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="edit-descricao">Descrição</Label>
              <Input
                id="edit-descricao"
                value={newRole.descricao}
                onChange={(e) => setNewRole(prev => ({ ...prev, descricao: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="edit-cor">Cor</Label>
              <Input
                id="edit-cor"
                type="color"
                value={newRole.cor}
                onChange={(e) => setNewRole(prev => ({ ...prev, cor: e.target.value }))}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditRoleOpen(false)}>
              Cancelar
            </Button>
            <Button onClick={updateRole}>
              Salvar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Modal de permissões do role */}
      <RolePermissionsModal
        role={selectedRole}
        isOpen={isPermissionsModalOpen}
        onClose={() => {
          setIsPermissionsModalOpen(false);
          setSelectedRole(null);
        }}
        onPermissionsUpdated={() => {
          // Opcional: recarregar dados se necessário
          loadCustomRoles();
        }}
      />
    </div>
  );
}
