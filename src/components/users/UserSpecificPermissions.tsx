import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { 
  Shield, 
  Users, 
  FileText,
  Calendar,
  BarChart3,
  UserCheck,
  Settings
} from "lucide-react";
import { toast } from "sonner";
import { supabase } from "@/lib/supabase";

interface UserProfile {
  id: string;
  email: string;
  nome?: string;
  role: string;
  foto_url?: string;
  status?: string;
  created_at?: string;
}

interface UserSpecificPermission {
  id?: string;
  user_id: string;
  resource_type: string;
  action: string;
  allowed: boolean;
}

interface UserSpecificPermissionsProps {
  user: UserProfile | null;
  isOpen: boolean;
  onClose: () => void;
}

const RESOURCE_TYPES = [
  { value: 'cliente', label: 'Clientes', icon: <Users className="h-4 w-4" /> },
  { value: 'precatorio', label: 'Precatórios', icon: <FileText className="h-4 w-4" /> },
  { value: 'rpv', label: 'RPVs', icon: <FileText className="h-4 w-4" /> },
  { value: 'documento', label: 'Documentos', icon: <FileText className="h-4 w-4" /> },
  { value: 'tarefa', label: 'Tarefas', icon: <Calendar className="h-4 w-4" /> },
  { value: 'relatorio', label: 'Relatórios', icon: <BarChart3 className="h-4 w-4" /> },
  { value: 'usuario', label: 'Usuários', icon: <UserCheck className="h-4 w-4" /> },
  { value: 'sistema', label: 'Sistema', icon: <Settings className="h-4 w-4" /> },
];

const ACTIONS = [
  { value: 'view', label: 'Visualizar' },
  { value: 'create', label: 'Criar' },
  { value: 'edit', label: 'Editar' },
  { value: 'delete', label: 'Excluir' },
  { value: 'export', label: 'Exportar' },
  { value: 'import', label: 'Importar' },
];

export function UserSpecificPermissions({ user, isOpen, onClose }: UserSpecificPermissionsProps) {
  const [userPermissions, setUserPermissions] = useState<UserSpecificPermission[]>([]);
  const [rolePermissions, setRolePermissions] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (user && isOpen) {
      loadUserPermissions();
      loadRolePermissions();
    }
  }, [user, isOpen]);

  const loadUserPermissions = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('user_specific_permissions')
        .select('*')
        .eq('user_id', user.id);

      if (error) throw error;
      setUserPermissions(data || []);
    } catch (error) {
      console.error('Erro ao carregar permissões do usuário:', error);
      toast.error("Erro ao carregar permissões do usuário");
    } finally {
      setLoading(false);
    }
  };

  const loadRolePermissions = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('role_default_permissions')
        .select('*')
        .eq('role_id', user.role);

      if (error) throw error;
      setRolePermissions(data || []);
    } catch (error) {
      console.error('Erro ao carregar permissões do role:', error);
    }
  };

  const updateUserPermission = async (resourceType: string, action: string, allowed: boolean) => {
    if (!user) return;

    try {
      const { error } = await supabase
        .from('user_specific_permissions')
        .upsert({
          user_id: user.id,
          resource_type: resourceType,
          action: action,
          allowed: allowed
        });

      if (error) throw error;

      setUserPermissions(prev => {
        const existing = prev.find(p => 
          p.resource_type === resourceType && 
          p.action === action
        );

        if (existing) {
          return prev.map(p => 
            p.resource_type === resourceType && 
            p.action === action
              ? { ...p, allowed }
              : p
          );
        } else {
          return [...prev, { user_id: user.id, resource_type: resourceType, action: action, allowed }];
        }
      });

      toast.success("Permissão atualizada!");
    } catch (error) {
      console.error('Erro ao atualizar permissão:', error);
      toast.error("Erro ao atualizar permissão");
    }
  };

  const getUserPermission = (resourceType: string, action: string): { hasPermission: boolean, isCustom: boolean } => {
    // Verificar se há permissão específica do usuário
    const userPermission = userPermissions.find(p => 
      p.resource_type === resourceType && 
      p.action === action
    );

    if (userPermission) {
      return { hasPermission: userPermission.allowed, isCustom: true };
    }

    // Se não há permissão específica, verificar permissão do role
    const rolePermission = rolePermissions.find(p => 
      p.resource_type === resourceType && 
      p.action === action
    );

    return { hasPermission: rolePermission?.allowed || false, isCustom: false };
  };

  const getRoleDisplayName = (role: string) => {
    const displayNames: Record<string, string> = {
      admin: "Administrador (ADC)",
      gerente_geral: "Gerente Geral",
      gerente_precatorio: "Gerente de Precatório",
      gerente_rpv: "Gerente de RPV",
      captador: "Captador",
      operacional_precatorio: "Operacional - Precatório",
      operacional_rpv: "Operacional - RPV",
      operacional_completo: "Operacional - Completo"
    };
    return displayNames[role] || role;
  };

  if (!user) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <Shield className="h-5 w-5" />
            Permissões Específicas - {user.nome || user.email}
          </DialogTitle>
          <div className="flex items-center gap-2 mt-2">
            <Badge variant="outline">
              Role: {getRoleDisplayName(user.role)}
            </Badge>
            <Badge variant="secondary">
              {userPermissions.length} permissão(ões) personalizada(s)
            </Badge>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          <div className="text-sm text-muted-foreground">
            <p>
              <strong>Permissões do Role:</strong> Baseadas no tipo de usuário ({getRoleDisplayName(user.role)})
            </p>
            <p>
              <strong>Permissões Personalizadas:</strong> Sobrescrevem as permissões do role para este usuário específico
            </p>
          </div>

          {loading ? (
            <div className="flex justify-center items-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : (
            <ScrollArea className="h-[500px]">
              <div className="space-y-6">
                {RESOURCE_TYPES.map((resource) => (
                  <Card key={resource.value}>
                    <CardHeader className="pb-3">
                      <CardTitle className="flex items-center gap-2 text-base">
                        {resource.icon}
                        {resource.label}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                        {ACTIONS.map((action) => {
                          const { hasPermission, isCustom } = getUserPermission(resource.value, action.value);
                          
                          return (
                            <div key={action.value} className="flex items-center justify-between p-3 border rounded-lg">
                              <div className="flex items-center space-x-2">
                                <span className="text-sm font-medium">{action.label}</span>
                                {isCustom && (
                                  <Badge variant="outline" className="text-xs">
                                    Personalizada
                                  </Badge>
                                )}
                              </div>
                              <Switch
                                checked={hasPermission}
                                onCheckedChange={(checked) => 
                                  updateUserPermission(resource.value, action.value, checked)
                                }
                              />
                            </div>
                          );
                        })}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </ScrollArea>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Fechar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
