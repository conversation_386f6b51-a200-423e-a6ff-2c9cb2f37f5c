import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2, Save, UserCog, FileText, Lock, Eye, Users, LayoutDashboard } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/hooks/useAuth';
import { 
  getUserPermissions, 
  updateUserPermission, 
  updateTaskVisibilitySettings, 
  updatePageAccess,
  UserPermissionsData,
  TaskVisibilitySettings
} from '@/services/permissionsService';
import { supabase } from '@/lib/supabase';

interface UserPermissionsManagerProps {
  userId: string;
  userName: string;
  userRole: string;
  onPermissionsUpdated?: () => void;
}

interface User {
  id: string;
  name: string;
  avatar_url?: string;
}

interface ResourcePermission {
  resourceType: string;
  resourceName: string;
  actions: {
    action: string;
    label: string;
    allowed: boolean;
  }[];
}

interface PageAccess {
  path: string;
  name: string;
  canAccess: boolean;
}

export function UserPermissionsManager({ 
  userId, 
  userName, 
  userRole,
  onPermissionsUpdated 
}: UserPermissionsManagerProps) {
  const { user } = useAuth();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('resource-permissions');
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [permissions, setPermissions] = useState<UserPermissionsData | null>(null);
  const [resourcePermissions, setResourcePermissions] = useState<ResourcePermission[]>([]);
  const [taskVisibility, setTaskVisibility] = useState<TaskVisibilitySettings>({
    can_see_own_tasks: true,
    can_see_team_tasks: false,
    can_see_all_tasks: false,
    visible_user_ids: []
  });
  const [pageAccess, setPageAccess] = useState<PageAccess[]>([]);
  const [allUsers, setAllUsers] = useState<User[]>([]);
  
  // Carregar permissões do usuário
  useEffect(() => {
    const loadPermissions = async () => {
      try {
        setLoading(true);
        
        // Carregar permissões
        const userPermissions = await getUserPermissions(userId);
        setPermissions(userPermissions);
        
        // Configurar permissões de recursos
        setupResourcePermissions(userPermissions);
        
        // Configurar visibilidade de tarefas
        if (userPermissions.task_visibility) {
          setTaskVisibility(userPermissions.task_visibility);
        }
        
        // Configurar acesso a páginas
        setupPageAccess(userPermissions);
        
        // Carregar todos os usuários para seleção de visibilidade de tarefas
        await loadAllUsers();
      } catch (error) {
        console.error('Erro ao carregar permissões:', error);
        toast({
          title: 'Erro ao carregar permissões',
          description: 'Não foi possível carregar as permissões do usuário.',
          variant: 'destructive'
        });
      } finally {
        setLoading(false);
      }
    };
    
    loadPermissions();
  }, [userId]);
  
  // Configurar permissões de recursos
  const setupResourcePermissions = (userPermissions: UserPermissionsData) => {
    const resources: ResourcePermission[] = [
      {
        resourceType: 'precatorio',
        resourceName: 'Precatórios',
        actions: [
          { action: 'view', label: 'Visualizar', allowed: false },
          { action: 'create', label: 'Criar', allowed: false },
          { action: 'edit', label: 'Editar', allowed: false },
          { action: 'delete', label: 'Excluir', allowed: false }
        ]
      },
      {
        resourceType: 'cliente',
        resourceName: 'Clientes',
        actions: [
          { action: 'view', label: 'Visualizar', allowed: false },
          { action: 'create', label: 'Criar', allowed: false },
          { action: 'edit', label: 'Editar', allowed: false },
          { action: 'delete', label: 'Excluir', allowed: false }
        ]
      },
      {
        resourceType: 'task',
        resourceName: 'Tarefas',
        actions: [
          { action: 'view', label: 'Visualizar', allowed: false },
          { action: 'create', label: 'Criar', allowed: false },
          { action: 'edit', label: 'Editar', allowed: false },
          { action: 'delete', label: 'Excluir', allowed: false },
          { action: 'assign', label: 'Atribuir', allowed: false }
        ]
      },
      {
        resourceType: 'document',
        resourceName: 'Documentos',
        actions: [
          { action: 'view', label: 'Visualizar', allowed: false },
          { action: 'create', label: 'Criar', allowed: false },
          { action: 'edit', label: 'Editar', allowed: false },
          { action: 'delete', label: 'Excluir', allowed: false }
        ]
      },
      {
        resourceType: 'user',
        resourceName: 'Usuários',
        actions: [
          { action: 'view', label: 'Visualizar', allowed: false },
          { action: 'create', label: 'Criar', allowed: false },
          { action: 'edit', label: 'Editar', allowed: false },
          { action: 'delete', label: 'Excluir', allowed: false }
        ]
      }
    ];
    
    // Marcar permissões baseadas em papel (role)
    userPermissions.role_permissions.forEach(permission => {
      const resource = resources.find(r => r.resourceType === permission.resource_type);
      if (resource) {
        const action = resource.actions.find(a => a.action === permission.action);
        if (action) {
          action.allowed = true;
        }
      }
    });
    
    // Sobrescrever com permissões específicas
    userPermissions.specific_permissions.forEach(permission => {
      if (!permission.resource_id) { // Apenas permissões globais
        const resource = resources.find(r => r.resourceType === permission.resource_type);
        if (resource) {
          const action = resource.actions.find(a => a.action === permission.action);
          if (action) {
            action.allowed = permission.allowed;
          }
        }
      }
    });
    
    setResourcePermissions(resources);
  };
  
  // Configurar acesso a páginas
  const setupPageAccess = (userPermissions: UserPermissionsData) => {
    const pages: PageAccess[] = [
      { path: '/dashboard', name: 'Dashboard', canAccess: true },
      { path: '/precatorios', name: 'Precatórios', canAccess: true },
      { path: '/clientes', name: 'Clientes', canAccess: true },
      { path: '/tarefas', name: 'Tarefas', canAccess: true },
      { path: '/documentos', name: 'Documentos', canAccess: true },
      { path: '/calendario', name: 'Calendário', canAccess: true },
      { path: '/usuarios', name: 'Usuários', canAccess: userRole === 'admin' },
      { path: '/configuracoes', name: 'Configurações', canAccess: userRole === 'admin' }
    ];
    
    // Aplicar configurações específicas
    if (userPermissions.page_access) {
      userPermissions.page_access.forEach(access => {
        const page = pages.find(p => p.path === access.page_path);
        if (page) {
          page.canAccess = access.can_access;
        }
      });
    }
    
    setPageAccess(pages);
  };
  
  // Carregar todos os usuários
  const loadAllUsers = async () => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('id, name, avatar_url')
        .order('name');
      
      if (error) throw error;
      
      setAllUsers(data || []);
    } catch (error) {
      console.error('Erro ao carregar usuários:', error);
    }
  };
  
  // Atualizar permissão de recurso
  const handleResourcePermissionChange = (resourceType: string, action: string, allowed: boolean) => {
    setResourcePermissions(prev => 
      prev.map(resource => 
        resource.resourceType === resourceType
          ? {
              ...resource,
              actions: resource.actions.map(a => 
                a.action === action ? { ...a, allowed } : a
              )
            }
          : resource
      )
    );
  };
  
  // Atualizar acesso a página
  const handlePageAccessChange = (path: string, canAccess: boolean) => {
    setPageAccess(prev => 
      prev.map(page => 
        page.path === path ? { ...page, canAccess } : page
      )
    );
  };
  
  // Atualizar visibilidade de tarefas
  const handleTaskVisibilityChange = (field: keyof TaskVisibilitySettings, value: any) => {
    setTaskVisibility(prev => ({
      ...prev,
      [field]: value
    }));
  };
  
  // Salvar todas as permissões
  const savePermissions = async () => {
    if (!user) return;
    
    try {
      setSaving(true);
      
      // 1. Salvar permissões de recursos
      for (const resource of resourcePermissions) {
        for (const action of resource.actions) {
          await updateUserPermission(
            user.id,
            userId,
            resource.resourceType,
            action.action,
            action.allowed
          );
        }
      }
      
      // 2. Salvar configurações de visibilidade de tarefas
      await updateTaskVisibilitySettings(
        user.id,
        userId,
        taskVisibility
      );
      
      // 3. Salvar acesso a páginas
      for (const page of pageAccess) {
        await updatePageAccess(
          user.id,
          userId,
          page.path,
          page.canAccess
        );
      }
      
      toast({
        title: 'Permissões atualizadas',
        description: 'As permissões do usuário foram atualizadas com sucesso.',
        variant: 'default'
      });
      
      // Notificar componente pai
      if (onPermissionsUpdated) {
        onPermissionsUpdated();
      }
    } catch (error) {
      console.error('Erro ao salvar permissões:', error);
      toast({
        title: 'Erro ao salvar permissões',
        description: 'Não foi possível salvar as permissões do usuário.',
        variant: 'destructive'
      });
    } finally {
      setSaving(false);
    }
  };
  
  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }
  
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Permissões do Usuário</h2>
          <p className="text-muted-foreground">
            Configure as permissões para <span className="font-medium">{userName}</span>
          </p>
        </div>
        
        <Badge variant={userRole === 'admin' ? 'default' : 'outline'}>
          {userRole === 'admin' ? 'Administrador' : 
           userRole === 'gerente_precatorio' ? 'Gerente' : 
           userRole === 'assistente' ? 'Assistente' : userRole}
        </Badge>
      </div>
      
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="resource-permissions" className="flex items-center gap-2">
            <Lock className="h-4 w-4" />
            <span>Permissões de Recursos</span>
          </TabsTrigger>
          <TabsTrigger value="task-visibility" className="flex items-center gap-2">
            <Eye className="h-4 w-4" />
            <span>Visibilidade de Tarefas</span>
          </TabsTrigger>
          <TabsTrigger value="page-access" className="flex items-center gap-2">
            <LayoutDashboard className="h-4 w-4" />
            <span>Acesso a Páginas</span>
          </TabsTrigger>
        </TabsList>
        
        {/* Permissões de Recursos */}
        <TabsContent value="resource-permissions" className="space-y-4 mt-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>Permissões de Recursos</CardTitle>
              <CardDescription>
                Configure o que este usuário pode fazer em cada tipo de recurso
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[400px] pr-4">
                <div className="space-y-6">
                  {resourcePermissions.map((resource) => (
                    <div key={resource.resourceType} className="space-y-3">
                      <div className="flex items-center gap-2">
                        <h3 className="text-lg font-medium">{resource.resourceName}</h3>
                        <Badge variant="outline" className="ml-2">
                          {resource.resourceType}
                        </Badge>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {resource.actions.map((action) => (
                          <div key={action.action} className="flex items-center justify-between space-x-2">
                            <Label htmlFor={`${resource.resourceType}-${action.action}`} className="flex-1">
                              {action.label}
                            </Label>
                            <Switch
                              id={`${resource.resourceType}-${action.action}`}
                              checked={action.allowed}
                              onCheckedChange={(checked) => 
                                handleResourcePermissionChange(resource.resourceType, action.action, checked)
                              }
                            />
                          </div>
                        ))}
                      </div>
                      
                      <Separator className="my-4" />
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Visibilidade de Tarefas */}
        <TabsContent value="task-visibility" className="space-y-4 mt-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>Visibilidade de Tarefas</CardTitle>
              <CardDescription>
                Configure quais tarefas este usuário pode ver
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      id="can-see-own-tasks" 
                      checked={taskVisibility.can_see_own_tasks}
                      onCheckedChange={(checked) => 
                        handleTaskVisibilityChange('can_see_own_tasks', checked === true)
                      }
                    />
                    <Label htmlFor="can-see-own-tasks">Pode ver suas próprias tarefas</Label>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      id="can-see-team-tasks" 
                      checked={taskVisibility.can_see_team_tasks}
                      onCheckedChange={(checked) => 
                        handleTaskVisibilityChange('can_see_team_tasks', checked === true)
                      }
                    />
                    <Label htmlFor="can-see-team-tasks">Pode ver tarefas da equipe</Label>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      id="can-see-all-tasks" 
                      checked={taskVisibility.can_see_all_tasks}
                      onCheckedChange={(checked) => 
                        handleTaskVisibilityChange('can_see_all_tasks', checked === true)
                      }
                    />
                    <Label htmlFor="can-see-all-tasks">Pode ver todas as tarefas</Label>
                  </div>
                </div>
                
                {taskVisibility.can_see_team_tasks && !taskVisibility.can_see_all_tasks && (
                  <div className="space-y-2 pt-4">
                    <Label>Usuários visíveis</Label>
                    <p className="text-sm text-muted-foreground mb-2">
                      Selecione os usuários cujas tarefas este usuário pode ver
                    </p>
                    
                    <ScrollArea className="h-[200px] border rounded-md p-4">
                      <div className="space-y-2">
                        {allUsers
                          .filter(u => u.id !== userId) // Excluir o próprio usuário
                          .map((user) => (
                            <div key={user.id} className="flex items-center space-x-2">
                              <Checkbox 
                                id={`user-${user.id}`} 
                                checked={taskVisibility.visible_user_ids.includes(user.id)}
                                onCheckedChange={(checked) => {
                                  if (checked) {
                                    handleTaskVisibilityChange('visible_user_ids', [
                                      ...taskVisibility.visible_user_ids,
                                      user.id
                                    ]);
                                  } else {
                                    handleTaskVisibilityChange('visible_user_ids', 
                                      taskVisibility.visible_user_ids.filter(id => id !== user.id)
                                    );
                                  }
                                }}
                              />
                              <Label htmlFor={`user-${user.id}`} className="flex items-center gap-2">
                                {user.avatar_url ? (
                                  <img 
                                    src={user.avatar_url} 
                                    alt={user.name} 
                                    className="h-6 w-6 rounded-full"
                                  />
                                ) : (
                                  <div className="h-6 w-6 rounded-full bg-muted flex items-center justify-center">
                                    <UserCog className="h-3 w-3" />
                                  </div>
                                )}
                                <span>{user.name}</span>
                              </Label>
                            </div>
                          ))}
                      </div>
                    </ScrollArea>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Acesso a Páginas */}
        <TabsContent value="page-access" className="space-y-4 mt-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>Acesso a Páginas</CardTitle>
              <CardDescription>
                Configure quais páginas este usuário pode acessar
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {pageAccess.map((page) => (
                  <div key={page.path} className="flex items-center justify-between space-x-2">
                    <Label htmlFor={`page-${page.path}`} className="flex-1">
                      {page.name}
                      <span className="text-xs text-muted-foreground block">
                        {page.path}
                      </span>
                    </Label>
                    <Switch
                      id={`page-${page.path}`}
                      checked={page.canAccess}
                      onCheckedChange={(checked) => 
                        handlePageAccessChange(page.path, checked)
                      }
                    />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      
      <div className="flex justify-end">
        <Button 
          onClick={savePermissions} 
          disabled={saving}
          className="flex items-center gap-2"
        >
          {saving ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <Save className="h-4 w-4" />
          )}
          Salvar Permissões
        </Button>
      </div>
    </div>
  );
}
