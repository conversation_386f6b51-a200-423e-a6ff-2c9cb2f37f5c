import { ReactNode, useEffect, useState } from "react";
import { Navigate, useLocation } from "react-router-dom";
import { supabase } from "@/lib/supabase";
import { Loader2 } from "lucide-react";

type UserRole = 
  | "admin" 
  | "gerente_geral" 
  | "gerente_precatorio" 
  | "gerente_rpv" 
  | "captador" 
  | "operacional_precatorio" 
  | "operacional_rpv" 
  | "operacional_completo";

interface ProtectedRouteProps {
  children: ReactNode;
  allowedRoles?: UserRole[];
}

export default function ProtectedRoute({ children, allowedRoles }: ProtectedRouteProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [userRole, setUserRole] = useState<UserRole | null>(null);
  const location = useLocation();
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    let isMounted = true; // Para evitar atualização de estado após desmontagem

    const checkAuth = async () => {
      try {
        console.log("ProtectedRoute: Verificando autenticação...");
        // Verificar se o usuário está autenticado
        const { data: session, error: sessionError } = await supabase.auth.getSession();

        if (sessionError) {
          console.error("ProtectedRoute: Erro ao obter sessão:", sessionError);
          if (isMounted) {
            setHasError(true);
            setIsLoading(false);
          }
          return;
        }

        if (session && session.session) {
          console.log("ProtectedRoute: Sessão encontrada, verificando perfil");
          try {
            const { data: profileData, error: profileError } = await supabase
              .from("profiles")
              .select("role, status")
              .eq("id", session.session.user.id)
              .single();

            if (profileError) {
              console.error("ProtectedRoute: Erro ao buscar perfil:", profileError);
              if (isMounted) {
                setHasError(true);
                setIsLoading(false);
              }
              return;
            }

            // Verificar se o perfil existe e está ativo
            if (profileData && profileData.status === "ativo") {
              console.log("ProtectedRoute: Usuário autenticado com role:", profileData.role);
              if (isMounted) {
                setIsAuthenticated(true);
                setUserRole(profileData.role as UserRole);
              }
            } else {
              // Perfil não encontrado ou inativo
              console.log("ProtectedRoute: Perfil inativo ou não encontrado");
              if (isMounted) {
                setIsAuthenticated(false);
              }
              await supabase.auth.signOut();
            }
          } catch (error) {
            console.error("ProtectedRoute: Erro ao processar perfil:", error);
            if (isMounted) {
              setHasError(true);
            }
          }
        } else {
          console.log("ProtectedRoute: Nenhuma sessão encontrada");
          if (isMounted) {
            setIsAuthenticated(false);
          }
        }
      } catch (error) {
        console.error("ProtectedRoute: Erro não tratado:", error);
        if (isMounted) {
          setHasError(true);
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    // Definir um timeout para evitar espera infinita
    const timeoutId = setTimeout(() => {
      if (isLoading && isMounted) {
        console.log("ProtectedRoute: Timeout atingido, redirecionando para login");
        setIsLoading(false);
        setIsAuthenticated(false);
      }
    }, 5000); // 5 segundos de timeout

    checkAuth();

    // Cleanup
    return () => {
      isMounted = false;
      clearTimeout(timeoutId);
    };
  }, []);

  // Mostrar loader enquanto verifica autenticação
  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  // Mostrar erro se ocorreu algum problema
  if (hasError) {
    return (
      <div className="flex min-h-screen items-center justify-center flex-col">
        <div className="text-red-500 mb-4">Erro ao verificar autenticação</div>
        <button 
          onClick={() => window.location.href = "/login"} 
          className="px-4 py-2 bg-primary text-white rounded-md"
        >
          Voltar para o login
        </button>
      </div>
    );
  }

  // Redirecionar para login se não estiver autenticado
  if (!isAuthenticated) {
    console.log("ProtectedRoute: Usuário não autenticado, redirecionando para login");
    // Usar window.location para um redirecionamento mais forte
    window.location.href = "/login";
    return null;
  }

  // Verificar permissões de acesso com base na role
  if (allowedRoles && userRole && !allowedRoles.includes(userRole)) {
    console.log(`ProtectedRoute: Usuário com role ${userRole} tentando acessar rota restrita a ${allowedRoles.join(", ")}`);
    
    // Redirecionamento baseado nos novos papéis
    if (userRole === "operacional_precatorio") {
      console.log("ProtectedRoute: Redirecionando operacional precatório para /tarefas");
      window.location.href = "/tarefas?area=PRECATORIO";
      return null;
    }
    
    if (userRole === "operacional_rpv") {
      console.log("ProtectedRoute: Redirecionando operacional RPV para /tarefas");
      window.location.href = "/tarefas?area=RPV";
      return null;
    }
    
    if (userRole === "operacional_completo") {
      console.log("ProtectedRoute: Redirecionando operacional completo para /tarefas");
      window.location.href = "/tarefas";
      return null;
    }
    
    if (userRole === "gerente_precatorio") {
      console.log("ProtectedRoute: Redirecionando gerente precatório para /precatorios");
      window.location.href = "/precatorios?tipo=PRECATORIO";
      return null;
    }
    
    if (userRole === "gerente_rpv") {
      console.log("ProtectedRoute: Redirecionando gerente RPV para /precatorios");
      window.location.href = "/precatorios?tipo=RPV";
      return null;
    }
    
    if (userRole === "gerente_geral") {
      console.log("ProtectedRoute: Redirecionando gerente geral para /precatorios");
      window.location.href = "/precatorios";
      return null;
    }
    
    if (userRole === "captador") {
      console.log("ProtectedRoute: Redirecionando captador para /clientes");
      window.location.href = "/clientes";
      return null;
    }
    
    // Para outros casos, redireciona para dashboard
    console.log("ProtectedRoute: Redirecionando para /dashboard (caso padrão)");
    window.location.href = "/dashboard";
    return null;
  }

  // Se passou por todas as verificações, renderiza o conteúdo
  console.log("ProtectedRoute: Renderizando conteúdo autorizado");
  return <>{children}</>;
}
