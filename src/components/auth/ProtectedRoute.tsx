import React, { ReactNode, useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { usePermissions } from '@/hooks/usePermissions';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Loader2,
  Lock,
  Shield,
  ArrowLeft,
  Home,
  Eye,
  EyeOff
} from 'lucide-react';

interface ProtectedRouteProps {
  children: ReactNode;
  requiredPermissions?: {
    resourceType: string;
    action: string;
    resourceId?: string;
  }[];
  requiredRole?: string | string[];
  adminOnly?: boolean;
  managerOnly?: boolean;
  fallbackPath?: string;
  showAccessDenied?: boolean;
}

interface AccessDeniedProps {
  reason: string;
  canGoBack?: boolean;
  fallbackPath?: string;
  suggestions?: string[];
}

function AccessDenied({ reason, canGoBack = true, fallbackPath = '/dashboard', suggestions = [] }: AccessDeniedProps) {
  const handleGoBack = () => {
    if (window.history.length > 1) {
      window.history.back();
    } else {
      window.location.href = fallbackPath;
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <Card className="w-full max-w-md">
        <CardContent className="p-6 text-center space-y-6">
          <div className="flex justify-center">
            <div className="rounded-full bg-destructive/10 p-3">
              <Lock className="h-8 w-8 text-destructive" />
            </div>
          </div>

          <div className="space-y-2">
            <h1 className="text-2xl font-bold text-foreground">Acesso Negado</h1>
            <p className="text-muted-foreground">{reason}</p>
          </div>

          {suggestions.length > 0 && (
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-foreground">Sugestões:</h3>
              <ul className="text-sm text-muted-foreground space-y-1">
                {suggestions.map((suggestion, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <span className="text-primary">•</span>
                    {suggestion}
                  </li>
                ))}
              </ul>
            </div>
          )}

          <div className="flex flex-col gap-2">
            {canGoBack && (
              <Button onClick={handleGoBack} variant="outline" className="w-full">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Voltar
              </Button>
            )}
            <Button asChild className="w-full">
              <a href={fallbackPath}>
                <Home className="h-4 w-4 mr-2" />
                Ir para Dashboard
              </a>
            </Button>
          </div>

          <div className="text-xs text-muted-foreground">
            Se você acredita que deveria ter acesso a esta página, entre em contato com o administrador.
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredPermissions = [],
  requiredRole,
  adminOnly = false,
  managerOnly = false,
  fallbackPath = '/dashboard',
  showAccessDenied = true
}) => {
  const { user, loading: authLoading } = useAuth();
  const {
    canAccessPageEnhanced,
    hasPermission,
    isAdmin,
    isManager,
    loading: permissionsLoading
  } = usePermissions();
  const navigate = useNavigate();
  const location = useLocation();
  const [hasAccess, setHasAccess] = useState<boolean | null>(null);
  const [accessDeniedReason, setAccessDeniedReason] = useState('');
  const [suggestions, setSuggestions] = useState<string[]>([]);

  useEffect(() => {
    checkAccess();
  }, [user, authLoading, permissionsLoading, location.pathname]);

  const checkAccess = async () => {
    // Aguardar carregamento da autenticação e permissões
    if (authLoading || permissionsLoading) {
      return;
    }

    // Verificar se o usuário está autenticado
    if (!user) {
      navigate('/login', { state: { from: location.pathname } });
      return;
    }

    let access = true;
    let reason = '';
    let pageSuggestions: string[] = [];

    // Verificar se é admin only
    if (adminOnly && !isAdmin) {
      access = false;
      reason = 'Esta página é restrita apenas para administradores.';
      pageSuggestions = [
        'Entre em contato com um administrador',
        'Verifique se você tem as permissões necessárias'
      ];
    }

    // Verificar se é manager only
    if (managerOnly && !isManager && !isAdmin) {
      access = false;
      reason = 'Esta página é restrita para gerentes e administradores.';
      pageSuggestions = [
        'Entre em contato com seu gerente',
        'Solicite elevação de permissões se necessário'
      ];
    }

    // Verificar role específico
    if (requiredRole && access) {
      const roles = Array.isArray(requiredRole) ? requiredRole : [requiredRole];
      if (!roles.includes(user.role)) {
        access = false;
        reason = `Esta página requer um dos seguintes papéis: ${roles.join(', ')}.`;
        pageSuggestions = [
          'Verifique seu papel no sistema',
          'Solicite alteração de papel se necessário'
        ];
      }
    }

    // Verificar acesso à página
    if (access) {
      const canAccess = canAccessPageEnhanced(location.pathname);
      if (!canAccess) {
        access = false;
        reason = 'Você não tem permissão para acessar esta página.';
        pageSuggestions = [
          'Verifique suas permissões de acesso',
          'Entre em contato com o administrador'
        ];
      }
    }

    // Verificar permissões específicas, se houver
    if (requiredPermissions.length > 0 && access) {
      const hasAllPermissions = requiredPermissions.every(({ resourceType, action, resourceId }) =>
        hasPermission(resourceType, action, resourceId)
      );

      if (!hasAllPermissions) {
        access = false;
        reason = 'Você não possui todas as permissões necessárias para acessar esta página.';
        pageSuggestions = [
          'Solicite as permissões necessárias ao administrador',
          'Verifique se suas permissões estão atualizadas'
        ];
      }
    }

    setHasAccess(access);
    setAccessDeniedReason(reason);
    setSuggestions(pageSuggestions);

    // Se não tem acesso e deve mostrar página de erro
    if (!access && !showAccessDenied) {
      navigate(fallbackPath);
    }
  };

  // Mostrar loader enquanto verifica permissões
  if (authLoading || permissionsLoading || hasAccess === null) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  // Acesso negado - mostrar página de erro
  if (!hasAccess) {
    return (
      <AccessDenied
        reason={accessDeniedReason}
        fallbackPath={fallbackPath}
        suggestions={suggestions}
      />
    );
  }

  // Renderizar conteúdo se tiver acesso
  return <>{children}</>;
};

// Componente para proteger seções específicas dentro de uma página
export function ProtectedSection({
  children,
  requiredPermissions = [],
  requiredRole,
  adminOnly = false,
  managerOnly = false,
  fallback = null,
  showPlaceholder = true
}: Omit<ProtectedRouteProps, 'fallbackPath' | 'showAccessDenied'> & {
  fallback?: React.ReactNode;
  showPlaceholder?: boolean;
}) {
  const { user } = useAuth();
  const { hasPermission, isAdmin, isManager } = usePermissions();

  if (!user) return null;

  // Verificar admin only
  if (adminOnly && !isAdmin) {
    return showPlaceholder ? (
      <div className="p-4 border border-dashed border-muted-foreground/30 rounded-lg text-center text-muted-foreground">
        <EyeOff className="h-6 w-6 mx-auto mb-2" />
        <p className="text-sm">Conteúdo restrito para administradores</p>
      </div>
    ) : fallback;
  }

  // Verificar manager only
  if (managerOnly && !isManager && !isAdmin) {
    return showPlaceholder ? (
      <div className="p-4 border border-dashed border-muted-foreground/30 rounded-lg text-center text-muted-foreground">
        <Shield className="h-6 w-6 mx-auto mb-2" />
        <p className="text-sm">Conteúdo restrito para gerentes</p>
      </div>
    ) : fallback;
  }

  // Verificar role específico
  if (requiredRole) {
    const roles = Array.isArray(requiredRole) ? requiredRole : [requiredRole];
    if (!roles.includes(user.role)) {
      return showPlaceholder ? (
        <div className="p-4 border border-dashed border-muted-foreground/30 rounded-lg text-center text-muted-foreground">
          <Lock className="h-6 w-6 mx-auto mb-2" />
          <p className="text-sm">Acesso restrito</p>
        </div>
      ) : fallback;
    }
  }

  // Verificar permissões específicas
  if (requiredPermissions.length > 0) {
    const hasAllPermissions = requiredPermissions.every(({ resourceType, action, resourceId }) =>
      hasPermission(resourceType, action, resourceId)
    );

    if (!hasAllPermissions) {
      return showPlaceholder ? (
        <div className="p-4 border border-dashed border-muted-foreground/30 rounded-lg text-center text-muted-foreground">
          <Eye className="h-6 w-6 mx-auto mb-2" />
          <p className="text-sm">Permissões insuficientes</p>
        </div>
      ) : fallback;
    }
  }

  return <>{children}</>;
}
