/**
 * Script para sincronizar os status_id dos precatórios com os status_uuid das colunas do kanban
 * 
 * Este script:
 * 1. Busca todos os precatórios
 * 2. Busca todos os status de precatórios
 * 3. Busca todas as colunas do kanban
 * 4. Atualiza os status_id dos precatórios com base no código de status
 * 5. Atualiza os status_uuid das colunas com base no código de status
 */

import { createClient } from '@supabase/supabase-js';

// Configurar cliente Supabase
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Variáveis de ambiente VITE_SUPABASE_URL e VITE_SUPABASE_ANON_KEY são necessárias');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Função principal
async function sincronizarStatusPrecatorios() {
  try {
    console.log('Iniciando sincronização de status de precatórios...');
    console.log(`Usando Supabase URL: ${supabaseUrl}`);

    // 1. Buscar todos os precatórios
    const { data: precatorios, error: precatoriosError } = await supabase
      .from('precatorios')
      .select('id, numero_precatorio, status, status_id, tipo')
      .or('is_deleted.is.null,is_deleted.eq.false');

    if (precatoriosError) {
      console.error('Erro ao buscar precatórios:', precatoriosError);
      return;
    }

    console.log(`Encontrados ${precatorios?.length || 0} precatórios`);

    // 2. Buscar todos os status de precatórios
    const { data: statusPrecatorios, error: statusError } = await supabase
      .from('status_precatorios')
      .select('id, nome, codigo, cor')
      .eq('ativo', true);

    if (statusError) {
      console.error('Erro ao buscar status de precatórios:', statusError);
      return;
    }

    console.log(`Encontrados ${statusPrecatorios?.length || 0} status de precatórios`);

    // 3. Buscar todas as colunas do kanban
    const { data: colunas, error: colunasError } = await supabase
      .from('kanban_colunas')
      .select('id, nome, status_id, status_uuid, status_ref_id')
      .eq('ativo', true);

    if (colunasError) {
      console.error('Erro ao buscar colunas do kanban:', colunasError);
      return;
    }

    console.log(`Encontradas ${colunas?.length || 0} colunas do kanban`);

    // Criar um mapa de código de status para ID de status
    const statusMap = new Map();
    statusPrecatorios?.forEach(status => {
      statusMap.set(status.codigo, status.id);
      console.log(`Status: ${status.nome} (${status.codigo}) -> ID: ${status.id}`);
    });

    // 4. Atualizar os status_id dos precatórios
    const precatoriosParaAtualizar = [];
    
    for (const precatorio of (precatorios || [])) {
      // Se o precatório tem status mas não tem status_id, ou tem status_id inválido
      if (precatorio.status && (!precatorio.status_id || !statusMap.has(precatorio.status))) {
        const statusId = statusMap.get(precatorio.status);
        
        if (statusId) {
          precatoriosParaAtualizar.push({
            id: precatorio.id,
            status_id: statusId
          });
          
          console.log(`Precatório ${precatorio.numero_precatorio} (${precatorio.id}): status=${precatorio.status} -> status_id=${statusId}`);
        } else {
          console.warn(`⚠️ Não foi encontrado status_id para o código ${precatorio.status} do precatório ${precatorio.id}`);
        }
      }
    }
    
    console.log(`${precatoriosParaAtualizar.length} precatórios precisam ser atualizados`);
    
    // Atualizar os precatórios em lotes para evitar sobrecarga
    const BATCH_SIZE = 50;
    for (let i = 0; i < precatoriosParaAtualizar.length; i += BATCH_SIZE) {
      const batch = precatoriosParaAtualizar.slice(i, i + BATCH_SIZE);
      console.log(`Atualizando lote ${i/BATCH_SIZE + 1} de ${Math.ceil(precatoriosParaAtualizar.length/BATCH_SIZE)}...`);
      
      for (const precatorio of batch) {
        const { error } = await supabase
          .from('precatorios')
          .update({ status_id: precatorio.status_id })
          .eq('id', precatorio.id);
          
        if (error) {
          console.error(`Erro ao atualizar precatório ${precatorio.id}:`, error);
        }
      }
    }
    
    // 5. Atualizar os status_uuid das colunas
    const colunasParaAtualizar = [];
    
    for (const coluna of (colunas || [])) {
      // Se a coluna tem status_id (código) mas não tem status_uuid
      if (coluna.status_id && (!coluna.status_uuid || !coluna.status_ref_id)) {
        const statusId = statusMap.get(coluna.status_id);
        
        if (statusId) {
          colunasParaAtualizar.push({
            id: coluna.id,
            status_uuid: statusId,
            status_ref_id: statusId
          });
          
          console.log(`Coluna ${coluna.nome} (${coluna.id}): status_id=${coluna.status_id} -> status_uuid=${statusId}`);
        } else {
          console.warn(`⚠️ Não foi encontrado status_uuid para o código ${coluna.status_id} da coluna ${coluna.id}`);
        }
      }
    }
    
    console.log(`${colunasParaAtualizar.length} colunas precisam ser atualizadas`);
    
    // Atualizar as colunas
    for (const coluna of colunasParaAtualizar) {
      const { error } = await supabase
        .from('kanban_colunas')
        .update({ 
          status_uuid: coluna.status_uuid,
          status_ref_id: coluna.status_ref_id
        })
        .eq('id', coluna.id);
        
      if (error) {
        console.error(`Erro ao atualizar coluna ${coluna.id}:`, error);
      }
    }
    
    console.log('Sincronização concluída com sucesso!');
    
  } catch (error) {
    console.error('Erro ao sincronizar status de precatórios:', error);
  }
}

// Executar o script
sincronizarStatusPrecatorios();
