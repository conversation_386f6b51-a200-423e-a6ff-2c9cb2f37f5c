/**
 * Script para corrigir os tipos dos precatórios no banco de dados
 * 
 * Este script:
 * 1. Busca todos os precatórios
 * 2. Verifica se o tipo está definido corretamente
 * 3. Atualiza os precatórios sem tipo ou com tipo em formato incorreto
 */

import { createClient } from '@supabase/supabase-js';

// Configurar cliente Supabase
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Variáveis de ambiente VITE_SUPABASE_URL e VITE_SUPABASE_ANON_KEY são necessárias');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Função principal
async function corrigirTiposPrecatorios() {
  try {
    console.log('Iniciando correção de tipos de precatórios...');
    console.log(`Usando Supabase URL: ${supabaseUrl}`);

    // 1. Buscar todos os precatórios
    const { data: precatorios, error: precatoriosError } = await supabase
      .from('precatorios')
      .select('id, numero_precatorio, tipo')
      .or('is_deleted.is.null,is_deleted.eq.false');

    if (precatoriosError) {
      console.error('Erro ao buscar precatórios:', precatoriosError);
      return;
    }

    console.log(`Encontrados ${precatorios?.length || 0} precatórios`);

    // 2. Verificar e corrigir os tipos dos precatórios
    const precatoriosParaAtualizar = [];
    
    for (const precatorio of (precatorios || [])) {
      // Verificar se o tipo está definido e no formato correto
      const tipoAtual = precatorio.tipo;
      let novoTipo = null;
      
      if (!tipoAtual) {
        // Se não tem tipo, definir como PRECATORIO por padrão
        novoTipo = 'PRECATORIO';
        console.log(`Precatório ${precatorio.numero_precatorio || precatorio.id}: sem tipo -> ${novoTipo}`);
      } else if (tipoAtual !== 'PRECATORIO' && tipoAtual !== 'RPV') {
        // Se o tipo não está no formato correto, normalizar
        novoTipo = tipoAtual.toUpperCase() === 'RPV' ? 'RPV' : 'PRECATORIO';
        console.log(`Precatório ${precatorio.numero_precatorio || precatorio.id}: tipo incorreto ${tipoAtual} -> ${novoTipo}`);
      }
      
      // Se precisa atualizar, adicionar à lista
      if (novoTipo) {
        precatoriosParaAtualizar.push({
          id: precatorio.id,
          tipo: novoTipo
        });
      }
    }
    
    console.log(`${precatoriosParaAtualizar.length} precatórios precisam ter o tipo corrigido`);
    
    // 3. Atualizar os precatórios
    for (const precatorio of precatoriosParaAtualizar) {
      const { error } = await supabase
        .from('precatorios')
        .update({ tipo: precatorio.tipo })
        .eq('id', precatorio.id);
        
      if (error) {
        console.error(`Erro ao atualizar precatório ${precatorio.id}:`, error);
      }
    }
    
    console.log('Correção de tipos concluída com sucesso!');
    
  } catch (error) {
    console.error('Erro ao corrigir tipos de precatórios:', error);
  }
}

// Executar o script
corrigirTiposPrecatorios();
