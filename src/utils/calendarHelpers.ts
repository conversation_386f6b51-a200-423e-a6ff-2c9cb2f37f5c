import { format, addDays, isSameDay, parseISO, addMonths } from 'date-fns';
import { ptBR } from 'date-fns/locale';

// Tipos de eventos
export interface Evento {
  id: string;
  tipo: string;
  titulo: string;
  descricao: string;
  data: string;
  hora: string;
  duracao: number;
  status: string;
  prioridade: string;
  local: string;
  responsavel: string;
}

// Tipos de visualização
export type Visualizacao = 'dia' | 'semana' | 'mes' | 'agenda';

// Interface para filtros
export interface Filtro {
  tipo: string[];
  status: string[];
  texto: string;
  prioridade?: string[];
}

// Função para gerar datas atuais baseadas na data atual
export const generateCurrentDates = () => {
  const today = new Date();
  const nextMonth = new Date();
  nextMonth.setMonth(nextMonth.getMonth() + 1);

  // Formatar hoje e datas próximas
  const formatDateString = (date: Date) => format(date, 'yyyy-MM-dd');

  return {
    today: formatDateString(today),
    tomorrow: formatDateString(addDays(today, 1)),
    nextWeek: formatDateString(addDays(today, 7)),
    nextMonth: formatDateString(addMonths(today, 1))
  };
};

// Função para filtrar eventos por dia
export const getEventosDoDia = (date: Date, eventos: Evento[]) => {
  const formattedDate = format(date, 'yyyy-MM-dd');
  return eventos.filter(evento => evento.data === formattedDate);
};

// Função para obter iniciais para o Avatar
export const getInitials = (name: string) => {
  if (!name) return '??';
  return name
    .split(' ')
    .map((part) => part[0])
    .slice(0, 2)
    .join('')
    .toUpperCase();
};

// Gerar cores baseadas no nome para o Avatar
export const getColorFromName = (name: string) => {
  if (!name) return 'bg-gray-500';

  const colors = [
    'bg-red-500', 'bg-blue-500', 'bg-green-500',
    'bg-yellow-500', 'bg-purple-500', 'bg-pink-500',
    'bg-indigo-500', 'bg-teal-500', 'bg-orange-500'
  ];

  // Usar soma de códigos de caracteres para gerar índice de cor
  const sum = name.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
  return colors[sum % colors.length];
};