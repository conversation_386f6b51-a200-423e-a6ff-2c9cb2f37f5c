/**
 * Utilitários de formatação para o Sistema Carbonário e Carbonaro
 * Implementa formatações específicas para o padrão brasileiro
 */

/**
 * Opções para formatação de moeda
 */
interface CurrencyFormatOptions {
  /** Mostrar símbolo da moeda (R$) */
  symbol?: boolean;
  /** Mostrar decimais */
  decimals?: boolean;
  /** Usar formato compacto para valores grandes (ex: 1.2M) */
  compact?: boolean;
  /** Número de casas decimais (padrão: 2) */
  decimalPlaces?: number;
}

/**
 * Formata um valor para moeda brasileira (R$)
 * @param value Valor a ser formatado
 * @param options Opções de formatação
 * @returns String formatada
 */
export function formatCurrency(value: number | string | null | undefined, options?: CurrencyFormatOptions): string {
  // Tratar valores nulos ou indefinidos
  if (value === null || value === undefined) return 'R$ 0,00';
  
  // Converter para número se for string
  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  
  // Verificar se é NaN
  if (isNaN(numValue)) return 'R$ 0,00';
  
  // Configurar opções padrão
  const defaultOptions: CurrencyFormatOptions = {
    symbol: true,
    decimals: true,
    compact: false,
    decimalPlaces: 2
  };
  
  const opts = { ...defaultOptions, ...options };
  
  // Usar o Intl.NumberFormat para formatação correta
  const formatter = new Intl.NumberFormat('pt-BR', {
    style: opts.symbol ? 'currency' : 'decimal',
    currency: 'BRL',
    minimumFractionDigits: opts.decimals ? opts.decimalPlaces : 0,
    maximumFractionDigits: opts.decimals ? opts.decimalPlaces : 0,
    notation: opts.compact ? 'compact' : 'standard',
    compactDisplay: 'short'
  });
  
  return formatter.format(numValue);
}

/**
 * Opções para formatação de números
 */
interface NumberFormatOptions {
  /** Número de casas decimais */
  decimalPlaces?: number;
  /** Usar formato compacto para valores grandes (ex: 1.2M) */
  compact?: boolean;
  /** Adicionar símbolo de percentual */
  percent?: boolean;
}

/**
 * Formata um número usando o padrão brasileiro
 * @param value Valor a ser formatado
 * @param options Opções de formatação
 * @returns String formatada
 */
export function formatNumber(value: number | string | null | undefined, options?: NumberFormatOptions): string {
  // Tratar valores nulos ou indefinidos
  if (value === null || value === undefined) return '0';
  
  // Converter para número se for string
  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  
  // Verificar se é NaN
  if (isNaN(numValue)) return '0';
  
  // Configurar opções padrão
  const defaultOptions: NumberFormatOptions = {
    decimalPlaces: 0,
    compact: false,
    percent: false
  };
  
  const opts = { ...defaultOptions, ...options };
  
  // Usar o Intl.NumberFormat para formatação correta
  const formatter = new Intl.NumberFormat('pt-BR', {
    style: opts.percent ? 'percent' : 'decimal',
    minimumFractionDigits: opts.decimalPlaces,
    maximumFractionDigits: opts.decimalPlaces,
    notation: opts.compact ? 'compact' : 'standard',
    compactDisplay: 'short'
  });
  
  return formatter.format(opts.percent ? numValue / 100 : numValue);
}

/**
 * Formata um valor como percentual
 * @param value Valor a ser formatado (0-100)
 * @param decimalPlaces Número de casas decimais
 * @returns String formatada com símbolo de percentual
 */
export function formatPercent(value: number | string | null | undefined, decimalPlaces: number = 1): string {
  return formatNumber(value, { percent: true, decimalPlaces });
}

/**
 * Opções para formatação de data
 */
interface DateFormatOptions {
  /** Formato da data (completo, curto, etc.) */
  format?: 'full' | 'short' | 'medium' | 'long' | 'datetime' | 'time' | 'monthYear';
  /** Incluir horário */
  includeTime?: boolean;
  /** Usar formato relativo (ex: "há 2 dias") */
  relative?: boolean;
}

/**
 * Formata uma data usando o padrão brasileiro
 * @param date Data a ser formatada (Date, string ISO ou timestamp)
 * @param options Opções de formatação
 * @returns String formatada
 */
export function formatDate(date: Date | string | number | null | undefined, options?: DateFormatOptions): string {
  // Tratar valores nulos ou indefinidos
  if (date === null || date === undefined) return '';
  
  // Converter para objeto Date
  let dateObj: Date;
  
  try {
    if (date instanceof Date) {
      dateObj = date;
    } else if (typeof date === 'string') {
      dateObj = new Date(date);
    } else if (typeof date === 'number') {
      dateObj = new Date(date);
    } else {
      return '';
    }
    
    // Verificar se a data é válida
    if (isNaN(dateObj.getTime())) {
      return '';
    }
  } catch (error) {
    return '';
  }
  
  // Configurar opções padrão
  const defaultOptions: DateFormatOptions = {
    format: 'short',
    includeTime: false,
    relative: false
  };
  
  const opts = { ...defaultOptions, ...options };
  
  // Formatar data relativa se solicitado
  if (opts.relative) {
    return formatRelativeDate(dateObj);
  }
  
  // Definir opções de formatação baseadas no formato solicitado
  let formatOptions: Intl.DateTimeFormatOptions = {};
  
  switch (opts.format) {
    case 'full':
      formatOptions = {
        dateStyle: 'full',
        timeStyle: opts.includeTime ? 'medium' : undefined
      } as Intl.DateTimeFormatOptions;
      break;
      
    case 'long':
      formatOptions = {
        dateStyle: 'long',
        timeStyle: opts.includeTime ? 'short' : undefined
      } as Intl.DateTimeFormatOptions;
      break;
      
    case 'medium':
      formatOptions = {
        dateStyle: 'medium',
        timeStyle: opts.includeTime ? 'short' : undefined
      } as Intl.DateTimeFormatOptions;
      break;
      
    case 'datetime':
      formatOptions = {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      };
      break;
      
    case 'time':
      formatOptions = {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      };
      break;
      
    case 'monthYear':
      formatOptions = {
        month: 'long',
        year: 'numeric'
      };
      break;
      
    case 'short':
    default:
      formatOptions = {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: opts.includeTime ? '2-digit' : undefined,
        minute: opts.includeTime ? '2-digit' : undefined
      };
      break;
  }
  
  // Usar o Intl.DateTimeFormat para formatação correta
  const formatter = new Intl.DateTimeFormat('pt-BR', formatOptions);
  return formatter.format(dateObj);
}

/**
 * Formata uma data em formato relativo (ex: "há 2 dias")
 * @param date Data a ser formatada
 * @returns String com data relativa
 */
function formatRelativeDate(date: Date): string {
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffSecs = Math.floor(diffMs / 1000);
  const diffMins = Math.floor(diffSecs / 60);
  const diffHours = Math.floor(diffMins / 60);
  const diffDays = Math.floor(diffHours / 24);
  const diffMonths = Math.floor(diffDays / 30);
  const diffYears = Math.floor(diffDays / 365);
  
  // Data no futuro
  if (diffMs < 0) {
    const absDiffSecs = Math.abs(diffSecs);
    const absDiffMins = Math.abs(diffMins);
    const absDiffHours = Math.abs(diffHours);
    const absDiffDays = Math.abs(diffDays);
    const absDiffMonths = Math.abs(diffMonths);
    const absDiffYears = Math.abs(diffYears);
    
    if (absDiffSecs < 60) return 'em poucos segundos';
    if (absDiffMins < 60) return `em ${absDiffMins} ${absDiffMins === 1 ? 'minuto' : 'minutos'}`;
    if (absDiffHours < 24) return `em ${absDiffHours} ${absDiffHours === 1 ? 'hora' : 'horas'}`;
    if (absDiffDays < 30) return `em ${absDiffDays} ${absDiffDays === 1 ? 'dia' : 'dias'}`;
    if (absDiffMonths < 12) return `em ${absDiffMonths} ${absDiffMonths === 1 ? 'mês' : 'meses'}`;
    return `em ${absDiffYears} ${absDiffYears === 1 ? 'ano' : 'anos'}`;
  }
  
  // Data no passado
  if (diffSecs < 60) return 'há poucos segundos';
  if (diffMins < 60) return `há ${diffMins} ${diffMins === 1 ? 'minuto' : 'minutos'}`;
  if (diffHours < 24) return `há ${diffHours} ${diffHours === 1 ? 'hora' : 'horas'}`;
  if (diffDays < 30) return `há ${diffDays} ${diffDays === 1 ? 'dia' : 'dias'}`;
  if (diffMonths < 12) return `há ${diffMonths} ${diffMonths === 1 ? 'mês' : 'meses'}`;
  return `há ${diffYears} ${diffYears === 1 ? 'ano' : 'anos'}`;
}

/**
 * Formata um número de telefone brasileiro
 * @param phone Número de telefone (apenas dígitos)
 * @returns Telefone formatado
 */
export function formatPhone(phone: string | null | undefined): string {
  if (!phone) return '';
  
  // Remover caracteres não numéricos
  const digits = phone.replace(/\D/g, '');
  
  // Verificar se é um número válido
  if (digits.length < 8) return phone;
  
  // Formatar conforme o número de dígitos
  if (digits.length === 8) {
    // Telefone fixo sem DDD: 1234-5678
    return `${digits.slice(0, 4)}-${digits.slice(4)}`;
  } else if (digits.length === 9) {
    // Celular sem DDD: 9 1234-5678
    return `${digits.slice(0, 1)} ${digits.slice(1, 5)}-${digits.slice(5)}`;
  } else if (digits.length === 10) {
    // Telefone fixo com DDD: (12) 3456-7890
    return `(${digits.slice(0, 2)}) ${digits.slice(2, 6)}-${digits.slice(6)}`;
  } else if (digits.length === 11) {
    // Celular com DDD: (12) 9 3456-7890
    return `(${digits.slice(0, 2)}) ${digits.slice(2, 3)} ${digits.slice(3, 7)}-${digits.slice(7)}`;
  } else if (digits.length > 11) {
    // Número internacional: +55 (12) 9 3456-7890
    return `+${digits.slice(0, digits.length - 11)} (${digits.slice(digits.length - 11, digits.length - 9)}) ${digits.slice(digits.length - 9, digits.length - 8)} ${digits.slice(digits.length - 8, digits.length - 4)}-${digits.slice(digits.length - 4)}`;
  }
  
  // Fallback para outros formatos
  return phone;
}

/**
 * Formata um CPF
 * @param cpf Número do CPF (apenas dígitos)
 * @returns CPF formatado
 */
export function formatCPF(cpf: string | null | undefined): string {
  if (!cpf) return '';
  
  // Remover caracteres não numéricos
  const digits = cpf.replace(/\D/g, '');
  
  // Verificar se é um CPF válido
  if (digits.length !== 11) return cpf;
  
  // Formatar: 123.456.789-09
  return `${digits.slice(0, 3)}.${digits.slice(3, 6)}.${digits.slice(6, 9)}-${digits.slice(9)}`;
}

/**
 * Formata um CNPJ
 * @param cnpj Número do CNPJ (apenas dígitos)
 * @returns CNPJ formatado
 */
export function formatCNPJ(cnpj: string | null | undefined): string {
  if (!cnpj) return '';
  
  // Remover caracteres não numéricos
  const digits = cnpj.replace(/\D/g, '');
  
  // Verificar se é um CNPJ válido
  if (digits.length !== 14) return cnpj;
  
  // Formatar: 12.345.678/0001-90
  return `${digits.slice(0, 2)}.${digits.slice(2, 5)}.${digits.slice(5, 8)}/${digits.slice(8, 12)}-${digits.slice(12)}`;
}

/**
 * Formata um documento (CPF ou CNPJ)
 * @param doc Número do documento (apenas dígitos)
 * @returns Documento formatado
 */
export function formatDocument(doc: string | null | undefined): string {
  if (!doc) return '';
  
  // Remover caracteres não numéricos
  const digits = doc.replace(/\D/g, '');
  
  // Verificar se é CPF ou CNPJ pelo número de dígitos
  if (digits.length === 11) {
    return formatCPF(digits);
  } else if (digits.length === 14) {
    return formatCNPJ(digits);
  }
  
  // Retornar o valor original se não for um formato reconhecido
  return doc;
}

/**
 * Formata um status para exibição
 * @param status Código do status
 * @returns Status formatado para exibição
 */
export function formatStatus(status: string | null | undefined): string {
  if (!status) return 'Desconhecido';
  
  // Mapeamento de status
  const statusMap: Record<string, string> = {
    'novo': 'Novo',
    'em_analise': 'Em Análise',
    'em_processamento': 'Em Processamento',
    'aguardando_cliente': 'Aguardando Cliente',
    'concluido': 'Concluído',
    'cancelado': 'Cancelado',
    'pendente': 'Pendente',
    'atrasado': 'Atrasado',
    'em_revisao': 'Em Revisão',
    'aprovado': 'Aprovado',
    'rejeitado': 'Rejeitado',
    'arquivado': 'Arquivado',
    'ativo': 'Ativo',
    'inativo': 'Inativo',
    'excluido': 'Excluído',
    'em_andamento': 'Em Andamento',
    'finalizado': 'Finalizado',
    'aguardando_pagamento': 'Aguardando Pagamento',
    'pago': 'Pago',
    'vencido': 'Vencido'
  };
  
  // Retornar o status formatado ou o próprio status se não estiver no mapeamento
  return statusMap[status] || status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
}

/**
 * Trunca um texto para o tamanho máximo especificado
 * @param text Texto a ser truncado
 * @param maxLength Tamanho máximo
 * @param suffix Sufixo a ser adicionado quando truncado (padrão: "...")
 * @returns Texto truncado
 */
export function truncateText(text: string | null | undefined, maxLength: number, suffix: string = '...'): string {
  if (!text) return '';
  
  if (text.length <= maxLength) {
    return text;
  }
  
  return text.substring(0, maxLength) + suffix;
}

/**
 * Valida se um CPF é válido
 * @param cpf Número do CPF
 * @returns true se o CPF for válido
 */
export function isValidCPF(cpf: string | null | undefined): boolean {
  if (!cpf) return false;
  
  // Remover caracteres não numéricos
  const digits = cpf.replace(/\D/g, '');
  
  // Verificar se tem 11 dígitos
  if (digits.length !== 11) return false;
  
  // Verificar se todos os dígitos são iguais
  if (/^(\d)\1+$/.test(digits)) return false;
  
  // Validar dígitos verificadores
  let sum = 0;
  let remainder;
  
  // Primeiro dígito verificador
  for (let i = 1; i <= 9; i++) {
    sum += parseInt(digits.substring(i - 1, i)) * (11 - i);
  }
  
  remainder = (sum * 10) % 11;
  if (remainder === 10 || remainder === 11) remainder = 0;
  if (remainder !== parseInt(digits.substring(9, 10))) return false;
  
  // Segundo dígito verificador
  sum = 0;
  for (let i = 1; i <= 10; i++) {
    sum += parseInt(digits.substring(i - 1, i)) * (12 - i);
  }
  
  remainder = (sum * 10) % 11;
  if (remainder === 10 || remainder === 11) remainder = 0;
  if (remainder !== parseInt(digits.substring(10, 11))) return false;
  
  return true;
}

/**
 * Valida se um CNPJ é válido
 * @param cnpj Número do CNPJ
 * @returns true se o CNPJ for válido
 */
export function isValidCNPJ(cnpj: string | null | undefined): boolean {
  if (!cnpj) return false;
  
  // Remover caracteres não numéricos
  const digits = cnpj.replace(/\D/g, '');
  
  // Verificar se tem 14 dígitos
  if (digits.length !== 14) return false;
  
  // Verificar se todos os dígitos são iguais
  if (/^(\d)\1+$/.test(digits)) return false;
  
  // Validar dígitos verificadores
  let size = digits.length - 2;
  let numbers = digits.substring(0, size);
  const digits_verification = digits.substring(size);
  let sum = 0;
  let pos = size - 7;
  
  // Primeiro dígito verificador
  for (let i = size; i >= 1; i--) {
    sum += parseInt(numbers.charAt(size - i)) * pos--;
    if (pos < 2) pos = 9;
  }
  
  let result = sum % 11 < 2 ? 0 : 11 - (sum % 11);
  if (result !== parseInt(digits_verification.charAt(0))) return false;
  
  // Segundo dígito verificador
  size += 1;
  numbers = digits.substring(0, size);
  sum = 0;
  pos = size - 7;
  
  for (let i = size; i >= 1; i--) {
    sum += parseInt(numbers.charAt(size - i)) * pos--;
    if (pos < 2) pos = 9;
  }
  
  result = sum % 11 < 2 ? 0 : 11 - (sum % 11);
  if (result !== parseInt(digits_verification.charAt(1))) return false;
  
  return true;
}

/**
 * Valida se um email é válido
 * @param email Endereço de email
 * @returns true se o email for válido
 */
export function isValidEmail(email: string | null | undefined): boolean {
  if (!email) return false;
  
  // Expressão regular para validação básica de email
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Valida se um número de telefone é válido
 * @param phone Número de telefone
 * @returns true se o telefone for válido
 */
export function isValidPhone(phone: string | null | undefined): boolean {
  if (!phone) return false;
  
  // Remover caracteres não numéricos
  const digits = phone.replace(/\D/g, '');
  
  // Verificar se tem pelo menos 8 dígitos (telefone sem DDD)
  if (digits.length < 8) return false;
  
  // Verificar se tem no máximo 15 dígitos (padrão internacional E.164)
  if (digits.length > 15) return false;
  
  return true;
}

/**
 * Formata um número de processo judicial
 * @param numero Número do processo
 * @returns Número formatado
 */
export function formatNumeroProcesso(numero: string | null | undefined): string {
  if (!numero) return '';
  
  // Remover caracteres não numéricos
  const digits = numero.replace(/\D/g, '');
  
  // Verificar se tem o número correto de dígitos (20)
  if (digits.length !== 20) return numero;
  
  // Formatar: 1234567-12.2020.8.26.0100
  return `${digits.slice(0, 7)}-${digits.slice(7, 9)}.${digits.slice(9, 13)}.${digits.slice(13, 14)}.${digits.slice(14, 16)}.${digits.slice(16)}`;
}

/**
 * Formata um valor para exibição em gráficos (formato compacto)
 * @param value Valor a ser formatado
 * @param decimalPlaces Número de casas decimais (padrão: 1)
 * @returns Valor formatado para exibição em gráficos
 */
export function formatCompactNumber(value: number | string | null | undefined, decimalPlaces: number = 1): string {
  return formatNumber(value, { compact: true, decimalPlaces });
}

/**
 * Formata um CEP
 * @param cep Número do CEP (apenas dígitos)
 * @returns CEP formatado
 */
export function formatCEP(cep: string | null | undefined): string {
  if (!cep) return '';
  
  // Remover caracteres não numéricos
  const digits = cep.replace(/\D/g, '');
  
  // Verificar se tem 8 dígitos
  if (digits.length !== 8) return cep;
  
  // Formatar: 12345-678
  return `${digits.slice(0, 5)}-${digits.slice(5)}`;
}

/**
 * Formata um valor monetário para número
 * @param value Valor monetário (ex: "R$ 1.234,56")
 * @returns Número (ex: 1234.56)
 */
export function parseCurrency(value: string | null | undefined): number {
  if (!value) return 0;
  
  // Remover símbolo da moeda e pontos de milhar, substituir vírgula por ponto
  const normalized = value
    .replace(/[^\d,.-]/g, '')  // Remove tudo exceto dígitos, vírgula, ponto e sinal
    .replace(/\./g, '')        // Remove pontos (separadores de milhar)
    .replace(/,/g, '.');       // Substitui vírgula por ponto (separador decimal)
  
  return parseFloat(normalized) || 0;
}

/**
 * Converte uma string de data brasileira (DD/MM/YYYY) para objeto Date
 * @param dateStr String de data no formato brasileiro
 * @returns Objeto Date ou null se inválido
 */
export function parseDate(dateStr: string | null | undefined): Date | null {
  if (!dateStr) return null;
  
  // Tentar converter diretamente se for formato ISO
  if (/^\d{4}-\d{2}-\d{2}/.test(dateStr)) {
    const date = new Date(dateStr);
    return isNaN(date.getTime()) ? null : date;
  }
  
  // Converter formato brasileiro (DD/MM/YYYY)
  const parts = dateStr.split('/');
  if (parts.length !== 3) return null;
  
  const day = parseInt(parts[0], 10);
  const month = parseInt(parts[1], 10) - 1; // Mês em JS começa em 0
  const year = parseInt(parts[2], 10);
  
  const date = new Date(year, month, day);
  
  // Verificar se a data é válida
  if (
    isNaN(date.getTime()) ||
    date.getDate() !== day ||
    date.getMonth() !== month ||
    date.getFullYear() !== year
  ) {
    return null;
  }
  
  return date;
}

/**
 * Calcula a idade a partir de uma data de nascimento
 * @param birthDate Data de nascimento
 * @returns Idade em anos
 */
export function calculateAge(birthDate: Date | string | null | undefined): number {
  if (!birthDate) return 0;
  
  let birthDateObj: Date;
  
  if (typeof birthDate === 'string') {
    const parsedDate = parseDate(birthDate);
    if (!parsedDate) return 0;
    birthDateObj = parsedDate;
  } else if (birthDate instanceof Date) {
    birthDateObj = birthDate;
  } else {
    return 0;
  }
  
  const today = new Date();
  let age = today.getFullYear() - birthDateObj.getFullYear();
  const monthDiff = today.getMonth() - birthDateObj.getMonth();
  
  // Ajustar idade se ainda não fez aniversário este ano
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDateObj.getDate())) {
    age--;
  }
  
  return age;
}

/**
 * Formata um valor para o formato de moeda para entrada em formulários
 * @param value Valor a ser formatado
 * @returns String formatada para entrada (ex: 1.234,56)
 */
export function formatCurrencyInput(value: string | number | null | undefined): string {
  if (value === null || value === undefined) return '';
  
  // Converter para string e garantir que seja um número válido
  let stringValue = String(value).replace(/[^\d]/g, '');
  
  // Adicionar zeros à esquerda se necessário
  while (stringValue.length < 3) {
    stringValue = '0' + stringValue;
  }
  
  // Separar reais e centavos
  const reais = stringValue.slice(0, -2);
  const centavos = stringValue.slice(-2);
  
  // Formatar com pontos para milhar
  let formattedReais = '';
  for (let i = 0; i < reais.length; i++) {
    if (i > 0 && (reais.length - i) % 3 === 0) {
      formattedReais += '.';
    }
    formattedReais += reais[i];
  }
  
  // Remover zeros à esquerda, exceto se for zero
  formattedReais = formattedReais.replace(/^0+(?=\d)/, '');
  if (formattedReais === '') formattedReais = '0';
  
  return `${formattedReais},${centavos}`;
}

/**
 * Formata bytes para uma representação legível (KB, MB, GB, etc.)
 * @param bytes Número de bytes
 * @param decimals Número de casas decimais
 * @returns String formatada
 */
export function formatBytes(bytes: number | null | undefined, decimals: number = 2): string {
  if (bytes === null || bytes === undefined || bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(decimals)) + ' ' + sizes[i];
}

/**
 * Formata um número de capítulo/versículo
 * @param numero Número do capítulo/versículo
 * @returns Número formatado em romano
 */
export function formatNumeroRomano(numero: number): string {
  if (numero <= 0) return '';
  
  const valores = [1000, 900, 500, 400, 100, 90, 50, 40, 10, 9, 5, 4, 1];
  const romanos = ['M', 'CM', 'D', 'CD', 'C', 'XC', 'L', 'XL', 'X', 'IX', 'V', 'IV', 'I'];
  
  let resultado = '';
  
  for (let i = 0; i < valores.length; i++) {
    while (numero >= valores[i]) {
      resultado += romanos[i];
      numero -= valores[i];
    }
  }
  
  return resultado;
}

/**
 * Formata uma cor para exibição
 * @param cor Código da cor (nome, hex, rgb)
 * @returns Cor formatada para exibição
 */
export function formatCor(cor: string | null | undefined): string {
  if (!cor) return '';
  
  // Mapeamento de nomes de cores em português para inglês
  const coresMap: Record<string, string> = {
    'vermelho': 'red',
    'azul': 'blue',
    'verde': 'green',
    'amarelo': 'yellow',
    'preto': 'black',
    'branco': 'white',
    'cinza': 'gray',
    'roxo': 'purple',
    'laranja': 'orange',
    'rosa': 'pink',
    'marrom': 'brown',
    'violeta': 'violet',
    'ciano': 'cyan',
    'magenta': 'magenta',
    'turquesa': 'turquoise',
    'prata': 'silver',
    'dourado': 'gold',
    'bege': 'beige'
  };
  
  // Verificar se é um nome de cor em português
  if (coresMap[cor.toLowerCase()]) {
    return coresMap[cor.toLowerCase()];
  }
  
  // Se for um código hexadecimal sem #, adicionar #
  if (/^[0-9A-Fa-f]{6}$/.test(cor)) {
    return `#${cor}`;
  }
  
  // Retornar a cor como está
  return cor;
}
