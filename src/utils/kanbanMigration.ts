import { supabase } from '@/lib/supabase';

/**
 * Script de migração para corrigir problemas no Kanban de Precatórios
 * 
 * Este script:
 * 1. Sincroniza status_precatorios com kanban_colunas_personalizadas
 * 2. Corrige precatórios com status_id inválido
 * 3. Remove duplicatas e inconsistências
 * 4. Garante que todos os status sejam colunas kanban
 */

export interface MigrationResult {
  success: boolean;
  message: string;
  details: {
    statusCriados: number;
    statusAtualizados: number;
    precatoriosCorrigidos: number;
    colunasRemovidas: number;
  };
}

export async function executarMigracaoKanban(): Promise<MigrationResult> {
  const result: MigrationResult = {
    success: false,
    message: '',
    details: {
      statusCriados: 0,
      statusAtualizados: 0,
      precatoriosCorrigidos: 0,
      colunasRemovidas: 0
    }
  };

  try {
    console.log('[KanbanMigration] Iniciando migração do Kanban...');

    // Passo 1: Verificar e criar status padrão se necessário
    await criarStatusPadraoSeNecessario(result);

    // Passo 2: Marcar todos os status como colunas kanban
    await marcarStatusComoColunasKanban(result);

    // Passo 3: Corrigir precatórios com status_id inválido
    await corrigirPrecatoriosComStatusInvalido(result);

    // Passo 4: Limpar colunas personalizadas duplicadas/desnecessárias
    await limparColunasPersonalizadas(result);

    result.success = true;
    result.message = 'Migração concluída com sucesso!';
    
    console.log('[KanbanMigration] Migração concluída:', result);
    return result;

  } catch (error) {
    console.error('[KanbanMigration] Erro na migração:', error);
    result.success = false;
    result.message = `Erro na migração: ${error instanceof Error ? error.message : 'Erro desconhecido'}`;
    return result;
  }
}

async function criarStatusPadraoSeNecessario(result: MigrationResult): Promise<void> {
  console.log('[KanbanMigration] Verificando status padrão...');

  // Verificar se existem status
  const { data: statusExistentes, error } = await supabase
    .from('status_precatorios')
    .select('id, codigo')
    .eq('ativo', true);

  if (error) {
    throw new Error(`Erro ao verificar status existentes: ${error.message}`);
  }

  // Se não há status ou há muito poucos, criar os padrão
  if (!statusExistentes || statusExistentes.length < 3) {
    console.log('[KanbanMigration] Criando status padrão...');

    const statusPadrao = [
      { nome: 'Novo', codigo: 'novo', cor: '#3b82f6', ordem: 1, is_default: true },
      { nome: 'Em Análise', codigo: 'analise', cor: '#8b5cf6', ordem: 2 },
      { nome: 'Em Andamento', codigo: 'em_andamento', cor: '#10b981', ordem: 3 },
      { nome: 'Aguardando', codigo: 'aguardando', cor: '#f59e0b', ordem: 4 },
      { nome: 'Concluído', codigo: 'concluido', cor: '#22c55e', ordem: 5 }
    ];

    const { data: userData } = await supabase.auth.getUser();
    const userId = userData?.user?.id;

    for (const status of statusPadrao) {
      // Verificar se já existe
      const existe = statusExistentes?.find(s => s.codigo === status.codigo);
      if (existe) continue;

      const { error: insertError } = await supabase
        .from('status_precatorios')
        .insert({
          nome: status.nome,
          codigo: status.codigo,
          cor: status.cor,
          ordem: status.ordem,
          is_default: status.is_default || false,
          is_system: true,
          ativo: true,
          visivel: true,
          kanban_coluna: true,
          created_by: userId,
          created_at: new Date().toISOString()
        });

      if (insertError) {
        console.error(`[KanbanMigration] Erro ao criar status "${status.nome}":`, insertError);
      } else {
        result.details.statusCriados++;
        console.log(`[KanbanMigration] Status "${status.nome}" criado`);
      }
    }
  }
}

async function marcarStatusComoColunasKanban(result: MigrationResult): Promise<void> {
  console.log('[KanbanMigration] Marcando status como colunas kanban...');

  // Buscar todos os status ativos que não são colunas kanban
  const { data: statusParaAtualizar, error } = await supabase
    .from('status_precatorios')
    .select('id, nome')
    .eq('ativo', true)
    .or('kanban_coluna.is.null,kanban_coluna.eq.false');

  if (error) {
    throw new Error(`Erro ao buscar status para atualizar: ${error.message}`);
  }

  if (statusParaAtualizar && statusParaAtualizar.length > 0) {
    for (const status of statusParaAtualizar) {
      const { error: updateError } = await supabase
        .from('status_precatorios')
        .update({
          kanban_coluna: true,
          visivel: true,
          updated_at: new Date().toISOString()
        })
        .eq('id', status.id);

      if (updateError) {
        console.error(`[KanbanMigration] Erro ao atualizar status "${status.nome}":`, updateError);
      } else {
        result.details.statusAtualizados++;
        console.log(`[KanbanMigration] Status "${status.nome}" marcado como coluna kanban`);
      }
    }
  }
}

async function corrigirPrecatoriosComStatusInvalido(result: MigrationResult): Promise<void> {
  console.log('[KanbanMigration] Corrigindo precatórios com status inválido...');

  // Buscar todos os status válidos
  const { data: statusValidos, error: statusError } = await supabase
    .from('status_precatorios')
    .select('id, codigo, nome, is_default')
    .eq('ativo', true);

  if (statusError || !statusValidos) {
    throw new Error(`Erro ao buscar status válidos: ${statusError?.message}`);
  }

  const statusMap = new Map(statusValidos.map(s => [s.codigo, s.id]));
  const statusPadrao = statusValidos.find(s => s.is_default) || statusValidos[0];

  // Buscar precatórios que podem ter problemas
  const { data: precatorios, error: precatoriosError } = await supabase
    .from('precatorios')
    .select('id, status, status_id')
    .or('is_deleted.is.null,is_deleted.eq.false');

  if (precatoriosError || !precatorios) {
    throw new Error(`Erro ao buscar precatórios: ${precatoriosError?.message}`);
  }

  for (const precatorio of precatorios) {
    let novoStatusId = null;
    let novoStatus = null;

    // Verificar se o status_id é válido
    const statusIdValido = statusValidos.find(s => s.id === precatorio.status_id);

    if (!statusIdValido) {
      // Status_id inválido, tentar mapear pelo código
      if (precatorio.status && statusMap.has(precatorio.status)) {
        novoStatusId = statusMap.get(precatorio.status);
        novoStatus = precatorio.status;
      } else {
        // Usar status padrão
        novoStatusId = statusPadrao.id;
        novoStatus = statusPadrao.codigo;
      }

      // Atualizar no banco
      const { error: updateError } = await supabase
        .from('precatorios')
        .update({
          status_id: novoStatusId,
          status: novoStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', precatorio.id);

      if (updateError) {
        console.error(`[KanbanMigration] Erro ao corrigir precatório ${precatorio.id}:`, updateError);
      } else {
        result.details.precatoriosCorrigidos++;
      }
    }
  }

  console.log(`[KanbanMigration] Corrigidos ${result.details.precatoriosCorrigidos} precatórios`);
}

async function limparColunasPersonalizadas(result: MigrationResult): Promise<void> {
  console.log('[KanbanMigration] Limpando colunas personalizadas desnecessárias...');

  // Como agora usamos apenas status_precatorios como colunas,
  // podemos marcar as colunas personalizadas como deletadas
  const { data: colunasPersonalizadas, error } = await supabase
    .from('kanban_colunas_personalizadas')
    .select('id, nome')
    .or('is_deleted.is.null,is_deleted.eq.false');

  if (error) {
    console.warn('[KanbanMigration] Erro ao buscar colunas personalizadas:', error);
    return;
  }

  if (colunasPersonalizadas && colunasPersonalizadas.length > 0) {
    for (const coluna of colunasPersonalizadas) {
      const { error: deleteError } = await supabase
        .from('kanban_colunas_personalizadas')
        .update({
          is_deleted: true,
          deleted_at: new Date().toISOString()
        })
        .eq('id', coluna.id);

      if (deleteError) {
        console.error(`[KanbanMigration] Erro ao marcar coluna "${coluna.nome}" como deletada:`, deleteError);
      } else {
        result.details.colunasRemovidas++;
      }
    }

    console.log(`[KanbanMigration] Marcadas ${result.details.colunasRemovidas} colunas personalizadas como deletadas`);
  }
}

// Função para verificar se a migração é necessária
export async function verificarNecessidadeMigracao(): Promise<boolean> {
  try {
    // Verificar se há precatórios com status_id inválido
    const { data: statusValidos } = await supabase
      .from('status_precatorios')
      .select('id')
      .eq('ativo', true);

    if (!statusValidos || statusValidos.length === 0) {
      return true; // Precisa criar status
    }

    const statusIds = statusValidos.map(s => s.id);

    const { data: precatoriosProblematicos } = await supabase
      .from('precatorios')
      .select('id')
      .not('status_id', 'in', `(${statusIds.join(',')})`)
      .or('is_deleted.is.null,is_deleted.eq.false')
      .limit(1);

    return (precatoriosProblematicos && precatoriosProblematicos.length > 0);
  } catch (error) {
    console.error('[KanbanMigration] Erro ao verificar necessidade de migração:', error);
    return true; // Em caso de erro, assumir que precisa migrar
  }
}
