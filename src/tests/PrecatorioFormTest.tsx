import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { NovoPrecatorioForm } from '@/components/Precatorios/NovoPrecatorioForm';
import { toast } from 'sonner';

export function PrecatorioFormTest() {
  const [isOpen, setIsOpen] = useState(false);
  
  const handleSave = (precatorio: any) => {
    console.log('Precatório salvo:', precatorio);
    toast.success('Precatório salvo com sucesso!');
  };
  
  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Teste do Formulário de Precatório</h1>
      <p className="mb-4 text-muted-foreground">
        Esta página permite testar o formulário de criação de precatórios com as novas validações e melhorias de UI.
      </p>
      
      <Button onClick={() => setIsOpen(true)}>A<PERSON>r Formulário</Button>
      
      <NovoPrecatorioForm 
        isOpen={isOpen}
        onOpenChange={setIsOpen}
        onSave={handleSave}
      />
    </div>
  );
}
