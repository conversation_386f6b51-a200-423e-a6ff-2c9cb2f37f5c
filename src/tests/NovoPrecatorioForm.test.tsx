import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { NovoPrecatorioForm } from '@/components/Precatorios/NovoPrecatorioForm';
import { vi } from 'vitest';

// Mock do supabase
vi.mock('@/lib/supabase', () => ({
  supabase: {
    from: () => ({
      insert: () => ({
        select: () => ({
          data: [{ id: '123' }],
          error: null
        })
      }),
      select: () => ({
        data: [],
        error: null
      })
    })
  }
}));

// Mock do toast
vi.mock('sonner', () => ({
  toast: {
    error: vi.fn(),
    success: vi.fn()
  }
}));

describe('NovoPrecatorioForm', () => {
  const mockOnSave = vi.fn();
  const mockOnOpenChange = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('deve mostrar erro quando campos obrigatórios não são preenchidos', async () => {
    render(
      <NovoPrecatorioForm 
        isOpen={true} 
        onOpenChange={mockOnOpenChange} 
        onSave={mockOnSave} 
      />
    );

    // Encontrar o botão de criar precatório
    const createButton = screen.getByText('Criar Precatório');
    
    // Clicar no botão sem preencher os campos
    fireEvent.click(createButton);
    
    // Verificar se os erros são exibidos
    await waitFor(() => {
      expect(screen.getByText('Número do precatório é obrigatório')).toBeInTheDocument();
      expect(screen.getByText('Tribunal é obrigatório')).toBeInTheDocument();
      expect(screen.getByText('Valor total é obrigatório')).toBeInTheDocument();
    });
    
    // Verificar se a função onSave não foi chamada
    expect(mockOnSave).not.toHaveBeenCalled();
  });

  test('deve mostrar erro quando valor total não é um número válido', async () => {
    render(
      <NovoPrecatorioForm 
        isOpen={true} 
        onOpenChange={mockOnOpenChange} 
        onSave={mockOnSave} 
      />
    );

    // Preencher o campo de valor total com um valor inválido
    const valorTotalInput = screen.getByPlaceholderText('Valor total');
    fireEvent.change(valorTotalInput, { target: { value: '-100' } });
    
    // Encontrar o botão de criar precatório
    const createButton = screen.getByText('Criar Precatório');
    
    // Clicar no botão
    fireEvent.click(createButton);
    
    // Verificar se o erro é exibido
    await waitFor(() => {
      expect(screen.getByText('Valor total deve ser um número positivo')).toBeInTheDocument();
    });
    
    // Verificar se a função onSave não foi chamada
    expect(mockOnSave).not.toHaveBeenCalled();
  });

  test('deve mostrar erro quando desconto não é um percentual válido', async () => {
    render(
      <NovoPrecatorioForm 
        isOpen={true} 
        onOpenChange={mockOnOpenChange} 
        onSave={mockOnSave} 
      />
    );

    // Preencher o campo de desconto com um valor inválido
    const descontoInput = screen.getByPlaceholderText('Percentual de desconto');
    fireEvent.change(descontoInput, { target: { value: '101' } });
    
    // Encontrar o botão de criar precatório
    const createButton = screen.getByText('Criar Precatório');
    
    // Clicar no botão
    fireEvent.click(createButton);
    
    // Verificar se o erro é exibido
    await waitFor(() => {
      expect(screen.getByText('Desconto deve ser um percentual entre 0 e 100')).toBeInTheDocument();
    });
    
    // Verificar se a função onSave não foi chamada
    expect(mockOnSave).not.toHaveBeenCalled();
  });

  test('deve limpar o erro quando o campo é editado', async () => {
    render(
      <NovoPrecatorioForm 
        isOpen={true} 
        onOpenChange={mockOnOpenChange} 
        onSave={mockOnSave} 
      />
    );

    // Encontrar o botão de criar precatório
    const createButton = screen.getByText('Criar Precatório');
    
    // Clicar no botão sem preencher os campos
    fireEvent.click(createButton);
    
    // Verificar se o erro é exibido
    await waitFor(() => {
      expect(screen.getByText('Número do precatório é obrigatório')).toBeInTheDocument();
    });
    
    // Preencher o campo de número do precatório
    const numeroInput = screen.getByPlaceholderText('Número do precatório');
    fireEvent.change(numeroInput, { target: { value: '123456' } });
    
    // Verificar se o erro foi limpo
    await waitFor(() => {
      expect(screen.queryByText('Número do precatório é obrigatório')).not.toBeInTheDocument();
    });
  });
});
