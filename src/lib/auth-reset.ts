import { supabase } from './supabase';

/**
 * Função para limpar completamente os dados de autenticação
 * e forçar um novo login
 */
export async function resetAuthentication() {
  console.log('Iniciando reset completo de autenticação...');

  try {
    // 1. Preservar o perfil do usuário no localStorage
    const userProfileStr = localStorage.getItem('userProfile');
    let userProfile = null;
    if (userProfileStr) {
      try {
        userProfile = JSON.parse(userProfileStr);
        console.log('Perfil do usuário preservado para restauração posterior');
      } catch (e) {
        console.error('Erro ao analisar perfil do usuário:', e);
      }
    }

    // Limpar localStorage relacionado ao Supabase
    const keysToRemove = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (
        key.startsWith('sb-') ||
        key.includes('supabase') ||
        key.includes('auth')
      )) {
        keysToRemove.push(key);
      }
    }

    keysToRemove.forEach(key => {
      console.log(`Removendo chave do localStorage: ${key}`);
      localStorage.removeItem(key);
    });

    // Restaurar o perfil do usuário
    if (userProfile) {
      localStorage.setItem('userProfile', JSON.stringify(userProfile));
      console.log('Perfil do usuário restaurado no localStorage');
    }

    // 2. Limpar sessionStorage
    const sessionKeysToRemove = [];
    for (let i = 0; i < sessionStorage.length; i++) {
      const key = sessionStorage.key(i);
      if (key && (
        key.startsWith('sb-') ||
        key.includes('supabase') ||
        key.includes('auth') ||
        key.includes('user') ||
        key.includes('profile')
      )) {
        sessionKeysToRemove.push(key);
      }
    }

    sessionKeysToRemove.forEach(key => {
      console.log(`Removendo chave do sessionStorage: ${key}`);
      sessionStorage.removeItem(key);
    });

    // 3. Limpar cookies
    document.cookie.split(';').forEach(cookie => {
      const [name] = cookie.trim().split('=');
      if (name && (
        name.startsWith('sb-') ||
        name.includes('supabase') ||
        name.includes('auth')
      )) {
        console.log(`Removendo cookie: ${name}`);
        document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
      }
    });

    // 4. Forçar logout no Supabase
    try {
      await supabase.auth.signOut({ scope: 'global' });
      console.log('Logout forçado realizado com sucesso');
    } catch (e) {
      console.error('Erro ao realizar logout forçado:', e);
    }

    console.log('Reset de autenticação concluído com sucesso');
    return true;
  } catch (error) {
    console.error('Erro durante o reset de autenticação:', error);
    return false;
  }
}

/**
 * Função para verificar se há problemas de autenticação
 * e tentar corrigir automaticamente
 */
export async function checkAndFixAuthIssues() {
  try {
    console.log('Verificando problemas de autenticação...');

    // Verificar se há uma sessão válida
    const { data, error } = await supabase.auth.getSession();

    if (error) {
      console.warn('Erro ao verificar sessão:', error);
      return { fixed: false, error };
    }

    if (!data.session) {
      console.warn('Nenhuma sessão encontrada');

      // Verificar se há tokens no localStorage
      const tokenStr = localStorage.getItem('sb-ubwzukpsqcrwzfbppoux-auth-token');
      if (!tokenStr) {
        console.warn('Nenhum token encontrado no localStorage');
        return { fixed: false, needsLogin: true };
      }

      try {
        const tokenData = JSON.parse(tokenStr);
        if (!tokenData || !tokenData.access_token) {
          console.warn('Token inválido no localStorage');
          return { fixed: false, needsLogin: true };
        }

        // Tentar definir a sessão com o token do localStorage
        const { data: setSessionData, error: setSessionError } =
          await supabase.auth.setSession({
            access_token: tokenData.access_token,
            refresh_token: tokenData.refresh_token || '',
          });

        if (setSessionError) {
          console.error('Erro ao definir sessão com token do localStorage:', setSessionError);
          return { fixed: false, error: setSessionError, needsLogin: true };
        }

        if (setSessionData.session) {
          console.log('Sessão restaurada com sucesso usando token do localStorage');
          return { fixed: true, session: setSessionData.session };
        }
      } catch (e) {
        console.error('Erro ao processar token do localStorage:', e);
        return { fixed: false, error: e, needsLogin: true };
      }
    }

    console.log('Sessão válida encontrada, não é necessário corrigir');
    return { fixed: true, session: data.session };
  } catch (error) {
    console.error('Erro ao verificar problemas de autenticação:', error);
    return { fixed: false, error };
  }
}
