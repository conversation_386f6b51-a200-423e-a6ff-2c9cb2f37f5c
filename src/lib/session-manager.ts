import { supabase } from './supabase';
import { resetAuthentication } from './auth-reset';

// Configurações
const SESSION_CHECK_INTERVAL = 300000; // Verificar a sessão a cada 5 minutos (aumentado para reduzir verificações frequentes)
const SESSION_REFRESH_THRESHOLD = 5 * 60; // Renovar se faltar menos de 5 minutos para expirar
const INACTIVITY_TIMEOUT = 15 * 60 * 1000; // 15 minutos de inatividade
const MAX_SESSION_DURATION = 60 * 60 * 1000; // 1 hora de duração máxima da sessão
const NETWORK_RETRY_INTERVAL = 5000; // 5 segundos entre tentativas de reconexão
const MAX_NETWORK_RETRIES = 3; // Número máximo de tentativas de reconexão

// Estado interno
let lastActivityTime = Date.now();
let sessionStartTime = Date.now();
let sessionCheckTimer: number | null = null;
let networkRetryCount = 0;
let isRefreshing = false;
let isOnline = navigator.onLine;

/**
 * Inicializa o gerenciador de sessão
 */
export function initSessionManager() {
  console.log('[SessionManager] Inicializando gerenciador de sessão');

  // Limpar qualquer timer existente
  if (sessionCheckTimer) {
    clearInterval(sessionCheckTimer);
  }

  // Resetar tempos de sessão
  lastActivityTime = Date.now();
  sessionStartTime = Date.now();

  // Registrar eventos de atividade do usuário
  registerActivityEvents();

  // Registrar eventos de conectividade
  registerConnectivityEvents();

  // Iniciar verificação periódica da sessão
  sessionCheckTimer = window.setInterval(checkAndRefreshSession, SESSION_CHECK_INTERVAL);

  // Verificar a sessão imediatamente
  checkAndRefreshSession();

  return {
    stop: stopSessionManager,
    refresh: forceSessionRefresh,
    getLastActivity: () => new Date(lastActivityTime),
    getSessionDuration: () => Date.now() - sessionStartTime,
    isSessionActive: checkSessionActive
  };
}

/**
 * Para o gerenciador de sessão
 */
function stopSessionManager() {
  console.log('[SessionManager] Parando gerenciador de sessão');

  // Limpar timer
  if (sessionCheckTimer) {
    clearInterval(sessionCheckTimer);
    sessionCheckTimer = null;
  }

  // Remover eventos de atividade
  unregisterActivityEvents();

  // Remover eventos de conectividade
  unregisterConnectivityEvents();
}

/**
 * Registra eventos para detectar atividade do usuário
 */
function registerActivityEvents() {
  const updateActivity = () => {
    lastActivityTime = Date.now();
  };

  // Eventos de interação do usuário
  window.addEventListener('mousedown', updateActivity);
  window.addEventListener('keydown', updateActivity);
  window.addEventListener('touchstart', updateActivity);
  window.addEventListener('scroll', updateActivity, { passive: true });

  // Eventos de navegação
  window.addEventListener('popstate', updateActivity);
  window.addEventListener('hashchange', updateActivity);

  // Eventos de visibilidade
  document.addEventListener('visibilitychange', async () => {
    if (!document.hidden) {
      console.log('[SessionManager] Página voltou a ficar visível, atualizando atividade e verificando sessão');
      updateActivity();

      // Forçar atualização da sessão quando a página se torna visível, ignorando o intervalo mínimo
      try {
        console.log('[SessionManager] Forçando atualização da sessão após mudança de visibilidade');

        // Resetar a flag isRefreshing para garantir que podemos atualizar a sessão
        isRefreshing = false;
        lastSessionCheck = 0;

        // Verificar a sessão atual
        const { data } = await supabase.auth.getSession();

        if (data.session) {
          console.log('[SessionManager] Sessão encontrada, verificando validade');

          // Verificar se a sessão ainda é válida
          const expiryTime = new Date(data.session.expires_at * 1000);
          const now = new Date();
          const timeToExpiry = expiryTime.getTime() - now.getTime();

          console.log(`[SessionManager] Sessão expira em ${Math.floor(timeToExpiry / 60000)} minutos`);

          // Forçar refresh da sessão independentemente do tempo restante
          await refreshSession();
        } else {
          console.log('[SessionManager] Nenhuma sessão encontrada, tentando recuperar');
          await refreshSession();
        }

        // Disparar um evento personalizado para notificar componentes que precisam atualizar dados
        const event = new CustomEvent('app-visibility-change', {
          detail: {
            visible: true,
            timestamp: Date.now(),
            forceRefresh: true
          }
        });
        document.dispatchEvent(event);

        // Também disparar o evento de sessão atualizada para garantir que os componentes atualizem seus dados
        const sessionEvent = new CustomEvent('session-refreshed', {
          detail: {
            timestamp: Date.now()
          }
        });
        document.dispatchEvent(sessionEvent);
      } catch (error) {
        console.error('[SessionManager] Erro ao atualizar sessão após mudança de visibilidade:', error);

        // Mesmo em caso de erro, disparar o evento para que os componentes tentem atualizar seus dados
        const event = new CustomEvent('app-visibility-change', {
          detail: {
            visible: true,
            timestamp: Date.now(),
            forceRefresh: true,
            error: true
          }
        });
        document.dispatchEvent(event);
      } finally {
        // Garantir que a flag isRefreshing seja resetada
        isRefreshing = false;
      }
    } else {
      console.log('[SessionManager] Página ficou oculta');
      // Disparar evento quando a página fica oculta
      const event = new CustomEvent('app-visibility-change', {
        detail: {
          visible: false,
          timestamp: Date.now()
        }
      });
      document.dispatchEvent(event);
    }
  });
}

/**
 * Remove os eventos de atividade do usuário
 */
function unregisterActivityEvents() {
  const updateActivity = () => {
    lastActivityTime = Date.now();
  };

  window.removeEventListener('mousedown', updateActivity);
  window.removeEventListener('keydown', updateActivity);
  window.removeEventListener('touchstart', updateActivity);
  window.removeEventListener('scroll', updateActivity);
  window.removeEventListener('popstate', updateActivity);
  window.removeEventListener('hashchange', updateActivity);
  document.removeEventListener('visibilitychange', updateActivity);
}

/**
 * Registra eventos para detectar conectividade de rede
 */
function registerConnectivityEvents() {
  window.addEventListener('online', handleOnline);
  window.addEventListener('offline', handleOffline);
}

/**
 * Remove os eventos de conectividade de rede
 */
function unregisterConnectivityEvents() {
  window.removeEventListener('online', handleOnline);
  window.removeEventListener('offline', handleOffline);
}

/**
 * Manipula o evento de conexão online
 */
function handleOnline() {
  console.log('[SessionManager] Conexão de rede restaurada');
  isOnline = true;
  networkRetryCount = 0;

  // Verificar a sessão imediatamente quando a conexão é restaurada
  checkAndRefreshSession();
}

/**
 * Manipula o evento de desconexão
 */
function handleOffline() {
  console.log('[SessionManager] Conexão de rede perdida');
  isOnline = false;
}

/**
 * Verifica se a sessão está ativa com base na última atividade e duração total
 */
function checkSessionActive(): boolean {
  const now = Date.now();
  const inactiveTime = now - lastActivityTime;
  const sessionDuration = now - sessionStartTime;

  // Verificar se o usuário está inativo por muito tempo
  if (inactiveTime > INACTIVITY_TIMEOUT) {
    console.log(`[SessionManager] Usuário inativo por ${Math.floor(inactiveTime / 60000)} minutos`);
    return false;
  }

  // Verificar se a sessão já durou tempo demais
  if (sessionDuration > MAX_SESSION_DURATION) {
    console.log(`[SessionManager] Sessão ativa por ${Math.floor(sessionDuration / 60000)} minutos, excedendo o limite de ${Math.floor(MAX_SESSION_DURATION / 60000)} minutos`);
    return false;
  }

  return true;
}

// Variável para controlar a última verificação de sessão
let lastSessionCheck = 0;
const MIN_CHECK_INTERVAL = 180000; // 3 minutos entre verificações (aumentado para reduzir frequência)

/**
 * Verifica e atualiza a sessão se necessário
 */
async function checkAndRefreshSession() {
  // Evitar verificações simultâneas
  if (isRefreshing) {
    return;
  }

  // Evitar verificações muito frequentes
  const now = Date.now();
  if (now - lastSessionCheck < MIN_CHECK_INTERVAL) {
    console.log('[SessionManager] Verificação recente, pulando');
    return;
  }

  lastSessionCheck = now;

  try {
    isRefreshing = true;

    // Verificar se estamos online
    if (!isOnline) {
      console.log('[SessionManager] Offline, pulando verificação de sessão');
      return;
    }

    // Verificar se o usuário está ativo
    if (!checkSessionActive()) {
      console.log('[SessionManager] Sessão inativa, forçando logout');
      // Forçar logout quando a sessão estiver inativa
      await forceLogout();
      return;
    }

    console.log('[SessionManager] Verificando sessão');

    // Obter a sessão atual
    const { data, error } = await supabase.auth.getSession();

    if (error) {
      console.error('[SessionManager] Erro ao verificar sessão:', error);
      handleSessionError(error);
      return;
    }

    if (!data.session) {
      console.warn('[SessionManager] Nenhuma sessão encontrada');
      return;
    }

    // Verificar se a sessão está prestes a expirar
    const expiresAt = data.session.expires_at;
    const nowInSeconds = Math.floor(Date.now() / 1000); // Converter para segundos
    const timeToExpiry = expiresAt - nowInSeconds;

    console.log(`[SessionManager] Sessão expira em ${Math.floor(timeToExpiry / 60)} minutos`);

    // Renovar se estiver próximo de expirar
    if (timeToExpiry < SESSION_REFRESH_THRESHOLD) {
      console.log('[SessionManager] Renovando sessão');
      await refreshSession();
    }
  } catch (error) {
    console.error('[SessionManager] Erro inesperado:', error);
    handleSessionError(error);
  } finally {
    isRefreshing = false;
  }
}

/**
 * Força a atualização da sessão
 * @param {boolean} bypassTimeCheck - Se true, ignora a verificação de tempo desde a última atualização
 */
async function forceSessionRefresh(bypassTimeCheck = false) {
  if (isRefreshing) {
    console.log('[SessionManager] Já está atualizando a sessão');
    return;
  }

  // Verificar se houve uma atualização recente (a menos que bypassTimeCheck seja true)
  const now = Date.now();
  const timeSinceLastCheck = now - lastSessionCheck;

  if (!bypassTimeCheck && timeSinceLastCheck < MIN_CHECK_INTERVAL / 2) { // Permitir atualizações forçadas com metade do intervalo mínimo
    console.log(`[SessionManager] Atualização recente (${Math.floor(timeSinceLastCheck/1000)}s atrás), usando cache`);
    return;
  }

  try {
    isRefreshing = true;
    console.log('[SessionManager] Forçando atualização da sessão');
    lastSessionCheck = now; // Atualizar timestamp da última verificação
    await refreshSession();
  } catch (error) {
    console.error('[SessionManager] Erro ao forçar atualização da sessão:', error);
    handleSessionError(error);
  } finally {
    isRefreshing = false;
  }
}

/**
 * Atualiza a sessão
 */
async function refreshSession() {
  try {
    console.log('[SessionManager] Iniciando atualização da sessão');

    // Resetar a flag isRefreshing para garantir que podemos atualizar a sessão
    isRefreshing = false;

    // Tentar atualizar a sessão
    const { data, error } = await supabase.auth.refreshSession();

    if (error) {
      console.error('[SessionManager] Erro ao atualizar sessão:', error);

      // Tentar recuperar a sessão do localStorage como último recurso
      try {
        console.log('[SessionManager] Tentando recuperar sessão do localStorage após erro');
        const sessionStr = localStorage.getItem('sb-ubwzukpsqcrwzfbppoux-auth-token');
        if (sessionStr) {
          console.log('[SessionManager] Dados de sessão encontrados no localStorage');
          const sessionData = JSON.parse(sessionStr);

          if (sessionData && sessionData.access_token) {
            console.log('[SessionManager] Tentando definir sessão manualmente');

            // Tentar definir a sessão manualmente
            const { data: setData, error: setError } = await supabase.auth.setSession({
              access_token: sessionData.access_token,
              refresh_token: sessionData.refresh_token
            });

            if (setError) {
              console.error('[SessionManager] Erro ao definir sessão do localStorage:', setError);
              handleSessionError(error);
              return false;
            }

            if (setData.session) {
              console.log('[SessionManager] Sessão recuperada com sucesso do localStorage');

              // Atualizar timestamp de atividade
              lastActivityTime = Date.now();

              // Disparar evento de sessão atualizada
              const event = new CustomEvent('session-refreshed', {
                detail: {
                  timestamp: Date.now(),
                  expiresAt: setData.session.expires_at,
                  recovered: true
                }
              });
              document.dispatchEvent(event);

              return true;
            }
          }
        }
      } catch (e) {
        console.error('[SessionManager] Erro ao tentar recuperar sessão do localStorage:', e);
      }

      handleSessionError(error);
      return false;
    }

    if (data.session) {
      console.log('[SessionManager] Sessão atualizada com sucesso, expira em:',
        new Date(data.session.expires_at * 1000).toLocaleString());

      // Resetar contador de tentativas
      networkRetryCount = 0;

      // Atualizar timestamp de atividade
      lastActivityTime = Date.now();

      // Atualizar timestamp da última verificação
      lastSessionCheck = Date.now();

      // Disparar um evento personalizado para notificar a aplicação que a sessão foi atualizada
      const event = new CustomEvent('session-refreshed', {
        detail: {
          timestamp: Date.now(),
          expiresAt: data.session.expires_at
        }
      });
      document.dispatchEvent(event);

      return true;
    } else {
      console.warn('[SessionManager] Sessão não disponível após atualização');

      // Tentar recuperar a sessão do localStorage como último recurso
      try {
        const sessionStr = localStorage.getItem('sb-ubwzukpsqcrwzfbppoux-auth-token');
        if (sessionStr) {
          console.log('[SessionManager] Tentando recuperar sessão do localStorage');
          const sessionData = JSON.parse(sessionStr);

          if (sessionData && sessionData.access_token) {
            console.log('[SessionManager] Dados de sessão encontrados no localStorage');

            // Tentar definir a sessão manualmente
            const { data: setData, error: setError } = await supabase.auth.setSession({
              access_token: sessionData.access_token,
              refresh_token: sessionData.refresh_token
            });

            if (setError) {
              console.error('[SessionManager] Erro ao definir sessão do localStorage:', setError);
              return false;
            }

            if (setData.session) {
              console.log('[SessionManager] Sessão recuperada com sucesso do localStorage');

              // Disparar evento de sessão atualizada
              const event = new CustomEvent('session-refreshed', {
                detail: {
                  timestamp: Date.now(),
                  expiresAt: setData.session.expires_at,
                  recovered: true
                }
              });
              document.dispatchEvent(event);

              return true;
            }
          }
        }
      } catch (e) {
        console.error('[SessionManager] Erro ao tentar recuperar sessão do localStorage:', e);
      }

      // Mesmo sem sessão, disparar o evento para que os componentes tentem atualizar seus dados
      const event = new CustomEvent('session-refreshed', {
        detail: {
          timestamp: Date.now(),
          noSession: true
        }
      });
      document.dispatchEvent(event);

      return false;
    }
  } catch (error) {
    console.error('[SessionManager] Erro ao atualizar sessão:', error);
    handleSessionError(error);

    // Mesmo em caso de erro, disparar o evento para que os componentes tentem atualizar seus dados
    const event = new CustomEvent('session-refreshed', {
      detail: {
        timestamp: Date.now(),
        error: true
      }
    });
    document.dispatchEvent(event);

    return false;
  } finally {
    // Garantir que a flag isRefreshing seja resetada
    isRefreshing = false;
  }
}

/**
 * Manipula erros de sessão
 */
function handleSessionError(error: any) {
  // Verificar se é um erro de rede
  if (!isOnline || error.message?.includes('network') || error.message?.includes('fetch')) {
    handleNetworkError();
    return;
  }

  // Verificar se é um erro de sessão expirada
  if (error.message?.includes('expired') ||
      error.message?.includes('invalid') ||
      error.message?.includes('missing')) {

    console.warn('[SessionManager] Erro de sessão, tentando resetar autenticação');

    // Tentar resetar a autenticação
    resetAuthentication().then(() => {
      console.log('[SessionManager] Autenticação resetada após erro de sessão');
    }).catch(resetError => {
      console.error('[SessionManager] Erro ao resetar autenticação:', resetError);
    });
  }
}

/**
 * Manipula erros de rede
 */
function handleNetworkError() {
  networkRetryCount++;

  if (networkRetryCount <= MAX_NETWORK_RETRIES) {
    console.log(`[SessionManager] Erro de rede, tentativa ${networkRetryCount}/${MAX_NETWORK_RETRIES} em ${NETWORK_RETRY_INTERVAL/1000}s`);

    // Agendar nova tentativa
    setTimeout(() => {
      if (isOnline) {
        checkAndRefreshSession();
      }
    }, NETWORK_RETRY_INTERVAL);
  } else {
    console.warn(`[SessionManager] Máximo de tentativas de reconexão (${MAX_NETWORK_RETRIES}) atingido`);
    networkRetryCount = 0;
  }
}

/**
 * Pré-carrega dados comuns para melhorar a performance
 */
export async function preloadCommonData() {
  try {
    console.log('[SessionManager] Pré-carregando dados comuns');

    // Verificar se estamos online e autenticados
    if (!isOnline) {
      console.log('[SessionManager] Offline, pulando pré-carregamento');
      return;
    }

    const { data } = await supabase.auth.getSession();
    if (!data.session) {
      console.log('[SessionManager] Sem sessão, pulando pré-carregamento');
      return;
    }

    // Pré-carregar perfil do usuário
    const { data: profileData } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', data.session.user.id)
      .single();

    if (profileData) {
      console.log('[SessionManager] Perfil pré-carregado');
    }

    // Pré-carregar outros dados comuns conforme necessário
    // Por exemplo, configurações, dados de referência, etc.

  } catch (error) {
    console.error('[SessionManager] Erro ao pré-carregar dados:', error);
  }
}

/**
 * Força o logout do usuário
 * @param {boolean} forceReload - Se true, força o recarregamento da página em vez de redirecionar
 */
async function forceLogout(forceReload = false) {
  try {
    console.log('[SessionManager] Forçando logout do usuário');

    // Resetar tempos de sessão
    sessionStartTime = Date.now();

    // Limpar a sessão no Supabase
    await supabase.auth.signOut({ scope: 'local' });

    // Preservar o perfil do usuário no localStorage para referência
    // mas forçar o redirecionamento para a página de login

    // Disparar um evento personalizado para notificar a aplicação
    const event = new CustomEvent('session-expired', {
      detail: {
        reason: 'inactivity',
        timestamp: new Date().toISOString(),
        forceReload: forceReload
      }
    });
    document.dispatchEvent(event);

    if (forceReload) {
      // Recarregar a página para garantir um estado limpo
      console.log('[SessionManager] Recarregando a página após logout');
      window.location.reload();
    } else {
      // Redirecionar para a página de login
      window.location.href = '/login?expired=true';
    }

    return true;
  } catch (error) {
    console.error('[SessionManager] Erro ao forçar logout:', error);
    return false;
  }
}

// Exportar funções úteis
export const SessionManager = {
  init: initSessionManager,
  stop: stopSessionManager,
  refresh: forceSessionRefresh,
  logout: forceLogout,
  preloadData: preloadCommonData,
  isActive: checkSessionActive
};
