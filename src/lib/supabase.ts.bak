import { createClient } from '@supabase/supabase-js';

// Estas variáveis devem ser definidas no arquivo .env na raiz do projeto
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || '';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || '';

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('As variáveis de ambiente VITE_SUPABASE_URL e VITE_SUPABASE_ANON_KEY devem ser definidas.');
}

// Opções avançadas para melhorar persistência e desempenho
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,
    storageKey: 'sb-ubwzukpsqcrwzfbppoux-auth-token',
    autoRefreshToken: true,
    debug: true, // Ativar logs de debug para autenticação
  },
  // Melhorar desempenho e confiabilidade
  global: {
    fetch: (...args) => {
      return fetch(...args).catch((error) => {
        console.error('Supabase fetch error:', error);
        throw error;
      });
    },
  },
  // Melhorar manipulação de erros para consultas
  db: {
    schema: 'public',
  },
  realtime: {
    timeout: 30000, // Aumentar timeout para conexões realtime
  },
});

// Exportar um helper para forçar o refresh do token
export const refreshSupabaseSession = async () => {
  try {
    console.log('Tentando renovar sessão do Supabase...');
    
    // Primeiro, obter a sessão atual
    try {
      const { data: sessionData } = await supabase.auth.getSession();
      if (sessionData && sessionData.session) {
        console.log('Sessão atual encontrada, verificando validade');
        
        // Verificar se a sessão ainda é válida
        const expiryTime = new Date(sessionData.session.expires_at * 1000);
        const now = new Date();
        const timeToExpiry = expiryTime.getTime() - now.getTime();
        
        // Se a sessão for válida por mais de 5 minutos, não é necessário renovar
        if (timeToExpiry > 5 * 60 * 1000) {
          console.log('Sessão atual ainda é válida, expira em:', 
            Math.floor(timeToExpiry / 60000), 'minutos');
          return { 
            success: true, 
            session: sessionData.session,
            message: 'Sessão atual ainda é válida'
          };
        }
        
        console.log('Sessão atual expira em breve, renovando...');
      }
    } catch (sessionError) {
      console.warn('Erro ao verificar sessão atual, tentando renovar mesmo assim:', sessionError);
    }
    
    // Renovar a sessão
    try {
      const { data, error } = await supabase.auth.refreshSession();
      
      if (error) {
        // Verificar se é o erro específico de sessão ausente
        if (error.message && (
            error.message.includes('Auth session missing') || 
            error.message.includes('AuthSessionMissingError') ||
            error.message.includes('expired'))) {
          console.warn('Sessão não encontrada ou expirada, operações não autenticadas');
          
          // Este é um erro esperado, não é necessário bloquear operações por causa dele
          // Tentar obter informações do localStorage para informação
          const tokenStr = localStorage.getItem('sb-ubwzukpsqcrwzfbppoux-auth-token');
          if (tokenStr) {
            console.log('Token encontrado no localStorage, mas não é mais válido');
            
            try {
              // Tentar extrair o ID do usuário do token para log
              const tokenData = JSON.parse(tokenStr);
              if (tokenData && tokenData.user && tokenData.user.id) {
                console.log('ID do usuário do token antigo:', tokenData.user.id);
              }
            } catch (e) {
              console.error('Erro ao analisar token do localStorage:', e);
            }
          }
          
          return { 
            success: false, 
            error,
            recoverable: true, // Indicar que não é um erro fatal
            message: 'Sessão ausente ou expirada, operações não autenticadas permitidas'
          };
        }
        
        // Outros erros de renovação
        console.error('Erro ao renovar sessão:', error);
        
        // Tentar um último recurso - verificar o token no localStorage
        const tokenStr = localStorage.getItem('sb-ubwzukpsqcrwzfbppoux-auth-token');
        if (!tokenStr) {
          return { 
            success: false, 
            error,
            recoverable: false, // Erro mais grave
            message: 'Erro ao renovar sessão e nenhum token encontrado no localStorage'
          };
        }
        
        try {
          const tokenData = JSON.parse(tokenStr);
          if (tokenData && tokenData.access_token) {
            console.log('Token encontrado no localStorage, tentando definir sessão...');
            
            // Tenta definir a sessão com o token do localStorage
            try {
              const { data: signInData, error: signInError } = 
                await supabase.auth.setSession({
                  access_token: tokenData.access_token,
                  refresh_token: tokenData.refresh_token || '',
                });
                
              if (signInError || !signInData.session) {
                console.error('Falha ao definir sessão com token do localStorage:', signInError);
                return { 
                  success: false, 
                  error: signInError || new Error('Falha ao definir sessão'),
                  recoverable: true, // Ainda pode funcionar sem autenticação
                  message: 'Falha ao usar token do localStorage'
                };
              }
              
              console.log('Sessão renovada com sucesso usando token do localStorage');
              return { 
                success: true, 
                session: signInData.session,
                message: 'Sessão renovada com token do localStorage'
              };
            } catch (setSessionError) {
              console.error('Erro ao definir sessão:', setSessionError);
              return { 
                success: false, 
                error: setSessionError,
                recoverable: true,
                message: 'Erro ao definir sessão com token do localStorage'
              };
            }
          }
        } catch (parseError) {
          console.error('Erro ao analisar token do localStorage:', parseError);
        }
        
        return { 
          success: false, 
          error,
          recoverable: true,
          message: 'Falha em todas as tentativas de renovação de sessão'
        };
      }
      
      if (data.session) {
        console.log('Sessão renovada com sucesso via API!');
        return { 
          success: true, 
          session: data.session,
          message: 'Sessão renovada com sucesso via API'
        };
      } else {
        console.warn('Sessão não disponível após renovação');
        return { 
          success: false, 
          error: new Error('Sessão não disponível após renovação'),
          recoverable: true,
          message: 'Sessão não disponível após renovação'
        };
      }
    } catch (refreshError) {
      // Verificar se é o erro específico de sessão ausente
      if (refreshError instanceof Error && (
          refreshError.message.includes('Auth session missing') || 
          refreshError.message.includes('AuthSessionMissingError') ||
          refreshError.message.includes('expired'))) {
        console.warn('Erro de sessão ausente ao tentar renovar:', refreshError.message);
        return { 
          success: false, 
          error: refreshError,
          recoverable: true,
          message: 'Sessão ausente, operações não autenticadas permitidas'
        };
      }
      
      console.error('Erro não tratado ao renovar sessão:', refreshError);
      return { 
        success: false, 
        error: refreshError,
        recoverable: true, // Permitir operações mesmo com falhas
        message: 'Erro não tratado ao renovar sessão'
      };
    }
  } catch (error) {
    console.error('Erro crítico ao renovar sessão:', error);
    return { 
      success: false, 
      error,
      recoverable: true, // Permitir operações mesmo com falhas
      message: 'Erro crítico ao renovar sessão'
    };
  }
};

// Monitorar eventos de autenticação para debug
supabase.auth.onAuthStateChange((event, session) => {
  console.log(`Supabase Auth: Evento ${event}`, session ? 'com sessão' : 'sem sessão');
});

// Verificar tokens existentes no localStorage para debug
console.log('Tokens Supabase no localStorage:', 
  Object.keys(localStorage).filter(k => k.includes('sb-') || k.includes('supabase')));
