import { supabase } from './supabase';

/**
 * Sistema de autenticação simplificado para resolver problemas de persistência de sessão
 */

// Chave para armazenar o token no localStorage
const AUTH_TOKEN_KEY = 'sb-ubwzukpsqcrwzfbppoux-auth-token';

/**
 * Verifica se o usuário está autenticado
 * @returns Retorna true se o usuário estiver autenticado, false caso contrário
 */
export async function isAuthenticated(): Promise<boolean> {
  try {
    // Verificar se temos uma sessão válida
    const { data: sessionData } = await supabase.auth.getSession();
    
    if (sessionData.session) {
      // Verificar se a sessão está prestes a expirar
      const expiryTime = new Date(sessionData.session.expires_at * 1000);
      const now = new Date();
      const timeToExpiry = expiryTime.getTime() - now.getTime();
      
      // Se a sessão ainda for válida por mais de 5 minutos, retornar true
      if (timeToExpiry > 5 * 60 * 1000) {
        console.log(`[Auth] Sessão válida, expira em ${Math.floor(timeToExpiry / 60000)} minutos`);
        return true;
      }
      
      // Se a sessão estiver prestes a expirar, tentar atualizá-la
      console.log('[Auth] Sessão prestes a expirar, tentando atualizar...');
      return await refreshSession();
    }
    
    // Se não temos uma sessão, tentar recuperar do localStorage
    return await recoverSession();
  } catch (error) {
    console.error('[Auth] Erro ao verificar autenticação:', error);
    return false;
  }
}

/**
 * Atualiza a sessão do usuário
 * @returns Retorna true se a sessão foi atualizada com sucesso, false caso contrário
 */
export async function refreshSession(): Promise<boolean> {
  try {
    console.log('[Auth] Atualizando sessão...');
    
    // Tentar atualizar a sessão
    const { data, error } = await supabase.auth.refreshSession();
    
    if (error) {
      console.error('[Auth] Erro ao atualizar sessão:', error);
      return await recoverSession();
    }
    
    if (data.session) {
      console.log('[Auth] Sessão atualizada com sucesso');
      return true;
    }
    
    return false;
  } catch (error) {
    console.error('[Auth] Erro ao atualizar sessão:', error);
    return false;
  }
}

/**
 * Recupera a sessão do localStorage
 * @returns Retorna true se a sessão foi recuperada com sucesso, false caso contrário
 */
export async function recoverSession(): Promise<boolean> {
  try {
    console.log('[Auth] Tentando recuperar sessão do localStorage...');
    
    // Verificar se temos dados no localStorage
    const tokenStr = localStorage.getItem(AUTH_TOKEN_KEY);
    if (!tokenStr) {
      console.warn('[Auth] Nenhum token encontrado no localStorage');
      return false;
    }
    
    // Tentar analisar os dados
    const tokenData = JSON.parse(tokenStr);
    if (!tokenData || !tokenData.access_token) {
      console.warn('[Auth] Token inválido no localStorage');
      return false;
    }
    
    // Tentar definir a sessão
    const { data, error } = await supabase.auth.setSession({
      access_token: tokenData.access_token,
      refresh_token: tokenData.refresh_token || '',
    });
    
    if (error) {
      console.error('[Auth] Erro ao definir sessão:', error);
      return false;
    }
    
    if (data.session) {
      console.log('[Auth] Sessão recuperada com sucesso do localStorage');
      return true;
    }
    
    return false;
  } catch (error) {
    console.error('[Auth] Erro ao recuperar sessão do localStorage:', error);
    return false;
  }
}

/**
 * Faz login com email e senha
 * @param email Email do usuário
 * @param password Senha do usuário
 * @returns Retorna true se o login foi bem-sucedido, false caso contrário
 */
export async function login(email: string, password: string): Promise<boolean> {
  try {
    console.log('[Auth] Tentando fazer login...');
    
    // Tentar fazer login
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    
    if (error) {
      console.error('[Auth] Erro ao fazer login:', error);
      return false;
    }
    
    if (data.session) {
      console.log('[Auth] Login bem-sucedido');
      return true;
    }
    
    return false;
  } catch (error) {
    console.error('[Auth] Erro ao fazer login:', error);
    return false;
  }
}

/**
 * Faz logout do usuário
 * @returns Retorna true se o logout foi bem-sucedido, false caso contrário
 */
export async function logout(): Promise<boolean> {
  try {
    console.log('[Auth] Fazendo logout...');
    
    // Tentar fazer logout
    const { error } = await supabase.auth.signOut();
    
    if (error) {
      console.error('[Auth] Erro ao fazer logout:', error);
      return false;
    }
    
    console.log('[Auth] Logout bem-sucedido');
    return true;
  } catch (error) {
    console.error('[Auth] Erro ao fazer logout:', error);
    return false;
  }
}
