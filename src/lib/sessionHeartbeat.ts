import { supabase, refreshSupabaseSession, SUPABASE_STORAGE_KEY } from './supabase';

// Intervalo de heartbeat em milissegundos (10 minutos)
const HEARTBEAT_INTERVAL = 10 * 60 * 1000;

// Intervalo de verificação de sessão em milissegundos (2 minutos)
const SESSION_CHECK_INTERVAL = 2 * 60 * 1000;

// Flag para controlar se o heartbeat está ativo
let heartbeatActive = false;

// IDs dos intervalos para poder limpar depois
let heartbeatIntervalId: number | null = null;
let sessionCheckIntervalId: number | null = null;

/**
 * Inicia o mecanismo de heartbeat para manter a sessão ativa
 */
export function startSessionHeartbeat(): void {
  if (heartbeatActive) {
    console.log('[Heartbeat] Heartbeat já está ativo');
    return;
  }

  console.log('[Heartbeat] Iniciando mecanismo de heartbeat para manter a sessão ativa');
  heartbeatActive = true;

  // Executar imediatamente uma verificação de sessão
  checkAndRefreshSession();

  // Configurar intervalo para heartbeat (ping leve para manter a conexão)
  heartbeatIntervalId = window.setInterval(sendHeartbeat, HEARTBEAT_INTERVAL);

  // Configurar intervalo para verificação de sessão (mais intensivo, verifica e atualiza a sessão)
  sessionCheckIntervalId = window.setInterval(checkAndRefreshSession, SESSION_CHECK_INTERVAL);

  // Adicionar listeners para eventos de visibilidade e foco
  window.addEventListener('visibilitychange', handleVisibilityChange);
  window.addEventListener('focus', handleWindowFocus);

  console.log('[Heartbeat] Mecanismo de heartbeat iniciado com sucesso');
}

/**
 * Para o mecanismo de heartbeat
 */
export function stopSessionHeartbeat(): void {
  if (!heartbeatActive) {
    return;
  }

  console.log('[Heartbeat] Parando mecanismo de heartbeat');
  heartbeatActive = false;

  // Limpar intervalos
  if (heartbeatIntervalId !== null) {
    window.clearInterval(heartbeatIntervalId);
    heartbeatIntervalId = null;
  }

  if (sessionCheckIntervalId !== null) {
    window.clearInterval(sessionCheckIntervalId);
    sessionCheckIntervalId = null;
  }

  // Remover listeners
  window.removeEventListener('visibilitychange', handleVisibilityChange);
  window.removeEventListener('focus', handleWindowFocus);

  console.log('[Heartbeat] Mecanismo de heartbeat parado com sucesso');
}

/**
 * Envia um heartbeat para manter a conexão ativa
 * Este é um ping leve que não atualiza a sessão, apenas mantém a conexão
 */
async function sendHeartbeat(): Promise<void> {
  try {
    // Verificar se o usuário está logado antes de enviar o heartbeat
    const { data } = await supabase.auth.getSession();
    if (!data.session) {
      console.log('[Heartbeat] Nenhuma sessão ativa, pulando heartbeat');
      return;
    }

    console.log('[Heartbeat] Enviando heartbeat para manter a conexão ativa');

    // Fazer uma requisição leve para manter a conexão ativa
    // Usamos uma tabela que provavelmente existe e uma consulta que retorna poucos dados
    const { error } = await supabase
      .from('profiles')
      .select('id')
      .limit(1);

    if (error) {
      console.warn('[Heartbeat] Erro ao enviar heartbeat:', error);
      // Se houver erro, tentar atualizar a sessão
      await checkAndRefreshSession();
    } else {
      console.log('[Heartbeat] Heartbeat enviado com sucesso');
    }
  } catch (error) {
    console.error('[Heartbeat] Erro ao enviar heartbeat:', error);
  }
}

/**
 * Verifica e atualiza a sessão se necessário
 */
async function checkAndRefreshSession(): Promise<void> {
  try {
    console.log('[Heartbeat] Verificando sessão...');

    // Primeiro, verificar se temos uma sessão válida
    const { data: sessionData } = await supabase.auth.getSession();

    if (sessionData.session) {
      // Verificar se a sessão está prestes a expirar
      const expiryTime = new Date(sessionData.session.expires_at * 1000);
      const now = new Date();
      const timeToExpiry = expiryTime.getTime() - now.getTime();

      // Se a sessão ainda for válida por mais de 10 minutos, não é necessário atualizar
      if (timeToExpiry > 10 * 60 * 1000) {
        console.log(`[Heartbeat] Sessão válida, expira em ${Math.floor(timeToExpiry / 60000)} minutos`);
        return;
      }

      console.log('[Heartbeat] Sessão está prestes a expirar, atualizando...');
    } else {
      console.log('[Heartbeat] Nenhuma sessão ativa encontrada, tentando recuperar...');
    }

    // Tentar atualizar a sessão
    const success = await refreshSupabaseSession();

    if (success) {
      console.log('[Heartbeat] Sessão verificada/atualizada com sucesso');

      // Disparar evento de sessão atualizada
      const sessionRefreshedEvent = new CustomEvent('supabase-session-refreshed', {
        detail: {
          timestamp: Date.now(),
          success: true
        }
      });
      document.dispatchEvent(sessionRefreshedEvent);
    } else {
      console.warn('[Heartbeat] Falha ao verificar/atualizar sessão');

      // Disparar evento de problema de sessão
      const sessionProblemEvent = new CustomEvent('session-problem', {
        detail: {
          timestamp: Date.now(),
          message: 'Falha ao verificar/atualizar sessão'
        }
      });
      document.dispatchEvent(sessionProblemEvent);

      // Tentar recuperar a sessão do localStorage como último recurso
      try {
        const tokenStr = localStorage.getItem(SUPABASE_STORAGE_KEY);

        if (tokenStr) {
          const tokenData = JSON.parse(tokenStr);

          if (tokenData && tokenData.access_token) {
            console.log('[Heartbeat] Token encontrado no localStorage, tentando definir sessão...');

            // Tentar definir a sessão com o token do localStorage
            const { data: setData, error: setError } = await supabase.auth.setSession({
              access_token: tokenData.access_token,
              refresh_token: tokenData.refresh_token || '',
            });

            if (!setError && setData.session) {
              console.log('[Heartbeat] Sessão recuperada com sucesso do localStorage');

              // Disparar evento de sessão recuperada
              const sessionRecoveredEvent = new CustomEvent('supabase-session-recovered', {
                detail: {
                  timestamp: Date.now(),
                  success: true
                }
              });
              document.dispatchEvent(sessionRecoveredEvent);
            }
          }
        }
      } catch (localStorageError) {
        console.error('[Heartbeat] Erro ao recuperar sessão do localStorage:', localStorageError);
      }
    }
  } catch (error) {
    console.error('[Heartbeat] Erro ao verificar/atualizar sessão:', error);
  }
}

/**
 * Manipula eventos de mudança de visibilidade da página
 */
function handleVisibilityChange(): void {
  if (document.visibilityState === 'visible') {
    console.log('[Heartbeat] Página voltou a ficar visível, verificando sessão');
    checkAndRefreshSession();
  }
}

/**
 * Manipula eventos de foco na janela
 */
function handleWindowFocus(): void {
  console.log('[Heartbeat] Janela recebeu foco, verificando sessão');
  checkAndRefreshSession();
}

// Exportar uma função para forçar a verificação da sessão
export function forceSessionCheck(): Promise<boolean> {
  return refreshSupabaseSession();
}
