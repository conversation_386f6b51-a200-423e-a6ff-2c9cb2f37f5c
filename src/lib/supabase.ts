import { createClient } from '@supabase/supabase-js';

// Estas variáveis devem ser definidas no arquivo .env na raiz do projeto
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || '';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || '';

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('As variáveis de ambiente VITE_SUPABASE_URL e VITE_SUPABASE_ANON_KEY devem ser definidas.');
}

import { supabaseLogger } from './logger';

// Verificar se as variáveis de ambiente estão carregadas corretamente (apenas em desenvolvimento)
if (import.meta.env.DEV) {
  if (!supabaseUrl.includes('supabase.co')) {
    supabaseLogger.error('ERRO GRAVE: URL do Supabase inválida ou ausente!');
  }

  if (!supabaseAnonKey || supabaseAnonKey.length < 10) {
    supabaseLogger.error('ERRO GRAVE: Chave anônima do Supabase inválida ou muito curta!');
  }
}

// Cache de respostas para consultas frequentes
const responseCache = new Map<string, {data: any, error: any, timestamp: number}>();

// Valores padrão para TTL (time-to-live) do cache
const DEFAULT_CACHE_TTL = 60000; // 1 minuto em milissegundos
// Outros valores de TTL disponíveis para uso em diferentes cenários
export const LONG_CACHE_TTL = 300000; // 5 minutos em milissegundos para dados estáticos
export const SHORT_CACHE_TTL = 15000; // 15 segundos para dados mais voláteis

// Chave de armazenamento para a sessão do Supabase
export const SUPABASE_STORAGE_KEY = 'sb-ubwzukpsqcrwzfbppoux-auth-token';
// Chave de backup para a sessão do Supabase (usado para recuperação)
export const SUPABASE_BACKUP_KEY = 'sb-ubwzukpsqcrwzfbppoux-auth-backup';
// Chave para armazenar o timestamp da última atualização de sessão
export const SUPABASE_LAST_REFRESH_KEY = 'sb-ubwzukpsqcrwzfbppoux-last-refresh';
// Chave para armazenar o perfil do usuário
export const USER_PROFILE_KEY = 'userProfile';

/**
 * Implementação robusta de armazenamento para o Supabase
 * - Armazena tokens em localStorage e sessionStorage para redundância
 * - Implementa backup automático dos tokens
 * - Inclui logs detalhados para depuração
 */
const robustStorage = {
  getItem: (key: string): string | null => {
    try {
      // Primeiro tentar localStorage (persistente)
      const localData = localStorage.getItem(key);

      if (localData) {
        console.log(`[Supabase] Token recuperado do localStorage: ${key.substring(0, 15)}...`);

        // Sincronizar com sessionStorage para redundância
        try {
          sessionStorage.setItem(key, localData);
        } catch (syncError) {
          console.warn('[Supabase] Não foi possível sincronizar com sessionStorage:', syncError);
        }

        return localData;
      }

      // Se não encontrar no localStorage, tentar sessionStorage
      const sessionData = sessionStorage.getItem(key);

      if (sessionData) {
        console.log(`[Supabase] Token recuperado do sessionStorage: ${key.substring(0, 15)}...`);

        // Sincronizar com localStorage para persistência
        try {
          localStorage.setItem(key, sessionData);
          console.log('[Supabase] Token sincronizado com localStorage');
        } catch (syncError) {
          console.warn('[Supabase] Não foi possível sincronizar com localStorage:', syncError);
        }

        return sessionData;
      }

      // Se não encontrar em nenhum lugar, tentar o backup
      if (key === SUPABASE_STORAGE_KEY) {
        const backupData = localStorage.getItem(SUPABASE_BACKUP_KEY);

        if (backupData) {
          console.log('[Supabase] Token recuperado do backup');

          // Restaurar do backup
          try {
            localStorage.setItem(key, backupData);
            sessionStorage.setItem(key, backupData);
            console.log('[Supabase] Token restaurado do backup');
          } catch (restoreError) {
            console.warn('[Supabase] Erro ao restaurar do backup:', restoreError);
          }

          return backupData;
        }
      }

      console.log(`[Supabase] Token não encontrado: ${key}`);
      return null;
    } catch (error) {
      console.error('[Supabase] Erro ao recuperar token:', error);
      return null;
    }
  },

  setItem: (key: string, value: string): void => {
    try {
      // Armazenar em localStorage (persistente)
      localStorage.setItem(key, value);

      // Armazenar em sessionStorage (redundância)
      sessionStorage.setItem(key, value);

      // Se for o token principal, criar um backup
      if (key === SUPABASE_STORAGE_KEY) {
        localStorage.setItem(SUPABASE_BACKUP_KEY, value);

        // Registrar timestamp da atualização
        localStorage.setItem(SUPABASE_LAST_REFRESH_KEY, Date.now().toString());

        console.log('[Supabase] Token armazenado com backup');
      } else {
        console.log(`[Supabase] Token armazenado: ${key}`);
      }
    } catch (error) {
      console.error('[Supabase] Erro ao armazenar token:', error);

      // Tentar armazenar apenas em sessionStorage como fallback
      try {
        sessionStorage.setItem(key, value);
        console.log('[Supabase] Token armazenado apenas em sessionStorage (fallback)');
      } catch (sessionError) {
        console.error('[Supabase] Erro crítico ao armazenar token:', sessionError);
      }
    }
  },

  removeItem: (key: string): void => {
    try {
      // Remover de localStorage
      localStorage.removeItem(key);

      // Remover de sessionStorage
      sessionStorage.removeItem(key);

      // Se for o token principal, manter o backup por segurança
      if (key === SUPABASE_STORAGE_KEY) {
        console.log('[Supabase] Token removido, mas backup mantido');
      } else {
        console.log(`[Supabase] Token removido: ${key}`);
      }
    } catch (error) {
      console.error('[Supabase] Erro ao remover token:', error);
    }
  }
};

// Client do Supabase com configuração robusta
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,
    storageKey: SUPABASE_STORAGE_KEY,
    autoRefreshToken: true,
    detectSessionInUrl: true,
    flowType: 'implicit',
    debug: import.meta.env.DEV, // Debug apenas em desenvolvimento
    // Usar implementação robusta de armazenamento
    storage: robustStorage
  },
  global: {
    fetch: async (url: RequestInfo | URL, options?: RequestInit) => {
      // Log detalhado apenas em desenvolvimento
      // Simplificar a lógica de log para evitar problemas de tipo
      const shouldLog = import.meta.env.DEV;

      // Log mínimo para operações de escrita
      if (shouldLog && (options?.method === 'POST' || options?.method === 'PUT' || options?.method === 'DELETE')) {
        // Simplificar a URL para o log
        const urlForLog = url.toString().split('?')[0];
        console.debug(`Supabase ${options?.method} - URL: ${urlForLog}`);
      }

      try {
        const response = await fetch(url, options);

        // Log de erros apenas para respostas com falha e não para autenticação
        if (!response.ok && response.status !== 304) {
          // Evitar log excessivo para erros 400 comuns de autenticação
          // Simplificar a lógica para evitar problemas de tipo
          const shouldLogError = import.meta.env.DEV &&
            (response.status >= 500 || (response.status !== 400 || !url.toString().includes('/auth/v1/')));

          if (shouldLogError) {
            console.warn(`Supabase response - Status: ${response.status} - URL: ${url.toString().split('?')[0]}`);
          }
        }

        return response;
      } catch (error) {
        // Sempre logar erros de rede críticos
        console.error('Supabase fetch error:', error instanceof Error ? error.message : error);
        throw error;
      }
    },
  },
  db: {
    schema: 'public',
  },
});

/**
 * Função para limpar completamente a autenticação e reiniciar o cliente Supabase
 * Útil para recuperar de erros 500 inesperados
 */
export async function resetSupabaseClient() {
  console.log('Reiniciando o cliente Supabase...');

  // Não limpar os tokens armazenados no localStorage para evitar ciclos de autenticação
  try {
    // Apenas registrar que a função foi chamada
    console.log('Tokens do Supabase mantidos para evitar ciclos de autenticação');
  } catch (e) {
    console.error('Erro ao verificar localStorage:', e);
  }

  // Forçar logout
  try {
    await supabase.auth.signOut({ scope: 'local' });
    console.log('Logout forçado realizado com sucesso');
  } catch (e) {
    console.error('Erro ao realizar logout forçado:', e);
  }

  // Limpar qualquer cache interno do Supabase
  try {
    await fetch(`${supabaseUrl}/auth/v1/logout`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': supabaseAnonKey,
      },
    });
  } catch (e) {
    console.error('Erro ao limpar cache interno:', e);
  }

  console.log('Cliente Supabase resetado com sucesso');
  return true;
}

// Esta função foi substituída por uma versão mais simples no final do arquivo
// Mantendo a assinatura para compatibilidade com código existente
export const refreshSupabaseSessionLegacy = async () => {
  console.log('[Supabase] Usando nova implementação de refreshSupabaseSession');
  const success = await refreshSupabaseSession();
  return {
    success,
    message: success ? 'Sessão atualizada com sucesso' : 'Falha ao atualizar sessão',
    recoverable: true
  };
};

// Monitorar eventos de autenticação e disparar eventos personalizados
supabase.auth.onAuthStateChange((event, session) => {
  // Registrar eventos de autenticação para debug
  if (import.meta.env.DEV) {
    console.log(`[Supabase Auth] Evento ${event}`, session ? 'com sessão' : 'sem sessão');
  }

  // Disparar evento personalizado para notificar componentes sobre mudanças de autenticação
  const authEvent = new CustomEvent('supabase-auth-state-change', {
    detail: {
      event,
      session,
      timestamp: Date.now()
    }
  });
  document.dispatchEvent(authEvent);
});

// Configurações para consultas robustas
const QUERY_TIMEOUT = 15000; // 15 segundos
const MAX_RETRY_ATTEMPTS = 3;
const RETRY_DELAY = 1000; // 1 segundo

/**
 * Função utilitária para cache de consultas com retentativas e timeout
 * @param cacheKey Chave para armazenar no cache
 * @param fetcher Função que realiza a consulta
 * @param ttl Tempo de vida do cache em milissegundos
 * @returns Resultado da consulta com dados ou erro
 */
export async function getCachedData<T>(
  cacheKey: string,
  fetcher: () => Promise<{data: T | null, error: any}>,
  ttl = DEFAULT_CACHE_TTL
): Promise<{data: T | null, error: any}> {
  const cached = responseCache.get(cacheKey);
  const now = Date.now();

  // Usar cache se existir e não tiver expirado
  if (cached && (now - cached.timestamp) < ttl) {
    supabaseLogger.debug(`Usando dados em cache para ${cacheKey}`);
    return {data: cached.data, error: cached.error};
  }

  // Buscar novos dados com retentativas
  return await fetchWithRetry(cacheKey, fetcher, ttl);
}

/**
 * Função para realizar consultas com retentativas e timeout
 * @param cacheKey Chave para armazenar no cache
 * @param fetcher Função que realiza a consulta
 * @param ttl Tempo de vida do cache em milissegundos
 * @returns Resultado da consulta com dados ou erro
 */
async function fetchWithRetry<T>(
  cacheKey: string,
  fetcher: () => Promise<{data: T | null, error: any}>,
  ttl = DEFAULT_CACHE_TTL
): Promise<{data: T | null, error: any}> {
  let attempts = 0;
  let lastError: any = null;

  while (attempts < MAX_RETRY_ATTEMPTS) {
    attempts++;
    supabaseLogger.debug(`Tentativa ${attempts}/${MAX_RETRY_ATTEMPTS} para ${cacheKey}`);

    try {
      // Criar uma promessa com timeout
      const timeoutPromise = new Promise<{data: T | null, error: any}>((_, reject) => {
        setTimeout(() => reject(new Error(`Timeout de ${QUERY_TIMEOUT}ms excedido`)), QUERY_TIMEOUT);
      });

      // Executar a consulta com timeout
      const result = await Promise.race([fetcher(), timeoutPromise]);

      // Verificar se a sessão está válida
      if (result.error && (result.error.code === 'PGRST301' || result.error.message?.includes('JWT'))) {
        supabaseLogger.warn(`Erro de autenticação na consulta ${cacheKey}, tentando atualizar sessão...`);

        // Tentar atualizar a sessão
        const sessionRefreshed = await refreshSupabaseSession();

        if (sessionRefreshed) {
          supabaseLogger.debug(`Sessão atualizada com sucesso, tentando novamente...`);
          continue; // Tentar novamente após atualizar a sessão
        }
      }

      // Armazenar no cache apenas se a requisição for bem-sucedida
      if (result.data && !result.error) {
        const now = Date.now();
        responseCache.set(cacheKey, {
          data: result.data,
          error: null,
          timestamp: now
        });
        supabaseLogger.debug(`Dados armazenados em cache para ${cacheKey}`);
      }

      return result;
    } catch (error) {
      supabaseLogger.error(`Erro na tentativa ${attempts} para ${cacheKey}:`, error);
      lastError = error;

      // Aguardar antes de tentar novamente
      if (attempts < MAX_RETRY_ATTEMPTS) {
        await new Promise(resolve => setTimeout(resolve, RETRY_DELAY * attempts));
      }
    }
  }

  // Todas as tentativas falharam
  supabaseLogger.error(`Todas as ${MAX_RETRY_ATTEMPTS} tentativas falharam para ${cacheKey}`);

  // Verificar se temos dados em cache, mesmo expirados, para usar como fallback
  const cachedData = responseCache.get(cacheKey);
  if (cachedData) {
    supabaseLogger.debug(`Usando dados em cache expirados como fallback para ${cacheKey}`);
    return {data: cachedData.data, error: null};
  }

  return { data: null, error: lastError };
}

// Função para limpar o cache
export function clearSupabaseCache(keyPattern?: string) {
  console.log(`[Supabase] Limpando cache${keyPattern ? ` com padrão: ${keyPattern}` : ''}`);

  if (keyPattern) {
    // Limpar apenas chaves que correspondem ao padrão
    const keysToDelete = [];
    for (const key of responseCache.keys()) {
      if (key.includes(keyPattern)) {
        keysToDelete.push(key);
      }
    }

    // Deletar as chaves encontradas
    for (const key of keysToDelete) {
      responseCache.delete(key);
    }

    console.log(`[Supabase] ${keysToDelete.length} itens removidos do cache`);
  } else {
    // Limpar todo o cache
    const cacheSize = responseCache.size;
    responseCache.clear();
    console.log(`[Supabase] Todo o cache foi limpo (${cacheSize} itens)`);
  }
}

// Flag para evitar múltiplas chamadas simultâneas de refreshSupabaseSession
let isRefreshingSession = false;
// Intervalo mínimo entre atualizações de sessão (em ms)
const MIN_REFRESH_INTERVAL = 60000; // 60 segundos para reduzir frequência de atualizações
// Timestamp da última atualização bem-sucedida
let lastSuccessfulRefresh = 0;

/**
 * Função para realizar consultas diretas ao Supabase com retentativas e timeout
 * @param queryFn Função que realiza a consulta
 * @returns Resultado da consulta
 */
export async function executeRobustQuery<T>(
  queryFn: () => Promise<{data: T | null, error: any}>,
  description: string = "consulta",
  maxRetries: number = 3
): Promise<{data: T | null, error: any}> {
  let attempts = 0;
  let lastError: any = null;
  let sessionRefreshAttempts = 0;
  const MAX_SESSION_REFRESH_ATTEMPTS = 1; // Reduzido para evitar loops
  const MAX_RETRY_COUNT = Math.max(1, Math.min(5, maxRetries)); // Limitar entre 1 e 5

  // Verificar a sessão antes de iniciar as consultas
  try {
    console.log(`[Supabase] Iniciando ${description}...`);
    const { data: sessionData } = await supabase.auth.getSession();

    if (!sessionData.session) {
      console.warn(`[Supabase] Nenhuma sessão encontrada antes de iniciar ${description}, tentando atualizar...`);

      // Usar a função simplificada para atualizar a sessão
      const sessionRefreshed = await refreshSupabaseSession();

      if (!sessionRefreshed) {
        console.warn(`[Supabase] Não foi possível recuperar a sessão antes de ${description}, continuando mesmo assim...`);
      } else {
        console.log(`[Supabase] Sessão recuperada com sucesso antes de ${description}`);
      }
    } else {
      console.log(`[Supabase] Sessão válida encontrada antes de ${description}`);
    }
  } catch (sessionError) {
    console.warn(`[Supabase] Erro ao verificar sessão antes de ${description}:`, sessionError);
  }

  while (attempts < MAX_RETRY_COUNT) {
    attempts++;
    console.log(`[Supabase] Tentativa ${attempts}/${MAX_RETRY_COUNT} para ${description}`);

    try {
      // Criar uma promessa com timeout
      const timeoutPromise = new Promise<{data: T | null, error: any}>((_, reject) => {
        setTimeout(() => reject(new Error(`Timeout de ${QUERY_TIMEOUT}ms excedido`)), QUERY_TIMEOUT);
      });

      // Executar a consulta com timeout
      const result = await Promise.race([queryFn(), timeoutPromise]);

      // Verificar se há erro de autenticação (códigos e mensagens comuns)
      const isAuthError = result.error && (
        result.error.code === 'PGRST301' ||
        result.error.code === '401' ||
        result.error.code === 'PGRST401' ||
        result.error.status === 401 ||
        result.error.message?.includes('JWT') ||
        result.error.message?.includes('token') ||
        result.error.message?.includes('auth') ||
        result.error.message?.includes('authentication') ||
        result.error.message?.includes('Authorization')
      );

      if (isAuthError) {
        console.warn(`[Supabase] Erro de autenticação na ${description}: ${result.error.message || result.error.code}`);

        if (sessionRefreshAttempts < MAX_SESSION_REFRESH_ATTEMPTS) {
          sessionRefreshAttempts++;
          console.log(`[Supabase] Tentativa ${sessionRefreshAttempts}/${MAX_SESSION_REFRESH_ATTEMPTS} de atualizar sessão...`);

          // Tentar atualizar a sessão usando a função simplificada
          const sessionRefreshed = await refreshSupabaseSession();

          if (sessionRefreshed) {
            console.log(`[Supabase] Sessão atualizada com sucesso, tentando novamente...`);
            // Aguardar um pouco para garantir que a sessão seja propagada
            await new Promise(resolve => setTimeout(resolve, 500));
            continue; // Tentar novamente após atualizar a sessão
          }
        } else {
          console.error(`[Supabase] Máximo de tentativas de atualização de sessão atingido para ${description}`);
        }
      }

      // Se não houver erro, retornar o resultado
      if (!result.error) {
        console.log(`[Supabase] ${description} concluída com sucesso na tentativa ${attempts}`);
        return result;
      }

      // Se houver erro, mas não for de autenticação, tentar novamente
      console.warn(`[Supabase] Erro na ${description}:`, result.error);
      lastError = result.error;

      // Aguardar antes de tentar novamente
      if (attempts < MAX_RETRY_COUNT) {
        const delay = RETRY_DELAY * attempts;
        console.log(`[Supabase] Aguardando ${delay}ms antes da próxima tentativa...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    } catch (error) {
      console.error(`[Supabase] Erro na tentativa ${attempts} para ${description}:`, error);
      lastError = error;

      // Aguardar antes de tentar novamente
      if (attempts < MAX_RETRY_COUNT) {
        const delay = RETRY_DELAY * attempts;
        console.log(`[Supabase] Aguardando ${delay}ms antes da próxima tentativa...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  // Todas as tentativas falharam
  console.error(`[Supabase] Todas as ${MAX_RETRY_COUNT} tentativas falharam para ${description}`);

  // Criar um erro mais informativo
  const errorMessage = lastError instanceof Error
    ? lastError.message
    : (lastError?.message || lastError?.code || 'Erro desconhecido');

  const enhancedError = {
    message: `Erro de conexão: Não foi possível carregar os dados. Tente recarregar a página ou fazer login novamente. (${errorMessage})`,
    originalError: lastError,
    code: lastError?.code || 'QUERY_ERROR'
  };

  return { data: null, error: enhancedError };
}

/**
 * Atualiza a sessão do Supabase com mecanismo simplificado e mais confiável
 * @returns Retorna true se a sessão foi atualizada com sucesso, false caso contrário
 */
export async function refreshSupabaseSession(): Promise<boolean> {
  // Verificar se já existe uma atualização em andamento
  if (isRefreshingSession) {
    console.log('[Supabase] Já existe uma atualização de sessão em andamento, aguardando...');
    // Aguardar um pouco e retornar o resultado da operação em andamento
    await new Promise(resolve => setTimeout(resolve, 500));
    return checkCurrentSession();
  }

  // Verificar se a última atualização foi recente demais
  try {
    // Primeiro verificar a variável em memória para maior eficiência
    if (lastSuccessfulRefresh > 0) {
      const now = Date.now();
      const timeSinceLastRefresh = now - lastSuccessfulRefresh;

      if (timeSinceLastRefresh < MIN_REFRESH_INTERVAL) {
        console.log(`[Supabase] Última atualização bem-sucedida foi há apenas ${Math.floor(timeSinceLastRefresh / 1000)}s, verificando sessão atual...`);
        return checkCurrentSession();
      }
    }
  } catch (timeError) {
    console.warn('[Supabase] Erro ao verificar timestamp da última atualização:', timeError);
  }

  try {
    isRefreshingSession = true;
    console.log('[Supabase] Iniciando atualização simplificada de sessão...');

    // Verificar se já temos uma sessão válida
    const { data: sessionData } = await supabase.auth.getSession();
    if (sessionData.session) {
      // Verificar se a sessão ainda é válida
      const expiryTime = new Date(sessionData.session.expires_at * 1000);
      const now = new Date();
      const timeToExpiry = expiryTime.getTime() - now.getTime();

      // Se a sessão ainda for válida por mais de 5 minutos, não é necessário atualizar
      if (timeToExpiry > 5 * 60 * 1000) {
        console.log(`[Supabase] Sessão atual ainda é válida, expira em ${Math.floor(timeToExpiry / 60000)} minutos`);

        // Registrar timestamp da atualização bem-sucedida
        const now = Date.now();
        localStorage.setItem(SUPABASE_LAST_REFRESH_KEY, now.toString());
        lastSuccessfulRefresh = now;

        // Disparar evento personalizado para notificar componentes
        dispatchSessionRefreshedEvent(true);

        isRefreshingSession = false;
        return true;
      }

      console.log('[Supabase] Sessão encontrada, mas está prestes a expirar. Renovando...');
    } else {
      console.log('[Supabase] Nenhuma sessão ativa encontrada. Tentando renovar...');
    }

    // Abordagem simplificada: tentar atualizar a sessão diretamente
    const { data, error } = await supabase.auth.refreshSession();

    if (!error && data.session) {
      console.log('[Supabase] Sessão atualizada com sucesso, expira em:',
        new Date(data.session.expires_at * 1000).toLocaleString());

      // Registrar timestamp da atualização bem-sucedida
      const now = Date.now();
      localStorage.setItem(SUPABASE_LAST_REFRESH_KEY, now.toString());
      lastSuccessfulRefresh = now;

      // Disparar evento personalizado para notificar componentes
      dispatchSessionRefreshedEvent(true);

      isRefreshingSession = false;
      return true;
    }

    // Se houve erro, tentar recuperar a sessão do localStorage como último recurso
    if (error) {
      console.warn('[Supabase] Erro ao atualizar sessão:', error.message);

      try {
        const tokenStr = localStorage.getItem(SUPABASE_STORAGE_KEY);
        if (tokenStr) {
          const tokenData = JSON.parse(tokenStr);
          if (tokenData && tokenData.access_token) {
            console.log('[Supabase] Token encontrado no localStorage, tentando definir sessão...');

            const { data: setData, error: setError } = await supabase.auth.setSession({
              access_token: tokenData.access_token,
              refresh_token: tokenData.refresh_token || '',
            });

            if (!setError && setData.session) {
              console.log('[Supabase] Sessão recuperada com sucesso do localStorage');

              // Registrar timestamp da atualização bem-sucedida
              const now = Date.now();
              localStorage.setItem(SUPABASE_LAST_REFRESH_KEY, now.toString());
              lastSuccessfulRefresh = now;

              // Disparar evento personalizado para notificar componentes
              dispatchSessionRefreshedEvent(true);

              isRefreshingSession = false;
              return true;
            }
          }
        }
      } catch (localStorageError) {
        console.error('[Supabase] Erro ao recuperar token do localStorage:', localStorageError);
      }
    }

    // Se chegou aqui, não conseguiu atualizar a sessão
    console.warn('[Supabase] Não foi possível atualizar a sessão');
    isRefreshingSession = false;
    dispatchSessionRefreshedEvent(false);
    return false;
  } catch (error) {
    console.error('[Supabase] Erro crítico ao atualizar sessão:', error);
    isRefreshingSession = false;
    dispatchSessionRefreshedEvent(false);
    return false;
  }
}



/**
 * Verifica se a sessão atual é válida e não está prestes a expirar
 * @param forceValidation Se true, faz uma validação mais agressiva da sessão
 */
async function checkCurrentSession(forceValidation: boolean = false): Promise<boolean> {
  try {
    console.log('[Supabase] Verificando sessão atual...');
    const { data: sessionData } = await supabase.auth.getSession();

    if (!sessionData.session) {
      console.warn('[Supabase] Nenhuma sessão encontrada');
      return false;
    }

    // Verificar se a sessão está prestes a expirar
    const expiryTime = new Date(sessionData.session.expires_at * 1000);
    const now = new Date();
    const timeToExpiry = expiryTime.getTime() - now.getTime();
    const minutesToExpiry = Math.floor(timeToExpiry / 60000);

    // Se forceValidation for true, fazer uma validação mais agressiva
    if (forceValidation) {
      console.log('[Supabase] Realizando validação agressiva da sessão...');

      // Fazer uma consulta simples para verificar se a sessão é realmente válida
      try {
        const { error: testError } = await supabase
          .from('profiles')
          .select('id')
          .limit(1);

        if (testError) {
          console.warn('[Supabase] Sessão inválida detectada na validação agressiva:', testError.message);
          return false;
        }

        console.log('[Supabase] Sessão validada com sucesso na validação agressiva');
      } catch (testError) {
        console.warn('[Supabase] Erro na validação agressiva da sessão:', testError);
        return false;
      }
    }

    // Reduzir o tempo mínimo para atualização para 5 minutos
    const minValidTime = forceValidation ? 5 * 60 * 1000 : 10 * 60 * 1000;

    if (timeToExpiry > minValidTime) {
      console.log(`[Supabase] Sessão atual ainda é válida, expira em ${minutesToExpiry} minutos`);
      return true;
    }

    console.log(`[Supabase] Sessão expira em ${minutesToExpiry} minutos, atualizando...`);
    return false;
  } catch (sessionError) {
    console.warn('[Supabase] Erro ao verificar sessão atual:', sessionError);
    return false;
  }
}

/**
 * Tenta recuperar a sessão do armazenamento usando uma estratégia robusta
 * com múltiplas fontes e tentativas
 */
async function recoverSessionFromStorage(): Promise<boolean> {
  console.log('[Supabase] Iniciando recuperação robusta de sessão');

  // Fontes de dados em ordem de prioridade
  const sources = [
    { name: 'localStorage', key: SUPABASE_STORAGE_KEY, get: () => localStorage.getItem(SUPABASE_STORAGE_KEY) },
    { name: 'sessionStorage', key: SUPABASE_STORAGE_KEY, get: () => sessionStorage.getItem(SUPABASE_STORAGE_KEY) },
    { name: 'backup', key: SUPABASE_BACKUP_KEY, get: () => localStorage.getItem(SUPABASE_BACKUP_KEY) }
  ];

  // Tentar cada fonte
  for (const source of sources) {
    try {
      console.log(`[Supabase] Tentando recuperar sessão de ${source.name}`);
      const storedData = source.get();

      if (!storedData) {
        console.log(`[Supabase] Nenhum dado encontrado em ${source.name}`);
        continue;
      }

      let parsedData: Record<string, any>;
      try {
        parsedData = JSON.parse(storedData);
      } catch (parseError) {
        console.warn(`[Supabase] Erro ao analisar dados de ${source.name}:`, parseError);
        continue;
      }

      if (!parsedData || !parsedData.access_token || !parsedData.refresh_token) {
        console.warn(`[Supabase] Dados incompletos em ${source.name}`);
        continue;
      }

      console.log(`[Supabase] Dados válidos encontrados em ${source.name}, tentando definir sessão`);

      // Tentar definir a sessão
      const success = await trySetSession(parsedData);
      if (success) {
        // Sincronizar com outras fontes para garantir consistência
        try {
          localStorage.setItem(SUPABASE_STORAGE_KEY, storedData);
          sessionStorage.setItem(SUPABASE_STORAGE_KEY, storedData);
          localStorage.setItem(SUPABASE_BACKUP_KEY, storedData);
          localStorage.setItem(SUPABASE_LAST_REFRESH_KEY, Date.now().toString());
          console.log('[Supabase] Dados de sessão sincronizados em todas as fontes');
        } catch (syncError) {
          console.warn('[Supabase] Erro ao sincronizar dados de sessão:', syncError);
        }

        return true;
      }
    } catch (error) {
      console.warn(`[Supabase] Erro ao acessar ${source.name}:`, error);
    }
  }

  // Se chegamos aqui, todas as fontes falharam
  console.warn('[Supabase] Todas as fontes de recuperação de sessão falharam');

  // Última tentativa: tentar fazer login anônimo se permitido
  try {
    console.log('[Supabase] Tentando criar sessão anônima como último recurso');
    const { data, error } = await supabase.auth.signInAnonymously();

    if (error) {
      console.warn('[Supabase] Falha ao criar sessão anônima:', error);
      return false;
    }

    if (data.session) {
      console.log('[Supabase] Sessão anônima criada com sucesso');
      dispatchSessionRefreshedEvent(true);
      return true;
    }
  } catch (anonError) {
    console.error('[Supabase] Erro ao tentar criar sessão anônima:', anonError);
  }

  return false;
}

/**
 * Tenta definir a sessão com os dados fornecidos
 */
async function trySetSession(sessionData: any): Promise<boolean> {
  try {
    // Garantir que todos os campos necessários estejam presentes
    const formattedData = {
      access_token: sessionData.access_token,
      refresh_token: sessionData.refresh_token,
      expires_at: sessionData.expires_at,
      expires_in: sessionData.expires_in || 3600,
      token_type: sessionData.token_type || 'bearer'
    };

    // Verificar se o token não está expirado
    const expiryTime = new Date(formattedData.expires_at * 1000);
    const now = new Date();

    if (expiryTime <= now) {
      console.warn('[Supabase] Token expirado, tentando usar refresh_token');
      // Tentar usar apenas o refresh_token
      const { data, error } = await supabase.auth.refreshSession({
        refresh_token: formattedData.refresh_token
      });

      if (error) {
        console.error('[Supabase] Erro ao atualizar sessão com refresh_token:', error);
        return false;
      }

      if (data.session) {
        console.log('[Supabase] Sessão atualizada com sucesso usando refresh_token');
        dispatchSessionRefreshedEvent(true);
        return true;
      }

      return false;
    }

    // Definir a sessão
    const { data: setData, error: setError } = await supabase.auth.setSession(formattedData);

    if (setError) {
      console.error('[Supabase] Erro ao definir sessão:', setError);
      return false;
    }

    if (setData.session) {
      console.log('[Supabase] Sessão definida com sucesso');
      dispatchSessionRefreshedEvent(true);
      return true;
    }

    console.warn('[Supabase] Falha ao definir sessão');
    return false;
  } catch (error) {
    console.error('[Supabase] Erro ao tentar definir sessão:', error);
    return false;
  }
}

/**
 * Dispara um evento personalizado para notificar componentes sobre a atualização da sessão
 */
function dispatchSessionRefreshedEvent(success: boolean): void {
  const now = Date.now();

  // Se a atualização foi bem-sucedida, registrar o timestamp
  if (success) {
    try {
      localStorage.setItem(SUPABASE_LAST_REFRESH_KEY, now.toString());
      lastSuccessfulRefresh = now;

      // Sincronizar o perfil do usuário entre localStorage e sessionStorage
      syncUserProfile();
    } catch (error) {
      console.warn('[Supabase] Erro ao registrar timestamp de atualização bem-sucedida:', error);
    }
  }

  const sessionEvent = new CustomEvent('supabase-session-refreshed', {
    detail: {
      timestamp: now,
      success
    }
  });
  document.dispatchEvent(sessionEvent);

  // Se a atualização foi bem-sucedida, também disparar um evento de reconexão
  if (success) {
    const reconnectEvent = new CustomEvent('app-reconnected', {
      detail: {
        timestamp: now,
        source: 'session-refresh'
      }
    });
    document.dispatchEvent(reconnectEvent);
  }
}

/**
 * Sincroniza o perfil do usuário entre localStorage e sessionStorage
 * para garantir persistência e redundância
 */
export function syncUserProfile(): void {
  try {
    // Verificar se existe perfil no localStorage
    const localProfileStr = localStorage.getItem(USER_PROFILE_KEY);

    if (localProfileStr) {
      // Sincronizar com sessionStorage
      try {
        sessionStorage.setItem(USER_PROFILE_KEY, localProfileStr);
        console.log('[Supabase] Perfil do usuário sincronizado com sessionStorage');
      } catch (syncError) {
        console.warn('[Supabase] Erro ao sincronizar perfil com sessionStorage:', syncError);
      }
      return;
    }

    // Se não existe no localStorage, verificar se existe no sessionStorage
    const sessionProfileStr = sessionStorage.getItem(USER_PROFILE_KEY);

    if (sessionProfileStr) {
      // Sincronizar com localStorage
      try {
        localStorage.setItem(USER_PROFILE_KEY, sessionProfileStr);
        console.log('[Supabase] Perfil do usuário sincronizado com localStorage');
      } catch (syncError) {
        console.warn('[Supabase] Erro ao sincronizar perfil com localStorage:', syncError);
      }
    }
  } catch (error) {
    console.error('[Supabase] Erro ao sincronizar perfil do usuário:', error);
  }
}

/**
 * Recupera o perfil do usuário de forma robusta, tentando várias fontes
 * @returns O perfil do usuário ou null se não encontrado
 */
export function getUserProfile(): any | null {
  try {
    // Primeiro tentar localStorage
    const localProfileStr = localStorage.getItem(USER_PROFILE_KEY);

    if (localProfileStr) {
      try {
        const profile = JSON.parse(localProfileStr);
        if (profile && profile.email && profile.role) {
          console.log('[Supabase] Perfil do usuário recuperado do localStorage');
          return profile;
        }
      } catch (parseError) {
        console.warn('[Supabase] Erro ao analisar perfil do localStorage:', parseError);
      }
    }

    // Se não encontrou no localStorage, tentar sessionStorage
    const sessionProfileStr = sessionStorage.getItem(USER_PROFILE_KEY);

    if (sessionProfileStr) {
      try {
        const profile = JSON.parse(sessionProfileStr);
        if (profile && profile.email && profile.role) {
          console.log('[Supabase] Perfil do usuário recuperado do sessionStorage');

          // Sincronizar com localStorage
          try {
            localStorage.setItem(USER_PROFILE_KEY, sessionProfileStr);
          } catch (syncError) {
            console.warn('[Supabase] Erro ao sincronizar perfil com localStorage:', syncError);
          }

          return profile;
        }
      } catch (parseError) {
        console.warn('[Supabase] Erro ao analisar perfil do sessionStorage:', parseError);
      }
    }

    console.log('[Supabase] Perfil do usuário não encontrado');
    return null;
  } catch (error) {
    console.error('[Supabase] Erro ao recuperar perfil do usuário:', error);
    return null;
  }
}

/**
 * Salva o perfil do usuário de forma robusta em localStorage e sessionStorage
 * @param profile O perfil do usuário a ser salvo
 */
export function saveUserProfile(profile: any): void {
  if (!profile) {
    console.warn('[Supabase] Tentativa de salvar perfil nulo');
    return;
  }

  try {
    const profileStr = JSON.stringify(profile);

    // Salvar em localStorage
    try {
      localStorage.setItem(USER_PROFILE_KEY, profileStr);
    } catch (localError) {
      console.warn('[Supabase] Erro ao salvar perfil no localStorage:', localError);
    }

    // Salvar em sessionStorage
    try {
      sessionStorage.setItem(USER_PROFILE_KEY, profileStr);
    } catch (sessionError) {
      console.warn('[Supabase] Erro ao salvar perfil no sessionStorage:', sessionError);
    }

    console.log('[Supabase] Perfil do usuário salvo com sucesso');
  } catch (error) {
    console.error('[Supabase] Erro ao salvar perfil do usuário:', error);
  }
}
