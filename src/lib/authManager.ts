import { supabase, SUPABASE_STORAGE_KEY } from './supabase';
import { startSessionHeartbeat, stopSessionHeartbeat, forceSessionCheck } from './sessionHeartbeat';

/**
 * Gerenciador de autenticação aprimorado
 * Esta classe gerencia a autenticação do usuário com mecanismos robustos de persistência de sessão
 */
class AuthManager {
  private isRunning = false;
  private isCheckingSession = false; // Flag para evitar verificações simultâneas
  private sessionCheckCount = 0; // Contador de verificações de sessão
  private lastSessionCheck = 0; // Timestamp da última verificação de sessão

  /**
   * Inicia o gerenciador de autenticação
   */
  public start(): void {
    if (this.isRunning) {
      console.log('[AuthManager] Já está em execução');
      return;
    }

    console.log('[AuthManager] Iniciando gerenciador de autenticação aprimorado');
    this.isRunning = true;

    // Verificar a sessão imediatamente
    this.checkSession();

    // Iniciar o mecanismo de heartbeat
    startSessionHeartbeat();

    // Adicionar listener para mudanças de visibilidade
    document.addEventListener('visibilitychange', this.handleVisibilityChange);

    // Adicionar listener para o evento de foco da janela
    window.addEventListener('focus', this.handleWindowFocus);

    // Adicionar listener para o evento de problema de sessão
    document.addEventListener('session-problem', this.handleSessionProblem);

    // Adicionar listener para o evento de sessão atualizada
    document.addEventListener('supabase-session-refreshed', this.handleSessionRefreshed);

    // Configurar verificação periódica da sessão para manter a conexão estável
    this.setupPeriodicSessionCheck();
  }

  /**
   * Configura verificação periódica da sessão para manter a conexão estável
   */
  private setupPeriodicSessionCheck(): void {
    // Verificar a sessão a cada 5 minutos para mantê-la ativa
    const checkInterval = 5 * 60 * 1000; // 5 minutos

    setInterval(() => {
      if (this.isRunning && !document.hidden) {
        console.log('[AuthManager] Executando verificação periódica da sessão');
        this.refreshSession().catch(err => {
          console.warn('[AuthManager] Erro na verificação periódica da sessão:', err);
        });
      }
    }, checkInterval);
  }

  /**
   * Para o gerenciador de autenticação
   */
  public stop(): void {
    console.log('[AuthManager] Parando gerenciador de autenticação');
    this.isRunning = false;

    // Parar o mecanismo de heartbeat
    stopSessionHeartbeat();

    // Remover listeners
    document.removeEventListener('visibilitychange', this.handleVisibilityChange);
    window.removeEventListener('focus', this.handleWindowFocus);
    document.removeEventListener('session-problem', this.handleSessionProblem);
    document.removeEventListener('supabase-session-refreshed', this.handleSessionRefreshed);
  }

  /**
   * Verifica se a sessão está válida e a atualiza se necessário
   */
  public async checkSession(): Promise<boolean> {
    // Evitar verificações simultâneas
    if (this.isCheckingSession) {
      console.log('[AuthManager] Já existe uma verificação de sessão em andamento, aguardando...');
      // Aguardar um pouco e retornar
      await new Promise(resolve => setTimeout(resolve, 500));
      return true;
    }

    // Limitar a frequência de verificações
    const now = Date.now();
    const timeSinceLastCheck = now - this.lastSessionCheck;
    if (timeSinceLastCheck < 2000 && this.sessionCheckCount > 0) {
      console.log(`[AuthManager] Verificação de sessão muito frequente (${timeSinceLastCheck}ms desde a última), ignorando`);
      return true;
    }

    try {
      this.isCheckingSession = true;
      this.sessionCheckCount++;
      this.lastSessionCheck = now;

      console.log(`[AuthManager] Verificando sessão (verificação #${this.sessionCheckCount})`);

      // Usar o mecanismo de heartbeat para verificar a sessão
      const sessionValid = await forceSessionCheck();

      if (sessionValid) {
        console.log('[AuthManager] Sessão verificada com sucesso pelo heartbeat');
        this.isCheckingSession = false;
        return true;
      }

      console.warn('[AuthManager] Falha na verificação pelo heartbeat, tentando recuperar sessão');

      // Tentar recuperar a sessão
      const recovered = await this.recoverSession();

      if (recovered) {
        console.log('[AuthManager] Sessão recuperada com sucesso');

        // Disparar evento de reconexão
        this.dispatchReconnectEvent('session-recovery');

        this.isCheckingSession = false;
        return true;
      }

      console.warn('[AuthManager] Falha ao recuperar sessão');
      this.isCheckingSession = false;
      return false;
    } catch (error) {
      console.error('[AuthManager] Erro ao verificar sessão:', error);

      // Tentar recuperar a sessão em caso de erro não tratado
      try {
        const recovered = await this.recoverSession();
        if (recovered) {
          console.log('[AuthManager] Sessão recuperada com sucesso após erro não tratado');

          // Disparar evento de reconexão
          this.dispatchReconnectEvent('error-recovery');

          this.isCheckingSession = false;
          return true;
        }
      } catch (recoverError) {
        console.error('[AuthManager] Erro ao tentar recuperar sessão:', recoverError);
      }

      this.isCheckingSession = false;
      return false;
    }
  }

  /**
   * Atualiza a sessão
   */
  public async refreshSession(): Promise<boolean> {
    try {
      console.log('[AuthManager] Atualizando sessão');

      // Adicionar um pequeno atraso para evitar conflitos com o GoTrueClient
      await new Promise(resolve => setTimeout(resolve, 100));

      // Verificar se temos dados de sessão no localStorage antes de tentar atualizar
      let hasLocalStorageSession = false;
      let localStorageToken = null;

      try {
        const storageData = localStorage.getItem(SUPABASE_STORAGE_KEY);
        if (storageData) {
          const parsedData = JSON.parse(storageData);
          hasLocalStorageSession = !!(parsedData && parsedData.access_token);
          if (hasLocalStorageSession) {
            localStorageToken = parsedData.access_token;
          }
        }
      } catch (storageError) {
        console.warn('[AuthManager] Erro ao verificar localStorage:', storageError);
      }

      // Verificar se já temos uma sessão válida antes de tentar atualizar
      try {
        const { data: sessionData } = await supabase.auth.getSession();

        if (sessionData.session) {
          console.log('[AuthManager] Sessão atual encontrada, verificando validade');

          // Verificar se a sessão está prestes a expirar
          const expiryTime = new Date(sessionData.session.expires_at * 1000);
          const now = new Date();
          const timeToExpiry = expiryTime.getTime() - now.getTime();

          // Se a sessão ainda for válida por mais de 5 minutos, não é necessário atualizar
          if (timeToExpiry > 5 * 60 * 1000) {
            console.log(`[AuthManager] Sessão atual ainda é válida, expira em ${Math.floor(timeToExpiry / 60000)} minutos`);
            return true;
          }

          console.log('[AuthManager] Sessão existe mas está prestes a expirar, tentando atualizar');
        } else {
          console.warn('[AuthManager] Nenhuma sessão encontrada para atualizar');

          // Se não temos sessão atual mas temos dados no localStorage, tentar definir a sessão
          if (hasLocalStorageSession) {
            console.log('[AuthManager] Tentando recuperar sessão do localStorage');
            return await this.recoverSession();
          } else {
            console.warn('[AuthManager] Sem dados de sessão no localStorage, autenticação necessária');
            return false;
          }
        }
      } catch (sessionError) {
        console.error('[AuthManager] Erro ao verificar sessão atual:', sessionError);

        // Se ocorrer um erro ao verificar a sessão, tentar recuperar do localStorage
        if (hasLocalStorageSession) {
          return await this.recoverSession();
        }
      }

      // Tentar atualizar a sessão
      let sessionData = null;
      let sessionError = null;

      try {
        const { data, error } = await supabase.auth.refreshSession();
        sessionData = data;
        sessionError = error;

        if (error) {
          console.error('[AuthManager] Erro ao atualizar sessão:', error);

          // Verificar se é um erro de sessão ausente
          if (error.message && (
              error.message.includes('Auth session missing') ||
              error.message.includes('AuthSessionMissingError') ||
              error.message.includes('expired'))) {
            console.log('[AuthManager] Erro de sessão ausente, tentando recuperar do localStorage');
            return await this.recoverSession();
          }

          // Verificar se é um erro de rede
          if (error.message && (
              error.message.includes('network') ||
              error.message.includes('fetch') ||
              error.message.includes('connection'))) {
            console.log('[AuthManager] Erro de rede, verificando se temos uma sessão válida no localStorage');
            return await this.recoverSession();
          }

          return false;
        }
      } catch (refreshError) {
        console.error('[AuthManager] Erro ao chamar refreshSession:', refreshError);

        // Em caso de erro ao chamar refreshSession, tentar recuperar do localStorage
        if (hasLocalStorageSession) {
          return await this.recoverSession();
        }

        return false;
      }

      if (sessionData && sessionData.session) {
        console.log('[AuthManager] Sessão atualizada com sucesso, expira em:',
          new Date(sessionData.session.expires_at * 1000).toLocaleString());

        // Atualizar o perfil do usuário no localStorage para garantir que temos os dados mais recentes
        try {
          const userProfileStr = localStorage.getItem('userProfile');
          if (userProfileStr) {
            const userProfile = JSON.parse(userProfileStr);
            // Atualizar o timestamp de última atualização
            userProfile.last_updated = new Date().toISOString();
            localStorage.setItem('userProfile', JSON.stringify(userProfile));
          }
        } catch (profileError) {
          console.error('[AuthManager] Erro ao atualizar perfil do usuário no localStorage:', profileError);
        }

        // Disparar evento personalizado para notificar componentes sobre a atualização da sessão
        const sessionEvent = new CustomEvent('auth-session-refreshed', {
          detail: {
            timestamp: Date.now(),
            success: true,
            expiresAt: data.session.expires_at
          }
        });
        document.dispatchEvent(sessionEvent);

        return true;
      } else {
        console.warn('[AuthManager] Sessão não disponível após atualização');
        return false;
      }
    } catch (error) {
      console.error('[AuthManager] Erro ao atualizar sessão:', error);

      // Tentar recuperar a sessão em caso de erro não tratado
      try {
        return await this.recoverSession();
      } catch (recoverError) {
        console.error('[AuthManager] Erro ao tentar recuperar sessão após falha de atualização:', recoverError);
        return false;
      }
    }
  }

  /**
   * Manipula mudanças de visibilidade
   */
  private handleVisibilityChange = (): void => {
    if (!document.hidden) {
      console.log('[AuthManager] Página voltou a ficar visível, verificando sessão');

      // Usar um timeout maior para garantir que o GoTrueClient tenha tempo de processar suas próprias
      // operações de visibilidade antes de tentarmos verificar a sessão
      setTimeout(() => {
        // Verificar se a página ainda está visível antes de verificar a sessão
        if (!document.hidden) {
          this.checkSession().catch(err => {
            console.error('[AuthManager] Erro ao verificar sessão após mudança de visibilidade:', err);
          });
        }
      }, 1000);
    }
  };

  /**
   * Manipula o evento de foco da janela
   */
  private handleWindowFocus = (): void => {
    console.log('[AuthManager] Janela recebeu foco, verificando sessão');

    // Usar um timeout maior para garantir que o GoTrueClient tenha tempo de processar suas próprias
    // operações de foco antes de tentarmos verificar a sessão
    setTimeout(() => {
      // Verificar se a janela ainda tem foco antes de verificar a sessão
      if (document.hasFocus()) {
        this.checkSession().catch(err => {
          console.error('[AuthManager] Erro ao verificar sessão após foco da janela:', err);
        });
      }
    }, 1000);
  };

  /**
   * Manipula o evento de problema de sessão
   */
  private handleSessionProblem = (event: Event): void => {
    const customEvent = event as CustomEvent;
    console.warn('[AuthManager] Problema de sessão detectado:', customEvent.detail);

    // Tentar recuperar a sessão
    setTimeout(() => {
      this.checkSession().catch(err => {
        console.error('[AuthManager] Erro ao verificar sessão após problema:', err);
      });
    }, 500);
  };

  /**
   * Manipula o evento de sessão atualizada
   */
  private handleSessionRefreshed = (event: Event): void => {
    const customEvent = event as CustomEvent;
    console.log('[AuthManager] Sessão atualizada:', customEvent.detail);

    // Disparar evento de reconexão
    this.dispatchReconnectEvent('session-refreshed');
  };

  /**
   * Dispara um evento de reconexão
   */
  private dispatchReconnectEvent(source: string): void {
    const reconnectEvent = new CustomEvent('app-reconnected', {
      detail: {
        timestamp: Date.now(),
        source
      }
    });
    document.dispatchEvent(reconnectEvent);
  };

  /**
   * Recupera a sessão do localStorage
   */
  public async recoverSession(): Promise<boolean> {
    try {
      console.log('[AuthManager] Tentando recuperar sessão');

      // Estratégia 1: Verificar se já temos uma sessão válida
      try {
        const { data } = await supabase.auth.getSession();
        if (data.session) {
          console.log('[AuthManager] Sessão já existe, verificando validade');

          // Verificar se a sessão está prestes a expirar
          const expiryTime = new Date(data.session.expires_at * 1000);
          const now = new Date();
          const timeToExpiry = expiryTime.getTime() - now.getTime();

          // Se a sessão ainda for válida por mais de 5 minutos, não é necessário atualizar
          if (timeToExpiry > 5 * 60 * 1000) {
            console.log(`[AuthManager] Sessão atual ainda é válida, expira em ${Math.floor(timeToExpiry / 60000)} minutos`);

            // Disparar evento de reconexão
            this.dispatchReconnectEvent('valid-session-found');

            return true;
          }

          console.log('[AuthManager] Sessão existe mas está prestes a expirar, tentando atualizar');
          const refreshResult = await this.refreshSession();
          if (refreshResult) {
            return true;
          }
        }
      } catch (sessionCheckError) {
        console.error('[AuthManager] Erro ao verificar sessão atual:', sessionCheckError);
      }

      // Estratégia 2: Verificar se temos dados de sessão no localStorage
      let sessionFromStorage = null;

      try {
        // Primeiro, tentar obter do localStorage
        const storedData = localStorage.getItem(SUPABASE_STORAGE_KEY);
        if (storedData) {
          const parsedData = JSON.parse(storedData);
          if (parsedData && parsedData.access_token) {
            sessionFromStorage = {
              access_token: parsedData.access_token,
              refresh_token: parsedData.refresh_token || '',
              expires_at: parsedData.expires_at,
              expires_in: parsedData.expires_in || 3600,
              token_type: 'bearer'
            };
            console.log('[AuthManager] Dados de sessão encontrados no localStorage');
          }
        }

        // Se não encontrou no localStorage, tentar no sessionStorage
        if (!sessionFromStorage) {
          const sessionData = sessionStorage.getItem(SUPABASE_STORAGE_KEY);
          if (sessionData) {
            const parsedData = JSON.parse(sessionData);
            if (parsedData && parsedData.access_token) {
              sessionFromStorage = {
                access_token: parsedData.access_token,
                refresh_token: parsedData.refresh_token || '',
                expires_at: parsedData.expires_at,
                expires_in: parsedData.expires_in || 3600,
                token_type: 'bearer'
              };
              console.log('[AuthManager] Dados de sessão encontrados no sessionStorage');

              // Sincronizar com localStorage para persistência
              localStorage.setItem(SUPABASE_STORAGE_KEY, sessionData);
            }
          }
        }
      } catch (storageError) {
        console.error('[AuthManager] Erro ao acessar storage:', storageError);
      }

      // Se encontramos dados de sessão no storage, tentar definir a sessão
      if (sessionFromStorage) {
        try {
          console.log('[AuthManager] Tentando definir sessão a partir do storage');

          // Adicionar um pequeno atraso para evitar conflitos com o GoTrueClient
          await new Promise(resolve => setTimeout(resolve, 200));

          const { data: setData, error: setError } = await supabase.auth.setSession(sessionFromStorage);

          if (setError) {
            console.error('[AuthManager] Erro ao definir sessão a partir do storage:', setError);
          } else if (setData.session) {
            console.log('[AuthManager] Sessão definida com sucesso a partir do storage');

            // Disparar evento personalizado para notificar componentes sobre a atualização da sessão
            const sessionEvent = new CustomEvent('auth-session-recovered', {
              detail: {
                timestamp: Date.now(),
                success: true,
                source: 'storage'
              }
            });
            document.dispatchEvent(sessionEvent);

            // Disparar evento de reconexão
            this.dispatchReconnectEvent('session-recovered-from-storage');

            return true;
          }
        } catch (setSessionError) {
          console.error('[AuthManager] Erro ao definir sessão:', setSessionError);
        }
      }

      // Estratégia 3: Tentar atualizar a sessão diretamente
      try {
        console.log('[AuthManager] Tentando atualizar sessão diretamente');

        // Adicionar um pequeno atraso para evitar conflitos com o GoTrueClient
        await new Promise(resolve => setTimeout(resolve, 200));

        // Verificar se temos dados de sessão no localStorage antes de tentar atualizar
        let hasLocalStorageSession = false;
        try {
          const storageData = localStorage.getItem(SUPABASE_STORAGE_KEY);
          if (storageData) {
            const parsedData = JSON.parse(storageData);
            hasLocalStorageSession = !!(parsedData && parsedData.access_token);
          }
        } catch (storageError) {
          console.warn('[AuthManager] Erro ao verificar localStorage:', storageError);
        }

        // Se não temos dados no localStorage, não adianta tentar refreshSession
        if (!hasLocalStorageSession) {
          console.warn('[AuthManager] Sem dados de sessão no localStorage, pulando refreshSession');
        } else {
          // Tentar atualizar a sessão
          const { data: refreshData, error: refreshError } = await supabase.auth.refreshSession();

          if (refreshError) {
            console.error('[AuthManager] Erro ao atualizar sessão diretamente:', refreshError);

            // Verificar se é um erro de sessão ausente
            if (refreshError.message && refreshError.message.includes('Auth session missing')) {
              console.warn('[AuthManager] Erro de sessão ausente, tentando recuperar de outra forma');

              // Tentar obter a sessão atual primeiro
              const { data: currentSession } = await supabase.auth.getSession();
              if (currentSession && currentSession.session) {
                console.log('[AuthManager] Sessão atual encontrada, usando-a');

                // Remover marcação de erro de autenticação
                document.body.removeAttribute('data-auth-error');

                // Disparar evento de reconexão
                this.dispatchReconnectEvent('current-session-used');

                return true;
              }
            } else {
              // Marcar o documento com atributo de erro de autenticação
              document.body.setAttribute('data-auth-error', 'true');
            }
          } else if (refreshData && refreshData.session) {
            // Remover marcação de erro de autenticação
            document.body.removeAttribute('data-auth-error');
            console.log('[AuthManager] Sessão atualizada com sucesso diretamente');

            // Disparar evento personalizado para notificar componentes sobre a atualização da sessão
            const sessionEvent = new CustomEvent('auth-session-recovered', {
              detail: {
                timestamp: Date.now(),
                success: true,
                source: 'direct-refresh'
              }
            });
            document.dispatchEvent(sessionEvent);

            // Disparar evento de reconexão
            this.dispatchReconnectEvent('session-refreshed-directly');

            return true;
          }
        }
      } catch (refreshError) {
        console.error('[AuthManager] Erro ao atualizar sessão diretamente:', refreshError);
      }

      // Estratégia 4: Se todas as tentativas falharam, verificar se temos um perfil de usuário no localStorage
      try {
        const userProfileStr = localStorage.getItem('userProfile');
        if (userProfileStr) {
          console.log('[AuthManager] Perfil de usuário encontrado no localStorage, permitindo acesso limitado');
          // Remover marcação de erro de autenticação
          document.body.removeAttribute('data-auth-error');

          // Disparar evento personalizado para notificar componentes sobre o acesso limitado
          const sessionEvent = new CustomEvent('auth-session-limited', {
            detail: {
              timestamp: Date.now(),
              limitedAccess: true
            }
          });
          document.dispatchEvent(sessionEvent);

          // Tentar iniciar uma nova sessão anônima em segundo plano
          try {
            console.log('[AuthManager] Tentando criar sessão anônima para acesso limitado');
            await supabase.auth.signInAnonymously();
          } catch (anonError) {
            console.error('[AuthManager] Erro ao criar sessão anônima:', anonError);
          }

          return true;
        }
      } catch (profileError) {
        console.error('[AuthManager] Erro ao acessar perfil do usuário:', profileError);
      }

      console.warn('[AuthManager] Falha ao recuperar sessão após todas as tentativas');
      // Marcar o documento com atributo de erro de autenticação
      document.body.setAttribute('data-auth-error', 'true');
      return false;
    } catch (error) {
      console.error('[AuthManager] Erro ao recuperar sessão:', error);
      // Marcar o documento com atributo de erro de autenticação
      document.body.setAttribute('data-auth-error', 'true');
      return false;
    }
  }
}

// Exportar uma instância única
export const authManager = new AuthManager();
