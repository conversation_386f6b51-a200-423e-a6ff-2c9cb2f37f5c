/**
 * Centralized logging utility for the application
 * Only logs in development mode to prevent console pollution in production
 */

type LogLevel = 'debug' | 'info' | 'warn' | 'error';

interface LogEntry {
  level: LogLevel;
  message: string;
  data?: unknown;
  timestamp: string;
  context?: string;
}

class Logger {
  private isDevelopment: boolean;
  private logHistory: LogEntry[] = [];
  private maxHistorySize = 1000;

  constructor() {
    this.isDevelopment = import.meta.env.DEV;
  }

  private createLogEntry(level: LogLevel, message: string, data?: unknown, context?: string): LogEntry {
    return {
      level,
      message,
      data,
      timestamp: new Date().toISOString(),
      context
    };
  }

  private addToHistory(entry: LogEntry): void {
    this.logHistory.push(entry);
    if (this.logHistory.length > this.maxHistorySize) {
      this.logHistory.shift();
    }
  }

  private formatMessage(level: LogLevel, message: string, context?: string): string {
    const prefix = context ? `[${context}]` : '';
    return `${prefix} ${message}`;
  }

  debug(message: string, data?: unknown, context?: string): void {
    const entry = this.createLogEntry('debug', message, data, context);
    this.addToHistory(entry);
    
    if (this.isDevelopment) {
      const formattedMessage = this.formatMessage('debug', message, context);
      if (data !== undefined) {
        console.debug(formattedMessage, data);
      } else {
        console.debug(formattedMessage);
      }
    }
  }

  info(message: string, data?: unknown, context?: string): void {
    const entry = this.createLogEntry('info', message, data, context);
    this.addToHistory(entry);
    
    if (this.isDevelopment) {
      const formattedMessage = this.formatMessage('info', message, context);
      if (data !== undefined) {
        console.info(formattedMessage, data);
      } else {
        console.info(formattedMessage);
      }
    }
  }

  warn(message: string, data?: unknown, context?: string): void {
    const entry = this.createLogEntry('warn', message, data, context);
    this.addToHistory(entry);
    
    if (this.isDevelopment) {
      const formattedMessage = this.formatMessage('warn', message, context);
      if (data !== undefined) {
        console.warn(formattedMessage, data);
      } else {
        console.warn(formattedMessage);
      }
    }
  }

  error(message: string, data?: unknown, context?: string): void {
    const entry = this.createLogEntry('error', message, data, context);
    this.addToHistory(entry);
    
    // Always log errors, even in production, but with minimal information
    const formattedMessage = this.formatMessage('error', message, context);
    if (this.isDevelopment) {
      if (data !== undefined) {
        console.error(formattedMessage, data);
      } else {
        console.error(formattedMessage);
      }
    } else {
      // In production, only log the message without sensitive data
      console.error(formattedMessage);
    }
  }

  /**
   * Get recent log history (useful for debugging)
   */
  getHistory(level?: LogLevel): LogEntry[] {
    if (level) {
      return this.logHistory.filter(entry => entry.level === level);
    }
    return [...this.logHistory];
  }

  /**
   * Clear log history
   */
  clearHistory(): void {
    this.logHistory = [];
  }

  /**
   * Create a context-specific logger
   */
  createContextLogger(context: string) {
    return {
      debug: (message: string, data?: unknown) => this.debug(message, data, context),
      info: (message: string, data?: unknown) => this.info(message, data, context),
      warn: (message: string, data?: unknown) => this.warn(message, data, context),
      error: (message: string, data?: unknown) => this.error(message, data, context),
    };
  }
}

// Export singleton instance
export const logger = new Logger();

// Export context-specific loggers for common modules
export const authLogger = logger.createContextLogger('Auth');
export const supabaseLogger = logger.createContextLogger('Supabase');
export const kanbanLogger = logger.createContextLogger('Kanban');
export const precatoriosLogger = logger.createContextLogger('Precatorios');
export const permissionsLogger = logger.createContextLogger('Permissions');
export const cacheLogger = logger.createContextLogger('Cache');

// Export the Logger class for custom instances if needed
export { Logger };
export type { LogLevel, LogEntry };
