/**
 * Tab Session Manager
 * 
 * This module provides enhanced session management for browser tab switching.
 * It ensures that the application maintains its authenticated state when users
 * switch between browser tabs and return to the application.
 */

import { supabase, refreshSupabaseSession, getUserProfile, saveUserProfile, syncUserProfile } from './supabase';
import { authManager } from './authManager';

// Configuration
const TAB_SYNC_INTERVAL = 3000; // 3 seconds
const SESSION_CHECK_INTERVAL = 30000; // 30 seconds
const SESSION_RECOVERY_ATTEMPTS = 3;
const VISIBILITY_CHANGE_DELAY = 500; // 500ms delay after visibility change

// State
let isActive = false;
let tabSyncIntervalId: number | null = null;
let sessionCheckIntervalId: number | null = null;
let lastTabId = '';
let currentTabId = '';
let recoveryAttempts = 0;
let lastVisibilityChange = 0;
let isProcessingVisibilityChange = false;

// Generate a unique ID for this tab
currentTabId = `tab_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

/**
 * Initialize the tab session manager
 */
export function initTabSessionManager(): void {
  if (isActive) {
    console.log('[TabSessionManager] Already initialized');
    return;
  }

  console.log('[TabSessionManager] Initializing with tab ID:', currentTabId);
  isActive = true;

  // Store the current tab ID in sessionStorage
  try {
    sessionStorage.setItem('current_tab_id', currentTabId);
    localStorage.setItem('last_active_tab', currentTabId);
  } catch (error) {
    console.error('[TabSessionManager] Error storing tab ID:', error);
  }

  // Set up event listeners
  setupEventListeners();

  // Start intervals
  startIntervals();

  // Perform initial session check
  checkSession();
}

/**
 * Set up event listeners for tab visibility and focus
 */
function setupEventListeners(): void {
  // Visibility change event
  document.addEventListener('visibilitychange', handleVisibilityChange);
  
  // Window focus event
  window.addEventListener('focus', handleWindowFocus);
  
  // Before unload event to clean up
  window.addEventListener('beforeunload', handleBeforeUnload);
  
  // Storage event to detect changes from other tabs
  window.addEventListener('storage', handleStorageEvent);
  
  // Custom events
  document.addEventListener('app-reconnected', handleReconnect);
  document.addEventListener('supabase-session-refreshed', handleSessionRefreshed);
}

/**
 * Start the interval timers
 */
function startIntervals(): void {
  // Clear any existing intervals
  stopIntervals();
  
  // Tab sync interval - keeps track of the active tab
  tabSyncIntervalId = window.setInterval(syncTabState, TAB_SYNC_INTERVAL);
  
  // Session check interval - periodically checks the session
  sessionCheckIntervalId = window.setInterval(checkSession, SESSION_CHECK_INTERVAL);
}

/**
 * Stop the interval timers
 */
function stopIntervals(): void {
  if (tabSyncIntervalId !== null) {
    window.clearInterval(tabSyncIntervalId);
    tabSyncIntervalId = null;
  }
  
  if (sessionCheckIntervalId !== null) {
    window.clearInterval(sessionCheckIntervalId);
    sessionCheckIntervalId = null;
  }
}

/**
 * Handle visibility change events
 */
async function handleVisibilityChange(): Promise<void> {
  const now = Date.now();
  const timeSinceLastChange = now - lastVisibilityChange;
  
  // Prevent rapid firing of visibility change events
  if (timeSinceLastChange < 1000) {
    console.log('[TabSessionManager] Ignoring rapid visibility change');
    return;
  }
  
  lastVisibilityChange = now;
  
  // If already processing a visibility change, don't start another one
  if (isProcessingVisibilityChange) {
    console.log('[TabSessionManager] Already processing visibility change, skipping');
    return;
  }
  
  isProcessingVisibilityChange = true;
  
  try {
    if (document.visibilityState === 'visible') {
      console.log('[TabSessionManager] Tab became visible');
      
      // Update the last active tab
      localStorage.setItem('last_active_tab', currentTabId);
      
      // Add a small delay to allow the browser to stabilize
      await new Promise(resolve => setTimeout(resolve, VISIBILITY_CHANGE_DELAY));
      
      // Check if we're still visible after the delay
      if (document.visibilityState === 'visible') {
        // Force a session check when becoming visible
        await forceSessionCheck();
      }
    } else {
      console.log('[TabSessionManager] Tab became hidden');
      
      // Sync user profile before tab becomes hidden
      syncUserProfile();
    }
  } catch (error) {
    console.error('[TabSessionManager] Error handling visibility change:', error);
  } finally {
    isProcessingVisibilityChange = false;
  }
}

/**
 * Handle window focus events
 */
async function handleWindowFocus(): Promise<void> {
  console.log('[TabSessionManager] Window received focus');
  
  // Update the last active tab
  localStorage.setItem('last_active_tab', currentTabId);
  
  // If the document is visible, force a session check
  if (document.visibilityState === 'visible' && !isProcessingVisibilityChange) {
    // Add a small delay to allow the browser to stabilize
    await new Promise(resolve => setTimeout(resolve, VISIBILITY_CHANGE_DELAY));
    
    // Check if we're still focused after the delay
    if (document.hasFocus() && document.visibilityState === 'visible') {
      await forceSessionCheck();
    }
  }
}

/**
 * Handle beforeunload event
 */
function handleBeforeUnload(): void {
  console.log('[TabSessionManager] Tab is being closed');
  
  // Clean up
  stopIntervals();
  
  // Try to sync user profile before unloading
  try {
    syncUserProfile();
  } catch (error) {
    console.error('[TabSessionManager] Error syncing user profile before unload:', error);
  }
}

/**
 * Handle storage events from other tabs
 */
function handleStorageEvent(event: StorageEvent): void {
  if (event.key === 'last_active_tab') {
    lastTabId = event.newValue || '';
    console.log('[TabSessionManager] Active tab changed to:', lastTabId);
  }
}

/**
 * Handle reconnect events
 */
function handleReconnect(): void {
  console.log('[TabSessionManager] Reconnect event received');
  
  // Reset recovery attempts
  recoveryAttempts = 0;
  
  // Sync user profile
  syncUserProfile();
}

/**
 * Handle session refreshed events
 */
function handleSessionRefreshed(): void {
  console.log('[TabSessionManager] Session refreshed event received');
  
  // Reset recovery attempts
  recoveryAttempts = 0;
  
  // Sync user profile
  syncUserProfile();
}

/**
 * Sync tab state with localStorage
 */
function syncTabState(): void {
  try {
    // Update timestamp for this tab
    sessionStorage.setItem('tab_last_active', Date.now().toString());
    
    // Check if this is the active tab
    const activeTabId = localStorage.getItem('last_active_tab');
    
    if (activeTabId === currentTabId) {
      // This is the active tab, update the timestamp
      localStorage.setItem('active_tab_timestamp', Date.now().toString());
    }
  } catch (error) {
    console.error('[TabSessionManager] Error syncing tab state:', error);
  }
}

/**
 * Check the current session
 */
async function checkSession(): Promise<boolean> {
  try {
    console.log('[TabSessionManager] Checking session');
    
    // Get the current session
    const { data } = await supabase.auth.getSession();
    
    if (data.session) {
      console.log('[TabSessionManager] Session is valid');
      
      // Reset recovery attempts
      recoveryAttempts = 0;
      
      return true;
    } else {
      console.warn('[TabSessionManager] No session found, attempting recovery');
      return await recoverSession();
    }
  } catch (error) {
    console.error('[TabSessionManager] Error checking session:', error);
    return await recoverSession();
  }
}

/**
 * Force a session check
 */
export async function forceSessionCheck(): Promise<boolean> {
  console.log('[TabSessionManager] Forcing session check');
  
  // Try to refresh the session first
  const refreshed = await refreshSupabaseSession();
  
  if (refreshed) {
    console.log('[TabSessionManager] Session refreshed successfully');
    
    // Reset recovery attempts
    recoveryAttempts = 0;
    
    // Sync user profile
    syncUserProfile();
    
    return true;
  }
  
  // If refresh failed, try to recover the session
  return await recoverSession();
}

/**
 * Attempt to recover the session
 */
async function recoverSession(): Promise<boolean> {
  if (recoveryAttempts >= SESSION_RECOVERY_ATTEMPTS) {
    console.error('[TabSessionManager] Maximum recovery attempts reached');
    return false;
  }
  
  recoveryAttempts++;
  console.log(`[TabSessionManager] Attempting session recovery (${recoveryAttempts}/${SESSION_RECOVERY_ATTEMPTS})`);
  
  try {
    // Try to recover using authManager
    const recovered = await authManager.recoverSession();
    
    if (recovered) {
      console.log('[TabSessionManager] Session recovered successfully');
      
      // Reset recovery attempts
      recoveryAttempts = 0;
      
      // Sync user profile
      syncUserProfile();
      
      return true;
    }
    
    // If authManager failed, try to recover using the user profile
    const userProfile = getUserProfile();
    
    if (userProfile) {
      console.log('[TabSessionManager] Found user profile, attempting to use it for recovery');
      
      // Try to refresh the session again
      const refreshed = await refreshSupabaseSession();
      
      if (refreshed) {
        console.log('[TabSessionManager] Session refreshed successfully using profile');
        
        // Reset recovery attempts
        recoveryAttempts = 0;
        
        return true;
      }
    }
    
    console.warn('[TabSessionManager] Session recovery failed');
    return false;
  } catch (error) {
    console.error('[TabSessionManager] Error recovering session:', error);
    return false;
  }
}
