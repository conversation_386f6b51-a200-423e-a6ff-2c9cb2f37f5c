import { supabase } from './supabase';

/**
 * Configurações do serviço de keep-alive
 */
const KEEP_ALIVE_INTERVAL = 4 * 60 * 1000; // 4 minutos (token expira em 60 minutos)
const RETRY_DELAY = 10000; // 10 segundos

/**
 * Estado interno
 */
let keepAliveTimer: number | null = null;
let isRunning = false;

/**
 * Inicia o serviço de keep-alive para manter a sessão ativa
 * Este serviço envia um ping periódico para o servidor para evitar que a sessão expire
 */
export function startKeepAlive() {
  if (isRunning) {
    console.log('[KeepAlive] Serviço já está em execução');
    return;
  }

  console.log('[KeepAlive] Iniciando serviço de keep-alive');
  isRunning = true;

  // Limpar qualquer timer existente
  if (keepAliveTimer) {
    clearInterval(keepAliveTimer);
  }

  // Verificar a sessão imediatamente
  pingSession();

  // Configurar verificação periódica
  keepAliveTimer = window.setInterval(pingSession, KEEP_ALIVE_INTERVAL);

  return {
    stop: stopKeepAlive,
    ping: pingSession,
    refresh: refreshSession
  };
}

/**
 * Para o serviço de keep-alive
 */
export function stopKeepAlive() {
  console.log('[KeepAlive] Parando serviço de keep-alive');
  isRunning = false;

  // Limpar timer
  if (keepAliveTimer) {
    clearInterval(keepAliveTimer);
    keepAliveTimer = null;
  }
}

/**
 * Envia um ping para o servidor para manter a sessão ativa
 */
async function pingSession() {
  try {
    console.log('[KeepAlive] Verificando sessão');

    // Verificar se temos uma sessão
    const { data, error } = await supabase.auth.getSession();

    if (error) {
      console.error('[KeepAlive] Erro ao verificar sessão:', error);
      return;
    }

    if (!data.session) {
      console.warn('[KeepAlive] Nenhuma sessão encontrada');
      return;
    }

    // Verificar se a sessão está prestes a expirar
    const expiryTime = new Date(data.session.expires_at * 1000);
    const now = new Date();
    const timeToExpiry = expiryTime.getTime() - now.getTime();
    const minutesToExpiry = Math.floor(timeToExpiry / 60000);

    console.log(`[KeepAlive] Sessão válida, expira em ${minutesToExpiry} minutos`);

    // Se estiver prestes a expirar (menos de 10 minutos), atualizar
    if (timeToExpiry < 10 * 60 * 1000) {
      console.log('[KeepAlive] Sessão prestes a expirar, atualizando');
      await refreshSession();
    }
  } catch (error) {
    console.error('[KeepAlive] Erro ao enviar ping:', error);
  }
}

/**
 * Atualiza a sessão
 */
async function refreshSession() {
  try {
    console.log('[KeepAlive] Atualizando sessão');
    const { data, error } = await supabase.auth.refreshSession();

    if (error) {
      console.error('[KeepAlive] Erro ao atualizar sessão:', error);
      return false;
    }

    if (data.session) {
      console.log('[KeepAlive] Sessão atualizada com sucesso, expira em:',
        new Date(data.session.expires_at * 1000).toLocaleString());
      return true;
    } else {
      console.warn('[KeepAlive] Sessão não disponível após atualização');
      return false;
    }
  } catch (error) {
    console.error('[KeepAlive] Erro ao atualizar sessão:', error);
    return false;
  }
}

// Exportar funções úteis
export const KeepAlive = {
  start: startKeepAlive,
  stop: stopKeepAlive,
  ping: pingSession,
  refresh: refreshSession
};
