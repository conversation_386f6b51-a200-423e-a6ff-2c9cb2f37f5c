/**
 * Sistema de Persistência de Sessão
 * 
 * Este módulo implementa um sistema robusto para garantir que a sessão do usuário
 * seja mantida mesmo quando o usuário alterna entre abas do navegador.
 */

import { supabase } from './supabase';

// Chaves para armazenamento
const SESSION_KEY = 'app-session-data';
const SESSION_TIMESTAMP_KEY = 'app-session-timestamp';
const SESSION_BACKUP_KEY = 'app-session-backup';
const HEARTBEAT_TIMESTAMP_KEY = 'app-heartbeat-timestamp';
const TAB_ID_KEY = 'app-tab-id';
const ACTIVE_TAB_KEY = 'app-active-tab';

// Configurações
const HEARTBEAT_INTERVAL = 10000; // 10 segundos
const SESSION_CHECK_INTERVAL = 30000; // 30 segundos
const SESSION_EXPIRY = 24 * 60 * 60 * 1000; // 24 horas

// Estado
let isInitialized = false;
let heartbeatIntervalId: number | null = null;
let sessionCheckIntervalId: number | null = null;
let currentTabId = `tab_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
let isActive = true;

/**
 * Inicializa o sistema de persistência de sessão
 */
export function initSessionPersistence(): void {
  if (isInitialized) {
    console.log('[SessionPersistence] Já inicializado');
    return;
  }

  console.log('[SessionPersistence] Inicializando sistema de persistência de sessão');
  isInitialized = true;

  // Registrar ID da aba atual
  try {
    localStorage.setItem(TAB_ID_KEY, currentTabId);
    sessionStorage.setItem(TAB_ID_KEY, currentTabId);
    localStorage.setItem(ACTIVE_TAB_KEY, currentTabId);
  } catch (error) {
    console.error('[SessionPersistence] Erro ao registrar ID da aba:', error);
  }

  // Configurar listeners de eventos
  setupEventListeners();

  // Iniciar intervalos
  startIntervals();

  // Verificar sessão imediatamente
  checkAndPersistSession();
}

/**
 * Configura os listeners de eventos
 */
function setupEventListeners(): void {
  // Evento de visibilidade
  document.addEventListener('visibilitychange', handleVisibilityChange);

  // Evento de foco
  window.addEventListener('focus', handleWindowFocus);

  // Evento de storage (para comunicação entre abas)
  window.addEventListener('storage', handleStorageEvent);

  // Evento de beforeunload
  window.addEventListener('beforeunload', handleBeforeUnload);
}

/**
 * Inicia os intervalos de verificação
 */
function startIntervals(): void {
  // Parar intervalos existentes
  stopIntervals();

  // Iniciar intervalo de heartbeat
  heartbeatIntervalId = window.setInterval(sendHeartbeat, HEARTBEAT_INTERVAL);

  // Iniciar intervalo de verificação de sessão
  sessionCheckIntervalId = window.setInterval(checkAndPersistSession, SESSION_CHECK_INTERVAL);
}

/**
 * Para os intervalos de verificação
 */
function stopIntervals(): void {
  if (heartbeatIntervalId !== null) {
    window.clearInterval(heartbeatIntervalId);
    heartbeatIntervalId = null;
  }

  if (sessionCheckIntervalId !== null) {
    window.clearInterval(sessionCheckIntervalId);
    sessionCheckIntervalId = null;
  }
}

/**
 * Manipula eventos de mudança de visibilidade
 */
function handleVisibilityChange(): void {
  const isVisible = document.visibilityState === 'visible';
  console.log(`[SessionPersistence] Visibilidade alterada: ${isVisible ? 'visível' : 'oculta'}`);

  if (isVisible) {
    // Aba se tornou visível
    isActive = true;
    localStorage.setItem(ACTIVE_TAB_KEY, currentTabId);

    // Verificar sessão imediatamente
    checkAndPersistSession();

    // Reiniciar intervalos
    startIntervals();
  } else {
    // Aba se tornou oculta
    isActive = false;

    // Persistir sessão antes de ficar oculta
    persistSession();
  }
}

/**
 * Manipula eventos de foco da janela
 */
function handleWindowFocus(): void {
  console.log('[SessionPersistence] Janela recebeu foco');
  
  // Marcar esta aba como ativa
  isActive = true;
  localStorage.setItem(ACTIVE_TAB_KEY, currentTabId);

  // Verificar sessão imediatamente
  checkAndPersistSession();
}

/**
 * Manipula eventos de storage (comunicação entre abas)
 */
function handleStorageEvent(event: StorageEvent): void {
  if (event.key === ACTIVE_TAB_KEY) {
    // Outra aba se tornou ativa
    isActive = event.newValue === currentTabId;
    console.log(`[SessionPersistence] Esta aba ${isActive ? 'é' : 'não é'} a ativa agora`);
  }
}

/**
 * Manipula evento de beforeunload
 */
function handleBeforeUnload(): void {
  console.log('[SessionPersistence] Aba está sendo fechada');
  
  // Persistir sessão antes de fechar
  persistSession();
  
  // Parar intervalos
  stopIntervals();
}

/**
 * Envia um heartbeat para manter a sessão ativa
 */
async function sendHeartbeat(): Promise<void> {
  try {
    // Registrar timestamp do heartbeat
    const now = Date.now();
    localStorage.setItem(HEARTBEAT_TIMESTAMP_KEY, now.toString());
    
    // Se esta é a aba ativa, fazer uma requisição leve para manter a conexão
    if (isActive) {
      console.log('[SessionPersistence] Enviando heartbeat');
      
      // Fazer uma requisição leve para manter a conexão
      const { error } = await supabase
        .from('profiles')
        .select('id')
        .limit(1);
      
      if (error) {
        console.warn('[SessionPersistence] Erro no heartbeat:', error);
        
        // Tentar recuperar a sessão
        checkAndPersistSession();
      }
    }
  } catch (error) {
    console.error('[SessionPersistence] Erro ao enviar heartbeat:', error);
  }
}

/**
 * Verifica e persiste a sessão atual
 */
export async function checkAndPersistSession(): Promise<boolean> {
  try {
    console.log('[SessionPersistence] Verificando sessão');
    
    // Verificar se temos uma sessão válida
    const { data, error } = await supabase.auth.getSession();
    
    if (error) {
      console.error('[SessionPersistence] Erro ao verificar sessão:', error);
      return false;
    }
    
    if (!data.session) {
      console.warn('[SessionPersistence] Nenhuma sessão encontrada');
      
      // Tentar recuperar a sessão do armazenamento
      const recovered = await recoverSessionFromStorage();
      
      if (recovered) {
        console.log('[SessionPersistence] Sessão recuperada do armazenamento');
        return true;
      }
      
      return false;
    }
    
    // Temos uma sessão válida, persistir
    persistSession(data.session);
    return true;
  } catch (error) {
    console.error('[SessionPersistence] Erro ao verificar sessão:', error);
    return false;
  }
}

/**
 * Persiste a sessão atual em múltiplos locais
 */
function persistSession(session?: any): void {
  try {
    // Se não foi fornecida uma sessão, tentar obter do Supabase
    if (!session) {
      const sessionStr = localStorage.getItem(SESSION_KEY);
      if (!sessionStr) {
        console.warn('[SessionPersistence] Nenhuma sessão para persistir');
        return;
      }
      
      try {
        session = JSON.parse(sessionStr);
      } catch (e) {
        console.error('[SessionPersistence] Erro ao analisar sessão:', e);
        return;
      }
    }
    
    // Persistir a sessão em múltiplos locais
    const sessionStr = JSON.stringify(session);
    const now = Date.now();
    
    // Armazenar em localStorage
    localStorage.setItem(SESSION_KEY, sessionStr);
    localStorage.setItem(SESSION_TIMESTAMP_KEY, now.toString());
    
    // Armazenar em sessionStorage
    sessionStorage.setItem(SESSION_KEY, sessionStr);
    sessionStorage.setItem(SESSION_TIMESTAMP_KEY, now.toString());
    
    // Armazenar backup
    localStorage.setItem(SESSION_BACKUP_KEY, sessionStr);
    
    console.log('[SessionPersistence] Sessão persistida com sucesso');
  } catch (error) {
    console.error('[SessionPersistence] Erro ao persistir sessão:', error);
  }
}

/**
 * Tenta recuperar a sessão do armazenamento
 */
async function recoverSessionFromStorage(): Promise<boolean> {
  try {
    console.log('[SessionPersistence] Tentando recuperar sessão do armazenamento');
    
    // Verificar se temos uma sessão armazenada
    const sessionStr = localStorage.getItem(SESSION_KEY) || 
                       sessionStorage.getItem(SESSION_KEY) || 
                       localStorage.getItem(SESSION_BACKUP_KEY);
    
    if (!sessionStr) {
      console.warn('[SessionPersistence] Nenhuma sessão armazenada encontrada');
      return false;
    }
    
    // Verificar se a sessão não está expirada
    const timestampStr = localStorage.getItem(SESSION_TIMESTAMP_KEY) || 
                         sessionStorage.getItem(SESSION_TIMESTAMP_KEY);
    
    if (timestampStr) {
      const timestamp = parseInt(timestampStr, 10);
      const now = Date.now();
      
      if (now - timestamp > SESSION_EXPIRY) {
        console.warn('[SessionPersistence] Sessão armazenada expirada');
        return false;
      }
    }
    
    // Tentar definir a sessão no Supabase
    try {
      const session = JSON.parse(sessionStr);
      
      // Tentar atualizar a sessão
      const { error } = await supabase.auth.setSession(session);
      
      if (error) {
        console.error('[SessionPersistence] Erro ao definir sessão:', error);
        return false;
      }
      
      console.log('[SessionPersistence] Sessão recuperada com sucesso');
      return true;
    } catch (e) {
      console.error('[SessionPersistence] Erro ao analisar ou definir sessão:', e);
      return false;
    }
  } catch (error) {
    console.error('[SessionPersistence] Erro ao recuperar sessão:', error);
    return false;
  }
}

/**
 * Limpa todos os dados de sessão
 */
export function clearSessionData(): void {
  try {
    // Limpar localStorage
    localStorage.removeItem(SESSION_KEY);
    localStorage.removeItem(SESSION_TIMESTAMP_KEY);
    localStorage.removeItem(SESSION_BACKUP_KEY);
    localStorage.removeItem(HEARTBEAT_TIMESTAMP_KEY);
    
    // Limpar sessionStorage
    sessionStorage.removeItem(SESSION_KEY);
    sessionStorage.removeItem(SESSION_TIMESTAMP_KEY);
    
    console.log('[SessionPersistence] Dados de sessão limpos');
  } catch (error) {
    console.error('[SessionPersistence] Erro ao limpar dados de sessão:', error);
  }
}
