import { supabase } from './supabase';

/**
 * Verifica se o erro está relacionado a uma sessão de autenticação ausente
 */
export function isAuthSessionMissingError(error: any): boolean {
  if (!error) return false;

  const errorMessage = (typeof error === 'string' ? error : error.message) || '';
  return (
    errorMessage.includes('Auth session missing') ||
    errorMessage.includes('AuthSessionMissingError') ||
    errorMessage.includes('expired')
  );
}

/**
 * Tenta recuperar dados da sessão do localStorage
 */
export function recoverSessionFromLocalStorage() {
  try {
    // Tentar recuperar token do localStorage
    const tokenStr = localStorage.getItem('sb-ubwzukpsqcrwzfbppoux-auth-token');
    if (!tokenStr) return null;

    const tokenData = JSON.parse(tokenStr);
    if (!tokenData || !tokenData.access_token) return null;

    return {
      access_token: tokenData.access_token,
      refresh_token: tokenData.refresh_token || ''
    };
  } catch (error) {
    console.error('Erro ao recuperar sessão do localStorage:', error);
    return null;
  }
}

/**
 * Garante que haja uma sessão válida antes de realizar operações autenticadas
 * Implementação melhorada com tratamento de erros mais robusto
 */
export async function ensureValidSession() {
  try {
    console.log('auth-helpers: Verificando sessão...');

    // Verificar se temos um perfil no localStorage primeiro
    const userProfileStr = localStorage.getItem("userProfile");
    let userProfile = null;

    if (userProfileStr) {
      try {
        userProfile = JSON.parse(userProfileStr);
        console.log('auth-helpers: Perfil encontrado no localStorage:', userProfile.email);
      } catch (parseError) {
        console.error('auth-helpers: Erro ao analisar perfil do localStorage:', parseError);
      }
    }

    // Verificar se já existe uma sessão
    const { data } = await supabase.auth.getSession();

    if (data.session) {
      // Verificar se a sessão ainda é válida
      const expiryTime = new Date(data.session.expires_at * 1000);
      const now = new Date();
      const timeToExpiry = expiryTime.getTime() - now.getTime();

      // Se a sessão estiver a mais de 5 minutos de expirar, ela ainda é válida
      if (timeToExpiry > 5 * 60 * 1000) {
        console.log(`auth-helpers: Sessão válida encontrada, expira em ${Math.floor(timeToExpiry / 60000)} minutos`);
        return { success: true, session: data.session, userProfile };
      }

      console.log('auth-helpers: Sessão encontrada, mas está prestes a expirar. Renovando...');
    } else {
      console.log('auth-helpers: Nenhuma sessão ativa encontrada. Tentando renovar...');
    }

    // Se não há sessão ou está prestes a expirar, tentar renovar
    try {
      // Adicionar um pequeno atraso para evitar conflitos com o GoTrueClient
      await new Promise(resolve => setTimeout(resolve, 100));

      const { data: refreshData, error: refreshError } = await supabase.auth.refreshSession();

      if (refreshError) {
        console.warn('auth-helpers: Erro ao renovar sessão:', refreshError.message);

        // Se for um erro de sessão ausente, tentar recuperar do localStorage
        if (isAuthSessionMissingError(refreshError)) {
          console.log('auth-helpers: Tentando recuperar sessão do localStorage...');
          const sessionData = recoverSessionFromLocalStorage();

          if (sessionData) {
            console.log('auth-helpers: Dados de sessão encontrados no localStorage, tentando definir sessão...');

            // Tentar definir a sessão com os dados recuperados
            try {
              // Adicionar um pequeno atraso para evitar conflitos com o GoTrueClient
              await new Promise(resolve => setTimeout(resolve, 100));

              const { data: setSessionData, error: setSessionError } =
                await supabase.auth.setSession(sessionData);

              if (setSessionError) {
                console.error('auth-helpers: Erro ao definir sessão recuperada:', setSessionError.message);

                // Se temos um perfil no localStorage, permitir acesso limitado
                if (userProfile) {
                  console.log('auth-helpers: Usando perfil do localStorage após falha na definição da sessão');
                  return {
                    success: true,
                    session: null,
                    userProfile,
                    limited: true,
                    message: 'Acesso limitado com perfil local após falha na definição da sessão'
                  };
                }

                return { success: false, error: setSessionError, recoverable: true };
              }

              if (setSessionData.session) {
                console.log('auth-helpers: Sessão definida com sucesso a partir do localStorage');
                return { success: true, session: setSessionData.session, userProfile };
              }
            } catch (setSessionError) {
              console.error('auth-helpers: Erro ao definir sessão:', setSessionError);

              // Se temos um perfil no localStorage, permitir acesso limitado
              if (userProfile) {
                console.log('auth-helpers: Usando perfil do localStorage após erro na definição da sessão');
                return {
                  success: true,
                  session: null,
                  userProfile,
                  limited: true,
                  message: 'Acesso limitado com perfil local após erro na definição da sessão'
                };
              }

              return { success: false, error: setSessionError, recoverable: true };
            }
          } else {
            console.log('auth-helpers: Nenhum dado de sessão encontrado no localStorage');
          }
        }

        // Se temos um perfil no localStorage, permitir acesso limitado
        if (userProfile) {
          console.log('auth-helpers: Perfil de usuário encontrado no localStorage, permitindo acesso limitado');
          return {
            success: true,
            session: null,
            userProfile,
            limited: true,
            message: 'Acesso limitado com perfil local'
          };
        }

        // Tentar fazer login anônimo para permitir acesso a recursos públicos
        try {
          console.log('auth-helpers: Tentando criar sessão anônima para recursos públicos');

          // Adicionar um pequeno atraso para evitar conflitos com o GoTrueClient
          await new Promise(resolve => setTimeout(resolve, 100));

          const { data: anonData, error: anonError } = await supabase.auth.signInAnonymously();

          if (anonError) {
            console.error('auth-helpers: Erro ao criar sessão anônima:', anonError);
          } else if (anonData.session) {
            console.log('auth-helpers: Sessão anônima criada com sucesso');
            return {
              success: true,
              session: anonData.session,
              anonymous: true,
              message: 'Acesso anônimo para recursos públicos'
            };
          }
        } catch (anonError) {
          console.error('auth-helpers: Erro ao tentar criar sessão anônima:', anonError);
        }

        console.log('auth-helpers: Sessão inválida, acesso negado');
        return {
          success: false,
          error: refreshError,
          recoverable: true,
          requiresLogin: true
        };
      }

      if (refreshData.session) {
        console.log('auth-helpers: Sessão renovada com sucesso');
        return { success: true, session: refreshData.session, userProfile };
      }

      console.warn('auth-helpers: Sessão não disponível após renovação');

      // Se temos um perfil no localStorage, permitir acesso limitado
      if (userProfile) {
        console.log('auth-helpers: Usando perfil do localStorage após falha na renovação da sessão');
        return {
          success: true,
          session: null,
          userProfile,
          limited: true,
          message: 'Acesso limitado com perfil local após falha na renovação'
        };
      }

      return { success: false, error: new Error('Não foi possível obter uma sessão válida'), recoverable: true };
    } catch (refreshError) {
      console.error('auth-helpers: Erro ao tentar renovar sessão:', refreshError);

      // Se temos um perfil no localStorage, permitir acesso limitado
      if (userProfile) {
        console.log('auth-helpers: Perfil de usuário encontrado no localStorage, permitindo acesso limitado');
        return {
          success: true,
          session: null,
          userProfile,
          limited: true,
          message: 'Acesso limitado com perfil local após erro de renovação'
        };
      }

      console.log('auth-helpers: Erro ao renovar sessão, acesso negado');
      return {
        success: false,
        error: refreshError,
        recoverable: true,
        requiresLogin: true
      };
    }
  } catch (error) {
    console.error('auth-helpers: Erro não tratado ao garantir sessão válida:', error);

    // Última tentativa - verificar se há um perfil no localStorage
    try {
      const userProfileStr = localStorage.getItem("userProfile");
      if (userProfileStr) {
        const userProfile = JSON.parse(userProfileStr);
        console.log('auth-helpers: Perfil de usuário encontrado no localStorage, permitindo acesso limitado');
        return {
          success: true,
          session: null,
          userProfile,
          limited: true,
          message: 'Acesso limitado com perfil local após erro crítico'
        };
      }
    } catch (e) {
      console.error('auth-helpers: Erro ao verificar perfil no localStorage:', e);
    }

    console.log('auth-helpers: Erro crítico, acesso negado');
    return {
      success: false,
      error,
      recoverable: false,
      requiresLogin: true
    };
  }
}

/**
 * Função para verificar se o usuário está autenticado
 * Retorna o userId se autenticado, null caso contrário
 */
export async function getUserId(): Promise<string | null> {
  try {
    const { data } = await supabase.auth.getUser();
    return data.user?.id || null;
  } catch (error) {
    console.error('Erro ao obter ID do usuário:', error);
    return null;
  }
}

/**
 * Função para verificar e restaurar a autenticação
 * Útil para ser chamada na inicialização da aplicação
 */
export async function checkAndRestoreAuth() {
  try {
    // Verificar se já existe uma sessão
    const { data: sessionData } = await supabase.auth.getSession();

    if (sessionData.session) {
      console.log('Sessão existente encontrada');
      return { success: true, session: sessionData.session };
    }

    // Se não há sessão, tentar recuperar do localStorage
    const sessionFromStorage = recoverSessionFromLocalStorage();

    if (sessionFromStorage) {
      console.log('Tentando restaurar sessão do localStorage');

      const { data, error } = await supabase.auth.setSession(sessionFromStorage);

      if (error) {
        console.error('Erro ao restaurar sessão:', error);

        // Verificar se há um perfil no localStorage
        const userProfileStr = localStorage.getItem("userProfile");
        if (userProfileStr) {
          console.log('Perfil encontrado no localStorage, permitindo acesso limitado');
          return {
            success: true,
            userProfile: JSON.parse(userProfileStr),
            limited: true
          };
        }

        return { success: false, error };
      }

      if (data.session) {
        console.log('Sessão restaurada com sucesso');
        return { success: true, session: data.session };
      }
    }

    // Verificar se há um perfil no localStorage mesmo sem sessão
    const userProfileStr = localStorage.getItem("userProfile");
    if (userProfileStr) {
      console.log('Perfil encontrado no localStorage, permitindo acesso limitado');
      return {
        success: true,
        userProfile: JSON.parse(userProfileStr),
        limited: true
      };
    }

    return { success: false, message: 'Nenhuma sessão para restaurar' };
  } catch (error) {
    console.error('Erro ao verificar/restaurar autenticação:', error);

    // Última tentativa - verificar se há um perfil no localStorage
    try {
      const userProfileStr = localStorage.getItem("userProfile");
      if (userProfileStr) {
        console.log('Perfil encontrado no localStorage após erro, permitindo acesso limitado');
        return {
          success: true,
          userProfile: JSON.parse(userProfileStr),
          limited: true
        };
      }
    } catch (e) {
      console.error('Erro ao verificar perfil no localStorage:', e);
    }

    return { success: false, error };
  }
}