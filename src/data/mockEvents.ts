import { addDays, parseISO } from "date-fns";
import { generateCurrentDates } from "../utils/calendarHelpers";
import { Evento } from "../utils/calendarHelpers";

const currentDates = generateCurrentDates();

// Exemplo de eventos para o calendário
export const eventos: Evento[] = [
  // Eventos anteriores
  {
    id: "1",
    tipo: "precatorio",
    titulo: "Análise de Precatório",
    descricao: "Análise detalhada do precatório 123456",
    data: "2024-07-15",
    hora: "09:00",
    duracao: 120, // em minutos
    status: "pendente",
    prioridade: "alta",
    local: "Sala 302",
    responsavel: "Ana Silva"
  },
  {
    id: "2",
    tipo: "audiencia",
    titulo: "Audiência de Conciliação",
    descricao: "Audiência para tentativa de acordo no processo 789012",
    data: "2024-07-16",
    hora: "14:00",
    duracao: 60,
    status: "confirmado",
    prioridade: "media",
    local: "Sala de Audiências 5",
    responsavel: "<PERSON>"
  },
  {
    id: "3",
    tipo: "reuniao",
    titulo: "Reunião com Cliente",
    descricao: "Discussão sobre estratégia processual",
    data: "2024-07-15",
    hora: "11:00",
    duracao: 60,
    status: "confirmado",
    prioridade: "alta",
    local: "Sala de Reuniões 2",
    responsavel: "Maria Costa"
  },
  {
    id: "4",
    tipo: "prazo",
    titulo: "Prazo Final - Recurso",
    descricao: "Último dia para apresentação de recurso no processo 654321",
    data: "2024-07-18",
    hora: "18:00",
    duracao: 30,
    status: "pendente",
    prioridade: "urgente",
    local: "Online",
    responsavel: "João Lima"
  },
  {
    id: "5",
    tipo: "despacho",
    titulo: "Despacho com Juiz",
    descricao: "Reunião para despachar processos urgentes",
    data: "2024-07-19",
    hora: "10:00",
    duracao: 90,
    status: "confirmado",
    prioridade: "alta",
    local: "Gabinete do Juiz",
    responsavel: "Dr. Paulo Mendes"
  },
  {
    id: "6",
    tipo: "precatorio",
    titulo: "Análise de Precatório Urgente (Caso Saúde)",
    descricao: "Análise prioritária do precatório relacionado a tratamento médico",
    data: "2024-08-05",
    hora: "08:30",
    duracao: 120,
    status: "pendente",
    prioridade: "urgente",
    local: "Sala 405",
    responsavel: "Ana Silva"
  },
  // Mais eventos antigos...
  
  // Novos eventos para hoje e datas próximas
  {
    id: "21",
    tipo: "analise",
    titulo: "Revisão Urgente de Precatório",
    descricao: "Verificação de documentação complementar para caso prioritário",
    data: currentDates.today,
    hora: "09:00",
    duracao: 90,
    status: "confirmado",
    prioridade: "urgente",
    local: "Sala 201",
    responsavel: "Fernanda Oliveira"
  },
  {
    id: "22",
    tipo: "reuniao",
    titulo: "Alinhamento da Equipe",
    descricao: "Discussão sobre organização de processos e definição de prioridades",
    data: currentDates.today,
    hora: "11:00",
    duracao: 60,
    status: "confirmado",
    prioridade: "alta",
    local: "Sala de Reuniões 3",
    responsavel: "Ricardo Gomes"
  },
  {
    id: "23",
    tipo: "atendimento",
    titulo: "Atendimento ao Cidadão",
    descricao: "Plantão de atendimento para esclarecimento de dúvidas sobre precatórios",
    data: currentDates.today,
    hora: "14:00",
    duracao: 180,
    status: "confirmado",
    prioridade: "media",
    local: "Térreo - Balcão de Atendimento",
    responsavel: "Equipe de Atendimento"
  },
  {
    id: "24",
    tipo: "audiencia",
    titulo: "Audiência Online",
    descricao: "Audiência virtual para discussão de caso com múltiplas partes",
    data: currentDates.today,
    hora: "16:30",
    duracao: 90,
    status: "confirmado",
    prioridade: "alta",
    local: "Sala Virtual 2",
    responsavel: "Dr. Alexandre Souza"
  },
  {
    id: "25",
    tipo: "despacho",
    titulo: "Despacho Extraordinário",
    descricao: "Assinatura de documentos prioritários com prazo expirando",
    data: currentDates.tomorrow,
    hora: "08:30",
    duracao: 120,
    status: "confirmado",
    prioridade: "urgente",
    local: "Gabinete 101",
    responsavel: "Dra. Beatriz Campos"
  },
  {
    id: "26",
    tipo: "treinamento",
    titulo: "Capacitação: Novo Sistema",
    descricao: "Treinamento para utilização do novo sistema de gerenciamento de precatórios",
    data: currentDates.tomorrow,
    hora: "10:00",
    duracao: 240,
    status: "confirmado",
    prioridade: "alta",
    local: "Sala de Treinamento",
    responsavel: "Equipe de TI"
  },
  {
    id: "27",
    tipo: "prazo",
    titulo: "Entrega de Documentação",
    descricao: "Prazo final para submissão de documentos complementares",
    data: currentDates.tomorrow,
    hora: "18:00",
    duracao: 30,
    status: "pendente",
    prioridade: "urgente",
    local: "Online",
    responsavel: "Todos os Departamentos"
  },
  // Eventos para a próxima semana e próximo mês
  {
    id: "28",
    tipo: "audiencia",
    titulo: "Audiência de Conciliação Especial",
    descricao: "Tentativa de acordo em caso de grande valor e complexidade",
    data: currentDates.nextWeek,
    hora: "09:30",
    duracao: 180,
    status: "confirmado",
    prioridade: "alta",
    local: "Sala de Audiências 1",
    responsavel: "Dr. Marcelo Lima"
  },
  {
    id: "35",
    tipo: "analise",
    titulo: "Força-Tarefa de Análise Final",
    descricao: "Revisão final de precatórios para inclusão no orçamento do próximo ano",
    data: addDays(parseISO(currentDates.nextMonth), 5),
    hora: "08:00",
    duracao: 540,
    status: "confirmado",
    prioridade: "urgente",
    local: "Centro de Operações",
    responsavel: "Equipe Multidisciplinar"
  }
]; 