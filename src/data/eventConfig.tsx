import { AlertCircle, CheckCircle, Clock3, X } from "lucide-react";
import React from "react";

// Configurações de tipos de eventos e cores
export const tipoEventoConfig = {
  precatorio: { nome: "Precatório", icon: "📄" },
  audiencia: { nome: "Audiência", icon: "⚖️" },
  reuniao: { nome: "Reuni<PERSON>", icon: "👥" },
  prazo: { nome: "Prazo", icon: "⏱️" },
  despacho: { nome: "Despacho", icon: "📝" },
  analise: { nome: "Análise", icon: "🔍" },
  treinamento: { nome: "Treinamento", icon: "📚" },
  atendimento: { nome: "Atendimento", icon: "👨‍⚖️" },
};

export const eventColors = {
  precatorio: "bg-blue-100 text-blue-800 dark:bg-blue-950/70 dark:text-blue-200 border border-blue-200 dark:border-blue-900",
  audiencia: "bg-purple-100 text-purple-800 dark:bg-purple-950/70 dark:text-purple-200 border border-purple-200 dark:border-purple-900",
  reuniao: "bg-green-100 text-green-800 dark:bg-green-950/70 dark:text-green-200 border border-green-200 dark:border-green-900",
  prazo: "bg-red-100 text-red-800 dark:bg-red-950/70 dark:text-red-200 border border-red-200 dark:border-red-900",
  despacho: "bg-amber-100 text-amber-800 dark:bg-amber-950/70 dark:text-amber-200 border border-amber-200 dark:border-amber-900",
  analise: "bg-indigo-100 text-indigo-800 dark:bg-indigo-950/70 dark:text-indigo-200 border border-indigo-200 dark:border-indigo-900",
  treinamento: "bg-teal-100 text-teal-800 dark:bg-teal-950/70 dark:text-teal-200 border border-teal-200 dark:border-teal-900",
  atendimento: "bg-rose-100 text-rose-800 dark:bg-rose-950/70 dark:text-rose-200 border border-rose-200 dark:border-rose-900",
};

export const statusConfig = {
  pendente: "bg-yellow-100 text-yellow-800 dark:bg-yellow-950/70 dark:text-yellow-200 border border-yellow-200 dark:border-yellow-900",
  confirmado: "bg-green-100 text-green-800 dark:bg-green-950/70 dark:text-green-200 border border-green-200 dark:border-green-900",
  cancelado: "bg-red-100 text-red-800 dark:bg-red-950/70 dark:text-red-200 border border-red-200 dark:border-red-900",
  concluido: "bg-blue-100 text-blue-800 dark:bg-blue-950/70 dark:text-blue-200 border border-blue-200 dark:border-blue-900",
  adiado: "bg-gray-100 text-gray-800 dark:bg-gray-950/70 dark:text-gray-200 border border-gray-200 dark:border-gray-900",
};

export const prioridadeConfig = {
  baixa: "bg-gray-100 text-gray-800 dark:bg-gray-950/70 dark:text-gray-200 border border-gray-200 dark:border-gray-900",
  media: "bg-blue-100 text-blue-800 dark:bg-blue-950/70 dark:text-blue-200 border border-blue-200 dark:border-blue-900",
  alta: "bg-orange-100 text-orange-800 dark:bg-orange-950/70 dark:text-orange-200 border border-orange-200 dark:border-orange-900",
  urgente: "bg-red-100 text-red-800 dark:bg-red-950/70 dark:text-red-200 border border-red-200 dark:border-red-900",
};

export const statusIcons = {
  pendente: <AlertCircle className="w-3 h-3 mr-1" />,
  confirmado: <CheckCircle className="w-3 h-3 mr-1" />,
  cancelado: <X className="w-3 h-3 mr-1" />,
  concluido: <CheckCircle className="w-3 h-3 mr-1" />,
  adiado: <Clock3 className="w-3 h-3 mr-1" />,
};