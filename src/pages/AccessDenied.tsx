import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Shield, AlertTriangle, ArrowLeft, Home } from 'lucide-react';
import { TopNav } from '@/components/top-nav';

export default function AccessDenied() {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-background">
      <TopNav title="Acesso Negado" />

      <div className="container max-w-6xl mx-auto py-8 px-4">
        <Card className="w-full max-w-md mx-auto border-destructive/50">
          <CardHeader className="text-center pb-2">
            <div className="flex justify-center mb-4">
              <div className="p-3 rounded-full bg-destructive/10">
                <Shield className="h-12 w-12 text-destructive" />
              </div>
            </div>
            <CardTitle className="text-2xl">Acesso Negado</CardTitle>
            <CardDescription className="text-destructive">
              Você não tem permissão para acessar esta página
            </CardDescription>
          </CardHeader>

          <CardContent className="text-center">
            <div className="bg-muted/50 rounded-lg p-4 mb-4 flex items-center">
              <AlertTriangle className="h-5 w-5 text-amber-500 mr-2 flex-shrink-0" />
              <p className="text-sm text-muted-foreground">
                Se você acredita que deveria ter acesso a esta página, entre em contato com o administrador do sistema.
              </p>
            </div>
          </CardContent>

          <CardFooter className="flex justify-center gap-4">
            <Button
              variant="outline"
              className="flex items-center gap-2"
              onClick={() => navigate(-1)}
            >
              <ArrowLeft className="h-4 w-4" />
              Voltar
            </Button>

            <Button
              className="flex items-center gap-2"
              onClick={() => navigate('/dashboard')}
            >
              <Home className="h-4 w-4" />
              Ir para Dashboard
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}
