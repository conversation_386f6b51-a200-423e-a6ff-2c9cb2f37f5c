import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { TopNav } from '@/components/top-nav';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { toast } from 'sonner';
import {
  Users,
  Shield,
  Eye,
  FileText,
  Settings,
  Search,
  RefreshCw,
  UserCog,
  LayoutDashboard,
  History,
  Filter
} from 'lucide-react';
import { UserPermissionsMatrix } from '@/components/permissions/UserPermissionsMatrix';
import { UserHierarchyViewer } from '@/components/permissions/UserHierarchyViewer';
import { CustomViewsManager } from '@/components/permissions/CustomViewsManager';
import { PermissionLogsViewer } from '@/components/permissions/PermissionLogsViewer';
import { AdvancedPermissionsSettings } from '@/components/permissions/AdvancedPermissionsSettings';
import { supabase } from '@/lib/supabase';
import { UserBasicInfo } from '@/types/permissions';

export default function PermissionsManagement() {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [users, setUsers] = useState<UserBasicInfo[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<UserBasicInfo[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('matrix');
  const [refreshing, setRefreshing] = useState(false);

  // Verificar permissões do usuário atual
  useEffect(() => {
    try {
      // Verificar se é administrador
      if (!user || user.role !== 'admin') {
        toast.error("Acesso negado", {
          description: "Você não tem permissão para acessar esta página."
        });
        navigate("/dashboard");
      } else {
        loadUsers();
      }
    } catch (error) {
      console.error("Erro ao verificar permissões:", error);
      navigate("/dashboard");
    }
  }, [navigate, user]);

  // Carregar lista de usuários
  const loadUsers = async () => {
    try {
      setLoading(true);
      
      const { data, error } = await supabase
        .from('auth.users')
        .select('id, email, raw_user_meta_data, role')
        .order('email');
      
      if (error) {
        console.error('Erro ao carregar usuários:', error);
        toast.error("Erro ao carregar usuários", {
          description: error.message
        });
        return;
      }
      
      // Formatar dados dos usuários
      const formattedUsers: UserBasicInfo[] = data.map(user => ({
        id: user.id,
        name: user.raw_user_meta_data?.name || user.email,
        email: user.email,
        role: user.role,
        avatar_url: user.raw_user_meta_data?.avatar_url
      }));
      
      setUsers(formattedUsers);
      setFilteredUsers(formattedUsers);
    } catch (error) {
      console.error('Erro ao carregar usuários:', error);
      toast.error("Erro ao carregar usuários", {
        description: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    } finally {
      setLoading(false);
    }
  };

  // Atualizar lista de usuários filtrados quando a busca mudar
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredUsers(users);
      return;
    }
    
    const query = searchQuery.toLowerCase();
    const filtered = users.filter(user => 
      user.name.toLowerCase().includes(query) || 
      user.email?.toLowerCase().includes(query) ||
      user.role?.toLowerCase().includes(query)
    );
    
    setFilteredUsers(filtered);
  }, [searchQuery, users]);

  // Função para atualizar a lista manualmente
  const handleRefresh = async () => {
    setRefreshing(true);
    await loadUsers();
    setRefreshing(false);
  };

  return (
    <div className="flex flex-col min-h-screen bg-background">
      <TopNav title="Gerenciamento de Permissões" />
      
      <main className="flex-1 container py-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Gerenciamento de Permissões</h1>
            <p className="text-muted-foreground">
              Configure permissões avançadas, visualizações personalizadas e hierarquias de usuários
            </p>
          </div>
          
          <div className="flex items-center gap-2">
            <div className="relative w-64">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Buscar usuários..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            
            <Button
              variant="outline"
              size="icon"
              onClick={handleRefresh}
              disabled={refreshing}
            >
              <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>
        
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-5 mb-6">
            <TabsTrigger value="matrix">
              <Shield className="h-4 w-4 mr-2" />
              Matriz de Permissões
            </TabsTrigger>
            <TabsTrigger value="hierarchy">
              <Users className="h-4 w-4 mr-2" />
              Hierarquia de Usuários
            </TabsTrigger>
            <TabsTrigger value="views">
              <Eye className="h-4 w-4 mr-2" />
              Visualizações Personalizadas
            </TabsTrigger>
            <TabsTrigger value="logs">
              <History className="h-4 w-4 mr-2" />
              Histórico de Alterações
            </TabsTrigger>
            <TabsTrigger value="settings">
              <Settings className="h-4 w-4 mr-2" />
              Configurações
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="matrix" className="space-y-4">
            <UserPermissionsMatrix users={filteredUsers} />
          </TabsContent>
          
          <TabsContent value="hierarchy" className="space-y-4">
            <UserHierarchyViewer users={users} />
          </TabsContent>
          
          <TabsContent value="views" className="space-y-4">
            <CustomViewsManager users={users} />
          </TabsContent>
          
          <TabsContent value="logs" className="space-y-4">
            <PermissionLogsViewer users={users} />
          </TabsContent>
          
          <TabsContent value="settings" className="space-y-4">
            <AdvancedPermissionsSettings />
          </TabsContent>
        </Tabs>
      </main>
    </div>
  );
}
