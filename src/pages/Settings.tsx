import { Bell, Lock, User, Globe, Moon, Sun, Settings as SettingsIcon, Shield, Cog, Database, AlertCircle, RefreshCw, Activity } from "lucide-react";
import { useTheme } from "next-themes";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/components/ui/use-toast";
import { TopNav } from "@/components/top-nav";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useAuth } from "@/contexts/AuthContext";
import { useState } from "react";
import { SupabaseDiagnostic } from "@/components/SupabaseDiagnostic";
import { PerformanceMonitoringDashboard } from "@/components/admin/PerformanceMonitoringDashboard";

const Settings = () => {
  const { setTheme, theme } = useTheme();
  const { toast } = useToast();
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState("perfil");
  const [formData, setFormData] = useState({
    nome: user?.nome || "",
    email: user?.email || "",
    telefone: "",
    empresa: "",
    cargo: "",
    idioma: "pt-BR",
    fuso: "America/Sao_Paulo",
    formato_data: "dd/MM/yyyy",
    notificacoes_email: true,
    notificacoes_sistema: true,
    notificacoes_tarefas: true
  });

  const handleThemeChange = () => {
    const newTheme = theme === "dark" ? "light" : "dark";
    setTheme(newTheme);
    toast({
      title: "Tema Atualizado",
      description: `Alterado para modo ${newTheme === "dark" ? "escuro" : "claro"}`,
      duration: 2000,
    });
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = () => {
    toast({
      title: "Configurações Salvas",
      description: "Suas configurações foram atualizadas com sucesso.",
      duration: 2000,
    });
  };

  return (
    <div className="flex flex-col h-screen w-screen overflow-hidden">
      <TopNav
        title="Configurações"
        icon={<SettingsIcon className="h-6 w-6 text-primary" />}
      />

      <div className="flex flex-1 overflow-auto pt-20">
        <div className="p-4 md:p-6 rounded-tl-2xl border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-900 flex flex-col gap-4 flex-1 w-full h-full">

          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className={`grid ${user?.role === 'admin' ? 'grid-cols-5' : 'grid-cols-3'} mb-6`}>
              <TabsTrigger value="perfil" className="flex items-center gap-2">
                <User className="h-4 w-4" />
                Perfil
              </TabsTrigger>
              <TabsTrigger value="seguranca" className="flex items-center gap-2">
                <Lock className="h-4 w-4" />
                Segurança
              </TabsTrigger>
              <TabsTrigger value="preferencias" className="flex items-center gap-2">
                <Globe className="h-4 w-4" />
                Preferências
              </TabsTrigger>
              {user?.role === 'admin' && (
                <>
                  <TabsTrigger value="sistema" className="flex items-center gap-2">
                    <Cog className="h-4 w-4" />
                    Sistema
                  </TabsTrigger>
                  <TabsTrigger value="performance" className="flex items-center gap-2">
                    <Activity className="h-4 w-4" />
                    Performance
                  </TabsTrigger>
                </>
              )}
            </TabsList>

            {/* Aba de Perfil */}
            <TabsContent value="perfil" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Informações Pessoais</CardTitle>
                  <CardDescription>
                    Atualize suas informações pessoais e de contato.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="nome">Nome Completo</Label>
                      <Input
                        id="nome"
                        value={formData.nome}
                        onChange={(e) => handleInputChange("nome", e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email">Email</Label>
                      <Input
                        id="email"
                        type="email"
                        value={formData.email}
                        onChange={(e) => handleInputChange("email", e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="telefone">Telefone</Label>
                      <Input
                        id="telefone"
                        value={formData.telefone}
                        onChange={(e) => handleInputChange("telefone", e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="empresa">Empresa</Label>
                      <Input
                        id="empresa"
                        value={formData.empresa}
                        onChange={(e) => handleInputChange("empresa", e.target.value)}
                      />
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-end">
                  <Button onClick={handleSave}>Salvar Alterações</Button>
                </CardFooter>
              </Card>
            </TabsContent>

            {/* Aba de Segurança */}
            <TabsContent value="seguranca" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Senha e Autenticação</CardTitle>
                  <CardDescription>
                    Gerencie sua senha e configurações de segurança.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="senha-atual">Senha Atual</Label>
                    <Input id="senha-atual" type="password" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="nova-senha">Nova Senha</Label>
                    <Input id="nova-senha" type="password" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="confirmar-senha">Confirmar Nova Senha</Label>
                    <Input id="confirmar-senha" type="password" />
                  </div>

                  <div className="pt-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Lock className="h-5 w-5 text-gray-600 dark:text-gray-400" />
                        <span className="font-medium">Autenticação de Dois Fatores</span>
                      </div>
                      <Switch className="data-[state=checked]:bg-primary" />
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-end">
                  <Button onClick={handleSave}>Atualizar Senha</Button>
                </CardFooter>
              </Card>
            </TabsContent>

            {/* Aba de Preferências */}
            <TabsContent value="preferencias" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Aparência e Tema</CardTitle>
                  <CardDescription>
                    Personalize a aparência do sistema.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between py-2">
                    <div className="flex items-center gap-2">
                      {theme === 'dark' ? (
                        <Moon className="h-5 w-5 text-gray-600 dark:text-gray-400" />
                      ) : (
                        <Sun className="h-5 w-5 text-gray-600" />
                      )}
                      <span className="font-medium">Modo Escuro</span>
                    </div>
                    <Switch
                      checked={theme === 'dark'}
                      onCheckedChange={handleThemeChange}
                      className="data-[state=checked]:bg-primary"
                    />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Notificações</CardTitle>
                  <CardDescription>
                    Configure como deseja receber notificações.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between py-2">
                    <div className="flex items-center gap-2">
                      <Bell className="h-5 w-5 text-gray-600 dark:text-gray-400" />
                      <span>Notificações por Email</span>
                    </div>
                    <Switch
                      checked={formData.notificacoes_email}
                      onCheckedChange={(checked) => handleInputChange("notificacoes_email", checked)}
                      className="data-[state=checked]:bg-primary"
                    />
                  </div>

                  <div className="flex items-center justify-between py-2">
                    <div className="flex items-center gap-2">
                      <Bell className="h-5 w-5 text-gray-600 dark:text-gray-400" />
                      <span>Notificações do Sistema</span>
                    </div>
                    <Switch
                      checked={formData.notificacoes_sistema}
                      onCheckedChange={(checked) => handleInputChange("notificacoes_sistema", checked)}
                      className="data-[state=checked]:bg-primary"
                    />
                  </div>

                  <div className="flex items-center justify-between py-2">
                    <div className="flex items-center gap-2">
                      <Bell className="h-5 w-5 text-gray-600 dark:text-gray-400" />
                      <span>Notificações de Tarefas</span>
                    </div>
                    <Switch
                      checked={formData.notificacoes_tarefas}
                      onCheckedChange={(checked) => handleInputChange("notificacoes_tarefas", checked)}
                      className="data-[state=checked]:bg-primary"
                    />
                  </div>
                </CardContent>
                <CardFooter className="flex justify-end">
                  <Button onClick={handleSave}>Salvar Preferências</Button>
                </CardFooter>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Regionalização</CardTitle>
                  <CardDescription>
                    Configure idioma, fuso horário e formato de data.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="idioma">Idioma</Label>
                    <Select
                      value={formData.idioma}
                      onValueChange={(value) => handleInputChange("idioma", value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione um idioma" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="pt-BR">Português (Brasil)</SelectItem>
                        <SelectItem value="en-US">English (US)</SelectItem>
                        <SelectItem value="es">Español</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="fuso">Fuso Horário</Label>
                    <Select
                      value={formData.fuso}
                      onValueChange={(value) => handleInputChange("fuso", value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione um fuso horário" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="America/Sao_Paulo">Brasília (GMT-3)</SelectItem>
                        <SelectItem value="America/Manaus">Manaus (GMT-4)</SelectItem>
                        <SelectItem value="America/New_York">New York (GMT-5)</SelectItem>
                        <SelectItem value="Europe/Lisbon">Lisboa (GMT+0)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="formato_data">Formato de Data</Label>
                    <Select
                      value={formData.formato_data}
                      onValueChange={(value) => handleInputChange("formato_data", value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione um formato de data" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="dd/MM/yyyy">DD/MM/AAAA</SelectItem>
                        <SelectItem value="MM/dd/yyyy">MM/DD/AAAA</SelectItem>
                        <SelectItem value="yyyy-MM-dd">AAAA-MM-DD</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-end">
                  <Button onClick={handleSave}>Salvar Configurações</Button>
                </CardFooter>
              </Card>
            </TabsContent>

            {/* Aba de Sistema (apenas para administradores) */}
            {user?.role === 'admin' && (
              <TabsContent value="sistema" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Configurações do Sistema</CardTitle>
                    <CardDescription>
                      Gerencie configurações avançadas do sistema.
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <Card className="border border-muted hover:border-primary/50 transition-colors">
                          <CardHeader className="pb-2">
                            <CardTitle className="text-lg flex items-center gap-2">
                              <Shield className="h-5 w-5 text-primary" />
                              Gerenciamento de Cargos
                            </CardTitle>
                          </CardHeader>
                          <CardContent className="pb-2">
                            <p className="text-sm text-muted-foreground">
                              Crie e gerencie cargos e suas permissões padrão no sistema.
                            </p>
                          </CardContent>
                          <CardFooter>
                            <Button variant="outline" className="w-full" onClick={() => window.location.href = "/cargos"}>
                              Acessar Gerenciamento de Cargos
                            </Button>
                          </CardFooter>
                        </Card>

                        <Card className="border border-muted hover:border-primary/50 transition-colors">
                          <CardHeader className="pb-2">
                            <CardTitle className="text-lg flex items-center gap-2">
                              <Database className="h-5 w-5 text-primary" />
                              Banco de Dados
                            </CardTitle>
                          </CardHeader>
                          <CardContent className="pb-2">
                            <p className="text-sm text-muted-foreground">
                              Diagnóstico e manutenção da conexão com o banco de dados.
                            </p>
                          </CardContent>
                          <CardFooter>
                            <SupabaseDiagnostic compact={true} />
                          </CardFooter>
                        </Card>

                        <Card className="border border-red-200 dark:border-red-800 hover:border-red-300 dark:hover:border-red-700 transition-colors">
                          <CardHeader className="pb-2">
                            <CardTitle className="text-lg flex items-center gap-2 text-red-600 dark:text-red-400">
                              <AlertCircle className="h-5 w-5" />
                              Problemas de Conexão
                            </CardTitle>
                          </CardHeader>
                          <CardContent className="pb-2">
                            <p className="text-sm text-muted-foreground">
                              Se você está enfrentando problemas para carregar dados, tente resetar a autenticação.
                            </p>
                          </CardContent>
                          <CardFooter>
                            <Button
                              variant="destructive"
                              className="w-full"
                              onClick={() => {
                                // Limpar localStorage
                                const keysToRemove = [];
                                for (let i = 0; i < localStorage.length; i++) {
                                  const key = localStorage.key(i);
                                  if (key && (
                                    key.startsWith('sb-') ||
                                    key.includes('supabase') ||
                                    key.includes('auth') ||
                                    key.includes('user') ||
                                    key.includes('profile')
                                  )) {
                                    keysToRemove.push(key);
                                  }
                                }

                                keysToRemove.forEach(key => {
                                  console.log(`Removendo chave do localStorage: ${key}`);
                                  localStorage.removeItem(key);
                                });

                                // Limpar sessionStorage
                                const sessionKeysToRemove = [];
                                for (let i = 0; i < sessionStorage.length; i++) {
                                  const key = sessionStorage.key(i);
                                  if (key && (
                                    key.startsWith('sb-') ||
                                    key.includes('supabase') ||
                                    key.includes('auth') ||
                                    key.includes('user') ||
                                    key.includes('profile')
                                  )) {
                                    sessionKeysToRemove.push(key);
                                  }
                                }

                                sessionKeysToRemove.forEach(key => {
                                  console.log(`Removendo chave do sessionStorage: ${key}`);
                                  sessionStorage.removeItem(key);
                                });

                                // Limpar cookies
                                document.cookie.split(';').forEach(cookie => {
                                  const [name] = cookie.trim().split('=');
                                  if (name && (
                                    name.startsWith('sb-') ||
                                    name.includes('supabase') ||
                                    name.includes('auth')
                                  )) {
                                    console.log(`Removendo cookie: ${name}`);
                                    document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
                                  }
                                });

                                toast({
                                  title: "Autenticação resetada com sucesso!",
                                  description: "Redirecionando para login...",
                                  duration: 2000,
                                });

                                // Redirecionar para login
                                setTimeout(() => {
                                  window.location.href = '/login';
                                }, 1500);
                              }}
                            >
                              <RefreshCw className="h-4 w-4 mr-2" />
                              Resetar Autenticação
                            </Button>
                          </CardFooter>
                        </Card>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            )}

            {/* Aba de Performance (apenas para administradores) */}
            {user?.role === 'admin' && (
              <TabsContent value="performance" className="space-y-6">
                <PerformanceMonitoringDashboard />
              </TabsContent>
            )}
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default Settings;