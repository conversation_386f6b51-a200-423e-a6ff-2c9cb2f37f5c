import React, { useState, useEffect } from 'react';
import { TopNav } from '@/components/top-nav';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { toast } from "sonner";
import {
  Users,
  Columns,
  LayoutDashboard,
  ShieldAlert,
  RefreshCw,
  Search,
  Loader2,
  ClipboardList
} from "lucide-react";
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/lib/supabase';
import { KanbanPermissionsAdmin } from '@/components/permissions/KanbanPermissionsAdmin';
import { KanbanViewsAdmin } from '@/components/permissions/KanbanViewsAdmin';
import { PermissionAuditLog } from '@/components/permissions/PermissionAuditLog';
import { UserBasicInfo } from '@/types/permissions';

export function PermissionsAdmin() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('kanban-columns');
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [users, setUsers] = useState<UserBasicInfo[]>([]);

  // Carregar usuários
  useEffect(() => {
    loadUsers();
  }, []);

  // Carregar usuários
  const loadUsers = async () => {
    try {
      setLoading(true);

      const { data, error } = await supabase
        .from('profiles')
        .select('id, name, email, role, avatar_url')
        .order('name');

      if (error) {
        console.error('Erro ao carregar usuários:', error);
        toast.error("Erro ao carregar usuários", {
          description: error.message
        });
        return;
      }

      setUsers(data || []);
    } catch (error) {
      console.error('Erro ao carregar usuários:', error);
      toast.error("Erro ao carregar usuários", {
        description: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    } finally {
      setLoading(false);
    }
  };

  // Atualizar manualmente
  const handleRefresh = async () => {
    setRefreshing(true);
    await loadUsers();
    setRefreshing(false);
  };

  return (
    <div className="flex flex-col min-h-screen">
      <TopNav title="Administração de Permissões" />

      <main className="flex-1 container mx-auto py-6 px-4 md:px-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold">Administração de Permissões</h1>
            <p className="text-muted-foreground">
              Gerencie permissões, visualizações personalizadas e auditoria de alterações
            </p>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={refreshing}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              Atualizar
            </Button>
          </div>
        </div>

        {loading ? (
          <div className="flex flex-col items-center justify-center h-[60vh]">
            <Loader2 className="h-8 w-8 animate-spin mb-4" />
            <div>Carregando dados...</div>
          </div>
        ) : (
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid grid-cols-3 mb-6">
              <TabsTrigger value="kanban-columns">
                <Columns className="h-4 w-4 mr-2" />
                Colunas do Kanban
              </TabsTrigger>
              <TabsTrigger value="kanban-views">
                <LayoutDashboard className="h-4 w-4 mr-2" />
                Visualizações Personalizadas
              </TabsTrigger>
              <TabsTrigger value="audit-logs">
                <ClipboardList className="h-4 w-4 mr-2" />
                Logs de Auditoria
              </TabsTrigger>
            </TabsList>

            <TabsContent value="kanban-columns" className="space-y-4">
              <KanbanPermissionsAdmin users={users} />
            </TabsContent>

            <TabsContent value="kanban-views" className="space-y-4">
              <KanbanViewsAdmin users={users} />
            </TabsContent>

            <TabsContent value="audit-logs" className="space-y-4">
              <PermissionAuditLog users={users} />
            </TabsContent>
          </Tabs>
        )}
      </main>
    </div>
  );
}
