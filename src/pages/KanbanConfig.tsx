import { useState } from "react";
import { GerenciadorColunas } from "@/components/Precatorios/KanbanConfig/GerenciadorColunas";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { AlertCircle, Settings, Columns, ArrowRightLeft } from "lucide-react";

function KanbanConfig() {
  const [tipoAtivo, setTipoAtivo] = useState<'TODOS' | 'PRECATORIO' | 'RPV'>('TODOS');

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold">Configuração do Kanban</h1>
          <p className="text-muted-foreground mt-1">
            <PERSON><PERSON><PERSON><PERSON> as colunas do Kanban para Precatórios e RPVs
          </p>
        </div>
        <Badge variant="outline" className="flex items-center gap-1 px-3 py-1">
          <Settings className="h-4 w-4" />
          <span>Configurações de Administrador</span>
        </Badge>
      </div>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-amber-500" />
            <span>Informações Importantes</span>
          </CardTitle>
          <CardDescription>
            Entenda como funciona a configuração do Kanban
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ul className="list-disc pl-5 space-y-2">
            <li>As colunas configuradas aqui serão utilizadas nos quadros Kanban de Precatórios e RPVs</li>
            <li>Você pode configurar colunas específicas para Precatórios, RPVs ou ambos</li>
            <li>A ordem das colunas pode ser alterada usando as setas para cima e para baixo</li>
            <li>Colunas inativas não aparecerão no Kanban, mas seus dados serão preservados</li>
            <li>O Status ID é um identificador único usado internamente e não pode ser alterado após a criação</li>
          </ul>
        </CardContent>
      </Card>

      <Tabs defaultValue="TODOS" onValueChange={(value) => setTipoAtivo(value as any)}>
        <div className="flex items-center gap-2 mb-4">
          <Columns className="h-5 w-5" />
          <h2 className="text-xl font-semibold">Gerenciamento de Colunas</h2>
        </div>
        
        <TabsList className="mb-6">
          <TabsTrigger value="TODOS" className="min-w-[100px]">
            Todas as Colunas
          </TabsTrigger>
          <TabsTrigger value="PRECATORIO" className="min-w-[100px]">
            Precatórios
          </TabsTrigger>
          <TabsTrigger value="RPV" className="min-w-[100px]">
            RPVs
          </TabsTrigger>
        </TabsList>

        <TabsContent value={tipoAtivo}>
          <GerenciadorColunas tipoInicial={tipoAtivo === 'TODOS' ? 'TODOS' : tipoAtivo} />
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default KanbanConfig;
