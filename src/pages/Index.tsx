import { Outlet, useNavigate } from "react-router-dom";
import { DockNav } from "@/components/dock-nav";
import { useEffect, useState, useRef } from "react";
import { AutoReconnect } from "@/components/AutoReconnect";
import { FullScreenLoading } from "@/components/ui/loading-indicator";
import { supabase, refreshSupabaseSession, getUserProfile, syncUserProfile } from "@/lib/supabase";
import { toast } from "sonner";
import { authManager } from "@/lib/authManager";
import TabReloader from "@/components/TabReloader";

const Index = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const authTimeoutRef = useRef<number | null>(null);
  const authRetryCountRef = useRef<number>(0);
  const MAX_AUTH_RETRIES = 3;

  // Adicionar um timeout global para evitar carregamento infinito
  useEffect(() => {
    // Definir um timeout global para redirecionar para login se a página ficar carregando por muito tempo
    const globalTimeout = window.setTimeout(() => {
      if (isLoading) {
        console.log("Index: Timeout global atingido, redirecionando para login");

        // Limpar completamente o estado de autenticação
        try {
          localStorage.removeItem("userProfile");
          sessionStorage.removeItem("userProfile");
          localStorage.removeItem("sb-ubwzukpsqcrwzfbppoux-auth-token");
          sessionStorage.removeItem("sb-ubwzukpsqcrwzfbppoux-auth-token");
          localStorage.removeItem("sb-ubwzukpsqcrwzfbppoux-auth-backup");
          sessionStorage.removeItem("sb-ubwzukpsqcrwzfbppoux-auth-backup");
          localStorage.removeItem("sb-ubwzukpsqcrwzfbppoux-last-refresh");

          // Limpar dados de tab session
          localStorage.removeItem("last_active_tab");
          localStorage.removeItem("active_tab_timestamp");
          sessionStorage.removeItem("current_tab_id");
          sessionStorage.removeItem("tab_last_active");
        } catch (error) {
          console.error("Index: Erro ao limpar estado de autenticação:", error);
        }

        // Redirecionar para login
        window.location.href = "/login?forced=true&reason=" + encodeURIComponent("Tempo limite de carregamento excedido");
      }
    }, 10000); // 10 segundos (aumentado para dar mais tempo)

    return () => {
      window.clearTimeout(globalTimeout);
    };
  }, [isLoading]);

  // Função para redirecionar para a página de login
  const redirectToLogin = (message?: string) => {
    // Limpar qualquer timeout pendente
    if (authTimeoutRef.current) {
      window.clearTimeout(authTimeoutRef.current);
      authTimeoutRef.current = null;
    }

    // Parar o gerenciador de autenticação
    authManager.stop();

    // Limpar dados de autenticação de localStorage e sessionStorage
    try {
      localStorage.removeItem("userProfile");
      sessionStorage.removeItem("userProfile");
    } catch (storageError) {
      console.error("Index: Erro ao limpar dados de autenticação:", storageError);
    }

    // Mostrar mensagem ao usuário
    toast.error("Sessão expirada", {
      description: message || "Sua sessão expirou ou é inválida. Por favor, faça login novamente."
    });

    // Usar window.location para um redirecionamento mais forte
    window.location.href = "/login";
  };

  // Verificar autenticação ao montar o componente
  useEffect(() => {
    let isMounted = true;

    // Adicionar listener para mudanças de visibilidade
    const handleVisibilityChange = async () => {
      if (!document.hidden) {
        console.log("Index: Página voltou a ficar visível, verificando autenticação");

        // Adicionar um pequeno atraso para evitar conflitos com o GoTrueClient
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Verificar se a página ainda está visível antes de verificar a sessão
        if (!document.hidden) {
          try {
            // Se estiver carregando, não fazer nada para evitar conflitos
            if (isLoading) {
              console.log("Index: Página já está carregando, pulando verificação de visibilidade");
              return;
            }

            // Primeiro tentar recuperar a sessão
            const recovered = await authManager.recoverSession();

            if (recovered) {
              console.log("Index: Sessão recuperada com sucesso após retorno de visibilidade");

              // Disparar evento de reconexão para atualizar os componentes
              const reconnectEvent = new CustomEvent('app-reconnected', {
                detail: {
                  timestamp: Date.now(),
                  source: 'visibility-change-recovery'
                }
              });
              document.dispatchEvent(reconnectEvent);
              return;
            }

            // Se não conseguiu recuperar, verificar a sessão
            const sessionValid = await authManager.checkSession();

            if (!sessionValid) {
              console.log("Index: Sessão inválida após retorno, tentando recuperar");

              // Tentar recuperar a sessão novamente com uma abordagem mais agressiva
              const forcedRecovery = await refreshSupabaseSession();

              if (!forcedRecovery) {
                console.log("Index: Falha na recuperação forçada, redirecionando para login");
                redirectToLogin("Sessão expirada após inatividade");
              } else {
                console.log("Index: Sessão recuperada com sucesso após recuperação forçada");

                // Disparar evento de reconexão
                const reconnectEvent = new CustomEvent('app-reconnected', {
                  detail: {
                    timestamp: Date.now(),
                    source: 'forced-recovery'
                  }
                });
                document.dispatchEvent(reconnectEvent);
              }
            } else {
              console.log("Index: Sessão válida após retorno");

              // Disparar evento personalizado para notificar componentes sobre a atualização da sessão
              const sessionEvent = new CustomEvent('index-session-checked', {
                detail: {
                  timestamp: Date.now(),
                  success: true
                }
              });
              document.dispatchEvent(sessionEvent);
            }
          } catch (error) {
            console.error("Index: Erro ao verificar sessão após retorno:", error);

            // Tentar recuperar a sessão como último recurso
            try {
              const lastChanceRecovery = await refreshSupabaseSession();
              if (!lastChanceRecovery) {
                console.log("Index: Falha na recuperação de último recurso");
                // Não redirecionar automaticamente para evitar ciclos
              }
            } catch (recoveryError) {
              console.error("Index: Erro na recuperação de último recurso:", recoveryError);
            }
          }
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    async function checkAuth() {
      // Resetar contador de tentativas
      authRetryCountRef.current = 0;

      async function attemptAuth() {
        try {
          console.log(`Index: Verificando autenticação... (tentativa ${authRetryCountRef.current + 1}/${MAX_AUTH_RETRIES + 1})`);

          // Definir um timeout para evitar carregamento infinito
          if (authTimeoutRef.current) {
            window.clearTimeout(authTimeoutRef.current);
            authTimeoutRef.current = null;
          }

          // Verificar se já existe um perfil de usuário
          const userProfile = getUserProfile();

          if (userProfile) {
            console.log("Index: Perfil do usuário encontrado, tentando usar");

            // Sincronizar o perfil
            syncUserProfile();

            // Verificar a sessão atual
            const { data } = await supabase.auth.getSession();

            if (data.session) {
              console.log("Index: Sessão válida encontrada");

              // Iniciar o gerenciador de autenticação
              authManager.start();

              // Parar o carregamento
              if (isMounted) setIsLoading(false);
              return;
            }

            console.log("Index: Perfil encontrado, mas sessão inválida, tentando recuperar");

            // Tentar recuperar a sessão
            const recovered = await authManager.recoverSession();

            if (recovered) {
              console.log("Index: Sessão recuperada com sucesso");

              // Iniciar o gerenciador de autenticação
              authManager.start();

              // Parar o carregamento
              if (isMounted) setIsLoading(false);
              return;
            }
          }

          // Definir um timeout para evitar carregamento infinito
          authTimeoutRef.current = window.setTimeout(() => {
            if (isMounted && isLoading) {
              console.log("Index: Timeout de autenticação atingido");

              // Se ainda não excedeu o número máximo de tentativas, tentar novamente
              if (authRetryCountRef.current < MAX_AUTH_RETRIES) {
                authRetryCountRef.current++;
                console.log(`Index: Tentando novamente (${authRetryCountRef.current}/${MAX_AUTH_RETRIES})...`);
                attemptAuth();
              } else {
                console.log("Index: Número máximo de tentativas excedido, redirecionando para login");
                redirectToLogin("Tempo limite de autenticação excedido");
              }
            }
          }, 5000); // 5 segundos de timeout

          // Verificar se já estamos na página de login
          if (window.location.pathname === "/login") {
            if (isMounted) setIsLoading(false);
            return;
          }

          // Estratégia 1: Tentar recuperar a sessão diretamente
          console.log("Index: Tentando recuperar sessão...");
          const recovered = await authManager.recoverSession();

          if (recovered) {
            console.log("Index: Sessão recuperada com sucesso");

            // Iniciar o gerenciador de autenticação
            authManager.start();

            if (isMounted) setIsLoading(false);

            // Limpar o timeout
            if (authTimeoutRef.current) {
              window.clearTimeout(authTimeoutRef.current);
              authTimeoutRef.current = null;
            }

            return;
          }

          // Estratégia 2: Verificar se existe uma sessão válida no Supabase
          console.log("Index: Verificando sessão no Supabase...");
          const { data: sessionData, error: sessionError } = await supabase.auth.getSession();

          if (sessionError) {
            console.error("Index: Erro ao obter sessão:", sessionError);

            // Tentar atualizar a sessão diretamente
            console.log("Index: Tentando atualizar sessão após erro...");
            const refreshed = await refreshSupabaseSession();

            if (refreshed) {
              console.log("Index: Sessão atualizada com sucesso após erro");

              // Iniciar o gerenciador de autenticação
              authManager.start();

              if (isMounted) setIsLoading(false);

              // Limpar o timeout
              if (authTimeoutRef.current) {
                window.clearTimeout(authTimeoutRef.current);
                authTimeoutRef.current = null;
              }

              return;
            }

            // Se não conseguiu atualizar, verificar localStorage
            if (isMounted) checkLocalAuth();
            return;
          }

          // Se há sessão válida
          if (sessionData.session) {
            // Sessão válida encontrada
            console.log("Index: Sessão válida encontrada para:", sessionData.session.user.email);

            // Iniciar o gerenciador de autenticação
            authManager.start();

            if (isMounted) setIsLoading(false);

            // Limpar o timeout
            if (authTimeoutRef.current) {
              window.clearTimeout(authTimeoutRef.current);
              authTimeoutRef.current = null;
            }

            return;
          }

          // Se não há sessão válida, verificar localStorage como fallback
          console.log("Index: Nenhuma sessão ativa encontrada, verificando localStorage...");
          if (isMounted) checkLocalAuth();
        } catch (error) {
          console.error("Index: Erro ao verificar autenticação:", error);

          // Tentar recuperar a sessão antes de redirecionar
          try {
            console.log("Index: Tentando recuperar sessão após erro...");
            const recovered = await refreshSupabaseSession();

            if (recovered) {
              console.log("Index: Sessão recuperada com sucesso após erro");

              // Iniciar o gerenciador de autenticação
              authManager.start();

              if (isMounted) setIsLoading(false);

              // Limpar o timeout
              if (authTimeoutRef.current) {
                window.clearTimeout(authTimeoutRef.current);
                authTimeoutRef.current = null;
              }

              return;
            }
          } catch (recoverError) {
            console.error("Index: Erro ao tentar recuperar sessão:", recoverError);
          }

          // Se ainda não excedeu o número máximo de tentativas, tentar novamente
          if (authRetryCountRef.current < MAX_AUTH_RETRIES) {
            authRetryCountRef.current++;
            console.log(`Index: Tentando novamente após erro (${authRetryCountRef.current}/${MAX_AUTH_RETRIES})...`);

            // Adicionar um pequeno atraso antes de tentar novamente
            await new Promise(resolve => setTimeout(resolve, 1000));

            attemptAuth();
          } else {
            console.log("Index: Número máximo de tentativas excedido após erro, redirecionando para login");
            if (isMounted) redirectToLogin();
          }
        }
      }

      // Iniciar a primeira tentativa
      await attemptAuth();
    }

    async function checkLocalAuth() {
      // Verificar se existe perfil no localStorage
      const userProfileStr = localStorage.getItem("userProfile");

      if (!userProfileStr) {
        console.log("Index: Nenhum perfil encontrado no localStorage, tentando recuperar sessão");

        // Tentar recuperar a sessão como último recurso
        try {
          const recovered = await refreshSupabaseSession();
          if (recovered) {
            console.log("Index: Sessão recuperada com sucesso após não encontrar perfil no localStorage");

            // Iniciar o gerenciador de autenticação
            authManager.start();

            if (isMounted) setIsLoading(false);
            return;
          }
        } catch (recoverError) {
          console.error("Index: Erro ao tentar recuperar sessão:", recoverError);
        }

        redirectToLogin();
        return;
      }

      try {
        const userProfile = JSON.parse(userProfileStr);

        if (!userProfile || !userProfile.email || !userProfile.role) {
          console.log("Index: Perfil inválido no localStorage, tentando recuperar sessão");

          // Tentar recuperar a sessão como último recurso
          try {
            const recovered = await refreshSupabaseSession();
            if (recovered) {
              console.log("Index: Sessão recuperada com sucesso após encontrar perfil inválido no localStorage");

              // Iniciar o gerenciador de autenticação
              authManager.start();

              if (isMounted) setIsLoading(false);
              return;
            }
          } catch (recoverError) {
            console.error("Index: Erro ao tentar recuperar sessão:", recoverError);
          }

          localStorage.removeItem("userProfile");
          redirectToLogin();
          return;
        }

        console.log("Index: Usuário autenticado via localStorage:", userProfile.email);

        // Estratégia 1: Tentar recuperar a sessão diretamente
        try {
          console.log("Index: Tentando recuperar sessão após encontrar perfil no localStorage...");
          const recovered = await authManager.recoverSession();

          if (recovered) {
            console.log("Index: Sessão recuperada com sucesso após encontrar perfil no localStorage");

            // Iniciar o gerenciador de autenticação
            authManager.start();

            if (isMounted) setIsLoading(false);
            return;
          }
        } catch (recoverError) {
          console.error("Index: Erro ao tentar recuperar sessão:", recoverError);
        }

        // Estratégia 2: Verificar se a sessão ainda é válida tentando uma operação simples
        try {
          console.log("Index: Verificando se a sessão ainda é válida...");
          const { data, error } = await supabase.auth.getUser();

          if (error || !data.user) {
            console.log("Index: Sessão expirada ou inválida, tentando atualizar...");

            // Tentar atualizar a sessão
            const refreshed = await refreshSupabaseSession();

            if (refreshed) {
              console.log("Index: Sessão atualizada com sucesso");

              // Iniciar o gerenciador de autenticação
              authManager.start();

              if (isMounted) setIsLoading(false);
              return;
            }

            console.log("Index: Falha ao atualizar sessão, redirecionando para login");
            if (isMounted) redirectToLogin();
            return;
          }

          console.log("Index: Sessão válida encontrada");

          // Iniciar o gerenciador de autenticação
          authManager.start();

          if (isMounted) setIsLoading(false);
        } catch (error) {
          console.error("Index: Erro ao verificar sessão:", error);

          // Tentar recuperar a sessão como último recurso
          try {
            const recovered = await refreshSupabaseSession();
            if (recovered) {
              console.log("Index: Sessão recuperada com sucesso após erro ao verificar sessão");

              // Iniciar o gerenciador de autenticação
              authManager.start();

              if (isMounted) setIsLoading(false);
              return;
            }
          } catch (recoverError) {
            console.error("Index: Erro ao tentar recuperar sessão:", recoverError);
          }

          if (isMounted) redirectToLogin();
        }
      } catch (error) {
        console.error("Index: Erro ao verificar perfil local:", error);

        // Tentar recuperar a sessão como último recurso
        try {
          const recovered = await refreshSupabaseSession();
          if (recovered) {
            console.log("Index: Sessão recuperada com sucesso após erro ao verificar perfil local");

            // Iniciar o gerenciador de autenticação
            authManager.start();

            if (isMounted) setIsLoading(false);
            return;
          }
        } catch (recoverError) {
          console.error("Index: Erro ao tentar recuperar sessão:", recoverError);
        }

        localStorage.removeItem("userProfile");
        if (isMounted) redirectToLogin();
      }
    }

    // Verificar autenticação imediatamente
    checkAuth();

    // Adicionar listener para eventos de reconexão
    const handleReconnect = (event: Event) => {
      const customEvent = event as CustomEvent;
      console.log("Index: Evento de reconexão recebido:", customEvent.detail);

      // Se não estiver carregando, verificar a sessão
      if (!isLoading) {
        // Verificar a sessão após um pequeno atraso
        setTimeout(async () => {
          try {
            // Verificar se o componente ainda está montado
            if (!isMounted) return;

            console.log("Index: Verificando sessão após evento de reconexão");
            const sessionValid = await authManager.checkSession();

            if (!sessionValid) {
              console.log("Index: Sessão inválida após evento de reconexão, tentando recuperar");

              // Tentar recuperar a sessão
              const recovered = await refreshSupabaseSession();

              if (!recovered) {
                console.log("Index: Falha na recuperação após evento de reconexão");
                // Não redirecionar automaticamente para evitar ciclos
              }
            } else {
              console.log("Index: Sessão válida após evento de reconexão");
            }
          } catch (error) {
            console.error("Index: Erro ao verificar sessão após evento de reconexão:", error);
          }
        }, 500);
      }
    };

    document.addEventListener('app-reconnected', handleReconnect);
    document.addEventListener('supabase-session-refreshed', handleReconnect);
    document.addEventListener('auth-session-recovered', handleReconnect);

    // Cleanup function
    return () => {
      isMounted = false;
      if (authTimeoutRef.current) {
        window.clearTimeout(authTimeoutRef.current);
        authTimeoutRef.current = null;
      }
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      document.removeEventListener('app-reconnected', handleReconnect);
      document.removeEventListener('supabase-session-refreshed', handleReconnect);
      document.removeEventListener('auth-session-recovered', handleReconnect);
    };
  }, [navigate, isLoading]);

  // Mostrar indicador de carregamento enquanto verifica autenticação
  if (isLoading) {
    return (
      <>
        {/* Add TabReloader to handle tab switching */}
        <TabReloader />

        <div className="flex items-center justify-center h-screen">
          <FullScreenLoading text="Carregando..." />
        </div>
      </>
    );
  }



  return (
    <>
      {/* Add TabReloader to handle tab switching */}
      <TabReloader />

      <div className="relative w-full min-h-screen bg-gradient-to-br from-neutral-50 to-neutral-100 dark:from-neutral-900 dark:to-black">
        <main className="relative min-h-[calc(100vh-6rem)] mb-16">
          <div className="min-h-full">
            <Outlet />
          </div>
        </main>

        <DockNav />

        {/* Componente de reconexão automática */}
        <AutoReconnect floating={true} />
      </div>
    </>
  );
};

export default Index;