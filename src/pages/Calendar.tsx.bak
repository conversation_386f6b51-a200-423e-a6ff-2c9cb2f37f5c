import { useState, useMemo, useRef, useEffect } from "react";
import { format, startOfMonth, endOfMonth, eachDayOfInterval, isSameMonth, isToday, parseISO, addMonths, subMonths, addWeeks, subWeeks, addDays, subDays, isSameDay, startOfWeek, endOfWeek, addMinutes, getDay, getDate, isWithinInterval, startOfDay, endOfDay, differenceInMinutes, addHours, isBefore, isAfter } from "date-fns";
import { ptBR } from "date-fns/locale";
import {
  Calendar as CalendarIcon2,
  ChevronLeft,
  ChevronRight,
  Plus,
  Filter,
  Clock,
  User,
  MapPin,
  FileText,
  CheckCircle2,
  Circle,
  AlertCircle,
  MoreVertical,
  Banknote,
  Users2,
  Building2,
  Settings,
  Download,
  Upload,
  Trash2,
  Edit,
  Share2,
  Bell,
  ChevronDown,
  BarChart3,
  Clock3,
  CalendarDays,
  List,
  Repeat,
  BellRing,
  FileDown,
  Smartphone
} from "lucide-react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { cn } from "@/lib/utils";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Calendar as CalendarPicker } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";

// Dados mockados para eventos
const eventos = [
  {
    id: 1,
    tipo: "precatorio",
    titulo: "Análise de Precatório",
    descricao: "Análise inicial do precatório nº 2024.001.123",
    data: "2024-03-20",
    hora: "14:00",
    duracao: 60,
    status: "pendente",
    prioridade: "alta",
    local: "Escritório - Sala 3",
    responsavel: {
      nome: "Ana Silva",
      avatar: "/avatars/ana.jpg"
    },
    precatorio: {
      numero: "2024.001.123",
      beneficiario: "João da Silva",
      valor: 250000,
      tipo: "Alimentar"
    }
  },
  {
    id: 2,
    tipo: "audiencia",
    titulo: "Audiência de Conciliação",
    descricao: "Audiência referente ao precatório nº 2024.001.456",
    data: "2024-03-21",
    hora: "10:00",
    duracao: 120,
    status: "confirmado",
    prioridade: "alta",
    local: "Fórum Central - Sala 405",
    responsavel: {
      nome: "Carlos Santos",
      avatar: "/avatars/carlos.jpg"
    },
    precatorio: {
      numero: "2024.001.456",
      beneficiario: "Maria Oliveira",
      valor: 180000,
      tipo: "Comum"
    }
  },
  {
    id: 3,
    tipo: "reuniao",
    titulo: "Reunião com Cliente",
    descricao: "Discussão sobre andamento do precatório",
    data: "2024-03-22",
    hora: "15:30",
    duracao: 60,
    status: "pendente",
    prioridade: "media",
    local: "Escritório - Sala de Reuniões",
    responsavel: {
      nome: "Maria Costa",
      avatar: "/avatars/maria.jpg"
    },
    precatorio: {
      numero: "2024.001.789",
      beneficiario: "Pedro Santos",
      valor: 320000,
      tipo: "Preferencial"
    }
  },
  {
    id: 4,
    tipo: "prazo",
    titulo: "Prazo Final - Recurso",
    descricao: "Prazo final para apresentação do recurso",
    data: "2024-03-25",
    hora: "18:00",
    duracao: 30,
    status: "pendente",
    prioridade: "urgente",
    local: "Tribunal Regional",
    responsavel: {
      nome: "Ana Silva",
      avatar: "/avatars/ana.jpg"
    }
  },
  {
    id: 5,
    tipo: "despacho",
    titulo: "Despacho com Juiz",
    descricao: "Despacho sobre processo urgente",
    data: "2024-03-23",
    hora: "11:00",
    duracao: 45,
    status: "confirmado",
    prioridade: "alta",
    local: "Gabinete do Juiz - Sala 202",
    responsavel: {
      nome: "Carlos Santos",
      avatar: "/avatars/carlos.jpg"
    }
  },
  {
    id: 6,
    tipo: "precatorio",
    titulo: "Atualização de Cálculos",
    descricao: "Atualização dos cálculos do precatório",
    data: "2024-03-24",
    hora: "09:00",
    duracao: 180,
    status: "emAndamento",
    prioridade: "media",
    local: "Escritório - Setor Financeiro",
    responsavel: {
      nome: "Maria Costa",
      avatar: "/avatars/maria.jpg"
    },
    precatorio: {
      numero: "2024.001.234",
      beneficiario: "Roberto Almeida",
      valor: 450000,
      tipo: "Comum"
    }
  },
  {
    id: 7,
    tipo: "audiencia",
    titulo: "Audiência de Instrução",
    descricao: "Audiência de instrução e julgamento",
    data: "2024-03-26",
    hora: "14:30",
    duracao: 150,
    status: "confirmado",
    prioridade: "alta",
    local: "Fórum - Sala 508",
    responsavel: {
      nome: "Carlos Santos",
      avatar: "/avatars/carlos.jpg"
    }
  },
  {
    id: 8,
    tipo: "reuniao",
    titulo: "Reunião de Equipe",
    descricao: "Alinhamento semanal da equipe",
    data: "2024-03-22",
    hora: "09:00",
    duracao: 60,
    status: "confirmado",
    prioridade: "baixa",
    local: "Escritório - Sala de Reuniões",
    responsavel: {
      nome: "Ana Silva",
      avatar: "/avatars/ana.jpg"
    }
  },
  {
    id: 9,
    tipo: "despacho",
    titulo: "Despacho Processual",
    descricao: "Análise e despacho de processos pendentes",
    data: "2024-03-27",
    hora: "10:00",
    duracao: 120,
    status: "pendente",
    prioridade: "media",
    local: "Escritório - Gabinete",
    responsavel: {
      nome: "Maria Costa",
      avatar: "/avatars/maria.jpg"
    }
  },
  {
    id: 10,
    tipo: "prazo",
    titulo: "Prazo - Manifestação",
    descricao: "Prazo para manifestação em processo",
    data: "2024-03-28",
    hora: "17:00",
    duracao: 30,
    status: "pendente",
    prioridade: "alta",
    local: "Tribunal",
    responsavel: {
      nome: "Carlos Santos",
      avatar: "/avatars/carlos.jpg"
    }
  },
  // Novos eventos para teste
  {
    id: 11,
    tipo: "precatorio",
    titulo: "Análise de Precatório Preferencial",
    descricao: "Análise detalhada do precatório preferencial nº 2024.002.345",
    data: "2024-04-02",
    hora: "10:30",
    duracao: 90,
    status: "pendente",
    prioridade: "urgente",
    local: "Escritório - Sala 5",
    responsavel: {
      nome: "Ana Silva",
      avatar: "/avatars/ana.jpg"
    },
    precatorio: {
      numero: "2024.002.345",
      beneficiario: "Antônio Ferreira",
      valor: 520000,
      tipo: "Preferencial"
    }
  },
  {
    id: 12,
    tipo: "audiencia",
    titulo: "Audiência Virtual",
    descricao: "Audiência virtual referente ao processo nº 0023456-78.2024.8.26.0100",
    data: "2024-04-03",
    hora: "14:00",
    duracao: 60,
    status: "confirmado",
    prioridade: "alta",
    local: "Online - Sala Virtual 3",
    responsavel: {
      nome: "Carlos Santos",
      avatar: "/avatars/carlos.jpg"
    }
  },
  {
    id: 13,
    tipo: "reuniao",
    titulo: "Reunião com Contador",
    descricao: "Discussão sobre cálculos de precatórios em andamento",
    data: "2024-04-04",
    hora: "11:00",
    duracao: 120,
    status: "confirmado",
    prioridade: "media",
    local: "Escritório - Sala de Reuniões 2",
    responsavel: {
      nome: "Maria Costa",
      avatar: "/avatars/maria.jpg"
    }
  },
  {
    id: 14,
    tipo: "prazo",
    titulo: "Prazo - Embargos",
    descricao: "Prazo final para apresentação de embargos",
    data: "2024-04-05",
    hora: "23:59",
    duracao: 30,
    status: "pendente",
    prioridade: "urgente",
    local: "Tribunal Regional Federal",
    responsavel: {
      nome: "Ana Silva",
      avatar: "/avatars/ana.jpg"
    }
  },
  {
    id: 15,
    tipo: "despacho",
    titulo: "Despacho com Desembargador",
    descricao: "Despacho sobre recurso pendente",
    data: "2024-04-06",
    hora: "09:30",
    duracao: 45,
    status: "confirmado",
    prioridade: "alta",
    local: "Tribunal - Gabinete 405",
    responsavel: {
      nome: "Carlos Santos",
      avatar: "/avatars/carlos.jpg"
    }
  },
  {
    id: 16,
    tipo: "precatorio",
    titulo: "Revisão de Cálculos",
    descricao: "Revisão dos cálculos do precatório nº 2024.001.567",
    data: "2024-04-08",
    hora: "13:00",
    duracao: 150,
    status: "pendente",
    prioridade: "media",
    local: "Escritório - Setor Financeiro",
    responsavel: {
      nome: "Maria Costa",
      avatar: "/avatars/maria.jpg"
    },
    precatorio: {
      numero: "2024.001.567",
      beneficiario: "Juliana Mendes",
      valor: 380000,
      tipo: "Comum"
    }
  },
  {
    id: 17,
    tipo: "reuniao",
    titulo: "Reunião com Procuradoria",
    descricao: "Discussão sobre acordo em precatórios",
    data: "2024-04-10",
    hora: "15:00",
    duracao: 90,
    status: "confirmado",
    prioridade: "alta",
    local: "Procuradoria Geral - Sala 302",
    responsavel: {
      nome: "Carlos Santos",
      avatar: "/avatars/carlos.jpg"
    }
  },
  {
    id: 18,
    tipo: "audiencia",
    titulo: "Audiência de Conciliação",
    descricao: "Tentativa de acordo no processo nº 0034567-89.2024.8.26.0100",
    data: "2024-04-12",
    hora: "10:00",
    duracao: 120,
    status: "confirmado",
    prioridade: "media",
    local: "CEJUSC - Sala 5",
    responsavel: {
      nome: "Ana Silva",
      avatar: "/avatars/ana.jpg"
    }
  },
  {
    id: 19,
    tipo: "prazo",
    titulo: "Prazo - Recurso Especial",
    descricao: "Prazo final para interposição de recurso especial",
    data: "2024-04-15",
    hora: "18:00",
    duracao: 30,
    status: "pendente",
    prioridade: "urgente",
    local: "STJ",
    responsavel: {
      nome: "Carlos Santos",
      avatar: "/avatars/carlos.jpg"
    }
  },
  {
    id: 20,
    tipo: "despacho",
    titulo: "Despacho com Ministro",
    descricao: "Despacho sobre processo em tramitação no STF",
    data: "2024-04-18",
    hora: "11:30",
    duracao: 60,
    status: "pendente",
    prioridade: "alta",
    local: "STF - Gabinete 103",
    responsavel: {
      nome: "Maria Costa",
      avatar: "/avatars/maria.jpg"
    }
  },
  {
    id: 21,
    tipo: "precatorio",
    titulo: "Pagamento de Precatório",
    descricao: "Acompanhamento do pagamento do precatório nº 2023.005.789",
    data: "2024-04-20",
    hora: "09:00",
    duracao: 120,
    status: "confirmado",
    prioridade: "alta",
    local: "Banco Central - Setor de Pagamentos",
    responsavel: {
      nome: "Ana Silva",
      avatar: "/avatars/ana.jpg"
    },
    precatorio: {
      numero: "2023.005.789",
      beneficiario: "Ricardo Souza",
      valor: 750000,
      tipo: "Alimentar"
    }
  },
  {
    id: 22,
    tipo: "reuniao",
    titulo: "Reunião Estratégica",
    descricao: "Planejamento de estratégias para novos processos",
    data: "2024-04-22",
    hora: "14:00",
    duracao: 180,
    status: "confirmado",
    prioridade: "media",
    local: "Escritório - Sala de Conferências",
    responsavel: {
      nome: "Carlos Santos",
      avatar: "/avatars/carlos.jpg"
    }
  },
  {
    id: 23,
    tipo: "audiencia",
    titulo: "Audiência com Perito",
    descricao: "Esclarecimentos sobre laudo pericial",
    data: "2024-04-24",
    hora: "15:30",
    duracao: 90,
    status: "pendente",
    prioridade: "media",
    local: "Fórum - Sala 201",
    responsavel: {
      nome: "Maria Costa",
      avatar: "/avatars/maria.jpg"
    }
  },
  {
    id: 24,
    tipo: "prazo",
    titulo: "Prazo - Contrarrazões",
    descricao: "Prazo final para apresentação de contrarrazões",
    data: "2024-04-26",
    hora: "23:59",
    duracao: 30,
    status: "pendente",
    prioridade: "alta",
    local: "Tribunal de Justiça",
    responsavel: {
      nome: "Ana Silva",
      avatar: "/avatars/ana.jpg"
    }
  },
  {
    id: 25,
    tipo: "despacho",
    titulo: "Despacho Administrativo",
    descricao: "Despacho sobre questões administrativas de precatórios",
    data: "2024-04-28",
    hora: "10:00",
    duracao: 60,
    status: "confirmado",
    prioridade: "baixa",
    local: "Escritório - Sala da Diretoria",
    responsavel: {
      nome: "Carlos Santos",
      avatar: "/avatars/carlos.jpg"
    }
  },
  {
    id: 26,
    tipo: "precatorio",
    titulo: "Análise de Sequestro de Valores",
    descricao: "Análise de pedido de sequestro de valores em precatório",
    data: "2024-04-30",
    hora: "13:30",
    duracao: 120,
    status: "pendente",
    prioridade: "urgente",
    local: "Escritório - Sala 7",
    responsavel: {
      nome: "Maria Costa",
      avatar: "/avatars/maria.jpg"
    },
    precatorio: {
      numero: "2024.003.456",
      beneficiario: "Fernanda Lima",
      valor: 420000,
      tipo: "Preferencial"
    }
  },
  {
    id: 27,
    tipo: "reuniao",
    titulo: "Reunião com Associação de Credores",
    descricao: "Discussão sobre estratégias coletivas",
    data: "2024-05-02",
    hora: "16:00",
    duracao: 120,
    status: "confirmado",
    prioridade: "media",
    local: "Associação - Auditório Principal",
    responsavel: {
      nome: "Ana Silva",
      avatar: "/avatars/ana.jpg"
    }
  },
  {
    id: 28,
    tipo: "audiencia",
    titulo: "Audiência Pública",
    descricao: "Audiência pública sobre pagamento de precatórios",
    data: "2024-05-05",
    hora: "09:00",
    duracao: 240,
    status: "confirmado",
    prioridade: "alta",
    local: "Assembleia Legislativa - Plenário",
    responsavel: {
      nome: "Carlos Santos",
      avatar: "/avatars/carlos.jpg"
    }
  },
  {
    id: 29,
    tipo: "prazo",
    titulo: "Prazo - Agravo",
    descricao: "Prazo final para interposição de agravo",
    data: "2024-05-08",
    hora: "18:00",
    duracao: 30,
    status: "pendente",
    prioridade: "urgente",
    local: "Tribunal Regional",
    responsavel: {
      nome: "Maria Costa",
      avatar: "/avatars/maria.jpg"
    }
  },
  {
    id: 30,
    tipo: "despacho",
    titulo: "Despacho Final",
    descricao: "Despacho final sobre processo de precatório",
    data: "2024-05-10",
    hora: "11:00",
    duracao: 45,
    status: "pendente",
    prioridade: "alta",
    local: "Tribunal - Gabinete 505",
    responsavel: {
      nome: "Ana Silva",
      avatar: "/avatars/ana.jpg"
    }
  },
  // Eventos para o mês atual (junho/julho 2024)
  {
    id: 31,
    tipo: "precatorio",
    titulo: "Análise de Precatório Urgente",
    descricao: "Análise de precatório com prioridade máxima",
    data: "2024-06-10",
    hora: "09:30",
    duracao: 120,
    status: "confirmado",
    prioridade: "urgente",
    local: "Escritório - Sala Principal",
    responsavel: {
      nome: "Ana Silva",
      avatar: "/avatars/ana.jpg"
    },
    precatorio: {
      numero: "2024.006.123",
      beneficiario: "Luiz Henrique Mendes",
      valor: 680000,
      tipo: "Alimentar"
    }
  },
  {
    id: 32,
    tipo: "audiencia",
    titulo: "Audiência de Conciliação",
    descricao: "Tentativa de acordo em processo de precatório",
    data: "2024-06-12",
    hora: "14:00",
    duracao: 90,
    status: "confirmado",
    prioridade: "alta",
    local: "Fórum Central - Sala 302",
    responsavel: {
      nome: "Carlos Santos",
      avatar: "/avatars/carlos.jpg"
    }
  },
  {
    id: 33,
    tipo: "reuniao",
    titulo: "Reunião com Advogados",
    descricao: "Alinhamento de estratégias processuais",
    data: "2024-06-14",
    hora: "10:00",
    duracao: 120,
    status: "pendente",
    prioridade: "media",
    local: "Escritório - Sala de Reuniões",
    responsavel: {
      nome: "Maria Costa",
      avatar: "/avatars/maria.jpg"
    }
  },
  {
    id: 34,
    tipo: "prazo",
    titulo: "Prazo - Recurso Extraordinário",
    descricao: "Prazo final para interposição de recurso extraordinário",
    data: "2024-06-15",
    hora: "23:59",
    duracao: 30,
    status: "pendente",
    prioridade: "urgente",
    local: "STF",
    responsavel: {
      nome: "Ana Silva",
      avatar: "/avatars/ana.jpg"
    }
  },
  {
    id: 35,
    tipo: "despacho",
    titulo: "Despacho com Juiz Federal",
    descricao: "Despacho sobre processo de precatório federal",
    data: "2024-06-17",
    hora: "11:30",
    duracao: 60,
    status: "confirmado",
    prioridade: "alta",
    local: "Justiça Federal - Gabinete 205",
    responsavel: {
      nome: "Carlos Santos",
      avatar: "/avatars/carlos.jpg"
    }
  },
  {
    id: 36,
    tipo: "precatorio",
    titulo: "Pagamento de Precatório Preferencial",
    descricao: "Acompanhamento de pagamento de precatório preferencial",
    data: "2024-06-20",
    hora: "14:30",
    duracao: 90,
    status: "confirmado",
    prioridade: "alta",
    local: "Banco Central - Setor de Pagamentos",
    responsavel: {
      nome: "Maria Costa",
      avatar: "/avatars/maria.jpg"
    },
    precatorio: {
      numero: "2023.012.456",
      beneficiario: "Amanda Oliveira",
      valor: 520000,
      tipo: "Preferencial"
    }
  },
  {
    id: 37,
    tipo: "reuniao",
    titulo: "Reunião de Planejamento",
    descricao: "Planejamento mensal da equipe",
    data: "2024-06-21",
    hora: "09:00",
    duracao: 180,
    status: "confirmado",
    prioridade: "media",
    local: "Escritório - Auditório",
    responsavel: {
      nome: "Ana Silva",
      avatar: "/avatars/ana.jpg"
    }
  },
  {
    id: 38,
    tipo: "audiencia",
    titulo: "Audiência com Perito Contábil",
    descricao: "Esclarecimentos sobre cálculos de precatório",
    data: "2024-06-24",
    hora: "10:30",
    duracao: 120,
    status: "pendente",
    prioridade: "media",
    local: "Fórum - Sala 105",
    responsavel: {
      nome: "Carlos Santos",
      avatar: "/avatars/carlos.jpg"
    }
  },
  {
    id: 39,
    tipo: "prazo",
    titulo: "Prazo - Impugnação",
    descricao: "Prazo final para impugnação de cálculos",
    data: "2024-06-25",
    hora: "18:00",
    duracao: 30,
    status: "pendente",
    prioridade: "alta",
    local: "Tribunal Regional Federal",
    responsavel: {
      nome: "Maria Costa",
      avatar: "/avatars/maria.jpg"
    }
  },
  {
    id: 40,
    tipo: "despacho",
    titulo: "Despacho Administrativo",
    descricao: "Despacho sobre questões internas",
    data: "2024-06-27",
    hora: "15:00",
    duracao: 45,
    status: "confirmado",
    prioridade: "baixa",
    local: "Escritório - Sala da Diretoria",
    responsavel: {
      nome: "Ana Silva",
      avatar: "/avatars/ana.jpg"
    }
  },
  {
    id: 41,
    tipo: "precatorio",
    titulo: "Análise de Novo Precatório",
    descricao: "Análise inicial de precatório recém-recebido",
    data: "2024-06-28",
    hora: "11:00",
    duracao: 120,
    status: "pendente",
    prioridade: "media",
    local: "Escritório - Sala 4",
    responsavel: {
      nome: "Carlos Santos",
      avatar: "/avatars/carlos.jpg"
    },
    precatorio: {
      numero: "2024.006.789",
      beneficiario: "Roberto Carlos Mendes",
      valor: 320000,
      tipo: "Comum"
    }
  },
  {
    id: 42,
    tipo: "reuniao",
    titulo: "Reunião com Cliente VIP",
    descricao: "Atualização sobre andamento de precatório de alto valor",
    data: "2024-06-30",
    hora: "16:00",
    duracao: 90,
    status: "confirmado",
    prioridade: "alta",
    local: "Escritório - Sala de Reuniões Executiva",
    responsavel: {
      nome: "Maria Costa",
      avatar: "/avatars/maria.jpg"
    }
  },
  {
    id: 43,
    tipo: "audiencia",
    titulo: "Audiência de Instrução",
    descricao: "Audiência de instrução em processo relacionado a precatório",
    data: "2024-07-02",
    hora: "09:30",
    duracao: 180,
    status: "confirmado",
    prioridade: "alta",
    local: "Fórum - Sala 405",
    responsavel: {
      nome: "Ana Silva",
      avatar: "/avatars/ana.jpg"
    }
  },
  {
    id: 44,
    tipo: "prazo",
    titulo: "Prazo - Embargos de Declaração",
    descricao: "Prazo final para embargos de declaração",
    data: "2024-07-05",
    hora: "23:59",
    duracao: 30,
    status: "pendente",
    prioridade: "urgente",
    local: "Tribunal de Justiça",
    responsavel: {
      nome: "Carlos Santos",
      avatar: "/avatars/carlos.jpg"
    }
  },
  {
    id: 45,
    tipo: "despacho",
    titulo: "Despacho com Presidente do Tribunal",
    descricao: "Despacho sobre questão de ordem em precatório",
    data: "2024-07-08",
    hora: "10:00",
    duracao: 60,
    status: "pendente",
    prioridade: "alta",
    local: "Tribunal - Gabinete da Presidência",
    responsavel: {
      nome: "Maria Costa",
      avatar: "/avatars/maria.jpg"
    }
  },
  {
    id: 46,
    tipo: "precatorio",
    titulo: "Atualização de Valores",
    descricao: "Atualização monetária de precatórios",
    data: "2024-07-10",
    hora: "14:00",
    duracao: 150,
    status: "confirmado",
    prioridade: "media",
    local: "Escritório - Setor Financeiro",
    responsavel: {
      nome: "Ana Silva",
      avatar: "/avatars/ana.jpg"
    },
    precatorio: {
      numero: "2024.002.345",
      beneficiario: "Marcelo Alves",
      valor: 450000,
      tipo: "Comum"
    }
  },
  {
    id: 47,
    tipo: "reuniao",
    titulo: "Reunião com Procuradoria",
    descricao: "Negociação de acordo em precatórios",
    data: "2024-07-12",
    hora: "11:30",
    duracao: 120,
    status: "confirmado",
    prioridade: "alta",
    local: "Procuradoria Geral - Sala 202",
    responsavel: {
      nome: "Carlos Santos",
      avatar: "/avatars/carlos.jpg"
    }
  },
  {
    id: 48,
    tipo: "audiencia",
    titulo: "Audiência Virtual",
    descricao: "Audiência online para tentativa de conciliação",
    data: "2024-07-15",
    hora: "15:00",
    duracao: 90,
    status: "pendente",
    prioridade: "media",
    local: "Online - Sala Virtual 2",
    responsavel: {
      nome: "Maria Costa",
      avatar: "/avatars/maria.jpg"
    }
  },
  {
    id: 49,
    tipo: "prazo",
    titulo: "Prazo - Agravo Interno",
    descricao: "Prazo final para agravo interno",
    data: "2024-07-18",
    hora: "18:00",
    duracao: 30,
    status: "pendente",
    prioridade: "alta",
    local: "Tribunal Superior",
    responsavel: {
      nome: "Ana Silva",
      avatar: "/avatars/ana.jpg"
    }
  },
  {
    id: 50,
    tipo: "despacho",
    titulo: "Despacho Final",
    descricao: "Despacho final em processo de precatório",
    data: "2024-07-20",
    hora: "09:00",
    duracao: 45,
    status: "confirmado",
    prioridade: "alta",
    local: "Tribunal - Gabinete 305",
    responsavel: {
      nome: "Carlos Santos",
      avatar: "/avatars/carlos.jpg"
    }
  },
  {
    id: 51,
    tipo: "precatorio",
    titulo: "Análise de Precatório Urgente",
    descricao: "Análise de precatório com prioridade máxima - Caso Saúde",
    data: "2024-08-05",
    hora: "09:00",
    duracao: 120,
    status: "pendente",
    prioridade: "urgente",
    local: "Escritório - Sala Principal",
    responsavel: {
      nome: "Ana Silva",
      avatar: "/avatars/ana.jpg"
    },
    precatorio: {
      numero: "2024.008.123",
      beneficiario: "Clara Santos",
      valor: 890000,
      tipo: "Alimentar"
    }
  },
  {
    id: 52,
    tipo: "audiencia",
    titulo: "Audiência Virtual Coletiva",
    descricao: "Audiência virtual para múltiplos precatórios da mesma natureza",
    data: "2024-08-08",
    hora: "14:00",
    duracao: 180,
    status: "confirmado",
    prioridade: "alta",
    local: "Sala Virtual 5",
    responsavel: {
      nome: "Carlos Santos",
      avatar: "/avatars/carlos.jpg"
    }
  },
  {
    id: 53,
    tipo: "reuniao",
    titulo: "Reunião com Defensoria Pública",
    descricao: "Discussão sobre acordos em precatórios de saúde",
    data: "2024-08-12",
    hora: "10:00",
    duracao: 120,
    status: "confirmado",
    prioridade: "media",
    local: "Defensoria Pública - Auditório",
    responsavel: {
      nome: "Maria Costa",
      avatar: "/avatars/maria.jpg"
    }
  },
  {
    id: 54,
    tipo: "prazo",
    titulo: "Prazo Final - Recursos Múltiplos",
    descricao: "Prazo final para recursos em 5 processos relacionados",
    data: "2024-08-15",
    hora: "23:59",
    duracao: 30,
    status: "pendente",
    prioridade: "urgente",
    local: "Tribunal Regional Federal",
    responsavel: {
      nome: "Ana Silva",
      avatar: "/avatars/ana.jpg"
    }
  },
  {
    id: 55,
    tipo: "despacho",
    titulo: "Despacho com Presidente do TRF",
    descricao: "Despacho sobre precatórios de grande valor",
    data: "2024-08-20",
    hora: "11:00",
    duracao: 60,
    status: "confirmado",
    prioridade: "alta",
    local: "Gabinete da Presidência - TRF",
    responsavel: {
      nome: "Carlos Santos",
      avatar: "/avatars/carlos.jpg"
    }
  },
  {
    id: 56,
    tipo: "precatorio",
    titulo: "Análise de Precatórios Educacionais",
    descricao: "Análise conjunta de precatórios do setor educacional",
    data: "2024-09-03",
    hora: "09:30",
    duracao: 240,
    status: "pendente",
    prioridade: "alta",
    local: "Escritório - Sala de Reuniões",
    responsavel: {
      nome: "Maria Costa",
      avatar: "/avatars/maria.jpg"
    },
    precatorio: {
      numero: "2024.009.456",
      beneficiario: "Sindicato dos Professores",
      valor: 1250000,
      tipo: "Comum"
    }
  },
  {
    id: 57,
    tipo: "audiencia",
    titulo: "Audiência de Conciliação em Massa",
    descricao: "Tentativa de acordo para múltiplos precatórios",
    data: "2024-09-10",
    hora: "13:00",
    duracao: 300,
    status: "confirmado",
    prioridade: "alta",
    local: "Fórum Central - Auditório Principal",
    responsavel: {
      nome: "Ana Silva",
      avatar: "/avatars/ana.jpg"
    }
  },
  {
    id: 58,
    tipo: "reuniao",
    titulo: "Workshop sobre Novos Procedimentos",
    descricao: "Treinamento da equipe sobre novas regras de precatórios",
    data: "2024-09-15",
    hora: "09:00",
    duracao: 480,
    status: "confirmado",
    prioridade: "media",
    local: "Centro de Treinamento",
    responsavel: {
      nome: "Carlos Santos",
      avatar: "/avatars/carlos.jpg"
    }
  },
  {
    id: 59,
    tipo: "prazo",
    titulo: "Prazo - Manifestações Diversas",
    descricao: "Prazo para manifestação em múltiplos processos",
    data: "2024-09-20",
    hora: "18:00",
    duracao: 30,
    status: "pendente",
    prioridade: "alta",
    local: "Tribunal de Justiça",
    responsavel: {
      nome: "Maria Costa",
      avatar: "/avatars/maria.jpg"
    }
  },
  {
    id: 60,
    tipo: "despacho",
    titulo: "Despacho Conjunto",
    descricao: "Despacho com múltiplos juízes sobre precatórios",
    data: "2024-09-25",
    hora: "14:30",
    duracao: 180,
    status: "confirmado",
    prioridade: "alta",
    local: "Tribunal - Sala de Reuniões",
    responsavel: {
      nome: "Ana Silva",
      avatar: "/avatars/ana.jpg"
    }
  },
  {
    id: 61,
    tipo: "precatorio",
    titulo: "Mutirão de Análise",
    descricao: "Análise intensiva de precatórios acumulados",
    data: "2024-10-01",
    hora: "08:00",
    duracao: 480,
    status: "pendente",
    prioridade: "alta",
    local: "Escritório - Todos os Andares",
    responsavel: {
      nome: "Carlos Santos",
      avatar: "/avatars/carlos.jpg"
    },
    precatorio: {
      numero: "2024.010.789",
      beneficiario: "Múltiplos Beneficiários",
      valor: 2500000,
      tipo: "Misto"
    }
  },
  {
    id: 62,
    tipo: "audiencia",
    titulo: "Audiência Pública sobre Precatórios",
    descricao: "Discussão pública sobre pagamento de precatórios",
    data: "2024-10-05",
    hora: "10:00",
    duracao: 240,
    status: "confirmado",
    prioridade: "alta",
    local: "Assembleia Legislativa",
    responsavel: {
      nome: "Maria Costa",
      avatar: "/avatars/maria.jpg"
    }
  },
  {
    id: 63,
    tipo: "reuniao",
    titulo: "Planejamento Estratégico 2025",
    descricao: "Definição de estratégias para o próximo ano",
    data: "2024-10-10",
    hora: "09:00",
    duracao: 360,
    status: "confirmado",
    prioridade: "alta",
    local: "Hotel Convention Center",
    responsavel: {
      nome: "Ana Silva",
      avatar: "/avatars/ana.jpg"
    }
  },
  {
    id: 64,
    tipo: "prazo",
    titulo: "Prazo - Prestação de Contas",
    descricao: "Prazo final para prestação de contas anual",
    data: "2024-10-15",
    hora: "17:00",
    duracao: 60,
    status: "pendente",
    prioridade: "urgente",
    local: "Escritório",
    responsavel: {
      nome: "Carlos Santos",
      avatar: "/avatars/carlos.jpg"
    }
  },
  {
    id: 65,
    tipo: "despacho",
    titulo: "Despacho de Encerramento",
    descricao: "Despacho final sobre processos do ano",
    data: "2024-10-20",
    hora: "15:00",
    duracao: 120,
    status: "pendente",
    prioridade: "alta",
    local: "Tribunal - Gabinete Principal",
    responsavel: {
      nome: "Maria Costa",
      avatar: "/avatars/maria.jpg"
    }
  }
];

// Atualizar configuração de cores para eventos
const eventColors = {
  precatorio: {
    light: "bg-blue-100 border-blue-200 text-blue-700",
    dark: "dark:bg-blue-900/20 dark:border-blue-800 dark:text-blue-300"
  },
  audiencia: {
    light: "bg-green-100 border-green-200 text-green-700",
    dark: "dark:bg-green-900/20 dark:border-green-800 dark:text-green-300"
  },
  reuniao: {
    light: "bg-purple-100 border-purple-200 text-purple-700",
    dark: "dark:bg-purple-900/20 dark:border-purple-800 dark:text-purple-300"
  },
  prazo: {
    light: "bg-red-100 border-red-200 text-red-700",
    dark: "dark:bg-red-900/20 dark:border-red-800 dark:text-red-300"
  },
  despacho: {
    light: "bg-orange-100 border-orange-200 text-orange-700",
    dark: "dark:bg-orange-900/20 dark:border-orange-800 dark:text-orange-300"
  }
};

// Atualizar configuração de cores para tipos de evento
const tipoEventoConfig = {
  precatorio: {
    cor: "bg-blue-500",
    bgClass: "bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800",
    icone: Banknote,
    label: "Precatório"
  },
  audiencia: {
    cor: "bg-purple-500",
    bgClass: "bg-purple-50 dark:bg-purple-900/20 border-purple-200 dark:border-purple-800",
    icone: Users2,
    label: "Audiência"
  },
  reuniao: {
    cor: "bg-amber-500",
    bgClass: "bg-amber-50 dark:bg-amber-900/20 border-amber-200 dark:border-amber-800",
    icone: Building2,
    label: "Reunião"
  },
  prazo: {
    cor: "bg-red-500",
    bgClass: "bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800",
    icone: Clock,
    label: "Prazo"
  },
  despacho: {
    cor: "bg-green-500",
    bgClass: "bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800",
    icone: FileText,
    label: "Despacho"
  }
};

// Atualizar configuração de cores para status
const statusConfig = {
  pendente: {
    cor: "bg-white text-neutral-800 border-neutral-200",
    icone: Circle,
    label: "Pendente"
  },
  confirmado: {
    cor: "bg-white text-neutral-800 border-neutral-200",
    icone: CheckCircle2,
    label: "Confirmado"
  },
  cancelado: {
    cor: "bg-white text-neutral-800 border-neutral-200",
    icone: AlertCircle,
    label: "Cancelado"
  },
  emAndamento: {
    cor: "bg-white text-neutral-800 border-neutral-200",
    icone: Clock3,
    label: "Em Andamento"
  },
  concluido: {
    cor: "bg-white text-neutral-800 border-neutral-200",
    icone: CheckCircle2,
    label: "Concluído"
  }
};

// Atualizar configuração de cores para prioridades
const prioridadeConfig = {
  baixa: {
    cor: "bg-green-100 text-green-800 border-green-200",
    label: "Baixa"
  },
  media: {
    cor: "bg-yellow-100 text-yellow-800 border-yellow-200",
    label: "Média"
  },
  alta: {
    cor: "bg-orange-100 text-orange-800 border-orange-200",
    label: "Alta"
  },
  urgente: {
    cor: "bg-red-100 text-red-800 border-red-200",
    label: "Urgente"
  }
};

export default function Calendar() {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [view, setView] = useState<"dia" | "semana" | "mes" | "agenda">("mes");
  const [showIntegrationDialog, setShowIntegrationDialog] = useState(false);
  const [showEventDialog, setShowEventDialog] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<typeof eventos[0] | null>(null);
  const [showStats, setShowStats] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showFilterDialog, setShowFilterDialog] = useState(false);
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [showReminderDialog, setShowReminderDialog] = useState(false);
  const [draggedEvent, setDraggedEvent] = useState<typeof eventos[0] | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [filtros, setFiltros] = useState({
    tipos: [] as string[],
    status: [] as string[],
    prioridades: [] as string[]
  });
  const [isMobile, setIsMobile] = useState(false);
  
  // Referência para o contêiner de arrastar e soltar
  const dropContainerRef = useRef<HTMLDivElement>(null);

  // Detectar dispositivo móvel
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  // Estatísticas calculadas
  const stats = useMemo(() => {
    const total = eventos.length;
    const pendentes = eventos.filter(e => e.status === "pendente").length;
    const confirmados = eventos.filter(e => e.status === "confirmado").length;
    const cancelados = eventos.filter(e => e.status === "cancelado").length;
    const porTipo = {
      precatorio: eventos.filter(e => e.tipo === "precatorio").length,
      audiencia: eventos.filter(e => e.tipo === "audiencia").length,
      reuniao: eventos.filter(e => e.tipo === "reuniao").length,
    };
    
    const valorTotal = eventos.reduce((acc, curr) => acc + (curr.precatorio?.valor || 0), 0);
    
    return {
      total,
      pendentes,
      confirmados,
      cancelados,
      porTipo,
      valorTotal
    };
  }, [eventos]);

  // Funções de navegação
  const navegarPara = (direcao: "anterior" | "proximo") => {
    if (view === "dia") {
      setCurrentDate(direcao === "anterior" ? subDays(currentDate, 1) : addDays(currentDate, 1));
    } else if (view === "semana") {
      setCurrentDate(direcao === "anterior" ? subWeeks(currentDate, 1) : addWeeks(currentDate, 1));
    } else {
      setCurrentDate(direcao === "anterior" ? subMonths(currentDate, 1) : addMonths(currentDate, 1));
    }
  };

  // Função para obter eventos do dia
  const getEventosDoDia = (data: Date) => {
    return eventos.filter(evento => {
      const dataEvento = parseISO(evento.data);
      return format(dataEvento, "yyyy-MM-dd") === format(data, "yyyy-MM-dd");
    });
  };

  // Função para integração com calendários externos
  const handleCalendarIntegration = (type: string) => {
    // Aqui você implementaria a lógica de integração com Google Calendar ou iCal
    console.log(`Integrando com ${type}`);
    setShowIntegrationDialog(false);
  };

  // Função para arrastar e soltar eventos
  const handleDragStart = (event: React.DragEvent, evento: typeof eventos[0]) => {
    setDraggedEvent(evento);
    setIsDragging(true);
    event.dataTransfer.setData('text/plain', evento.id.toString());
    event.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (event: React.DragEvent, data: Date) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  };

  const handleDrop = (event: React.DragEvent, data: Date) => {
    event.preventDefault();
    
    if (draggedEvent) {
      // Aqui você implementaria a lógica para atualizar a data do evento
      console.log(`Movendo evento ${draggedEvent.id} para ${format(data, 'yyyy-MM-dd')}`);
      
      // Exemplo de como seria a atualização:
      // const updatedEventos = eventos.map(e => {
      //   if (e.id === draggedEvent.id) {
      //     return { ...e, data: format(data, 'yyyy-MM-dd') };
      //   }
      //   return e;
      // });
      
      setIsDragging(false);
      setDraggedEvent(null);
    }
  };

  const handleDragEnd = () => {
    setIsDragging(false);
    setDraggedEvent(null);
  };

  // Função para exportar eventos
  const handleExportEvents = (format: 'csv' | 'ical' | 'pdf') => {
    // Aqui você implementaria a lógica de exportação
    console.log(`Exportando eventos em formato ${format}`);
    setShowExportDialog(false);
  };

  // Função para configurar lembretes
  const handleSetReminder = (eventId: number, reminderTime: number) => {
    // Aqui você implementaria a lógica de lembretes
    console.log(`Configurando lembrete para evento ${eventId} com ${reminderTime} minutos de antecedência`);
    setShowReminderDialog(false);
  };

  const EventForm = ({ event = null }: { event?: typeof eventos[0] | null }) => {
    const [date, setDate] = useState<Date | undefined>(event ? parseISO(event.data) : undefined);
    const [hora, setHora] = useState(event?.hora || "");
    const [isRecurring, setIsRecurring] = useState(false);
    const [recurrencePattern, setRecurrencePattern] = useState("diario");
    const [recurrenceEndDate, setRecurrenceEndDate] = useState<Date | undefined>(undefined);

    const handleHoraChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      let value = e.target.value.replace(/\D/g, "");
      
      if (value.length >= 2) {
        const horas = parseInt(value.substring(0, 2));
        if (horas > 23) value = "23" + value.substring(2);
      }
      
      if (value.length >= 4) {
        const minutos = parseInt(value.substring(2, 4));
        if (minutos > 59) value = value.substring(0, 2) + "59";
      }
      
      let formattedValue = value;
      if (value.length >= 2) {
        formattedValue = value.substring(0, 2) + ":" + value.substring(2);
      }
      
      setHora(formattedValue.substring(0, 5));
    };

    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault();
      const form = e.target as HTMLFormElement;
      const formData = new FormData(form);
      
      // Adicionar a data formatada ao FormData
      if (date) {
        formData.set('data', format(date, 'yyyy-MM-dd'));
      }
      
      // Aqui você implementaria a lógica de salvar o evento
      console.log('Dados do evento:', {
        titulo: formData.get('titulo'),
        data: formData.get('data'),
        hora: formData.get('hora'),
        tipo: formData.get('tipo'),
        prioridade: formData.get('prioridade'),
        descricao: formData.get('descricao'),
        local: formData.get('local'),
        duracao: formData.get('duracao'),
        status: formData.get('status'),
        responsavel: formData.get('responsavel'),
        isRecurring,
        recurrencePattern: isRecurring ? recurrencePattern : null,
        recurrenceEndDate: isRecurring && recurrenceEndDate ? format(recurrenceEndDate, 'yyyy-MM-dd') : null
      });
      
      setShowEventDialog(false);
    };

    return (
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="titulo">Título</Label>
          <Input id="titulo" name="titulo" defaultValue={event?.titulo} required />
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label>Data</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !date && "text-neutral-500"
                  )}
                >
                  <CalendarIcon2 className="mr-2 h-4 w-4" />
                  {date ? format(date, "PPP", { locale: ptBR }) : "Selecione uma data"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <CalendarPicker
                  mode="single"
                  selected={date}
                  onSelect={setDate}
                  initialFocus
                  locale={ptBR}
                />
              </PopoverContent>
            </Popover>
          </div>
          <div className="space-y-2">
            <Label htmlFor="hora">Hora</Label>
            <Input
              id="hora"
              name="hora"
              value={hora}
              onChange={handleHoraChange}
              placeholder="00:00"
              maxLength={5}
              className="font-mono"
              required
            />
            <p className="text-xs text-neutral-500">
              Digite o horário no formato 24h (ex: 14:30)
            </p>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="tipo">Tipo</Label>
            <Select name="tipo" defaultValue={event?.tipo}>
              <SelectTrigger>
                <SelectValue placeholder="Selecione o tipo" />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(tipoEventoConfig).map(([value, config]) => (
                  <SelectItem key={value} value={value}>
                    <div className="flex items-center gap-2">
                      <config.icone className="w-4 h-4" />
                      {config.label}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label htmlFor="prioridade">Prioridade</Label>
            <Select name="prioridade" defaultValue={event?.prioridade}>
              <SelectTrigger>
                <SelectValue placeholder="Selecione a prioridade" />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(prioridadeConfig).map(([value, config]) => (
                  <SelectItem key={value} value={value}>
                    <div className="flex items-center gap-2">
                      <div className={cn("w-2 h-2 rounded-full", config.cor)} />
                      {config.label}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="descricao">Descrição</Label>
          <Textarea id="descricao" name="descricao" defaultValue={event?.descricao} />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="local">Local</Label>
            <Input id="local" name="local" defaultValue={event?.local} />
          </div>
          <div className="space-y-2">
            <Label htmlFor="duracao">Duração (minutos)</Label>
            <Input 
              id="duracao" 
              name="duracao"
              type="number" 
              min="15" 
              step="15" 
              defaultValue={event?.duracao || "60"} 
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="status">Status</Label>
          <Select name="status" defaultValue={event?.status}>
            <SelectTrigger>
              <SelectValue placeholder="Selecione o status" />
            </SelectTrigger>
            <SelectContent>
              {Object.entries(statusConfig).map(([value, config]) => (
                <SelectItem key={value} value={value}>
                  <div className="flex items-center gap-2">
                    <config.icone className="w-4 h-4" />
                    {config.label}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {event?.tipo === "precatorio" && (
          <div className="space-y-4 border-t pt-4">
            <h4 className="font-medium">Detalhes do Precatório</h4>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="precatorio_numero">Número</Label>
                <Input 
                  id="precatorio_numero" 
                  name="precatorio_numero"
                  defaultValue={event?.precatorio?.numero}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="precatorio_tipo">Tipo</Label>
                <Select name="precatorio_tipo" defaultValue={event?.precatorio?.tipo}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione o tipo" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Alimentar">Alimentar</SelectItem>
                    <SelectItem value="Comum">Comum</SelectItem>
                    <SelectItem value="Preferencial">Preferencial</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="precatorio_beneficiario">Beneficiário</Label>
                <Input 
                  id="precatorio_beneficiario" 
                  name="precatorio_beneficiario"
                  defaultValue={event?.precatorio?.beneficiario}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="precatorio_valor">Valor</Label>
                <Input 
                  id="precatorio_valor" 
                  name="precatorio_valor"
                  type="number" 
                  defaultValue={event?.precatorio?.valor}
                />
              </div>
            </div>
          </div>
        )}

        <div className="space-y-2">
          <Label htmlFor="responsavel">Responsável</Label>
          <Select name="responsavel" defaultValue={event?.responsavel?.nome}>
            <SelectTrigger>
              <SelectValue placeholder="Selecione o responsável" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Ana Silva">
                <div className="flex items-center gap-2">
                  <Avatar className="h-6 w-6">
                    <AvatarImage src="/avatars/ana.jpg" />
                    <AvatarFallback>AS</AvatarFallback>
                  </Avatar>
                  Ana Silva
                </div>
              </SelectItem>
              <SelectItem value="Carlos Santos">
                <div className="flex items-center gap-2">
                  <Avatar className="h-6 w-6">
                    <AvatarImage src="/avatars/carlos.jpg" />
                    <AvatarFallback>CS</AvatarFallback>
                  </Avatar>
                  Carlos Santos
                </div>
              </SelectItem>
              <SelectItem value="Maria Costa">
                <div className="flex items-center gap-2">
                  <Avatar className="h-6 w-6">
                    <AvatarImage src="/avatars/maria.jpg" />
                    <AvatarFallback>MC</AvatarFallback>
                  </Avatar>
                  Maria Costa
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Campos de recorrência */}
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="isRecurring"
              checked={isRecurring}
              onChange={(e) => setIsRecurring(e.target.checked)}
              className="rounded border-neutral-300"
            />
            <Label htmlFor="isRecurring" className="flex items-center">
              <Repeat className="w-4 h-4 mr-1" />
              Evento recorrente
            </Label>
          </div>
          
          {isRecurring && (
            <div className="pl-6 space-y-3 border-l-2 border-neutral-200 dark:border-neutral-800">
              <div className="space-y-2">
                <Label htmlFor="recurrencePattern">Padrão de recorrência</Label>
                <Select 
                  value={recurrencePattern} 
                  onValueChange={setRecurrencePattern}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione o padrão" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="diario">Diário</SelectItem>
                    <SelectItem value="semanal">Semanal</SelectItem>
                    <SelectItem value="mensal">Mensal</SelectItem>
                    <SelectItem value="anual">Anual</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label>Data de término</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !recurrenceEndDate && "text-neutral-500"
                      )}
                    >
                      <CalendarIcon2 className="mr-2 h-4 w-4" />
                      {recurrenceEndDate ? format(recurrenceEndDate, "PPP", { locale: ptBR }) : "Sem data de término"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <CalendarPicker
                      mode="single"
                      selected={recurrenceEndDate}
                      onSelect={setRecurrenceEndDate}
                      initialFocus
                      locale={ptBR}
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
          )}
        </div>
        
        {/* Lembretes */}
        <div className="space-y-2">
          <Label className="flex items-center">
            <BellRing className="w-4 h-4 mr-1" />
            Lembretes
          </Label>
          <Select name="lembrete" defaultValue="30">
            <SelectTrigger>
              <SelectValue placeholder="Selecione quando notificar" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="0">No momento do evento</SelectItem>
              <SelectItem value="5">5 minutos antes</SelectItem>
              <SelectItem value="15">15 minutos antes</SelectItem>
              <SelectItem value="30">30 minutos antes</SelectItem>
              <SelectItem value="60">1 hora antes</SelectItem>
              <SelectItem value="1440">1 dia antes</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex justify-end gap-2 pt-4">
          <Button type="button" variant="outline" onClick={() => setShowEventDialog(false)}>
            Cancelar
          </Button>
          <Button type="submit">
            {event ? "Salvar alterações" : "Criar evento"}
          </Button>
        </div>
      </form>
    );
  };

  // Dialog de Filtros
  const FilterDialog = () => (
    <Dialog open={showFilterDialog} onOpenChange={setShowFilterDialog}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Filtros</DialogTitle>
          <DialogDescription>
            Selecione os filtros para visualizar os eventos
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <div className="space-y-2">
            <Label>Tipos de Evento</Label>
            <div className="flex flex-wrap gap-2">
              {Object.entries(tipoEventoConfig).map(([tipo, config]) => (
                <Button
                  key={tipo}
                  variant="outline"
                  size="sm"
                  className={cn(
                    "gap-2",
                    filtros.tipos.includes(tipo) && config.cor
                  )}
                  onClick={() => {
                    setFiltros(prev => ({
                      ...prev,
                      tipos: prev.tipos.includes(tipo)
                        ? prev.tipos.filter(t => t !== tipo)
                        : [...prev.tipos, tipo]
                    }));
                  }}
                >
                  <config.icone className="w-4 h-4" />
                  {config.label}
                </Button>
              ))}
            </div>
          </div>

          <div className="space-y-2">
            <Label>Status</Label>
            <div className="flex flex-wrap gap-2">
              {Object.entries(statusConfig).map(([status, config]) => (
                <Button
                  key={status}
                  variant="outline"
                  size="sm"
                  className={cn(
                    "gap-2",
                    filtros.status.includes(status) && config.cor
                  )}
                  onClick={() => {
                    setFiltros(prev => ({
                      ...prev,
                      status: prev.status.includes(status)
                        ? prev.status.filter(s => s !== status)
                        : [...prev.status, status]
                    }));
                  }}
                >
                  <config.icone className="w-4 h-4" />
                  {config.label}
                </Button>
              ))}
            </div>
          </div>

          <div className="space-y-2">
            <Label>Prioridade</Label>
            <div className="flex flex-wrap gap-2">
              {Object.entries(prioridadeConfig).map(([prioridade, config]) => (
                <Button
                  key={prioridade}
                  variant="outline"
                  size="sm"
                  className={cn(
                    "gap-2",
                    filtros.prioridades.includes(prioridade) && config.cor
                  )}
                  onClick={() => {
                    setFiltros(prev => ({
                      ...prev,
                      prioridades: prev.prioridades.includes(prioridade)
                        ? prev.prioridades.filter(p => p !== prioridade)
                        : [...prev.prioridades, prioridade]
                    }));
                  }}
                >
                  <div className={cn("w-2 h-2 rounded-full", config.cor)} />
                  {config.label}
                </Button>
              ))}
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => setFiltros({ tipos: [], status: [], prioridades: [] })}
          >
            Limpar Filtros
          </Button>
          <Button onClick={() => setShowFilterDialog(false)}>
            Aplicar Filtros
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );

  // Dialog de Exportação
  const ExportDialog = () => (
    <Dialog open={showExportDialog} onOpenChange={setShowExportDialog}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Exportar Eventos</DialogTitle>
          <DialogDescription>
            Escolha o formato para exportar seus eventos do calendário.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <Button 
            variant="outline" 
            className="flex items-center justify-start"
            onClick={() => handleExportEvents('csv')}
          >
            <FileDown className="mr-2 h-4 w-4" />
            Exportar como CSV
          </Button>
          <Button 
            variant="outline" 
            className="flex items-center justify-start"
            onClick={() => handleExportEvents('ical')}
          >
            <CalendarIcon2 className="mr-2 h-4 w-4" />
            Exportar como iCalendar (.ics)
          </Button>
          <Button 
            variant="outline" 
            className="flex items-center justify-start"
            onClick={() => handleExportEvents('pdf')}
          >
            <FileText className="mr-2 h-4 w-4" />
            Exportar como PDF
          </Button>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => setShowExportDialog(false)}>
            Cancelar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );

  // Renderiza um evento com mais detalhes
  const renderEvento = (evento: typeof eventos[0]) => {
    const Icon = tipoEventoConfig[evento.tipo].icone;
    const StatusIcon = statusConfig[evento.status].icone;

    return (
      <TooltipProvider>
      <div
        key={evento.id}
        className="p-3 rounded-lg bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 hover:shadow-md transition-shadow"
      >
        <div className="flex items-start justify-between gap-2">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-2">
                <Tooltip>
                  <TooltipTrigger>
              <div className={cn("p-1.5 rounded-lg", tipoEventoConfig[evento.tipo].cor)}>
                <Icon className="w-4 h-4" />
              </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{tipoEventoConfig[evento.tipo].label}</p>
                  </TooltipContent>
                </Tooltip>
              <div className="flex-1 min-w-0">
                <h4 className="font-medium truncate">{evento.titulo}</h4>
                <p className="text-sm text-neutral-500 dark:text-neutral-400 truncate">
                  {evento.descricao}
                </p>
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => {
                      setSelectedEvent(evento);
                      setShowEventDialog(true);
                    }}>
                      <Edit className="w-4 h-4 mr-2" />
                      Editar
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Share2 className="w-4 h-4 mr-2" />
                      Compartilhar
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem 
                      className="text-red-600"
                      onClick={() => {
                        setSelectedEvent(evento);
                        setShowDeleteDialog(true);
                      }}
                    >
                      <Trash2 className="w-4 h-4 mr-2" />
                      Excluir
                    </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
            
            <div className="grid grid-cols-2 gap-2 text-sm">
                <Tooltip>
                  <TooltipTrigger>
              <div className="flex items-center gap-1 text-neutral-600 dark:text-neutral-400">
                <Clock className="w-4 h-4" />
                <span>{evento.hora}</span>
                <span className="text-xs">({evento.duracao}min)</span>
              </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Duração: {evento.duracao} minutos</p>
                  </TooltipContent>
                </Tooltip>
                <Tooltip>
                  <TooltipTrigger>
              <div className="flex items-center gap-1 text-neutral-600 dark:text-neutral-400">
                <MapPin className="w-4 h-4" />
                <span className="truncate">{evento.local}</span>
              </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{evento.local}</p>
                  </TooltipContent>
                </Tooltip>
            </div>

            {evento.precatorio && (
              <div className="mt-2 flex items-center gap-2">
                  <Tooltip>
                    <TooltipTrigger>
                <Badge variant="outline" className="text-xs">
                  {evento.precatorio.numero}
                </Badge>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Número do Precatório</p>
                    </TooltipContent>
                  </Tooltip>
                  <Tooltip>
                    <TooltipTrigger>
                <Badge variant="outline" className="text-xs">
                  {evento.precatorio.tipo}
                </Badge>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Tipo do Precatório</p>
                    </TooltipContent>
                  </Tooltip>
                  <Tooltip>
                    <TooltipTrigger>
                <span className="text-xs text-neutral-500">
                  {new Intl.NumberFormat('pt-BR', { 
                    style: 'currency', 
                    currency: 'BRL',
                    notation: 'compact'
                  }).format(evento.precatorio.valor)}
                </span>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Valor do Precatório</p>
                    </TooltipContent>
                  </Tooltip>
              </div>
            )}

            <div className="mt-3 flex items-center justify-between">
                <Tooltip>
                  <TooltipTrigger>
              <div className="flex items-center gap-2">
                <Avatar className="h-6 w-6">
                  <AvatarImage src={evento.responsavel.avatar} />
                  <AvatarFallback>
                    {evento.responsavel.nome.split(" ").map(n => n[0]).join("")}
                  </AvatarFallback>
                </Avatar>
                <span className="text-sm text-neutral-600 dark:text-neutral-400">
                  {evento.responsavel.nome}
                </span>
              </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Responsável pelo evento</p>
                  </TooltipContent>
                </Tooltip>
                <Tooltip>
                  <TooltipTrigger>
              <Badge variant="outline" className={cn("text-xs", statusConfig[evento.status].cor)}>
                <StatusIcon className="w-3 h-3 mr-1" />
                {evento.status.charAt(0).toUpperCase() + evento.status.slice(1)}
              </Badge>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Status do evento</p>
                  </TooltipContent>
                </Tooltip>
            </div>

              {evento.prioridade && (
                <div className="mt-2">
                  <Badge className={cn("text-xs", prioridadeConfig[evento.prioridade].cor)}>
                    {prioridadeConfig[evento.prioridade].label}
                  </Badge>
          </div>
              )}
        </div>
      </div>
        </div>
      </TooltipProvider>
    );
  };

  // Renderiza as estatísticas
  const renderStats = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total de Eventos</CardTitle>
          <BarChart3 className="h-4 w-4 text-neutral-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.total}</div>
          <div className="text-xs text-neutral-500">
            Eventos no período atual
          </div>
          <div className="mt-4 space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-neutral-600">Pendentes</span>
              <span className="font-medium">{stats.pendentes}</span>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span className="text-neutral-600">Confirmados</span>
              <span className="font-medium">{stats.confirmados}</span>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span className="text-neutral-600">Cancelados</span>
              <span className="font-medium">{stats.cancelados}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Por Tipo</CardTitle>
          <FileText className="h-4 w-4 text-neutral-600" />
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center">
              <Banknote className="h-4 w-4 mr-2 text-blue-500" />
              <div className="flex-1">
                <div className="text-sm font-medium">Precatórios</div>
                <div className="text-xs text-neutral-500">{stats.porTipo.precatorio} eventos</div>
              </div>
            </div>
            <div className="flex items-center">
              <Users2 className="h-4 w-4 mr-2 text-purple-500" />
              <div className="flex-1">
                <div className="text-sm font-medium">Audiências</div>
                <div className="text-xs text-neutral-500">{stats.porTipo.audiencia} eventos</div>
              </div>
            </div>
            <div className="flex items-center">
              <Building2 className="h-4 w-4 mr-2 text-amber-500" />
              <div className="flex-1">
                <div className="text-sm font-medium">Reuniões</div>
                <div className="text-xs text-neutral-500">{stats.porTipo.reuniao} eventos</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Valor Total</CardTitle>
          <Banknote className="h-4 w-4 text-neutral-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {new Intl.NumberFormat('pt-BR', { 
              style: 'currency', 
              currency: 'BRL',
              maximumFractionDigits: 0
            }).format(stats.valorTotal)}
          </div>
          <div className="text-xs text-neutral-500">
            Em precatórios
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Próximos Eventos</CardTitle>
          <Clock3 className="h-4 w-4 text-neutral-600" />
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {eventos
              .sort((a, b) => new Date(a.data).getTime() - new Date(b.data).getTime())
              .slice(0, 3)
              .map(evento => (
                <div key={evento.id} className="flex items-center">
                  <div className={cn("w-2 h-2 rounded-full mr-2", statusConfig[evento.status].cor)} />
                  <div className="flex-1 min-w-0">
                    <div className="text-sm font-medium truncate">{evento.titulo}</div>
                    <div className="text-xs text-neutral-500">
                      {format(parseISO(evento.data), "dd/MM/yyyy")} às {evento.hora}
                    </div>
                  </div>
                </div>
              ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );

  // Atualizar renderDia para ajustar tamanhos
  const renderDia = () => {
    const eventosHoje = getEventosDoDia(currentDate);
    const horasDisponiveis = Array.from({ length: 24 }, (_, i) => i);
    
    return (
      <div className="space-y-4">
        <div className="text-center p-4 bg-white dark:bg-neutral-900 rounded-lg border border-neutral-200 dark:border-neutral-800">
          <h2 className="text-2xl font-bold text-neutral-800 dark:text-neutral-200">
            {format(currentDate, "dd 'de' MMMM 'de' yyyy", { locale: ptBR })}
          </h2>
          <p className="text-neutral-600 dark:text-neutral-400 capitalize">
            {format(currentDate, "EEEE", { locale: ptBR })}
          </p>
        </div>

        <div className="relative h-[600px] overflow-y-auto">
          <div className="absolute left-0 top-0 bottom-0 w-12 border-r border-neutral-200 dark:border-neutral-800">
            {horasDisponiveis.map((hora) => (
              <div
                key={hora}
                className="h-16 flex items-center justify-center text-xs text-neutral-500"
              >
                {String(hora).padStart(2, '0')}:00
              </div>
            ))}
          </div>
          
          <div className="ml-12 relative">
            {/* Linha do tempo atual */}
            {isToday(currentDate) && (
              <div
                className="absolute left-0 right-0 h-0.5 bg-blue-500 z-10"
                style={{
                  top: `${(new Date().getHours() * 60 + new Date().getMinutes()) * (64 / 60)}px`
                }}
              >
                <div className="absolute -left-2 -top-1 w-2 h-2 rounded-full bg-blue-500" />
            </div>
          )}

            {/* Grade de horas */}
            {horasDisponiveis.map((hora) => (
              <div
                key={hora}
                className="h-16 border-b border-neutral-100 dark:border-neutral-800 relative"
              >
                <div className="absolute top-1/2 left-0 right-0 border-b border-dashed border-neutral-100 dark:border-neutral-800" />
              </div>
            ))}

            {/* Eventos */}
            {eventosHoje.map((evento) => {
              const [hora, minuto] = evento.hora.split(':').map(Number);
              const duracaoEmMinutos = evento.duracao || 60;
              const top = (hora * 60 + minuto) * (64 / 60);
              const height = duracaoEmMinutos * (64 / 60);

              return (
                <div
                  key={evento.id}
                  className={cn(
                    "absolute left-1 right-1 rounded-md border p-2 shadow-sm transition-all hover:shadow-md",
                    eventColors[evento.tipo as keyof typeof eventColors]?.light,
                    eventColors[evento.tipo as keyof typeof eventColors]?.dark,
                    "hover:border-blue-500"
                  )}
                  style={{
                    top: `${top}px`,
                    height: `${height}px`,
                    minHeight: "32px"
                  }}
                  onClick={() => {
                    setSelectedEvent(evento);
                    setShowEventDialog(true);
                  }}
                >
                  <div className="flex items-start gap-2 h-full">
                    <div className={cn(
                      "w-1 h-full rounded-full",
                      tipoEventoConfig[evento.tipo as keyof typeof tipoEventoConfig]?.cor
                    )} />
                    <div className="flex-1 min-w-0 overflow-hidden">
                      <h4 className="font-medium truncate text-sm">{evento.titulo}</h4>
                      {height > 48 && (
                        <>
                          <p className="text-xs truncate text-neutral-500">{evento.descricao}</p>
                          <div className="flex items-center gap-1 mt-1 text-xs text-neutral-500">
                            <Clock className="w-3 h-3" />
                            {evento.hora} - {format(addMinutes(parseISO(`2024-01-01T${evento.hora}`), duracaoEmMinutos), 'HH:mm')}
                          </div>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    );
  };

  // Atualizar renderSemana para ajustar tamanhos
  const renderSemana = () => {
    const diasDaSemana = eachDayOfInterval({
      start: startOfWeek(currentDate, { locale: ptBR }),
      end: endOfWeek(currentDate, { locale: ptBR })
    });

    return (
      <div className="space-y-4">
        <div className="grid grid-cols-7 gap-2">
        {diasDaSemana.map((dia, index) => (
          <div
            key={index}
            className={cn(
                "p-2 text-center rounded-md transition-colors cursor-pointer",
                isToday(dia) && "bg-blue-50 dark:bg-blue-900/20 text-blue-600"
            )}
              onClick={() => {
                setCurrentDate(dia);
                setView("dia");
              }}
          >
              <p className="text-xs font-medium capitalize text-neutral-600 dark:text-neutral-400">
                {format(dia, "EEEE", { locale: ptBR })}
              </p>
              <p className="text-lg font-bold mt-1">
                {format(dia, "dd")}
              </p>
            </div>
          ))}
        </div>

        <div className="grid grid-cols-7 gap-2">
          {diasDaSemana.map((dia, index) => {
            const eventosDoDia = getEventosDoDia(dia);
            return (
              <div
                key={index}
                  className={cn(
                  "h-[300px] p-2 rounded-md border bg-white dark:bg-neutral-900",
                  isToday(dia) ? "border-blue-500" : "border-neutral-200 dark:border-neutral-800"
                )}
              >
                <ScrollArea className="h-full">
                  <div className="space-y-1">
                    {eventosDoDia.length > 0 ? (
                      eventosDoDia
                        .sort((a, b) => a.hora.localeCompare(b.hora))
                        .map(evento => (
                          <div
                            key={evento.id}
                            className={cn(
                              "p-2 rounded-md border transition-colors cursor-pointer",
                              eventColors[evento.tipo as keyof typeof eventColors]?.light,
                              eventColors[evento.tipo as keyof typeof eventColors]?.dark,
                              "hover:border-blue-500"
                            )}
                            onClick={() => {
                              setSelectedEvent(evento);
                              setShowEventDialog(true);
                            }}
                          >
                            <div className="flex items-center gap-2">
                              <div className={cn(
                                "w-1 h-4 rounded-full",
                                tipoEventoConfig[evento.tipo as keyof typeof tipoEventoConfig]?.cor
                              )} />
                              <div className="flex-1 min-w-0">
                                <h4 className="font-medium truncate text-sm">{evento.titulo}</h4>
                                <div className="flex items-center gap-1 mt-1 text-xs text-neutral-500">
                                  <Clock className="w-3 h-3" />
                                  {evento.hora}
                                </div>
                              </div>
                            </div>
                          </div>
                        ))
                    ) : (
                      <div className="text-center py-2 text-xs text-neutral-500">
                        Nenhum evento
          </div>
                    )}
                  </div>
                </ScrollArea>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  // Renderização da visualização de agenda
  const renderAgenda = () => {
    // Obter eventos da semana atual
    const inicio = startOfWeek(currentDate, { weekStartsOn: 0 });
    const fim = endOfWeek(currentDate, { weekStartsOn: 0 });
    
    const diasDaSemana = eachDayOfInterval({ start: inicio, end: fim });
    
    return (
      <div className="space-y-4">
        {diasDaSemana.map((dia) => {
          const eventosDoDia = getEventosDoDia(dia);
          if (eventosDoDia.length === 0) return null;
          
          return (
            <div key={format(dia, 'yyyy-MM-dd')} className="space-y-2">
              <div className="flex items-center space-x-2">
                <div className={cn(
                  "w-10 h-10 rounded-full flex items-center justify-center font-semibold",
                  isToday(dia) ? "bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300" : "bg-neutral-100 text-neutral-700 dark:bg-neutral-800 dark:text-neutral-300"
                )}>
                  {format(dia, 'd')}
                </div>
                <div>
                  <h3 className="font-medium">{format(dia, 'EEEE', { locale: ptBR })}</h3>
                  <p className="text-sm text-neutral-500">{format(dia, 'dd/MM/yyyy')}</p>
                </div>
              </div>
              
              <div className="pl-5 border-l-2 border-neutral-200 dark:border-neutral-800 space-y-2">
                {eventosDoDia.sort((a, b) => {
                  const timeA = a.hora.split(':').map(Number);
                  const timeB = b.hora.split(':').map(Number);
                  return (timeA[0] * 60 + timeA[1]) - (timeB[0] * 60 + timeB[1]);
                }).map(evento => (
                  <div 
                    key={evento.id}
                    className={cn(
                      "p-3 rounded-md border transition-colors cursor-pointer",
                      eventColors[evento.tipo as keyof typeof eventColors]?.light,
                      eventColors[evento.tipo as keyof typeof eventColors]?.dark,
                      "hover:border-blue-500"
                    )}
                    onClick={() => {
                      setSelectedEvent(evento);
                      setShowEventDialog(true);
                    }}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-2">
                        <div className={cn(
                          "w-1 h-full min-h-[2rem] rounded-full",
                          tipoEventoConfig[evento.tipo as keyof typeof tipoEventoConfig]?.cor
                        )} />
                        <div>
                          <h4 className="font-medium">{evento.titulo}</h4>
                          <div className="flex items-center text-sm text-neutral-500">
                            <Clock className="w-3 h-3 mr-1" />
                            {evento.hora} - {format(addMinutes(parseISO(`${evento.data}T${evento.hora}`), evento.duracao), 'HH:mm')}
                          </div>
                          {evento.local && (
                            <div className="flex items-center text-sm text-neutral-500 mt-1">
                              <MapPin className="w-3 h-3 mr-1" />
                              {evento.local}
                            </div>
                          )}
                        </div>
                      </div>
                      <Badge variant={
                        evento.status === "confirmado" ? "default" :
                        evento.status === "pendente" ? "outline" : "destructive"
                      } className={cn(
                        evento.status === "confirmado" ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300" :
                        evento.status === "pendente" ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300" : ""
                      )}>
                        {evento.status === "confirmado" ? "Confirmado" :
                         evento.status === "pendente" ? "Pendente" : "Cancelado"}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  // Renderização da visualização mensal com suporte a arrastar e soltar
  const renderMes = () => {
    const diasDoMes = eachDayOfInterval({
      start: startOfMonth(currentDate),
      end: endOfMonth(currentDate)
    });

    const diasDaSemana = ["Dom", "Seg", "Ter", "Qua", "Qui", "Sex", "Sáb"];

    return (
      <div className="space-y-2" ref={dropContainerRef}>
        <div className="grid grid-cols-7 gap-2">
          {diasDaSemana.map((dia, index) => (
            <div
              key={index}
              className="text-center text-xs font-medium text-neutral-600 dark:text-neutral-400 p-1"
            >
              {dia}
            </div>
          ))}
        </div>

        <div className="grid grid-cols-7 gap-2">
          {diasDoMes.map((dia, index) => {
            const eventosDoDia = getEventosDoDia(dia);
            const isOutOfMonth = !isSameMonth(dia, currentDate);

            return (
              <div
                key={index}
                className={cn(
                  "min-h-[100px] p-1 rounded-md border transition-colors",
                  isToday(dia) && "border-blue-500",
                  !isToday(dia) && !isOutOfMonth && "border-neutral-200 dark:border-neutral-800",
                  isOutOfMonth && "opacity-50",
                  isDragging && !isOutOfMonth && "border-dashed border-blue-500 bg-blue-50 dark:bg-blue-950/20"
                )}
                onClick={() => {
                  if (!isOutOfMonth) {
                    setCurrentDate(dia);
                    setView("dia");
                  }
                }}
                onDragOver={(e) => !isOutOfMonth && handleDragOver(e, dia)}
                onDrop={(e) => !isOutOfMonth && handleDrop(e, dia)}
              >
                <div className="flex items-center justify-between mb-1">
                  <span className={cn(
                    "text-sm",
                    isToday(dia) && "text-blue-600 font-bold",
                    !isToday(dia) && "text-neutral-600 dark:text-neutral-400"
                  )}>
                    {format(dia, "d")}
                  </span>
                  {eventosDoDia.length > 0 && (
                    <Badge variant="outline" className="text-xs h-4 px-1">
                      {eventosDoDia.length}
                    </Badge>
                  )}
                </div>
                <ScrollArea className="h-[70px]">
                  <div className="space-y-1">
                    {eventosDoDia.map(evento => (
                      <Tooltip key={evento.id}>
                        <TooltipTrigger asChild>
                          <div
                            className={cn(
                              "p-1 rounded text-xs truncate cursor-pointer border",
                              eventColors[evento.tipo as keyof typeof eventColors]?.light,
                              eventColors[evento.tipo as keyof typeof eventColors]?.dark,
                              "hover:border-blue-500"
                            )}
                            onClick={(e) => {
                              e.stopPropagation();
                              setSelectedEvent(evento);
                              setShowEventDialog(true);
                            }}
                            draggable
                            onDragStart={(e) => handleDragStart(e, evento)}
                            onDragEnd={handleDragEnd}
                          >
                            <div className="flex items-center gap-1">
                              <div className={cn(
                                "w-1 h-3 rounded-full",
                                tipoEventoConfig[evento.tipo as keyof typeof tipoEventoConfig]?.cor
                              )} />
                              <span>{evento.hora} - {evento.titulo}</span>
                            </div>
                          </div>
                        </TooltipTrigger>
                        <TooltipContent>
                          <div className="space-y-1">
                            <p className="font-medium">{evento.titulo}</p>
                            <p className="text-xs flex items-center">
                              <Clock className="w-3 h-3 mr-1" />
                              {evento.hora} - {format(addMinutes(parseISO(`${evento.data}T${evento.hora}`), evento.duracao), 'HH:mm')}
                            </p>
                            {evento.local && (
                              <p className="text-xs flex items-center">
                                <MapPin className="w-3 h-3 mr-1" />
                                {evento.local}
                              </p>
                            )}
                            <Badge variant={
                              evento.status === "confirmado" ? "default" :
                              evento.status === "pendente" ? "outline" : "destructive"
                            } className={cn(
                              evento.status === "confirmado" ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300" :
                              evento.status === "pendente" ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300" : ""
                            )}>
                              {evento.status === "confirmado" ? "Confirmado" :
                               evento.status === "pendente" ? "Pendente" : "Cancelado"}
                            </Badge>
                          </div>
                        </TooltipContent>
                      </Tooltip>
                    ))}
                  </div>
                </ScrollArea>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-4">
      {/* Cabeçalho */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
            <div className="flex flex-col">
              <h1 className="text-2xl font-bold">Calendário</h1>
              <p className="text-neutral-500">Gerencie seus eventos e compromissos</p>
            </div>
            <div className="flex flex-wrap gap-2">
              <Button onClick={() => setShowEventDialog(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Novo Evento
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline">
                    <Settings className="w-4 h-4 mr-2" />
                    Opções
                    <ChevronDown className="w-4 h-4 ml-2" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => setShowIntegrationDialog(true)}>
                    <Upload className="w-4 h-4 mr-2" />
                    Importar Eventos
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setShowExportDialog(true)}>
                    <Download className="w-4 h-4 mr-2" />
                    Exportar Eventos
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => setShowFilterDialog(true)}>
                    <Filter className="w-4 h-4 mr-2" />
                    Filtrar Eventos
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setShowStats(!showStats)}>
                    <BarChart3 className="w-4 h-4 mr-2" />
                    {showStats ? "Ocultar Estatísticas" : "Mostrar Estatísticas"}
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {showStats && renderStats()}

          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-1">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => navegarPara("anterior")}
                >
                  <ChevronLeft className="w-4 h-4" />
                </Button>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => navegarPara("proximo")}
                >
                  <ChevronRight className="w-4 h-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentDate(new Date())}
                >
                  Hoje
                </Button>
              </div>
              <h2 className="text-lg font-semibold">
                {format(currentDate, view === "dia" ? "dd 'de' MMMM 'de' yyyy" : view === "semana" ? "'Semana de' dd 'de' MMMM" : "MMMM 'de' yyyy", { locale: ptBR })}
              </h2>
            </div>
            <Tabs value={view} onValueChange={(v) => setView(v as typeof view)}>
              <TabsList>
                <TabsTrigger value="dia" className="flex items-center">
                  <CalendarIcon2 className="w-4 h-4 mr-1 sm:mr-2" />
                  <span className="hidden sm:inline">Dia</span>
                </TabsTrigger>
                <TabsTrigger value="semana" className="flex items-center">
                  <CalendarDays className="w-4 h-4 mr-1 sm:mr-2" />
                  <span className="hidden sm:inline">Semana</span>
                </TabsTrigger>
                <TabsTrigger value="mes" className="flex items-center">
                  <CalendarIcon2 className="w-4 h-4 mr-1 sm:mr-2" />
                  <span className="hidden sm:inline">Mês</span>
                </TabsTrigger>
                <TabsTrigger value="agenda" className="flex items-center">
                  <List className="w-4 h-4 mr-1 sm:mr-2" />
                  <span className="hidden sm:inline">Agenda</span>
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </CardContent>
      </Card>

      {/* Calendário */}
      <Card>
        <CardContent className="p-6">
          {view === "dia" && renderDia()}
          {view === "semana" && renderSemana()}
          {view === "mes" && renderMes()}
          {view === "agenda" && renderAgenda()}
        </CardContent>
      </Card>

      {/* Dialog de Evento */}
      <Dialog open={showEventDialog} onOpenChange={setShowEventDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {selectedEvent ? "Editar Evento" : "Novo Evento"}
            </DialogTitle>
            <DialogDescription>
              {selectedEvent ? "Edite os detalhes do evento" : "Preencha os detalhes do novo evento"}
            </DialogDescription>
          </DialogHeader>
          {selectedEvent && (
            <div className="flex items-center gap-2 mb-4">
              <div className={cn(
                "p-2 rounded-lg",
                tipoEventoConfig[selectedEvent.tipo as keyof typeof tipoEventoConfig]?.cor
              )}>
                <tipoEventoConfig[selectedEvent.tipo as keyof typeof tipoEventoConfig].icone className="w-4 h-4 text-white" />
              </div>
              <div>
                <h3 className="font-medium">{selectedEvent.titulo}</h3>
                <p className="text-sm text-neutral-500">{format(parseISO(selectedEvent.data), "PPP", { locale: ptBR })}</p>
              </div>
              <Badge variant={
                selectedEvent.status === "confirmado" ? "default" :
                selectedEvent.status === "pendente" ? "outline" : "destructive"
              } className={cn(
                selectedEvent.status === "confirmado" ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300" :
                selectedEvent.status === "pendente" ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300" : ""
              )}>
                {statusConfig[selectedEvent.status as keyof typeof statusConfig].label}
              </Badge>
            </div>
          )}
          <EventForm event={selectedEvent} />
        </DialogContent>
      </Dialog>

      {/* Dialog de Exclusão */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Excluir Evento</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir este evento? Esta ação não pode ser desfeita.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction
              className="bg-red-600 hover:bg-red-700"
              onClick={() => {
                // Implementar lógica de exclusão
                setShowDeleteDialog(false);
              }}
            >
              Excluir
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Dialog de Filtros */}
      <FilterDialog />
      
      {/* Dialog de Exportação */}
      <ExportDialog />
      
      {/* Dica de uso para dispositivos móveis */}
      {isMobile && (
        <Card className="bg-blue-50 dark:bg-blue-950/30 border-blue-200 dark:border-blue-800">
          <CardContent className="p-4">
            <div className="flex items-start space-x-3">
              <Smartphone className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5" />
              <div>
                <h3 className="font-medium text-blue-800 dark:text-blue-300">Dica para dispositivos móveis</h3>
                <p className="text-sm text-blue-700 dark:text-blue-400">
                  Toque em um dia para ver os detalhes. Use a visualização de Agenda para uma melhor experiência em telas menores.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
} 