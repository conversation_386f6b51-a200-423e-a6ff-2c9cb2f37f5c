              responsaveis={responsaveis} 
            />
          </div>
          
          <div className="flex items-center gap-2">
            {selecionados.length > 0 && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="gap-2">
                    Ações em Massa ({selecionados.length})
                    <ChevronDown size={16} />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuLabel>Ações para {selecionados.length} itens</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {canDelete && (
                    <DropdownMenuItem 
                      onClick={handleBulkDelete} 
                      className="text-destructive focus:text-destructive focus:bg-destructive/10"
                    >
                      <Trash2 size={14} className="mr-2" /> Excluir Selecionados
                    </DropdownMenuItem>
                  )}
                  <DropdownMenuSeparator />
                  <DropdownMenuLabel>Atualizar Status</DropdownMenuLabel>
                  {['novo', 'em_analise', 'em_processamento', 'concluido', 'cancelado'].map(status => (
                    <DropdownMenuItem key={status} onClick={() => handleBulkUpdateStatus(status as StatusPrecatorio)}>
                      Marcar como {formatStatus(status)}
                    </DropdownMenuItem>
                  ))}
                  <DropdownMenuSeparator />
                  <DropdownMenuLabel>Atribuir Responsável</DropdownMenuLabel>
                  {responsaveis?.map(resp => (
                    <DropdownMenuItem key={resp.id} onClick={() => handleBulkAssignResponsible(resp.id)}>
                      {resp.nome}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            )}
            
            {canExport && (
              <Button variant="outline" size="sm" className="gap-2" onClick={handleExport}>
                <Download size={16} />
                Exportar
              </Button>
            )}
            
            <div className="flex items-center gap-1 rounded-md border p-1">
              <Button 
                variant={viewMode === 'tabela' ? 'secondary' : 'ghost'} 
                size="icon-sm" 
                onClick={() => setViewMode('tabela')}
                title="Visualizar em tabela"
              >
                <LayoutList size={16} />
              </Button>
              <Button 
                variant={viewMode === 'cards' ? 'secondary' : 'ghost'} 
                size="icon-sm" 
                onClick={() => setViewMode('cards')}
                title="Visualizar em cards"
              >
                <LayoutGrid size={16} />
              </Button>
            </div>
            
            {canCreate && (
              <Button 
                size="sm" 
                className="gap-2" 
                onClick={() => {
                  setPrecatorioIdForm(null);
                  setFormModalOpen(true);
                }}
              >
                <Plus size={16} />
                Novo Precatório
              </Button>
            )}
          </div>
        </div>
        
        {/* Conteúdo principal (tabela ou cards) */}
        {viewMode === 'tabela' ? (
          <TabelaPrecatorios 
            precatorios={precatorios} 
            isLoading={isLoading} 
            onView={(id) => { setPrecatorioIdDetalhes(id); setDetalhesModalOpen(true); }}
            onEdit={(id) => { setPrecatorioIdForm(id); setFormModalOpen(true); }}
            onDelete={(id) => { setPrecatorioIdDelete(id); setDeleteModalOpen(true); }}
            selecionados={selecionados}
            setSelecionados={setSelecionados}
            sortConfig={sortConfig}
            setSortConfig={setSortConfig}
            canEdit={canEdit}
            canDelete={canDelete}
          />
        ) : (
          <CardsPrecatorios 
            precatorios={precatorios} 
            isLoading={isLoading} 
            onView={(id) => { setPrecatorioIdDetalhes(id); setDetalhesModalOpen(true); }}
            onEdit={(id) => { setPrecatorioIdForm(id); setFormModalOpen(true); }}
            onDelete={(id) => { setPrecatorioIdDelete(id); setDeleteModalOpen(true); }}
            selecionados={selecionados}
            setSelecionados={setSelecionados}
            canEdit={canEdit}
            canDelete={canDelete}
          />
        )}
        
        {/* Paginação */}
        {totalPages > 1 && (
          <div className="flex justify-between items-center mt-6">
            <div className="text-sm text-muted-foreground">
              Mostrando {precatorios.length} de {totalPages * itemsPerPage} resultados
            </div>
            <div className="flex items-center gap-2">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => setPage(prev => Math.max(1, prev - 1))}
                disabled={page === 1 || isLoading}
              >
                Anterior
              </Button>
              <span className="text-sm">
                Página {page} de {totalPages}
              </span>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => setPage(prev => Math.min(totalPages, prev + 1))}
                disabled={page === totalPages || isLoading}
              >
                Próxima
              </Button>
              <Select 
                value={String(itemsPerPage)}
                onValueChange={(value) => {
                  setItemsPerPage(Number(value));
                  setPage(1); // Resetar para a primeira página
                }}
              >
                <SelectTrigger className="w-[70px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="20">20</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                  <SelectItem value="100">100</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        )}
      </>
    );
  };
  
  return (
    <div className="flex flex-col min-h-screen bg-muted/40">
      <TopNav title="Gestão de Precatórios" icon={<FileText className="h-6 w-6 text-primary" />}>
        <div className="ml-auto flex items-center space-x-2">
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => {
              setPage(1); 
              setFiltros(prev => ({...prev})); // Forçar recarregamento
            }}
            disabled={isLoading}
            className="gap-2"
          >
            <RefreshCw size={16} className={isLoading ? 'animate-spin' : ''} />
            Atualizar
          </Button>
          {canCreate && (
            <Button 
              size="sm" 
              className="gap-2" 
              onClick={() => {
                setPrecatorioIdForm(null);
                setFormModalOpen(true);
              }}
            >
              <Plus size={16} />
              Novo Precatório
            </Button>
          )}
        </div>
      </TopNav>
      
      <main className="flex-1 p-4 pt-[70px] md:p-6 space-y-6">
        {renderContent()}
      </main>
      
      {/* Modais */}
      <DetalhesModal 
        isOpen={detalhesModalOpen}
        setIsOpen={setDetalhesModalOpen}
        precatorioId={precatorioIdDetalhes}
        isLoading={isLoading}
        onEdit={(id) => { setPrecatorioIdForm(id); setFormModalOpen(true); }}
        canEdit={canEdit}
      />
      
      <PrecatorioForm 
        isOpen={formModalOpen}
        setIsOpen={setFormModalOpen}
        precatorioId={precatorioIdForm}
        onSuccess={handleFormSuccess}
        clientes={clientes}
        responsaveis={responsaveis}
        isLoading={isLoading}
      />
      
      <ConfirmDeleteModal 
        isOpen={deleteModalOpen}
        setIsOpen={setDeleteModalOpen}
        precatorioId={precatorioIdDelete}
        onConfirm={handleDelete}
        isLoading={isDeleting}
      />
    </div>
  );
}
