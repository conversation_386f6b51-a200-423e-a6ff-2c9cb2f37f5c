import React from 'react';
import { PermissionsManagementInterface } from '@/components/permissions/PermissionsManagementInterface';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import TopNav from '@/components/layout/TopNav';

export default function PermissionsPage() {
  return (
    <ProtectedRoute adminOnly={true}>
      <div className="min-h-screen bg-background">
        <TopNav />
        <main className="container mx-auto px-4 py-8 pt-20">
          <PermissionsManagementInterface />
        </main>
      </div>
    </ProtectedRoute>
  );
}
