import { useState, useEffect, useCallback, useMemo } from "react";
import { toast } from "sonner";
import {
  buscarPrecatoriosKanban,
  buscarColunasKanban,
  atualizarStatusPrecatorioKanban
} from "@/services/kanbanPrecatoriosServiceNew";
import {
  salvarPrecatorio,
  excluirPrecatorio
} from "@/services/precatoriosServiceSimples";
import { useKanbanPermissionsSimple } from "@/hooks/useKanbanPermissionsSimple";
import { useAuth } from "@/hooks/useAuth";
import { TopNav } from "@/components/top-nav";
import { DataVisibilityGuard, useFilteredData } from "@/components/permissions/DataVisibilityGuard";
import { useDataVisibility } from "@/hooks/useDataVisibility";
import { Precatorio, KanbanColuna } from "@/components/Precatorios";
import { KanbanContainerOptimized } from "@/components/Precatorios/KanbanContainerOptimized";
import { KanbanFiltersPanel } from "@/components/Precatorios/KanbanFiltersPanel";
import {
  Loader2,
  RefreshCw,
  AlertCircle,
  Settings,
  Search,
  Filter,
  Plus,
  X,
  FilterX,
  BarChart3
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { executarMigracaoKanban, verificarNecessidadeMigracao } from "@/utils/kanbanMigration";

/**
 * Página Kanban de Precatórios/RPV - Versão Corrigida
 *
 * Esta versão resolve os problemas principais:
 * - Sincronização automática entre status e colunas
 * - Sistema de permissões simplificado
 * - Carregamento otimizado de dados
 * - Funcionalidade drag-and-drop robusta
 */
export default function PrecatoriosKanbanFixed() {
  // Estados principais
  const [precatorios, setPrecatorios] = useState<Precatorio[]>([]);
  const [colunas, setColunas] = useState<KanbanColuna[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [migrating, setMigrating] = useState(false);
  const [needsMigration, setNeedsMigration] = useState(false);

  // Estados para filtros e busca
  const [searchTerm, setSearchTerm] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const [showStats, setShowStats] = useState(false);
  const [filtros, setFiltros] = useState({
    prioridade: "",
    tribunal: "",
    responsavel: "",
    tipo: "",
    tags: [] as string[]
  });

  // Hooks
  const { user, isAdmin } = useAuth();
  const {
    loading: permissionsLoading,
    error: permissionsError,
    canAccessKanban,
    filterVisibleColumns,
    filterVisiblePrecatorios
  } = useKanbanPermissionsSimple();

  // Hook para filtrar dados baseado em permissões granulares
  const { filteredData: precatoriosComPermissao, loading: loadingDataPermissions } = useFilteredData(precatorios, 'precatorios');

  // Função para verificar se precisa de migração
  const verificarMigracao = useCallback(async () => {
    try {
      const precisa = await verificarNecessidadeMigracao();
      setNeedsMigration(precisa);
      return precisa;
    } catch (err) {
      console.error('[KanbanFixed] Erro ao verificar migração:', err);
      return false;
    }
  }, []);

  // Função para executar migração
  const executarMigracao = useCallback(async () => {
    try {
      setMigrating(true);
      toast.info("Executando migração do Kanban...");

      const resultado = await executarMigracaoKanban();

      if (resultado.success) {
        toast.success(`Migração concluída! ${resultado.details.statusCriados} status criados, ${resultado.details.precatoriosCorrigidos} precatórios corrigidos.`);
        setNeedsMigration(false);
        // Recarregar dados após migração
        await carregarDados(false);
      } else {
        toast.error(`Erro na migração: ${resultado.message}`);
      }
    } catch (err) {
      console.error('[KanbanFixed] Erro ao executar migração:', err);
      toast.error("Erro ao executar migração");
    } finally {
      setMigrating(false);
    }
  }, []);

  // Função para carregar dados
  const carregarDados = useCallback(async (showLoading = true) => {
    try {
      if (showLoading) {
        setLoading(true);
      } else {
        setRefreshing(true);
      }
      setError(null);

      console.log('[KanbanFixed] Iniciando carregamento de dados...');

      // Verificar se precisa de migração primeiro
      if (showLoading) {
        await verificarMigracao();
      }

      // Carregar colunas e precatórios em paralelo
      const [colunasData, precatoriosData] = await Promise.all([
        buscarColunasKanban(),
        buscarPrecatoriosKanban()
      ]);

      console.log(`[KanbanFixed] Carregados: ${colunasData.length} colunas, ${precatoriosData.length} precatórios`);

      setColunas(colunasData);
      setPrecatorios(precatoriosData);

      // Mostrar toast de sucesso apenas no refresh manual
      if (!showLoading) {
        toast.success("Dados atualizados com sucesso!");
      }

    } catch (err) {
      console.error('[KanbanFixed] Erro ao carregar dados:', err);
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido ao carregar dados';
      setError(errorMessage);
      toast.error(`Erro ao carregar dados: ${errorMessage}`);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [verificarMigracao]);

  // Carregar dados na inicialização
  useEffect(() => {
    if (user && canAccessKanban()) {
      carregarDados();
    } else if (user && !permissionsLoading && !canAccessKanban()) {
      setError('Você não tem permissão para acessar o Kanban');
      setLoading(false);
    }
  }, [user, canAccessKanban, permissionsLoading, carregarDados]);

  // Função para salvar precatório
  const handleSavePrecatorio = useCallback(async (precatorio: Precatorio) => {
    try {
      console.log('[KanbanFixed] Salvando precatório:', precatorio.id);

      await salvarPrecatorio(precatorio);
      toast.success("Precatório salvo com sucesso!");

      // Recarregar dados
      await carregarDados(false);
    } catch (err) {
      console.error('[KanbanFixed] Erro ao salvar precatório:', err);
      toast.error("Erro ao salvar precatório");
    }
  }, [carregarDados]);

  // Função para excluir precatório
  const handleDeletePrecatorio = useCallback(async (id: string) => {
    try {
      console.log('[KanbanFixed] Excluindo precatório:', id);

      await excluirPrecatorio(id);
      toast.success("Precatório excluído com sucesso!");

      // Recarregar dados
      await carregarDados(false);
    } catch (err) {
      console.error('[KanbanFixed] Erro ao excluir precatório:', err);
      toast.error("Erro ao excluir precatório");
    }
  }, [carregarDados]);

  // Função para mover precatório entre colunas
  const handleMovePrecatorio = useCallback(async (precatorioId: string, novoStatusId: string) => {
    try {
      console.log('[KanbanFixed] Movendo precatório:', precatorioId, 'para status:', novoStatusId);

      const sucesso = await atualizarStatusPrecatorioKanban(precatorioId, novoStatusId);

      if (sucesso) {
        // Atualizar estado local imediatamente para feedback visual
        setPrecatorios(prev => prev.map(p =>
          p.id === precatorioId
            ? { ...p, status_id: novoStatusId }
            : p
        ));

        toast.success("Status atualizado com sucesso!");

        // Recarregar dados para garantir sincronização
        setTimeout(() => carregarDados(false), 1000);
      } else {
        toast.error("Erro ao atualizar status do precatório");
      }
    } catch (err) {
      console.error('[KanbanFixed] Erro ao mover precatório:', err);
      toast.error("Erro ao mover precatório");
    }
  }, [carregarDados]);

  // Função para refresh manual
  const handleRefresh = useCallback(() => {
    carregarDados(false);
  }, [carregarDados]);

  // Filtrar precatórios com base na busca e filtros
  const precatoriosFiltrados = useMemo(() => {
    // Combinar filtros de permissões antigas com permissões granulares
    let filtered = filterVisiblePrecatorios(precatoriosComPermissao);

    // Aplicar busca textual
    if (searchTerm.trim()) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(p =>
        (p.numero_precatorio?.toLowerCase() || '').includes(searchLower) ||
        (p.cliente?.nome?.toLowerCase() || '').includes(searchLower) ||
        (p.responsavel?.nome?.toLowerCase() || '').includes(searchLower) ||
        (p.tribunal?.toLowerCase() || '').includes(searchLower) ||
        (p.natureza?.toLowerCase() || '').includes(searchLower) ||
        (p.observacoes?.toLowerCase() || '').includes(searchLower)
      );
    }

    // Aplicar filtros
    if (filtros.prioridade) {
      filtered = filtered.filter(p => p.prioridade === filtros.prioridade);
    }
    if (filtros.tribunal) {
      filtered = filtered.filter(p => p.tribunal === filtros.tribunal);
    }
    if (filtros.responsavel) {
      filtered = filtered.filter(p => p.responsavel?.nome === filtros.responsavel);
    }
    if (filtros.tipo) {
      filtered = filtered.filter(p => p.tipo === filtros.tipo);
    }
    if (filtros.tags.length > 0) {
      filtered = filtered.filter(p =>
        filtros.tags.some(tag => p.tags?.includes(tag))
      );
    }

    return filtered;
  }, [precatoriosComPermissao, searchTerm, filtros, filterVisiblePrecatorios]);

  // Calcular estatísticas
  const stats = useMemo(() => {
    const total = precatoriosFiltrados.length;
    const valorTotal = precatoriosFiltrados.reduce((sum, p) => sum + (p.valor || 0), 0);
    const porStatus = colunas.map(coluna => ({
      nome: coluna.nome,
      cor: coluna.cor,
      count: precatoriosFiltrados.filter(p => p.status_id === coluna.id).length
    }));

    return { total, valorTotal, porStatus };
  }, [precatoriosFiltrados, colunas]);

  // Limpar filtros
  const limparFiltros = useCallback(() => {
    setSearchTerm("");
    setFiltros({
      prioridade: "",
      tribunal: "",
      responsavel: "",
      tipo: "",
      tags: []
    });
  }, []);

  // Verificar se há filtros ativos
  const temFiltrosAtivos = useMemo(() => {
    return searchTerm.trim() !== "" ||
           filtros.prioridade !== "" ||
           filtros.tribunal !== "" ||
           filtros.responsavel !== "" ||
           filtros.tipo !== "" ||
           filtros.tags.length > 0;
  }, [searchTerm, filtros]);

  // Verificar se está carregando
  if (loading || permissionsLoading) {
    return (
      <div className="flex flex-col h-screen">
        <TopNav title="Kanban Precatórios/RPV" />
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p className="text-muted-foreground">Carregando Kanban...</p>
          </div>
        </div>
      </div>
    );
  }

  // Verificar permissões
  if (!canAccessKanban()) {
    return (
      <div className="flex flex-col h-screen">
        <TopNav title="Kanban Precatórios/RPV" />
        <div className="flex-1 flex items-center justify-center p-8">
          <Alert className="max-w-md">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Você não tem permissão para acessar o Kanban de Precatórios.
              Entre em contato com um administrador.
            </AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  // Verificar erro
  if (error) {
    return (
      <div className="flex flex-col h-screen">
        <TopNav title="Kanban Precatórios/RPV" />
        <div className="flex-1 flex items-center justify-center p-8">
          <div className="text-center max-w-md">
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
            <Button onClick={() => carregarDados()} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Tentar Novamente
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Filtrar dados com base nas permissões
  const colunasVisiveis = filterVisibleColumns(colunas);

  return (
    <div className="flex flex-col h-screen bg-background overflow-hidden fixed inset-0">
      {/* Header fixo com busca e filtros */}
      <div className="flex-none border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 z-10">
        <TopNav
          title="Kanban Precatórios/RPV"
          actions={
            <div className="flex items-center gap-2">
              {/* Busca */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Buscar precatórios..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-9 w-64"
                />
                {searchTerm && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
                    onClick={() => setSearchTerm("")}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                )}
              </div>

              {/* Botões de ação */}
              <Button
                variant={showStats ? "default" : "outline"}
                size="sm"
                onClick={() => setShowStats(!showStats)}
              >
                <BarChart3 className="h-4 w-4 mr-2" />
                Stats
              </Button>

              <Button
                variant={showFilters ? "default" : "outline"}
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
                className="relative"
              >
                <Filter className="h-4 w-4 mr-2" />
                Filtros
                {temFiltrosAtivos && (
                  <Badge className="absolute -top-1 -right-1 h-2 w-2 p-0 bg-primary" />
                )}
              </Button>

              {temFiltrosAtivos && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={limparFiltros}
                >
                  <FilterX className="h-4 w-4 mr-2" />
                  Limpar
                </Button>
              )}

              <Button
                variant="default"
                size="sm"
                onClick={() => {
                  // Implementar criação de novo precatório
                }}
              >
                <Plus className="h-4 w-4 mr-2" />
                Novo Precatório
              </Button>

              {needsMigration && isAdmin && (
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={executarMigracao}
                  disabled={migrating}
                >
                  <Settings className={`h-4 w-4 mr-2 ${migrating ? 'animate-spin' : ''}`} />
                  {migrating ? 'Migrando...' : 'Corrigir'}
                </Button>
              )}

              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={refreshing}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                Atualizar
              </Button>
            </div>
          }
        />

        {/* Painel de estatísticas */}
        {showStats && (
          <div className="px-6 py-3 border-t bg-muted/30">
            <div className="flex items-center gap-6 text-sm">
              <div className="flex items-center gap-2">
                <span className="font-medium">Total:</span>
                <Badge variant="secondary">{stats.total}</Badge>
              </div>
              <DataVisibilityGuard resourceType="precatorios" requireFinancialData={true}>
                <div className="flex items-center gap-2">
                  <span className="font-medium">Valor Total:</span>
                  <Badge variant="secondary">
                    {new Intl.NumberFormat('pt-BR', {
                      style: 'currency',
                      currency: 'BRL'
                    }).format(stats.valorTotal)}
                  </Badge>
                </div>
              </DataVisibilityGuard>
              <div className="flex items-center gap-2">
                {stats.porStatus.map((stat) => (
                  <div key={stat.nome} className="flex items-center gap-1">
                    <div
                      className="w-2 h-2 rounded-full"
                      style={{ backgroundColor: stat.cor }}
                    />
                    <span className="text-xs">{stat.nome}: {stat.count}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Alerta de migração */}
        {needsMigration && isAdmin && (
          <div className="px-6 py-2 border-t">
            <Alert className="border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950">
              <AlertCircle className="h-4 w-4 text-orange-600" />
              <AlertDescription className="text-orange-800 dark:text-orange-200">
                Problemas detectados na configuração do Kanban.
                <Button
                  variant="link"
                  className="p-0 h-auto ml-1 text-orange-600 hover:text-orange-800"
                  onClick={executarMigracao}
                  disabled={migrating}
                >
                  Corrigir automaticamente
                </Button>
              </AlertDescription>
            </Alert>
          </div>
        )}

        {/* Painel de filtros */}
        <KanbanFiltersPanel
          isOpen={showFilters}
          onOpenChange={setShowFilters}
          precatorios={precatorios}
          filtros={filtros}
          onFiltrosChange={setFiltros}
          onLimparFiltros={limparFiltros}
        />
      </div>

      {/* Container do Kanban - ocupa o resto da tela sem scroll */}
      <div className="flex-1 overflow-hidden relative h-full">
        <KanbanContainerOptimized
          precatorios={precatoriosFiltrados}
          colunas={colunasVisiveis}
          onSavePrecatorio={handleSavePrecatorio}
          onDeletePrecatorio={handleDeletePrecatorio}
          onMovePrecatorio={handleMovePrecatorio}
        />
      </div>
    </div>
  );
}
