import React, { useState, useEffect, useMemo } from 'react';
import {
  Activity, ArrowDownRight, ArrowUpRight, Banknote, CalendarDays, CheckCircle2, ChevronDown, ChevronRight, CircleDollarSign,
  Clock, Download, Filter as FilterIcon, FileText, Home, Users, BarChart2, <PERSON><PERSON>hart as PieIcon, LineChart as LineIcon,
  RefreshCw, AlertTriangle, Settings2, Edit3, Trash2, Eye, MoreHorizontal, Search, ExternalLink,
  LayoutGrid, List, CalendarIcon as CalendarFilterIcon, Users2, DollarSign, Briefcase, Percent, TrendingUp, TrendingDown, Target
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger, DropdownMenuCheckboxItem, DropdownMenuGroup } from "@/components/ui/dropdown-menu";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { ResponsiveContainer, LineChart, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, Legend, Line, PieChart, Pie, Cell, BarChart, Bar } from 'recharts';
import { TopNav } from "@/components/top-nav";
import { useDashboardData, DashboardFilters } from '@/hooks/useDashboardData';
import { useAuth } from '@/contexts/AuthContext';
import { DataVisibilityGuard, ConditionalButton } from '@/components/permissions/DataVisibilityGuard';
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger, SheetFooter, SheetClose } from "@/components/ui/sheet";
import { DateRangePicker } from "@/components/ui/date-range-picker"; // Supondo que este componente exista
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { toast } from "sonner";

// Constantes e Helpers
const METRIC_ICON_SIZE = 6;
const CHART_COLORS = ['#3b82f6', '#22c55e', '#f59e0b', '#8b5cf6', '#ef4444', '#14b8a6', '#ec4899'];

const formatCurrency = (value: number | undefined | null, compact = false) => {
  if (value === undefined || value === null) return 'N/A';
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
    notation: compact ? 'compact' : 'standard',
    maximumFractionDigits: compact ? 1 : 2,
  }).format(value);
};

const formatNumber = (value: number | undefined | null, compact = false) => {
  if (value === undefined || value === null) return 'N/A';
  return new Intl.NumberFormat('pt-BR', {
    notation: compact ? 'compact' : 'standard',
    maximumFractionDigits: compact ? 1 : 0,
  }).format(value);
};

// Componente de Filtros Avançados
const AdvancedFiltersPanel: React.FC<{
  filters: DashboardFilters;
  setFilters: (filters: Partial<DashboardFilters>) => void;
  resetFilters: () => void;
  isLoading: boolean;
}> = ({ filters, setFilters, resetFilters, isLoading }) => {
  const { user } = useAuth();
  const [localFilters, setLocalFilters] = useState<DashboardFilters>(filters);

  useEffect(() => {
    setLocalFilters(filters);
  }, [filters]);

  const handleApplyFilters = () => {
    setFilters(localFilters);
  };

  const handleDateRangeChange = (range: { from?: Date; to?: Date } | undefined) => {
    setLocalFilters(prev => ({
      ...prev,
      periodo: 'personalizado',
      dataInicio: range?.from?.toISOString(),
      dataFim: range?.to?.toISOString(),
    }));
  };

  // Mock data para selects - idealmente viria do backend ou de constantes
  const tiposPrecatorioMock = ['Alimentar', 'Comum', 'RPV'];
  const statusPrecatorioMock = ['Novo', 'Em Análise', 'Aguardando Pagamento', 'Pago', 'Cancelado'];
  const responsaveisMock = [{id: '1', nome: 'Fulano'}, {id: '2', nome: 'Ciclano'}]; // Viria do profilesService
  const departamentosMock = ['Jurídico', 'Financeiro', 'Operacional'];

  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button variant="outline" size="sm" className="gap-2">
          <FilterIcon size={16} />
          Filtros Avançados
        </Button>
      </SheetTrigger>
      <SheetContent className="w-[400px] sm:w-[540px] flex flex-col">
        <SheetHeader>
          <SheetTitle>Filtros Avançados do Dashboard</SheetTitle>
          <SheetDescription>
            Refine os dados exibidos no dashboard aplicando filtros específicos.
          </SheetDescription>
        </SheetHeader>
        <div className="flex-grow overflow-y-auto pr-6 space-y-4 py-4">
          <div>
            <label className="text-sm font-medium">Período</label>
            <Select
              value={localFilters.periodo || 'mes'}
              onValueChange={(value) => setLocalFilters(prev => ({ ...prev, periodo: value as any, dataInicio: undefined, dataFim: undefined }))}
            >
              <SelectTrigger><SelectValue /></SelectTrigger>
              <SelectContent>
                <SelectItem value="semana">Última Semana</SelectItem>
                <SelectItem value="mes">Último Mês</SelectItem>
                <SelectItem value="trimestre">Último Trimestre</SelectItem>
                <SelectItem value="ano">Último Ano</SelectItem>
                <SelectItem value="personalizado">Personalizado</SelectItem>
              </SelectContent>
            </Select>
            {localFilters.periodo === 'personalizado' && (
              <div className="mt-2">
                <DateRangePicker
                  onUpdate={({range}) => handleDateRangeChange(range)}
                  initialDateFrom={localFilters.dataInicio ? new Date(localFilters.dataInicio) : undefined}
                  initialDateTo={localFilters.dataFim ? new Date(localFilters.dataFim) : undefined}
                  align="center"
                  locale="pt-BR"
                  showCompare={false}
                />
              </div>
            )}
          </div>

          <div>
            <label className="text-sm font-medium">Tipos de Precatório</label>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="w-full justify-between">
                  Selecionar Tipos <ChevronDown size={16} />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-[350px]">
                {tiposPrecatorioMock.map(tipo => (
                  <DropdownMenuCheckboxItem
                    key={tipo}
                    checked={localFilters.tiposPrecatorio?.includes(tipo)}
                    onCheckedChange={(checked) => {
                      const current = localFilters.tiposPrecatorio || [];
                      setLocalFilters(prev => ({
                        ...prev,
                        tiposPrecatorio: checked ? [...current, tipo] : current.filter(t => t !== tipo)
                      }));
                    }}
                  >
                    {tipo}
                  </DropdownMenuCheckboxItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
            {localFilters.tiposPrecatorio && localFilters.tiposPrecatorio.length > 0 && (
                <div className="mt-1 space-x-1">
                    {localFilters.tiposPrecatorio.map(t => <Badge key={t} variant="secondary">{t}</Badge>)}
                </div>
            )}
          </div>
          
          <div>
            <label className="text-sm font-medium">Status de Precatório</label>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="w-full justify-between">
                  Selecionar Status <ChevronDown size={16} />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-[350px]">
                {statusPrecatorioMock.map(status => (
                  <DropdownMenuCheckboxItem
                    key={status}
                    checked={localFilters.statusPrecatorio?.includes(status)}
                    onCheckedChange={(checked) => {
                      const current = localFilters.statusPrecatorio || [];
                      setLocalFilters(prev => ({
                        ...prev,
                        statusPrecatorio: checked ? [...current, status] : current.filter(s => s !== status)
                      }));
                    }}
                  >
                    {status}
                  </DropdownMenuCheckboxItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
             {localFilters.statusPrecatorio && localFilters.statusPrecatorio.length > 0 && (
                <div className="mt-1 space-x-1">
                    {localFilters.statusPrecatorio.map(s => <Badge key={s} variant="secondary">{s}</Badge>)}
                </div>
            )}
          </div>

          <DataVisibilityGuard resourceType="dashboard" requireAdminOrManager={true}>
            <div>
              <label className="text-sm font-medium">Responsáveis</label>
               <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="w-full justify-between">
                    Selecionar Responsáveis <ChevronDown size={16} />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-[350px]">
                  {responsaveisMock.map(resp => ( // Substituir por dados reais
                    <DropdownMenuCheckboxItem
                      key={resp.id}
                      checked={localFilters.responsaveis?.includes(resp.id)}
                      onCheckedChange={(checked) => {
                        const current = localFilters.responsaveis || [];
                        setLocalFilters(prev => ({
                          ...prev,
                          responsaveis: checked ? [...current, resp.id] : current.filter(r => r !== resp.id)
                        }));
                      }}
                    >
                      {resp.nome}
                    </DropdownMenuCheckboxItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
              {localFilters.responsaveis && localFilters.responsaveis.length > 0 && (
                  <div className="mt-1 space-x-1">
                      {localFilters.responsaveis.map(rId => <Badge key={rId} variant="secondary">{responsaveisMock.find(r=>r.id === rId)?.nome || rId}</Badge>)}
                  </div>
              )}
            </div>
          </DataVisibilityGuard>

          <DataVisibilityGuard resourceType="dashboard" requireFinancialData={true}>
            <div>
              <label className="text-sm font-medium">Valor do Precatório</label>
              <div className="flex gap-2">
                <Input
                  type="number"
                  placeholder="Mínimo"
                  value={localFilters.valorMinimo || ''}
                  onChange={(e) => setLocalFilters(prev => ({ ...prev, valorMinimo: parseFloat(e.target.value) || undefined }))}
                />
                <Input
                  type="number"
                  placeholder="Máximo"
                  value={localFilters.valorMaximo || ''}
                  onChange={(e) => setLocalFilters(prev => ({ ...prev, valorMaximo: parseFloat(e.target.value) || undefined }))}
                />
              </div>
            </div>
          </DataVisibilityGuard>
        </div>
        <SheetFooter className="mt-auto">
          <Button variant="outline" onClick={() => { resetFilters(); }} disabled={isLoading}>
            Limpar Filtros
          </Button>
          <SheetClose asChild>
            <Button onClick={handleApplyFilters} disabled={isLoading}>
              {isLoading ? 'Aplicando...' : 'Aplicar Filtros'}
            </Button>
          </SheetClose>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
};

// Card de Métrica Individual
const MetricCard: React.FC<{
  title: string;
  value: string | number;
  icon: React.ReactNode;
  trend?: number;
  trendPeriod?: string;
  description?: string;
  colorClass?: string;
  isLoading?: boolean;
  onClick?: () => void;
}> = ({ title, value, icon, trend, trendPeriod, description, colorClass = 'text-primary', isLoading, onClick }) => {
  if (isLoading) {
    return (
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <Skeleton className="h-4 w-3/5" />
          <Skeleton className={`h-${METRIC_ICON_SIZE} w-${METRIC_ICON_SIZE} rounded-full`} />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-8 w-4/5 mb-1" />
          <Skeleton className="h-3 w-3/5" />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card onClick={onClick} className={onClick ? "cursor-pointer hover:shadow-lg transition-shadow" : ""}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {React.cloneElement(icon as React.ReactElement, { className: `h-${METRIC_ICON_SIZE} w-${METRIC_ICON_SIZE} text-muted-foreground ${colorClass}` })}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {trend !== undefined && (
          <p className={`text-xs ${trend >= 0 ? 'text-green-600' : 'text-red-600'} flex items-center`}>
            {trend >= 0 ? <ArrowUpRight className="h-4 w-4 mr-1" /> : <ArrowDownRight className="h-4 w-4 mr-1" />}
            {trend.toFixed(1)}% {trendPeriod || 'vs período anterior'}
          </p>
        )}
        {description && <p className="text-xs text-muted-foreground mt-1">{description}</p>}
      </CardContent>
    </Card>
  );
};

// Seção de Métricas
const MetricsSection: React.FC<{ metrics: DashboardMetrics | null; isLoading: boolean }> = ({ metrics, isLoading }) => {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5">
      <MetricCard
        title="Total de Precatórios"
        value={formatNumber(metrics?.totalPrecatorios)}
        icon={<FileText />}
        description="Número total de precatórios no sistema"
        colorClass="text-blue-500"
        isLoading={isLoading}
      />
      <DataVisibilityGuard resourceType="dashboard" requireFinancialData={true}>
        <MetricCard
          title="Valor Total em Precatórios"
          value={formatCurrency(metrics?.valorTotalPrecatorios)}
          icon={<CircleDollarSign />}
          description="Soma dos valores de todos os precatórios"
          colorClass="text-green-500"
          isLoading={isLoading}
        />
      </DataVisibilityGuard>
      <MetricCard
        title="Tarefas Pendentes"
        value={formatNumber(metrics?.tarefasPendentes)}
        icon={<Clock />}
        description="Tarefas que requerem atenção"
        colorClass="text-orange-500"
        isLoading={isLoading}
      />
      <MetricCard
        title="Tarefas Concluídas"
        value={formatNumber(metrics?.tarefasConcluidas)}
        icon={<CheckCircle2 />}
        description="Tarefas finalizadas com sucesso"
        colorClass="text-teal-500"
        isLoading={isLoading}
      />
      <MetricCard
        title="Clientes Ativos"
        value={formatNumber(metrics?.clientesAtivos)}
        icon={<Users />}
        description="Número de clientes com status ativo"
        colorClass="text-purple-500"
        isLoading={isLoading}
      />
    </div>
  );
};

// Seção de Gráficos
const ChartsSection: React.FC<{
  evolucao: { labels: string[]; datasets: any[] } | null;
  metrics: DashboardMetrics | null;
  isLoadingEvolucao: boolean;
  isLoadingMetrics: boolean;
}> = ({ evolucao, metrics, isLoadingEvolucao, isLoadingMetrics }) => {
  const [evolucaoChartType, setEvolucaoChartType] = useState<'line' | 'bar'>('line');

  const precatoriosPorStatusData = useMemo(() => {
    if (!metrics?.precatoriosPorStatus) return [];
    return Object.entries(metrics.precatoriosPorStatus).map(([name, value], index) => ({
      name,
      value,
      fill: CHART_COLORS[index % CHART_COLORS.length],
    }));
  }, [metrics?.precatoriosPorStatus]);

  const precatoriosPorTipoData = useMemo(() => {
    if (!metrics?.precatoriosPorTipo) return [];
    return Object.entries(metrics.precatoriosPorTipo).map(([name, value], index) => ({
      name,
      value,
      fill: CHART_COLORS[index % CHART_COLORS.length],
    }));
  }, [metrics?.precatoriosPorTipo]);

  const precatoriosPorMesData = useMemo(() => {
    if (!metrics?.precatoriosPorMes) return [];
    return Object.entries(metrics.precatoriosPorMes).map(([name, value]) => ({
      name, // ex: "05/2025"
      value,
    })).sort((a,b) => { // Sort by date
        const [aMonth, aYear] = a.name.split('/');
        const [bMonth, bYear] = b.name.split('/');
        return new Date(+aYear, +aMonth -1).getTime() - new Date(+bYear, +bMonth-1).getTime();
    });
  }, [metrics?.precatoriosPorMes]);


  if (isLoadingEvolucao || isLoadingMetrics) {
    return (
      <div className="grid gap-4 md:grid-cols-2 mt-6">
        <Card><CardHeader><Skeleton className="h-6 w-1/2" /></CardHeader><CardContent><Skeleton className="h-64 w-full" /></CardContent></Card>
        <Card><CardHeader><Skeleton className="h-6 w-1/2" /></CardHeader><CardContent><Skeleton className="h-64 w-full" /></CardContent></Card>
        <Card><CardHeader><Skeleton className="h-6 w-1/2" /></CardHeader><CardContent><Skeleton className="h-64 w-full" /></CardContent></Card>
        <Card><CardHeader><Skeleton className="h-6 w-1/2" /></CardHeader><CardContent><Skeleton className="h-64 w-full" /></CardContent></Card>
      </div>
    );
  }

  return (
    <div className="grid gap-6 mt-6 md:grid-cols-1 lg:grid-cols-2">
      <Card className="lg:col-span-2">
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Evolução de Precatórios</CardTitle>
            <DropdownMenu>
              <DropdownMenuTrigger asChild><Button variant="outline" size="sm">Visualização <ChevronDown size={16} className="ml-2"/></Button></DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuCheckboxItem checked={evolucaoChartType === 'line'} onCheckedChange={() => setEvolucaoChartType('line')}>Linha</DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem checked={evolucaoChartType === 'bar'} onCheckedChange={() => setEvolucaoChartType('bar')}>Barra</DropdownMenuCheckboxItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          <CardDescription>Novos precatórios registrados ao longo do tempo.</CardDescription>
        </CardHeader>
        <CardContent className="h-[350px] pr-0">
          <ResponsiveContainer width="100%" height="100%">
            {evolucaoChartType === 'line' ? (
              <LineChart data={evolucao?.datasets?.[0]?.data ? evolucao.datasets[0].data.map((val: any, i: number) => ({name: evolucao.labels[i], value: val})) : []}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <RechartsTooltip formatter={(value: any) => [value, "Novos Precatórios"]} />
                <Legend />
                <Line type="monotone" dataKey="value" name="Novos Precatórios" stroke={CHART_COLORS[0]} strokeWidth={2} dot={{ r: 3 }} activeDot={{ r: 5 }} />
              </LineChart>
            ) : (
              <BarChart data={evolucao?.datasets?.[0]?.data ? evolucao.datasets[0].data.map((val: any, i: number) => ({name: evolucao.labels[i], value: val})) : []}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <RechartsTooltip formatter={(value: any) => [value, "Novos Precatórios"]} />
                <Legend />
                <Bar dataKey="value" name="Novos Precatórios" fill={CHART_COLORS[0]} />
              </BarChart>
            )}
          </ResponsiveContainer>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Distribuição por Status</CardTitle>
          <CardDescription>Percentual de precatórios em cada status.</CardDescription>
        </CardHeader>
        <CardContent className="h-[300px] flex items-center justify-center">
          {precatoriosPorStatusData.length > 0 ? (
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie data={precatoriosPorStatusData} dataKey="value" nameKey="name" cx="50%" cy="50%" outerRadius={80} labelLine={false} label={({ name, percent }) => `${name} (${(percent * 100).toFixed(0)}%)`}>
                  {precatoriosPorStatusData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.fill} />
                  ))}
                </Pie>
                <RechartsTooltip formatter={(value: any, name: any) => [value, name]} />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          ) : <p className="text-muted-foreground">Sem dados de status para exibir.</p>}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Distribuição por Tipo</CardTitle>
          <CardDescription>Percentual de precatórios por tipo.</CardDescription>
        </CardHeader>
        <CardContent className="h-[300px] flex items-center justify-center">
         {precatoriosPorTipoData.length > 0 ? (
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie data={precatoriosPorTipoData} dataKey="value" nameKey="name" cx="50%" cy="50%" innerRadius={50} outerRadius={80} label>
                  {precatoriosPorTipoData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.fill} />
                  ))}
                </Pie>
                <RechartsTooltip formatter={(value: any, name: any) => [value, name]} />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          ) : <p className="text-muted-foreground">Sem dados de tipo para exibir.</p>}
        </CardContent>
      </Card>
      
      <Card className="lg:col-span-2">
        <CardHeader>
            <CardTitle>Precatórios por Mês de Criação</CardTitle>
            <CardDescription>Quantidade de precatórios criados em cada mês.</CardDescription>
        </CardHeader>
        <CardContent className="h-[350px] pr-0">
            <ResponsiveContainer width="100%" height="100%">
                <BarChart data={precatoriosPorMesData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <RechartsTooltip formatter={(value: any) => [value, "Precatórios Criados"]}/>
                    <Legend />
                    <Bar dataKey="value" name="Precatórios Criados" fill={CHART_COLORS[1]} />
                </BarChart>
            </ResponsiveContainer>
        </CardContent>
      </Card>
    </div>
  );
};

// Seção de Desempenho da Equipe
const TeamPerformanceSection: React.FC<{ metrics: DashboardMetrics | null; isLoading: boolean }> = ({ metrics, isLoading }) => {
  const { user } = useAuth();

  if (!['admin', 'gerente_geral', 'gerente_precatorio', 'gerente_rpv'].includes(user?.role || '')) {
    return null; // Não mostrar para quem não tem permissão
  }
  
  const equipe = metrics?.desempenhoEquipe || [];

  if (isLoading) {
    return (
      <Card className="mt-6">
        <CardHeader><CardTitle>Desempenho da Equipe</CardTitle><Skeleton className="h-4 w-1/4 mt-1" /></CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="flex items-center space-x-4">
                <Skeleton className="h-12 w-12 rounded-full" />
                <div className="space-y-2 flex-1">
                  <Skeleton className="h-4 w-3/5" />
                  <Skeleton className="h-4 w-4/5" />
                </div>
                <Skeleton className="h-8 w-1/5" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }
  
  if (equipe.length === 0) {
      return (
          <Card className="mt-6">
              <CardHeader><CardTitle>Desempenho da Equipe</CardTitle></CardHeader>
              <CardContent>
                  <p className="text-muted-foreground">Não há dados de desempenho da equipe para exibir.</p>
              </CardContent>
          </Card>
      )
  }

  return (
    <Card className="mt-6">
      <CardHeader>
        <CardTitle>Desempenho da Equipe</CardTitle>
        <CardDescription>Visão geral do desempenho dos membros da equipe.</CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Membro</TableHead>
              <TableHead>Cargo</TableHead>
              <TableHead className="text-right">Tarefas Concluídas</TableHead>
              <TableHead className="text-right">Precatórios Processados</TableHead>
              <TableHead className="text-right">Tempo Médio (dias)</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {equipe.map((membro) => (
              <TableRow key={membro.userId}>
                <TableCell>
                  <div className="flex items-center gap-3">
                    <Avatar className="h-9 w-9">
                      <AvatarImage src={membro.foto_url || undefined} alt={membro.nome} />
                      <AvatarFallback>{membro.nome?.charAt(0).toUpperCase()}</AvatarFallback>
                    </Avatar>
                    <span className="font-medium">{membro.nome}</span>
                  </div>
                </TableCell>
                <TableCell>{membro.cargo}</TableCell>
                <TableCell className="text-right">{formatNumber(membro.tarefasConcluidas)}</TableCell>
                <TableCell className="text-right">{formatNumber(membro.precatoriosProcessados)}</TableCell>
                <TableCell className="text-right">{membro.tempoMedioProcessamento.toFixed(1)}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};

// Componente Principal do Dashboard
export default function DashboardEnhancedPage() {
  const {
    metrics, evolucao, isLoading, isRefreshing, isLoadingEvolucao, error, lastUpdated,
    refreshData, filters, setFilters, resetFilters, isFilterActive
  } = useDashboardData();
  const { user } = useAuth();

  const handleRefresh = () => {
    refreshData(true); // true para mostrar toast de sucesso
  };
  
  const exportData = () => {
    // Lógica de exportação (ex: JSON.stringify(metrics) ou conversão para CSV)
    const dataToExport = {
        metrics,
        evolucao,
        filters,
        lastUpdated: lastUpdated?.toISOString()
    };
    const jsonString = `data:text/json;charset=utf-8,${encodeURIComponent(
      JSON.stringify(dataToExport, null, 2)
    )}`;
    const link = document.createElement("a");
    link.href = jsonString;
    link.download = `dashboard_carbonario_${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    toast.success("Dados do dashboard exportados como JSON.");
  };

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4 text-center bg-background">
        <AlertTriangle className="w-16 h-16 text-destructive mb-4" />
        <h2 className="text-2xl font-semibold mb-2">Erro ao Carregar o Dashboard</h2>
        <p className="text-muted-foreground mb-6">{error.message}</p>
        <Button onClick={() => window.location.reload()} className="gap-2">
          <RefreshCw size={16} /> Tentar Novamente
        </Button>
      </div>
    );
  }
  
  // Simulação de notificações
  const notifications = [
    { id: '1', message: 'Novo precatório #12345 atribuído a você.', type: 'info', unread: true, date: new Date(Date.now() - 3600000)},
    { id: '2', message: 'Tarefa "Revisar Documentos" vence hoje.', type: 'alert', unread: true, date: new Date(Date.now() - 7200000)},
    { id: '3', message: 'Relatório mensal gerado com sucesso.', type: 'success', unread: false, date: new Date(Date.now() - 86400000)},
  ];
  const unreadNotifications = notifications.filter(n => n.unread).length;


  return (
    <TooltipProvider>
      <div className="flex flex-col min-h-screen bg-muted/40">
        <TopNav title="Dashboard Analítico" icon={<LayoutGrid className="h-6 w-6 text-primary" />}>
            <div className="ml-auto flex items-center space-x-2">
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button variant="outline" size="icon" className="relative">
                            <Bell size={18} />
                            {unreadNotifications > 0 && (
                                <span className="absolute -top-1 -right-1 flex h-4 w-4 items-center justify-center rounded-full bg-red-500 text-xs text-white">
                                    {unreadNotifications}
                                </span>
                            )}
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-80">
                        <DropdownMenuLabel>Notificações</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        {notifications.map(n => (
                            <DropdownMenuItem key={n.id} className={`flex items-start gap-2 ${n.unread ? 'font-semibold' : ''}`}>
                                {n.type === 'info' && <Info size={16} className="text-blue-500 mt-1"/>}
                                {n.type === 'alert' && <AlertTriangle size={16} className="text-orange-500 mt-1"/>}
                                {n.type === 'success' && <CheckCircle2 size={16} className="text-green-500 mt-1"/>}
                                <div className="flex-1">
                                    <p className="text-sm">{n.message}</p>
                                    <p className="text-xs text-muted-foreground">{n.date.toLocaleString('pt-BR')}</p>
                                </div>
                            </DropdownMenuItem>
                        ))}
                         <DropdownMenuSeparator />
                        <DropdownMenuItem className="justify-center">
                            Ver todas
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
                 <AdvancedFiltersPanel filters={filters} setFilters={setFilters} resetFilters={resetFilters} isLoading={isLoading || isRefreshing}/>
                <ConditionalButton resourceType="dashboard" action="export">
                     <Button variant="outline" size="sm" className="gap-2" onClick={exportData}>
                        <Download size={16} /> Exportar
                    </Button>
                </ConditionalButton>
                <Button variant="ghost" size="sm" onClick={handleRefresh} disabled={isLoading || isRefreshing} className="gap-2">
                  <RefreshCw size={16} className={isRefreshing ? "animate-spin" : ""} />
                  {isRefreshing ? "Atualizando..." : "Atualizar"}
                </Button>
            </div>
        </TopNav>

        <main className="flex-1 p-4 pt-[70px] md:p-6 space-y-6">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div>
              <h1 className="text-2xl font-semibold">Olá, {user?.nome || 'Usuário'}!</h1>
              <p className="text-muted-foreground">
                Bem-vindo(a) ao seu painel de controle do Sistema Carbonário e Carbonaro.
                {lastUpdated && ` Última atualização: ${lastUpdated.toLocaleTimeString('pt-BR')}`}
              </p>
            </div>
             {isFilterActive && (
                <Badge variant="info" className="mt-2 md:mt-0">
                    Filtros ativos. <Button variant="link" size="sm" className="p-0 h-auto ml-1" onClick={resetFilters}>Limpar</Button>
                </Badge>
            )}
          </div>
          
          <MetricsSection metrics={metrics} isLoading={isLoading && !metrics} />
          
          <Tabs defaultValue="visao_geral" className="w-full">
            <TabsList className="grid w-full grid-cols-2 md:grid-cols-4 mb-4">
              <TabsTrigger value="visao_geral">Visão Geral</TabsTrigger>
              <TabsTrigger value="desempenho_equipe">Desempenho da Equipe</TabsTrigger>
              <TabsTrigger value="analise_precatorios">Análise de Precatórios</TabsTrigger>
              <TabsTrigger value="financeiro_avancado">Financeiro Avançado</TabsTrigger>
            </TabsList>

            <TabsContent value="visao_geral">
              <ChartsSection evolucao={evolucao} metrics={metrics} isLoadingEvolucao={isLoadingEvolucao && !evolucao} isLoadingMetrics={isLoading && !metrics} />
            </TabsContent>
            <TabsContent value="desempenho_equipe">
                <DataVisibilityGuard resourceType="dashboard" requireAdminOrManager={true}
                    fallback={<p className="text-muted-foreground p-4 text-center">Você não tem permissão para visualizar o desempenho da equipe.</p>}
                >
                    <TeamPerformanceSection metrics={metrics} isLoading={isLoading && !metrics} />
                </DataVisibilityGuard>
            </TabsContent>
            <TabsContent value="analise_precatorios">
                <Card>
                    <CardHeader><CardTitle>Análise Detalhada de Precatórios</CardTitle></CardHeader>
                    <CardContent>
                        <p className="text-muted-foreground">Seção em desenvolvimento. Aqui você poderá ver análises mais profundas sobre os tipos, status e valores dos precatórios.</p>
                        {/* Futuros gráficos e tabelas detalhadas */}
                    </CardContent>
                </Card>
            </TabsContent>
            <TabsContent value="financeiro_avancado">
                <DataVisibilityGuard resourceType="dashboard" requireFinancialData={true}
                    fallback={<p className="text-muted-foreground p-4 text-center">Você não tem permissão para visualizar dados financeiros avançados.</p>}
                >
                    <Card>
                        <CardHeader><CardTitle>Análise Financeira Avançada</CardTitle></CardHeader>
                        <CardContent>
                            <p className="text-muted-foreground">Seção em desenvolvimento. Aqui você poderá ver projeções financeiras, fluxo de caixa e outros indicadores.</p>
                            {/* Futuros gráficos e tabelas financeiras */}
                        </CardContent>
                    </Card>
                </DataVisibilityGuard>
            </TabsContent>
          </Tabs>

        </main>
        <footer className="p-4 text-center text-xs text-muted-foreground border-t">
            Sistema Carbonário e Carbonaro &copy; {new Date().getFullYear()} - Equipe Carbonaro e Carbonário
        </footer>
      </div>
    </TooltipProvider>
  );
}

// Adicionar Badge variant="info" se não existir
// components/ui/badge.tsx
// ...
// info: "border-transparent bg-blue-500 text-blue-50 hover:bg-blue-500/80 dark:bg-blue-700 dark:text-blue-50 dark:hover:bg-blue-700/80",
// ...
