"use client";

import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { TopNav } from "@/components/top-nav";
import {
  FileText,
  Search,
  Filter,
  Plus,
  Download,
  Upload,
  Trash2,
  CheckCircle,
  XCircle,
  Clock,
  FileUp,
  FileEdit,
  Tag,
  User,
  Calendar,
  FileSpreadsheet,
  RefreshCw,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import { buscarDocumentos, Documento, obterUrlDownload, atualizarStatusDocumento, excluirDocumento } from "@/services/documentosService";
import { DocumentUploader } from "@/components/DocumentUploader";
import { DocumentEditor } from "@/components/DocumentEditor";
import { buscarClientes } from "@/modules/common/services/clientesService";
import { buscarPrecatorios } from "@/services/precatoriosService";

export default function Documents() {
  const navigate = useNavigate();
  const [documentos, setDocumentos] = useState<Documento[]>([]);
  const [filteredDocumentos, setFilteredDocumentos] = useState<Documento[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("todos");
  const [tipoFilter, setTipoFilter] = useState<string>("todos");
  const [categoriaFilter, setCategoriaFilter] = useState<string>("todos");
  const [activeTab, setActiveTab] = useState("todos");
  const [uploaderOpen, setUploaderOpen] = useState(false);
  const [editorOpen, setEditorOpen] = useState(false);
  const [clientes, setClientes] = useState<any[]>([]);
  const [precatorios, setPrecatorios] = useState<any[]>([]);
  const [selectedDocumento, setSelectedDocumento] = useState<Documento | null>(null);

  // Carregar documentos
  useEffect(() => {
    const carregarDados = async () => {
      try {
        setIsLoading(true);
        console.log("Iniciando carregamento de dados da página de documentos...");

        // Carregar documentos
        const docs = await buscarDocumentos();
        console.log(`Documentos carregados: ${docs.length}`);
        setDocumentos(docs);
        setFilteredDocumentos(docs);

        // Carregar clientes e precatórios para os dropdowns
        try {
          const clientesData = await buscarClientes();
          console.log(`Clientes carregados: ${clientesData.length}`);
          setClientes(clientesData);
        } catch (error) {
          console.error("Erro ao carregar clientes:", error);
          setClientes([]);
        }

        try {
          const precatoriosData = await buscarPrecatorios();
          console.log(`Precatórios carregados: ${precatoriosData.length}`);
          setPrecatorios(precatoriosData);
        } catch (error) {
          console.error("Erro ao carregar precatórios:", error);
          setPrecatorios([]);
        }
      } catch (error) {
        console.error("Erro ao carregar documentos:", error);
        toast.error("Erro ao carregar documentos", {
          description: "Verifique sua conexão e tente novamente"
        });
        setDocumentos([]);
        setFilteredDocumentos([]);
      } finally {
        setIsLoading(false);
      }
    };

    carregarDados();
  }, []);

  // Aplicar filtros
  useEffect(() => {
    let filtered = [...documentos];

    // Filtrar por termo de busca
    if (searchTerm) {
      filtered = filtered.filter(doc =>
        doc.nome.toLowerCase().includes(searchTerm.toLowerCase()) ||
        doc.tipo.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filtrar por status
    if (statusFilter !== "todos") {
      filtered = filtered.filter(doc => doc.status === statusFilter);
    }

    // Filtrar por tipo
    if (tipoFilter !== "todos") {
      filtered = filtered.filter(doc => doc.tipo === tipoFilter);
    }

    // Filtrar por categoria
    if (categoriaFilter !== "todos") {
      filtered = filtered.filter(doc => doc.categoria === categoriaFilter);
    }

    // Filtrar por tab
    if (activeTab === "pendentes") {
      filtered = filtered.filter(doc => doc.status === "pendente");
    } else if (activeTab === "aprovados") {
      filtered = filtered.filter(doc => doc.status === "aprovado");
    } else if (activeTab === "rejeitados") {
      filtered = filtered.filter(doc => doc.status === "rejeitado");
    }

    setFilteredDocumentos(filtered);
  }, [documentos, searchTerm, statusFilter, tipoFilter, categoriaFilter, activeTab]);

  // Função para baixar documento
  const handleDownload = async (documento: Documento) => {
    try {
      if (!documento.url) {
        toast.error("URL do documento não disponível");
        return;
      }

      const url = await obterUrlDownload(documento.url);
      window.open(url, "_blank");
    } catch (error) {
      console.error("Erro ao baixar documento:", error);
      toast.error("Erro ao baixar documento");
    }
  };

  // Função para atualizar status
  const handleUpdateStatus = async (documento: Documento, novoStatus: 'pendente' | 'aprovado' | 'rejeitado') => {
    try {
      if (!documento.id || !documento.categoria) {
        toast.error("Informações do documento incompletas");
        return;
      }

      await atualizarStatusDocumento(documento.id, novoStatus, documento.categoria as 'cliente' | 'precatorio');

      // Atualizar a lista de documentos
      setDocumentos(prev =>
        prev.map(doc =>
          doc.id === documento.id ? { ...doc, status: novoStatus } : doc
        )
      );

      toast.success(`Status atualizado para ${novoStatus}`);
    } catch (error) {
      console.error("Erro ao atualizar status:", error);
      toast.error("Erro ao atualizar status");
    }
  };

  // Função para excluir documento
  const handleDelete = async (documento: Documento) => {
    try {
      if (!documento.id || !documento.categoria) {
        toast.error("Informações do documento incompletas");
        return;
      }

      await excluirDocumento(documento.id, documento.categoria as 'cliente' | 'precatorio');

      // Remover da lista de documentos
      setDocumentos(prev => prev.filter(doc => doc.id !== documento.id));

      toast.success("Documento excluído com sucesso");
    } catch (error) {
      console.error("Erro ao excluir documento:", error);
      toast.error("Erro ao excluir documento");
    }
  };

  // Função para abrir a página de criação de novo documento
  const handleNewDocument = () => {
    navigate('/documents/novo');
  };

  // Função para atualizar a lista após upload ou criação
  const handleDocumentoAdicionado = (novoDocumento: Documento) => {
    setDocumentos(prev => [...prev, novoDocumento]);
    setUploaderOpen(false);
    setEditorOpen(false);
    toast.success("Documento adicionado com sucesso");
  };

  // Função para recarregar documentos
  const recarregarDocumentos = async () => {
    try {
      setIsLoading(true);
      const docs = await buscarDocumentos();
      setDocumentos(docs);
      setFilteredDocumentos(docs);
      toast.success("Documentos atualizados!");
    } catch (error) {
      console.error("Erro ao recarregar documentos:", error);
      toast.error("Erro ao recarregar documentos");
    } finally {
      setIsLoading(false);
    }
  };

  // Obter tipos únicos para o filtro
  const tiposUnicos = Array.from(new Set(documentos.map(doc => doc.tipo)));

  // Renderizar status do documento
  const renderStatus = (status: string) => {
    switch (status) {
      case 'aprovado':
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            <CheckCircle className="w-3 h-3 mr-1" /> Aprovado
          </Badge>
        );
      case 'rejeitado':
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            <XCircle className="w-3 h-3 mr-1" /> Rejeitado
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
            <Clock className="w-3 h-3 mr-1" /> Pendente
          </Badge>
        );
    }
  };

  return (
    <div className="flex flex-col h-screen w-screen overflow-hidden">
      <TopNav
        title="Documentos"
        icon={<FileText className="h-6 w-6 text-primary" />}
      />

      <div className="flex flex-1 overflow-auto pt-[65px]">
        <div className="p-4 md:p-6 rounded-tl-2xl border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-900 flex flex-col gap-6 flex-1 w-full h-full">
          {/* Cabeçalho e Filtros */}
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div>
              <h1 className="text-2xl font-bold">Gerenciamento de Documentos</h1>
              <p className="text-muted-foreground">
                Gerencie todos os documentos do sistema em um só lugar
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-2">
              <Button onClick={() => setUploaderOpen(true)} className="gap-2">
                <Upload className="h-4 w-4" />
                Enviar Documento
              </Button>
              <Button onClick={handleNewDocument} variant="outline" className="gap-2">
                <FileEdit className="h-4 w-4" />
                Novo Documento
              </Button>
              <Button onClick={recarregarDocumentos} variant="outline" className="gap-2" disabled={isLoading}>
                <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
                Atualizar
              </Button>
            </div>
          </div>

          {/* Filtros */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Pesquisar documentos..."
                className="pl-10"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filtrar por status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="todos">Todos os status</SelectItem>
                <SelectItem value="pendente">Pendentes</SelectItem>
                <SelectItem value="aprovado">Aprovados</SelectItem>
                <SelectItem value="rejeitado">Rejeitados</SelectItem>
              </SelectContent>
            </Select>

            <Select value={tipoFilter} onValueChange={setTipoFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filtrar por tipo" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="todos">Todos os tipos</SelectItem>
                {tiposUnicos.map((tipo) => (
                  <SelectItem key={tipo} value={tipo}>
                    {tipo}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={categoriaFilter} onValueChange={setCategoriaFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filtrar por categoria" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="todos">Todas as categorias</SelectItem>
                <SelectItem value="cliente">Clientes</SelectItem>
                <SelectItem value="precatorio">Precatórios</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid grid-cols-4 w-full max-w-md">
              <TabsTrigger value="todos" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Todos
              </TabsTrigger>
              <TabsTrigger value="pendentes" className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Pendentes
              </TabsTrigger>
              <TabsTrigger value="aprovados" className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4" />
                Aprovados
              </TabsTrigger>
              <TabsTrigger value="rejeitados" className="flex items-center gap-2">
                <XCircle className="h-4 w-4" />
                Rejeitados
              </TabsTrigger>
            </TabsList>

            {/* Conteúdo das tabs (mesmo para todas, filtrado pelo estado) */}
            <TabsContent value={activeTab} className="mt-6">
              {isLoading ? (
                <div className="flex justify-center items-center h-64">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
              ) : filteredDocumentos.length === 0 ? (
                <div className="flex flex-col items-center justify-center py-12 text-center">
                  <FileText className="h-16 w-16 text-muted-foreground mb-4 opacity-50" />
                  <h3 className="text-lg font-medium mb-2">Nenhum documento encontrado</h3>
                  <p className="text-sm text-muted-foreground mb-4 max-w-md">
                    Não foram encontrados documentos com os filtros selecionados.
                  </p>
                  <Button onClick={() => {
                    setSearchTerm("");
                    setStatusFilter("todos");
                    setTipoFilter("todos");
                    setCategoriaFilter("todos");
                    setActiveTab("todos");
                  }}>
                    Limpar Filtros
                  </Button>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {filteredDocumentos.map((documento) => (
                    <Card key={documento.id} className="overflow-hidden hover:shadow-md transition-shadow">
                      <CardHeader className="pb-2">
                        <div className="flex justify-between items-start">
                          <div className="flex-1 truncate">
                            <CardTitle
                              className="text-lg truncate hover:text-primary cursor-pointer"
                              onClick={() => navigate(`/documents/${documento.id}`)}
                            >
                              {documento.nome}
                            </CardTitle>
                            <CardDescription className="flex items-center gap-1">
                              <Tag className="h-3 w-3" />
                              {documento.tipo}
                            </CardDescription>
                          </div>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon" className="h-8 w-8">
                                <FileText className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Ações</DropdownMenuLabel>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem onClick={() => navigate(`/documents/${documento.id}`)}>
                                <FileText className="h-4 w-4 mr-2" />
                                Visualizar
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleDownload(documento)}>
                                <Download className="h-4 w-4 mr-2" />
                                Baixar
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleUpdateStatus(documento, 'aprovado')}>
                                <CheckCircle className="h-4 w-4 mr-2" />
                                Aprovar
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleUpdateStatus(documento, 'rejeitado')}>
                                <XCircle className="h-4 w-4 mr-2" />
                                Rejeitar
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleUpdateStatus(documento, 'pendente')}>
                                <Clock className="h-4 w-4 mr-2" />
                                Marcar como Pendente
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                onClick={() => handleDelete(documento)}
                                className="text-red-600 focus:text-red-600"
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Excluir
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="flex items-center gap-2 mb-2">
                          {renderStatus(documento.status)}
                          <Badge variant="outline" className="bg-primary/10">
                            {documento.categoria === 'cliente' ? 'Cliente' : 'Precatório'}
                          </Badge>
                        </div>

                        <div className="text-sm text-muted-foreground space-y-1">
                          {documento.cliente_id && (
                            <div className="flex items-center gap-1">
                              <User className="h-3 w-3" />
                              <span>
                                Cliente: {documento.cliente_nome || 'Não especificado'}
                              </span>
                            </div>
                          )}

                          {documento.precatorio_id && (
                            <div className="flex items-center gap-1">
                              <FileSpreadsheet className="h-3 w-3" />
                              <span>
                                Precatório: {documento.precatorio_numero || documento.precatorio_id}
                              </span>
                            </div>
                          )}

                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            <span>
                              {new Date(documento.data_upload).toLocaleDateString('pt-BR')}
                            </span>
                          </div>

                          <div className="flex items-center gap-1">
                            <FileUp className="h-3 w-3" />
                            <span>{documento.tamanho || '128 KB'}</span>
                          </div>
                        </div>
                      </CardContent>
                      <CardFooter className="pt-0 pb-3 flex gap-2">
                        <Button
                          variant="default"
                          size="sm"
                          className="flex-1"
                          onClick={() => navigate(`/documents/${documento.id}`)}
                        >
                          <FileText className="h-3 w-3 mr-2" />
                          Visualizar
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="flex-1"
                          onClick={() => handleDownload(documento)}
                        >
                          <Download className="h-3 w-3 mr-2" />
                          Baixar
                        </Button>
                      </CardFooter>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* Dialog para upload de documento */}
      <Dialog open={uploaderOpen} onOpenChange={setUploaderOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Enviar Novo Documento</DialogTitle>
            <DialogDescription>
              Faça upload de um documento e associe-o a um cliente ou precatório.
            </DialogDescription>
          </DialogHeader>

          <DocumentUploader
            clientes={clientes}
            precatorios={precatorios}
            onDocumentoAdicionado={handleDocumentoAdicionado}
            onCancel={() => setUploaderOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Dialog para criar novo documento */}
      <Dialog open={editorOpen} onOpenChange={setEditorOpen}>
        <DialogContent className="sm:max-w-[800px] h-[80vh]">
          <DialogHeader>
            <DialogTitle>Criar Novo Documento</DialogTitle>
            <DialogDescription>
              Crie um novo documento de texto e associe-o a um cliente ou precatório.
            </DialogDescription>
          </DialogHeader>

          <DocumentEditor
            documento={selectedDocumento}
            clientes={clientes}
            precatorios={precatorios}
            onDocumentoAdicionado={handleDocumentoAdicionado}
            onCancel={() => setEditorOpen(false)}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}
