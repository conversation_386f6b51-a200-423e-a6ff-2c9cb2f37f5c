import { 
  Users, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar, 
  Clock, 
  FileText, 
  Award,
  Briefcase,
  GraduationCap,
  Star,
  Activity,
  ChevronLeft,
  BookOpen,
  Target,
  Wallet,
  Clock3,
  CalendarDays,
  FileCheck,
  MessageSquare,
  History,
  Bookmark,
  PhoneCall,
  Send,
  UserPlus,
  BarChart,
  Percent,
  Timer
} from "lucide-react";
import { motion } from "framer-motion";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useNavigate, useParams } from "react-router-dom";
import { ChartContainer } from "@/components/ui/chart";
import { AreaChart, Area, XAxis, YAxis, <PERSON><PERSON><PERSON>, <PERSON>, Line, ResponsiveContainer } from 'recharts';
import { Progress } from "@/components/ui/progress";

const performanceHistory = [
  { month: 'Jan', desempenho: 88, media: 85 },
  { month: 'Fev', desempenho: 90, media: 86 },
  { month: 'Mar', desempenho: 92, media: 87 },
  { month: 'Abr', desempenho: 91, media: 86 },
  { month: 'Mai', desempenho: 94, media: 88 },
  { month: 'Jun', desempenho: 95, media: 89 },
];

const achievements = [
  {
    title: "Melhor Performance",
    date: "Junho 2023",
    description: "Maior índice de produtividade do mês",
    icon: <Star className="w-4 h-4" />
  },
  {
    title: "Certificação OAB",
    date: "Março 2023",
    description: "Aprovação com distinção",
    icon: <Award className="w-4 h-4" />
  },
  {
    title: "Curso de Especialização",
    date: "Janeiro 2023",
    description: "Direito Tributário",
    icon: <GraduationCap className="w-4 h-4" />
  }
];

const processos = [
  {
    numero: "2024.001.123",
    cliente: "Empresa ABC Ltda",
    tipo: "Tributário",
    status: "Em andamento",
    data: "15/03/2024"
  },
  {
    numero: "2024.001.124",
    cliente: "João Silva",
    tipo: "Trabalhista",
    status: "Concluído",
    data: "10/03/2024"
  },
  {
    numero: "2024.001.125",
    cliente: "Maria Santos",
    tipo: "Cível",
    status: "Em análise",
    data: "05/03/2024"
  }
];

const precatorios = [
  {
    numero: "2024.001.123",
    cliente: "Empresa ABC Ltda",
    tipo: "Tributário",
    status: "Em andamento",
    data: "15/03/2024"
  },
  {
    numero: "2024.001.124",
    cliente: "João Silva",
    tipo: "Trabalhista",
    status: "Concluído",
    data: "10/03/2024"
  },
  {
    numero: "2024.001.125",
    cliente: "Maria Santos",
    tipo: "Cível",
    status: "Em análise",
    data: "05/03/2024"
  }
];

const feedbackHistory = [
  {
    date: "15/03/2024",
    author: "Carlos Santos",
    type: "Avaliação Mensal",
    rating: 4.8,
    comment: "Excelente desempenho no caso tributário da Empresa ABC. Demonstrou grande conhecimento técnico e habilidade de negociação."
  },
  {
    date: "15/02/2024",
    author: "Maria Oliveira",
    type: "Feedback de Projeto",
    rating: 4.5,
    comment: "Ótima gestão do tempo e organização nos precatórios. Sugestão de melhorar a documentação das reuniões."
  }
];

const treinamentos = [
  {
    titulo: "Atualização em Direito Tributário",
    instituicao: "Escola Superior de Advocacia",
    status: "Concluído",
    data: "Janeiro 2024",
    cargaHoraria: "40h",
    certificado: true
  },
  {
    titulo: "Gestão de Equipes Jurídicas",
    instituicao: "FGV",
    status: "Em andamento",
    data: "Março 2024",
    cargaHoraria: "60h",
    certificado: false
  }
];

const historicoFinanceiro = [
  {
    mes: "Março 2024",
    salarioBase: "R$ 12.000,00",
    bonus: "R$ 2.500,00",
    beneficios: "R$ 1.200,00",
    total: "R$ 15.700,00"
  },
  {
    mes: "Fevereiro 2024",
    salarioBase: "R$ 12.000,00",
    bonus: "R$ 1.800,00",
    beneficios: "R$ 1.200,00",
    total: "R$ 15.000,00"
  }
];

const pontosHoras = [
  {
    data: "18/03/2024",
    entrada: "09:00",
    saidaAlmoco: "12:00",
    retornoAlmoco: "13:00",
    saida: "18:00",
    horasExtras: "0",
    status: "Regular"
  },
  {
    data: "19/03/2024",
    entrada: "08:30",
    saidaAlmoco: "12:00",
    retornoAlmoco: "13:00",
    saida: "18:30",
    horasExtras: "1",
    status: "Regular"
  }
];

const atividadesCaptacao = [
  {
    data: '19/03/2024',
    tipo: 'Ligação',
    empresa: 'Tech Solutions Ltda',
    resultado: 'Interessado',
    observacao: 'Agendada reunião para próxima semana',
    duracao: '15min'
  },
  {
    data: '19/03/2024',
    tipo: 'Email',
    empresa: 'Indústria ABC',
    resultado: 'Aguardando resposta',
    observacao: 'Enviada proposta comercial',
    duracao: '10min'
  },
  {
    data: '18/03/2024',
    tipo: 'Reunião',
    empresa: 'Comércio XYZ',
    resultado: 'Convertido',
    observacao: 'Cliente assinou contrato',
    duracao: '45min'
  }
];

const metricasCaptacao = [
  {
    titulo: 'Ligações Realizadas',
    valor: 45,
    meta: 50,
    icon: <PhoneCall className="w-4 h-4" />,
    periodo: 'Hoje'
  },
  {
    titulo: 'Emails Enviados',
    valor: 28,
    meta: 30,
    icon: <Send className="w-4 h-4" />,
    periodo: 'Hoje'
  },
  {
    titulo: 'Taxa de Conversão',
    valor: '18%',
    meta: '20%',
    icon: <Percent className="w-4 h-4" />,
    periodo: 'Este mês'
  },
  {
    titulo: 'Tempo em Chamadas',
    valor: '3h 45min',
    meta: '4h',
    icon: <Timer className="w-4 h-4" />,
    periodo: 'Hoje'
  }
];

const historicoConversoes = [
  { mes: 'Jan', tentativas: 180, conversoes: 15, taxaConversao: 8.3 },
  { mes: 'Fev', tentativas: 210, conversoes: 18, taxaConversao: 8.6 },
  { mes: 'Mar', tentativas: 250, conversoes: 25, taxaConversao: 10 },
  { mes: 'Abr', tentativas: 220, conversoes: 20, taxaConversao: 9.1 },
  { mes: 'Mai', tentativas: 280, conversoes: 28, taxaConversao: 10 },
  { mes: 'Jun', tentativas: 300, conversoes: 32, taxaConversao: 10.7 }
];

const tarefasCompartilhadas = [
  {
    id: 1,
    titulo: "Análise de Contrato Comercial",
    descricao: "Revisão dos termos contratuais da Empresa ABC",
    prioridade: "Alta",
    status: "Em andamento",
    dataInicio: "2024-03-18",
    dataFim: "2024-03-25",
    responsaveis: [
      { nome: "Ana Silva", avatar: "/avatars/ana.jpg" },
      { nome: "Carlos Santos", avatar: "/avatars/carlos.jpg" }
    ],
    progresso: 65,
    comentarios: [
      {
        autor: "Ana Silva",
        texto: "Iniciada revisão da seção 3.2",
        data: "2024-03-19"
      }
    ]
  },
  {
    id: 2,
    titulo: "Preparação de Relatório Fiscal",
    descricao: "Elaboração do relatório trimestral",
    prioridade: "Média",
    status: "Pendente",
    dataInicio: "2024-03-20",
    dataFim: "2024-03-28",
    responsaveis: [
      { nome: "Ana Silva", avatar: "/avatars/ana.jpg" },
      { nome: "Maria Costa", avatar: "/avatars/maria.jpg" }
    ],
    progresso: 30,
    comentarios: []
  },
  {
    id: 3,
    titulo: "Reunião com Cliente XYZ",
    descricao: "Apresentação da estratégia processual",
    prioridade: "Alta",
    status: "Agendada",
    dataInicio: "2024-03-22",
    dataFim: "2024-03-22",
    responsaveis: [
      { nome: "Ana Silva", avatar: "/avatars/ana.jpg" },
      { nome: "Carlos Santos", avatar: "/avatars/carlos.jpg" },
      { nome: "Maria Costa", avatar: "/avatars/maria.jpg" }
    ],
    progresso: 0,
    comentarios: []
  }
];

const EmployeeDetails = () => {
  const navigate = useNavigate();
  const { id } = useParams();

  // Aqui você buscaria os dados do funcionário usando o ID
  const employee = {
    name: "Ana Silva",
    role: "Advogada Sênior",
    department: "Cível",
    email: "<EMAIL>",
    phone: "(11) 99999-9999",
    location: "São Paulo, SP",
    joinDate: "01/01/2020",
    status: "Ativo",
    avatar: "/avatars/ana.jpg",
    performance: 95,
    specialization: "Direito Civil e Contratos",
    education: [
      {
        degree: "Mestrado em Direito Civil",
        institution: "USP",
        year: "2019"
      },
      {
        degree: "Graduação em Direito",
        institution: "PUC-SP",
        year: "2017"
      }
    ]
  };

  return (
    <div className="flex flex-1">
      <div className="p-2 md:p-10 rounded-tl-2xl border border-neutral-200 dark:border-neutral-700 bg-gradient-to-br from-neutral-50 to-neutral-100 dark:from-neutral-900 dark:to-black flex flex-col gap-6 flex-1 w-full h-full">
        <div className="flex items-center gap-4">
          <Button 
            variant="ghost" 
            size="icon"
            onClick={() => navigate('/employees')}
            className="bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm hover:bg-neutral-100 dark:hover:bg-neutral-700"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-neutral-800 to-neutral-600 dark:from-neutral-200 dark:to-neutral-400">
            Perfil do Funcionário
          </h1>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <Card className="border-neutral-200 dark:border-neutral-700 hover:shadow-lg transition-shadow bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm">
            <CardContent className="p-6">
              <div className="flex flex-col items-center text-center">
                <Avatar className="h-24 w-24 mb-4">
                  <AvatarImage src={employee.avatar} />
                  <AvatarFallback>{employee.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                </Avatar>
                <h2 className="text-xl font-semibold text-neutral-800 dark:text-neutral-200 mb-1">
                  {employee.name}
                </h2>
                <p className="text-sm text-neutral-500 mb-4">{employee.role}</p>
                <Badge variant="secondary" className="mb-6">
                  {employee.department}
                </Badge>
                <div className="w-full space-y-4">
                  <div className="flex items-center gap-3 text-sm text-neutral-600 dark:text-neutral-400">
                    <Mail className="w-4 h-4" />
                    <span>{employee.email}</span>
                  </div>
                  <div className="flex items-center gap-3 text-sm text-neutral-600 dark:text-neutral-400">
                    <Phone className="w-4 h-4" />
                    <span>{employee.phone}</span>
                  </div>
                  <div className="flex items-center gap-3 text-sm text-neutral-600 dark:text-neutral-400">
                    <MapPin className="w-4 h-4" />
                    <span>{employee.location}</span>
                  </div>
                  <div className="flex items-center gap-3 text-sm text-neutral-600 dark:text-neutral-400">
                    <Calendar className="w-4 h-4" />
                    <span>Admissão: {employee.joinDate}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="lg:col-span-2">
            <Tabs defaultValue="overview" className="w-full">
              <TabsList className="bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm">
                <TabsTrigger value="overview">Visão Geral</TabsTrigger>
                <TabsTrigger value="tasks">Tarefas</TabsTrigger>
                <TabsTrigger value="captacao">Captação</TabsTrigger>
                <TabsTrigger value="processes">Precatórios</TabsTrigger>
                <TabsTrigger value="performance">Performance</TabsTrigger>
                <TabsTrigger value="financial">Financeiro</TabsTrigger>
                <TabsTrigger value="timesheet">Ponto</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="mt-4">
                <div className="grid gap-4">
                  <Card className="border-neutral-200 dark:border-neutral-700 bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm">
                    <CardHeader>
                      <CardTitle className="text-lg">Formação Acadêmica</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {employee.education.map((edu, index) => (
                        <div key={index} className="flex items-start gap-4">
                          <div className="p-2 rounded-lg bg-neutral-100 dark:bg-neutral-700">
                            <GraduationCap className="w-4 h-4" />
                          </div>
                          <div>
                            <h4 className="font-medium text-neutral-800 dark:text-neutral-200">{edu.degree}</h4>
                            <p className="text-sm text-neutral-500">{edu.institution} • {edu.year}</p>
                          </div>
                        </div>
                      ))}
                    </CardContent>
                  </Card>

                  <Card className="border-neutral-200 dark:border-neutral-700 bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm">
                    <CardHeader>
                      <CardTitle className="text-lg">Conquistas</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {achievements.map((achievement, index) => (
                        <div key={index} className="flex items-start gap-4">
                          <div className="p-2 rounded-lg bg-neutral-100 dark:bg-neutral-700">
                            {achievement.icon}
                          </div>
                          <div>
                            <h4 className="font-medium text-neutral-800 dark:text-neutral-200">{achievement.title}</h4>
                            <p className="text-sm text-neutral-600 dark:text-neutral-400">{achievement.description}</p>
                            <p className="text-xs text-neutral-500">{achievement.date}</p>
                          </div>
                        </div>
                      ))}
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="tasks" className="mt-6">
                <div className="grid gap-6">
                  <Card className="border-neutral-200 dark:border-neutral-700 bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm">
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <CardTitle>Tarefas Compartilhadas</CardTitle>
                        <Button variant="outline" size="sm" className="gap-2">
                          <FileText className="w-4 h-4" />
                          Nova Tarefa
                        </Button>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-6">
                        {tarefasCompartilhadas.map((tarefa, index) => (
                          <div key={tarefa.id} className="p-4 rounded-lg border border-neutral-200 dark:border-neutral-700">
                            <div className="flex items-start justify-between mb-4">
                              <div>
                                <h4 className="font-medium text-lg mb-1">{tarefa.titulo}</h4>
                                <p className="text-sm text-neutral-500 mb-2">{tarefa.descricao}</p>
                                <div className="flex items-center gap-2">
                                  <Badge variant={
                                    tarefa.prioridade === "Alta" ? "destructive" : 
                                    tarefa.prioridade === "Média" ? "default" : 
                                    "secondary"
                                  }>
                                    {tarefa.prioridade}
                                  </Badge>
                                  <Badge variant={
                                    tarefa.status === "Em andamento" ? "default" :
                                    tarefa.status === "Pendente" ? "secondary" :
                                    "outline"
                                  }>
                                    {tarefa.status}
                                  </Badge>
                                </div>
                              </div>
                              <div className="flex -space-x-2">
                                {tarefa.responsaveis.map((resp, i) => (
                                  <Avatar key={i} className="border-2 border-white dark:border-neutral-800 h-8 w-8">
                                    <AvatarImage src={resp.avatar} />
                                    <AvatarFallback>{resp.nome.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                                  </Avatar>
                                ))}
                              </div>
                            </div>
                            <div className="space-y-3">
                              <div className="flex justify-between text-sm text-neutral-500">
                                <span>Progresso</span>
                                <span>{tarefa.progresso}%</span>
                              </div>
                              <Progress value={tarefa.progresso} className="h-2" />
                              <div className="flex items-center justify-between text-sm">
                                <div className="flex items-center gap-2">
                                  <Calendar className="w-4 h-4" />
                                  <span>{tarefa.dataInicio} - {tarefa.dataFim}</span>
                                </div>
                                {tarefa.comentarios.length > 0 && (
                                  <div className="flex items-center gap-1 text-neutral-500">
                                    <MessageSquare className="w-4 h-4" />
                                    <span>{tarefa.comentarios.length}</span>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="captacao" className="mt-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                  {metricasCaptacao.map((metrica, index) => (
                    <Card key={index} className="border-neutral-200 dark:border-neutral-700 bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm">
                      <CardContent className="p-6">
                        <div className="flex items-center gap-2 mb-2">
                          {metrica.icon}
                          <p className="text-sm font-medium">{metrica.titulo}</p>
                        </div>
                        <div className="flex items-end justify-between mb-2">
                          <p className="text-2xl font-bold">{metrica.valor}</p>
                          <Badge variant="outline" className="text-xs">
                            Meta: {metrica.meta}
                          </Badge>
                        </div>
                        <p className="text-xs text-neutral-500">{metrica.periodo}</p>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <Card className="border-neutral-200 dark:border-neutral-700 bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm">
                    <CardHeader>
                      <CardTitle>Histórico de Conversões</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="h-[300px]">
                        <ResponsiveContainer width="100%" height="100%">
                          <AreaChart data={historicoConversoes}>
                            <XAxis dataKey="mes" />
                            <YAxis yAxisId="left" />
                            <YAxis yAxisId="right" orientation="right" />
                            <Tooltip />
                            <Legend />
                            <Area 
                              yAxisId="left"
                              type="monotone" 
                              dataKey="tentativas" 
                              name="Tentativas"
                              stroke="#0ea5e9" 
                              fill="#0ea5e9" 
                              fillOpacity={0.1} 
                            />
                            <Area 
                              yAxisId="left"
                              type="monotone" 
                              dataKey="conversoes" 
                              name="Conversões"
                              stroke="#22c55e" 
                              fill="#22c55e" 
                              fillOpacity={0.1} 
                            />
                            <Line 
                              yAxisId="right"
                              type="monotone" 
                              dataKey="taxaConversao" 
                              name="Taxa de Conversão (%)"
                              stroke="#f59e0b" 
                            />
                          </AreaChart>
                        </ResponsiveContainer>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border-neutral-200 dark:border-neutral-700 bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm">
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <CardTitle>Atividades de Hoje</CardTitle>
                        <Button variant="outline" size="sm" className="gap-2">
                          <UserPlus className="w-4 h-4" />
                          Nova Atividade
                        </Button>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-6">
                        {atividadesCaptacao.map((atividade, index) => (
                          <div key={index} className="flex items-start gap-4 p-4 rounded-lg border border-neutral-200 dark:border-neutral-700">
                            <div className="p-2 rounded-full bg-neutral-100 dark:bg-neutral-700">
                              {atividade.tipo === 'Ligação' && <PhoneCall className="w-4 h-4" />}
                              {atividade.tipo === 'Email' && <Send className="w-4 h-4" />}
                              {atividade.tipo === 'Reunião' && <Users className="w-4 h-4" />}
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center justify-between mb-1">
                                <p className="font-medium">{atividade.empresa}</p>
                                <Badge 
                                  variant={
                                    atividade.resultado === 'Convertido' 
                                      ? 'default' 
                                      : atividade.resultado === 'Interessado'
                                      ? 'secondary'
                                      : 'outline'
                                  }
                                >
                                  {atividade.resultado}
                                </Badge>
                              </div>
                              <p className="text-sm text-neutral-500 mb-2">{atividade.observacao}</p>
                              <div className="flex items-center gap-4 text-sm text-neutral-500">
                                <div className="flex items-center gap-1">
                                  <Calendar className="w-4 h-4" />
                                  <span>{atividade.data}</span>
                                </div>
                                <div className="flex items-center gap-1">
                                  <Clock className="w-4 h-4" />
                                  <span>{atividade.duracao}</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="processes" className="mt-4">
                <Card className="border-neutral-200 dark:border-neutral-700 bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="text-lg">Precatórios Atribuídos</CardTitle>
                    <CardDescription>Últimos precatórios sob responsabilidade</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {precatorios.map((precatorio, index) => (
                        <motion.div
                          key={index}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: index * 0.1 }}
                          className="p-4 rounded-lg bg-white/60 dark:bg-neutral-800/60 backdrop-blur-sm border border-neutral-200/50 dark:border-neutral-700/50"
                        >
                          <div className="flex items-center justify-between">
                            <div>
                              <h4 className="font-medium text-neutral-800 dark:text-neutral-200">{precatorio.numero}</h4>
                              <p className="text-sm text-neutral-500">{precatorio.cliente}</p>
                            </div>
                            <div className="flex items-center gap-4">
                              <Badge variant="secondary" className="bg-neutral-100 dark:bg-neutral-800">
                                {precatorio.tipo}
                              </Badge>
                              <Badge variant="outline">
                                {precatorio.status}
                              </Badge>
                              <span className="text-xs text-neutral-500">{precatorio.data}</span>
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="performance" className="mt-4">
                <Card className="border-neutral-200 dark:border-neutral-700 bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="text-lg">Histórico de Performance</CardTitle>
                    <CardDescription>Comparação com a média da equipe</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[300px]">
                      <ChartContainer config={{}}>
                        <AreaChart data={performanceHistory}>
                          <defs>
                            <linearGradient id="colorDesempenho" x1="0" y1="0" x2="0" y2="1">
                              <stop offset="5%" stopColor="#64748b" stopOpacity={0.8}/>
                              <stop offset="95%" stopColor="#64748b" stopOpacity={0}/>
                            </linearGradient>
                            <linearGradient id="colorMedia" x1="0" y1="0" x2="0" y2="1">
                              <stop offset="5%" stopColor="#94a3b8" stopOpacity={0.8}/>
                              <stop offset="95%" stopColor="#94a3b8" stopOpacity={0}/>
                            </linearGradient>
                          </defs>
                          <XAxis dataKey="month" stroke="#888888" />
                          <YAxis stroke="#888888" />
                          <Tooltip />
                          <Legend />
                          <Area 
                            type="monotone" 
                            dataKey="desempenho" 
                            stroke="#64748b" 
                            fillOpacity={1} 
                            fill="url(#colorDesempenho)" 
                            name="Desempenho Individual"
                          />
                          <Area 
                            type="monotone" 
                            dataKey="media" 
                            stroke="#94a3b8" 
                            fillOpacity={1} 
                            fill="url(#colorMedia)" 
                            name="Média da Equipe"
                          />
                        </AreaChart>
                      </ChartContainer>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="financial" className="mt-4">
                <Card className="border-neutral-200 dark:border-neutral-700 bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="text-lg">Histórico Financeiro</CardTitle>
                    <CardDescription>Remuneração e benefícios</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {historicoFinanceiro.map((registro, index) => (
                        <motion.div
                          key={index}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: index * 0.1 }}
                          className="p-4 rounded-lg bg-white/60 dark:bg-neutral-800/60 backdrop-blur-sm border border-neutral-200/50 dark:border-neutral-700/50"
                        >
                          <div className="flex items-center justify-between mb-4">
                            <div className="flex items-center gap-3">
                              <div className="p-2 rounded-lg bg-neutral-100 dark:bg-neutral-700">
                                <Wallet className="w-4 h-4" />
                              </div>
                              <h4 className="font-medium text-neutral-800 dark:text-neutral-200">{registro.mes}</h4>
                            </div>
                            <Badge variant="secondary" className="text-lg font-semibold">
                              {registro.total}
                            </Badge>
                          </div>
                          <div className="grid grid-cols-3 gap-4">
                            <div>
                              <p className="text-sm text-neutral-500">Salário Base</p>
                              <p className="font-medium">{registro.salarioBase}</p>
                            </div>
                            <div>
                              <p className="text-sm text-neutral-500">Bônus</p>
                              <p className="font-medium">{registro.bonus}</p>
                            </div>
                            <div>
                              <p className="text-sm text-neutral-500">Benefícios</p>
                              <p className="font-medium">{registro.beneficios}</p>
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="timesheet" className="mt-4">
                <Card className="border-neutral-200 dark:border-neutral-700 bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="text-lg">Registro de Ponto</CardTitle>
                    <CardDescription>Controle de horários e frequência</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {pontosHoras.map((registro, index) => (
                        <motion.div
                          key={index}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: index * 0.1 }}
                          className="p-4 rounded-lg bg-white/60 dark:bg-neutral-800/60 backdrop-blur-sm border border-neutral-200/50 dark:border-neutral-700/50"
                        >
                          <div className="flex items-center justify-between mb-4">
                            <div className="flex items-center gap-3">
                              <div className="p-2 rounded-lg bg-neutral-100 dark:bg-neutral-700">
                                <CalendarDays className="w-4 h-4" />
                              </div>
                              <h4 className="font-medium text-neutral-800 dark:text-neutral-200">{registro.data}</h4>
                            </div>
                            <Badge 
                              variant={registro.status === "Regular" ? "default" : "destructive"}
                              className="capitalize"
                            >
                              {registro.status}
                            </Badge>
                          </div>
                          <div className="grid grid-cols-3 md:grid-cols-6 gap-4">
                            <div>
                              <p className="text-sm text-neutral-500">Entrada</p>
                              <p className="font-medium">{registro.entrada}</p>
                            </div>
                            <div>
                              <p className="text-sm text-neutral-500">Saída Almoço</p>
                              <p className="font-medium">{registro.saidaAlmoco}</p>
                            </div>
                            <div>
                              <p className="text-sm text-neutral-500">Retorno</p>
                              <p className="font-medium">{registro.retornoAlmoco}</p>
                            </div>
                            <div>
                              <p className="text-sm text-neutral-500">Saída</p>
                              <p className="font-medium">{registro.saida}</p>
                            </div>
                            <div>
                              <p className="text-sm text-neutral-500">Horas Extras</p>
                              <p className="font-medium">{registro.horasExtras}h</p>
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmployeeDetails; 