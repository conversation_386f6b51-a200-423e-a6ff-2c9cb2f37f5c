import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { TopNav } from '@/components/top-nav';
import { Settings, Tag, LayoutGrid, Eye, RotateCcw } from 'lucide-react';
import { TagManager } from '@/components/tags/TagManager';
import { KanbanColumnManager } from '@/components/kanban/KanbanColumnManager';
import { CustomViewManager } from '@/components/kanban/CustomViewManager';
import { DeletedItemsManager } from '@/components/recovery/DeletedItemsManager';
import { useToast } from '@/components/ui/use-toast';

export default function KanbanSettings() {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('tags');

  const handleItemRestored = () => {
    toast({
      title: 'Item restaurado',
      description: 'O item foi restaurado com sucesso. Atualize a página para ver as alterações.',
    });
  };

  const handleTagsChange = () => {
    toast({
      title: 'Tags atualizadas',
      description: 'As alterações nas tags foram salvas com sucesso.',
    });
  };

  const handleColumnsChange = () => {
    toast({
      title: 'Colunas atualizadas',
      description: 'As alterações nas colunas foram salvas com sucesso.',
    });
  };

  const handleViewsChange = () => {
    toast({
      title: 'Visualizações atualizadas',
      description: 'As alterações nas visualizações foram salvas com sucesso.',
    });
  };

  return (
    <div className="flex flex-col h-screen w-screen overflow-hidden">
      <TopNav
        title="Configurações do Kanban"
        icon={<Settings className="h-6 w-6 text-primary" />}
      />

      <div className="p-6 pt-[65px] overflow-auto">
        <Card>
          <CardHeader>
            <CardTitle>Configurações do Kanban</CardTitle>
            <CardDescription>
              Gerencie tags, colunas, visualizações e itens excluídos do quadro Kanban
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue={activeTab} onValueChange={setActiveTab}>
              <TabsList className="mb-6">
                <TabsTrigger value="tags" className="flex items-center gap-2">
                  <Tag className="w-4 h-4" />
                  <span>Tags</span>
                </TabsTrigger>
                <TabsTrigger value="columns" className="flex items-center gap-2">
                  <LayoutGrid className="w-4 h-4" />
                  <span>Colunas</span>
                </TabsTrigger>
                <TabsTrigger value="views" className="flex items-center gap-2">
                  <Eye className="w-4 h-4" />
                  <span>Visualizações</span>
                </TabsTrigger>
                <TabsTrigger value="deleted" className="flex items-center gap-2">
                  <RotateCcw className="w-4 h-4" />
                  <span>Itens Excluídos</span>
                </TabsTrigger>
              </TabsList>

              <TabsContent value="tags">
                <TagManager onTagsChange={handleTagsChange} />
              </TabsContent>

              <TabsContent value="columns">
                <KanbanColumnManager onColumnsChange={handleColumnsChange} />
              </TabsContent>

              <TabsContent value="views">
                <CustomViewManager onViewsChange={handleViewsChange} />
              </TabsContent>

              <TabsContent value="deleted">
                <DeletedItemsManager onItemRestored={handleItemRestored} />
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
