import { useState, useEffect, useRef } from "react";
import {
  KanbanContainer,
  Precatorio,
  KanbanColuna as KanbanColunaType
} from "@/components/Precatorios";
import { GerenciadorColunas } from "@/components/Precatorios/KanbanConfig/GerenciadorColunas";
import {
  salvarPrecatorio,
  excluirPrecatorio,
  atualizarStatusPrecatorio
} from "@/services/precatoriosServiceSimples";
import {
  buscarPrecatoriosParaKanban,
  buscarColunasKanban
} from "@/services/kanbanPrecatoriosService";
import { clearCache } from '@/services/cacheService';
import {
  Loader2, Settings, X, KanbanSquare, RefreshCw,
  ShieldAlert, Wifi, AlertCircle, Plus, Filter,
  UserCheck, Lock, Info
} from "lucide-react";
import { TopNav } from "@/components/top-nav";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { supabase } from "@/lib/supabase";
import { useAuth } from "@/contexts/AuthContext";
import { useKanbanPermissions } from "@/hooks/useKanbanPermissions";
import { AutoReconnect } from "@/components/AutoReconnect";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from "@/components/ui/dialog";
import { cn } from "@/lib/utils";
import {
  NavigationMenu,
  NavigationMenuItem,
  NavigationMenuList,
  NavigationMenuTrigger,
} from "@/components/ui/navigation-menu";
import { KanbanPermissionGuard } from "@/components/permissions/KanbanPermissionGuard";
import { PermissionButton } from "@/components/ui/permission-button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

function PrecatoriosKanban() {
  const [precatorios, setPrecatorios] = useState<Precatorio[]>([]);
  const [colunas, setColunas] = useState<KanbanColunaType[]>([]);
  const [loading, setLoading] = useState(true);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [tipoVisualizacao, setTipoVisualizacao] = useState<'PRECATORIO' | 'RPV'>('PRECATORIO');
  const [gerenciadorAberto, setGerenciadorAberto] = useState(false);
  const [permissionsDialogOpen, setPermissionsDialogOpen] = useState(false);
  const { user, isAdmin } = useAuth();
  const {
    filterVisibleColumns,
    filterVisiblePrecatorios,
    canAccessKanban,
    canCreatePrecatorio,
    loading: permissionsLoading,
    error: permissionsError
  } = useKanbanPermissions();

  // Referências para controlar o estado de carregamento
  const isLoadingRef = useRef(false);
  const loadingTimeoutRef = useRef<number | null>(null);
  const loadAttemptCountRef = useRef(0);
  const [forceReloadNeeded, setForceReloadNeeded] = useState(false);

  // Referência para armazenar o último estado válido dos dados
  const lastValidDataRef = useRef<{
    precatorios: Precatorio[];
    colunas: KanbanColunaType[];
    timestamp: number;
  } | null>(null);

  // Estado para controlar se a página está visível
  const [isPageVisible, setIsPageVisible] = useState(true);

  // Função para carregar dados com retry e melhor sincronização
  const carregarDados = async (retry = 0, forceReload = false) => {
      // Evitar múltiplos carregamentos simultâneos
      if (isLoadingRef.current && !forceReload) {
        console.log('Carregamento já em andamento, ignorando solicitação');
        return;
      }

      try {
        // Verificar se a sessão está válida antes de carregar os dados
        try {
          console.log("PrecatoriosKanban: Verificando sessão antes de carregar dados...");
          const { data: sessionData } = await supabase.auth.getSession();

          if (!sessionData.session) {
            console.warn("PrecatoriosKanban: Sessão não encontrada, tentando continuar mesmo assim...");
            // Continuar mesmo sem sessão, pois o usuário pode ser admin
            // Não redirecionar para login para evitar loops
          } else {
            console.log("PrecatoriosKanban: Sessão válida encontrada, continuando...");
          }
        } catch (sessionError) {
          console.warn("PrecatoriosKanban: Erro ao verificar sessão:", sessionError);
          // Continuar mesmo com erro, pois o Supabase tentará usar o token armazenado
        }

        // Incrementar contador de tentativas
        loadAttemptCountRef.current += 1;

        // Configurar timeout para evitar carregamento infinito
        if (loadingTimeoutRef.current) {
          clearTimeout(loadingTimeoutRef.current);
        }

        loadingTimeoutRef.current = window.setTimeout(() => {
          // Se ainda estiver carregando após 15 segundos, mostrar erro e interromper o carregamento
          if (isLoadingRef.current) {
            console.error('Timeout ao carregar dados - carregamento demorou mais de 15 segundos');
            setLoading(false);
            isLoadingRef.current = false;

            // Verificar se temos dados em cache para mostrar
            if (lastValidDataRef.current && lastValidDataRef.current.precatorios.length > 0) {
              console.log('Usando dados em cache após timeout');
              setPrecatorios(lastValidDataRef.current.precatorios);
              setColunas(lastValidDataRef.current.colunas);

              // Mostrar toast informando que estamos usando dados em cache
              toast.info('Usando dados em cache', {
                description: 'O carregamento demorou muito tempo. Mostrando a última versão disponível.'
              });
            } else {
              // Se não temos cache, mostrar erro
              setErrorMessage('O carregamento demorou muito tempo. Tente novamente ou use o botão de reconexão.');
              setForceReloadNeeded(true);
            }
          }
        }, 15000); // Reduzido para 15 segundos para evitar espera longa

        isLoadingRef.current = true;
        setLoading(true);
        setErrorMessage(null);
        setForceReloadNeeded(false);

        const tipoLabel = tipoVisualizacao === 'PRECATORIO' ? 'precatórios' : 'RPVs';

        // Limpar cache apenas se forçar recarregamento
        if (forceReload) {
          clearCache(`precatorios-${tipoVisualizacao}`);
        }

        // Carregar precatórios e colunas em paralelo com melhor tratamento de erros
        let precatoriosData: Precatorio[] = [];
        let colunasData: KanbanColunaType[] = [];

        try {
          // Verificar se existem colunas personalizadas antes de carregar os dados
          try {
            const { data: colunasExistentes } = await supabase
              .from('kanban_colunas_personalizadas')
              .select('id')
              .eq('is_deleted', false);

            // Se não existem colunas, criar colunas padrão
            if (!colunasExistentes || colunasExistentes.length === 0) {
              console.log('Verificando se é necessário criar colunas padrão...');
              try {
                const { criarColunasPersonalizadasPadrao } = await import('@/services/kanbanService');
                await criarColunasPersonalizadasPadrao();
                console.log('Colunas padrão verificadas/criadas.');
              } catch (createError) {
                console.warn('Erro ao criar colunas padrão:', createError);
                // Continuar mesmo com erro
              }
            }
          } catch (colunasError) {
            console.warn('Erro ao verificar colunas existentes:', colunasError);
            // Continuar mesmo com erro
          }

          // Carregar dados usando os serviços específicos para o kanban
          // Usar Promise.allSettled para garantir que mesmo se uma promessa falhar, a outra continue
          const [precatoriosPromise, colunasPromise] = await Promise.allSettled([
            buscarPrecatoriosParaKanban(),
            buscarColunasKanban()
          ]);

          // Extrair resultados das promessas
          const precatoriosResult = precatoriosPromise.status === 'fulfilled' ? precatoriosPromise.value : [];
          const colunasResult = colunasPromise.status === 'fulfilled' ? colunasPromise.value : [];

          // Filtrar precatórios pelo tipo selecionado
          precatoriosData = precatoriosResult.filter(p => {
            // Verificar se o tipo corresponde, considerando possíveis diferenças de formato
            const tipoNormalizado = (p.tipo || '').toUpperCase();
            return tipoNormalizado === tipoVisualizacao;
          });

          // Verificar se há precatórios sem tipo definido
          const precatoriosSemTipo = precatoriosResult.filter(p => !p.tipo);

          // Se não houver precatórios do tipo selecionado, incluir os sem tipo
          if (precatoriosData.length === 0 && tipoVisualizacao === 'PRECATORIO' && precatoriosSemTipo.length > 0) {
            precatoriosData = [...precatoriosData, ...precatoriosSemTipo];
          }

          colunasData = colunasResult;
        } catch (loadError: any) {
          console.error(`Erro ao carregar dados do Supabase:`, loadError);

          // Verificar se é um erro de conexão ou autenticação
          const isConnectionError = loadError.message?.toLowerCase().includes('network') ||
                                   loadError.message?.toLowerCase().includes('fetch') ||
                                   loadError.message?.toLowerCase().includes('connection') ||
                                   loadError.message?.toLowerCase().includes('internet') ||
                                   loadError.message?.toLowerCase().includes('offline') ||
                                   loadError.message?.toLowerCase().includes('timeout') ||
                                   loadError.message?.toLowerCase().includes('cors') ||
                                   loadError.code === 'NETWORK_ERROR' ||
                                   loadError.code === 'CONNECTION_ERROR';

          const isAuthError = loadError.message?.toLowerCase().includes('auth') ||
                             loadError.message?.toLowerCase().includes('session') ||
                             loadError.message?.toLowerCase().includes('token') ||
                             loadError.message?.toLowerCase().includes('jwt') ||
                             loadError.message?.toLowerCase().includes('login') ||
                             loadError.message?.toLowerCase().includes('autenticação') ||
                             loadError.message?.toLowerCase().includes('unauthorized') ||
                             loadError.message?.toLowerCase().includes('permission') ||
                             loadError.code === 'UNAUTHENTICATED' ||
                             loadError.code === 'JWT_INVALID' ||
                             loadError.code === 'SESSION_EXPIRED';

          // Se for um erro de conexão, tentar mais vezes
          const maxRetries = isConnectionError ? 3 : 2;

          // Se falhar e ainda não atingimos o máximo de tentativas
          if (retry < maxRetries) {
            const waitTime = isConnectionError ? 2000 : 1000; // Esperar mais tempo para erros de conexão
            console.log(`Tentando novamente... (tentativa ${retry + 1}/${maxRetries}) em ${waitTime/1000}s`);
            setTimeout(() => carregarDados(retry + 1), waitTime);
            return;
          }

          // Se for um erro de autenticação, mostrar mensagem específica
          if (isAuthError) {
            console.warn('Detectado problema de autenticação:', loadError);
            throw new Error(`Problema de autenticação: ${loadError.message || 'Sessão inválida ou expirada'}. Tente reconectar usando o botão abaixo ou faça login novamente.`);
          }

          // Se for um erro de conexão, mostrar mensagem específica
          if (isConnectionError) {
            console.warn('Detectado problema de conexão:', loadError);
            throw new Error(`Problema de conexão: ${loadError.message || 'Não foi possível conectar ao servidor'}. Verifique sua conexão com a internet e tente novamente.`);
          }

          throw loadError; // Re-throw se esgotou as tentativas
        }

        // Verificar se os dados têm o formato esperado
        if (!Array.isArray(precatoriosData)) {
          console.error(`Erro: dados de ${tipoLabel} não é um array`, precatoriosData);
          throw new Error(`Os dados de ${tipoLabel} não estão no formato esperado`);
        }

        if (!Array.isArray(colunasData)) {
          console.error(`Erro: colunasData para ${tipoLabel} não é um array`, colunasData);
          throw new Error(`Os dados de colunas para ${tipoLabel} não estão no formato esperado`);
        }

        // Aplicar filtros de permissão
        console.log(`Filtrando ${precatoriosData.length} precatórios e ${colunasData.length} colunas com base nas permissões...`);

        // Filtrar colunas visíveis com base nas permissões do usuário
        const colunasVisiveis = filterVisibleColumns(colunasData);

        // Filtrar precatórios visíveis com base nas permissões do usuário
        const precatoriosVisiveis = filterVisiblePrecatorios(precatoriosData);

        console.log(`Após filtro de permissões: ${precatoriosVisiveis.length} precatórios e ${colunasVisiveis.length} colunas visíveis`);

        // Usar os dados filtrados
        precatoriosData = precatoriosVisiveis;
        colunasData = colunasVisiveis;

        // Verificar se temos colunas configuradas
        if (colunasData.length === 0) {
          console.log('Nenhuma coluna encontrada, tentando criar colunas padrão...');

          try {
            // Tentar criar colunas padrão
            const { criarColunasPersonalizadasPadrao } = await import('@/services/kanbanService');
            await criarColunasPersonalizadasPadrao();

            // Tentar buscar as colunas novamente
            const { data: colunasAtualizadas } = await supabase
              .from('kanban_colunas_personalizadas')
              .select('*')
              .eq('is_deleted', false)
              .order('ordem');

            if (colunasAtualizadas && colunasAtualizadas.length > 0) {
              console.log(`Criadas ${colunasAtualizadas.length} colunas padrão com sucesso!`);
              colunasData = colunasAtualizadas;
            } else {
              // Se ainda não temos colunas, mostrar mensagem
              setErrorMessage('Este quadro Kanban ainda não possui colunas configuradas.');
              setLoading(false);
              isLoadingRef.current = false;
              return; // Retornar sem lançar erro para mostrar a mensagem de configuração
            }
          } catch (createError) {
            console.error('Erro ao criar colunas padrão:', createError);
            setErrorMessage('Este quadro Kanban ainda não possui colunas configuradas.');
            setLoading(false);
            isLoadingRef.current = false;
            return; // Retornar sem lançar erro para mostrar a mensagem de configuração
          }
        }

        // Garantir que os precatórios tenham status_id válidos
        const precatoriosCorrigidos = precatoriosData.map(p => {
          // Se não tiver status_id, tentar encontrar uma coluna correspondente pelo status
          if (!p.status_id && p.status) {
            const colunaCorrespondente = colunasData.find(c => c.status_id === p.status);
            if (colunaCorrespondente && colunaCorrespondente.status_uuid) {
              return { ...p, status_id: colunaCorrespondente.status_uuid };
            }
          }
          return p;
        });

        // Adicionar contagem para cada coluna
        const colunasAtualizadas = colunasData.map(coluna => {
          // Contar precatórios para esta coluna
          // Verificar tanto status_uuid quanto status_ref_id para garantir compatibilidade
          const precatoriosDaColuna = precatoriosCorrigidos.filter(p =>
            p.status_id === coluna.status_uuid ||
            p.status_id === coluna.status_ref_id ||
            p.status === coluna.status_id
          );

          const count = precatoriosDaColuna.length;

          console.log(`Coluna ${coluna.nome}: encontrados ${count} precatórios`);
          if (count > 0) {
            console.log(`Exemplo de precatório na coluna ${coluna.nome}:`,
              precatoriosDaColuna[0].id,
              precatoriosDaColuna[0].status_id,
              precatoriosDaColuna[0].status
            );
          }

          return { ...coluna, count };
        });

        console.log('Colunas atualizadas com contagem:', colunasAtualizadas.map(c => ({
          nome: c.nome,
          count: c.count,
          status_uuid: c.status_uuid,
          status_id: c.status_id
        })));

        // Se não temos dados, mas temos cache, usar o cache
        if (precatoriosCorrigidos.length === 0 && lastValidDataRef.current?.precatorios.length > 0) {
          console.log('Usando dados do cache local em vez de dados vazios');
          // Não atualizar o estado com dados vazios
        } else {
          // Atualizar estado com dados novos
          setPrecatorios(precatoriosCorrigidos);
          setColunas(colunasAtualizadas);
        }

        // Mostrar toast apenas se for um recarregamento forçado ou se não houver precatórios
        if (forceReload || precatoriosData.length === 0) {
          toast.success(`${precatoriosData.length} ${tipoLabel} carregados em ${colunasAtualizadas.length} colunas`);
        }
      } catch (error: any) {
        console.error(`Erro ao carregar dados para ${tipoVisualizacao}:`, error);
        const tipoLabel = tipoVisualizacao === 'PRECATORIO' ? 'precatórios' : 'RPVs';

        // Se temos dados em cache, usá-los em vez de mostrar erro
        if (lastValidDataRef.current && lastValidDataRef.current.precatorios.length > 0) {
          console.log('Erro ao carregar dados, usando cache local...');

          // Usar dados do cache
          setPrecatorios(lastValidDataRef.current.precatorios);
          setColunas(lastValidDataRef.current.colunas);

          // Mostrar toast informando que estamos usando dados em cache
          toast.info('Usando dados em cache', {
            description: 'Não foi possível atualizar os dados. Mostrando a última versão disponível.'
          });

          // Não mostrar mensagem de erro, já que estamos mostrando dados do cache
          setErrorMessage(null);
        }
        // Se não temos cache, mostrar erro
        else {
          setErrorMessage(`Ocorreu um erro ao carregar os ${tipoLabel}. Por favor, tente novamente.`);
          toast.error(`Erro ao carregar ${tipoLabel}: ${error?.message || 'Erro desconhecido'}`);
        }
      } finally {
        // Limpar o timeout para evitar problemas
        if (loadingTimeoutRef.current) {
          clearTimeout(loadingTimeoutRef.current);
          loadingTimeoutRef.current = null;
        }

        setLoading(false);
        isLoadingRef.current = false;

        // Se houve muitas tentativas consecutivas com falha, indicar que precisa de reload forçado
        if (loadAttemptCountRef.current > 5) {
          console.warn(`Muitas tentativas de carregamento (${loadAttemptCountRef.current}), pode ser necessário um reload forçado`);
          setForceReloadNeeded(true);
        }
      }
    };

  // Carregar dados iniciais
  useEffect(() => {
    // Usar um ref para controlar se é a primeira montagem
    const isFirstMount = useRef(true);
    // Referência para controlar se o componente está montado
    const isMounted = { current: true };

    // Definir um pequeno timeout para evitar problemas de concorrência
    const loadTimeout = setTimeout(() => {
      if (isMounted.current) {
        if (isFirstMount.current) {
          // Na primeira montagem, carregar dados normalmente
          carregarDados(0, false);
          isFirstMount.current = false;
        } else {
          // Nas mudanças subsequentes de tipo, carregar dados sem forçar recarregamento
          carregarDados(0, false);
        }
      }
    }, 100);

    // Função de limpeza quando o componente for desmontado
    return () => {
      isMounted.current = false;
      clearTimeout(loadTimeout);
    };
  }, [tipoVisualizacao]);

  // Detector de visibilidade da página
  useEffect(() => {
    // Referência para controlar se o componente está montado
    const isMounted = { current: true };

    // Função para lidar com mudanças de visibilidade
    const handleVisibilityChange = async () => {
      // Verificar se o componente ainda está montado
      if (!isMounted.current) return;

      const isVisible = document.visibilityState === 'visible';
      setIsPageVisible(isVisible);

      if (isVisible) {
        console.log('Página voltou a ficar visível, verificando dados...');

        // Adicionar um pequeno atraso para evitar conflitos com o GoTrueClient
        await new Promise(resolve => setTimeout(resolve, 1500));

        // Verificar novamente se o componente ainda está montado e a página ainda está visível
        if (!isMounted.current || document.visibilityState !== 'visible') return;

        // Verificar se há um carregamento em andamento
        if (isLoadingRef.current) {
          console.log('Carregamento já em andamento, ignorando solicitação de recarregamento');
          return;
        }

        // Se temos dados em cache, verificar se precisam ser atualizados
        if (lastValidDataRef.current) {
          const timeSinceLastData = Date.now() - lastValidDataRef.current.timestamp;

          // Se os dados atuais estão vazios mas temos dados em cache, restaurar do cache
          if (precatorios.length === 0 && lastValidDataRef.current.precatorios.length > 0) {
            console.log('Restaurando dados do cache local...');

            // Verificar novamente se o componente ainda está montado
            if (!isMounted.current) return;

            setPrecatorios(lastValidDataRef.current.precatorios);
            setColunas(lastValidDataRef.current.colunas);
          }

          // Se passou mais de 30 segundos desde o último carregamento válido, recarregar
          if (timeSinceLastData > 30000) {
            console.log('Dados podem estar desatualizados, recarregando silenciosamente...');

            // Verificar se não há carregamento em andamento
            if (!isLoadingRef.current) {
              // Carregar dados sem forçar recarregamento
              carregarDados(0, false);
            }
          }
        } else {
          // Se não temos dados em cache, tentar carregar
          if (precatorios.length === 0) {
            console.log('Sem dados em cache, carregando dados...');

            // Verificar se não há carregamento em andamento
            if (!isLoadingRef.current) {
              carregarDados(0, true);
            }
          }
        }
      }
    };

    // Registrar o listener de visibilidade
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Limpar o listener quando o componente for desmontado
    return () => {
      isMounted.current = false;
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [precatorios.length]);

  // Salvar dados válidos em cache
  useEffect(() => {
    // Se temos dados válidos, salvar em cache
    if (precatorios.length > 0 && colunas.length > 0) {
      lastValidDataRef.current = {
        precatorios: [...precatorios],
        colunas: [...colunas],
        timestamp: Date.now()
      };
      console.log(`Dados válidos salvos em cache: ${precatorios.length} precatórios, ${colunas.length} colunas`);
    }
  }, [precatorios, colunas]);

  // Configurar verificação periódica para manter os dados atualizados
  useEffect(() => {
    // Verificar a cada 2 minutos se os dados precisam ser atualizados
    const intervalId = setInterval(() => {
      // Verificar se não há carregamento em andamento e a página está visível
      if (!isLoadingRef.current && precatorios.length > 0 && isPageVisible) {
        console.log('Verificação periódica: atualizando dados silenciosamente');
        // Carregar dados sem forçar recarregamento e sem mostrar toasts
        carregarDados(0, false);
      }
    }, 120000); // 2 minutos

    // Limpar o intervalo quando o componente for desmontado
    return () => clearInterval(intervalId);
  }, [precatorios.length, isPageVisible]);

  // Salvar precatório (criar ou atualizar)
  const handleSavePrecatorio = async (precatorio: Precatorio) => {
    try {
      setLoading(true);
      const precatorioSalvo = await salvarPrecatorio(precatorio);

      // Atualizar localmente o precatório salvo
      setPrecatorios(prevPrecatorios => {
        const index = prevPrecatorios.findIndex(p => p.id === precatorioSalvo.id);
        if (index >= 0) {
          // Atualizar precatório existente
          const updatedPrecatorios = [...prevPrecatorios];
          updatedPrecatorios[index] = precatorioSalvo;
          return updatedPrecatorios;
        } else {
          // Adicionar novo precatório
          return [...prevPrecatorios, precatorioSalvo];
        }
      });

      // Recarregar dados apenas se for um novo precatório
      if (precatorio.id === 'novo' || !precatorio.id) {
        await carregarDados(0, true);
      }

      toast.success(`${tipoVisualizacao === 'PRECATORIO' ? 'Precatório' : 'RPV'} ${precatorio.numero} salvo com sucesso!`);
    } catch (error) {
      console.error(`Erro ao salvar ${tipoVisualizacao === 'PRECATORIO' ? 'precatório' : 'RPV'}:`, error);
      toast.error(`Erro ao salvar ${tipoVisualizacao === 'PRECATORIO' ? 'precatório' : 'RPV'}`);
    } finally {
      setLoading(false);
    }
  };

  // Excluir precatório
  const handleDeletePrecatorio = async (id: string) => {
    try {
      setLoading(true);
      await excluirPrecatorio(id);

      // Remover precatório da lista local
      setPrecatorios(precatorios.filter(p => p.id !== id));

      // Recalcular contagens para as colunas
      const colunasAtualizadas = colunas.map(coluna => {
        // Contar precatórios para esta coluna após a remoção
        const count = precatorios
          .filter(p => p.id !== id) // Excluir o precatório removido
          .filter(p => p.status_id === coluna.status_uuid)
          .length;
        return { ...coluna, count };
      });

      setColunas(colunasAtualizadas);

      toast.success(`${tipoVisualizacao === 'PRECATORIO' ? 'Precatório' : 'RPV'} excluído com sucesso!`);
    } catch (error) {
      console.error(`Erro ao excluir ${tipoVisualizacao === 'PRECATORIO' ? 'precatório' : 'RPV'}:`, error);
      toast.error(`Erro ao excluir ${tipoVisualizacao === 'PRECATORIO' ? 'precatório' : 'RPV'}`);
    } finally {
      setLoading(false);
    }
  };

  // Mover precatório entre colunas (atualizar status) - versão otimizada
  const handleMovePrecatorio = async (precatorioId: string, novoStatus: string) => {
    const tipoLabel = tipoVisualizacao === 'PRECATORIO' ? 'precatório' : 'RPV';
    let precatorioOriginal: Precatorio | undefined;
    let statusIdOriginal: string | undefined;

    try {
      // 1. Encontrar o precatório e status original ANTES da mudança
      precatorioOriginal = precatorios.find(p => p.id === precatorioId);
      if (!precatorioOriginal) {
        throw new Error(`${tipoLabel} não encontrado para mover.`);
      }

      // Guardar o status_id original para poder reverter se necessário
      statusIdOriginal = precatorioOriginal.status_id;

      // 2. Encontrar a coluna pelo ID para obter o status_uuid
      const coluna = colunas.find(c => c.id === novoStatus);
      if (!coluna) {
        throw new Error(`Coluna com ID ${novoStatus} não encontrada`);
      }

      console.log(`Movendo precatório ${precatorioId} para coluna:`, {
        coluna_id: coluna.id,
        coluna_nome: coluna.nome || coluna.name,
        coluna_status_id: coluna.status_id,
        coluna_status_uuid: coluna.status_uuid,
        coluna_status_ref_id: coluna.status_ref_id
      });

      // Determinar o novo status_id a ser usado
      // Preferência: status_uuid > status_ref_id > id
      let novoStatusId = coluna.status_uuid || coluna.status_ref_id || coluna.id;
      const novoStatusCodigo = coluna.status_id;

      console.log(`Novo status_id: ${novoStatusId}, Novo código de status: ${novoStatusCodigo}`);

      // 3. Atualizar localmente primeiro para UI responsiva imediata
      setPrecatorios(prevPrecatorios => prevPrecatorios.map(p =>
        p.id === precatorioId ? {
          ...p,
          status_id: novoStatusId, // Atualizar o status_id com o UUID do status
          status: novoStatusCodigo // Atualizar o código do status
        } : p
      ));

      // 4. Atualizar contagem das colunas localmente (otimista)
      setColunas(prevColunas => prevColunas.map(col => {
        if (col.id === novoStatus) {
          return { ...col, count: (col.count || 0) + 1 };
        }
        if (col.id === statusIdOriginal) {
          return { ...col, count: Math.max(0, (col.count || 0) - 1) };
        }
        return col;
      }));

      // 5. Atualizar no banco de dados usando o serviço especializado
      // Importar o serviço de forma dinâmica para evitar dependências circulares
      const { atualizarStatusPrecatorio } = await import('@/services/kanbanPrecatoriosService');

      // Iniciar a atualização no banco de dados
      const updatePromise = atualizarStatusPrecatorio(precatorioId, novoStatusId);

      // Mostrar toast de sucesso com animação suave
      toast.success(`${tipoLabel} movido para ${coluna.nome || coluna.name}`, {
        duration: 2000,
        position: 'bottom-right',
        className: 'kanban-toast'
      });

      // 6. Aguardar a atualização no banco de dados
      const resultado = await updatePromise;

      // Se a atualização falhou, lançar erro para entrar no bloco catch
      if (!resultado) {
        throw new Error(`Falha ao atualizar status do ${tipoLabel} no banco de dados`);
      }

      // Atualizar o cache local para manter a consistência
      if (lastValidDataRef.current) {
        lastValidDataRef.current.precatorios = lastValidDataRef.current.precatorios.map(p =>
          p.id === precatorioId ? {
            ...p,
            status_id: novoStatusId,
            status: novoStatusCodigo
          } : p
        );
      }

    } catch (error) {
      console.error(`Erro ao mover ${tipoLabel}:`, error);

      // Mostrar toast de erro com detalhes
      toast.error(`Erro ao atualizar status do ${tipoLabel}`, {
        description: 'Revertendo para o status anterior...',
        duration: 3000
      });

      // Reverter mudanças locais em caso de erro
      if (precatorioOriginal) {
        setPrecatorios(prevPrecatorios => prevPrecatorios.map(p =>
          p.id === precatorioId ? precatorioOriginal : p
        ));

        // Reverter contagem das colunas
        setColunas(prevColunas => prevColunas.map(col => {
          if (col.id === novoStatus) {
            return { ...col, count: Math.max(0, (col.count || 0) - 1) };
          }
          if (col.id === statusIdOriginal) {
            return { ...col, count: (col.count || 0) + 1 };
          }
          return col;
        }));
      } else {
        // Se não achou o original, recarrega tudo como fallback
        carregarDados();
      }
    }
  };

  // Altura estimada da barra de navegação superior (ajuste conforme necessário)
  const topNavHeight = "65px";

  const renderLoading = () => (
    <div className="flex h-[calc(100vh-var(--top-nav-height))] w-full items-center justify-center flex-col" style={{['--top-nav-height']: topNavHeight} as React.CSSProperties}>
      <div className="relative">
        <div className="absolute -inset-1 rounded-full bg-gradient-to-r from-primary to-primary/50 opacity-75 blur-sm"></div>
        <Loader2 className="h-12 w-12 animate-spin text-primary relative" />
      </div>
      <span className="mt-6 text-lg font-medium text-muted-foreground">
        Carregando {tipoVisualizacao === 'PRECATORIO' ? 'precatórios' : 'RPVs'}...
      </span>
    </div>
  );

  const renderError = () => {
    // Verificar se o erro está relacionado à configuração do kanban
    const isConfigError = errorMessage?.toLowerCase().includes('configur') ||
                         errorMessage?.toLowerCase().includes('coluna') ||
                         colunas.length === 0;

    // Verificar se o erro está relacionado à autenticação
    const isAuthError = errorMessage?.toLowerCase().includes('autenticação') ||
                       errorMessage?.toLowerCase().includes('sessão') ||
                       errorMessage?.toLowerCase().includes('login') ||
                       errorMessage?.toLowerCase().includes('token') ||
                       errorMessage?.toLowerCase().includes('jwt');

    // Verificar se o erro está relacionado à conexão
    const isConnectionError = errorMessage?.toLowerCase().includes('conexão') ||
                             errorMessage?.toLowerCase().includes('network') ||
                             errorMessage?.toLowerCase().includes('internet') ||
                             errorMessage?.toLowerCase().includes('offline');

    return (
      <div className="flex h-[calc(100vh-var(--top-nav-height))] w-full flex-col items-center justify-center p-4" style={{['--top-nav-height']: topNavHeight} as React.CSSProperties}>
        <div className="w-full max-w-md p-6 rounded-lg border border-destructive/20 bg-destructive/5 shadow-sm">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 rounded-full bg-destructive/10">
              {isConfigError ? (
                <Settings className="h-6 w-6 text-amber-600" />
              ) : isAuthError ? (
                <ShieldAlert className="h-6 w-6 text-blue-600" />
              ) : isConnectionError ? (
                <Wifi className="h-6 w-6 text-orange-600" />
              ) : (
                <X className="h-6 w-6 text-destructive" />
              )}
            </div>
            <h3 className="text-lg font-semibold text-destructive">
              {isConfigError ? "Configuração Necessária" :
               isAuthError ? "Problema de Autenticação" :
               isConnectionError ? "Problema de Conexão" :
               "Erro ao Carregar Dados"}
            </h3>
          </div>

          {isConfigError ? (
            <div>
              <p className="text-sm text-foreground mb-3">
                Estamos configurando o quadro Kanban para você.
              </p>
              <p className="text-sm text-muted-foreground mb-5">
                Clique no botão abaixo para criar colunas padrão automaticamente ou peça a um administrador para configurar colunas personalizadas.
              </p>

              <Button
                onClick={async () => {
                  try {
                    setLoading(true);
                    const { criarColunasPersonalizadasPadrao } = await import('@/services/kanbanService');
                    await criarColunasPersonalizadasPadrao();
                    toast.success('Colunas padrão criadas com sucesso!');
                    // Recarregar dados
                    carregarDados(0, true);
                  } catch (error) {
                    console.error('Erro ao criar colunas padrão:', error);
                    toast.error('Erro ao criar colunas padrão. Tente novamente.');
                  } finally {
                    setLoading(false);
                  }
                }}
                variant="default"
                size="sm"
                className="w-full mb-3"
              >
                <KanbanSquare className="mr-2 h-4 w-4" />
                Criar Colunas Padrão
              </Button>

              {isAdmin && (
                <Button
                  onClick={() => setGerenciadorAberto(true)}
                  variant="outline"
                  size="sm"
                  className="w-full mb-3"
                >
                  <Settings className="mr-2 h-4 w-4" />
                  Configurar Colunas Manualmente
                </Button>
              )}

              <Button
                onClick={() => {
                  // Redirecionar para a página inicial
                  window.location.href = '/';
                }}
                variant="outline"
                size="sm"
                className="w-full"
              >
                Voltar para Dashboard
              </Button>
            </div>
          ) : isAuthError ? (
            <>
              <p className="text-sm text-foreground mb-3">
                Detectamos um problema com sua autenticação. Isso pode ocorrer quando sua sessão expirou ou houve um problema com o token de acesso.
              </p>
              <div className="p-3 bg-background border rounded-md mb-4">
                <p className="text-xs text-muted-foreground mb-2">Detalhes técnicos:</p>
                <p className="text-xs font-mono bg-background p-2 rounded border overflow-auto">
                  {errorMessage}
                </p>
              </div>
              <div className="flex flex-col gap-3">
                <Button
                  onClick={() => {
                    // Tentar reconectar
                    const reconnectEvent = new CustomEvent('force-reconnect');
                    document.dispatchEvent(reconnectEvent);

                    // Após um breve delay, tentar carregar novamente
                    setTimeout(() => {
                      loadAttemptCountRef.current = 0;
                      carregarDados(0, true);
                    }, 1500);
                  }}
                  variant="default"
                  size="sm"
                  className="w-full"
                >
                  <ShieldAlert className="mr-2 h-4 w-4" />
                  Reconectar
                </Button>
                <Button
                  onClick={() => {
                    // Redirecionar para a página de login
                    window.location.href = '/login';
                  }}
                  variant="outline"
                  size="sm"
                  className="w-full"
                >
                  Ir para Login
                </Button>
              </div>
            </>
          ) : isConnectionError ? (
            <>
              <p className="text-sm text-foreground mb-3">
                Detectamos um problema de conexão com o servidor. Verifique sua conexão com a internet e tente novamente.
              </p>
              <div className="p-3 bg-background border rounded-md mb-4">
                <p className="text-xs text-muted-foreground mb-2">Detalhes técnicos:</p>
                <p className="text-xs font-mono bg-background p-2 rounded border overflow-auto">
                  {errorMessage}
                </p>
              </div>
              <div className="flex flex-col gap-3">
                <Button
                  onClick={() => {
                    // Resetar contador de tentativas
                    loadAttemptCountRef.current = 0;
                    carregarDados(0, true); // Forçar recarregamento
                  }}
                  variant="default"
                  size="sm"
                  className="w-full"
                >
                  <Wifi className="mr-2 h-4 w-4" />
                  Verificar Conexão
                </Button>
              </div>
            </>
          ) : (
            <>
              <p className="text-sm text-foreground mb-3">Ocorreu um erro ao carregar os precatórios:</p>
              <div className="p-3 bg-background border rounded-md mb-4">
                <p className="text-xs text-muted-foreground mb-2">Detalhes do erro:</p>
                <p className="text-xs font-mono bg-background p-2 rounded border overflow-auto">
                  {errorMessage}
                </p>
              </div>

              <div className="flex flex-col gap-3">
                <Button
                  onClick={() => {
                    // Resetar contador de tentativas
                    loadAttemptCountRef.current = 0;
                    carregarDados(0, true); // Forçar recarregamento
                  }}
                  variant="destructive"
                  size="sm"
                >
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Tentar Novamente
                </Button>

                {forceReloadNeeded && (
                  <div className="mt-4 flex flex-col gap-2">
                    <Button
                      onClick={() => {
                        // Limpar cache e forçar recarregamento da página
                        clearCache();
                        window.location.reload();
                      }}
                      variant="outline"
                      size="sm"
                      className="bg-background hover:bg-accent"
                    >
                      <RefreshCw className="mr-2 h-4 w-4" />
                      Recarregar Página
                    </Button>

                    <Button
                      onClick={() => {
                        // Redirecionar para a página inicial
                        window.location.href = '/';
                      }}
                      variant="outline"
                      size="sm"
                    >
                      Voltar para Dashboard
                    </Button>
                  </div>
                )}
              </div>
            </>
          )}
        </div>
      </div>
    );
  };

  // Função para reconectar e recarregar dados
  const handleReconnect = () => {
    console.log('Reconectado com sucesso, recarregando dados...');
    // Resetar contadores e estados
    loadAttemptCountRef.current = 0;
    setForceReloadNeeded(false);
    // Forçar recarregamento completo
    carregarDados(0, true);
  };

  // Função para lidar com falhas de reconexão
  const handleReconnectFailure = () => {
    console.error('Falha na reconexão, redirecionando para login...');
    // Limpar cache antes de redirecionar
    clearCache();
    // Redirecionar para login após um breve delay
    setTimeout(() => {
      window.location.href = '/login';
    }, 1500);
  };

  return (
    <div className="flex flex-col h-screen w-screen overflow-hidden" style={{['--dock-nav-height']: '6rem'} as React.CSSProperties}>
      {/* Componente de reconexão automática */}
      <AutoReconnect
        floating={true}
        onReconnect={handleReconnect}
        onReconnectFailure={handleReconnectFailure}
        reconnectInterval={15000} // Tentar reconectar a cada 15 segundos
        autoReconnect={!forceReloadNeeded} // Desativar reconexão automática se precisar de reload forçado
      />

      <TopNav
        title="Kanban Precatório/RPV"
        icon={<KanbanSquare className="h-6 w-6 text-primary" />}
      >
        {/* Navegação Principal (Precatórios/RPVs) */}
        <NavigationMenu>
          <NavigationMenuList>
            <NavigationMenuItem>
              <NavigationMenuTrigger
                className={cn(tipoVisualizacao === 'PRECATORIO' && "bg-accent text-accent-foreground")}
                onClick={() => setTipoVisualizacao('PRECATORIO')}
              >
                Precatórios
              </NavigationMenuTrigger>
            </NavigationMenuItem>
            <NavigationMenuItem>
               <NavigationMenuTrigger
                className={cn(tipoVisualizacao === 'RPV' && "bg-accent text-accent-foreground")}
                onClick={() => setTipoVisualizacao('RPV')}
               >
                 RPVs
               </NavigationMenuTrigger>
            </NavigationMenuItem>
          </NavigationMenuList>
        </NavigationMenu>

        {/* Botões à direita */}
        <div className="ml-auto flex items-center gap-2">
          {/* Botão de novo precatório */}
          <Button
            variant="default"
            size="sm"
            onClick={() => {
              const event = new CustomEvent('open-new-precatorio-modal');
              document.dispatchEvent(event);
            }}
          >
            <Plus className="mr-1.5 h-4 w-4" />
            Novo Precatório
          </Button>

          {/* Botão de filtros */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              // Disparar evento para abrir o modal de filtros
              const event = new CustomEvent('toggle-filtros-modal');
              document.dispatchEvent(event);
            }}
          >
            <Filter className="mr-1.5 h-4 w-4" />
            Filtros
          </Button>

          {/* Botão de atualizar dados */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              toast.info(`Atualizando ${tipoVisualizacao === 'PRECATORIO' ? 'precatórios' : 'RPVs'}...`);
              carregarDados(0, true); // Forçar recarregamento
            }}
            disabled={loading}
          >
            <RefreshCw className={cn("mr-1.5 h-4 w-4", loading && "animate-spin")} />
            Atualizar
          </Button>

          {/* Botão de informações de permissões */}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setPermissionsDialogOpen(true)}
                >
                  <Info className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Informações de permissões</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          {/* Botão de gerenciar colunas (baseado em permissões) */}
          <PermissionButton
            variant="outline"
            size="sm"
            onClick={() => setGerenciadorAberto(true)}
            action="edit"
            resource="kanban_column"
            tooltipMessage="Você não tem permissão para gerenciar colunas"
          >
            <Settings className="mr-1.5 h-4 w-4" />
            Gerenciar Colunas
          </PermissionButton>
        </div>
      </TopNav>

      {/* Área Principal de Conteúdo (Kanban) - Ocupa o espaço restante */}
      <main
        className="flex-1 overflow-auto bg-gradient-to-b from-background to-muted/10 pt-[65px]"
      >
        {permissionsLoading ? (
          <div className="flex h-[calc(100vh-var(--top-nav-height))] w-full items-center justify-center flex-col" style={{['--top-nav-height']: "65px"} as React.CSSProperties}>
            <div className="relative">
              <div className="absolute -inset-1 rounded-full bg-gradient-to-r from-primary to-primary/50 opacity-75 blur-sm"></div>
              <Loader2 className="h-12 w-12 animate-spin text-primary relative" />
            </div>
            <span className="mt-6 text-lg font-medium text-muted-foreground">
              Verificando permissões...
            </span>
          </div>
        ) : !canAccessKanban() ? (
          <div className="flex h-[calc(100vh-var(--top-nav-height))] w-full items-center justify-center flex-col" style={{['--top-nav-height']: "65px"} as React.CSSProperties}>
            <div className="max-w-md p-6 bg-card rounded-lg border shadow-sm text-center">
              <Lock className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <h2 className="text-xl font-semibold mb-2">Acesso Restrito</h2>
              <p className="text-muted-foreground mb-4">
                Você não tem permissão para acessar o Kanban de Precatórios/RPV.
              </p>
              <p className="text-sm text-muted-foreground">
                Entre em contato com um administrador para solicitar acesso.
              </p>
            </div>
          </div>
        ) : loading && precatorios.length === 0 ? renderLoading() :
         errorMessage && precatorios.length === 0 ? renderError() :
         (
            <KanbanContainer
              key={tipoVisualizacao} // Forçar remontagem ao mudar tipo
              todosPrecatorios={precatorios}
              colunas={colunas}
              onSavePrecatorio={handleSavePrecatorio}
              onDeletePrecatorio={handleDeletePrecatorio}
              onMovePrecatorio={handleMovePrecatorio}
            />
         )
        }
      </main>

      {/* Modal Gerenciador de Colunas */}
      <Dialog open={gerenciadorAberto} onOpenChange={setGerenciadorAberto}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Gerenciar Colunas do Kanban ({tipoVisualizacao})</DialogTitle>
            <DialogDescription>
              Adicione, edite ou reordene as colunas para o fluxo de {tipoVisualizacao === 'PRECATORIO' ? 'Precatórios' : 'RPVs'}.
            </DialogDescription>
          </DialogHeader>
          <KanbanPermissionGuard
            action="edit"
            resourceType="kanban_column"
            fallback={
              <div className="p-6 text-center">
                <Lock className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-medium mb-2">Acesso Restrito</h3>
                <p className="text-muted-foreground mb-4">
                  Você não tem permissão para gerenciar colunas do Kanban.
                </p>
                <p className="text-sm text-muted-foreground">
                  Entre em contato com um administrador para solicitar acesso.
                </p>
              </div>
            }
          >
            <GerenciadorColunas
              tipoInicial={tipoVisualizacao}
              onColumnsChange={() => {
                setGerenciadorAberto(false);
                carregarDados(); // Recarrega após mudanças
              }}
            />
          </KanbanPermissionGuard>
          <DialogFooter>
            <Button variant="outline" onClick={() => setGerenciadorAberto(false)}>
              Fechar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Modal de Informações de Permissões */}
      <Dialog open={permissionsDialogOpen} onOpenChange={setPermissionsDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Permissões do Kanban</DialogTitle>
            <DialogDescription>
              Informações sobre suas permissões no Kanban de Precatórios/RPV.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <h3 className="text-sm font-medium">Seu perfil</h3>
              <div className="flex items-center gap-2 p-2 bg-muted rounded-md">
                <UserCheck className="h-5 w-5 text-muted-foreground" />
                <span>{user?.role || 'Usuário'}</span>
              </div>
            </div>

            {permissionsError && (
              <div className="p-3 bg-destructive/10 text-destructive rounded-md text-sm">
                <p className="font-medium">Erro ao carregar permissões</p>
                <p className="text-xs mt-1">{permissionsError.message}</p>
              </div>
            )}

            <div className="text-sm text-muted-foreground">
              <p>
                O acesso às colunas e precatórios é controlado por um sistema de permissões.
                Caso precise de acesso a mais recursos, entre em contato com um administrador.
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setPermissionsDialogOpen(false)}>
              Fechar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default PrecatoriosKanban;
