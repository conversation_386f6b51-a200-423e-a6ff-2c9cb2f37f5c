import { useState, useEffect } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { TopNav } from "@/components/top-nav";
import {
  FileText,
  ArrowLeft,
  Download,
  Edit,
  Save,
  Trash2,
  CheckCircle,
  XCircle,
  Clock,
  User,
  Calendar,
  Tag,
  FileUp,
  Share2,
  Printer,
  MoreHorizontal,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import {
  buscarDocumentoPorId,
  Documento,
  obterUrlDownload,
  atualizarStatusDocumento,
  excluirDocumento
} from "@/services/documentosService";
import { RichTextEditor } from "@/components/RichTextEditor";
import { Skeleton } from "@/components/ui/skeleton";

export default function DocumentView() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [documento, setDocumento] = useState<Documento | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [editedContent, setEditedContent] = useState("");

  useEffect(() => {
    const carregarDocumento = async () => {
      if (!id) return;

      try {
        setIsLoading(true);
        const doc = await buscarDocumentoPorId(id);
        setDocumento(doc);
        setEditedContent(doc?.conteudo || "");
      } catch (error) {
        console.error("Erro ao carregar documento:", error);
        toast.error("Erro ao carregar documento");
      } finally {
        setIsLoading(false);
      }
    };

    carregarDocumento();
  }, [id]);

  const handleDownload = async () => {
    try {
      if (!documento?.url) {
        toast.error("URL do documento não disponível");
        return;
      }

      const url = await obterUrlDownload(documento.url);
      window.open(url, "_blank");
    } catch (error) {
      console.error("Erro ao baixar documento:", error);
      toast.error("Erro ao baixar documento");
    }
  };

  const handleUpdateStatus = async (novoStatus: 'pendente' | 'aprovado' | 'rejeitado') => {
    try {
      if (!documento?.id || !documento?.categoria) {
        toast.error("Informações do documento incompletas");
        return;
      }

      await atualizarStatusDocumento(
        documento.id,
        novoStatus,
        documento.categoria as 'cliente' | 'precatorio'
      );

      setDocumento(prev => prev ? { ...prev, status: novoStatus } : null);
      toast.success(`Status atualizado para ${novoStatus}`);
    } catch (error) {
      console.error("Erro ao atualizar status:", error);
      toast.error("Erro ao atualizar status");
    }
  };

  const handleDelete = async () => {
    try {
      if (!documento?.id || !documento?.categoria) {
        toast.error("Informações do documento incompletas");
        return;
      }

      await excluirDocumento(documento.id, documento.categoria as 'cliente' | 'precatorio');
      toast.success("Documento excluído com sucesso");
      navigate("/documents");
    } catch (error) {
      console.error("Erro ao excluir documento:", error);
      toast.error("Erro ao excluir documento");
    }
  };

  const handleSaveEdit = async () => {
    // Aqui implementaríamos a lógica para salvar o conteúdo editado
    // Por enquanto, apenas simulamos o sucesso
    toast.success("Documento atualizado com sucesso");
    setIsEditing(false);
  };

  const handlePrint = () => {
    window.print();
  };

  // Renderizar status do documento
  const renderStatus = (status: string) => {
    switch (status) {
      case 'aprovado':
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            <CheckCircle className="w-3 h-3 mr-1" /> Aprovado
          </Badge>
        );
      case 'rejeitado':
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            <XCircle className="w-3 h-3 mr-1" /> Rejeitado
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
            <Clock className="w-3 h-3 mr-1" /> Pendente
          </Badge>
        );
    }
  };

  if (isLoading) {
    return (
      <div className="flex flex-col h-screen w-screen overflow-hidden">
        <TopNav
          title="Visualizando Documento"
          icon={<FileText className="h-6 w-6 text-primary" />}
        />

        <div className="flex flex-1 overflow-auto pt-[65px]">
          <div className="p-4 md:p-6 rounded-tl-2xl border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-900 flex flex-col gap-6 flex-1 w-full h-full">
            <div className="flex items-center gap-4">
              <Button variant="outline" size="icon" onClick={() => navigate("/documents")}>
                <ArrowLeft className="h-4 w-4" />
              </Button>
              <Skeleton className="h-8 w-64" />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card className="md:col-span-2">
                <CardHeader>
                  <Skeleton className="h-6 w-32" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-[400px] w-full" />
                </CardContent>
              </Card>

              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <Skeleton className="h-6 w-32" />
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-full" />
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <Skeleton className="h-6 w-32" />
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-full" />
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!documento) {
    return (
      <div className="flex flex-col h-screen w-screen overflow-hidden">
        <TopNav
          title="Documento não encontrado"
          icon={<FileText className="h-6 w-6 text-primary" />}
        />

        <div className="flex flex-1 overflow-auto pt-[65px]">
          <div className="p-4 md:p-6 rounded-tl-2xl border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-900 flex flex-col gap-6 flex-1 w-full h-full">
            <div className="flex items-center gap-4">
              <Button variant="outline" size="icon" onClick={() => navigate("/documents")}>
                <ArrowLeft className="h-4 w-4" />
              </Button>
              <h1 className="text-2xl font-bold">Documento não encontrado</h1>
            </div>

            <div className="flex flex-col items-center justify-center py-12 text-center">
              <FileText className="h-16 w-16 text-muted-foreground mb-4 opacity-50" />
              <h3 className="text-lg font-medium mb-2">Documento não encontrado</h3>
              <p className="text-sm text-muted-foreground mb-4 max-w-md">
                O documento que você está procurando não existe ou foi removido.
              </p>
              <Button onClick={() => navigate("/documents")}>
                Voltar para Documentos
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-screen w-screen overflow-hidden">
      <TopNav
        title="Visualizando Documento"
        icon={<FileText className="h-6 w-6 text-primary" />}
      />

      <div className="flex flex-1 overflow-auto pt-[65px]">
        <div className="p-4 md:p-6 rounded-tl-2xl border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-900 flex flex-col gap-6 flex-1 w-full h-full">
          {/* Cabeçalho */}
          <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
            <div className="flex items-center gap-4">
              <Button variant="outline" size="icon" onClick={() => navigate("/documents")}>
                <ArrowLeft className="h-4 w-4" />
              </Button>
              <div>
                <h1 className="text-2xl font-bold">{documento.nome}</h1>
                <div className="flex flex-wrap items-center gap-2 mt-1">
                  {renderStatus(documento.status)}
                  <Badge variant="outline" className="bg-primary/10">
                    {documento.tipo}
                  </Badge>
                  <Badge variant="outline">
                    {documento.categoria === 'cliente' ? 'Cliente' : 'Precatório'}
                  </Badge>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-2">
              {!isEditing ? (
                <>
                  <Button variant="outline" size="sm" onClick={handlePrint}>
                    <Printer className="h-4 w-4 mr-2" />
                    Imprimir
                  </Button>
                  <Button variant="outline" size="sm" onClick={handleDownload}>
                    <Download className="h-4 w-4 mr-2" />
                    Baixar
                  </Button>
                  <Button size="sm" onClick={() => navigate(`/documents/editar/${documento.id}`)}>
                    <Edit className="h-4 w-4 mr-2" />
                    Editar
                  </Button>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Ações</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => handleUpdateStatus('aprovado')}>
                        <CheckCircle className="h-4 w-4 mr-2" />
                        Aprovar
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleUpdateStatus('rejeitado')}>
                        <XCircle className="h-4 w-4 mr-2" />
                        Rejeitar
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleUpdateStatus('pendente')}>
                        <Clock className="h-4 w-4 mr-2" />
                        Marcar como Pendente
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={handleDelete} className="text-red-600">
                        <Trash2 className="h-4 w-4 mr-2" />
                        Excluir
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </>
              ) : (
                <>
                  <Button variant="outline" size="sm" onClick={() => setIsEditing(false)}>
                    Cancelar
                  </Button>
                  <Button size="sm" onClick={handleSaveEdit}>
                    <Save className="h-4 w-4 mr-2" />
                    Salvar
                  </Button>
                </>
              )}
            </div>
          </div>

          {/* Conteúdo principal */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Documento */}
            <Card className="md:col-span-2 border-none shadow-md">
              <CardHeader className="bg-primary/5 pb-2">
                <CardTitle className="text-lg flex items-center gap-2">
                  <FileText className="h-5 w-5 text-primary" />
                  Conteúdo do Documento
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                {isEditing ? (
                  <RichTextEditor
                    initialContent={documento.conteudo || ""}
                    onChange={setEditedContent}
                    minHeight="500px"
                  />
                ) : (
                  <div className="prose dark:prose-invert max-w-none">
                    <RichTextEditor
                      initialContent={documento.conteudo || ""}
                      readOnly
                      minHeight="500px"
                    />
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Informações laterais */}
            <div className="space-y-6">
              {/* Detalhes do documento */}
              <Card className="border-none shadow-md">
                <CardHeader className="bg-primary/5 pb-2">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <FileUp className="h-5 w-5 text-primary" />
                    Detalhes do Documento
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-6 space-y-4">
                  <div className="flex items-center gap-3 text-sm p-2 rounded-md hover:bg-muted/50 transition-colors">
                    <div className="bg-primary/10 p-2 rounded-full">
                      <Calendar className="w-4 h-4 text-primary" />
                    </div>
                    <div className="flex flex-col">
                      <span className="text-xs text-muted-foreground">Data de Upload</span>
                      <span className="font-medium">
                        {new Date(documento.data_upload).toLocaleDateString('pt-BR')}
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center gap-3 text-sm p-2 rounded-md hover:bg-muted/50 transition-colors">
                    <div className="bg-primary/10 p-2 rounded-full">
                      <Tag className="w-4 h-4 text-primary" />
                    </div>
                    <div className="flex flex-col">
                      <span className="text-xs text-muted-foreground">Tipo</span>
                      <span className="font-medium">{documento.tipo}</span>
                    </div>
                  </div>

                  <div className="flex items-center gap-3 text-sm p-2 rounded-md hover:bg-muted/50 transition-colors">
                    <div className="bg-primary/10 p-2 rounded-full">
                      <FileUp className="w-4 h-4 text-primary" />
                    </div>
                    <div className="flex flex-col">
                      <span className="text-xs text-muted-foreground">Tamanho</span>
                      <span className="font-medium">{documento.tamanho || "128 KB"}</span>
                    </div>
                  </div>

                  {documento.usuario_nome && (
                    <div className="flex items-center gap-3 text-sm p-2 rounded-md hover:bg-muted/50 transition-colors">
                      <div className="bg-primary/10 p-2 rounded-full">
                        <User className="w-4 h-4 text-primary" />
                      </div>
                      <div className="flex flex-col">
                        <span className="text-xs text-muted-foreground">Criado por</span>
                        <span className="font-medium">{documento.usuario_nome}</span>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Associação */}
              <Card className="border-none shadow-md">
                <CardHeader className="bg-primary/5 pb-2">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Share2 className="h-5 w-5 text-primary" />
                    Associado a
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  {documento.cliente_id && (
                    <div className="flex items-center gap-3 text-sm p-2 rounded-md hover:bg-muted/50 transition-colors">
                      <div className="bg-primary/10 p-2 rounded-full">
                        <User className="w-4 h-4 text-primary" />
                      </div>
                      <div className="flex flex-col">
                        <span className="text-xs text-muted-foreground">Cliente</span>
                        <span className="font-medium">
                          {documento.cliente_nome || "Cliente não especificado"}
                        </span>
                      </div>
                    </div>
                  )}

                  {documento.precatorio_id && (
                    <div className="flex items-center gap-3 text-sm p-2 rounded-md hover:bg-muted/50 transition-colors">
                      <div className="bg-primary/10 p-2 rounded-full">
                        <FileText className="w-4 h-4 text-primary" />
                      </div>
                      <div className="flex flex-col">
                        <span className="text-xs text-muted-foreground">Precatório</span>
                        <span className="font-medium">
                          {documento.precatorio_numero || documento.precatorio_id}
                        </span>
                      </div>
                    </div>
                  )}

                  {!documento.cliente_id && !documento.precatorio_id && (
                    <div className="text-sm text-muted-foreground text-center py-4">
                      Documento não associado a nenhum cliente ou precatório.
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Ações */}
              <Card className="border-none shadow-md">
                <CardHeader className="bg-primary/5 pb-2">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <MoreHorizontal className="h-5 w-5 text-primary" />
                    Ações
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  <div className="flex flex-col gap-2">
                    <Button variant="outline" className="justify-start" onClick={handleDownload}>
                      <Download className="h-4 w-4 mr-2" />
                      Baixar Documento
                    </Button>
                    <Button variant="outline" className="justify-start" onClick={handlePrint}>
                      <Printer className="h-4 w-4 mr-2" />
                      Imprimir Documento
                    </Button>
                    <Separator className="my-2" />
                    <Button
                      variant="outline"
                      className="justify-start text-red-600 hover:text-red-700 hover:bg-red-50"
                      onClick={handleDelete}
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Excluir Documento
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
