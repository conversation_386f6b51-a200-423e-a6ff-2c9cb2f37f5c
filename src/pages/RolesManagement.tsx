import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { Card, CardContent } from "@/components/ui/card";
import { TopNav } from "@/components/top-nav";
import { RoleManager } from "@/components/roles/RoleManager";
import { Shield } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";

export default function RolesManagement() {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);

  // Verificar permissões do usuário atual
  useEffect(() => {
    try {
      // Verificar se é administrador
      if (!user || user.role !== 'admin') {
        toast.error("Acesso negado", {
          description: "Você não tem permissão para acessar esta página."
        });
        navigate("/dashboard");
      } else {
        setLoading(false);
      }
    } catch (error) {
      console.error("Erro ao verificar permissões:", error);
      navigate("/dashboard");
    }
  }, [navigate, user]);

  // Função para atualizar a lista manualmente
  const handleRolesUpdated = () => {
    console.log("RolesManagement: Cargos atualizados");
  };

  return (
    <div className="flex flex-col h-screen w-screen overflow-hidden">
      <TopNav
        title="Gerenciamento de Cargos"
        icon={<Shield className="h-6 w-6 text-primary" />}
      />

      <div className="flex flex-1 overflow-auto pt-[65px]">
        <div className="p-4 md:p-6 rounded-tl-2xl border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-900 flex flex-col gap-4 flex-1 w-full h-full">
          <Card>
            <CardContent className="p-6">
              <RoleManager onRolesUpdated={handleRolesUpdated} />
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
