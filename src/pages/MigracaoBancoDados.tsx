import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/lib/supabase';
import { Loader2, AlertTriangle, CheckCircle2 } from 'lucide-react';
import { toast } from 'sonner';

export function MigracaoBancoDados() {
  const { isAdmin } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [resultado, setResultado] = useState<{
    sucesso: boolean;
    mensagem: string;
    detalhes?: string;
  } | null>(null);

  // Redirecionar se não for admin
  if (!isAdmin) {
    navigate('/');
    return null;
  }

  const criarTabelaKanbanColunas = async () => {
    setLoading(true);
    setResultado(null);
    
    try {
      // Verificar se a tabela já existe
      const { error: checkError } = await supabase
        .from('kanban_colunas')
        .select('count')
        .limit(1);

      if (!checkError) {
        setResultado({
          sucesso: false,
          mensagem: 'A tabela kanban_colunas já existe!',
          detalhes: 'Não é necessário criar a tabela novamente.'
        });
        setLoading(false);
        return;
      }

      // Criar a tabela kanban_colunas
      const { error: createTableError } = await supabase.rpc('criar_tabela_kanban_colunas');

      if (createTableError) {
        throw new Error(`Erro ao criar tabela: ${createTableError.message}`);
      }

      // Inserir dados iniciais
      const colunasIniciais = [
        { nome: 'Análise', cor: '#3b82f6', tipo: 'AMBOS', ordem: 1, status_id: 'analise', ativo: true },
        { nome: 'Proposta TMJ', cor: '#8b5cf6', tipo: 'AMBOS', ordem: 2, status_id: 'proposta_tmj', ativo: true },
        { nome: 'Proposta BTG', cor: '#ec4899', tipo: 'AMBOS', ordem: 3, status_id: 'proposta_btg', ativo: true },
        { nome: 'Negociação', cor: '#f59e0b', tipo: 'AMBOS', ordem: 4, status_id: 'negociacao', ativo: true },
        { nome: 'Documentação', cor: '#10b981', tipo: 'AMBOS', ordem: 5, status_id: 'documentacao', ativo: true },
        { nome: 'Pagamento', cor: '#6366f1', tipo: 'AMBOS', ordem: 6, status_id: 'pagamento', ativo: true },
        { nome: 'Concluído', cor: '#22c55e', tipo: 'AMBOS', ordem: 7, status_id: 'concluido', ativo: true },
        { nome: 'Cancelado', cor: '#ef4444', tipo: 'AMBOS', ordem: 8, status_id: 'cancelado', ativo: true }
      ];

      const { error: insertError } = await supabase
        .from('kanban_colunas')
        .insert(colunasIniciais);

      if (insertError) {
        throw new Error(`Erro ao inserir dados iniciais: ${insertError.message}`);
      }

      setResultado({
        sucesso: true,
        mensagem: 'Tabela kanban_colunas criada com sucesso!',
        detalhes: 'Todas as colunas iniciais foram inseridas.'
      });
      
      toast.success('Migração concluída com sucesso!');
    } catch (error: any) {
      console.error('Erro na migração:', error);
      setResultado({
        sucesso: false,
        mensagem: 'Erro ao criar tabela kanban_colunas',
        detalhes: error.message
      });
      toast.error('Erro na migração do banco de dados');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container py-8">
      <Card className="w-full max-w-3xl mx-auto">
        <CardHeader>
          <CardTitle>Migração do Banco de Dados</CardTitle>
          <CardDescription>
            Esta página permite criar e configurar tabelas necessárias para o sistema.
            Apenas administradores têm acesso a esta funcionalidade.
          </CardDescription>
        </CardHeader>
        
        <CardContent>
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-2">Criar Tabela Kanban Colunas</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Cria a tabela kanban_colunas e insere as colunas padrão do Kanban.
                Esta operação é necessária para o funcionamento do gerenciador de colunas do Kanban.
              </p>
              
              <Button 
                onClick={criarTabelaKanbanColunas} 
                disabled={loading}
                variant="default"
              >
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Processando...
                  </>
                ) : (
                  'Criar Tabela Kanban Colunas'
                )}
              </Button>
            </div>
            
            {resultado && (
              <>
                <Separator className="my-4" />
                
                <Alert variant={resultado.sucesso ? "default" : "destructive"}>
                  {resultado.sucesso ? (
                    <CheckCircle2 className="h-4 w-4" />
                  ) : (
                    <AlertTriangle className="h-4 w-4" />
                  )}
                  <AlertTitle>
                    {resultado.sucesso ? 'Operação concluída' : 'Erro na operação'}
                  </AlertTitle>
                  <AlertDescription>
                    {resultado.mensagem}
                    {resultado.detalhes && (
                      <div className="mt-2 text-xs bg-muted p-2 rounded">
                        {resultado.detalhes}
                      </div>
                    )}
                  </AlertDescription>
                </Alert>
              </>
            )}
          </div>
        </CardContent>
        
        <CardFooter className="flex justify-between">
          <Button variant="outline" onClick={() => navigate('/')}>
            Voltar para o início
          </Button>
          <Button variant="outline" onClick={() => navigate('/precatorios/kanban')}>
            Ir para o Kanban
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}

export default MigracaoBancoDados;
