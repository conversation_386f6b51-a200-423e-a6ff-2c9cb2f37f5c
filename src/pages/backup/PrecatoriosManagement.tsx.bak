// Buscar dados de precatórios do Supabase
const fetchPrecatorios = async () => {
  try {
    setLoadingPrecatorios(true);
    console.log("Iniciando busca de precatórios...");
    
    // Consulta principal para precatórios - versão simplificada
    const { data, error } = await supabase
      .from('precatorios')
      .select('*');

    if (error) {
      console.error("Erro na consulta principal:", error);
      throw error;
    }

    console.log(`Encontrados ${data?.length || 0} precatórios`);
    
    if (!data || data.length === 0) {
      // Se não houver dados, definimos arrays vazios e estatísticas zeradas
      setFilteredPrecatorios([]);
      setPrecatoriosStats({
        total: 0,
        aguardandoPagamento: 0,
        emProcessamento: 0,
        pagos: 0,
        suspensos: 0,
        valorTotal: 0
      });
      setLoadingPrecatorios(false);
      return;
    }

    // Formatar os dados para o formato esperado pela UI - versão simplificada
    const formattedData = data.map(item => ({
      id: item.id || "",
      numero_precatorio: item.numero_precatorio || "",
      beneficiario: { nome: "Carregando..." },
      tribunal: { nome: "Carregando..." },
      tipo: { nome: "Carregando..." },
      status: item.status || "",
      responsavel: { nome: "Carregando..." },
      data_entrada: item.data_entrada,
      valor_total: item.valor_total?.toString() || "0",
      prioridade: item.prioridade || "normal",
      entidade_devedora: item.entidade_devedora || "",
      data_previsao_pagamento: item.data_previsao_pagamento,
      prazo_atual: item.prazo_atual || "",
      natureza: item.natureza || "",
      documentos: 0,
      ultima_movimentacao: "",
      detalhes_movimentacao: ""
    }));

    // Obter estatísticas
    console.log("Calculando estatísticas...");
    const estatisticas = {
      total: formattedData.length,
      aguardandoPagamento: formattedData.filter(p => p.status === "Aguardando Pagamento").length,
      emProcessamento: formattedData.filter(p => p.status === "Em Processamento").length,
      pagos: formattedData.filter(p => p.status === "Pago").length,
      suspensos: formattedData.filter(p => p.status === "Suspenso").length,
      valorTotal: formattedData.reduce((acc, curr) => acc + Number(curr.valor_total || 0), 0)
    };

    console.log("Estatísticas:", estatisticas);
    setPrecatoriosStats(estatisticas);
    setFilteredPrecatorios(formattedData);
    setLoadingPrecatorios(false);
    console.log("Busca de precatórios concluída com sucesso");
  } catch (error) {
    console.error("Erro ao buscar precatórios:", error);
    // Definir um estado de erro mas permitir que a UI continue renderizando
    toast.error("Erro ao carregar precatórios. Verifique a conexão com o banco de dados.");
    setFilteredPrecatorios([]);
    setPrecatoriosStats({
      total: 0,
      aguardandoPagamento: 0,
      emProcessamento: 0,
      pagos: 0,
      suspensos: 0,
      valorTotal: 0
    });
    setLoadingPrecatorios(false);
  }
};

export default function PrecatoriosManagement() {
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState("todos");
  const [sorting, setSorting] = useState([]);
  const [columnVisibility, setColumnVisibility] = useState({});
  const [loadingPrecatorios, setLoadingPrecatorios] = useState(false);
  const [filteredPrecatorios, setFilteredPrecatorios] = useState([]);
  const [precatoriosStats, setPrecatoriosStats] = useState({
    total: 0,
    aguardandoPagamento: 0,
    emProcessamento: 0,
    pagos: 0,
    suspensos: 0,
    valorTotal: 0
  });
  const navigate = useNavigate();
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Definições de tipos
  interface Beneficiario {
    nome: string;
  }

  interface Tribunal {
    nome: string;
  }

  interface TipoPrecatorio {
    nome: string;
  }

  interface Responsavel {
    nome: string;
  }

  interface Precatorio {
    id: string;
    numero_precatorio: string;
    beneficiario: Beneficiario;
    tribunal: Tribunal;
    tipo: TipoPrecatorio;
    status: string;
    responsavel: Responsavel;
    data_entrada: string;
    valor_total: string;
    prioridade: string;
    entidade_devedora: string;
    data_previsao_pagamento: string;
    prazo_atual: string;
    natureza: string;
    documentos: number;
    ultima_movimentacao: string;
    detalhes_movimentacao: string;
  }

  interface VisibilityState {
    [key: string]: boolean;
  }

  interface PrecatoriosStats {
    total: number;
    aguardandoPagamento: number;
    emProcessamento: number;
    pagos: number;
    suspensos: number;
    valorTotal: number;
  }

  // Utilizamos o tipo correto do react-table
  type SortingState = {
    id: string;
    desc: boolean;
  }[];

  const handleFetchPrecatorios = async () => {
    try {
      setLoadingPrecatorios(true);
      console.log("Iniciando busca de precatórios...");
      
      // Consulta principal para precatórios - versão simplificada
      const { data, error } = await supabase
        .from('precatorios')
        .select('*');

      if (error) {
        console.error("Erro na consulta principal:", error);
        throw error;
      }

      console.log(`Encontrados ${data?.length || 0} precatórios`);
      
      if (!data || data.length === 0) {
        // Se não houver dados, definimos arrays vazios e estatísticas zeradas
        setFilteredPrecatorios([]);
        setPrecatoriosStats({
          total: 0,
          aguardandoPagamento: 0,
          emProcessamento: 0,
          pagos: 0,
          suspensos: 0,
          valorTotal: 0
        });
        setLoadingPrecatorios(false);
        return;
      }

      // Formatar os dados para o formato esperado pela UI - versão simplificada
      const formattedData = data.map(item => ({
        id: item.id || "",
        numero_precatorio: item.numero_precatorio || "",
        beneficiario: { nome: "Carregando..." },
        tribunal: { nome: "Carregando..." },
        tipo: { nome: "Carregando..." },
        status: item.status || "",
        responsavel: { nome: "Carregando..." },
        data_entrada: item.data_entrada,
        valor_total: item.valor_total?.toString() || "0",
        prioridade: item.prioridade || "normal",
        entidade_devedora: item.entidade_devedora || "",
        data_previsao_pagamento: item.data_previsao_pagamento,
        prazo_atual: item.prazo_atual || "",
        natureza: item.natureza || "",
        documentos: 0,
        ultima_movimentacao: "",
        detalhes_movimentacao: ""
      }));

      // Obter estatísticas
      console.log("Calculando estatísticas...");
      const estatisticas = {
        total: formattedData.length,
        aguardandoPagamento: formattedData.filter(p => p.status === "Aguardando Pagamento").length,
        emProcessamento: formattedData.filter(p => p.status === "Em Processamento").length,
        pagos: formattedData.filter(p => p.status === "Pago").length,
        suspensos: formattedData.filter(p => p.status === "Suspenso").length,
        valorTotal: formattedData.reduce((acc, curr) => acc + Number(curr.valor_total || 0), 0)
      };

      console.log("Estatísticas:", estatisticas);
      setPrecatoriosStats(estatisticas);
      setFilteredPrecatorios(formattedData);
      setLoadingPrecatorios(false);
      console.log("Busca de precatórios concluída com sucesso");
    } catch (error) {
      console.error("Erro ao buscar precatórios:", error);
      // Definir um estado de erro mas permitir que a UI continue renderizando
      toast.error("Erro ao carregar precatórios. Verifique a conexão com o banco de dados.");
      setFilteredPrecatorios([]);
      setPrecatoriosStats({
        total: 0,
        aguardandoPagamento: 0,
        emProcessamento: 0,
        pagos: 0,
        suspensos: 0,
        valorTotal: 0
      });
      setLoadingPrecatorios(false);
    }
  };

  // Buscar dados quando o componente for montado
  useEffect(() => {
    handleFetchPrecatorios();
  }, []);

  // Função para filtrar precatórios por status
  const handleFilterByStatus = (status: string) => {
    setFilterStatus(status);
  };

  // Função para filtrar precatórios por termo de busca
  const handleSearch = (term: string) => {
    setSearchTerm(term);
  };

  // Função para navegar para a página de detalhes do precatório
  const handleViewPrecatorio = (id: string) => {
    navigate(`/precatorios/${id}`);
  };

  // Função para criar um novo precatório
  const handleCreatePrecatorio = () => {
    navigate("/precatorios/novo");
  };

  // Renderizando o componente
  return (
    <div className="flex flex-col gap-6 p-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Gestão de Precatórios</h1>
        <Button onClick={handleCreatePrecatorio} className="px-4 py-2 bg-primary hover:bg-primary/90">
          <Plus className="w-4 h-4 mr-2" />
          Novo Precatório
        </Button>
      </div>

      {/* Cards de estatísticas */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-5">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Total de Precatórios</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{precatoriosStats.total}</div>
            <p className="text-xs text-muted-foreground">Precatórios em todos os status</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Aguardando Pagamento</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{precatoriosStats.aguardandoPagamento}</div>
            <p className="text-xs text-muted-foreground">Precatórios prontos para pagamento</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Em Processamento</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{precatoriosStats.emProcessamento}</div>
            <p className="text-xs text-muted-foreground">Precatórios em andamento</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Pagos</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{precatoriosStats.pagos}</div>
            <p className="text-xs text-muted-foreground">Precatórios já pagos</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Valor Total</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">R$ {precatoriosStats.valorTotal.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}</div>
            <p className="text-xs text-muted-foreground">Soma de todos os precatórios</p>
          </CardContent>
        </Card>
      </div>

      {/* Restante do componente será implementado nas próximas partes */}
      {loadingPrecatorios ? (
        <div className="flex items-center justify-center h-40">
          <Loader2 className="w-8 h-8 animate-spin text-primary" />
          <span className="ml-2">Carregando precatórios...</span>
        </div>
      ) : (
        <div className="bg-background border rounded-lg p-4">
          <div className="text-center">
            {filteredPrecatorios.length === 0 ? (
              <p>Nenhum precatório encontrado. Verifique os filtros ou conecte-se ao banco de dados.</p>
            ) : (
              <p>{filteredPrecatorios.length} precatórios encontrados.</p>
            )}
          </div>
        </div>
      )}
    </div>
  );
}