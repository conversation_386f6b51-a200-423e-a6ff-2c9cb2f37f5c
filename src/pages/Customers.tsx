"use client";

import { useState, useEffect, useMemo, useCallback } from 'react';
import { useVisibilityChange } from '@/hooks/useVisibilityChange';
import {
  Users,
  UserPlus,
  Filter,
  Mail,
  Phone,
  ArrowUpRight,
  MoreHorizontal,
  FileText,
  AlertCircle,
  CheckCircle2,
  CircleDollarSign,
  Download,
  Upload,
  ChevronDown,
  ChevronUp,
  Loader2,
  Search,
  Calendar,
  LayoutGrid,
  LayoutList,
  SlidersHorizontal,
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { PermissionButton } from '@/components/ui/permission-button';
// import { PermissionTooltip } from '@/components/ui/permission-tooltip';
import { PermissionSection } from '@/components/permissions/PermissionSection';
// import { usePermissions } from '@/hooks/usePermissions';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useNavigate } from 'react-router-dom';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import type { ColumnDef } from "@tanstack/react-table";
import {
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  useReactTable,
  type SortingState,
  getPaginationRowModel,
} from "@tanstack/react-table";

// import { CSSProperties } from 'react';
import { buscarClientes, buscarClientesComFiltrosAvancados, FiltrosAvancados } from '@/services/clientesService';
import { clearCache } from '@/services/cacheService';
import { clearSupabaseCache } from '@/lib/supabase';
import { toast } from 'sonner';
import { TopNav } from '@/components/top-nav';
import { ClientCard } from '@/components/Customers/ClientCard';
import { ClientsStatistics } from '@/components/Customers/ClientsStatistics';
import { ClientsPagination } from '@/components/Customers/ClientsPagination';
import { ViewToggle } from '@/components/Customers/ViewToggle';
import { FilterModal, ClientesFiltros } from '@/components/Customers/FilterModal';
import { DataVisibilityGuard, ConditionalButton, useFilteredData } from '@/components/permissions/DataVisibilityGuard';
import { useDataVisibility } from '@/hooks/useDataVisibility';
// import { formatarMoeda } from '@/lib/utils';
import { CardLoading } from '@/components/ui/loading-spinner';

// Interface para o tipo Cliente
interface Cliente {
  id: string;
  nome: string;
  email: string;
  telefone: string;
  documento: string;
  tipo: "pessoa_fisica" | "pessoa_juridica";
  status: "ativo" | "inativo" | "pendente";
  endereco?: {
    rua: string;
    numero: string;
    complemento?: string;
    bairro: string;
    cidade: string;
    estado: string;
    cep: string;
  };
  dataCriacao: string;
  dataAtualizacao: string;
  precatorios?: number;
  valorTotal?: number;
}

// Estatísticas gerais mockadas
const estatisticasGerais = {
  total: 0,
  ativos: 0,
  inativos: 0,
  pendentes: 0,
  valorTotal: 0,
  precatoriosTotal: 0,
  precatoriosConcluidos: 0,
  precatoriosEmAndamento: 0,
};

// Componente para renderizar o cabeçalho da tabela
const TableColumnHeader = ({ column, title }: { column: any, title: string }) => {
  return (
    <div className="flex items-center justify-start gap-0.5">
      <span className="grow truncate">{title}</span>
      {column.getCanSort && typeof column.getCanSort === 'function' && column.getCanSort() && (
        <Button
          variant="ghost"
          size="icon"
          className="group -mr-1 size-7 shadow-none"
          onClick={() => {
            if (column.toggleSorting && typeof column.toggleSorting === 'function') {
              column.toggleSorting();
            }
          }}
        >
          {column.getIsSorted && typeof column.getIsSorted === 'function' ? (
            column.getIsSorted() === 'asc' ? (
              <ChevronUp className="h-4 w-4 opacity-60" />
            ) : column.getIsSorted() === 'desc' ? (
              <ChevronDown className="h-4 w-4 opacity-60" />
            ) : (
              <ChevronUp className="h-4 w-4 opacity-0 group-hover:opacity-60" />
            )
          ) : (
            <ChevronUp className="h-4 w-4 opacity-0 group-hover:opacity-60" />
          )}
          <span className="sr-only">Ordenar</span>
        </Button>
      )}
    </div>
  );
};

// Função para converter dados do banco para o formato da interface Cliente
const converterParaCliente = (clienteDB: any): Cliente => {
  return {
    id: clienteDB.id,
    nome: clienteDB.nome,
    email: clienteDB.email,
    telefone: clienteDB.telefone,
    documento: clienteDB.documento || clienteDB.cpf || clienteDB.cnpj || 'Não informado',
    tipo: clienteDB.tipo as "pessoa_fisica" | "pessoa_juridica",
    status: clienteDB.status as "ativo" | "inativo" | "pendente",
    endereco: clienteDB.endereco ? {
      rua: clienteDB.endereco.rua || '',
      numero: clienteDB.endereco.numero || '',
      complemento: clienteDB.endereco.complemento,
      bairro: clienteDB.endereco.bairro || '',
      cidade: clienteDB.endereco.cidade || '',
      estado: clienteDB.endereco.estado || '',
      cep: clienteDB.endereco.cep || '',
    } : undefined,
    dataCriacao: clienteDB.dataCriacao || clienteDB.data_criacao || new Date().toISOString(),
    dataAtualizacao: clienteDB.dataAtualizacao || clienteDB.data_atualizacao || new Date().toISOString(),
    precatorios: clienteDB.precatorios_count || clienteDB.precatorios?.length || 0,
    valorTotal: clienteDB.valor_total || 0,
  };
};

export default function Customers() {
  const navigate = useNavigate();
  // const { canCreate, canEdit, canDelete, canView } = usePermissions();
  const [sorting, setSorting] = useState<SortingState>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState("todos");
  const [filterTipo, setFilterTipo] = useState("todos");
  const [isLoading, setIsLoading] = useState(true);
  const [clientes, setClientes] = useState<Cliente[]>([]);
  const [clientesFiltrados, setClientesFiltrados] = useState<Cliente[]>([]);
  const [estatisticas, setEstatisticas] = useState(estatisticasGerais);

  // Hook para filtrar dados baseado em permissões
  const { filteredData: clientesComPermissao, loading: loadingPermissions } = useFilteredData(clientes, 'clientes');

  // Novos estados para as funcionalidades adicionadas
  const [viewMode, setViewMode] = useState<"table" | "card">("table");
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);
  const [filtrosAvancados, setFiltrosAvancados] = useState<ClientesFiltros>({
    status: [],
    tipo: [],
  });

  // MOVED columns definition here and wrapped in useMemo
  const columns: ColumnDef<Cliente>[] = useMemo(() => [
    {
      id: "nome",
      header: "Nome",
      accessorKey: "nome",
      cell: ({ row }) => (
        <div className="flex items-center gap-3">
          <Avatar className="h-8 w-8">
            <AvatarImage src={`/avatars/${row.original.nome.toLowerCase().replace(' ', '_')}.jpg`} />
            <AvatarFallback>{row.original.nome.split(' ').map(n => n[0]).join('')}</AvatarFallback>
          </Avatar>
          <div>
            <div className="font-medium">{row.getValue("nome")}</div>
            <div className="text-sm text-muted-foreground">{row.original.documento}</div>
          </div>
        </div>
      ),
    },
    {
      id: "tipo",
      header: "Tipo",
      accessorKey: "tipo",
      cell: ({ row }) => (
        <Badge variant="outline">
          {row.getValue("tipo") === "pessoa_fisica" ? "Pessoa Física" : "Pessoa Jurídica"}
        </Badge>
      ),
    },
    {
      id: "contato",
      header: "Contato",
      cell: ({ row }) => (
        <div className="flex flex-col">
          <div className="flex items-center gap-1">
            <Mail className="h-3 w-3 text-muted-foreground" />
            <span className="text-sm">{row.original.email}</span>
          </div>
          <div className="flex items-center gap-1">
            <Phone className="h-3 w-3 text-muted-foreground" />
            <span className="text-sm">{row.original.telefone}</span>
          </div>
        </div>
      ),
    },
    {
      id: "status",
      header: "Status",
      accessorKey: "status",
      cell: ({ row }) => {
        const status = row.getValue("status") as string;
        return (
          <Badge
            className={
              status === "ativo"
                ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                : status === "inativo"
                ? "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
                : "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400"
            }
          >
            {status === "ativo"
              ? "Ativo"
              : status === "inativo"
              ? "Inativo"
              : "Pendente"}
          </Badge>
        );
      },
    },
    {
      id: "precatorios",
      header: "Precatórios",
      accessorKey: "precatorios",
      cell: ({ row }) => (
        <div className="flex items-center gap-1">
          <FileText className="h-4 w-4 text-muted-foreground" />
          <span>{row.original.precatorios || 0}</span>
        </div>
      ),
    },
    {
      id: "valorTotal",
      header: "Valor Total",
      accessorKey: "valorTotal",
      cell: ({ row }) => (
        <div className="flex items-center gap-1">
          <CircleDollarSign className="h-4 w-4 text-muted-foreground" />
          <span>
            {new Intl.NumberFormat('pt-BR', {
              style: 'currency',
              currency: 'BRL'
            }).format(row.original.valorTotal || 0)}
          </span>
        </div>
      ),
    },
    {
      id: "acoes",
      header: "",
      cell: ({ row }) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon">
              <MoreHorizontal className="h-4 w-4" />
              <span className="sr-only">Abrir menu</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <PermissionSection action="view" resource="cliente" resourceId={row.original.id}>
              <DropdownMenuItem onClick={() => navigate(`/customers/${row.original.id}`)}>
                <ArrowUpRight className="mr-2 h-4 w-4" />
                <span>Ver detalhes</span>
              </DropdownMenuItem>
            </PermissionSection>
            <PermissionSection action="edit" resource="cliente" resourceId={row.original.id}>
              <DropdownMenuItem>
                <Download className="mr-2 h-4 w-4" />
                <span>Exportar dados</span>
              </DropdownMenuItem>
            </PermissionSection>
            <PermissionSection action="create" resource="precatorio">
              <DropdownMenuItem>
                <Upload className="mr-2 h-4 w-4" />
                <span>Importar precatórios</span>
              </DropdownMenuItem>
            </PermissionSection>
          </DropdownMenuContent>
        </DropdownMenu>
      ),
    },
  ], [navigate]); // Added navigate as dependency

  const columnOrder = columns.map((column) => column.id as string); // Moved after columns definition

  // Função para carregar os clientes do banco de dados
  const carregarClientes = useCallback(async (showToast = true) => {
    try {
      setIsLoading(true);
      console.log("Customers: Carregando clientes do banco de dados...");

      // Limpar todos os caches relacionados a clientes para garantir dados atualizados
      clearCache('clientes');
      clearCache('view_clientes_com_totais');
      clearSupabaseCache('clientes');

      // Forçar uma pequena pausa para garantir que os caches foram limpos
      await new Promise(resolve => setTimeout(resolve, 100));

      // Usar função do serviço para buscar clientes
      const clientesData = await buscarClientes();
      console.log("Customers: Clientes carregados:", clientesData?.length || 0);

      if (clientesData && Array.isArray(clientesData)) {
        // Converter para o formato usado pelo componente
        const clientesConvertidos = clientesData.map(converterParaCliente);
        console.log("Customers: Clientes convertidos:", clientesConvertidos.length);

        setClientes(clientesConvertidos);
        setClientesFiltrados(clientesConvertidos);
        setCurrentPage(1); // Resetar para a primeira página ao carregar novos dados

        // Calcular estatísticas
        const estatisticasCalculadas = {
          total: clientesConvertidos.length,
          ativos: clientesConvertidos.filter(c => c.status === 'ativo').length,
          inativos: clientesConvertidos.filter(c => c.status === 'inativo').length,
          pendentes: clientesConvertidos.filter(c => c.status === 'pendente').length,
          valorTotal: clientesConvertidos.reduce((sum, c) => sum + (c.valorTotal || 0), 0),
          precatoriosTotal: clientesConvertidos.reduce((sum, c) => sum + (c.precatorios || 0), 0),
          precatoriosConcluidos: Math.floor(Math.random() * 100), // Mockado por enquanto
          precatoriosEmAndamento: Math.floor(Math.random() * 100), // Mockado por enquanto
        };

        setEstatisticas(estatisticasCalculadas);

        if (showToast) {
          toast.success(`${clientesConvertidos.length} clientes carregados`);
        }
      } else {
        console.error("Customers: Formato de dados inválido:", clientesData);
        toast.error("Erro ao carregar clientes: formato de dados inválido");
      }
    } catch (error) {
      console.error("Customers: Erro ao carregar clientes:", error);
      toast.error("Erro ao carregar clientes. Tente novamente mais tarde.");

      // Tentar novamente após um breve atraso
      setTimeout(() => {
        console.log("Customers: Tentando carregar clientes novamente após erro");
        setIsLoading(true);
        buscarClientes()
          .then(clientesData => {
            if (clientesData && Array.isArray(clientesData)) {
              const clientesConvertidos = clientesData.map(converterParaCliente);
              setClientes(clientesConvertidos);
              setClientesFiltrados(clientesConvertidos);
              console.log("Customers: Segunda tentativa concluída com sucesso");
            }
          })
          .catch(err => {
            console.error("Erro na segunda tentativa de carregar clientes:", err);
          })
          .finally(() => {
            setIsLoading(false);
          });
      }, 1500);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Função para aplicar filtros avançados
  const aplicarFiltrosAvancados = async (filtros: ClientesFiltros) => {
    setFiltrosAvancados(filtros);
    setIsLoading(true);

    try {
      // Converter ClientesFiltros para FiltrosAvancados
      const filtrosParaAPI: FiltrosAvancados = {
        status: filtros.status,
        tipo: filtros.tipo,
        valorMin: filtros.valorMin,
        valorMax: filtros.valorMax,
        dataInicio: filtros.dataInicio,
        dataFim: filtros.dataFim,
        precatoriosMin: filtros.precatoriosMin,
        precatoriosMax: filtros.precatoriosMax
      };

      // Buscar clientes com filtros avançados no servidor
      const clientesData = await buscarClientesComFiltrosAvancados(filtrosParaAPI);

      if (clientesData && Array.isArray(clientesData)) {
        // Converter para o formato usado pelo componente
        const clientesConvertidos = clientesData.map(converterParaCliente);

        setClientesFiltrados(clientesConvertidos);
        setCurrentPage(1); // Resetar para a primeira página ao aplicar filtros

        // Atualizar estatísticas com base nos resultados filtrados
        const estatisticasCalculadas = {
          total: clientesConvertidos.length,
          ativos: clientesConvertidos.filter(c => c.status === 'ativo').length,
          inativos: clientesConvertidos.filter(c => c.status === 'inativo').length,
          pendentes: clientesConvertidos.filter(c => c.status === 'pendente').length,
          valorTotal: clientesConvertidos.reduce((sum, c) => sum + (c.valorTotal || 0), 0),
          precatoriosTotal: clientesConvertidos.reduce((sum, c) => sum + (c.precatorios || 0), 0),
          precatoriosConcluidos: Math.floor(Math.random() * 100), // Mockado por enquanto
          precatoriosEmAndamento: Math.floor(Math.random() * 100), // Mockado por enquanto
        };

        setEstatisticas(estatisticasCalculadas);
        toast.success(`${clientesConvertidos.length} clientes encontrados`);
      } else {
        console.error("Formato de dados inválido:", clientesData);
        toast.error("Erro ao aplicar filtros: formato de dados inválido");
      }
    } catch (error) {
      console.error("Erro ao aplicar filtros:", error);
      toast.error("Erro ao aplicar filtros. Tente novamente.");
    } finally {
      setIsLoading(false);
    }
  };

  // Função para limpar todos os filtros
  const limparFiltros = () => {
    setFiltrosAvancados({
      status: [],
      tipo: [],
    });
    setFilterStatus("todos");
    setFilterTipo("todos");
    setSearchTerm("");
    setClientesFiltrados(clientes);
    setCurrentPage(1);
    toast.success("Filtros limpos");
  };

  // Usar o hook de visibilidade para recarregar dados quando a página voltar a ficar visível
  const { isVisible } = useVisibilityChange(
    // Função para recarregar dados sem mostrar toast
    () => carregarClientes(false),
    {
      clearCacheOnReturn: true,
      refreshSessionOnReturn: true,
      minTimeSinceLastVisible: 30000 // 30 segundos
    }
  );

  // Não precisamos mais de um listener específico para o evento de sessão atualizada
  // pois o hook useVisibilityChange já cuida disso

  // Carregar clientes quando o componente montar
  useEffect(() => {
    console.log("Customers: Componente montado, carregando dados iniciais");
    carregarClientes();
  }, [carregarClientes]);

  // Filtrar clientes quando os filtros mudarem
  useEffect(() => {
    const filtrarClientes = () => {
      // Usar dados filtrados por permissões como base
      let clientesFiltrados = [...clientesComPermissao];

      // Filtrar por termo de busca
      if (searchTerm) {
        const termLower = searchTerm.toLowerCase();
        clientesFiltrados = clientesFiltrados.filter(
          cliente =>
            cliente.nome.toLowerCase().includes(termLower) ||
            cliente.email.toLowerCase().includes(termLower) ||
            cliente.telefone.includes(termLower) ||
            cliente.documento.includes(termLower)
        );
      }

      // Filtrar por status
      if (filterStatus !== "todos") {
        clientesFiltrados = clientesFiltrados.filter(
          cliente => cliente.status === filterStatus
        );
      }

      // Filtrar por tipo
      if (filterTipo !== "todos") {
        clientesFiltrados = clientesFiltrados.filter(
          cliente => cliente.tipo === filterTipo
        );
      }

      setClientesFiltrados(clientesFiltrados);
      setCurrentPage(1); // Resetar para a primeira página ao filtrar
    };

    filtrarClientes();
  }, [clientesComPermissao, searchTerm, filterStatus, filterTipo]);

  // Calcular dados paginados
  const paginatedData = useMemo(() => {
    const firstPageIndex = (currentPage - 1) * pageSize;
    const lastPageIndex = firstPageIndex + pageSize;
    return clientesFiltrados.slice(firstPageIndex, lastPageIndex);
  }, [clientesFiltrados, currentPage, pageSize]);

  // Calcular total de páginas
  const totalPages = useMemo(() => {
    return Math.ceil(clientesFiltrados.length / pageSize);
  }, [clientesFiltrados.length, pageSize]);

  // Função para mudar de página
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Função para mudar o tamanho da página
  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1); // Resetar para a primeira página ao mudar o tamanho
  };

  // Função para alternar entre visualização de tabela e cards
  const handleViewModeChange = (mode: "table" | "card") => {
    setViewMode(mode);
  };

  // Função para abrir o modal de filtros avançados
  const handleOpenFilterModal = () => {
    setIsFilterModalOpen(true);
  };

  const handleClientClick = (clientId: string) => {
    navigate(`/customers/${clientId}`);
  };

  const table = useReactTable({
    data: paginatedData,
    columns,
    state: { sorting },
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  return (
    <div className="flex flex-col h-screen w-screen overflow-hidden">
      <TopNav
        title="Clientes"
        icon={<Users className="h-6 w-6 text-primary" />}
      />

      <div className="flex flex-col gap-6 p-6 pt-[65px] overflow-auto">
        {/* Header */}
        <div className="flex flex-col gap-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-muted-foreground">
                Gerencie os clientes e seus precatórios
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                className="gap-2"
                onClick={handleOpenFilterModal}
              >
                <SlidersHorizontal size={16} />
                Filtros Avançados
              </Button>
              <Button
                variant="outline"
                className="gap-2"
                onClick={() => carregarClientes(true)}
              >
                <Filter size={16} />
                Atualizar
              </Button>
              <PermissionButton
                action="create"
                resource="cliente"
                className="gap-2"
                onClick={() => navigate('/customers/new')}
                tooltipMessage="Você não tem permissão para criar novos clientes."
              >
                <UserPlus size={16} />
                Novo Cliente
              </PermissionButton>
            </div>
          </div>

          {/* Estatísticas */}
          <ClientsStatistics estatisticas={estatisticas} className="mb-2" />
        </div>

        {/* Filtros e Pesquisa */}
        <div className="flex flex-wrap items-center justify-between gap-4">
          <div className="flex flex-wrap items-center gap-4">
            <div className="relative w-full max-w-sm">
              <Input
                type="search"
                placeholder="Buscar clientes..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            </div>

            <Select
              value={filterStatus}
              onValueChange={setFilterStatus}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="todos">Todos os status</SelectItem>
                <SelectItem value="ativo">Ativos</SelectItem>
                <SelectItem value="inativo">Inativos</SelectItem>
                <SelectItem value="pendente">Pendentes</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={filterTipo}
              onValueChange={setFilterTipo}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Tipo" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="todos">Todos os tipos</SelectItem>
                <SelectItem value="pessoa_fisica">Pessoa Física</SelectItem>
                <SelectItem value="pessoa_juridica">Pessoa Jurídica</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <ViewToggle viewMode={viewMode} onViewModeChange={handleViewModeChange} />
        </div>

        {/* Conteúdo Principal (Tabela ou Cards) */}
        <Card>
          <CardContent className="p-6">
            <div>
              {isLoading ? (
                <CardLoading text="Carregando clientes..." description="Aguarde enquanto buscamos os dados" />
              ) : clientesFiltrados.length === 0 ? (
                <div className="flex flex-col items-center justify-center py-8 text-center">
                  <AlertCircle className="h-8 w-8 text-muted-foreground mb-2" />
                  <h3 className="text-lg font-medium">Nenhum cliente encontrado</h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    Tente ajustar os filtros ou adicione um novo cliente.
                  </p>
                </div>
              ) : viewMode === "table" ? (
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        {columnOrder.map((columnId) => {
                          const column = table.getColumn(columnId);
                          if (!column) return null;

                          return (
                            <TableHead
                              key={columnId}
                              className="relative h-10 border-t before:absolute before:inset-y-0 before:start-0 before:w-px before:bg-border first:before:bg-transparent"
                            >
                              <TableColumnHeader
                                column={column}
                                title={typeof column.columnDef.header === 'string' ? column.columnDef.header : columnId}
                              />
                            </TableHead>
                          );
                        })}
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {table.getRowModel().rows.length > 0 ? (
                        table.getRowModel().rows.map((row) => (
                          <TableRow
                            key={row.id}
                            className="cursor-pointer"
                            onClick={() => handleClientClick(row.original.id)}
                          >
                            {columnOrder.map((columnId) => {
                              const cell = row.getAllCells().find(c => c.column.id === columnId);
                              if (!cell) return null;

                              return (
                                <TableCell key={cell.id}>
                                  {flexRender(
                                    cell.column.columnDef.cell,
                                    cell.getContext()
                                  )}
                                </TableCell>
                              );
                            })}
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={columns.length} className="h-24 text-center">
                            Nenhum resultado encontrado.
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {paginatedData.map((cliente) => (
                    <ClientCard
                      key={cliente.id}
                      cliente={cliente}
                      onClick={handleClientClick}
                    />
                  ))}
                </div>
              )}

              {/* Paginação */}
              {clientesFiltrados.length > 0 && (
                <ClientsPagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  pageSize={pageSize}
                  totalItems={clientesFiltrados.length}
                  onPageChange={handlePageChange}
                  onPageSizeChange={handlePageSizeChange}
                />
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Modal de Filtros Avançados */}
      <FilterModal
        isOpen={isFilterModalOpen}
        onOpenChange={setIsFilterModalOpen}
        filtros={filtrosAvancados}
        onAplicarFiltros={aplicarFiltrosAvancados}
        onLimparFiltros={limparFiltros}
        estatisticas={estatisticas}
      />
    </div>
  );
}
