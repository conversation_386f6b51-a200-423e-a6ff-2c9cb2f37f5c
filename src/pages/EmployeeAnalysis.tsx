import { 
  Users, 
  UserPlus, 
  Clock, 
  Target, 
  TrendingUp, 
  Filter, 
  Search,
  MoreVertical,
  ArrowUpRight,
  ArrowDownRight,
  Calendar,
  Mail,
  Phone,
  Building2,
  Briefcase,
  Star
} from "lucide-react";
import { motion } from "framer-motion";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";
import { AreaChart, Area, XAxis, YAxis, ResponsiveContainer, LineChart, Line, Tooltip, Legend, BarChart, Bar, CartesianGrid } from 'recharts';
import { useNavigate } from "react-router-dom";
import { Progress } from "@/components/ui/progress";

const performanceData = [
  { month: 'Jan', produtividade: 85, presenca: 92 },
  { month: 'Fev', produtividade: 88, presenca: 94 },
  { month: 'Mar', produtividade: 92, presenca: 91 },
  { month: 'Abr', produtividade: 90, presenca: 93 },
  { month: 'Mai', produtividade: 95, presenca: 95 },
  { month: 'Jun', produtividade: 93, presenca: 94 },
];

const employeeStats = [
  {
    title: "Total de Funcionários",
    value: "24",
    change: "+2",
    trend: "up",
    icon: <Users className="w-4 h-4" />,
    description: "Funcionários ativos"
  },
  {
    title: "Produtividade Média",
    value: "92%",
    change: "+5%",
    trend: "up",
    icon: <Target className="w-4 h-4" />,
    description: "Últimos 30 dias"
  },
  {
    title: "Taxa de Presença",
    value: "95%",
    change: "-2%",
    trend: "down",
    icon: <Clock className="w-4 h-4" />,
    description: "Média mensal"
  },
  {
    title: "Satisfação",
    value: "4.8",
    change: "+0.3",
    trend: "up",
    icon: <TrendingUp className="w-4 h-4" />,
    description: "Avaliação geral"
  }
];

const funcionarios = [
  {
    id: 1,
    nome: "Ana Silva",
    cargo: "Advogada Sênior",
    departamento: "Cível",
    email: "<EMAIL>",
    telefone: "(11) 99999-1111",
    avatar: "/avatars/ana.jpg",
    precatorios: 45,
    produtividade: 95,
    avaliacao: 4.8,
    proximaAudiencia: "20/03/2024",
    status: "Ativo",
    indicadores: {
      precatoriosFinalizados: 38,
      precatoriosEmAndamento: 7,
      prazosMedios: 30,
      clientesSatisfeitos: 92
    }
  },
  {
    id: 2,
    nome: "Carlos Santos",
    cargo: "Advogado Pleno",
    departamento: "Trabalhista",
    email: "<EMAIL>",
    telefone: "(11) 99999-2222",
    avatar: "/avatars/carlos.jpg",
    precatorios: 32,
    produtividade: 88,
    avaliacao: 4.5,
    proximaAudiencia: "22/03/2024",
    status: "Ativo",
    indicadores: {
      precatoriosFinalizados: 25,
      precatoriosEmAndamento: 7,
      prazosMedios: 28,
      clientesSatisfeitos: 88
    }
  },
  {
    id: 3,
    nome: "Maria Costa",
    cargo: "Advogada Júnior",
    departamento: "Tributário",
    email: "<EMAIL>",
    telefone: "(11) 99999-3333",
    avatar: "/avatars/maria.jpg",
    precatorios: 28,
    produtividade: 82,
    avaliacao: 4.2,
    proximaAudiencia: "25/03/2024",
    status: "Férias",
    indicadores: {
      precatoriosFinalizados: 20,
      precatoriosEmAndamento: 8,
      prazosMedios: 35,
      clientesSatisfeitos: 85
    }
  }
];

const desempenhoMensal = [
  { mes: 'Jan', precatorios: 85, produtividade: 92, audiencias: 45 },
  { mes: 'Fev', precatorios: 88, produtividade: 94, audiencias: 52 },
  { mes: 'Mar', precatorios: 92, produtividade: 91, audiencias: 48 },
  { mes: 'Abr', precatorios: 90, produtividade: 93, audiencias: 50 },
  { mes: 'Mai', precatorios: 95, produtividade: 95, audiencias: 55 },
  { mes: 'Jun', precatorios: 93, produtividade: 94, audiencias: 49 },
];

const desempenhoPorDepartamento = [
  { departamento: 'Cível', produtividade: 92, precatorios: 150, funcionarios: 8 },
  { departamento: 'Trabalhista', produtividade: 88, precatorios: 120, funcionarios: 6 },
  { departamento: 'Tributário', produtividade: 90, precatorios: 100, funcionarios: 5 },
  { departamento: 'Comercial', produtividade: 85, precatorios: 80, funcionarios: 4 },
];

const indicadoresGerais = [
  {
    titulo: "Total de Funcionários",
    valor: "23",
    variacao: "+2",
    tendencia: "up",
    descricao: "Ativos no momento"
  },
  {
    titulo: "Média de Precatórios",
    valor: "38",
    variacao: "+5",
    tendencia: "up",
    descricao: "Por funcionário"
  },
  {
    titulo: "Taxa de Sucesso",
    valor: "92%",
    variacao: "+3%",
    tendencia: "up",
    descricao: "Precatórios finalizados"
  },
  {
    titulo: "Satisfação Geral",
    valor: "4.8",
    variacao: "+0.2",
    tendencia: "up",
    descricao: "Avaliação média"
  }
];

const EmployeeAnalysis = () => {
  const navigate = useNavigate();

  const handleEmployeeClick = (id: number) => {
    navigate(`/employees/${id}`);
  };

  return (
    <div className="flex flex-1">
      <div className="p-2 md:p-10 rounded-tl-2xl border border-neutral-200 dark:border-neutral-700 bg-gradient-to-br from-neutral-50 to-neutral-100 dark:from-neutral-900 dark:to-black flex flex-col gap-6 flex-1 w-full h-full">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-neutral-800 to-neutral-600 dark:from-neutral-200 dark:to-neutral-400">
              Funcionários
            </h1>
            <p className="text-neutral-600 dark:text-neutral-400">
              Análise e gestão da equipe
            </p>
          </div>

          <div className="flex flex-col md:flex-row gap-3 w-full md:w-auto">
            <div className="relative flex-1 md:w-64">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-500 w-4 h-4" />
              <Input 
                placeholder="Buscar funcionário..." 
                className="pl-10 bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm"
              />
            </div>
            <Button variant="outline" size="sm" className="gap-2 bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm">
              <Filter className="w-4 h-4" />
              Filtros
            </Button>
          </div>
        </div>

        <div className="space-y-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {indicadoresGerais.map((indicador, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card className="border-neutral-200 dark:border-neutral-700 bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm">
                  <CardContent className="pt-6">
                    <div className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <p className="text-sm font-medium text-neutral-600 dark:text-neutral-400">{indicador.titulo}</p>
                      <Badge 
                        variant={indicador.tendencia === "up" ? "default" : "destructive"}
                        className="flex items-center gap-1"
                      >
                        {indicador.tendencia === "up" ? (
                          <ArrowUpRight className="w-3 h-3" />
                        ) : (
                          <ArrowDownRight className="w-3 h-3" />
                        )}
                        {indicador.variacao}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-4">
                      <p className="text-2xl font-bold">{indicador.valor}</p>
                      <p className="text-xs text-neutral-500">{indicador.descricao}</p>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>

          <div className="grid gap-4 grid-cols-1 lg:grid-cols-2">
            <Card className="border-neutral-200 dark:border-neutral-700 bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-lg">Desempenho Mensal</CardTitle>
                <CardDescription>Evolução dos principais indicadores</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart data={desempenhoMensal} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
                      <defs>
                        <linearGradient id="colorPrecatorios" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="5%" stopColor="#64748b" stopOpacity={0.8}/>
                          <stop offset="95%" stopColor="#64748b" stopOpacity={0}/>
                        </linearGradient>
                        <linearGradient id="colorProdutividade" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="5%" stopColor="#94a3b8" stopOpacity={0.8}/>
                          <stop offset="95%" stopColor="#94a3b8" stopOpacity={0}/>
                        </linearGradient>
                      </defs>
                      <CartesianGrid strokeDasharray="3 3" className="stroke-neutral-200/50 dark:stroke-neutral-700/50" />
                      <XAxis 
                        dataKey="mes" 
                        stroke="#888888"
                        fontSize={12}
                        tickLine={false}
                        axisLine={false}
                      />
                      <YAxis 
                        stroke="#888888"
                        fontSize={12}
                        tickLine={false}
                        axisLine={false}
                        tickFormatter={(value) => `${value}%`}
                      />
                      <Tooltip 
                        contentStyle={{ 
                          backgroundColor: "rgba(255, 255, 255, 0.8)",
                          borderColor: "rgba(0, 0, 0, 0.1)",
                          borderRadius: "8px",
                          backdropFilter: "blur(8px)"
                        }}
                      />
                      <Legend />
                      <Area 
                        type="monotone" 
                        dataKey="precatorios" 
                        stroke="#64748b" 
                        fillOpacity={1} 
                        fill="url(#colorPrecatorios)" 
                        name="Precatórios"
                      />
                      <Area 
                        type="monotone" 
                        dataKey="produtividade" 
                        stroke="#94a3b8" 
                        fillOpacity={1} 
                        fill="url(#colorProdutividade)" 
                        name="Produtividade"
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card className="border-neutral-200 dark:border-neutral-700 bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-lg">Análise por Departamento</CardTitle>
                <CardDescription>Comparativo de produtividade e precatórios</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart 
                      data={desempenhoPorDepartamento}
                      margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                      barGap={8}
                      barSize={32}
                    >
                      <CartesianGrid strokeDasharray="3 3" className="stroke-neutral-200/50 dark:stroke-neutral-700/50" />
                      <XAxis 
                        dataKey="departamento" 
                        stroke="#888888"
                        fontSize={12}
                        tickLine={false}
                        axisLine={false}
                      />
                      <YAxis 
                        stroke="#888888"
                        fontSize={12}
                        tickLine={false}
                        axisLine={false}
                      />
                      <Tooltip 
                        cursor={{ 
                          fill: 'transparent',
                          stroke: '#64748b',
                          strokeWidth: 2,
                          radius: 4
                        }}
                        contentStyle={{ 
                          backgroundColor: "rgba(255, 255, 255, 0.8)",
                          borderColor: "rgba(0, 0, 0, 0.1)",
                          borderRadius: "8px",
                          backdropFilter: "blur(8px)"
                        }}
                      />
                      <Legend />
                      <Bar 
                        dataKey="produtividade" 
                        fill="#64748b" 
                        name="Produtividade"
                        radius={[4, 4, 0, 0]}
                        activeBar={{ 
                          stroke: '#64748b',
                          strokeWidth: 2,
                          fill: '#64748b'
                        }}
                      />
                      <Bar 
                        dataKey="precatorios" 
                        fill="#94a3b8" 
                        name="Precatórios"
                        radius={[4, 4, 0, 0]}
                        activeBar={{ 
                          stroke: '#94a3b8',
                          strokeWidth: 2,
                          fill: '#94a3b8'
                        }}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
          {funcionarios.map((funcionario) => (
            <motion.div
              key={funcionario.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              whileHover={{ scale: 1.02 }}
              onClick={() => handleEmployeeClick(funcionario.id)}
              className="cursor-pointer"
            >
              <Card className="border-neutral-200 dark:border-neutral-700 bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm hover:shadow-lg transition-shadow">
                <CardHeader className="pb-2">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-4">
                      <Avatar className="h-12 w-12">
                        <AvatarImage src={funcionario.avatar} />
                        <AvatarFallback>{funcionario.nome.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                      </Avatar>
                      <div>
                        <CardTitle className="text-lg flex items-center gap-2">
                          {funcionario.nome}
                          <ArrowUpRight className="w-4 h-4 text-neutral-400" />
                        </CardTitle>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge variant="secondary">{funcionario.cargo}</Badge>
                          <Badge 
                            variant={funcionario.status === "Ativo" ? "default" : "outline"}
                            className="text-xs"
                          >
                            {funcionario.status}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4 mt-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <div className="flex items-center gap-2 text-sm text-neutral-500">
                          <Building2 className="w-4 h-4" />
                          <span>{funcionario.departamento}</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm text-neutral-500">
                          <Mail className="w-4 h-4" />
                          <span>{funcionario.email}</span>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <div className="flex items-center gap-2 text-sm text-neutral-500">
                          <Phone className="w-4 h-4" />
                          <span>{funcionario.telefone}</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm text-neutral-500">
                          <Calendar className="w-4 h-4" />
                          <span>Próx. Audiência: {funcionario.proximaAudiencia}</span>
                        </div>
                      </div>
                    </div>

                    <div className="pt-4 space-y-4">
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm text-neutral-500 flex items-center gap-2">
                            <Briefcase className="w-4 h-4" />
                            Precatórios Ativos
                          </span>
                          <span className="text-sm font-medium">{funcionario.precatorios}</span>
                        </div>
                        <Progress value={funcionario.produtividade} className="h-2" />
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div className="p-3 rounded-lg bg-neutral-50 dark:bg-neutral-900">
                          <div className="flex items-center gap-2">
                            <Star className="w-4 h-4 text-yellow-500" />
                            <span className="text-sm font-medium">{funcionario.avaliacao}</span>
                          </div>
                          <p className="text-xs text-neutral-500 mt-1">Avaliação</p>
                        </div>
                        <div className="p-3 rounded-lg bg-neutral-50 dark:bg-neutral-900">
                          <div className="flex items-center gap-2">
                            <TrendingUp className="w-4 h-4 text-green-500" />
                            <span className="text-sm font-medium">{funcionario.produtividade}%</span>
                          </div>
                          <p className="text-xs text-neutral-500 mt-1">Produtividade</p>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4 text-xs">
                        <div className="flex items-center gap-2">
                          <Target className="w-3 h-3 text-blue-500" />
                          <span className="text-neutral-500">
                            {funcionario.indicadores.precatoriosFinalizados} precatórios finalizados
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Clock className="w-3 h-3 text-purple-500" />
                          <span className="text-neutral-500">
                            {funcionario.indicadores.prazosMedios} dias em média
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default EmployeeAnalysis;