import { useState, useCallback, useRef, useEffect, useMemo } from 'react';
import ReactFlow, {
  Controls,
  Background,
  MiniMap,
  Panel,
  useNodesState,
  useEdgesState,
  addEdge,
  Connection,
  Edge,
  Node,
  MarkerType,
  Handle,
  Position,
  ConnectionMode,
  useReactFlow,
  ReactFlowProvider,
  EdgeProps,
} from 'reactflow';
import 'reactflow/dist/style.css';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from "@/components/ui/separator";
import { Bell, CheckCircle2, CircleDot, FileCheck, GitBranch, Loader2, MessageSquare, Plug, Trash2, Plus, Minus, Maximize2, Lock, Unlock, AlignVerticalJustifyCenter, AlignHorizontalJustifyCenter, Grid } from 'lucide-react';
import { cn } from '@/lib/utils';

// Definição do EdgeWithDelete no escopo global
function EdgeWithDelete({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  style = {},
  markerEnd,
  data,
}: EdgeProps) {
  const edgePathRef = useRef<SVGPathElement>(null);
  const [edgeCenterX, setEdgeCenterX] = useState(0);
  const [edgeCenterY, setEdgeCenterY] = useState(0);

  useEffect(() => {
    if (edgePathRef.current) {
      const pathLength = edgePathRef.current.getTotalLength();
      const center = edgePathRef.current.getPointAtLength(pathLength / 2);
      setEdgeCenterX(center.x - 20);
      setEdgeCenterY(center.y - 20);
    }
  }, [sourceX, sourceY, targetX, targetY]);

  const onEdgeClick = (evt: React.MouseEvent, id: string) => {
    evt.stopPropagation();
    const event = new CustomEvent('DELETE_EDGE', { detail: { id } });
    window.dispatchEvent(event);
  };

  return (
    <>
      <path
        ref={edgePathRef}
        d={`M ${sourceX} ${sourceY} C ${sourceX} ${(sourceY + targetY) / 2}, ${targetX} ${(sourceY + targetY) / 2}, ${targetX} ${targetY}`}
        className="react-flow__edge-path"
        style={style}
        markerEnd={markerEnd}
      />
      <foreignObject
        width={40}
        height={40}
        x={edgeCenterX}
        y={edgeCenterY}
        className="edge-delete-button opacity-0 hover:opacity-100 transition-opacity"
        requiredExtensions="http://www.w3.org/1999/xhtml"
      >
        <div className="flex items-center justify-center w-10 h-10 bg-white dark:bg-neutral-800 rounded-full border-2 border-neutral-200 dark:border-neutral-700 cursor-pointer hover:bg-red-50 dark:hover:bg-red-900 hover:border-red-500 dark:hover:border-red-500"
             onClick={(evt) => onEdgeClick(evt, id)}
        >
          <Trash2 className="w-5 h-5 text-red-600" />
        </div>
      </foreignObject>
    </>
  );
}

// Tipos de nós personalizados
const nodeTypes = {
  tarefa: TarefaNode,
  condicao: CondicaoNode,
  inicioFim: InicioFimNode,
  aprovacao: AprovacaoNode,
  notificacao: NotificacaoNode,
  integracao: IntegracaoNode,
} as const;

// Tipos de edges personalizados
const edgeTypes = {
  default: EdgeWithDelete,
} as const;

// Componentes dos nós
function TarefaNode({ data }: any) {
  return (
    <div className="p-4 rounded-lg border-2 node-tarefa min-w-[200px]">
      <Handle type="target" position={Position.Top} className="w-3 h-3 !bg-blue-500" id="target" />
      <div className="flex items-center gap-2">
        <FileCheck className="w-5 h-5 text-blue-600" />
        <div className="font-semibold">{data.label}</div>
      </div>
      <div className="mt-2 text-sm text-neutral-600 dark:text-neutral-400">{data.description}</div>
      <div className="mt-2 text-xs">
        <span className="font-medium">Prazo:</span> {data.deadline}
      </div>
      <div className="mt-1 text-xs">
        <span className="font-medium">Responsável:</span> {data.responsible}
      </div>
      <Handle type="source" position={Position.Bottom} className="w-3 h-3 !bg-blue-500" id="source" />
    </div>
  );
}

function CondicaoNode({ data }: any) {
  return (
    <div className="p-4 diamond-node node-condicao border-2 min-w-[180px] min-h-[180px] flex items-center justify-center text-center">
      <Handle type="target" position={Position.Top} className="w-3 h-3 !bg-amber-500" id="target" />
      <div>
        <GitBranch className="w-5 h-5 text-amber-600 mx-auto mb-2" />
        <div className="font-semibold">{data.label}</div>
        <div className="mt-2 text-sm text-neutral-600 dark:text-neutral-400">{data.description}</div>
      </div>
      <Handle type="source" position={Position.Bottom} className="w-3 h-3 !bg-amber-500" id="source-bottom" />
      <Handle type="source" position={Position.Left} className="w-3 h-3 !bg-amber-500" id="source-left" />
      <Handle type="source" position={Position.Right} className="w-3 h-3 !bg-amber-500" id="source-right" />
    </div>
  );
}

function InicioFimNode({ data }: any) {
  return (
    <div className="p-4 rounded-full node-inicioFim border-2 min-w-[150px] text-center">
      <Handle type="target" position={Position.Top} className="w-3 h-3 !bg-green-500" id="target" />
      <CircleDot className="w-5 h-5 text-green-600 mx-auto mb-2" />
      <div className="font-semibold">{data.label}</div>
      <Handle type="source" position={Position.Bottom} className="w-3 h-3 !bg-green-500" id="source" />
    </div>
  );
}

function AprovacaoNode({ data }: any) {
  return (
    <div className="p-4 rounded-lg border-2 node-aprovacao min-w-[200px]">
      <Handle type="target" position={Position.Top} className="w-3 h-3 !bg-purple-500" id="target" />
      <div className="flex items-center gap-2">
        <CheckCircle2 className="w-5 h-5 text-purple-600" />
        <div className="font-semibold">{data.label}</div>
      </div>
      <div className="mt-2 text-sm text-neutral-600 dark:text-neutral-400">{data.description}</div>
      <div className="mt-2 text-xs">
        <span className="font-medium">Aprovador:</span> {data.approver}
      </div>
      <Handle type="source" position={Position.Bottom} className="w-3 h-3 !bg-purple-500" id="source" />
    </div>
  );
}

function NotificacaoNode({ data }: any) {
  return (
    <div className="p-4 rounded-lg border-2 node-notificacao min-w-[200px]">
      <Handle type="target" position={Position.Top} className="w-3 h-3 !bg-pink-500" id="target" />
      <div className="flex items-center gap-2">
        <Bell className="w-5 h-5 text-pink-600" />
        <div className="font-semibold">{data.label}</div>
      </div>
      <div className="mt-2 text-sm text-neutral-600 dark:text-neutral-400">{data.description}</div>
      <div className="mt-2 text-xs">
        <span className="font-medium">Destinatários:</span> {data.recipients}
      </div>
      <Handle type="source" position={Position.Bottom} className="w-3 h-3 !bg-pink-500" id="source" />
    </div>
  );
}

function IntegracaoNode({ data }: any) {
  return (
    <div className="p-4 rounded-lg border-2 node-integracao min-w-[200px]">
      <Handle type="target" position={Position.Top} className="w-3 h-3 !bg-cyan-500" id="target" />
      <div className="flex items-center gap-2">
        <Plug className="w-5 h-5 text-cyan-600" />
        <div className="font-semibold">{data.label}</div>
      </div>
      <div className="mt-2 text-sm text-neutral-600 dark:text-neutral-400">{data.description}</div>
      <div className="mt-2 text-xs">
        <span className="font-medium">Sistema:</span> {data.system}
      </div>
      <Handle type="source" position={Position.Bottom} className="w-3 h-3 !bg-cyan-500" id="source" />
    </div>
  );
}

// Nós iniciais
const initialNodes: Node[] = [
  {
    id: '1',
    type: 'inicioFim',
    position: { x: 400, y: 0 },
    data: { label: 'Início', isStart: true },
  },
  {
    id: '2',
    type: 'tarefa',
    position: { x: 350, y: 100 },
    data: {
      label: 'Análise Inicial',
      description: 'Verificar documentação e requisitos',
      deadline: '2 dias',
      responsible: 'Ana Silva',
    },
  },
  {
    id: '3',
    type: 'condicao',
    position: { x: 350, y: 250 },
    data: {
      label: 'Documentação Completa?',
      description: 'Verificar se todos os documentos foram enviados',
    },
  },
  {
    id: '4',
    type: 'notificacao',
    position: { x: 600, y: 250 },
    data: {
      label: 'Solicitar Documentos',
      description: 'Enviar e-mail solicitando documentos faltantes',
      recipients: 'Cliente',
    },
  },
  {
    id: '5',
    type: 'aprovacao',
    position: { x: 350, y: 400 },
    data: {
      label: 'Aprovação do Parecer',
      description: 'Revisar e aprovar parecer técnico',
      approver: 'Carlos Mendes',
    },
  },
  {
    id: '6',
    type: 'integracao',
    position: { x: 350, y: 550 },
    data: {
      label: 'Integrar com Sistema',
      description: 'Enviar dados para sistema de gestão',
      system: 'ERP',
    },
  },
  {
    id: '7',
    type: 'inicioFim',
    position: { x: 400, y: 700 },
    data: { label: 'Fim', isEnd: true },
  },
];

// Conexões iniciais
const initialEdges: Edge[] = [
  {
    id: 'e1-2',
    source: '1',
    target: '2',
    sourceHandle: 'source',
    targetHandle: 'target',
    animated: true,
    className: 'edge-default',
    markerEnd: { type: MarkerType.ArrowClosed },
  },
  {
    id: 'e2-3',
    source: '2',
    target: '3',
    sourceHandle: 'source',
    targetHandle: 'target',
    animated: true,
    className: 'edge-default',
    markerEnd: { type: MarkerType.ArrowClosed },
  },
  {
    id: 'e3-4',
    source: '3',
    target: '4',
    sourceHandle: 'source-right',
    targetHandle: 'target',
    animated: true,
    label: 'Não',
    className: 'edge-error',
    markerEnd: { type: MarkerType.ArrowClosed },
  },
  {
    id: 'e4-2',
    source: '4',
    target: '2',
    sourceHandle: 'source',
    targetHandle: 'target',
    animated: true,
    className: 'edge-default',
    markerEnd: { type: MarkerType.ArrowClosed },
  },
  {
    id: 'e3-5',
    source: '3',
    target: '5',
    sourceHandle: 'source-bottom',
    targetHandle: 'target',
    animated: true,
    label: 'Sim',
    className: 'edge-success',
    markerEnd: { type: MarkerType.ArrowClosed },
  },
  {
    id: 'e5-6',
    source: '5',
    target: '6',
    sourceHandle: 'source',
    targetHandle: 'target',
    animated: true,
    className: 'edge-default',
    markerEnd: { type: MarkerType.ArrowClosed },
  },
  {
    id: 'e6-7',
    source: '6',
    target: '7',
    sourceHandle: 'source',
    targetHandle: 'target',
    animated: true,
    className: 'edge-default',
    markerEnd: { type: MarkerType.ArrowClosed },
  },
];

function AutomacaoFlow() {
  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const [selectedEdge, setSelectedEdge] = useState<Edge | null>(null);
  const [showAddNode, setShowAddNode] = useState(false);
  const [newNode, setNewNode] = useState({
    type: 'tarefa',
    label: '',
    description: '',
    deadline: '',
    responsible: '',
  });
  const [isLocked, setIsLocked] = useState(false);
  const { fitView, zoomIn, zoomOut, project } = useReactFlow();

  // Adicionar event listener para deletar edges
  useEffect(() => {
    const handleDeleteEdge = (event: Event) => {
      const customEvent = event as CustomEvent;
      const edgeId = customEvent.detail.id;
      setEdges((eds) => eds.filter((e) => e.id !== edgeId));
    };

    window.addEventListener('DELETE_EDGE', handleDeleteEdge as EventListener);
    return () => {
      window.removeEventListener('DELETE_EDGE', handleDeleteEdge as EventListener);
    };
  }, [setEdges]);

  const onConnect = useCallback(
    (params: Connection | Edge) => {
      const edge = {
        ...params,
        animated: true,
        className: 'edge-default',
        markerEnd: { type: MarkerType.ArrowClosed },
      };
      setEdges((eds) => addEdge(edge, eds));
    },
    [setEdges]
  );

  const onEdgeClick = useCallback(
    (event: React.MouseEvent, edge: Edge) => {
      event.stopPropagation();
      setSelectedEdge(edge);
    },
    []
  );

  const handleDeleteEdge = useCallback(() => {
    if (selectedEdge) {
      setEdges((eds) => eds.filter((e) => e.id !== selectedEdge.id));
      setSelectedEdge(null);
    }
  }, [selectedEdge, setEdges]);

  const onDragOver = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);

  const onDrop = useCallback(
    (event: React.DragEvent<HTMLDivElement>) => {
      event.preventDefault();

      if (!reactFlowWrapper.current) return;

      const reactFlowBounds = reactFlowWrapper.current.getBoundingClientRect();
      const position = project({
        x: event.clientX - reactFlowBounds.left,
        y: event.clientY - reactFlowBounds.top,
      });

      const newNodeId = `node-${nodes.length + 1}`;
      const newNode = {
        id: newNodeId,
        type: 'tarefa',
        position,
        data: { label: `Novo Nó ${nodes.length + 1}` },
      };

      setNodes((nds) => nds.concat(newNode));
    },
    [project, nodes, setNodes]
  );

  const handleAddNode = () => {
    const position = { x: Math.random() * 500, y: Math.random() * 500 };
    const newNodeId = `node-${nodes.length + 1}`;
    
    setNodes((nds) => [
      ...nds,
      {
        id: newNodeId,
        type: newNode.type,
        position,
        data: {
          label: newNode.label,
          description: newNode.description,
          deadline: newNode.deadline,
          responsible: newNode.responsible,
        },
      },
    ]);
    
    setShowAddNode(false);
    setNewNode({
      type: 'tarefa',
      label: '',
      description: '',
      deadline: '',
      responsible: '',
    });
  };

  // Função para alinhar verticalmente
  const alignNodesVertically = useCallback(() => {
    const VERTICAL_SPACING = 150;
    const CENTER_X = 400;

    setNodes((nds) => {
      const sortedNodes = [...nds].sort((a, b) => a.position.y - b.position.y);
      let currentY = 50;

      return sortedNodes.map((node) => ({
        ...node,
        position: {
          x: CENTER_X - (node.type === 'condicao' ? 90 : 100),
          y: currentY + (currentY += VERTICAL_SPACING) - VERTICAL_SPACING
        }
      }));
    });

    setTimeout(() => fitView({ padding: 0.2 }), 50);
  }, [setNodes, fitView]);

  // Função para alinhar horizontalmente
  const alignNodesHorizontally = useCallback(() => {
    const HORIZONTAL_SPACING = 250;
    const CENTER_Y = window.innerHeight / 2 - 100;
    const START_X = 100;

    setNodes((nds) => {
      const sortedNodes = [...nds].sort((a, b) => a.position.x - b.position.x);
      
      return sortedNodes.map((node, index) => ({
        ...node,
        position: {
          x: START_X + (index * HORIZONTAL_SPACING),
          y: CENTER_Y
        }
      }));
    });

    setTimeout(() => fitView({ padding: 0.2 }), 50);
  }, [setNodes, fitView]);

  // Função para organizar em grade
  const arrangeInGrid = useCallback(() => {
    const GRID_SPACING_X = 300;
    const GRID_SPACING_Y = 200;
    const NODES_PER_ROW = 3;

    setNodes((nds) => {
      return nds.map((node, index) => ({
        ...node,
        position: {
          x: (index % NODES_PER_ROW) * GRID_SPACING_X + 50,
          y: Math.floor(index / NODES_PER_ROW) * GRID_SPACING_Y + 50
        }
      }));
    });

    setTimeout(() => fitView({ padding: 0.2 }), 50);
  }, [setNodes, fitView]);

  return (
    <div className="w-full h-full absolute inset-0" ref={reactFlowWrapper}>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onDrop={onDrop}
        onDragOver={onDragOver}
        nodeTypes={nodeTypes}
        edgeTypes={edgeTypes}
        connectionMode={ConnectionMode.Loose}
        snapToGrid
        snapGrid={[15, 15]}
        fitView
        className="bg-white dark:bg-neutral-900"
        style={{ width: '100%', height: '100%' }}
        defaultEdgeOptions={{
          type: 'default',
          animated: true,
          markerEnd: { type: MarkerType.ArrowClosed },
        }}
        panOnDrag={!isLocked}
        nodesDraggable={!isLocked}
        nodesConnectable={!isLocked}
        elementsSelectable={!isLocked}
        minZoom={0.2}
        maxZoom={4}
        defaultViewport={{ x: 0, y: 0, zoom: 1 }}
        zoomOnScroll
        zoomOnPinch
        panOnScroll={false}
        selectionOnDrag
        panActivationKeyCode="Space"
        zoomActivationKeyCode={null}
        preventScrolling
        elevateNodesOnSelect
        elevateEdgesOnSelect
        proOptions={{ hideAttribution: true }}
      >
        <Background 
          gap={15} 
          size={1} 
          variant="dots"
          color="#e5e5e5"
          style={{ 
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            width: '100%',
            height: '100%',
            pointerEvents: 'none',
            margin: 0,
            padding: 0
          }}
        />
        
        <Panel position="top-center" className="mt-2 mx-2 max-w-[calc(100%-1rem)] overflow-x-auto">
          <div className="flex gap-2 bg-white dark:bg-neutral-800 p-2 rounded-lg shadow-lg border border-neutral-200 dark:border-neutral-700">
            <div className="flex items-center gap-2 flex-nowrap min-w-fit">
              <Button 
                variant="outline" 
                size="icon"
                onClick={() => fitView({ padding: 0.2 })}
                title="Centralizar"
                className="hover:bg-neutral-100 dark:hover:bg-neutral-700 shrink-0"
              >
                <Maximize2 className="h-4 w-4" />
              </Button>
              
              <Separator orientation="vertical" className="h-8" />
              
              <div className="flex gap-2 overflow-x-auto">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={alignNodesVertically}
                  className="gap-2 hover:bg-neutral-100 dark:hover:bg-neutral-700 whitespace-nowrap shrink-0"
                  title="Alinhar Verticalmente"
                >
                  <AlignVerticalJustifyCenter className="h-4 w-4" />
                  <span className="hidden sm:inline">Vertical</span>
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={alignNodesHorizontally}
                  className="gap-2 hover:bg-neutral-100 dark:hover:bg-neutral-700 whitespace-nowrap shrink-0"
                  title="Alinhar Horizontalmente"
                >
                  <AlignHorizontalJustifyCenter className="h-4 w-4" />
                  <span className="hidden sm:inline">Horizontal</span>
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={arrangeInGrid}
                  className="gap-2 hover:bg-neutral-100 dark:hover:bg-neutral-700 whitespace-nowrap shrink-0"
                  title="Organizar em Grade"
                >
                  <Grid className="h-4 w-4" />
                  <span className="hidden sm:inline">Grade</span>
                </Button>
              </div>
              
              <Separator orientation="vertical" className="h-8" />
              
              <Button 
                variant="outline" 
                size="icon"
                onClick={() => setIsLocked(!isLocked)}
                className={cn(
                  "hover:bg-neutral-100 dark:hover:bg-neutral-700 shrink-0",
                  isLocked && "bg-neutral-200 dark:bg-neutral-700"
                )}
                title={isLocked ? 'Desbloquear Edição' : 'Bloquear Edição'}
              >
                {isLocked ? <Lock className="h-4 w-4" /> : <Unlock className="h-4 w-4" />}
              </Button>
            </div>
          </div>
        </Panel>

        <Panel position="top-left" className="p-2 max-w-[90vw]">
          <div className="flex flex-col gap-2 bg-white dark:bg-neutral-800 p-2 rounded-lg shadow-lg border border-neutral-200 dark:border-neutral-700">
            <Button 
              variant="outline" 
              size="icon" 
              onClick={() => zoomIn()}
              title="Aumentar Zoom"
              className="hover:bg-neutral-100 dark:hover:bg-neutral-700"
            >
              <Plus className="h-4 w-4" />
            </Button>
            <Button 
              variant="outline" 
              size="icon" 
              onClick={() => zoomOut()}
              title="Diminuir Zoom"
              className="hover:bg-neutral-100 dark:hover:bg-neutral-700"
            >
              <Minus className="h-4 w-4" />
            </Button>
          </div>
        </Panel>

        <MiniMap 
          className="!top-2 !right-2 max-w-[20vw] max-h-[20vh]" 
          zoomable 
          pannable
        />
      </ReactFlow>
    </div>
  );
}

export default function Automacao() {
  return (
    <div className="w-full h-screen bg-white dark:bg-neutral-900 overflow-hidden p-0 m-0">
      <ReactFlowProvider>
        <AutomacaoFlow />
      </ReactFlowProvider>
    </div>
  );
} 