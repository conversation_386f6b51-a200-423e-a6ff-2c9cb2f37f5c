// Dados mockados para o dashboard

// Estatísticas gerais
export const estatisticasGerais = {
  total_precatorios: 245,
  precatorios_ativos: 180,
  novos_mes: 12,
  valor_total: 25000000,
  valor_pago: 8500000,
  media_prazo: 180,
  taxa_sucesso: 85,
  total_tarefas: 156,
  tarefas_concluidas: 89,
  tarefas_pendentes: 67,
  precatorios_por_tipo: {
    alimentar: 150,
    comum: 80,
    preferencial: 15
  },
  valor_medio_precatorio: 102040.81,
  crescimento_mensal: 15.8,
  eficiencia_pagamentos: 92,
  entidades_devedoras: 12,
  precatorios_atrasados: 8,
  precatorios_em_dia: 237,
  total_beneficiarios: 220,
  novos_beneficiarios_mes: 15
};

// Histórico de valores
export const historicoValores = [
  { mes: "Jan", valor: 18500000, pagamentos: 2500000 },
  { mes: "Fev", valor: 19800000, pagamentos: 3200000 },
  { mes: "Mar", valor: 22000000, pagamentos: 4100000 },
  { mes: "Abr", valor: 23500000, pagamentos: 5800000 },
  { mes: "Mai", valor: 24200000, pagamentos: 7200000 },
  { mes: "Jun", valor: 25000000, pagamentos: 8500000 },
];

// Distribuição por status
export const distribuicaoStatus = [
  { name: "Em Processamento", value: 120, color: "#3b82f6" },
  { name: "Aguardando Pagamento", value: 60, color: "#f59e0b" },
  { name: "Concluídos", value: 45, color: "#10b981" },
  { name: "Suspensos", value: 20, color: "#ef4444" },
];

// Precatórios recentes
export const precatoriosRecentes = [
  {
    numero: "2024.001.123456-7",
    beneficiario: "João Silva",
    tipo: "Alimentar",
    valor: 250000,
    status: "Em Processamento",
    data_entrada: "2024-03-15",
    tribunal: "TRF-1",
    progresso: 65,
  },
  {
    numero: "2024.001.789012-3",
    beneficiario: "Maria Santos",
    tipo: "Comum",
    valor: 180000,
    status: "Aguardando Pagamento",
    data_entrada: "2024-03-10",
    tribunal: "TRF-2",
    progresso: 85,
  },
  {
    numero: "2024.001.345678-9",
    beneficiario: "Carlos Ferreira",
    tipo: "Alimentar",
    valor: 320000,
    status: "Concluído",
    data_entrada: "2024-02-15",
    tribunal: "TRF-3",
    progresso: 100,
  },
];

// Tarefas urgentes
export const tarefasUrgentes = [
  {
    id: "1",
    titulo: "Análise de documentação",
    responsavel: {
      nome: "Ana Costa",
      avatar: "/avatars/ana_costa.jpg",
    },
    precatorio: "2024.001.123456-7",
    prazo: "2024-03-25",
    prioridade: "alta",
    status: "pendente",
  },
  {
    id: "2",
    titulo: "Atualização de cálculos",
    responsavel: {
      nome: "Pedro Silva",
      avatar: "/avatars/pedro_silva.jpg",
    },
    precatorio: "2024.001.789012-3",
    prazo: "2024-03-26",
    prioridade: "alta",
    status: "em_andamento",
  },
  {
    id: "3",
    titulo: "Envio de notificação",
    responsavel: {
      nome: "Maria Oliveira",
      avatar: "/avatars/maria_oliveira.jpg",
    },
    precatorio: "2024.001.345678-9",
    prazo: "2024-03-27",
    prioridade: "media",
    status: "pendente",
  },
];

// Distribuição por tipo
export const distribuicaoTipo = [
  { name: "Alimentar", value: 150, color: "#3b82f6" },
  { name: "Comum", value: 80, color: "#64748b" },
  { name: "Preferencial", value: 15, color: "#10b981" },
];

// Desempenho mensal
export const desempenhoMensal = [
  { mes: "Jan", concluidos: 15, novos: 18, meta: 20 },
  { mes: "Fev", concluidos: 18, novos: 15, meta: 20 },
  { mes: "Mar", concluidos: 22, novos: 20, meta: 20 },
  { mes: "Abr", concluidos: 25, novos: 18, meta: 20 },
  { mes: "Mai", concluidos: 28, novos: 22, meta: 20 },
  { mes: "Jun", concluidos: 30, novos: 25, meta: 20 },
];

// Tempo médio por etapa
export const tempoMedioPorEtapa = [
  { etapa: "Análise Inicial", tempo: 15 },
  { etapa: "Documentação", tempo: 30 },
  { etapa: "Processamento", tempo: 45 },
  { etapa: "Validação", tempo: 20 },
  { etapa: "Pagamento", tempo: 60 },
];

// Metas de desempenho
export const metasDesempenho = {
  tempo_medio_conclusao: {
    atual: 170,
    meta: 150,
    variacao: -12,
  },
  taxa_conclusao: {
    atual: 85,
    meta: 90,
    variacao: 5,
  },
  eficiencia_processamento: {
    atual: 92,
    meta: 95,
    variacao: 3,
  },
};

// Notificações
export const notificacoes = [
  {
    id: "1",
    tipo: "alerta",
    mensagem: "3 precatórios com vencimento próximo",
    data: new Date(),
    lida: false
  },
  {
    id: "2",
    tipo: "info",
    mensagem: "Nova atualização do sistema disponível",
    data: new Date(Date.now() - 3600000),
    lida: true
  },
  {
    id: "3",
    tipo: "sucesso",
    mensagem: "Precatório #2024.001.345678-9 foi pago com sucesso",
    data: new Date(Date.now() - 86400000),
    lida: false
  }
];

// Desempenho da equipe
export const desempenhoEquipe = [
  {
    membro: "Ana Costa",
    avatar: "/avatars/ana_costa.jpg",
    cargo: "Analista",
    precatorios_processados: 45,
    taxa_conclusao: 95,
    tempo_medio: 12,
  },
  {
    membro: "Pedro Silva",
    avatar: "/avatars/pedro_silva.jpg",
    cargo: "Especialista",
    precatorios_processados: 38,
    taxa_conclusao: 89,
    tempo_medio: 15,
  },
  {
    membro: "Maria Oliveira",
    avatar: "/avatars/maria_oliveira.jpg",
    cargo: "Coordenadora",
    precatorios_processados: 52,
    taxa_conclusao: 97,
    tempo_medio: 10,
  }
];

// Projeções e tendências
export const projecoes = {
  crescimento_trimestral: 22,
  economia_processamento: 15,
  reducao_tempo_medio: 18,
  aumento_eficiencia: 12,
};