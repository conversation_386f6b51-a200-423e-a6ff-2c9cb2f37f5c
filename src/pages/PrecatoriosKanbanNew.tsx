import { useState, useEffect, useMemo, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { supabase } from "@/lib/supabase";
import {
  KanbanContainer,
  Precatorio,
  KanbanColuna as KanbanColunaType
} from "@/components/Precatorios";
import {
  salvarPrecatorio,
  excluirPrecatorio,
  atualizarStatusPrecatorio
} from "@/services/precatoriosServiceSimples";
import { clearCache } from "@/services/cacheService";
import {
  buscarPrecatoriosParaKanban,
  buscarColunasKanban
} from "@/services/kanbanPrecatoriosService";
import { useAuth } from "@/hooks/useAuth";
import { usePermissions } from "@/contexts/PermissionsContext";
import { TopNav } from "@/components/top-nav";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ScrollA<PERSON> } from "@/components/ui/scroll-area";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from "@/components/ui/dropdown-menu";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  KanbanSquare,
  Plus,
  Settings,
  Filter,
  FilterX,
  LayoutGrid,
  List,
  Table as TableIcon,
  Star,
  Eye,
  Sliders,
  MoreVertical,
  Loader2,
  PlusCircle,
  Trash2,
  Edit,
  Save,
  BookmarkPlus,
  Bookmark,
  ChevronDown,
  Search,
  X,
  ArrowDownUp,
  Tag as TagIcon,
  RefreshCw,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";

// Importar componentes do sistema de tags e visualizações
import { CustomView, fetchUserViews, getPrecatoriosByView, fetchVisibleColunas, fetchAllColunasPersonalizadas } from "@/services/kanbanService";
import { Tag, fetchVisibleTags } from "@/services/tagsService";
import { KanbanColumnManager } from "@/components/kanban/KanbanColumnManager";
import { CustomViewManager } from "@/components/kanban/CustomViewManager";
import { TagManager } from "@/components/tags/TagManager";
import { DeletedItemsManager } from "@/components/recovery/DeletedItemsManager";

function PrecatoriosKanbanNew() {
  const navigate = useNavigate();
  const { user, isAdmin } = useAuth();
  const { hasPermission } = usePermissions();

  // Estados para precatórios e colunas
  const [precatorios, setPrecatorios] = useState<Precatorio[]>([]);
  const [colunas, setColunas] = useState<KanbanColunaType[]>([]);
  const [loading, setLoading] = useState(true);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  // Estados para visualizações e tags
  const [views, setViews] = useState<CustomView[]>([]);
  const [selectedView, setSelectedView] = useState<CustomView | null>(null);
  const [tags, setTags] = useState<Tag[]>([]);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [viewsLoading, setViewsLoading] = useState(false);
  const [tagsLoading, setTagsLoading] = useState(false);

  // Estados para modais e configurações
  const [isTagsDialogOpen, setIsTagsDialogOpen] = useState(false);
  const [isColumnsDialogOpen, setIsColumnsDialogOpen] = useState(false);
  const [isViewsDialogOpen, setIsViewsDialogOpen] = useState(false);
  const [isDeletedItemsDialogOpen, setIsDeletedItemsDialogOpen] = useState(false);
  const [isCreateViewDialogOpen, setIsCreateViewDialogOpen] = useState(false);
  const [isFilterDialogOpen, setIsFilterDialogOpen] = useState(false);

  // Estado para o modo de visualização
  const [viewMode, setViewMode] = useState<'kanban' | 'list' | 'table'>('kanban');

  // Estados para filtros e busca
  const [searchTerm, setSearchTerm] = useState("");
  const [showFilters, setShowFilters] = useState(false);

  // Referência para controlar carregamentos múltiplos
  const loadingRef = useRef(false);
  const initialLoadDoneRef = useRef(false);

  // Carregar visualizações, tags e colunas
  useEffect(() => {
    let isMounted = true;

    // Limpar tags selecionadas ao inicializar o componente
    // Isso evita que filtros anteriores afetem a visualização inicial
    if (selectedTags.length > 0) {
      console.log('Limpando tags selecionadas na inicialização:', selectedTags);
      setSelectedTags([]);
    }

    const initializeData = async () => {
      // Evitar carregamentos múltiplos simultâneos
      if (loadingRef.current) {
        console.log('🔄 Já existe um carregamento em andamento, ignorando...');
        return;
      }

      // Evitar recarregamentos desnecessários se já carregamos os dados iniciais
      if (initialLoadDoneRef.current && precatorios.length > 0 && colunas.length > 0) {
        console.log('🔄 Dados iniciais já carregados, ignorando carregamento duplicado...');
        return;
      }

      try {
        loadingRef.current = true;
        console.log('🔄 Inicializando dados do Kanban...');

        // Verificar se a sessão está válida antes de carregar os dados
        try {
          console.log('Verificando sessão antes de carregar dados...');

          // Usar o sistema de autenticação simplificado
          const { isAuthenticated } = await import('@/lib/auth-simple');
          const authenticated = await isAuthenticated();

          if (!authenticated && isMounted) {
            console.error("PrecatoriosKanbanNew: Usuário não autenticado, redirecionando para login");
            navigate("/login");
            return;
          }

          console.log('Usuário autenticado, continuando carregamento de dados...');
        } catch (sessionError) {
          console.error("PrecatoriosKanbanNew: Erro ao verificar sessão:", sessionError);

          // Em caso de erro, redirecionar para login
          if (isMounted) {
            navigate("/login");
            return;
          }
        }

        if (!isMounted) return;

        // Carregar dados em paralelo para melhor performance
        await Promise.all([
          loadViews(),
          loadTags(),
          loadColunas()
        ]);

        if (!isMounted) return;

        // Carregar precatórios imediatamente, sem esperar pela seleção de visualização
        await loadAllPrecatorios();
        console.log('✅ Dados iniciais carregados com sucesso');

        // Marcar que o carregamento inicial foi concluído
        initialLoadDoneRef.current = true;
      } catch (error) {
        console.error('❌ Erro ao carregar dados iniciais:', error);
        if (isMounted) {
          toast.error('Erro ao carregar dados', {
            description: 'Não foi possível carregar os dados iniciais. Tente novamente mais tarde.'
          });
        }
      } finally {
        if (isMounted) {
          loadingRef.current = false;
        }
      }
    };

    initializeData();

    // Adicionar listener para o evento de visibilidade da página
    const handleVisibilityChange = async () => {
      if (document.visibilityState === 'visible') {
        // Evitar recarregamentos múltiplos quando a página volta a ficar visível
        if (loadingRef.current) {
          console.log('Já existe um carregamento em andamento, ignorando evento de visibilidade...');
          return;
        }

        console.log('Página voltou a ficar visível, verificando sessão...');

        try {
          loadingRef.current = true;

          // Verificar e atualizar a sessão usando o sistema simplificado
          const { isAuthenticated } = await import('@/lib/auth-simple');
          const authenticated = await isAuthenticated();

          if (!authenticated) {
            console.error("PrecatoriosKanbanNew: Usuário não autenticado após retorno à página, redirecionando para login");
            navigate("/login");
            return;
          }

          // Só recarregar dados se não houver dados ou se a última carga foi há mais de 30 segundos
          const shouldReload = precatorios.length === 0 || colunas.length === 0;

          if (shouldReload && isMounted) {
            console.log('Recarregando dados após retorno à página...');
            if (selectedView) {
              await loadPrecatoriosByView(selectedView.id);
            } else {
              await loadAllPrecatorios();
            }
          } else {
            console.log('Dados já carregados, ignorando recarga após retorno à página');
          }
        } catch (error) {
          console.error('Erro ao recarregar dados após retorno à página:', error);
        } finally {
          if (isMounted) {
            loadingRef.current = false;
          }
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      isMounted = false;
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [navigate, selectedView, precatorios.length, colunas.length]);

  // Carregar precatórios quando a visualização mudar
  useEffect(() => {
    if (selectedView) {
      loadPrecatoriosByView(selectedView.id);
    }
  }, [selectedView]);

  // Carregar colunas do Kanban
  const loadColunas = async () => {
    try {
      const colunasData = await fetchVisibleColunas();
      console.log('Colunas carregadas:', colunasData);

      if (colunasData.length === 0) {
        // Se não houver colunas, criar colunas padrão
        const colunasDefault: KanbanColunaType[] = [
          {
            id: 'novo',
            nome: 'Novo',
            cor: '#3b82f6',
            ordem: 1,
            is_default: true,
            is_system: true
          },
          {
            id: 'em-analise',
            nome: 'Em Análise',
            cor: '#8b5cf6',
            ordem: 2,
            is_default: true,
            is_system: true
          },
          {
            id: 'em-andamento',
            nome: 'Em Andamento',
            cor: '#10b981',
            ordem: 3,
            is_default: true,
            is_system: true
          },
          {
            id: 'aguardando',
            nome: 'Aguardando',
            cor: '#f59e0b',
            ordem: 4,
            is_default: true,
            is_system: true
          },
          {
            id: 'concluido',
            nome: 'Concluído',
            cor: '#6366f1',
            ordem: 5,
            is_default: true,
            is_system: true
          }
        ];
        setColunas(colunasDefault);
      } else {
        setColunas(colunasData);
      }
    } catch (error) {
      console.error('Erro ao carregar colunas do Kanban:', error);
      toast.error('Erro ao carregar colunas', {
        description: 'Não foi possível carregar as colunas do Kanban.'
      });

      // Em caso de erro, usar colunas padrão
      const colunasDefault: KanbanColunaType[] = [
        {
          id: 'novo',
          nome: 'Novo',
          cor: '#3b82f6',
          ordem: 1,
          is_default: true,
          is_system: true
        },
        {
          id: 'em-analise',
          nome: 'Em Análise',
          cor: '#8b5cf6',
          ordem: 2,
          is_default: true,
          is_system: true
        },
        {
          id: 'em-andamento',
          nome: 'Em Andamento',
          cor: '#10b981',
          ordem: 3,
          is_default: true,
          is_system: true
        },
        {
          id: 'aguardando',
          nome: 'Aguardando',
          cor: '#f59e0b',
          ordem: 4,
          is_default: true,
          is_system: true
        },
        {
          id: 'concluido',
          nome: 'Concluído',
          cor: '#6366f1',
          ordem: 5,
          is_default: true,
          is_system: true
        }
      ];
      setColunas(colunasDefault);
    }
  };

  // Carregar visualizações
  const loadViews = async () => {
    try {
      setViewsLoading(true);
      const userViews = await fetchUserViews();
      setViews(userViews);

      // Selecionar a visualização padrão ou a primeira disponível
      const defaultView = userViews.find(v => v.is_default) || userViews[0];
      if (defaultView) {
        setSelectedView(defaultView);
        // Definir o modo de visualização com base no layout da visualização
        if (defaultView.layout && ['kanban', 'list', 'table'].includes(defaultView.layout as any)) {
          setViewMode(defaultView.layout as 'kanban' | 'list' | 'table');
        } else {
          setViewMode('kanban'); // Fallback para kanban se o layout não for válido
        }
      }
    } catch (error) {
      console.error('Erro ao carregar visualizações:', error);
      toast.error('Erro ao carregar visualizações', {
        description: 'Não foi possível carregar as visualizações personalizadas.'
      });
    } finally {
      setViewsLoading(false);
    }
  };

  // Carregar tags
  const loadTags = async () => {
    try {
      setTagsLoading(true);
      const visibleTags = await fetchVisibleTags();
      setTags(visibleTags);
    } catch (error) {
      console.error('Erro ao carregar tags:', error);
      toast.error('Erro ao carregar tags', {
        description: 'Não foi possível carregar as tags disponíveis.'
      });
    } finally {
      setTagsLoading(false);
    }
  };

  // Carregar precatórios por visualização
  const loadPrecatoriosByView = async (viewId: string, retry = 0) => {
    try {
      setLoading(true);
      setErrorMessage(null);

      // Verificar se a sessão está válida antes de buscar os dados
      try {
        console.log("loadPrecatoriosByView: Verificando autenticação...");

        // Usar o sistema de autenticação simplificado
        const { isAuthenticated } = await import('@/lib/auth-simple');
        const authenticated = await isAuthenticated();

        if (!authenticated) {
          console.error("loadPrecatoriosByView: Usuário não autenticado, redirecionando para login");
          navigate("/login");
          return;
        }

        console.log("loadPrecatoriosByView: Usuário autenticado, continuando...");
      } catch (sessionError) {
        console.error("loadPrecatoriosByView: Erro ao verificar autenticação:", sessionError);

        // Em caso de erro, redirecionar para login
        navigate("/login");
        return;
      }

      // Obter a visualização selecionada
      const view = views.find(v => v.id === viewId);
      if (!view) {
        throw new Error(`Visualização com ID ${viewId} não encontrada`);
      }

      // Verificar se é a visualização "Todos" (identificada pelo nome ou por não ter filtros)
      const isAllView = view.nome?.toLowerCase() === 'todos' ||
                        view.nome?.toLowerCase() === 'all' ||
                        view.nome?.toLowerCase() === 'tudo' ||
                        (!view.tags_selecionadas || view.tags_selecionadas.length === 0);

      // Atualizar tags selecionadas com base na visualização
      if (isAllView) {
        // Se for a visualização "Todos", garantir que não há filtros ativos
        console.log('Visualização "Todos" selecionada, limpando todas as tags');
        setSelectedTags([]);
        setSearchTerm("");
      } else if (view.tags_selecionadas && view.tags_selecionadas.length > 0) {
        console.log(`Aplicando ${view.tags_selecionadas.length} tags da visualização "${view.nome}"`);
        setSelectedTags(view.tags_selecionadas);
      } else {
        setSelectedTags([]);
      }

      // Atualizar modo de visualização com base no layout da visualização
      if (view.layout && ['kanban', 'list', 'table'].includes(view.layout as any)) {
        setViewMode(view.layout as 'kanban' | 'list' | 'table');
      }

      // Verificar se a visualização tem colunas válidas
      try {
        // Verificar se as colunas da visualização existem
        if (view.colunas_selecionadas && view.colunas_selecionadas.length > 0) {
          console.log(`[PrecatoriosKanbanNew] Verificando colunas da visualização ${viewId}...`);

          const { data: colunasData, error: colunasError } = await supabase
            .from('kanban_colunas_personalizadas')
            .select('id, nome')
            .in('id', view.colunas_selecionadas)
            .eq('is_deleted', false);

          if (colunasError) {
            console.error(`[PrecatoriosKanbanNew] Erro ao verificar colunas:`, colunasError);
          } else {
            console.log(`[PrecatoriosKanbanNew] Encontradas ${colunasData?.length || 0} colunas válidas de ${view.colunas_selecionadas.length} selecionadas`);

            // Se não encontrou nenhuma coluna, atualizar a visualização com colunas padrão
            if (!colunasData || colunasData.length === 0) {
              console.warn(`[PrecatoriosKanbanNew] Nenhuma coluna válida encontrada, criando colunas padrão...`);

              // Criar colunas padrão
              const { criarColunasPersonalizadasPadrao } = await import('@/services/kanbanService');
              const colunasIds = await criarColunasPersonalizadasPadrao();

              if (colunasIds.length > 0) {
                console.log(`[PrecatoriosKanbanNew] Atualizando visualização com novas colunas: ${colunasIds}`);

                // Atualizar a visualização com as novas colunas
                await supabase
                  .from('custom_views')
                  .update({
                    colunas_selecionadas: colunasIds,
                    updated_at: new Date().toISOString()
                  })
                  .eq('id', viewId);

                // Atualizar a visualização na memória
                view.colunas_selecionadas = colunasIds;

                // Atualizar a lista de visualizações
                setViews(views.map(v => v.id === viewId ? {...v, colunas_selecionadas: colunasIds} : v));
              }
            }
          }
        }
      } catch (colunasError) {
        console.error(`[PrecatoriosKanbanNew] Erro ao verificar/criar colunas:`, colunasError);
      }

      // Carregar precatórios com base na visualização
      try {
        console.log(`[PrecatoriosKanbanNew] Carregando precatórios para visualização ${viewId}...`);
        const precatoriosData = await getPrecatoriosByView(viewId);
        console.log('[PrecatoriosKanbanNew] Precatórios carregados por visualização:', precatoriosData);

        // Garantir que precatoriosData é um array
        const precatoriosArray = Array.isArray(precatoriosData) ? precatoriosData : [];

        // Verificar se temos dados válidos
        if (!precatoriosArray.length && retry < 2) {
          console.warn(`[PrecatoriosKanbanNew] Nenhum precatório encontrado para a visualização ${viewId}, tentando novamente...`);
          // Esperar um pouco antes de tentar novamente
          await new Promise(resolve => setTimeout(resolve, 500));
          return loadPrecatoriosByView(viewId, retry + 1);
        }

        // Log detalhado dos primeiros 3 precatórios para debug
        if (precatoriosArray.length > 0) {
          console.log('Detalhes dos primeiros 3 precatórios:');
          precatoriosArray.slice(0, 3).forEach((p, index) => {
            console.log(`Precatório ${index + 1}:`, {
              id: p.id,
              numero: p.numero_precatorio || p.numero,
              status: p.status,
              status_id: p.status_id
            });
          });
        } else {
          console.warn('Nenhum precatório encontrado para esta visualização');
        }

        setPrecatorios(precatoriosArray);

        // Filtrar colunas com base na visualização
        if (view.colunas_selecionadas && view.colunas_selecionadas.length > 0) {
          try {
            // Buscar colunas personalizadas
            const colunasPersonalizadas = await fetchAllColunasPersonalizadas();
            console.log('Colunas personalizadas disponíveis:', colunasPersonalizadas.length);

            // Filtrar apenas as colunas selecionadas na visualização
            const colunasFiltradas = colunasPersonalizadas
              .filter(coluna => view.colunas_selecionadas?.includes(coluna.id))
              .map(coluna => ({
                id: coluna.id,
                nome: coluna.nome,
                cor: coluna.cor || '#3b82f6',
                ordem: coluna.ordem,
                name: coluna.nome, // Para compatibilidade
                status_id: coluna.status_id, // Importante para a relação com status
                status_uuid: coluna.status_uuid // Importante para a relação com status
              }));

            // Se não encontrou nenhuma coluna personalizada, tentar com colunas normais
            if (colunasFiltradas.length === 0) {
              console.log('Nenhuma coluna personalizada encontrada, tentando colunas normais');
              const colunasNormais = await fetchVisibleColunas();

              // Usar todas as colunas normais disponíveis
              setColunas(colunasNormais);
            } else {
              // Ordenar colunas conforme a ordem na visualização
              const colunasOrdenadas = colunasFiltradas.sort((a, b) => {
                const indexA = view.colunas_selecionadas?.indexOf(a.id) ?? 0;
                const indexB = view.colunas_selecionadas?.indexOf(b.id) ?? 0;
                return indexA - indexB;
              });

              console.log('Colunas filtradas para a visualização:', colunasOrdenadas);
              setColunas(colunasOrdenadas);
            }
          } catch (error) {
            console.error('Erro ao filtrar colunas:', error);
            // Em caso de erro, carregar todas as colunas
            loadColunas();
          }
        } else {
          // Se não houver colunas selecionadas, carregar todas as colunas
          loadColunas();
        }
      } catch (dataError) {
        console.error('Erro ao carregar dados da visualização:', dataError);

        // Verificar se é um erro de autenticação
        if (dataError.message?.includes('JWT') ||
            dataError.message?.includes('token') ||
            dataError.message?.includes('auth') ||
            dataError.message?.includes('session')) {

          console.warn("loadPrecatoriosByView: Erro de autenticação, tentando recuperar sessão...");

          // Tentar recuperar a sessão usando o sistema simplificado
          const { isAuthenticated } = await import('@/lib/auth-simple');
          const authenticated = await isAuthenticated();

          if (authenticated && retry < 2) {
            console.log("loadPrecatoriosByView: Sessão recuperada, tentando novamente...");
            // Esperar um pouco antes de tentar novamente
            await new Promise(resolve => setTimeout(resolve, 500));
            return loadPrecatoriosByView(viewId, retry + 1);
          } else {
            console.error("loadPrecatoriosByView: Falha ao recuperar sessão após erro de autenticação");
            navigate("/login");
            return;
          }
        }

        // Se não for erro de autenticação, propagar o erro
        throw dataError;
      }
    } catch (error) {
      console.error('Erro ao carregar precatórios por visualização:', error);
      setErrorMessage('Não foi possível carregar os precatórios para esta visualização.');
      toast.error('Erro ao carregar precatórios', {
        description: 'Não foi possível carregar os precatórios para esta visualização.'
      });

      // Em caso de erro, carregar todos os precatórios e colunas padrão
      loadAllPrecatorios();
      loadColunas();
    } finally {
      setLoading(false);
    }
  };

  // Função de debug para inspecionar dados
  const debugData = () => {
    console.group('🔍 DEBUG: Estado atual do Kanban');
    console.log('Precatórios:', precatorios.length);
    if (precatorios.length > 0) {
      console.log('Amostra de precatórios:', precatorios.slice(0, 3).map(p => ({
        id: p.id,
        numero: p.numero_precatorio || p.numero,
        status: p.status,
        status_id: p.status_id
      })));
    }

    console.log('Colunas:', colunas.length);
    if (colunas.length > 0) {
      console.log('Amostra de colunas:', colunas.slice(0, 3).map(c => ({
        id: c.id,
        nome: c.nome || c.name,
        status_id: c.status_id,
        status_uuid: c.status_uuid
      })));
    }

    console.log('Filtros aplicados:', {
      searchTerm,
      selectedTags,
      selectedView: selectedView?.nome
    });

    console.groupEnd();
  };

  // Carregar todos os precatórios
  const loadAllPrecatorios = async (retry = 0, clearFilters = true) => {
    try {
      setLoading(true);
      setErrorMessage(null);

      // Limpar filtros se solicitado
      if (clearFilters) {
        console.log('Limpando filtros antes de carregar todos os precatórios');
        setSelectedTags([]);
        setSearchTerm("");
      }

      console.log('🔍 Iniciando carregamento de todos os precatórios para o kanban...');

      // Verificar se a sessão está válida antes de buscar os dados
      try {
        console.log("loadAllPrecatorios: Verificando autenticação...");

        // Usar o sistema de autenticação simplificado
        const { isAuthenticated } = await import('@/lib/auth-simple');
        const authenticated = await isAuthenticated();

        if (!authenticated) {
          console.error("loadAllPrecatorios: Usuário não autenticado, redirecionando para login");
          navigate("/login");
          return;
        }

        console.log("loadAllPrecatorios: Usuário autenticado, continuando...");
      } catch (sessionError) {
        console.error("loadAllPrecatorios: Erro ao verificar autenticação:", sessionError);

        // Em caso de erro, redirecionar para login
        navigate("/login");
        return;
      }

      // Limpar cache para garantir dados atualizados
      clearCache();

      console.log('Buscando precatórios no Supabase...');

      // Buscar diretamente do Supabase para garantir dados atualizados
      const { data: precatoriosRaw, error: precatoriosError } = await supabase
        .from('precatorios')
        .select(`
          id,
          numero_precatorio,
          valor_total,
          desconto,
          status,
          status_id,
          beneficiario_id,
          responsavel_id,
          tribunal_id,
          data_previsao_pagamento,
          natureza,
          observacoes,
          tags,
          prioridade,
          created_at,
          updated_at,
          tipo,
          status_info:status_id(id, nome, codigo, cor)
        `)
        .or('is_deleted.is.null,is_deleted.eq.false');

      console.log('Resultado da busca de precatórios:', {
        quantidade: precatoriosRaw?.length || 0,
        erro: precatoriosError ? precatoriosError.message : null
      });

      if (precatoriosError) {
        // Verificar se é um erro de autenticação
        if (precatoriosError.message?.includes('JWT') ||
            precatoriosError.message?.includes('token') ||
            precatoriosError.message?.includes('auth') ||
            precatoriosError.message?.includes('session')) {

          console.warn("loadAllPrecatorios: Erro de autenticação, tentando recuperar sessão...");

          // Tentar recuperar a sessão usando o sistema simplificado
          const { isAuthenticated } = await import('@/lib/auth-simple');
          const authenticated = await isAuthenticated();

          if (authenticated && retry < 2) {
            console.log("loadAllPrecatorios: Sessão recuperada, tentando novamente...");
            // Esperar um pouco antes de tentar novamente
            await new Promise(resolve => setTimeout(resolve, 500));
            return loadAllPrecatorios(retry + 1);
          } else {
            console.error("loadAllPrecatorios: Falha ao recuperar sessão após erro de autenticação");
            navigate("/login");
            return;
          }
        }

        throw precatoriosError;
      }

      console.log('Precatórios carregados do Supabase:', precatoriosRaw?.length || 0);

      // Buscar colunas do kanban
      const { data: colunasData, error: colunasError } = await supabase
        .from('kanban_colunas')
        .select('*')
        .eq('ativo', true)
        .order('ordem');

      if (colunasError) {
        throw colunasError;
      }

      // Transformar os dados brutos em precatórios formatados
      const precatoriosData = precatoriosRaw.map(p => {
        // Garantir que status_id está definido - usar o ID da coluna correspondente se necessário
        let statusId = p.status_id;

        // Se não tiver status_id mas tiver status, tentar encontrar a coluna correspondente
        if (!statusId && p.status) {
          console.log(`Precatório ${p.id} sem status_id, tentando encontrar correspondência pelo status: ${p.status}`);

          // Buscar na tabela status_precatorios
          const statusCorrespondente = colunasData?.find(c =>
            (c.status_id?.toLowerCase() === p.status?.toLowerCase()) ||
            (c.nome?.toLowerCase() === p.status?.toLowerCase())
          );

          if (statusCorrespondente) {
            console.log(`Encontrada coluna correspondente para precatório ${p.id}: ${statusCorrespondente.nome} (${statusCorrespondente.id})`);
            statusId = statusCorrespondente.status_uuid || statusCorrespondente.id;
          } else {
            console.log(`Não foi encontrada coluna correspondente para precatório ${p.id} com status: ${p.status}`);
          }
        }

        // Buscar informações do cliente (beneficiário)
        let clienteInfo = { nome: 'Cliente não especificado' };
        if (p.beneficiario_id) {
          clienteInfo = {
            id: p.beneficiario_id,
            nome: 'Cliente ' + p.beneficiario_id.substring(0, 4)
          };
        }

        // Buscar informações do responsável
        let responsavelInfo = { nome: 'Responsável não atribuído' };
        if (p.responsavel_id) {
          responsavelInfo = {
            id: p.responsavel_id,
            nome: 'Responsável ' + p.responsavel_id.substring(0, 4)
          };
        }

        const precatorio = {
          id: p.id,
          numero: p.numero_precatorio || `PR-${p.id.substring(0, 4)}`,
          numero_precatorio: p.numero_precatorio,
          valor: parseFloat(String(p.valor_total || 0)),
          valor_total: p.valor_total,
          desconto: parseFloat(String(p.desconto || 0)),
          status: p.status || 'analise',
          status_id: statusId, // Usar o status_id encontrado
          cliente: clienteInfo,
          responsavel: responsavelInfo,
          tribunal: p.tribunal_id || 'Tribunal não especificado',
          data_previsao_pagamento: p.data_previsao_pagamento,
          natureza: p.natureza || '',
          observacoes: p.observacoes || '',
          tags: p.tags || [],
          prioridade: p.prioridade || 'media',
          created_at: p.created_at,
          updated_at: p.updated_at,
          tipo: p.tipo || 'PRECATORIO'
        };

        console.log(`Precatório formatado: ${precatorio.id} - status: ${precatorio.status}, status_id: ${precatorio.status_id}`);

        return precatorio;
      });

      console.log(`✅ Carregados ${precatoriosData?.length || 0} precatórios para o kanban`);
      console.log(`✅ Carregadas ${colunasData?.length || 0} colunas para o kanban`);

      // Log detalhado para debug
      if (precatoriosData && precatoriosData.length > 0) {
        console.log('📋 Primeiros 3 precatórios:', precatoriosData.slice(0, 3).map(p => ({
          id: p.id,
          numero: p.numero_precatorio || p.numero,
          status: p.status,
          status_id: p.status_id,
          tipo: p.tipo
        })));

        // Verificar se algum precatório tem status_id nulo
        const semStatusId = precatoriosData.filter(p => !p.status_id);
        if (semStatusId.length > 0) {
          console.warn(`⚠️ ${semStatusId.length} precatórios sem status_id`);

          // Tentar corrigir precatórios sem status_id
          for (const precatorio of semStatusId) {
            if (precatorio.status) {
              console.log(`Tentando corrigir precatório ${precatorio.id} com status=${precatorio.status}`);

              // Buscar o status_id correspondente ao código de status
              const { data: statusData } = await supabase
                .from('status_precatorios')
                .select('id')
                .eq('codigo', precatorio.status)
                .maybeSingle();

              if (statusData) {
                console.log(`Encontrado status_id ${statusData.id} para código ${precatorio.status}`);
                precatorio.status_id = statusData.id;

                // Atualizar no banco de dados
                await supabase
                  .from('precatorios')
                  .update({ status_id: statusData.id })
                  .eq('id', precatorio.id);
              }
            }
          }
        }

        // Verificar se algum precatório tem status nulo
        const semStatus = precatoriosData.filter(p => !p.status);
        if (semStatus.length > 0) {
          console.warn(`⚠️ ${semStatus.length} precatórios sem status`);

          // Definir status padrão para precatórios sem status
          for (const precatorio of semStatus) {
            precatorio.status = 'analise';

            // Buscar o status_id para 'analise'
            const { data: statusData } = await supabase
              .from('status_precatorios')
              .select('id')
              .eq('codigo', 'analise')
              .maybeSingle();

            if (statusData) {
              precatorio.status_id = statusData.id;

              // Atualizar no banco de dados
              await supabase
                .from('precatorios')
                .update({
                  status: 'analise',
                  status_id: statusData.id
                })
                .eq('id', precatorio.id);
            }
          }
        }
      } else {
        console.warn('⚠️ Nenhum precatório encontrado');
      }

      // Formatar colunas para o formato esperado pelo componente
      const colunasFormatadas = colunasData.map(coluna => {
        const colunaFormatada = {
          ...coluna,
          id: coluna.id.toString(),
          nome: coluna.nome,
          cor: coluna.cor || '#3b82f6',
          name: coluna.nome, // Para compatibilidade
          status_id: coluna.status_id,
          status_uuid: coluna.status_uuid
        };

        console.log(`Coluna formatada: ${colunaFormatada.nome} (id=${colunaFormatada.id}, status_id=${colunaFormatada.status_id}, status_uuid=${colunaFormatada.status_uuid})`);

        return colunaFormatada;
      });

      // Definir os precatórios e colunas - garantir que são arrays
      setPrecatorios(Array.isArray(precatoriosData) ? precatoriosData : []);
      setColunas(Array.isArray(colunasFormatadas) ? colunasFormatadas : []);

      console.log('✅ Dados carregados com sucesso para o kanban');

      // Chamar função de debug para verificar os dados
      setTimeout(() => {
        debugData();
      }, 500); // Pequeno delay para garantir que o estado foi atualizado
    } catch (error) {
      console.error('❌ Erro ao carregar precatórios para o kanban:', error);
      setErrorMessage('Não foi possível carregar os precatórios. Tente novamente mais tarde.');
      toast.error('Erro ao carregar precatórios', {
        description: 'Não foi possível carregar os precatórios. Tente novamente mais tarde.'
      });
    } finally {
      setLoading(false);
    }
  };

  // Filtrar precatórios por tags selecionadas
  const filteredPrecatorios = useMemo(() => {
    // Garantir que precatorios é sempre um array
    const precatoriosArray = Array.isArray(precatorios) ? precatorios : [];

    // Log para debug
    console.log('Filtrando precatórios por tags:', {
      totalPrecatorios: precatoriosArray.length,
      selectedTags,
      primeiros3Precatorios: precatoriosArray.slice(0, 3).map(p => ({
        id: p.id,
        numero: p.numero_precatorio || p.numero,
        tags: p.tags
      }))
    });

    // Se não houver tags selecionadas, retornar todos os precatórios
    if (selectedTags.length === 0) return precatoriosArray;

    // Verificar se os precatórios têm a propriedade tags definida corretamente
    const precatoriosComTags = precatoriosArray.filter(p => p.tags && Array.isArray(p.tags));
    const precatoriosSemTags = precatoriosArray.filter(p => !p.tags || !Array.isArray(p.tags));

    if (precatoriosSemTags.length > 0) {
      console.warn(`${precatoriosSemTags.length} precatórios sem tags definidas corretamente`);
    }

    // Filtrar apenas se houver tags selecionadas
    return precatoriosArray.filter(precatorio => {
      // Se o precatório não tiver tags, não incluir no resultado quando há filtro de tags
      if (!precatorio.tags || !Array.isArray(precatorio.tags)) return false;

      // Verificar se o precatório tem pelo menos uma das tags selecionadas
      return precatorio.tags.some(tagId => selectedTags.includes(tagId));
    });
  }, [precatorios, selectedTags]);

  // Filtrar precatórios por termo de busca
  const searchFilteredPrecatorios = useMemo(() => {
    // Garantir que filteredPrecatorios é sempre um array
    const precatoriosArray = Array.isArray(filteredPrecatorios) ? filteredPrecatorios : [];

    if (!searchTerm.trim()) return precatoriosArray;

    const searchLower = searchTerm.toLowerCase();
    return precatoriosArray.filter(p =>
      (p.numero?.toLowerCase() || '').includes(searchLower) ||
      (p.natureza?.toLowerCase() || '').includes(searchLower) ||
      (p.observacoes?.toLowerCase() || '').includes(searchLower) ||
      (p.cliente?.nome?.toLowerCase() || '').includes(searchLower) ||
      (p.tribunal?.toLowerCase() || '').includes(searchLower) ||
      (p.responsavel?.nome?.toLowerCase() || '').includes(searchLower)
    );
  }, [filteredPrecatorios, searchTerm]);

  // Handlers para ações de precatórios
  const handleSavePrecatorio = async (precatorio: Precatorio) => {
    try {
      await salvarPrecatorio(precatorio);

      // Recarregar dados
      if (selectedView) {
        loadPrecatoriosByView(selectedView.id);
      } else {
        loadAllPrecatorios();
      }

      toast.success('Precatório salvo', {
        description: 'O precatório foi salvo com sucesso.'
      });
    } catch (error) {
      console.error('Erro ao salvar precatório:', error);
      toast.error('Erro ao salvar precatório', {
        description: 'Não foi possível salvar o precatório. Tente novamente mais tarde.'
      });
    }
  };

  const handleDeletePrecatorio = async (id: string) => {
    try {
      await excluirPrecatorio(id);

      // Recarregar dados
      if (selectedView) {
        loadPrecatoriosByView(selectedView.id);
      } else {
        loadAllPrecatorios();
      }

      toast.success('Precatório excluído', {
        description: 'O precatório foi excluído com sucesso.'
      });
    } catch (error) {
      console.error('Erro ao excluir precatório:', error);
      toast.error('Erro ao excluir precatório', {
        description: 'Não foi possível excluir o precatório. Tente novamente mais tarde.'
      });
    }
  };

  const handleMovePrecatorio = async (precatorioId: string, novoStatusId: string) => {
    try {
      // Encontrar a coluna pelo ID para obter o nome do status
      const coluna = colunas.find(c => c.id === novoStatusId);
      if (!coluna) {
        throw new Error(`Coluna com ID ${novoStatusId} não encontrada`);
      }

      console.log(`Movendo precatório ${precatorioId} para coluna:`, coluna);

      // Atualizar o estado local imediatamente para feedback visual instantâneo
      setPrecatorios(prevPrecatorios =>
        prevPrecatorios.map(p =>
          p.id === precatorioId
            ? {
                ...p,
                status: coluna.status_id || coluna.nome.toLowerCase().replace(/\s+/g, '_'),
                status_id: coluna.status_uuid || p.status_id
              }
            : p
        )
      );

      // Determinar o valor correto para atualizar o status
      // Prioridade: status_uuid > status_id > id
      const statusValue = coluna.status_uuid || coluna.status_id || coluna.id;

      try {
        // Atualizar o status do precatório no banco de dados
        await atualizarStatusPrecatorio(precatorioId, statusValue);
        console.log(`Status do precatório ${precatorioId} atualizado para ${statusValue}`);

        // Mostrar mensagem de sucesso
        toast.success('Precatório movido', {
          description: `O precatório foi movido para ${coluna.nome} com sucesso.`
        });

        // Não recarregar dados imediatamente, confiar na atualização do estado local
        // Isso evita problemas de flickering e recargas desnecessárias
      } catch (updateError) {
        console.error('Erro ao atualizar status do precatório:', updateError);

        // Em caso de erro na atualização, recarregar dados para restaurar o estado correto
        toast.error('Erro ao mover precatório', {
          description: 'Houve um problema ao salvar a alteração. Recarregando dados...'
        });

        // Recarregar dados após um pequeno delay
        setTimeout(() => {
          if (selectedView) {
            loadPrecatoriosByView(selectedView.id);
          } else {
            loadAllPrecatorios(0, false); // Não limpar filtros ao recarregar
          }
        }, 1000);
      }
    } catch (error) {
      console.error('Erro ao mover precatório:', error);
      toast.error('Erro ao mover precatório', {
        description: 'Não foi possível mover o precatório. Tente novamente mais tarde.'
      });
    }
  };

  // Handlers para tags
  const handleTagSelect = (tagId: string) => {
    setSelectedTags(prev =>
      prev.includes(tagId)
        ? prev.filter(id => id !== tagId)
        : [...prev, tagId]
    );
  };

  // Handlers para visualizações
  const handleViewSelect = (view: CustomView) => {
    console.log('Selecionando visualização:', view);
    setSelectedView(view);

    // Limpar a busca ao trocar de visualização
    setSearchTerm("");

    // Verificar se é a visualização "Todos" (identificada pelo nome ou por não ter filtros)
    const isAllView = view.nome?.toLowerCase() === 'todos' ||
                      view.nome?.toLowerCase() === 'all' ||
                      view.nome?.toLowerCase() === 'tudo' ||
                      (!view.tags_selecionadas || view.tags_selecionadas.length === 0);

    // Atualizar tags selecionadas
    if (isAllView) {
      // Se for a visualização "Todos", limpar todas as tags selecionadas
      console.log('Visualização "Todos" selecionada, limpando todas as tags');
      setSelectedTags([]);
    } else if (view.tags_selecionadas && view.tags_selecionadas.length > 0) {
      console.log(`Aplicando ${view.tags_selecionadas.length} tags da visualização "${view.nome}"`);
      setSelectedTags(view.tags_selecionadas);
    } else {
      setSelectedTags([]);
    }

    // Atualizar modo de visualização
    if (view.layout && ['kanban', 'list', 'table'].includes(view.layout as any)) {
      setViewMode(view.layout as 'kanban' | 'list' | 'table');
    }

    // Carregar precatórios com base na visualização
    loadPrecatoriosByView(view.id);

    toast.success(`Visualização "${view.nome}" selecionada`, {
      description: isAllView
        ? 'Mostrando todos os precatórios sem filtros.'
        : 'Os precatórios foram filtrados conforme a visualização selecionada.'
    });
  };

  // Renderizar loading
  const renderLoading = () => (
    <div className="flex flex-col items-center justify-center h-[calc(100vh-200px)]">
      <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
      <p className="text-muted-foreground">Carregando precatórios...</p>
    </div>
  );

  // Renderizar erro
  const renderError = () => (
    <div className="flex flex-col items-center justify-center h-[calc(100vh-200px)]">
      <div className="bg-destructive/10 p-4 rounded-lg mb-4">
        <X className="h-12 w-12 text-destructive mx-auto mb-2" />
        <p className="text-center text-destructive font-medium">{errorMessage}</p>
      </div>
      <Button onClick={selectedView ? () => loadPrecatoriosByView(selectedView.id) : () => loadAllPrecatorios(0, true)}>
        Tentar novamente
      </Button>
    </div>
  );

  // Renderizar tags
  const renderTags = () => (
    <div className="flex flex-wrap gap-2 mb-4">
      {tags.map(tag => (
        <Badge
          key={tag.id}
          variant={selectedTags.includes(tag.id) ? "default" : "outline"}
          className={cn(
            "cursor-pointer hover:bg-muted/80 transition-colors",
            selectedTags.includes(tag.id) && "bg-primary text-primary-foreground"
          )}
          style={{
            backgroundColor: selectedTags.includes(tag.id) ? tag.cor : 'transparent',
            borderColor: tag.cor,
            color: selectedTags.includes(tag.id) ? '#fff' : tag.cor
          }}
          onClick={() => handleTagSelect(tag.id)}
        >
          {tag.nome}
        </Badge>
      ))}

      {tagsLoading && (
        <div className="flex items-center gap-2 text-muted-foreground">
          <Loader2 className="h-3 w-3 animate-spin" />
          <span className="text-xs">Carregando tags...</span>
        </div>
      )}

      {!tagsLoading && tags.length === 0 && (
        <div className="text-sm text-muted-foreground">
          Nenhuma tag disponível. Crie tags para organizar seus precatórios.
        </div>
      )}
    </div>
  );

  return (
    <div className="flex flex-col h-screen w-screen overflow-hidden">
      <TopNav
        title="Kanban Precatório/RPV"
        icon={<KanbanSquare className="h-6 w-6 text-primary" />}
      >
        {/* Seletor de visualizações */}
        <div className="flex items-center gap-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="gap-2">
                <Eye className="h-4 w-4" />
                <span>{selectedView?.nome || "Visualização Padrão"}</span>
                <ChevronDown className="h-4 w-4 opacity-50" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>Visualizações</DropdownMenuLabel>
              <DropdownMenuSeparator />

              {viewsLoading ? (
                <div className="flex items-center justify-center p-4">
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  <span>Carregando...</span>
                </div>
              ) : views.length === 0 ? (
                <div className="p-3 text-sm text-muted-foreground text-center">
                  Nenhuma visualização disponível
                </div>
              ) : (
                <>
                  {/* Visualizações favoritas */}
                  {views.filter(v => v.is_favorite).length > 0 && (
                    <>
                      <DropdownMenuLabel className="text-xs flex items-center gap-1">
                        <Star className="h-3 w-3 text-amber-500 fill-amber-500" />
                        Favoritas
                      </DropdownMenuLabel>
                      {views
                        .filter(v => v.is_favorite)
                        .map(view => (
                          <DropdownMenuItem
                            key={view.id}
                            className="flex items-center gap-2 cursor-pointer"
                            onClick={() => handleViewSelect(view)}
                          >
                            <div
                              className="w-3 h-3 rounded-full"
                              style={{ backgroundColor: view.cor || '#3b82f6' }}
                            />
                            <span>{view.nome}</span>
                            {view.is_default && (
                              <Badge variant="secondary" className="ml-auto text-xs py-0">Padrão</Badge>
                            )}
                          </DropdownMenuItem>
                        ))}
                      <DropdownMenuSeparator />
                    </>
                  )}

                  {/* Outras visualizações */}
                  <DropdownMenuLabel className="text-xs">Todas as Visualizações</DropdownMenuLabel>
                  {views
                    .filter(v => !v.is_favorite)
                    .map(view => (
                      <DropdownMenuItem
                        key={view.id}
                        className="flex items-center gap-2 cursor-pointer"
                        onClick={() => handleViewSelect(view)}
                      >
                        <div
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: view.cor || '#3b82f6' }}
                        />
                        <span>{view.nome}</span>
                        {view.is_default && (
                          <Badge variant="secondary" className="ml-auto text-xs py-0">Padrão</Badge>
                        )}
                      </DropdownMenuItem>
                    ))}
                </>
              )}

              <DropdownMenuSeparator />
              <DropdownMenuItem
                className="flex items-center gap-2 cursor-pointer"
                onClick={() => setIsViewsDialogOpen(true)}
              >
                <Settings className="h-4 w-4" />
                <span>Gerenciar Visualizações</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                className="flex items-center gap-2 cursor-pointer"
                onClick={() => setIsCreateViewDialogOpen(true)}
              >
                <PlusCircle className="h-4 w-4" />
                <span>Nova Visualização</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>


        </div>

        {/* Botões de ações */}
        <div className="flex items-center gap-2">
          <Button
            variant={selectedTags.length > 0 || searchTerm ? "default" : "outline"}
            size="sm"
            className={cn(
              "gap-2",
              selectedTags.length > 0 || searchTerm
                ? "bg-primary text-primary-foreground hover:bg-primary/90"
                : ""
            )}
            onClick={() => setShowFilters(!showFilters)}
          >
            <Filter className="h-4 w-4" />
            <span className="hidden md:inline">
              Filtros
              {(selectedTags.length > 0 || searchTerm) && (
                <span className="ml-1">
                  ({selectedTags.length > 0 ? `${selectedTags.length} tags` : ""}
                  {selectedTags.length > 0 && searchTerm ? ", " : ""}
                  {searchTerm ? "busca" : ""})
                </span>
              )}
            </span>
          </Button>

          <Button
            variant="outline"
            size="sm"
            className="gap-2"
            onClick={() => setIsTagsDialogOpen(true)}
          >
            <TagIcon className="h-4 w-4" />
            <span className="hidden md:inline">Tags</span>
          </Button>



          {isAdmin && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <Settings className="h-4 w-4 mr-2" />
                  <span className="hidden md:inline">Configurações</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => setIsColumnsDialogOpen(true)}>
                  <LayoutGrid className="h-4 w-4 mr-2" />
                  Gerenciar Colunas
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setIsTagsDialogOpen(true)}>
                  <TagIcon className="h-4 w-4 mr-2" />
                  Gerenciar Tags
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setIsViewsDialogOpen(true)}>
                  <Eye className="h-4 w-4 mr-2" />
                  Gerenciar Visualizações
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => setIsDeletedItemsDialogOpen(true)}>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Itens Excluídos
                </DropdownMenuItem>

              </DropdownMenuContent>
            </DropdownMenu>
          )}

          <Button size="sm" onClick={() => {
            // Usar diretamente o evento personalizado para abrir o modal
            const event = new CustomEvent('open-new-precatorio-modal');
            document.dispatchEvent(event);
          }}>
            <Plus className="h-4 w-4 mr-2" />
            <span className="hidden md:inline">Novo Precatório</span>
          </Button>
        </div>
      </TopNav>

      {/* Área de filtros e tags */}
      {showFilters && (
        <div className="border-b bg-muted/30 px-4 py-3">
          <div className="flex flex-col md:flex-row gap-4 items-start md:items-center">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Buscar precatórios..."
                className="pl-9 pr-9"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              {searchTerm && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute right-1 top-1 h-8 w-8"
                  onClick={() => setSearchTerm("")}
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>

            <ScrollArea className="w-full md:w-auto max-w-full">
              {renderTags()}
            </ScrollArea>
          </div>
        </div>
      )}

      {/* Área principal de conteúdo */}
      <main className="flex-1 overflow-auto bg-muted/20 pt-[75px]">
        {loading ? renderLoading() :
         errorMessage ? renderError() :
         (
           <div className="p-4 kanban-container">

               {searchFilteredPrecatorios.length === 0 && (
                 <div className="flex justify-end mb-2">
                   {(selectedTags.length > 0 || searchTerm) && (
                     <Button
                       variant="outline"
                       size="sm"
                       onClick={() => {
                         setSelectedTags([]);
                         setSearchTerm("");
                         loadAllPrecatorios(0, true);
                       }}
                     >
                       <FilterX className="h-3.5 w-3.5 mr-1" />
                       Limpar Filtros
                     </Button>
                   )}
                 </div>
               )}

               <KanbanContainer
                 todosPrecatorios={searchFilteredPrecatorios} // Usar precatórios filtrados por busca e tags
                 colunas={colunas}
                 onSavePrecatorio={handleSavePrecatorio}
                 onDeletePrecatorio={handleDeletePrecatorio}
                 onMovePrecatorio={handleMovePrecatorio}
               />
           </div>
         )
        }
      </main>

      {/* Modais */}
      {/* Modal de Gerenciamento de Tags */}
      <Dialog open={isTagsDialogOpen} onOpenChange={setIsTagsDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Gerenciamento de Tags</DialogTitle>
            <DialogDescription>
              Crie e gerencie tags para organizar seus precatórios
            </DialogDescription>
          </DialogHeader>
          <TagManager onTagsChange={loadTags} />
        </DialogContent>
      </Dialog>

      {/* Modal de Gerenciamento de Colunas */}
      <Dialog open={isColumnsDialogOpen} onOpenChange={setIsColumnsDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Gerenciamento de Colunas</DialogTitle>
            <DialogDescription>
              Configure as colunas que serão exibidas no quadro Kanban
            </DialogDescription>
          </DialogHeader>
          <KanbanColumnManager onColumnsChange={() => {
            // Recarregar dados
            if (selectedView) {
              loadPrecatoriosByView(selectedView.id);
            } else {
              loadAllPrecatorios();
            }
          }} />
        </DialogContent>
      </Dialog>

      {/* Modal de Gerenciamento de Visualizações */}
      <Dialog open={isViewsDialogOpen} onOpenChange={setIsViewsDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Gerenciamento de Visualizações</DialogTitle>
            <DialogDescription>
              Crie e gerencie visualizações personalizadas para o quadro Kanban
            </DialogDescription>
          </DialogHeader>
          <CustomViewManager onViewsChange={loadViews} />
        </DialogContent>
      </Dialog>

      {/* Modal de Itens Excluídos */}
      <Dialog open={isDeletedItemsDialogOpen} onOpenChange={setIsDeletedItemsDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Itens Excluídos</DialogTitle>
            <DialogDescription>
              Recupere itens que foram excluídos recentemente
            </DialogDescription>
          </DialogHeader>
          <DeletedItemsManager onItemRestored={() => {
            // Recarregar dados
            if (selectedView) {
              loadPrecatoriosByView(selectedView.id);
            } else {
              loadAllPrecatorios();
            }
            loadTags();
            loadViews();
          }} />
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default PrecatoriosKanbanNew;
