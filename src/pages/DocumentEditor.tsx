import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate, useLocation } from "react-router-dom";
import { TopNav } from "@/components/top-nav";
import {
  FileText,
  ArrowLeft,
  Save,
  X,
  User,
  FileSpreadsheet,
  Tag,
  Calendar,
  Clock,
  CheckCircle,
  XCircle,
  Loader2,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import {
  buscarDocumentoPorId,
  Documento,
  criarNovoDocumento,
  atualizarDocumento,
} from "@/services/documentosService";
import { buscarClientes } from "@/modules/common/services/clientesService";
import { buscarPrecatorios } from "@/services/precatoriosService";
import { RichTextEditor } from "@/components/RichTextEditor";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";

// Schema de validação
const formSchema = z.object({
  nome: z.string()
    .min(3, { message: 'O nome deve ter pelo menos 3 caracteres' })
    .max(100, { message: 'O nome não pode ter mais de 100 caracteres' })
    .refine(val => val.trim().length > 0, { message: 'O nome não pode conter apenas espaços' }),
  conteudo: z.string()
    .min(1, { message: 'O conteúdo não pode estar vazio' })
    .refine(val => val.trim().length > 0, { message: 'O conteúdo não pode conter apenas espaços ou HTML vazio' }),
  tipo: z.string()
    .min(1, { message: 'Selecione o tipo de documento' }),
  categoria: z.enum(['cliente', 'precatorio'], {
    required_error: 'Selecione a categoria'
  }),
  clienteId: z.string().optional(),
  precatorioId: z.string().optional(),
  tags: z.array(z.string()).optional(),
  status: z.enum(['pendente', 'aprovado', 'rejeitado']).default('pendente'),
}).refine(data => {
  if (data.categoria === 'cliente') {
    return !!data.clienteId;
  }
  if (data.categoria === 'precatorio') {
    return !!data.precatorioId;
  }
  return true;
}, {
  message: 'Selecione um cliente ou precatório',
  path: ['clienteId'],
});

export default function DocumentEditorPage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const isNewDocument = !id || id === 'novo';

  const [documento, setDocumento] = useState<Documento | null>(null);
  const [clientes, setClientes] = useState<any[]>([]);
  const [precatorios, setPrecatorios] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [activeTab, setActiveTab] = useState("editor");
  const [editorContent, setEditorContent] = useState("");

  // Inicializar formulário
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      nome: '',
      conteudo: '',
      tipo: '',
      categoria: 'cliente',
      clienteId: '',
      precatorioId: '',
    },
  });

  // Carregar dados iniciais
  useEffect(() => {
    const carregarDados = async () => {
      try {
        setIsLoading(true);

        // Carregar clientes e precatórios para os dropdowns
        const clientesData = await buscarClientes();
        setClientes(clientesData);

        const precatoriosData = await buscarPrecatorios();
        setPrecatorios(precatoriosData);

        // Se for edição, carregar documento existente
        if (!isNewDocument) {
          const doc = await buscarDocumentoPorId(id);
          if (doc) {
            setDocumento(doc);
            setEditorContent(doc.conteudo || "");

            // Preencher formulário com dados do documento
            form.reset({
              nome: doc.nome,
              conteudo: doc.conteudo || "",
              tipo: doc.tipo,
              categoria: doc.cliente_id ? 'cliente' : 'precatorio',
              clienteId: doc.cliente_id || "",
              precatorioId: doc.precatorio_id || "",
            });
          }
        } else {
          // Verificar se há parâmetros na URL para pré-preencher o formulário
          const params = new URLSearchParams(location.search);
          const clienteId = params.get('clienteId');
          const precatorioId = params.get('precatorioId');

          if (clienteId) {
            form.setValue('categoria', 'cliente');
            form.setValue('clienteId', clienteId);
          } else if (precatorioId) {
            form.setValue('categoria', 'precatorio');
            form.setValue('precatorioId', precatorioId);
          }
        }
      } catch (error) {
        console.error("Erro ao carregar dados:", error);
        toast.error("Erro ao carregar dados");
      } finally {
        setIsLoading(false);
      }
    };

    carregarDados();
  }, [id, isNewDocument, form, location.search]);

  // Atualizar o conteúdo do editor quando o formulário mudar
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === 'conteudo') {
        setEditorContent(value.conteudo || "");
      }
    });

    return () => subscription.unsubscribe();
  }, [form]);

  // Função para salvar o documento
  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      setIsSaving(true);

      let resultado: Documento;

      if (isNewDocument) {
        // Criar novo documento
        if (values.categoria === 'cliente' && values.clienteId) {
          resultado = await criarNovoDocumento(
            values.nome,
            values.conteudo,
            values.tipo,
            values.clienteId
          );
        } else if (values.categoria === 'precatorio' && values.precatorioId) {
          resultado = await criarNovoDocumento(
            values.nome,
            values.conteudo,
            values.tipo,
            undefined,
            values.precatorioId
          );
        } else {
          throw new Error('Categoria ou ID inválido');
        }

        toast.success("Documento criado com sucesso");
      } else {
        // Atualizar documento existente
        if (!documento?.id) {
          throw new Error('ID do documento não encontrado');
        }

        resultado = await atualizarDocumento(
          documento.id,
          {
            nome: values.nome,
            conteudo: values.conteudo,
            tipo: values.tipo,
            cliente_id: values.categoria === 'cliente' ? values.clienteId : undefined,
            precatorio_id: values.categoria === 'precatorio' ? values.precatorioId : undefined,
          },
          documento.categoria as 'cliente' | 'precatorio'
        );

        toast.success("Documento atualizado com sucesso");
      }

      // Navegar para a página de visualização do documento
      navigate(`/documents/${resultado.id}`);
    } catch (error) {
      console.error("Erro ao salvar documento:", error);
      toast.error("Erro ao salvar documento");
    } finally {
      setIsSaving(false);
    }
  };

  // Função para cancelar e voltar
  const handleCancel = () => {
    if (isNewDocument) {
      navigate("/documents");
    } else {
      navigate(`/documents/${id}`);
    }
  };

  // Renderizar esqueleto de carregamento
  if (isLoading) {
    return (
      <div className="flex flex-col h-screen w-screen overflow-hidden">
        <TopNav
          title={isNewDocument ? "Novo Documento" : "Editando Documento"}
          icon={<FileText className="h-6 w-6 text-primary" />}
        />

        <div className="flex flex-1 overflow-auto pt-[65px]">
          <div className="p-4 md:p-6 rounded-tl-2xl border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-900 flex flex-col gap-6 flex-1 w-full h-full">
            <div className="flex items-center gap-4">
              <Button variant="outline" size="icon" onClick={handleCancel}>
                <ArrowLeft className="h-4 w-4" />
              </Button>
              <Skeleton className="h-8 w-64" />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="md:col-span-3">
                <Card>
                  <CardHeader>
                    <Skeleton className="h-6 w-32" />
                  </CardHeader>
                  <CardContent>
                    <Skeleton className="h-[500px] w-full" />
                  </CardContent>
                </Card>
              </div>

              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <Skeleton className="h-6 w-32" />
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-full" />
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-screen w-screen overflow-hidden">
      <TopNav
        title={isNewDocument ? "Novo Documento" : "Editando Documento"}
        icon={<FileText className="h-6 w-6 text-primary" />}
      />

      <div className="flex flex-1 overflow-auto pt-[65px]">
        <div className="p-4 md:p-6 rounded-tl-2xl border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-900 flex flex-col gap-6 flex-1 w-full h-full">
          {/* Cabeçalho simplificado */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-4">
              <Button variant="ghost" size="icon" onClick={handleCancel} className="rounded-full">
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <h1 className="text-3xl font-bold">
                {isNewDocument ? "Novo Documento" : "Editar Documento"}
              </h1>
            </div>

            <Button variant="outline" size="sm" onClick={handleCancel}>
              Cancelar
            </Button>
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* Conteúdo principal */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                {/* Editor e Propriedades */}
                <div className="md:col-span-3 space-y-6">
                  {/* Nome do documento */}
                  <FormField
                    control={form.control}
                    name="nome"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center gap-1">
                          <FormLabel className="text-2xl font-medium">Nome do Documento</FormLabel>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger className="cursor-help">
                                <span className="text-xs text-muted-foreground hover:text-primary">(?)</span>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p className="max-w-xs">Nome do documento para identificação. Deve ter pelo menos 3 caracteres.</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                        <FormControl>
                          <Input
                            placeholder="Digite o nome do documento..."
                            className={`text-xl p-4 h-14 ${form.formState.errors.nome ? 'border-red-500 focus-visible:ring-red-500' : ''}`}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Editor de texto */}
                  <FormField
                    control={form.control}
                    name="conteudo"
                    render={({ field }) => (
                      <FormItem className="mt-6">
                        <div className="flex items-center gap-1">
                          <FormLabel className="text-xl font-medium">Conteúdo</FormLabel>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger className="cursor-help">
                                <span className="text-xs text-muted-foreground hover:text-primary">(?)</span>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p className="max-w-xs">Conteúdo principal do documento. Use as ferramentas de formatação para melhorar a apresentação.</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                        <FormControl>
                          <div className={`border ${form.formState.errors.conteudo ? 'border-red-500' : 'border-input'} rounded-lg overflow-hidden bg-white dark:bg-neutral-900 flex flex-col`} style={{ minHeight: 'calc(100vh - 350px)' }}>
                            <RichTextEditor
                              initialContent={field.value}
                              onChange={(html) => {
                                field.onChange(html);
                                setEditorContent(html);
                              }}
                              placeholder="Comece a escrever seu documento aqui..."
                              minHeight="100%"
                              className="border-0 focus-within:ring-0 focus-within:ring-offset-0 flex-grow"
                            />
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Painel lateral simplificado */}
                <div className="space-y-6">
                  <div className="bg-white dark:bg-neutral-900 rounded-lg p-6 border border-input">
                    <h3 className="text-xl font-medium mb-4">Propriedades</h3>

                    {/* Tipo de documento */}
                    <FormField
                      control={form.control}
                      name="tipo"
                      render={({ field }) => (
                        <FormItem className="mb-6">
                          <div className="flex items-center gap-1">
                            <FormLabel>Tipo de Documento</FormLabel>
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger className="cursor-help">
                                  <span className="text-xs text-muted-foreground hover:text-primary">(?)</span>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p className="max-w-xs">Selecione o tipo de documento para facilitar a organização e busca.</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </div>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                            value={field.value}
                          >
                            <FormControl>
                              <SelectTrigger className={!field.value ? "border-red-500" : ""}>
                                <SelectValue placeholder="Selecione o tipo de documento" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="contrato">Contrato</SelectItem>
                              <SelectItem value="peticao">Petição</SelectItem>
                              <SelectItem value="procuracao">Procuração</SelectItem>
                              <SelectItem value="declaracao">Declaração</SelectItem>
                              <SelectItem value="termo">Termo</SelectItem>
                              <SelectItem value="relatorio">Relatório</SelectItem>
                              <SelectItem value="parecer">Parecer</SelectItem>
                              <SelectItem value="oficio">Ofício</SelectItem>
                              <SelectItem value="outros">Outros</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Categoria (Cliente ou Precatório) */}
                    <FormField
                      control={form.control}
                      name="categoria"
                      render={({ field }) => (
                        <FormItem className="mb-6">
                          <div className="flex items-center gap-1">
                            <FormLabel>Associar a</FormLabel>
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger className="cursor-help">
                                  <span className="text-xs text-muted-foreground hover:text-primary">(?)</span>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p className="max-w-xs">Escolha se este documento está associado a um cliente ou a um precatório específico.</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </div>
                          <FormControl>
                            <RadioGroup
                              onValueChange={field.onChange}
                              value={field.value}
                              className="flex gap-4"
                            >
                              <div className="flex items-center space-x-2">
                                <RadioGroupItem value="cliente" id="cliente-radio" />
                                <Label htmlFor="cliente-radio">Cliente</Label>
                              </div>
                              <div className="flex items-center space-x-2">
                                <RadioGroupItem value="precatorio" id="precatorio-radio" />
                                <Label htmlFor="precatorio-radio">Precatório</Label>
                              </div>
                            </RadioGroup>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Cliente ou Precatório */}
                    {form.watch('categoria') === 'cliente' ? (
                      <FormField
                        control={form.control}
                        name="clienteId"
                        render={({ field }) => (
                          <FormItem className="mb-6">
                            <div className="flex items-center gap-1">
                              <FormLabel>Cliente</FormLabel>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger className="cursor-help">
                                    <span className="text-xs text-muted-foreground hover:text-primary">(?)</span>
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p className="max-w-xs">Selecione o cliente ao qual este documento pertence.</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                            <Select
                              onValueChange={field.onChange}
                              value={field.value}
                            >
                              <FormControl>
                                <SelectTrigger className={!field.value ? "border-red-500" : ""}>
                                  <SelectValue placeholder="Selecione um cliente" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {clientes.map((cliente) => (
                                  <SelectItem key={cliente.id} value={cliente.id}>
                                    {cliente.nome}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    ) : (
                      <FormField
                        control={form.control}
                        name="precatorioId"
                        render={({ field }) => (
                          <FormItem className="mb-6">
                            <div className="flex items-center gap-1">
                              <FormLabel>Precatório</FormLabel>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger className="cursor-help">
                                    <span className="text-xs text-muted-foreground hover:text-primary">(?)</span>
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p className="max-w-xs">Selecione o precatório ao qual este documento pertence.</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                            <Select
                              onValueChange={field.onChange}
                              value={field.value}
                            >
                              <FormControl>
                                <SelectTrigger className={!field.value ? "border-red-500" : ""}>
                                  <SelectValue placeholder="Selecione um precatório" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {precatorios.map((precatorio) => (
                                  <SelectItem key={precatorio.id} value={precatorio.id}>
                                    {precatorio.numero_precatorio || precatorio.id}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}

                    {/* Status do documento */}
                    <FormField
                      control={form.control}
                      name="status"
                      render={({ field }) => (
                        <FormItem className="mb-6">
                          <div className="flex items-center gap-1">
                            <FormLabel>Status</FormLabel>
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger className="cursor-help">
                                  <span className="text-xs text-muted-foreground hover:text-primary">(?)</span>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p className="max-w-xs">Define o status atual do documento no sistema.</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </div>
                          <Select
                            onValueChange={field.onChange}
                            value={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Selecione o status" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="pendente">Pendente</SelectItem>
                              <SelectItem value="aprovado">Aprovado</SelectItem>
                              <SelectItem value="rejeitado">Rejeitado</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <Button
                      type="submit"
                      className="w-full mt-6"
                      disabled={isSaving}
                    >
                      {isSaving ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Salvando...
                        </>
                      ) : (
                        <>
                          <Save className="h-4 w-4 mr-2" />
                          Salvar Documento
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </div>
            </form>
          </Form>
        </div>
      </div>
    </div>
  );
}
