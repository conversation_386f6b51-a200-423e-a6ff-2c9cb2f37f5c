import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { UserPlus, Pencil, UserCheck, Users, AlertCircle, RefreshCw, User, Settings, Search, Filter, Eye, Edit, Trash2, Shield, Plus, Database } from "lucide-react";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/lib/supabase";
import { toast } from "sonner";
import { useAuth } from "@/contexts/AuthContext";
import { TopNav } from "@/components/top-nav";
import { UserPermissionsManager } from "@/components/users/UserPermissionsManager";
import { FullScreenLoading } from "@/components/ui/loading-indicator";
import { AdvancedPermissionsManager } from "@/components/users/AdvancedPermissionsManager";
import { UserDetailsModal } from "@/components/users/UserDetailsModal";
import { UserSpecificPermissions } from "@/components/users/UserSpecificPermissions";
import { EditUserModal } from "@/components/users/EditUserModal";
import { DeleteUserModal } from "@/components/users/DeleteUserModal";
import { DataVisibilityManager } from "@/components/users/DataVisibilityManager";

// Novos tipos de papel conforme a estrutura solicitada
type UserRole =
  | "admin"  // Administrador (ADC)
  | "gerente_geral"  // Gerente Geral de Precatórios e RPVs
  | "gerente_precatorio"  // Gerente de Precatório
  | "gerente_rpv"  // Gerente de RPV
  | "captador"  // Captador
  | "operacional_precatorio"  // Funcionário Operacional de Precatório
  | "operacional_rpv"  // Funcionário Operacional de RPV
  | "operacional_completo";  // Funcionário Operacional Completo (Precatório + RPV)

interface UserProfile {
  id: string;
  email: string;
  nome?: string;
  role: UserRole;
  foto_url?: string;
  status?: string;
  created_at?: string;
  cargo?: string;
  departamento?: string;
  telefone?: string;
  data_entrada?: string;
  custom_role_id?: string;
}

export default function UserManagement() {
  const [users, setUsers] = useState<UserProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [isAddUserOpen, setIsAddUserOpen] = useState(false);
  const [isEditRoleOpen, setIsEditRoleOpen] = useState(false);
  const [isPermissionsOpen, setIsPermissionsOpen] = useState(false);
  const [isUserDetailsOpen, setIsUserDetailsOpen] = useState(false);
  const [isUserSpecificPermissionsOpen, setIsUserSpecificPermissionsOpen] = useState(false);
  const [isEditUserOpen, setIsEditUserOpen] = useState(false);
  const [isDeleteUserOpen, setIsDeleteUserOpen] = useState(false);
  const [isDataVisibilityOpen, setIsDataVisibilityOpen] = useState(false);
  const [currentUser, setCurrentUser] = useState<UserProfile | null>(null);
  const [selectedUser, setSelectedUser] = useState<UserProfile | null>(null);
  const [newRole, setNewRole] = useState<UserRole>("captador");

  // Estados para funcionalidades avançadas
  const [searchTerm, setSearchTerm] = useState("");
  const [roleFilter, setRoleFilter] = useState<UserRole | "all">("all");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [sortBy, setSortBy] = useState<"name" | "email" | "role" | "created_at">("name");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [activeTab, setActiveTab] = useState("users");
  const [newUser, setNewUser] = useState<{
    nome: string;
    email: string;
    password: string;
    role: UserRole;
    cargo?: string;
    departamento?: string;
    foto_url?: string;
    data_entrada?: string;
    telefone?: string;
    status?: string;
  }>({
    nome: "",
    email: "",
    password: "",
    role: "captador",
    cargo: "",
    departamento: "",
    foto_url: "",
    data_entrada: new Date().toISOString().split('T')[0],
    telefone: "",
    status: "ativo",
  });
  const [addingUser, setAddingUser] = useState(false);
  const navigate = useNavigate();
  const { getAllUsers, updateUserPermissions, createUser, updateUser, deleteUser } = useAuth();

  // Verificar permissões do usuário atual
  useEffect(() => {
    try {
      const userProfileStr = localStorage.getItem('userProfile');
      if (userProfileStr) {
        try {
          const userProfile = JSON.parse(userProfileStr);
          setCurrentUser(userProfile);

          // Carregar lista de usuários independentemente do papel
          // A verificação de admin será feita na renderização
          console.log("UserManagement: Usuário carregado, carregando lista de usuários...");
          loadUsers();
        } catch (parseError) {
          console.error("Erro ao analisar perfil do localStorage:", parseError);
          navigate("/login");
        }
      } else {
        console.log("UserManagement: Nenhum perfil encontrado, redirecionando para login");
        navigate("/login");
      }
    } catch (error) {
      console.error("Erro ao verificar permissões:", error);
      navigate("/dashboard");
    }
  }, [navigate]);

  // Efeito para carregar usuários ao montar o componente
  useEffect(() => {
    // Limpar o cache para garantir que as alterações sejam aplicadas
    localStorage.removeItem('cached_users');

    loadUsers();
  }, []);

  // Adicionar um efeito adicional para tentar novamente após 2 segundos
  useEffect(() => {
    // Se não houver usuários mas o usuário atual é admin, tentar carregar novamente
    if (users.length === 0 && currentUser?.role === 'admin' && !loading) {
      console.log("UserManagement: Tentando carregar usuários novamente após delay...");
      const timer = setTimeout(() => {
        loadUsers();
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [users.length, currentUser, loading]);

  // Carregar lista de usuários de forma segura (evitando recursão infinita)
  const loadUsers = async () => {
    console.log("UserManagement: Iniciando carregamento de usuários...");
    try {
      setLoading(true);

      // Verificar se o usuário atual é admin antes de tentar carregar usuários do backend
      const userProfileStr = localStorage.getItem('userProfile');
      let isAdmin = false;

      if (userProfileStr) {
        try {
          const userProfile = JSON.parse(userProfileStr);
          isAdmin = userProfile.role === 'admin';
        } catch (e) {
          console.error("UserManagement: Erro ao verificar perfil do usuário:", e);
        }
      }

      // Se não for admin, usar dados mockados diretamente
      if (!isAdmin) {
        console.log("UserManagement: Usuário não é admin, usando dados mockados");
        // Criar usuários mockados
      console.log("UserManagement: Criando usuários mockados");

      // Incluir o usuário atual
      const mockUsers: UserProfile[] = [];

      if (currentUser) {
        // Verificar se o ID do usuário atual é um UUID válido
        const isValidUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(currentUser.id);

        if (isValidUUID) {
          mockUsers.push(currentUser);
        } else {
          // Converter para UUID válido
          const mockUUID = `00000000-0000-0000-0000-${currentUser.id.toString().padStart(12, '0')}`;
          mockUsers.push({
            ...currentUser,
            id: mockUUID
          });
        }
      }

      // Adicionar alguns usuários mockados para demonstração
      if (!mockUsers.some(u => u.role === 'admin')) {
        mockUsers.push({
          id: '00000000-0000-0000-0000-000000000001',
          email: '<EMAIL>',
          nome: 'Administrador',
          role: 'admin',
          status: 'ativo',
          created_at: new Date().toISOString()
        });
      }

      mockUsers.push({
        id: '00000000-0000-0000-0000-000000000002',
        email: '<EMAIL>',
        nome: 'Gerente',
        role: 'gerente_geral',
        status: 'ativo',
        created_at: new Date().toISOString()
      });

      mockUsers.push({
        id: '00000000-0000-0000-0000-000000000003',
        email: '<EMAIL>',
        nome: 'Operacional',
        role: 'operacional_completo',
        status: 'ativo',
        created_at: new Date().toISOString()
      });

      // Salvar no cache para uso futuro
      localStorage.setItem('cached_users', JSON.stringify(mockUsers));

      setUsers(mockUsers);
      setLoading(false);
        return;
      }

      // Verificar se há usuários em cache
      const cachedUsersStr = localStorage.getItem('cached_users');
      if (cachedUsersStr) {
        try {
          const cachedUsers = JSON.parse(cachedUsersStr);
          console.log("UserManagement: Usando", cachedUsers.length, "usuários em cache");

          // Verificar se os IDs dos usuários em cache são válidos
          const validatedUsers = cachedUsers.map(user => {
            // Verificar se o ID é um UUID válido
            const isValidUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(user.id);

            // Se não for válido e for um número ou string simples, converter para formato UUID
            if (!isValidUUID) {
              console.log("UserManagement: Convertendo ID inválido:", user.id);
              // Criar um UUID baseado no ID original
              const mockUUID = `00000000-0000-0000-0000-${user.id.toString().padStart(12, '0')}`;
              return { ...user, id: mockUUID };
            }

            return user;
          });

          if (validatedUsers.length > 0) {
            // Atualizar o cache com os IDs corrigidos
            localStorage.setItem('cached_users', JSON.stringify(validatedUsers));
            setUsers(validatedUsers);
            setLoading(false);
            return;
          }
        } catch (e) {
          console.error("UserManagement: Erro ao analisar usuários em cache:", e);
        }
      }

      // Tentar buscar usuários do AuthContext
      try {
        console.log("UserManagement: Chamando getAllUsers do AuthContext...");
        const { data: fetchedUsers, error } = await getAllUsers();

        if (error) {
          console.error("UserManagement: Erro ao carregar usuários:", error);
          // Não lançar erro, apenas usar dados mockados
          console.log("UserManagement: Usando dados mockados devido a erro");
          // Criar usuários mockados
          console.log("UserManagement: Criando usuários mockados");

          // Incluir o usuário atual
          const mockUsers: UserProfile[] = [];

          if (currentUser) {
            // Verificar se o ID do usuário atual é um UUID válido
            const isValidUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(currentUser.id);

            if (isValidUUID) {
              mockUsers.push(currentUser);
            } else {
              // Converter para UUID válido
              const mockUUID = `00000000-0000-0000-0000-${currentUser.id.toString().padStart(12, '0')}`;
              mockUsers.push({
                ...currentUser,
                id: mockUUID
              });
            }
          }

          // Adicionar alguns usuários mockados para demonstração
          if (!mockUsers.some(u => u.role === 'admin')) {
            mockUsers.push({
              id: '00000000-0000-0000-0000-000000000001',
              email: '<EMAIL>',
              nome: 'Administrador',
              role: 'admin',
              status: 'ativo',
              created_at: new Date().toISOString()
            });
          }

          mockUsers.push({
            id: '00000000-0000-0000-0000-000000000002',
            email: '<EMAIL>',
            nome: 'Gerente',
            role: 'gerente_geral',
            status: 'ativo',
            created_at: new Date().toISOString()
          });

          mockUsers.push({
            id: '00000000-0000-0000-0000-000000000003',
            email: '<EMAIL>',
            nome: 'Operacional',
            role: 'operacional_completo',
            status: 'ativo',
            created_at: new Date().toISOString()
          });

          // Salvar no cache para uso futuro
          localStorage.setItem('cached_users', JSON.stringify(mockUsers));

          setUsers(mockUsers);
          setLoading(false);
          return;
        }

        if (!fetchedUsers || fetchedUsers.length === 0) {
          console.log("UserManagement: Nenhum usuário encontrado, usando dados mockados");
          // Criar usuários mockados
          console.log("UserManagement: Criando usuários mockados");

          // Incluir o usuário atual
          const mockUsers: UserProfile[] = [];

          if (currentUser) {
            // Verificar se o ID do usuário atual é um UUID válido
            const isValidUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(currentUser.id);

            if (isValidUUID) {
              mockUsers.push(currentUser);
            } else {
              // Converter para UUID válido
              const mockUUID = `00000000-0000-0000-0000-${currentUser.id.toString().padStart(12, '0')}`;
              mockUsers.push({
                ...currentUser,
                id: mockUUID
              });
            }
          }

          // Adicionar alguns usuários mockados para demonstração
          if (!mockUsers.some(u => u.role === 'admin')) {
            mockUsers.push({
              id: '00000000-0000-0000-0000-000000000001',
              email: '<EMAIL>',
              nome: 'Administrador',
              role: 'admin',
              status: 'ativo',
              created_at: new Date().toISOString()
            });
          }

          mockUsers.push({
            id: '00000000-0000-0000-0000-000000000002',
            email: '<EMAIL>',
            nome: 'Gerente',
            role: 'gerente_geral',
            status: 'ativo',
            created_at: new Date().toISOString()
          });

          mockUsers.push({
            id: '00000000-0000-0000-0000-000000000003',
            email: '<EMAIL>',
            nome: 'Operacional',
            role: 'operacional_completo',
            status: 'ativo',
            created_at: new Date().toISOString()
          });

          // Salvar no cache para uso futuro
          localStorage.setItem('cached_users', JSON.stringify(mockUsers));

          setUsers(mockUsers);
          setLoading(false);
          return;
        }

        console.log("UserManagement: Usuários carregados com sucesso:", fetchedUsers.length);

        // Salvar usuários no cache
        localStorage.setItem('cached_users', JSON.stringify(fetchedUsers));

        // Mapear os usuários para o formato esperado
        setUsers(fetchedUsers);
        setLoading(false);
        return;
      } catch (error) {
        console.error("UserManagement: Erro ao carregar usuários do AuthContext:", error);

        // Criar usuários mockados como fallback
        console.log("UserManagement: Criando usuários mockados como fallback");

        // Incluir o usuário atual
        const mockUsers: UserProfile[] = [];

        if (currentUser) {
          // Verificar se o ID do usuário atual é um UUID válido
          const isValidUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(currentUser.id);

          if (isValidUUID) {
            mockUsers.push(currentUser);
          } else {
            // Converter para UUID válido
            const mockUUID = `00000000-0000-0000-0000-${currentUser.id.toString().padStart(12, '0')}`;
            mockUsers.push({
              ...currentUser,
              id: mockUUID
            });
          }
        }

        // Adicionar alguns usuários mockados para demonstração
        if (!mockUsers.some(u => u.role === 'admin')) {
          mockUsers.push({
            id: '00000000-0000-0000-0000-000000000001',
            email: '<EMAIL>',
            nome: 'Administrador',
            role: 'admin',
            status: 'ativo',
            created_at: new Date().toISOString()
          });
        }

        mockUsers.push({
          id: '00000000-0000-0000-0000-000000000002',
          email: '<EMAIL>',
          nome: 'Gerente',
          role: 'gerente_geral',
          status: 'ativo',
          created_at: new Date().toISOString()
        });

        mockUsers.push({
          id: '00000000-0000-0000-0000-000000000003',
          email: '<EMAIL>',
          nome: 'Operacional',
          role: 'operacional_completo',
          status: 'ativo',
          created_at: new Date().toISOString()
        });

        // Salvar no cache para uso futuro
        localStorage.setItem('cached_users', JSON.stringify(mockUsers));

        setUsers(mockUsers);
      }
    } catch (error: any) {
      console.error("UserManagement: Erro ao carregar usuários:", error);
      toast.error("Erro ao carregar usuários", {
        description: error instanceof Error ? error.message : "Ocorreu um erro desconhecido ao tentar carregar a lista de usuários."
      });

      // Usar array vazio em caso de erro
      setUsers([]);
    } finally {
      setLoading(false);
    }
  };

  // Estado para controlar erros de validação
  const [formErrors, setFormErrors] = useState<{
    nome?: string;
    email?: string;
    password?: string;
    role?: string;
    telefone?: string;
    data_entrada?: string;
  }>({});

  // Adicionar novo usuário
  const handleAddUser = async () => {
    // Resetar erros anteriores
    setFormErrors({});

    // Objeto para armazenar erros
    const errors: {[key: string]: string} = {};
    let firstErrorField: string | null = null;

    // Validar campos obrigatórios
    if (!newUser.nome) {
      errors.nome = "Nome é obrigatório";
      firstErrorField = firstErrorField || 'nome';
    }

    if (!newUser.email) {
      errors.email = "E-mail é obrigatório";
      firstErrorField = firstErrorField || 'email';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(newUser.email)) {
      errors.email = "Formato de e-mail inválido";
      firstErrorField = firstErrorField || 'email';
    }

    if (!newUser.password) {
      errors.password = "Senha é obrigatória";
      firstErrorField = firstErrorField || 'password';
    } else if (newUser.password.length < 6) {
      errors.password = "A senha deve ter pelo menos 6 caracteres";
      firstErrorField = firstErrorField || 'password';
    }

    if (!newUser.role) {
      errors.role = "Cargo é obrigatório";
      firstErrorField = firstErrorField || 'role';
    }

    // Validar formato do telefone (opcional)
    if (newUser.telefone && !/^\(?\d{2}\)?[\s-]?\d{4,5}-?\d{4}$/.test(newUser.telefone)) {
      errors.telefone = "Formato de telefone inválido. Use (00) 00000-0000";
      firstErrorField = firstErrorField || 'telefone';
    }

    // Validar data de entrada (opcional)
    if (newUser.data_entrada) {
      const dataEntrada = new Date(newUser.data_entrada);
      const hoje = new Date();
      if (isNaN(dataEntrada.getTime())) {
        errors.data_entrada = "Data inválida";
        firstErrorField = firstErrorField || 'data_entrada';
      } else if (dataEntrada > hoje) {
        errors.data_entrada = "A data não pode ser futura";
        firstErrorField = firstErrorField || 'data_entrada';
      }
    }

    // Se houver erros, atualizar o estado e focar no primeiro campo com erro
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      toast.error("Campos incompletos", {
        description: "Verifique os campos destacados em vermelho."
      });

      // Focar no primeiro campo com erro
      if (firstErrorField) {
        const element = document.getElementById(firstErrorField);
        if (element) {
          setTimeout(() => {
            element.focus();
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
          }, 100);
        }
      }

      return;
    }

    try {
      setAddingUser(true);

      // Utilizar a função createUser do AuthContext
      const { error, user: createdUser } = await createUser(
        newUser.email,
        newUser.password,
        {
          nome: newUser.nome,
          role: newUser.role,
          status: "ativo",
          cargo: newUser.cargo || null,
          departamento: newUser.departamento || null,
          foto_url: newUser.foto_url || null,
          data_entrada: newUser.data_entrada || null,
          telefone: newUser.telefone || null
        }
      );

      if (error) {
        console.error("Erro ao criar usuário:", error);
        toast.error("Erro ao criar usuário", {
          description: error
        });
        return;
      }

      if (!createdUser) {
        toast.error("Erro ao criar usuário", {
          description: "Não foi possível obter os dados do usuário criado."
        });
        return;
      }

      toast.success("Usuário criado com sucesso!", {
        description: `${newUser.nome} foi adicionado como ${roleDisplayName(newUser.role)}.`
      });

      // Resetar formulário
      setNewUser({
        nome: "",
        email: "",
        password: "",
        role: "captador",
        cargo: "",
        departamento: "",
        foto_url: "",
        data_entrada: new Date().toISOString().split('T')[0],
        telefone: "",
        status: "ativo",
      });

      // Fechar modal e atualizar lista
      setIsAddUserOpen(false);

      // Adicionar o novo usuário à lista local para evitar ter que recarregar
      const novoUsuario: UserProfile = {
        id: createdUser.id,
        email: createdUser.email,
        nome: createdUser.nome,
        role: createdUser.role,
        status: "ativo",
        created_at: new Date().toISOString(),
        cargo: createdUser.cargo,
        departamento: createdUser.departamento,
        telefone: createdUser.telefone,
        data_entrada: createdUser.data_entrada,
        foto_url: createdUser.foto_url,
        custom_role_id: createdUser.custom_role_id
      };

      setUsers(prevUsers => [...prevUsers, novoUsuario]);

    } catch (error: any) {
      console.error("Erro ao adicionar usuário:", error);
      toast.error("Erro ao adicionar usuário", {
        description: error instanceof Error ? error.message : "Ocorreu um erro desconhecido"
      });
    } finally {
      setAddingUser(false);
    }
  };

  // Atualizar o papel do usuário
  const handleUpdateRole = async () => {
    if (!selectedUser) return;

    try {
      console.log("Atualizando cargo do usuário:", selectedUser);

      // Se o cargo não mudou, não fazer nada
      if (selectedUser.role === newRole) {
        toast.info("Nenhuma alteração necessária", {
          description: `${selectedUser.nome || selectedUser.email} já possui o cargo ${roleDisplayName(newRole)}.`
        });
        setIsEditRoleOpen(false);
        return;
      }

      // Verificar se o ID é válido
      const isValidUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(selectedUser.id);

      if (!isValidUUID) {
        console.error("ID de usuário inválido:", selectedUser.id);
        toast.error("Erro ao atualizar cargo", {
          description: "ID de usuário inválido. Não é possível atualizar no banco de dados."
        });
        return;
      }

      // Usar a função do contexto de autenticação
      const { error, success } = await updateUserPermissions(selectedUser.id, newRole);

      if (error) {
        console.error("Erro ao atualizar cargo:", error);
        toast.error("Erro ao atualizar cargo", {
          description: error
        });

        // Atualizar apenas localmente se houver erro no backend
        toast.warning("Atualização parcial", {
          description: "Falha ao salvar no banco de dados, mas o cargo foi atualizado localmente."
        });
      } else {
        console.log("Cargo atualizado com sucesso no banco de dados!");
        toast.success("Cargo atualizado com sucesso!", {
          description: `${selectedUser.nome || selectedUser.email} agora é ${roleDisplayName(newRole)}.`
        });
      }

      // Atualizar o usuário na lista local independentemente do resultado do backend
      // Isso garante que a UI permaneça consistente
      setUsers(prevUsers => {
        return prevUsers.map(user =>
          user.id === selectedUser.id
            ? { ...user, role: newRole }
            : user
        );
      });

      // Fechar modal e recarregar usuários
      setIsEditRoleOpen(false);

    } catch (error: any) {
      console.error("Erro ao atualizar cargo:", error);
      toast.error("Erro ao atualizar cargo", {
        description: error instanceof Error ? error.message : "Ocorreu um erro desconhecido"
      });
    }
  };

  // Abrir o modal de edição de papel
  const handleOpenEditRole = (user: UserProfile) => {
    // Verificar se o ID do usuário é um UUID válido
    const isValidUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(user.id);

    // Se não for válido, converter para UUID válido
    if (!isValidUUID) {
      console.log("Convertendo ID inválido para edição:", user.id);
      const mockUUID = `00000000-0000-0000-0000-${user.id.toString().padStart(12, '0')}`;
      user = { ...user, id: mockUUID };

      // Atualizar o usuário na lista
      setUsers(prevUsers =>
        prevUsers.map(u =>
          u.id === user.id
            ? user
            : u
        )
      );
    }

    setSelectedUser(user);
    setNewRole(user.role);
    setIsEditRoleOpen(true);
  };

  // Funções para os modais de edição e exclusão
  const handleEditUser = (user: UserProfile) => {
    setSelectedUser(user);
    setIsEditUserOpen(true);
  };

  const handleDeleteUser = (user: UserProfile) => {
    setSelectedUser(user);
    setIsDeleteUserOpen(true);
  };

  const handleDataVisibility = (user: UserProfile) => {
    setSelectedUser(user);
    setIsDataVisibilityOpen(true);
  };

  const handleUserUpdated = (updatedUser: UserProfile) => {
    setUsers(prevUsers =>
      prevUsers.map(user =>
        user.id === updatedUser.id ? updatedUser : user
      )
    );
    // Limpar cache para forçar reload
    localStorage.removeItem('cached_users');
  };

  const handleUserDeleted = (deletedUserId: string) => {
    setUsers(prevUsers =>
      prevUsers.filter(user => user.id !== deletedUserId)
    );
    // Limpar cache para forçar reload
    localStorage.removeItem('cached_users');
  };

  // Tradução para exibição amigável
  const roleTranslation: Record<UserRole, string> = {
    admin: "Administrador (ADC)",
    gerente_geral: "Gerente Geral",
    gerente_precatorio: "Gerente de Precatório",
    gerente_rpv: "Gerente de RPV",
    captador: "Captador",
    operacional_precatorio: "Operacional - Precatório",
    operacional_rpv: "Operacional - RPV",
    operacional_completo: "Operacional - Completo"
  };

  // Cores para os badges de papel
  const roleBadgeColors: Record<UserRole, string> = {
    admin: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",
    gerente_geral: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
    gerente_precatorio: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
    gerente_rpv: "bg-teal-100 text-teal-800 dark:bg-teal-900 dark:text-teal-200",
    captador: "bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200",
    operacional_precatorio: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
    operacional_rpv: "bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200",
    operacional_completo: "bg-lime-100 text-lime-800 dark:bg-lime-900 dark:text-lime-200"
  };

  // Função para exibir nome amigável do papel
  const roleDisplayName = (role: UserRole) => {
    const displayNames = {
      admin: "Administrador (ADC)",
      gerente_geral: "Gerente Geral",
      gerente_precatorio: "Gerente de Precatório",
      gerente_rpv: "Gerente de RPV",
      captador: "Captador",
      operacional_precatorio: "Operacional - Precatório",
      operacional_rpv: "Operacional - RPV",
      operacional_completo: "Operacional - Completo"
    };
    return displayNames[role] || role;
  };

  // Função para atualizar a lista manualmente
  const refreshUserList = () => {
    console.log("UserManagement: Atualizando lista de usuários manualmente...");
    setLoading(true);
    loadUsers();
  };

  // Função para filtrar e ordenar usuários
  const getFilteredAndSortedUsers = () => {
    let filteredUsers = [...users];

    // Aplicar filtro de busca
    if (searchTerm) {
      filteredUsers = filteredUsers.filter(user =>
        user.nome?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.cargo?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.departamento?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Aplicar filtro de role
    if (roleFilter !== "all") {
      filteredUsers = filteredUsers.filter(user => user.role === roleFilter);
    }

    // Aplicar filtro de status
    if (statusFilter !== "all") {
      filteredUsers = filteredUsers.filter(user => user.status === statusFilter);
    }

    // Aplicar ordenação
    filteredUsers.sort((a, b) => {
      let aValue: string | Date;
      let bValue: string | Date;

      switch (sortBy) {
        case "name":
          aValue = a.nome || a.email;
          bValue = b.nome || b.email;
          break;
        case "email":
          aValue = a.email;
          bValue = b.email;
          break;
        case "role":
          aValue = roleDisplayName(a.role);
          bValue = roleDisplayName(b.role);
          break;
        case "created_at":
          aValue = new Date(a.created_at || "");
          bValue = new Date(b.created_at || "");
          break;
        default:
          aValue = a.nome || a.email;
          bValue = b.nome || b.email;
      }

      if (sortBy === "created_at") {
        const comparison = (aValue as Date).getTime() - (bValue as Date).getTime();
        return sortOrder === "asc" ? comparison : -comparison;
      } else {
        const comparison = (aValue as string).localeCompare(bValue as string);
        return sortOrder === "asc" ? comparison : -comparison;
      }
    });

    return filteredUsers;
  };

  // Função para paginar usuários
  const getPaginatedUsers = () => {
    const filteredUsers = getFilteredAndSortedUsers();
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return {
      users: filteredUsers.slice(startIndex, endIndex),
      totalUsers: filteredUsers.length,
      totalPages: Math.ceil(filteredUsers.length / itemsPerPage)
    };
  };

  // Verificar se não é admin
  if (currentUser?.role !== 'admin') {
    return (
      <div className="p-6">
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Acesso negado</AlertTitle>
          <AlertDescription>
            Você não tem permissão para acessar esta página.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-screen w-screen overflow-hidden">
      <TopNav
        title="Gerenciamento de Usuários"
        icon={<Users className="h-6 w-6 text-primary" />}
      />

      <div className="p-6 pt-20 overflow-auto">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="users" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Usuários
            </TabsTrigger>
            <TabsTrigger value="permissions" className="flex items-center gap-2">
              <Shield className="h-4 w-4" />
              Gerenciamento de Permissões
            </TabsTrigger>
          </TabsList>

          <TabsContent value="users" className="space-y-6">
            {/* Barra de ferramentas */}
            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
              <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center flex-1">
                {/* Busca */}
                <div className="relative flex-1 max-w-sm">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                  <Input
                    placeholder="Buscar usuários..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>

                {/* Filtros */}
                <div className="flex gap-2">
                  <Select value={roleFilter} onValueChange={(value) => setRoleFilter(value as UserRole | "all")}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Filtrar por cargo" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Todos os cargos</SelectItem>
                      <SelectItem value="admin">Administrador</SelectItem>
                      <SelectItem value="gerente_geral">Gerente Geral</SelectItem>
                      <SelectItem value="gerente_precatorio">Gerente Precatório</SelectItem>
                      <SelectItem value="gerente_rpv">Gerente RPV</SelectItem>
                      <SelectItem value="captador">Captador</SelectItem>
                      <SelectItem value="operacional_precatorio">Op. Precatório</SelectItem>
                      <SelectItem value="operacional_rpv">Op. RPV</SelectItem>
                      <SelectItem value="operacional_completo">Op. Completo</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-[140px]">
                      <SelectValue placeholder="Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Todos</SelectItem>
                      <SelectItem value="ativo">Ativo</SelectItem>
                      <SelectItem value="inativo">Inativo</SelectItem>
                      <SelectItem value="suspenso">Suspenso</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Ações */}
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={refreshUserList}
                  disabled={loading}
                >
                  {loading ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-current" />
                  ) : (
                    <RefreshCw className="h-4 w-4" />
                  )}
                </Button>
                <Button
                  size="sm"
                  onClick={() => setIsAddUserOpen(true)}
                >
                  <UserPlus className="h-4 w-4 mr-2" />
                  Novo Usuário
                </Button>
              </div>
            </div>

            {/* Tabela de usuários */}
            {loading ? (
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            ) : (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>Lista de Usuários</span>
                    <Badge variant="secondary">
                      {getFilteredAndSortedUsers().length} usuário(s)
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead
                          className="cursor-pointer hover:bg-muted/50"
                          onClick={() => {
                            if (sortBy === "name") {
                              setSortOrder(sortOrder === "asc" ? "desc" : "asc");
                            } else {
                              setSortBy("name");
                              setSortOrder("asc");
                            }
                          }}
                        >
                          Nome {sortBy === "name" && (sortOrder === "asc" ? "↑" : "↓")}
                        </TableHead>
                        <TableHead
                          className="cursor-pointer hover:bg-muted/50"
                          onClick={() => {
                            if (sortBy === "email") {
                              setSortOrder(sortOrder === "asc" ? "desc" : "asc");
                            } else {
                              setSortBy("email");
                              setSortOrder("asc");
                            }
                          }}
                        >
                          E-mail {sortBy === "email" && (sortOrder === "asc" ? "↑" : "↓")}
                        </TableHead>
                        <TableHead
                          className="cursor-pointer hover:bg-muted/50"
                          onClick={() => {
                            if (sortBy === "role") {
                              setSortOrder(sortOrder === "asc" ? "desc" : "asc");
                            } else {
                              setSortBy("role");
                              setSortOrder("asc");
                            }
                          }}
                        >
                          Cargo {sortBy === "role" && (sortOrder === "asc" ? "↑" : "↓")}
                        </TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead
                          className="cursor-pointer hover:bg-muted/50"
                          onClick={() => {
                            if (sortBy === "created_at") {
                              setSortOrder(sortOrder === "asc" ? "desc" : "asc");
                            } else {
                              setSortBy("created_at");
                              setSortOrder("asc");
                            }
                          }}
                        >
                          Criado em {sortBy === "created_at" && (sortOrder === "asc" ? "↑" : "↓")}
                        </TableHead>
                        <TableHead>Ações</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {getFilteredAndSortedUsers().length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={6} className="text-center py-8">
                            <div className="flex flex-col items-center justify-center space-y-3">
                              <Users className="h-12 w-12 text-muted-foreground" />
                              <div>
                                <p className="text-lg font-medium">Nenhum usuário encontrado</p>
                                <p className="text-sm text-muted-foreground">
                                  {searchTerm || roleFilter !== "all" || statusFilter !== "all"
                                    ? "Tente ajustar os filtros de busca"
                                    : "Adicione o primeiro usuário ao sistema"
                                  }
                                </p>
                              </div>
                              <Button size="sm" variant="outline" onClick={refreshUserList}>
                                <RefreshCw className="h-4 w-4 mr-2" />
                                Atualizar
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : (
                        getFilteredAndSortedUsers().map((user) => (
                          <TableRow key={user.id} className="hover:bg-muted/50">
                            <TableCell>
                              <div className="flex items-center gap-3">
                                <Avatar className="h-8 w-8">
                                  <AvatarImage src={user.foto_url || ""} />
                                  <AvatarFallback className="text-xs">
                                    {user.nome ? user.nome.slice(0, 2).toUpperCase() : "U"}
                                  </AvatarFallback>
                                </Avatar>
                                <div>
                                  <p className="font-medium">{user.nome || "Usuário sem nome"}</p>
                                  {user.cargo && (
                                    <p className="text-xs text-muted-foreground">{user.cargo}</p>
                                  )}
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div>
                                <p className="font-mono text-sm">{user.email}</p>
                                {user.telefone && (
                                  <p className="text-xs text-muted-foreground">{user.telefone}</p>
                                )}
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge className={`text-xs ${
                                user.role === 'admin'
                                  ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                                  : user.role === 'gerente_geral'
                                    ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'
                                    : user.role === 'operacional_completo'
                                      ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                                      : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
                              }`}>
                                {roleDisplayName(user.role)}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <Badge
                                variant={
                                  user.status === "ativo" ? "default" :
                                  user.status === "inativo" ? "secondary" :
                                  "destructive"
                                }
                              >
                                {user.status || "Desconhecido"}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <p className="text-sm">
                                {user.created_at
                                  ? new Date(user.created_at).toLocaleDateString('pt-BR')
                                  : "Não informado"
                                }
                              </p>
                            </TableCell>
                            <TableCell>
                              <div className="flex gap-1">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => navigate(`/usuario/${user.id}`)}
                                  title="Ver perfil completo"
                                >
                                  <Eye className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleEditUser(user)}
                                  title="Editar usuário"
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => {
                                    setSelectedUser(user);
                                    setIsUserSpecificPermissionsOpen(true);
                                  }}
                                  title="Permissões específicas"
                                >
                                  <Shield className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleDataVisibility(user)}
                                  title="Configurar visibilidade de dados"
                                >
                                  <Database className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleDeleteUser(user)}
                                  title="Excluir usuário"
                                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                  disabled={currentUser?.id === user.id}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="permissions" className="space-y-6">
            <AdvancedPermissionsManager />
          </TabsContent>
        </Tabs>

      {/* Modal de adicionar usuário */}
      <Dialog open={isAddUserOpen} onOpenChange={setIsAddUserOpen}>
        <DialogContent className="sm:max-w-[500px]" aria-describedby="add-user-description">
          <DialogHeader>
            <DialogTitle>Adicionar Novo Usuário</DialogTitle>
            <DialogDescription id="add-user-description">
              Preencha os dados para criar um novo usuário no sistema.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="nome" className="text-right">
                Nome <span className="text-red-500">*</span>
              </Label>
              <div className="col-span-3 space-y-1">
                <Input
                  id="nome"
                  value={newUser.nome}
                  onChange={(e) => {
                    setNewUser({ ...newUser, nome: e.target.value });
                    if (formErrors.nome) {
                      setFormErrors(prev => ({ ...prev, nome: undefined }));
                    }
                  }}
                  className={`${formErrors.nome ? 'border-red-500 focus-visible:ring-red-500' : ''}`}
                  aria-invalid={!!formErrors.nome}
                  aria-describedby={formErrors.nome ? "nome-error" : undefined}
                />
                {formErrors.nome && (
                  <p id="nome-error" className="text-sm font-medium text-red-500">{formErrors.nome}</p>
                )}
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="email" className="text-right">
                E-mail <span className="text-red-500">*</span>
              </Label>
              <div className="col-span-3 space-y-1">
                <Input
                  id="email"
                  type="email"
                  value={newUser.email}
                  onChange={(e) => {
                    setNewUser({ ...newUser, email: e.target.value });
                    if (formErrors.email) {
                      setFormErrors(prev => ({ ...prev, email: undefined }));
                    }
                  }}
                  className={`${formErrors.email ? 'border-red-500 focus-visible:ring-red-500' : ''}`}
                  aria-invalid={!!formErrors.email}
                  aria-describedby={formErrors.email ? "email-error" : undefined}
                />
                {formErrors.email && (
                  <p id="email-error" className="text-sm font-medium text-red-500">{formErrors.email}</p>
                )}
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="password" className="text-right">
                Senha <span className="text-red-500">*</span>
              </Label>
              <div className="col-span-3 space-y-1">
                <Input
                  id="password"
                  type="password"
                  value={newUser.password}
                  onChange={(e) => {
                    setNewUser({ ...newUser, password: e.target.value });
                    if (formErrors.password) {
                      setFormErrors(prev => ({ ...prev, password: undefined }));
                    }
                  }}
                  className={`${formErrors.password ? 'border-red-500 focus-visible:ring-red-500' : ''}`}
                  aria-invalid={!!formErrors.password}
                  aria-describedby={formErrors.password ? "password-error" : undefined}
                />
                {formErrors.password && (
                  <p id="password-error" className="text-sm font-medium text-red-500">{formErrors.password}</p>
                )}
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="role" className="text-right">
                Cargo <span className="text-red-500">*</span>
              </Label>
              <div className="col-span-3 space-y-1">
                <Select
                  value={newUser.role}
                  onValueChange={(value: UserRole) => {
                    setNewUser({ ...newUser, role: value });
                    if (formErrors.role) {
                      setFormErrors(prev => ({ ...prev, role: undefined }));
                    }
                  }}
                >
                  <SelectTrigger
                    id="role"
                    className={`${formErrors.role ? 'border-red-500 focus-visible:ring-red-500' : ''}`}
                    aria-invalid={!!formErrors.role}
                    aria-describedby={formErrors.role ? "role-error" : undefined}
                  >
                    <SelectValue placeholder="Selecione o cargo" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="admin">Administrador (ADC)</SelectItem>
                    <SelectItem value="gerente_geral">Gerente Geral</SelectItem>
                    <SelectItem value="gerente_precatorio">Gerente de Precatório</SelectItem>
                    <SelectItem value="gerente_rpv">Gerente de RPV</SelectItem>
                    <SelectItem value="captador">Captador</SelectItem>
                    <SelectItem value="operacional_precatorio">Operacional - Precatório</SelectItem>
                    <SelectItem value="operacional_rpv">Operacional - RPV</SelectItem>
                    <SelectItem value="operacional_completo">Operacional - Completo</SelectItem>
                  </SelectContent>
                </Select>
                {formErrors.role && (
                  <p id="role-error" className="text-sm font-medium text-red-500">{formErrors.role}</p>
                )}
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="cargo" className="text-right">
                Cargo/Função
              </Label>
              <div className="col-span-3">
                <Input
                  id="cargo"
                  value={newUser.cargo}
                  onChange={(e) => setNewUser({ ...newUser, cargo: e.target.value })}
                  placeholder="Ex: Advogado, Assistente, etc."
                />
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="departamento" className="text-right">
                Departamento
              </Label>
              <div className="col-span-3">
                <Input
                  id="departamento"
                  value={newUser.departamento}
                  onChange={(e) => setNewUser({ ...newUser, departamento: e.target.value })}
                  placeholder="Ex: Jurídico, Administrativo, etc."
                />
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="telefone" className="text-right">
                Telefone
              </Label>
              <div className="col-span-3 space-y-1">
                <Input
                  id="telefone"
                  value={newUser.telefone}
                  onChange={(e) => {
                    setNewUser({ ...newUser, telefone: e.target.value });
                    if (formErrors.telefone) {
                      setFormErrors(prev => ({ ...prev, telefone: undefined }));
                    }
                  }}
                  placeholder="(00) 00000-0000"
                  className={`${formErrors.telefone ? 'border-red-500 focus-visible:ring-red-500' : ''}`}
                  aria-invalid={!!formErrors.telefone}
                  aria-describedby={formErrors.telefone ? "telefone-error" : undefined}
                />
                {formErrors.telefone && (
                  <p id="telefone-error" className="text-sm font-medium text-red-500">{formErrors.telefone}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="data_entrada" className="text-right">
                Data de Entrada
              </Label>
              <div className="col-span-3 space-y-1">
                <Input
                  id="data_entrada"
                  type="date"
                  value={newUser.data_entrada}
                  onChange={(e) => {
                    setNewUser({ ...newUser, data_entrada: e.target.value });
                    if (formErrors.data_entrada) {
                      setFormErrors(prev => ({ ...prev, data_entrada: undefined }));
                    }
                  }}
                  className={`${formErrors.data_entrada ? 'border-red-500 focus-visible:ring-red-500' : ''}`}
                  aria-invalid={!!formErrors.data_entrada}
                  aria-describedby={formErrors.data_entrada ? "data_entrada-error" : undefined}
                />
                {formErrors.data_entrada && (
                  <p id="data_entrada-error" className="text-sm font-medium text-red-500">{formErrors.data_entrada}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="foto_url" className="text-right">
                URL da Foto
              </Label>
              <div className="col-span-3">
                <Input
                  id="foto_url"
                  value={newUser.foto_url}
                  onChange={(e) => setNewUser({ ...newUser, foto_url: e.target.value })}
                  placeholder="https://exemplo.com/foto.jpg"
                />
              </div>
            </div>

            <div className="col-span-4 mt-2">
              <p className="text-sm text-muted-foreground"><span className="text-red-500">*</span> Campos obrigatórios</p>
            </div>
          </div>
          <DialogFooter className="mt-6">
            <Button variant="outline" onClick={() => setIsAddUserOpen(false)}>Cancelar</Button>
            <Button onClick={handleAddUser} disabled={addingUser}>
              {addingUser ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 mr-2 border-t-2 border-b-2 border-white"></div>
                  Adicionando...
                </>
              ) : "Adicionar Usuário"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Modal de editar papel */}
      <Dialog open={isEditRoleOpen} onOpenChange={setIsEditRoleOpen}>
        <DialogContent className="sm:max-w-md" aria-describedby="edit-role-description">
          <DialogHeader>
            <DialogTitle>Alterar Cargo</DialogTitle>
            <DialogDescription id="edit-role-description">
              Selecione o novo cargo para {selectedUser?.nome || selectedUser?.email}.
              <div className="mt-2 text-sm">
                <span className="font-medium">Cargo atual: </span>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  selectedUser?.role === 'admin'
                    ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                    : selectedUser?.role === 'gerente_geral'
                      ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'
                      : selectedUser?.role === 'operacional_completo'
                        ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                        : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
                }`}>
                  {selectedUser ? roleDisplayName(selectedUser.role) : ''}
                </span>
              </div>
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="role" className="text-right">
                Novo Cargo
              </Label>
              <Select
                value={newRole}
                onValueChange={(value) => {
                  console.log("Selecionando novo cargo:", value);
                  setNewRole(value as UserRole);
                }}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Selecione um cargo" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="admin">Administrador</SelectItem>
                  <SelectItem value="gerente_geral">Gerente Geral</SelectItem>
                  <SelectItem value="gerente_precatorio">Gerente de Precatório</SelectItem>
                  <SelectItem value="gerente_rpv">Gerente de RPV</SelectItem>
                  <SelectItem value="captador">Captador</SelectItem>
                  <SelectItem value="operacional_precatorio">Operacional - Precatório</SelectItem>
                  <SelectItem value="operacional_rpv">Operacional - RPV</SelectItem>
                  <SelectItem value="operacional_completo">Operacional - Completo</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter className="mt-6">
            <Button variant="outline" onClick={() => setIsEditRoleOpen(false)}>Cancelar</Button>
            <Button onClick={handleUpdateRole}>Salvar Alterações</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Modal de permissões avançadas */}
      <Dialog open={isPermissionsOpen} onOpenChange={setIsPermissionsOpen}>
        <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Permissões Avançadas</DialogTitle>
            <DialogDescription>
              Configure permissões detalhadas para {selectedUser?.nome || selectedUser?.email || "Usuário selecionado"}
            </DialogDescription>
          </DialogHeader>

          {selectedUser && (
            <UserPermissionsManager
              userId={selectedUser.id}
              userName={selectedUser.nome || selectedUser.email || "Usuário"}
              userRole={selectedUser.role}
              onPermissionsUpdated={() => {
                setIsPermissionsOpen(false);
                toast.success("Permissões atualizadas", {
                  description: "As permissões do usuário foram atualizadas com sucesso."
                });
              }}
            />
          )}

          <DialogFooter className="mt-6">
            <Button variant="outline" onClick={() => setIsPermissionsOpen(false)}>
              Fechar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Modal de detalhes do usuário */}
      <UserDetailsModal
        user={selectedUser}
        isOpen={isUserDetailsOpen}
        onClose={() => {
          setIsUserDetailsOpen(false);
          setSelectedUser(null);
        }}
      />

      {/* Modal de permissões específicas do usuário */}
      <UserSpecificPermissions
        user={selectedUser}
        isOpen={isUserSpecificPermissionsOpen}
        onClose={() => {
          setIsUserSpecificPermissionsOpen(false);
          setSelectedUser(null);
        }}
      />

      {/* Modal de edição de usuário */}
      <EditUserModal
        isOpen={isEditUserOpen}
        onClose={() => {
          setIsEditUserOpen(false);
          setSelectedUser(null);
        }}
        user={selectedUser}
        onUserUpdated={handleUserUpdated}
      />

      {/* Modal de exclusão de usuário */}
      <DeleteUserModal
        isOpen={isDeleteUserOpen}
        onClose={() => {
          setIsDeleteUserOpen(false);
          setSelectedUser(null);
        }}
        user={selectedUser}
        onUserDeleted={handleUserDeleted}
      />

      {/* Modal de configuração de visibilidade de dados */}
      <DataVisibilityManager
        isOpen={isDataVisibilityOpen}
        onClose={() => {
          setIsDataVisibilityOpen(false);
          setSelectedUser(null);
        }}
        user={selectedUser}
        onConfigUpdated={() => {
          // Recarregar dados se necessário
          console.log("Configurações de visibilidade atualizadas");
        }}
      />
      </div>
    </div>
  );
}