import { useState, useRef } from "react";
import { Task<PERSON>anager, TaskManagerHandles } from "@/features/tasks/components/TaskManager";
import { Button } from "@/components/ui/button";
import { PlusCircle, ListChecks, CheckSquare, FileText, LayoutList } from "lucide-react";
import { TopNav } from "@/components/top-nav";
import { DataVisibilityGuard } from "@/components/permissions/DataVisibilityGuard";

type TaskViewType = 'TODAS' | 'PRECATORIO' | 'RPV';

export default function Tasks() {
  const [activeView, setActiveView] = useState<TaskViewType>('TODAS');
  const taskManagerRef = useRef<TaskManagerHandles>(null);

  // Função para abrir o modal de criação de tarefa
  const handleOpenCreateTask = () => {
    if (taskManagerRef.current) {
      taskManagerRef.current.openCreateTask();
    }
  };

  return (
    <div
      className="flex flex-col h-screen w-screen overflow-hidden"
    >
      <TopNav
        title="Tarefas"
        icon={<LayoutList className="h-6 w-6 text-primary" />}
        actions={
          <div className="flex items-center gap-2">
            {/* Navegação de tipos de tarefa */}
            <div className="hidden md:flex items-center gap-1 mr-4">
              <Button
                variant={activeView === 'TODAS' ? "default" : "ghost"}
                size="sm"
                onClick={() => setActiveView('TODAS')}
                className="text-xs px-3"
              >
                <ListChecks className="h-3 w-3 mr-1" />
                Todas
              </Button>
              <Button
                variant={activeView === 'PRECATORIO' ? "default" : "ghost"}
                size="sm"
                onClick={() => setActiveView('PRECATORIO')}
                className="text-xs px-3"
              >
                <FileText className="h-3 w-3 mr-1" />
                Precatórios
              </Button>
              <Button
                variant={activeView === 'RPV' ? "default" : "ghost"}
                size="sm"
                onClick={() => setActiveView('RPV')}
                className="text-xs px-3"
              >
                <CheckSquare className="h-3 w-3 mr-1" />
                RPVs
              </Button>
            </div>

            {/* Navegação compacta para mobile */}
            <div className="md:hidden flex items-center gap-1 mr-2">
              <Button
                variant={activeView === 'TODAS' ? "default" : "ghost"}
                size="sm"
                onClick={() => setActiveView('TODAS')}
                className="text-xs px-2"
              >
                <ListChecks className="h-3 w-3 mr-1" />
                Todas
              </Button>
              <Button
                variant={activeView === 'PRECATORIO' ? "default" : "ghost"}
                size="sm"
                onClick={() => setActiveView('PRECATORIO')}
                className="text-xs px-2"
              >
                <FileText className="h-3 w-3 mr-1" />
                Prec.
              </Button>
              <Button
                variant={activeView === 'RPV' ? "default" : "ghost"}
                size="sm"
                onClick={() => setActiveView('RPV')}
                className="text-xs px-2"
              >
                <CheckSquare className="h-3 w-3 mr-1" />
                RPVs
              </Button>
            </div>

            {/* Botão de nova tarefa */}
            <DataVisibilityGuard resourceType="tarefas">
              <Button
                className="gap-1 text-xs md:text-sm px-2 md:px-4"
                size="sm"
                onClick={handleOpenCreateTask}
              >
                <PlusCircle className="h-3 w-3 md:h-4 md:w-4" />
                <span className="hidden sm:inline">Nova Tarefa</span>
                <span className="sm:hidden">Nova</span>
              </Button>
            </DataVisibilityGuard>
          </div>
        }
      />

      {/* Área Principal de Conteúdo (TaskManager) */}
      <main
        className="flex-1 overflow-auto bg-transparent pt-20"
      >
        {/* Renderizar o TaskManager correto baseado na activeView */}
        <TaskManager
          key={activeView} // Forçar remontagem ao mudar a view
          ref={taskManagerRef}
          areaFilter={activeView === 'TODAS' ? null : activeView}
          defaultView="lista" // Ou "quadro" se preferir
          showHeader={false} // O cabeçalho está na barra superior agora
          showFilters={true} // Manter filtros internos se o TaskManager tiver
        />
      </main>
    </div>
  );
}