import { useState, useEffect } from "react";
import { useN<PERSON>gate, Link, useLocation } from "react-router-dom";
import { supabase, saveUserProfile, syncUserProfile } from "@/lib/supabase";
import { checkAndRestoreAuth } from "@/lib/auth-helpers";
import { resetAuthentication } from "@/lib/auth-reset";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { toast } from "sonner";
import { clearSessionData } from "@/lib/sessionPersistence";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Separator } from "@/components/ui/separator";
import { <PERSON>, <PERSON>O<PERSON>, Loader2 } from "lucide-react";

const formSchema = z.object({
  email: z.string().email({ message: "Email inválido" }),
  password: z.string().min(6, { message: "Senha deve ter pelo menos 6 caracteres" }),
});

export default function Login() {
  const navigate = useNavigate();
  const location = useLocation();
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  useEffect(() => {
    // Verificar parâmetros da URL
    const params = new URLSearchParams(location.search);
    const expired = params.get('expired');
    const forced = params.get('forced');
    const reason = params.get('reason');

    // Limpar completamente o estado de autenticação
    const clearAuthState = () => {
      try {
        // Limpar dados de perfil
        localStorage.removeItem("userProfile");
        sessionStorage.removeItem("userProfile");

        // Limpar tokens de autenticação
        localStorage.removeItem("sb-ubwzukpsqcrwzfbppoux-auth-token");
        sessionStorage.removeItem("sb-ubwzukpsqcrwzfbppoux-auth-token");
        localStorage.removeItem("sb-ubwzukpsqcrwzfbppoux-auth-backup");
        sessionStorage.removeItem("sb-ubwzukpsqcrwzfbppoux-auth-backup");
        localStorage.removeItem("sb-ubwzukpsqcrwzfbppoux-last-refresh");

        // Limpar dados de tab session
        localStorage.removeItem("last_active_tab");
        localStorage.removeItem("active_tab_timestamp");
        sessionStorage.removeItem("current_tab_id");
        sessionStorage.removeItem("tab_last_active");

        // Limpar dados de sessão usando a função especializada
        clearSessionData();

        console.log("Login: Estado de autenticação limpo com sucesso");
      } catch (error) {
        console.error("Login: Erro ao limpar estado de autenticação:", error);
      }
    };

    // Sempre limpar o estado de autenticação ao entrar na página de login
    clearAuthState();

    // Se a sessão expirou
    if (expired === 'true') {
      toast.warning("Sessão expirada", {
        description: "Sua sessão expirou por inatividade. Por favor, faça login novamente."
      });
      // Limpar o parâmetro da URL
      navigate('/login', { replace: true });
    }

    // Se o redirecionamento foi forçado
    if (forced === 'true') {
      // Usar a razão fornecida ou uma mensagem padrão
      const reasonMessage = reason
        ? decodeURIComponent(reason)
        : "Ocorreu um problema com sua sessão. Por favor, faça login novamente.";

      toast.warning("Redirecionamento forçado", {
        description: reasonMessage
      });
      // Limpar o parâmetro da URL
      navigate('/login', { replace: true });
    }

    const checkExistingSession = async () => {
      try {
        // Verificar se estamos em um redirecionamento forçado ou expirado
        const params = new URLSearchParams(location.search);
        if (params.get('forced') === 'true' || params.get('expired') === 'true') {
          console.log("Login: Ignorando verificação de sessão devido a redirecionamento forçado ou expirado");
          return;
        }

        // Definir um timeout para evitar bloqueio
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error("Timeout ao verificar sessão")), 5000);
        });

        // Limpar qualquer sessão problemática antes de verificar
        await resetAuthentication();

        // Usar Promise.race para evitar bloqueio
        const authCheckPromise = checkAndRestoreAuth();
        const result = await Promise.race([authCheckPromise, timeoutPromise]) as { success: boolean, session: any };

        if (result.success && result.session) {
          console.log("Sessão existente encontrada, redirecionando para o dashboard...");

          try {
            const { data: profile, error } = await supabase
              .from("profiles")
              .select("*")
              .eq("id", result.session.user.id)
              .single();

            if (error) {
              console.error("Erro ao buscar perfil:", error);
              return;
            }

            if (profile) {
              // Usar a função robusta para salvar o perfil
              saveUserProfile(profile);
              // Garantir que o perfil esteja sincronizado
              syncUserProfile();
            }

            navigate("/dashboard");
          } catch (profileError) {
            console.error("Erro ao buscar perfil:", profileError);
          }
        }
      } catch (error) {
        console.error("Erro ao verificar sessão existente:", error);
      }
    };

    checkExistingSession();
  }, [navigate, location.search]);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    try {
      setIsLoading(true);
      toast.loading("Autenticando...");

      // Limpar completamente os dados de autenticação antes de fazer login
      await resetAuthentication();

      console.log("Tentando fazer login com:", values.email);

      const { data, error } = await supabase.auth.signInWithPassword({
        email: values.email,
        password: values.password,
      });

      if (error) {
        console.error("Erro de autenticação:", error);

        if (error.status === 400) {
          toast.dismiss();
          toast.error("Email ou senha incorretos", {
            description: "Verifique suas credenciais e tente novamente."
          });
        } else {
          toast.dismiss();
          toast.error("Erro ao fazer login", {
            description: error.message || "Ocorreu um erro durante a autenticação."
          });
        }

        setIsLoading(false);
        return;
      }

      if (!data || !data.user) {
        toast.dismiss();
        toast.error("Erro de autenticação", {
          description: "Dados do usuário não encontrados."
        });
        setIsLoading(false);
        return;
      }

      console.log("Login bem-sucedido:", data.user.email);

      const { data: profile, error: profileError } = await supabase
        .from("profiles")
        .select("*")
        .eq("id", data.user.id)
        .single();

      if (profileError) {
        console.warn("Aviso: Erro ao buscar perfil do usuário:", profileError);
      }

      if (profile) {
        // Usar a função robusta para salvar o perfil
        saveUserProfile(profile);
        // Garantir que o perfil esteja sincronizado
        syncUserProfile();

        // Verificar e persistir a sessão
        try {
          const { data: sessionData } = await supabase.auth.getSession();
          if (sessionData.session) {
            // Persistir a sessão usando o sistema de persistência
            localStorage.setItem('app-session-data', JSON.stringify(sessionData.session));
            sessionStorage.setItem('app-session-data', JSON.stringify(sessionData.session));
            localStorage.setItem('app-session-timestamp', Date.now().toString());
            sessionStorage.setItem('app-session-timestamp', Date.now().toString());
            localStorage.setItem('app-session-backup', JSON.stringify(sessionData.session));
            console.log("Login: Sessão persistida com sucesso");
          }
        } catch (sessionError) {
          console.error("Login: Erro ao persistir sessão:", sessionError);
        }
      } else {
        console.warn("Aviso: Perfil do usuário não encontrado.");
      }

      toast.dismiss();
      toast.success("Login realizado com sucesso!", {
        description: "Redirecionando para o dashboard..."
      });

      setTimeout(() => {
        // Verificar se há uma URL salva para retornar
        try {
          const lastUrl = sessionStorage.getItem('last_url');
          if (lastUrl && lastUrl !== '/login' && !lastUrl.includes('/login')) {
            console.log("Login: Redirecionando para a última URL:", lastUrl);
            // Limpar a URL salva
            sessionStorage.removeItem('last_url');
            // Redirecionar para a última URL
            navigate(lastUrl);
            return;
          }
        } catch (error) {
          console.error("Login: Erro ao verificar última URL:", error);
        }

        // Redirecionar para o dashboard
        navigate("/dashboard");
      }, 1000);
    } catch (error: any) {
      console.error("Erro inesperado durante o login:", error);

      toast.dismiss();
      toast.error("Erro ao fazer login", {
        description: "Ocorreu um erro inesperado. Tente novamente mais tarde."
      });

      setIsLoading(false);
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-background to-muted/40 p-4">
      <Card className="w-full max-w-md shadow-lg">
        <CardHeader className="space-y-2 text-center">
          <CardTitle className="text-3xl font-bold">Login</CardTitle>
          <CardDescription>
            Entre com seu email e senha para acessar
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="<EMAIL>"
                            type="email"
                            autoComplete="email"
                            {...field}
                            disabled={isLoading}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="password"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Senha</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Input
                              placeholder="••••••••"
                              type={showPassword ? "text" : "password"}
                              autoComplete="current-password"
                              {...field}
                              disabled={isLoading}
                              className="pr-10"
                            />
                            <Button
                              type="button"
                              variant="ghost"
                              size="icon"
                              className="absolute inset-y-0 right-0 h-full px-3 text-muted-foreground hover:bg-transparent"
                              onClick={() => setShowPassword(!showPassword)}
                              disabled={isLoading}
                              aria-label={showPassword ? "Esconder senha" : "Mostrar senha"}
                            >
                              {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                            </Button>
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <div className="text-right">
                    <Link to="/forgot-password" className="text-sm text-primary hover:underline">
                      Esqueceu a senha?
                    </Link>
                  </div>
                  <Button
                    type="submit"
                    className="w-full"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Autenticando...</>
                    ) : (
                      "Entrar"
                    )}
                  </Button>
                </form>
          </Form>
          <div className="mt-4">
            <Separator className="my-4" />
            <p className="px-8 text-center text-sm text-muted-foreground">
              Ao clicar em entrar, você concorda com nossos{" "}
              <Link
                to="/terms"
                className="underline underline-offset-4 hover:text-primary"
              >
                Termos de Serviço
              </Link>{" "}
              e{" "}
              <Link
                to="/privacy"
                className="underline underline-offset-4 hover:text-primary"
              >
                Política de Privacidade
              </Link>
              .
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
