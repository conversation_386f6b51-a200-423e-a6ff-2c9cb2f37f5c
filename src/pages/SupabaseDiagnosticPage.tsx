import { SupabaseDiagnostic } from '@/components/SupabaseDiagnostic';
import { TopNav } from '@/components/top-nav';
import { Database, ServerCog } from 'lucide-react';

export default function SupabaseDiagnosticPage() {
  return (
    <div className="flex flex-col min-h-screen">
      <TopNav
        title="Diagnóstico do Supabase"
        icon={<Database className="h-6 w-6 text-primary" />}
      />
      
      <main className="flex-1 container max-w-screen-lg mx-auto p-6 pt-20">
        <div className="flex flex-col items-center justify-center h-full">
          <div className="mb-8 flex items-center justify-center">
            <ServerCog className="h-16 w-16 text-primary" />
          </div>
          
          <h1 className="text-2xl font-bold mb-8 text-center">
            Diagnóstico de Conexão com o Banco de Dados
          </h1>
          
          <SupabaseDiagnostic />
          
          <div className="mt-8 text-center text-sm text-muted-foreground">
            <p>
              Esta página verifica a conexão com o banco de dados Supabase e ajuda a diagnosticar problemas.
            </p>
            <p className="mt-2">
              Se você estiver enfrentando problemas de carregamento de dados, tente resetar a autenticação.
            </p>
          </div>
        </div>
      </main>
    </div>
  );
}
