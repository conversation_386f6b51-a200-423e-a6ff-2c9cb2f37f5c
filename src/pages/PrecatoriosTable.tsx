import { useState, useEffect, useMemo, useCallback, useRef } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useVisibilityChange } from "@/hooks/useVisibilityChange";
import {
  FileSpreadsheet,
  Search,
  Filter,
  Plus,
  CircleDollarSign,
  Clock,
  CheckCircle2,
  FileText,
  SlidersHorizontal
} from "lucide-react";
import { TopNav } from "@/components/top-nav";
import { toast } from "sonner";
import { formatarMoeda } from "@/lib/utils";
import { CardLoading } from "@/components/ui/loading-spinner";
import { supabase } from "@/lib/supabase";
import { NoPrecatoriosMessage } from "@/components/ui/no-results-message";

// Componentes UI
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";

// Tanstack Table
import type { ColumnDef } from "@tanstack/react-table";
import {
  flexRender,
  getCoreRowModel,
  useReactTable,
  getPaginationRowModel,
  getSortedRowModel,
  type SortingState,
} from "@tanstack/react-table";

// Serviços e tipos
import { buscarTodosPrecatorios } from "@/services/precatoriosService";
import { clearCache } from "@/services/cacheService";
import { clearSupabaseCache } from "@/lib/supabase";
import { CSSProperties } from "react";

// Componentes personalizados
import { PrecatorioCardView } from "@/components/Precatorios/PrecatorioCardView";
import { PrecatoriosPagination } from "@/components/Precatorios/PrecatoriosPagination";
import { PrecatoriosViewToggle } from "@/components/Precatorios/PrecatoriosViewToggle";
import { PrecatoriosFilterModal, PrecatoriosFiltros } from "@/components/Precatorios/PrecatoriosFilterModal";

// Interface para o tipo Precatorio
interface Precatorio {
  id: string;
  numero: string;
  tribunal: string;
  valor: number;
  status: string;
  tipo: 'PRECATORIO' | 'RPV';
  cliente: {
    id: string;
    nome: string;
  };
  responsavel?: {
    id: string;
    nome: string;
  };
  dataCriacao: string;
  dataAtualizacao: string;
}

// Componente para renderizar o cabeçalho da tabela
const TableColumnHeader = ({ column, title }: { column: any, title: string }) => {
  // Desabilitar ordenação para evitar travamentos
  return (
    <div className="flex items-center justify-start gap-0.5">
      <span className="grow truncate">{title}</span>
    </div>
  );
};

// Função para obter a cor do status
const getStatusColor = (status: string) => {
  const statusMap: Record<string, string> = {
    'NOVO': 'bg-blue-100 text-blue-800 dark:bg-blue-950/70 dark:text-blue-200',
    'EM_ANALISE': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-950/70 dark:text-yellow-200',
    'APROVADO': 'bg-green-100 text-green-800 dark:bg-green-950/70 dark:text-green-200',
    'REJEITADO': 'bg-red-100 text-red-800 dark:bg-red-950/70 dark:text-red-200',
    'EM_PROCESSAMENTO': 'bg-purple-100 text-purple-800 dark:bg-purple-950/70 dark:text-purple-200',
    'CONCLUIDO': 'bg-teal-100 text-teal-800 dark:bg-teal-950/70 dark:text-teal-200',
    'CANCELADO': 'bg-gray-100 text-gray-800 dark:bg-gray-950/70 dark:text-gray-200',
    'analise': 'bg-blue-100 text-blue-800 dark:bg-blue-950/70 dark:text-blue-200',
    'proposta_tmj': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-950/70 dark:text-yellow-200',
    'proposta_btg': 'bg-purple-100 text-purple-800 dark:bg-purple-950/70 dark:text-purple-200',
    'negociacao': 'bg-orange-100 text-orange-800 dark:bg-orange-950/70 dark:text-orange-200',
    'documentacao': 'bg-indigo-100 text-indigo-800 dark:bg-indigo-950/70 dark:text-indigo-200',
    'pagamento': 'bg-green-100 text-green-800 dark:bg-green-950/70 dark:text-green-200',
    'concluido': 'bg-teal-100 text-teal-800 dark:bg-teal-950/70 dark:text-teal-200',
    'cancelado': 'bg-gray-100 text-gray-800 dark:bg-gray-950/70 dark:text-gray-200',
  };

  return statusMap[status] || 'bg-gray-100 text-gray-800 dark:bg-gray-950/70 dark:text-gray-200';
};

// Função para formatar o status para exibição
const formatarStatus = (status: string) => {
  const statusMap: Record<string, string> = {
    'NOVO': 'Novo',
    'EM_ANALISE': 'Em Análise',
    'APROVADO': 'Aprovado',
    'REJEITADO': 'Rejeitado',
    'EM_PROCESSAMENTO': 'Em Processamento',
    'CONCLUIDO': 'Concluído',
    'CANCELADO': 'Cancelado',
    'analise': 'Análise',
    'proposta_tmj': 'Proposta TMJ',
    'proposta_btg': 'Proposta BTG',
    'negociacao': 'Negociação',
    'documentacao': 'Documentação',
    'pagamento': 'Pagamento',
    'concluido': 'Concluído',
    'cancelado': 'Cancelado',
  };

  return statusMap[status] || status;
};

// Interface para estatísticas
interface EstatisticasPrecatorios {
  total: number;
  valorTotal: number;
  emAndamento: number;
  concluidos: number;
  mediaPrazo: number;
}

function PrecatoriosTable() {
  const [precatorios, setPrecatorios] = useState<Precatorio[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [searchParams, setSearchParams] = useSearchParams();
  const [tipoVisualizacao, setTipoVisualizacao] = useState<'PRECATORIO' | 'RPV'>(
    (searchParams.get('tipo') as 'PRECATORIO' | 'RPV') || 'PRECATORIO'
  );
  const [authError, setAuthError] = useState(false);

  const [columnOrder, setColumnOrder] = useState<string[]>([
    "numero",
    "cliente",
    "tribunal",
    "valor",
    "status",
    "tipo",
    "dataCriacao",
  ]);

  // Novos estados para as funcionalidades adicionadas
  const [viewMode, setViewMode] = useState<"table" | "card">("table");
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [sorting, setSorting] = useState<SortingState>([]);
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);
  const [filtrosAvancados, setFiltrosAvancados] = useState<PrecatoriosFiltros>({
    status: [],
    tribunal: [],
  });

  // Estatísticas calculadas com base nos precatórios
  const estatisticas = useMemo<EstatisticasPrecatorios & { statusCount: Record<string, number>, tribunalCount: Record<string, number> }>(() => {
    const total = precatorios.length;
    const valorTotal = precatorios.reduce((sum, p) => sum + (p.valor || 0), 0);
    const concluidos = precatorios.filter(p => p.status === 'concluido' || p.status === 'CONCLUIDO').length;
    const emAndamento = total - concluidos;

    // Contar status
    const statusCount: Record<string, number> = {};
    precatorios.forEach(p => {
      statusCount[p.status] = (statusCount[p.status] || 0) + 1;
    });

    // Contar tribunais
    const tribunalCount: Record<string, number> = {};
    precatorios.forEach(p => {
      tribunalCount[p.tribunal] = (tribunalCount[p.tribunal] || 0) + 1;
    });

    return {
      total,
      valorTotal,
      emAndamento,
      concluidos,
      mediaPrazo: 120, // Valor mockado por enquanto
      statusCount,
      tribunalCount
    };
  }, [precatorios]);
  const navigate = useNavigate();



  // Definição das colunas
  const columns: ColumnDef<Precatorio>[] = [
    {
      id: "numero",
      accessorKey: "numero",
      header: "Número",
      cell: ({ row }) => <div className="font-medium">{row.original.numero}</div>,

    },
    {
      id: "cliente",
      accessorKey: "cliente.nome",
      header: "Cliente",
      cell: ({ row }) => <div>{row.original.cliente.nome}</div>,

    },
    {
      id: "tribunal",
      accessorKey: "tribunal",
      header: "Tribunal",
      cell: ({ row }) => <div>{row.original.tribunal}</div>,

    },
    {
      id: "valor",
      accessorKey: "valor",
      header: "Valor",
      cell: ({ row }) => <div>{formatarMoeda(row.original.valor)}</div>,

    },
    {
      id: "status",
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => (
        <Badge className={getStatusColor(row.original.status)}>
          {formatarStatus(row.original.status)}
        </Badge>
      ),

    },
    {
      id: "tipo",
      accessorKey: "tipo",
      header: "Tipo",
      cell: ({ row }) => (
        <Badge variant="outline">
          {row.original.tipo === 'PRECATORIO' ? 'Precatório' : 'RPV'}
        </Badge>
      ),

    },
    {
      id: "dataCriacao",
      accessorKey: "dataCriacao",
      header: "Data de Criação",
      cell: ({ row }) => <div>{new Date(row.original.dataCriacao).toLocaleDateString('pt-BR')}</div>,

    },
  ];

  // Referência para controlar se o componente está montado
  const isMountedRef = useRef(true);

  // Referência para controlar se há um carregamento em andamento
  const isLoadingRef = useRef(false);

  // Função para recarregar a página
  const handleRefresh = () => {
    window.location.reload();
  };

  // Função para carregar os precatórios
  const carregarPrecatorios = useCallback(async (showToast = true) => {
    // Evitar múltiplos carregamentos simultâneos
    if (isLoadingRef.current) {
      console.log("PrecatoriosTable: Carregamento já em andamento, ignorando solicitação");
      return;
    }

    console.log("PrecatoriosTable: Iniciando carregamento de precatórios...");
    isLoadingRef.current = true;
    setLoading(true);
    setAuthError(false);

    try {
      // Verificar se o componente ainda está montado
      if (!isMountedRef.current) {
        console.log("PrecatoriosTable: Componente desmontado, cancelando carregamento");
        return;
      }

      // Verificar se a sessão está válida antes de carregar os dados
      try {
        const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
        if (sessionError || !sessionData.session) {
          console.warn("PrecatoriosTable: Sessão inválida, tentando recuperar...");

          // Tentar recuperar a sessão
          const { authManager } = await import('@/lib/authManager');
          const sessionValid = await authManager.checkSession();

          if (!sessionValid) {
            console.error("PrecatoriosTable: Falha ao recuperar sessão");
            setAuthError(true);
            setLoading(false);
            isLoadingRef.current = false;
            return;
          }
        }
      } catch (sessionError) {
        console.error("PrecatoriosTable: Erro ao verificar sessão:", sessionError);
        setAuthError(true);
        setLoading(false);
        isLoadingRef.current = false;
        return;
      }

      // Limpar apenas o cache específico para este tipo de visualização
      // em vez de limpar todos os caches relacionados a precatórios
      const cacheKey = `precatorios_${tipoVisualizacao}`;
      clearCache(cacheKey);

      // Forçar uma pequena pausa para garantir que os caches foram limpos
      await new Promise(resolve => setTimeout(resolve, 100));

      // Verificar novamente se o componente ainda está montado
      if (!isMountedRef.current) {
        console.log("PrecatoriosTable: Componente desmontado após limpeza de cache, cancelando carregamento");
        return;
      }

      // Buscar dados atualizados
      const precatoriosData = await buscarTodosPrecatorios(tipoVisualizacao);
      console.log(`PrecatoriosTable: Recebidos ${precatoriosData?.length || 0} precatórios do servidor`);

      // Formatar os dados para o formato da interface
      const precatoriosFormatados = precatoriosData.map((item: any): Precatorio => ({
        id: item.id,
        numero: item.numero || item.numero_precatorio || 'Sem número',
        tribunal: item.tribunal || 'Não informado',
        valor: item.valor || item.valor_total || 0,
        status: item.status || 'Não definido',
        tipo: item.tipo || tipoVisualizacao,
        cliente: {
          id: item.cliente?.id || item.cliente_id || 'sem-id',
          nome: item.cliente?.nome || 'Cliente não informado'
        },
        responsavel: item.responsavel ? {
          id: item.responsavel.id || 'sem-id',
          nome: item.responsavel.nome || 'Não atribuído'
        } : undefined,
        dataCriacao: item.dataCriacao || item.data_criacao || new Date().toISOString(),
        dataAtualizacao: item.dataAtualizacao || item.data_atualizacao || new Date().toISOString()
      }));

      setPrecatorios(precatoriosFormatados);

      if (showToast) {
        toast.success(`${precatoriosFormatados.length} ${tipoVisualizacao === 'PRECATORIO' ? 'precatórios' : 'RPVs'} carregados`);
      }

      console.log("PrecatoriosTable: Carregamento concluído com sucesso");
    } catch (error) {
      console.error("Erro ao carregar precatórios:", error);

      // Verificar se é um erro de autenticação
      if (error instanceof Error &&
          (error.message.includes('auth') ||
           error.message.includes('JWT') ||
           error.message.includes('session'))) {
        setAuthError(true);
        toast.error("Erro de autenticação. Tente recarregar a página ou fazer login novamente.");
      } else {
        toast.error("Erro ao carregar precatórios. Tente novamente mais tarde.");
      }

      // Tentar novamente após um breve atraso
      const retryTimeout = setTimeout(() => {
        // Verificar se o componente ainda está montado
        if (!isMountedRef.current) {
          console.log("PrecatoriosTable: Componente desmontado, cancelando segunda tentativa");
          return;
        }

        console.log("PrecatoriosTable: Tentando carregar precatórios novamente após erro");

        if (isMountedRef.current) {
          setLoading(true);
        }

        buscarTodosPrecatorios(tipoVisualizacao)
          .then(precatoriosData => {
            // Verificar novamente se o componente ainda está montado
            if (!isMountedRef.current) {
              console.log("PrecatoriosTable: Componente desmontado durante segunda tentativa, cancelando atualização");
              return;
            }

            if (precatoriosData && Array.isArray(precatoriosData)) {
              const precatoriosFormatados = precatoriosData.map((item: any): Precatorio => ({
                id: item.id,
                numero: item.numero || item.numero_precatorio || 'Sem número',
                tribunal: item.tribunal || 'Não informado',
                valor: item.valor || item.valor_total || 0,
                status: item.status || 'Não definido',
                tipo: item.tipo || tipoVisualizacao,
                cliente: {
                  id: item.cliente?.id || item.cliente_id || 'sem-id',
                  nome: item.cliente?.nome || 'Cliente não informado'
                },
                responsavel: item.responsavel ? {
                  id: item.responsavel.id || 'sem-id',
                  nome: item.responsavel.nome || 'Não atribuído'
                } : undefined,
                dataCriacao: item.dataCriacao || item.data_criacao || new Date().toISOString(),
                dataAtualizacao: item.dataAtualizacao || item.data_atualizacao || new Date().toISOString()
              }));

              if (isMountedRef.current) {
                setPrecatorios(precatoriosFormatados);
                console.log("PrecatoriosTable: Segunda tentativa concluída com sucesso");
              }
            }
          })
          .catch(err => {
            console.error("Erro na segunda tentativa de carregar precatórios:", err);
          })
          .finally(() => {
            // Verificar se o componente ainda está montado antes de atualizar o estado
            if (isMountedRef.current) {
              setLoading(false);
            }
            // Resetar a flag de carregamento em andamento
            isLoadingRef.current = false;
          });
      }, 1500);

      // Registrar o timeout para limpeza
      const originalCleanup = useRef<(() => void) | null>(null);
      originalCleanup.current = () => clearTimeout(retryTimeout);
    } finally {
      // Verificar se o componente ainda está montado antes de atualizar o estado
      if (isMountedRef.current) {
        setLoading(false);
      }
      // Resetar a flag de carregamento em andamento
      isLoadingRef.current = false;
    }
  }, [tipoVisualizacao]);

  // Usar o hook de visibilidade para recarregar dados quando a página voltar a ficar visível
  useVisibilityChange(
    // Função para recarregar dados sem mostrar toast
    async () => {
      // Verificar se o componente ainda está montado
      if (isMountedRef.current && !isLoadingRef.current) {
        await carregarPrecatorios(false);
      }
    },
    {
      clearCacheOnReturn: true,
      refreshSessionOnReturn: true,
      minTimeSinceLastVisible: 30000 // 30 segundos
    }
  );

  // Não precisamos mais de um listener específico para o evento de sessão atualizada
  // pois o hook useVisibilityChange já cuida disso

  // Carregar precatórios quando o componente montar ou o tipo mudar
  useEffect(() => {
    // Resetar a flag de montado
    isMountedRef.current = true;

    console.log(`PrecatoriosTable: Tipo de visualização alterado para ${tipoVisualizacao}, recarregando dados`);

    // Definir um pequeno timeout para evitar problemas de concorrência
    const loadTimeout = setTimeout(() => {
      if (isMountedRef.current) {
        carregarPrecatorios();
      }
    }, 100);

    // Função de limpeza quando o componente for desmontado
    return () => {
      console.log("PrecatoriosTable: Componente desmontado, limpando recursos");
      isMountedRef.current = false;
      clearTimeout(loadTimeout);
    };
  }, [tipoVisualizacao, carregarPrecatorios]);

  // Atualizar o tipo de visualização quando o parâmetro de URL mudar
  useEffect(() => {
    const tipo = searchParams.get('tipo') as 'PRECATORIO' | 'RPV' | null;
    if (tipo) {
      setTipoVisualizacao(tipo);
    }
  }, [searchParams]);

  // Função para filtrar precatórios com base no termo de busca e filtros avançados
  const precatoriosFiltrados = useMemo(() => {
    // Filtrar por tipo de visualização (PRECATORIO ou RPV)
    let filtrados = precatorios.filter(p => p.tipo === tipoVisualizacao);

    // Aplicar termo de busca
    if (searchTerm) {
      const termLower = searchTerm.toLowerCase();
      filtrados = filtrados.filter(p =>
        p.numero.toLowerCase().includes(termLower) ||
        p.cliente.nome.toLowerCase().includes(termLower) ||
        p.tribunal.toLowerCase().includes(termLower) ||
        (p.responsavel?.nome?.toLowerCase().includes(termLower) || false) ||
        p.status.toLowerCase().includes(termLower)
      );
    }

    // Aplicar filtros avançados
    if (filtrosAvancados.status.length > 0) {
      filtrados = filtrados.filter(p => filtrosAvancados.status.includes(p.status));
    }

    if (filtrosAvancados.tribunal.length > 0) {
      filtrados = filtrados.filter(p => filtrosAvancados.tribunal.includes(p.tribunal));
    }

    if (filtrosAvancados.valorMin !== undefined) {
      filtrados = filtrados.filter(p => p.valor >= (filtrosAvancados.valorMin || 0));
    }

    if (filtrosAvancados.valorMax !== undefined) {
      filtrados = filtrados.filter(p => p.valor <= (filtrosAvancados.valorMax || 0));
    }

    if (filtrosAvancados.dataInicio !== undefined) {
      const dataInicio = new Date(filtrosAvancados.dataInicio);
      filtrados = filtrados.filter(p => {
        const dataCriacao = new Date(p.dataCriacao);
        return dataCriacao >= dataInicio;
      });
    }

    if (filtrosAvancados.dataFim !== undefined) {
      const dataFim = new Date(filtrosAvancados.dataFim);
      dataFim.setHours(23, 59, 59, 999); // Fim do dia
      filtrados = filtrados.filter(p => {
        const dataCriacao = new Date(p.dataCriacao);
        return dataCriacao <= dataFim;
      });
    }

    return filtrados;
  }, [precatorios, searchTerm, tipoVisualizacao, filtrosAvancados]);

  // Calcular dados paginados
  const paginatedData = useMemo(() => {
    const firstPageIndex = (currentPage - 1) * pageSize;
    const lastPageIndex = firstPageIndex + pageSize;
    return precatoriosFiltrados.slice(firstPageIndex, lastPageIndex);
  }, [precatoriosFiltrados, currentPage, pageSize]);

  // Calcular total de páginas
  const totalPages = useMemo(() => {
    return Math.ceil(precatoriosFiltrados.length / pageSize);
  }, [precatoriosFiltrados.length, pageSize]);

  // Função para navegar para a página de detalhes
  const handlePrecatorioClick = (id: string) => {
    navigate(`/precatorios/${id}`);
  };



  // Função para mudar de página
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Função para mudar o tamanho da página
  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1); // Resetar para a primeira página ao mudar o tamanho
  };

  // Função para alternar entre visualização de tabela e cards
  const handleViewModeChange = (mode: "table" | "card") => {
    setViewMode(mode);
  };

  // Função para abrir o modal de filtros avançados
  const handleOpenFilterModal = () => {
    setIsFilterModalOpen(true);
  };

  // Função para aplicar filtros avançados
  const aplicarFiltrosAvancados = (filtros: PrecatoriosFiltros) => {
    setFiltrosAvancados(filtros);
    setCurrentPage(1); // Resetar para a primeira página ao aplicar filtros
  };

  // Função para limpar todos os filtros
  const limparFiltros = () => {
    setFiltrosAvancados({
      status: [],
      tribunal: [],
    });
    setSearchTerm("");
    setCurrentPage(1);
  };

  // Configuração da tabela
  const table = useReactTable({
    data: paginatedData,
    columns,
    state: {
      columnOrder,
      sorting
    },
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    columnResizeMode: "onChange",
  });

  return (
    <div className="flex flex-col h-screen w-screen overflow-hidden">
      <TopNav
        title="Precatório/RPV"
        icon={<FileSpreadsheet className="h-6 w-6 text-primary" />}
      />

      <div className="flex-1 p-6 pt-[75px] overflow-auto">
        {/* Header */}
        <div className="flex flex-col gap-4 mb-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-muted-foreground">
                Gerencie os {tipoVisualizacao === 'PRECATORIO' ? 'precatórios' : 'RPVs'} e seus processos
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                className="gap-2"
                onClick={handleOpenFilterModal}
              >
                <SlidersHorizontal size={16} />
                Filtros Avançados
              </Button>
              <Button
                variant="outline"
                className="gap-2"
                onClick={() => carregarPrecatorios()}
              >
                <Filter size={16} />
                Atualizar
              </Button>
              <Button className="gap-2" onClick={() => navigate('/precatorios/novo')}>
                <Plus size={16} />
                Novo {tipoVisualizacao === 'PRECATORIO' ? 'Precatório' : 'RPV'}
              </Button>
            </div>
          </div>

          {/* Estatísticas */}
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardContent className="flex flex-row items-center justify-between p-6">
                <div className="flex flex-col gap-1">
                  <p className="text-sm text-muted-foreground">Total de {tipoVisualizacao === 'PRECATORIO' ? 'Precatórios' : 'RPVs'}</p>
                  <div className="flex items-center gap-2">
                    <p className="text-2xl font-bold">{estatisticas.total}</p>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {estatisticas.emAndamento} em andamento
                  </p>
                </div>
                <div className="rounded-full bg-muted p-3">
                  <FileText className="w-4 h-4" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="flex flex-row items-center justify-between p-6">
                <div className="flex flex-col gap-1">
                  <p className="text-sm text-muted-foreground">Valor Total</p>
                  <div className="flex items-center gap-2">
                    <p className="text-2xl font-bold">{formatarMoeda(estatisticas.valorTotal)}</p>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Média: {formatarMoeda(estatisticas.total > 0 ? estatisticas.valorTotal / estatisticas.total : 0)}
                  </p>
                </div>
                <div className="rounded-full bg-muted p-3">
                  <CircleDollarSign className="w-4 h-4" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="flex flex-row items-center justify-between p-6">
                <div className="flex flex-col gap-1">
                  <p className="text-sm text-muted-foreground">Prazo Médio</p>
                  <div className="flex items-center gap-2">
                    <p className="text-2xl font-bold">{estatisticas.mediaPrazo}</p>
                    <Badge variant="outline" className="text-xs">dias</Badge>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Estimativa de conclusão
                  </p>
                </div>
                <div className="rounded-full bg-muted p-3">
                  <Clock className="w-4 h-4" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="flex flex-row items-center justify-between p-6">
                <div className="flex flex-col gap-1">
                  <p className="text-sm text-muted-foreground">Concluídos</p>
                  <div className="flex items-center gap-2">
                    <p className="text-2xl font-bold">{estatisticas.concluidos}</p>
                    <Badge variant="default" className="text-xs">
                      {estatisticas.total > 0 ? Math.round((estatisticas.concluidos / estatisticas.total) * 100) : 0}%
                    </Badge>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Taxa de conclusão
                  </p>
                </div>
                <div className="rounded-full bg-muted p-3">
                  <CheckCircle2 className="w-4 h-4" />
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Filtros e Pesquisa */}
        <div className="flex flex-wrap justify-between items-center mb-6">
          <div className="flex flex-wrap items-center gap-4">
            <Tabs
              value={tipoVisualizacao}
              onValueChange={(value) => {
                const newTipo = value as 'PRECATORIO' | 'RPV';
                setTipoVisualizacao(newTipo);
                setSearchParams({ tipo: newTipo });
              }}
              className="w-[400px]"
            >
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="PRECATORIO">Precatórios</TabsTrigger>
                <TabsTrigger value="RPV">RPVs</TabsTrigger>
              </TabsList>
            </Tabs>

            <div className="relative w-full max-w-sm">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Buscar precatórios..."
                className="pl-8 w-full"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>

          <PrecatoriosViewToggle viewMode={viewMode} onViewModeChange={handleViewModeChange} />
        </div>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Lista de {tipoVisualizacao === 'PRECATORIO' ? 'Precatórios' : 'RPVs'}</CardTitle>
            <CardDescription>
              {precatoriosFiltrados.length} {tipoVisualizacao === 'PRECATORIO' ? 'precatórios' : 'RPVs'} encontrados
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div>
              {loading ? (
                <CardLoading text="Carregando precatórios..." description="Aguarde enquanto buscamos os dados" />
              ) : precatorios.length === 0 ? (
                <NoPrecatoriosMessage
                  showClearFiltersButton={searchTerm !== "" || Object.values(filtrosAvancados).some(v => Array.isArray(v) ? v.length > 0 : v !== undefined)}
                  onClearFilters={limparFiltros}
                  showRefreshButton={true}
                  onRefresh={handleRefresh}
                  isAuthError={authError}
                />
              ) : viewMode === "table" ? (
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        {columnOrder.map((columnId) => {
                          const column = table.getColumn(columnId);
                          if (!column) return null;

                          return (
                            <TableHead
                              key={columnId}
                              className="relative h-10 border-t before:absolute before:inset-y-0 before:start-0 before:w-px before:bg-border first:before:bg-transparent"
                            >
                              <TableColumnHeader
                                column={column}
                                title={typeof column.columnDef.header === 'string' ? column.columnDef.header : columnId}
                              />
                            </TableHead>
                          );
                        })}
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {table.getRowModel().rows.length > 0 ? (
                        table.getRowModel().rows.map((row) => (
                          <TableRow
                            key={row.id}
                            className="cursor-pointer"
                            onClick={() => handlePrecatorioClick(row.original.id)}
                          >
                            {columnOrder.map((columnId) => {
                              const cell = row.getAllCells().find(c => c.column.id === columnId);
                              if (!cell) return null;

                              return (
                                <TableCell key={cell.id}>
                                  {flexRender(
                                    cell.column.columnDef.cell,
                                    cell.getContext()
                                  )}
                                </TableCell>
                              );
                            })}
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={columns.length} className="h-24">
                            <NoPrecatoriosMessage
                              showClearFiltersButton={searchTerm !== "" || Object.values(filtrosAvancados).some(v => Array.isArray(v) ? v.length > 0 : v !== undefined)}
                              onClearFilters={limparFiltros}
                              showRefreshButton={true}
                              onRefresh={handleRefresh}
                              isAuthError={authError}
                            />
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {paginatedData.map((precatorio) => (
                    <PrecatorioCardView
                      key={precatorio.id}
                      precatorio={precatorio}
                      onClick={handlePrecatorioClick}
                    />
                  ))}
                </div>
              )}
            </div>

            {/* Paginação */}
            {precatoriosFiltrados.length > 0 && (
              <PrecatoriosPagination
                currentPage={currentPage}
                totalPages={totalPages}
                pageSize={pageSize}
                totalItems={precatoriosFiltrados.length}
                onPageChange={handlePageChange}
                onPageSizeChange={handlePageSizeChange}
                tipoVisualizacao={tipoVisualizacao}
              />
            )}
          </CardContent>
        </Card>
      </div>

      {/* Modal de Filtros Avançados */}
      <PrecatoriosFilterModal
        isOpen={isFilterModalOpen}
        onOpenChange={setIsFilterModalOpen}
        filtros={filtrosAvancados}
        onAplicarFiltros={aplicarFiltrosAvancados}
        onLimparFiltros={limparFiltros}
        estatisticas={estatisticas}
        tipoVisualizacao={tipoVisualizacao}
      />
    </div>
  );
}

export default PrecatoriosTable;
