import {
  Scale,
  FileText,
  Users,
  Settings,
  LayoutDashboard,
  ArrowRight,
  Calendar,
  Clock,
  Folder,
  BookOpen,
  MessageSquare,
  FileCheck,
  Bell,
  Building2,
  Briefcase,
  Target,
  TrendingUp,
  ChevronRight,
  Star,
  Activity,
  CheckCircle2,
  AlertCircle,
  Timer,
  FileIcon,
  Gavel,
  BarChart,
  PieChart,
  CalendarDays,
  User,
  Home as HomeIcon,
  MapPin,
  Phone,
  Mail,
  Link2,
  ExternalLink,
  Download,
  Plus,
  Filter,
  SlidersHorizontal,
  Tag,
  CalendarRange,
  ListTodo,
  CheckSquare,
  XCircle,
  Clock4,
  Users2,
  Repeat,
  ArrowUpRight,
  MoreVertical,
  Banknote,
  CircleDollarSign
} from "lucide-react";
import { BentoCard, BentoGrid } from "@/components/ui/bento-grid";
import { OrbEffect } from "@/components/ui/orb-effect";
import { motion, AnimatePresence } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { TopNav } from "@/components/top-nav";

const features = [
  {
    Icon: Banknote,
    name: "Precatórios",
    description: "Gerencie precatórios, acompanhe prazos e pagamentos.",
    href: "/precatorios-management",
    cta: "Acessar Precatórios",
    background: "bg-gradient-to-br from-neutral-50/80 via-white/80 to-neutral-100/80 dark:from-neutral-900/80 dark:via-neutral-950/80 dark:to-black/80",
    className: "lg:col-start-1 lg:col-end-2 lg:row-start-1 lg:row-end-3 hover:shadow-xl transition-all duration-300",
  },
  {
    Icon: CheckSquare,
    name: "Tarefas",
    description: "Controle de tarefas, prazos e responsabilidades.",
    href: "/tasks",
    cta: "Ver Tarefas",
    background: "bg-gradient-to-br from-neutral-50/80 via-white/80 to-neutral-100/80 dark:from-neutral-900/80 dark:via-neutral-950/80 dark:to-black/80",
    className: "lg:row-start-1 lg:row-end-2 lg:col-start-2 lg:col-end-3 hover:shadow-xl transition-all duration-300",
  },
  {
    Icon: Users,
    name: "Clientes",
    description: "Gestão de beneficiários e seus precatórios.",
    href: "/clients",
    cta: "Gerenciar Clientes",
    background: "bg-gradient-to-br from-neutral-50/80 via-white/80 to-neutral-100/80 dark:from-neutral-900/80 dark:via-neutral-950/80 dark:to-black/80",
    className: "lg:col-start-2 lg:col-end-3 lg:row-start-2 lg:row-end-4 hover:shadow-xl transition-all duration-300",
  },
  {
    Icon: LayoutDashboard,
    name: "Dashboard",
    description: "Análises e métricas dos precatórios.",
    href: "/dashboard",
    cta: "Ver Dashboard",
    background: "bg-gradient-to-br from-neutral-50/80 via-white/80 to-neutral-100/80 dark:from-neutral-900/80 dark:via-neutral-950/80 dark:to-black/80",
    className: "lg:col-start-3 lg:col-end-3 lg:row-start-1 lg:row-end-2 hover:shadow-xl transition-all duration-300",
  },
  {
    Icon: FileCheck,
    name: "Relatórios",
    description: "Relatórios e documentos dos precatórios.",
    href: "/reports",
    cta: "Ver Relatórios",
    background: "bg-gradient-to-br from-neutral-50/80 via-white/80 to-neutral-100/80 dark:from-neutral-900/80 dark:via-neutral-950/80 dark:to-black/80",
    className: "lg:col-start-3 lg:col-end-3 lg:row-start-2 lg:row-end-4 hover:shadow-xl transition-all duration-300",
  },
];

const quickAccess = [
  {
    icon: <Clock className="w-5 h-5 text-blue-500" />,
    title: "Prazos Próximos",
    description: "5 precatórios com prazo esta semana",
    action: "Ver Prazos",
    color: "text-blue-500 bg-blue-100/50 dark:bg-blue-900/20"
  },
  {
    icon: <Bell className="w-5 h-5 text-purple-500" />,
    title: "Pagamentos",
    description: "3 pagamentos previstos para hoje",
    action: "Ver Detalhes",
    color: "text-purple-500 bg-purple-100/50 dark:bg-purple-900/20"
  },
  {
    icon: <CheckSquare className="w-5 h-5 text-emerald-500" />,
    title: "Tarefas",
    description: "8 tarefas pendentes",
    action: "Ver Tarefas",
    color: "text-emerald-500 bg-emerald-100/50 dark:bg-emerald-900/20"
  },
  {
    icon: <Users className="w-5 h-5 text-amber-500" />,
    title: "Beneficiários",
    description: "2 novos cadastros hoje",
    action: "Acessar",
    color: "text-amber-500 bg-amber-100/50 dark:bg-amber-900/20"
  }
];

const stats = [
  {
    title: "Precatórios Ativos",
    value: "180",
    change: "+12",
    trend: "up",
    icon: <Banknote className="w-4 h-4 text-blue-500" />,
    description: "Últimos 30 dias",
    color: "bg-blue-100 dark:bg-blue-900/20"
  },
  {
    title: "Taxa de Sucesso",
    value: "92%",
    change: "+5%",
    trend: "up",
    icon: <Target className="w-4 h-4 text-emerald-500" />,
    description: "Precatórios pagos",
    color: "bg-emerald-100 dark:bg-emerald-900/20"
  },
  {
    title: "Beneficiários",
    value: "220",
    change: "+15",
    trend: "up",
    icon: <Users className="w-4 h-4 text-purple-500" />,
    description: "Base atual",
    color: "bg-purple-100 dark:bg-purple-900/20"
  },
  {
    title: "Valor Total",
    value: "25M",
    change: "+2.5M",
    trend: "up",
    icon: <CircleDollarSign className="w-4 h-4 text-amber-500" />,
    description: "Em precatórios",
    color: "bg-amber-100 dark:bg-amber-900/20"
  }
];

const recentActivities = [
  {
    type: "precatorio",
    title: "Novo precatório cadastrado",
    description: "Precatório nº 2024.001.123 - João da Silva",
    time: "Há 2 horas",
    user: {
      name: "Ana Silva",
      avatar: "/avatars/ana.jpg"
    }
  },
  {
    type: "pagamento",
    title: "Pagamento recebido",
    description: "Precatório nº 2024.001.456 - R$ 150.000,00",
    time: "Há 3 horas",
    user: {
      name: "Carlos Santos",
      avatar: "/avatars/carlos.jpg"
    }
  },
  {
    type: "tarefa",
    title: "Tarefa concluída",
    description: "Atualização de documentos - Precatório nº 2024.001.789",
    time: "Há 4 horas",
    user: {
      name: "Maria Costa",
      avatar: "/avatars/maria.jpg"
    }
  }
];

// Novo array de categorias de tarefas
const taskCategories = [
  { id: 1, name: "Precatórios", color: "bg-blue-100 text-blue-500 dark:bg-blue-900/20" },
  { id: 2, name: "Audiências", color: "bg-purple-100 text-purple-500 dark:bg-purple-900/20" },
  { id: 3, name: "Documentos", color: "bg-amber-100 text-amber-500 dark:bg-amber-900/20" },
  { id: 4, name: "Reuniões", color: "bg-emerald-100 text-emerald-500 dark:bg-emerald-900/20" }
];

// Novo array de etiquetas
const taskTags = [
  { id: 1, name: "Urgente", color: "bg-red-100 text-red-500 dark:bg-red-900/20" },
  { id: 2, name: "Em Progresso", color: "bg-amber-100 text-amber-500 dark:bg-amber-900/20" },
  { id: 3, name: "Revisão", color: "bg-blue-100 text-blue-500 dark:bg-blue-900/20" },
  { id: 4, name: "Aguardando", color: "bg-purple-100 text-purple-500 dark:bg-purple-900/20" }
];

// Array de tarefas melhorado
const enhancedPendingTasks = [
  {
    id: 1,
    title: "Revisar petição inicial",
    description: "Revisar e finalizar a documentação do precatório XYZ",
    priority: "Alta",
    deadline: "Hoje, 15:00",
    process: "2024.001.123",
    status: "pending",
    category: 1,
    tags: [1, 3],
    assignedTo: {
      name: "Ana Silva",
      avatar: "/avatars/ana.jpg"
    },
    checklist: [
      { id: 1, text: "Verificar documentação", completed: true },
      { id: 2, text: "Revisar argumentação", completed: false },
      { id: 3, text: "Conferir jurisprudência", completed: false }
    ],
    comments: [
      {
        id: 1,
        user: "Carlos Santos",
        text: "Adicionei os documentos necessários",
        time: "Há 1 hora"
      }
    ],
    attachments: [
      { id: 1, name: "documento1.pdf", size: "2.4 MB", type: "pdf" }
    ],
    recurrence: "none",
    reminder: "15min"
  },
  // ... outras tarefas com o mesmo formato
];

// Array de eventos do calendário melhorado
const enhancedCalendarEvents = [
  {
    id: 1,
    title: "Audiência de Conciliação",
    type: "Audiência",
    date: "2024-02-17",
    time: "14:00",
    duration: 60,
    location: {
      name: "Vara Cível - Sala 304",
      address: "Fórum Central, Av. Principal, 1000",
      coordinates: { lat: -23.550520, lng: -46.633308 }
    },
    process: {
      number: "2024.001.123",
      title: "Empresa ABC Ltda vs XYZ Serviços",
      type: "Cível"
    },
    participants: [
      {
        name: "João Silva",
        role: "Cliente",
        contact: "(11) 99999-9999",
        confirmed: true
      },
      {
        name: "Ana Silva",
        role: "Advogada",
        contact: "(11) 98888-8888",
        confirmed: true
      }
    ],
    documents: [
      { name: "Petição Inicial", type: "pdf", required: true },
      { name: "Procuração", type: "pdf", required: true }
    ],
    status: "confirmed",
    notes: "Levar documentos originais",
    reminder: "1h",
    recurrence: "none",
    color: "bg-blue-100 text-blue-500 dark:bg-blue-900/20",
    priority: "Alta"
  },
  {
    id: 2,
    title: "Reunião com Cliente",
    type: "Reunião",
    date: "2024-02-17",
    time: "16:30",
    duration: 45,
    location: {
      name: "Escritório - Sala de Reuniões",
      address: "Av. Paulista, 1000",
      coordinates: { lat: -23.550520, lng: -46.633308 }
    },
    process: {
      number: "2024.001.456",
      title: "Maria Oliveira vs Empresa QWE",
      type: "Trabalhista"
    },
    participants: [
      {
        name: "Maria Oliveira",
        role: "Cliente",
        contact: "(11) 97777-7777",
        confirmed: true
      }
    ],
    documents: [
      { name: "Relatório", type: "pdf", required: true }
    ],
    status: "confirmed",
    notes: "Discutir próximos passos",
    reminder: "30min",
    recurrence: "none",
    color: "bg-purple-100 text-purple-500 dark:bg-purple-900/20",
    priority: "Média"
  }
];

// Array de precatórios ativos
const precatoriosAtivos = [
  {
    numero: "2024.001.123",
    beneficiario: "João da Silva",
    tipo: "Alimentar",
    valor: 250000,
    status: "Em processamento",
    progress: 65,
    prazo: "2024-02-25",
    responsavel: "Ana Silva",
    prioridade: "Alta"
  },
  {
    numero: "2024.001.456",
    beneficiario: "Maria Oliveira",
    tipo: "Comum",
    valor: 180000,
    status: "Aguardando pagamento",
    progress: 40,
    prazo: "2024-02-21",
    responsavel: "Carlos Santos",
    prioridade: "Média"
  },
  {
    numero: "2024.001.789",
    beneficiario: "Pedro Santos",
    tipo: "Preferencial",
    valor: 320000,
    status: "Em análise",
    progress: 85,
    prazo: "2024-03-01",
    responsavel: "Maria Costa",
    prioridade: "Baixa"
  }
];

// Array de próximas tarefas
const proximasTarefas = [
  {
    id: 1,
    tipo: "Documentação",
    data: "2024-02-17",
    hora: "14:00",
    precatorio: "2024.001.123",
    status: "pendente",
    descricao: "Atualizar documentos do beneficiário",
    responsavel: {
      nome: "João Silva",
      telefone: "(11) 99999-9999"
    },
    documentos: [
      { nome: "Petição", tipo: "pdf" },
      { nome: "Documentos pessoais", tipo: "pdf" }
    ]
  },
  {
    id: 2,
    tipo: "Análise",
    data: "2024-02-19",
    hora: "10:00",
    precatorio: "2024.001.456",
    status: "em_andamento",
    descricao: "Análise de cálculos do precatório",
    responsavel: {
      nome: "Maria Oliveira",
      telefone: "(11) 98888-8888"
    },
    documentos: [
      { nome: "Planilha de cálculos", tipo: "xlsx" },
      { nome: "Documentos", tipo: "pdf" }
    ]
  }
];

// Array de documentos recentes
const documentosRecentes = [
  {
    id: 1,
    nome: "Petição - Precatório 2024.001.123",
    tipo: "pdf",
    tamanho: "2.4 MB",
    status: "final",
    autor: "Ana Silva",
    data: new Date().toISOString()
  },
  {
    id: 2,
    nome: "Cálculos - Precatório 2024.001.456",
    tipo: "xlsx",
    tamanho: "1.8 MB",
    status: "em_revisao",
    autor: "Carlos Santos",
    data: new Date(Date.now() - 86400000).toISOString() // ontem
  },
  {
    id: 3,
    nome: "Documentos - Precatório 2024.001.789",
    tipo: "pdf",
    tamanho: "3.2 MB",
    status: "final",
    autor: "Maria Costa",
    data: new Date(Date.now() - 172800000).toISOString() // 2 dias atrás
  }
];

const Home = () => {
  const currentHour = new Date().getHours();
  const greeting = currentHour < 12 ? "Bom dia" : currentHour < 18 ? "Boa tarde" : "Boa noite";
  const today = new Date();

  // Função para formatar a data em português
  const formatDateInPtBR = (date: Date) => {
    return format(date, "MMMM 'de' yyyy", { locale: ptBR });
  };

  // Função para formatar o dia da semana em português
  const formatWeekDayInPtBR = (date: Date) => {
    return format(date, "EEEE", { locale: ptBR });
  };

  // Função para formatar hora
  const formatTime = (timeStr: string) => {
    const [hours, minutes] = timeStr.split(':');
    return `${hours}:${minutes}`;
  };

  // Função para formatar data relativa em português
  const formatRelativeDate = (dateStr: string) => {
    const date = new Date(dateStr);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 24) {
      return diffInHours === 0 ? "Há menos de 1 hora" : `Há ${diffInHours} horas`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return diffInDays === 1 ? "Ontem" : `Há ${diffInDays} dias`;
    }
  };

  return (
    <div className="flex flex-col h-screen w-screen overflow-hidden">
      <TopNav
        title="Home"
        icon={<HomeIcon className="h-6 w-6 text-primary" />}
      />

      <div className="relative min-h-screen bg-gradient-to-br from-neutral-50 to-neutral-100 dark:from-neutral-900 dark:to-black pt-[65px] overflow-auto">
        <div className="absolute inset-0 overflow-hidden">
          <OrbEffect className="opacity-20" />
        </div>

        <div className="relative px-4 py-0 mx-auto max-w-7xl">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="space-y-6"
        >
          {/* Header Section */}
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-6 mb-8">
            <div className="relative">
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm text-sm text-neutral-700 dark:text-neutral-300 mb-4 border border-neutral-200/50 dark:border-neutral-700/50 shadow-sm"
              >
                <Banknote className="w-4 h-4 text-neutral-500" />
                <span>Sistema de Gestão de Precatórios</span>
                <Badge variant="secondary" className="ml-2">v2.0</Badge>
              </motion.div>

              <motion.h1
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.3 }}
                className="text-4xl md:text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-neutral-800 to-neutral-600 dark:from-neutral-200 dark:to-neutral-400 leading-tight mb-2"
              >
                {greeting}, Doutor(a)!
              </motion.h1>
              <motion.p
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.4 }}
                className="text-neutral-600 dark:text-neutral-400 text-lg"
              >
                Aqui está o resumo dos seus precatórios
              </motion.p>
            </div>

            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                className="gap-2 bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm hover:bg-neutral-100 dark:hover:bg-neutral-700 shadow-sm"
              >
                <Calendar className="w-4 h-4" />
                Prazos
              </Button>
              <Button
                size="sm"
                className="gap-2 bg-neutral-900 hover:bg-neutral-800 dark:bg-neutral-50 dark:hover:bg-neutral-200 dark:text-neutral-900 shadow-sm"
              >
                Novo Precatório
                <ArrowRight className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Main Content - Reorganização do Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Coluna Principal - Stats e Precatórios */}
            <div className="lg:col-span-2 space-y-6">
              {/* Stats Grid */}
              <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
                {stats.map((stat, i) => (
                  <Card key={stat.title} className="border-neutral-200 dark:border-neutral-700 bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className={cn("p-1.5 rounded-lg", stat.color)}>
                          {stat.icon}
                        </div>
                        <Badge variant="outline" className={cn(
                          "text-xs px-1 h-5",
                          stat.trend === "up" ? "text-emerald-500" : "text-red-500"
                        )}>
                          {stat.change}
                        </Badge>
                      </div>
                      <h3 className="text-xl font-bold mb-1">{stat.value}</h3>
                      <p className="text-xs text-neutral-600 dark:text-neutral-400">{stat.title}</p>
                      <p className="text-[10px] text-neutral-500">{stat.description}</p>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {/* Precatórios Ativos */}
              <Card className="border-neutral-200 dark:border-neutral-700 bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm">
                <CardHeader className="p-4">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-base font-semibold flex items-center gap-2">
                      <Banknote className="w-4 h-4" />
                      Precatórios Ativos
                    </CardTitle>
                    <Button variant="outline" size="sm" className="h-7 text-xs gap-1">
                      <ArrowRight className="w-3 h-3" />
                      Ver Todos
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="p-4 pt-0">
                  <div className="space-y-3">
                    {precatoriosAtivos.map((precatorio, i) => (
                      <div key={i} className="p-3 rounded-lg bg-neutral-50 dark:bg-neutral-800/50">
                        <div className="flex items-start justify-between gap-2">
                          <div className="flex-1 min-w-0">
                            <div className="flex flex-wrap gap-1 mb-2">
                              <h4 className="text-sm font-medium truncate">{precatorio.beneficiario}</h4>
                              <div className="flex flex-wrap gap-1">
                                <Badge variant="outline" className="text-[10px] px-1 h-4">
                                  {precatorio.tipo}
                                </Badge>
                                <Badge variant="outline" className={cn(
                                  "text-[10px] px-1 h-4",
                                  precatorio.prioridade === "Alta" ? "text-red-500" :
                                  precatorio.prioridade === "Média" ? "text-amber-500" :
                                  "text-blue-500"
                                )}>
                                  {precatorio.prioridade}
                                </Badge>
                              </div>
                            </div>
                            <div className="flex flex-wrap gap-2 text-[10px] text-neutral-600 dark:text-neutral-400">
                              <span className="truncate">Precatório: {precatorio.numero}</span>
                              <span>•</span>
                              <span className="truncate">Responsável: {precatorio.responsavel}</span>
                              <span>•</span>
                              <span>
                                {new Intl.NumberFormat('pt-BR', {
                                  style: 'currency',
                                  currency: 'BRL',
                                  notation: 'compact',
                                  maximumFractionDigits: 1
                                }).format(precatorio.valor)}
                              </span>
                            </div>
                            <div className="flex items-center gap-2 mt-2">
                              <Progress value={precatorio.progress} className="h-1.5" />
                              <span className="text-[10px] text-neutral-600 dark:text-neutral-400">
                                {precatorio.progress}%
                              </span>
                            </div>
                          </div>
                          <Badge variant={
                            precatorio.status === "Em processamento" ? "secondary" :
                            precatorio.status === "Aguardando pagamento" ? "default" :
                            "outline"
                          } className="text-[10px]">
                            {precatorio.status}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Coluna Lateral - Calendário e Tarefas */}
            <div className="space-y-6">
              {/* Calendário */}
              <Card className="border-neutral-200 dark:border-neutral-700 bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm">
                <CardHeader className="p-4">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-base font-semibold flex items-center gap-2">
                      <CalendarDays className="w-4 h-4" />
                      {formatDateInPtBR(today)}
                    </CardTitle>
                    <Button size="sm" className="h-7 text-xs gap-1">
                      <Plus className="w-3 h-3" />
                      Novo
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="p-4 pt-0">
                  <div className="space-y-4">
                    <div className="text-center p-3 bg-neutral-50 dark:bg-neutral-800/50 rounded-lg">
                      <h3 className="text-2xl font-bold">{format(today, "dd")}</h3>
                      <p className="text-xs text-neutral-600 dark:text-neutral-400 capitalize">
                        {formatWeekDayInPtBR(today)}
                      </p>
                      <div className="flex items-center justify-center gap-2 mt-2">
                        <Badge variant="outline" className="text-[10px] px-1 h-4 bg-blue-100 text-blue-500 dark:bg-blue-900/20">
                          {enhancedCalendarEvents.filter(e => e.type === "Audiência").length} Audiências
                        </Badge>
                        <Badge variant="outline" className="text-[10px] px-1 h-4 bg-purple-100 text-purple-500 dark:bg-purple-900/20">
                          {enhancedCalendarEvents.filter(e => e.type === "Reunião").length} Reuniões
                        </Badge>
                      </div>
                    </div>

                    {enhancedCalendarEvents.map((event, i) => (
                      <div key={event.id} className="p-3 rounded-lg bg-neutral-50 dark:bg-neutral-800/50">
                        <div className="flex items-start gap-3">
                          <div className="flex-shrink-0 w-12 text-center">
                            <div className="text-base font-bold">
                              {formatTime(event.time)}
                            </div>
                            <div className="text-[10px] text-neutral-600 dark:text-neutral-400">
                              {event.duration}min
                            </div>
                          </div>
                          <div className="flex-1 min-w-0">
                            <h4 className="text-sm font-medium truncate flex items-center gap-1">
                              {event.title}
                              {event.recurrence !== "none" && (
                                <Repeat className="w-3 h-3 text-neutral-400" />
                              )}
                            </h4>
                            <div className="flex flex-wrap gap-1 mt-1">
                              <Badge variant="outline" className="text-[10px] px-1 h-4">
                                {event.type}
                              </Badge>
                              <Badge variant="outline" className="text-[10px] px-1 h-4">
                                {event.process.number}
                              </Badge>
                            </div>
                            <div className="grid grid-cols-1 gap-1 mt-2">
                              <div className="flex items-center gap-1 text-[10px] text-neutral-600 dark:text-neutral-400">
                                <MapPin className="w-3 h-3" />
                                <span className="truncate">{event.location.name}</span>
                              </div>
                              <div className="flex items-center gap-1 text-[10px] text-neutral-600 dark:text-neutral-400">
                                <Users2 className="w-3 h-3" />
                                <span>{event.participants.length} participantes</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
                <CardFooter className="p-4 pt-0">
                  <Button variant="outline" size="sm" className="w-full h-7 text-xs gap-1">
                    <Calendar className="w-3 h-3" />
                    Ver Calendário Completo
                  </Button>
                </CardFooter>
              </Card>

              {/* Tarefas */}
              <Card className="border-neutral-200 dark:border-neutral-700 bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm">
                <CardHeader className="p-4">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-base font-semibold flex items-center gap-2">
                      <CheckSquare className="w-4 h-4" />
                      Tarefas Pendentes
                    </CardTitle>
                    <Button variant="outline" size="sm" className="h-7 text-xs gap-1">
                      <ArrowRight className="w-3 h-3" />
                      Ver Todas
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="p-4 pt-0">
                  <div className="space-y-3">
                    {proximasTarefas.map((tarefa, i) => (
                      <div key={i} className="p-3 rounded-lg bg-neutral-50 dark:bg-neutral-800/50">
                        <div className="flex items-start justify-between gap-2">
                          <div className="flex-1 min-w-0">
                            <div className="flex flex-wrap gap-1 mb-2">
                              <h4 className="text-sm font-medium truncate">{tarefa.tipo}</h4>
                              <Badge variant="outline" className="text-[10px] px-1 h-4">
                                {tarefa.precatorio}
                              </Badge>
                            </div>
                            <p className="text-[10px] text-neutral-600 dark:text-neutral-400 line-clamp-2">
                              {tarefa.descricao}
                            </p>
                            <div className="flex items-center gap-2 mt-2 text-[10px] text-neutral-600 dark:text-neutral-400">
                              <div className="flex items-center gap-1">
                                <Clock className="w-3 h-3" />
                                {new Date(tarefa.data).toLocaleDateString('pt-BR')} às {tarefa.hora}
                              </div>
                              <span>•</span>
                              <div className="flex items-center gap-1">
                                <User className="w-3 h-3" />
                                {tarefa.responsavel.nome}
                              </div>
                            </div>
                          </div>
                          <Badge variant={
                            tarefa.status === "pendente" ? "destructive" :
                            tarefa.status === "em_andamento" ? "secondary" :
                            "default"
                          } className="text-[10px]">
                            {tarefa.status === "pendente" ? "Pendente" : "Em Andamento"}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
                <CardFooter className="p-4 pt-0 flex justify-between">
                  <Button variant="outline" size="sm" className="h-7 text-xs gap-1">
                    <CheckCircle2 className="w-3 h-3" />
                    Ver Todas
                  </Button>
                  <div className="flex items-center gap-1 text-[10px] text-neutral-600 dark:text-neutral-400">
                    <Users2 className="w-3 h-3" />
                    <span>3 pessoas atribuídas</span>
                  </div>
                </CardFooter>
              </Card>
            </div>
          </div>
        </motion.div>
      </div>
      </div>
    </div>
  );
};

export default Home;