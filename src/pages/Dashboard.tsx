import React from 'react';
import { PersonalizedDashboard } from '@/components/dashboard/PersonalizedDashboard';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { TopNav } from '@/components/top-nav';

function Dashboard() {
  return (
    <ProtectedRoute requiredPermissions={[{ resourceType: 'dashboard', action: 'view' }]}>
      <div className="min-h-screen bg-background">
        <TopNav title="Dashboard" />
        <main className="container mx-auto px-4 py-8 pt-20">
          <PersonalizedDashboard />
        </main>
      </div>
    </ProtectedRoute>
  );
}

export default Dashboard;
