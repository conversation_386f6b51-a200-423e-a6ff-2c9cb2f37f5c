
import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { usePermissions } from '@/hooks/usePermissions';
import { 
  getPrecatorios, 
  getPrecatorioById, 
  createPrecatorio, 
  updatePrecatorio, 
  deletePrecatorio,
  getEstatisticasPrecatorios,
  exportarPrecatorios,
  PrecatorioFilter,
  Precatorio,
  TipoPrecatorio,
  StatusPrecatorio
} from '@/services/precatoriosService';
import { getClientes } from '@/services/clientesService';
import { formatCurrency, formatDate, formatStatus, formatDocument } from '@/utils/formatters';
import { DataVisibilityGuard } from '@/components/permissions/DataVisibilityGuard';
import { PermissionGuard } from '@/components/permissions/PermissionGuard';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Skeleton } from '@/components/ui/skeleton';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Sheet, SheetContent, SheetDescription, SheetFooter, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Calendar } from '@/components/ui/calendar';
import { toast } from 'sonner';
import { 
  Search, 
  Filter, 
  Plus, 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Eye, 
  Download, 
  LayoutGrid, 
  LayoutList, 
  RefreshCw, 
  AlertTriangle, 
  CheckCircle2, 
  FileText, 
  CalendarIcon, 
  Users, 
  DollarSign, 
  ArrowUpDown,
  ChevronDown,
  X,
  Clock,
  CalendarDays,
  BarChart4
} from 'lucide-react';
import { FormProvider, useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { TopNav } from '@/components/top-nav';

// Esquema de validação para o formulário de precatório
const precatorioSchema = z.object({
  numero: z.string().min(1, 'Número é obrigatório'),
  cliente_id: z.string().min(1, 'Cliente é obrigatório'),
  tipo: z.string().min(1, 'Tipo é obrigatório'),
  valor: z.string().min(1, 'Valor é obrigatório'),
  data_entrada: z.date().optional(),
  data_vencimento: z.date().optional(),
  status: z.string().min(1, 'Status é obrigatório'),
  responsavel_id: z.string().optional(),
  descricao: z.string().optional(),
  processo_judicial: z.string().optional(),
  vara: z.string().optional(),
  comarca: z.string().optional(),
  tribunal: z.string().optional(),
  observacoes: z.string().optional(),
  prioridade: z.boolean().optional(),
});

type PrecatorioFormValues = z.infer<typeof precatorioSchema>;

// Componente de filtros avançados
const FiltrosAvancados = ({ 
  filtros, 
  setFiltros, 
  isLoading, 
  clientes, 
  responsaveis 
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [dataInicioTemp, setDataInicioTemp] = useState<Date | undefined>(
    filtros.dataInicio ? new Date(filtros.dataInicio) : undefined
  );
  const [dataFimTemp, setDataFimTemp] = useState<Date | undefined>(
    filtros.dataFim ? new Date(filtros.dataFim) : undefined
  );

  const handleReset = () => {
    setFiltros({
      termo: '',
      status: [],
      tipo: [],
      cliente_id: '',
      responsavel_id: '',
      dataInicio: undefined,
      dataFim: undefined,
      valorMinimo: undefined,
      valorMaximo: undefined,
      prioridade: undefined
    });
    setDataInicioTemp(undefined);
    setDataFimTemp(undefined);
    toast.success('Filtros redefinidos');
  };

  const handleApplyDateRange = () => {
    setFiltros({
      ...filtros,
      dataInicio: dataInicioTemp?.toISOString(),
      dataFim: dataFimTemp?.toISOString()
    });
  };

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        <Button variant="outline" size="sm" className="gap-2">
          <Filter size={16} />
          Filtros Avançados
          {Object.keys(filtros).filter(k => 
            filtros[k] !== undefined && 
            filtros[k] !== '' && 
            (Array.isArray(filtros[k]) ? filtros[k].length > 0 : true)
          ).length > 1 && (
            <Badge variant="secondary" className="ml-1">
              {Object.keys(filtros).filter(k => 
                filtros[k] !== undefined && 
                filtros[k] !== '' && 
                (Array.isArray(filtros[k]) ? filtros[k].length > 0 : true)
              ).length - 1}
            </Badge>
          )}
        </Button>
      </SheetTrigger>
      <SheetContent className="sm:max-w-md md:max-w-lg">
        <SheetHeader>
          <SheetTitle>Filtros Avançados</SheetTitle>
          <SheetDescription>
            Refine sua busca de precatórios com filtros avançados
          </SheetDescription>
        </SheetHeader>
        <div className="grid gap-4 py-4 overflow-y-auto max-h-[calc(100vh-200px)]">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select
                value={filtros.status?.length === 1 ? filtros.status[0] : ''}
                onValueChange={(value) => {
                  if (value === '') {
                    setFiltros({ ...filtros, status: [] });
                  } else {
                    setFiltros({ ...filtros, status: [value] });
                  }
                }}
              >
                <SelectTrigger id="status">
                  <SelectValue placeholder="Todos os status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Todos os status</SelectItem>
                  <SelectItem value="novo">Novo</SelectItem>
                  <SelectItem value="em_analise">Em Análise</SelectItem>
                  <SelectItem value="em_processamento">Em Processamento</SelectItem>
                  <SelectItem value="aguardando_cliente">Aguardando Cliente</SelectItem>
                  <SelectItem value="concluido">Concluído</SelectItem>
                  <SelectItem value="cancelado">Cancelado</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="tipo">Tipo</Label>
              <Select
                value={filtros.tipo?.length === 1 ? filtros.tipo[0] : ''}
                onValueChange={(value) => {
                  if (value === '') {
                    setFiltros({ ...filtros, tipo: [] });
                  } else {
                    setFiltros({ ...filtros, tipo: [value] });
                  }
                }}
              >
                <SelectTrigger id="tipo">
                  <SelectValue placeholder="Todos os tipos" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Todos os tipos</SelectItem>
                  <SelectItem value="federal">Federal</SelectItem>
                  <SelectItem value="estadual">Estadual</SelectItem>
                  <SelectItem value="municipal">Municipal</SelectItem>
                  <SelectItem value="rpv">RPV</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="cliente">Cliente</Label>
            <Select
              value={filtros.cliente_id || ''}
              onValueChange={(value) => setFiltros({ ...filtros, cliente_id: value })}
              disabled={isLoading}
            >
              <SelectTrigger id="cliente">
                <SelectValue placeholder="Todos os clientes" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Todos os clientes</SelectItem>
                {clientes?.map(cliente => (
                  <SelectItem key={cliente.id} value={cliente.id}>
                    {cliente.nome}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="responsavel">Responsável</Label>
            <Select
              value={filtros.responsavel_id || ''}
              onValueChange={(value) => setFiltros({ ...filtros, responsavel_id: value })}
              disabled={isLoading}
            >
              <SelectTrigger id="responsavel">
                <SelectValue placeholder="Todos os responsáveis" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Todos os responsáveis</SelectItem>
                <SelectItem value="sem_responsavel">Sem responsável</SelectItem>
                <SelectItem value="meus">Meus precatórios</SelectItem>
                {responsaveis?.map(resp => (
                  <SelectItem key={resp.id} value={resp.id}>
                    {resp.nome}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Período de Entrada</Label>
            <div className="grid grid-cols-2 gap-2">
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="justify-start text-left font-normal"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {dataInicioTemp ? format(dataInicioTemp, 'dd/MM/yyyy') : 'Data Inicial'}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={dataInicioTemp}
                    onSelect={setDataInicioTemp}
                    initialFocus
                    locale={ptBR}
                  />
                </PopoverContent>
              </Popover>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="justify-start text-left font-normal"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {dataFimTemp ? format(dataFimTemp, 'dd/MM/yyyy') : 'Data Final'}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={dataFimTemp}
                    onSelect={setDataFimTemp}
                    initialFocus
                    locale={ptBR}
                  />
                </PopoverContent>
              </Popover>
            </div>
            <Button 
              variant="secondary" 
              size="sm" 
              className="mt-1"
              onClick={handleApplyDateRange}
              disabled={!dataInicioTemp && !dataFimTemp}
            >
              Aplicar Período
            </Button>
          </div>

          <DataVisibilityGuard requiredPermission="visualizar_relatorios">
            <div className="space-y-2">
              <Label>Valor do Precatório</Label>
              <div className="grid grid-cols-2 gap-2">
                <div className="space-y-1">
                  <Label htmlFor="valorMinimo" className="text-xs">Mínimo</Label>
                  <Input
                    id="valorMinimo"
                    type="number"
                    placeholder="Valor mínimo"
                    value={filtros.valorMinimo || ''}
                    onChange={(e) => setFiltros({ 
                      ...filtros, 
                      valorMinimo: e.target.value ? parseFloat(e.target.value) : undefined 
                    })}
                  />
                </div>
                <div className="space-y-1">
                  <Label htmlFor="valorMaximo" className="text-xs">Máximo</Label>
                  <Input
                    id="valorMaximo"
                    type="number"
                    placeholder="Valor máximo"
                    value={filtros.valorMaximo || ''}
                    onChange={(e) => setFiltros({ 
                      ...filtros, 
                      valorMaximo: e.target.value ? parseFloat(e.target.value) : undefined 
                    })}
                  />
                </div>
              </div>
            </div>
          </DataVisibilityGuard>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="prioridade"
              checked={filtros.prioridade === true}
              onCheckedChange={(checked) => setFiltros({ 
                ...filtros, 
                prioridade: checked === true ? true : undefined 
              })}
            />
            <Label htmlFor="prioridade">Apenas precatórios prioritários</Label>
          </div>
        </div>
        <SheetFooter>
          <Button variant="outline" onClick={handleReset} disabled={isLoading}>
            Limpar Filtros
          </Button>
          <Button onClick={() => setIsOpen(false)} disabled={isLoading}>
            Aplicar Filtros
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
};

// Componente de estatísticas
const EstatisticasPrecatorios = ({ estatisticas, isLoading }) => {
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardHeader className="pb-2">
              <Skeleton className="h-4 w-24" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-16" />
              <Skeleton className="h-4 w-32 mt-2" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (!estatisticas) {
    return null;
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">
            Total de Precatórios
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {estatisticas.total || 0}
          </div>
          <div className="text-xs text-muted-foreground mt-1 flex items-center">
            <FileText size={12} className="mr-1" />
            <span>{estatisticas.novos || 0} novos nos últimos 30 dias</span>
          </div>
        </CardContent>
      </Card>

      <DataVisibilityGuard requiredPermission="visualizar_relatorios">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Valor Total
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(estatisticas.valorTotal || 0)}
            </div>
            <div className="text-xs text-muted-foreground mt-1 flex items-center">
              <DollarSign size={12} className="mr-1" />
              <span>Média: {formatCurrency(estatisticas.valorMedio || 0)}</span>
            </div>
          </CardContent>
        </Card>
      </DataVisibilityGuard>

      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">
            Em Processamento
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {estatisticas.emProcessamento || 0}
          </div>
          <div className="text-xs text-muted-foreground mt-1 flex items-center">
            <Clock size={12} className="mr-1" />
            <span>Tempo médio: {estatisticas.tempoMedio || 0} dias</span>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">
            Concluídos
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {estatisticas.concluidos || 0}
          </div>
          <div className="text-xs text-muted-foreground mt-1 flex items-center">
            <CheckCircle2 size={12} className="mr-1" />
            <span>Taxa de sucesso: {estatisticas.taxaSucesso || 0}%</span>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Componente de visualização em tabela
const TabelaPrecatorios = ({ 
  precatorios, 
  isLoading, 
  onView, 
  onEdit, 
  onDelete, 
  selecionados, 
  setSelecionados,
  sortConfig,
  setSortConfig,
  canEdit,
  canDelete
}) => {
  // Função para alternar ordenação
  const handleSort = (key) => {
    let direction = 'asc';
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  // Função para alternar seleção de item
  const toggleSelect = (id) => {
    if (selecionados.includes(id)) {
      setSelecionados(selecionados.filter(item => item !== id));
    } else {
      setSelecionados([...selecionados, id]);
    }
  };

  // Função para alternar seleção de todos os itens
  const toggleSelectAll = () => {
    if (selecionados.length === precatorios.length) {
      setSelecionados([]);
    } else {
      setSelecionados(precatorios.map(p => p.id));
    }
  };

  if (isLoading) {
    return (
      <div>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12"></TableHead>
                <TableHead>Número</TableHead>
                <TableHead>Cliente</TableHead>
                <TableHead>Tipo</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Valor</TableHead>
                <TableHead>Data</TableHead>
                <TableHead className="w-[100px]">Ações</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {[...Array(5)].map((_, i) => (
                <TableRow key={i}>
                  <TableCell><Skeleton className="h-4 w-4" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-32" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                  <TableCell><Skeleton className="h-8 w-20" /></TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    );
  }

  if (!precatorios || precatorios.length === 0) {
    return (
      <div className="rounded-md border p-8 text-center">
        <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-muted">
          <FileText className="h-6 w-6 text-muted-foreground" />
        </div>
        <h3 className="mt-4 text-lg font-semibold">Nenhum precatório encontrado</h3>
        <p className="mt-2 text-sm text-muted-foreground">
          Não encontramos precatórios com os critérios de busca atuais.
        </p>
      </div>
    );
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-12">
              <Checkbox 
                checked={precatorios.length > 0 && selecionados.length === precatorios.length}
                onCheckedChange={toggleSelectAll}
              />
            </TableHead>
            <TableHead>
              <div className="flex items-center gap-1 cursor-pointer" onClick={() => handleSort('numero')}>
                Número
                {sortConfig.key === 'numero' && (
                  <ArrowUpDown size={14} className={sortConfig.direction === 'desc' ? 'transform rotate-180' : ''} />
                )}
              </div>
            </TableHead>
            <TableHead>
              <div className="flex items-center gap-1 cursor-pointer" onClick={() => handleSort('cliente')}>
                Cliente
                {sortConfig.key === 'cliente' && (
                  <ArrowUpDown size={14} className={sortConfig.direction === 'desc' ? 'transform rotate-180' : ''} />
                )}
              </div>
            </TableHead>
            <TableHead>
              <div className="flex items-center gap-1 cursor-pointer" onClick={() => handleSort('tipo')}>
                Tipo
                {sortConfig.key === 'tipo' && (
                  <ArrowUpDown size={14} className={sortConfig.direction === 'desc' ? 'transform rotate-180' : ''} />
                )}
              </div>
            </TableHead>
            <TableHead>
              <div className="flex items-center gap-1 cursor-pointer" onClick={() => handleSort('status')}>
                Status
                {sortConfig.key === 'status' && (
                  <ArrowUpDown size={14} className={sortConfig.direction === 'desc' ? 'transform rotate-180' : ''} />
                )}
              </div>
            </TableHead>
            <TableHead>
              <div className="flex items-center gap-1 cursor-pointer" onClick={() => handleSort('valor')}>
                Valor
                {sortConfig.key === 'valor' && (
                  <ArrowUpDown size={14} className={sortConfig.direction === 'desc' ? 'transform rotate-180' : ''} />
                )}
              </div>
            </TableHead>
            <TableHead>
              <div className="flex items-center gap-1 cursor-pointer" onClick={() => handleSort('data_entrada')}>
                Data
                {sortConfig.key === 'data_entrada' && (
                  <ArrowUpDown size={14} className={sortConfig.direction === 'desc' ? 'transform rotate-180' : ''} />
                )}
              </div>
            </TableHead>
            <TableHead className="w-[100px]">Ações</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {precatorios.map((precatorio) => (
            <TableRow key={precatorio.id} className={precatorio.prioridade ? 'bg-amber-50 dark:bg-amber-950/20' : ''}>
              <TableCell>
                <Checkbox 
                  checked={selecionados.includes(precatorio.id)}
                  onCheckedChange={() => toggleSelect(precatorio.id)}
                />
              </TableCell>
              <TableCell className="font-medium">
                {precatorio.numero}
                {precatorio.prioridade && (
                  <Badge variant="outline" className="ml-2 bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300">
                    Prioritário
                  </Badge>
                )}
              </TableCell>
              <TableCell>
                {precatorio.cliente?.nome || 'Cliente não encontrado'}
              </TableCell>
              <TableCell>
                <Badge variant="outline">
                  {formatStatus(precatorio.tipo)}
                </Badge>
              </TableCell>
              <TableCell>
                <Badge 
                  variant={
                    precatorio.status === 'concluido' ? 'success' : 
                    precatorio.status === 'cancelado' ? 'destructive' : 
                    precatorio.status === 'em_processamento' ? 'default' : 
                    precatorio.status === 'em_analise' ? 'secondary' : 
                    precatorio.status === 'aguardando_cliente' ? 'warning' : 
                    'outline'
                  }
                >
                  {formatStatus(precatorio.status)}
                </Badge>
              </TableCell>
              <TableCell>
                {formatCurrency(precatorio.valor)}
              </TableCell>
              <TableCell>
                {formatDate(precatorio.data_entrada)}
              </TableCell>
              <TableCell>
                <div className="flex items-center gap-2">
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    onClick={() => onView(precatorio.id)}
                    title="Ver detalhes"
                  >
                    <Eye size={16} />
                  </Button>
                  {canEdit && (
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      onClick={() => onEdit(precatorio.id)}
                      title="Editar"
                    >
                      <Edit size={16} />
                    </Button>
                  )}
                  {canDelete && (
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      onClick={() => onDelete(precatorio.id)}
                      title="Excluir"
                    >
                      <Trash2 size={16} />
                    </Button>
                  )}
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

// Componente de visualização em cards
const CardsPrecatorios = ({ 
  precatorios, 
  isLoading, 
  onView, 
  onEdit, 
  onDelete, 
  selecionados, 
  setSelecionados,
  canEdit,
  canDelete
}) => {
  // Função para alternar seleção de item
  const toggleSelect = (id) => {
    if (selecionados.includes(id)) {
      setSelecionados(selecionados.filter(item => item !== id));
    } else {
      setSelecionados([...selecionados, id]);
    }
  };

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {[...Array(6)].map((_, i) => (
          <Card key={i}>
            <CardHeader className="pb-2">
              <div className="flex justify-between items-start">
                <div>
                  <Skeleton className="h-5 w-32 mb-1" />
                  <Skeleton className="h-4 w-24" />
                </div>
                <Skeleton className="h-8 w-8 rounded-full" />
              </div>
            </CardHeader>
            <CardContent className="pb-2">
              <div className="space-y-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
                <div className="pt-2">
                  <Skeleton className="h-6 w-24" />
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <div className="flex justify-between items-center w-full">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-8 w-24" />
              </div>
            </CardFooter>
          </Card>
        ))}
      </div>
    );
  }

  if (!precatorios || precatorios.length === 0) {
    return (
      <div className="rounded-md border p-8 text-center">
        <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-muted">
          <FileText className="h-6 w-6 text-muted-foreground" />
        </div>
        <h3 className="mt-4 text-lg font-semibold">Nenhum precatório encontrado</h3>
        <p className="mt-2 text-sm text-muted-foreground">
          Não encontramos precatórios com os critérios de busca atuais.
        </p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {precatorios.map((precatorio) => (
        <Card 
          key={precatorio.id} 
          className={precatorio.prioridade ? 'bg-amber-50 dark:bg-amber-950/20 border-amber-200 dark:border-amber-800' : ''}
        >
          <CardHeader className="pb-2">
            <div className="flex justify-between items-start">
              <div>
                <CardTitle className="text-base flex items-center gap-2">
                  {precatorio.numero}
                  {precatorio.prioridade && (
                    <Badge variant="outline" className="bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300">
                      Prioritário
                    </Badge>
                  )}
                </CardTitle>
                <CardDescription>
                  {precatorio.cliente?.nome || 'Cliente não encontrado'}
                </CardDescription>
              </div>
              <Checkbox 
                checked={selecionados.includes(precatorio.id)}
                onCheckedChange={() => toggleSelect(precatorio.id)}
              />
            </div>
          </CardHeader>
          <CardContent className="pb-2">
            <div className="grid grid-cols-2 gap-2 text-sm mb-2">
              <div>
                <span className="text-muted-foreground">Tipo:</span>{' '}
                <Badge variant="outline">
                  {formatStatus(precatorio.tipo)}
                </Badge>
              </div>
              <div>
                <span className="text-muted-foreground">Status:</span>{' '}
                <Badge 
                  variant={
                    precatorio.status === 'concluido' ? 'success' : 
                    precatorio.status === 'cancelado' ? 'destructive' : 
                    precatorio.status === 'em_processamento' ? 'default' : 
                    precatorio.status === 'em_analise' ? 'secondary' : 
                    precatorio.status === 'aguardando_cliente' ? 'warning' : 
                    'outline'
                  }
                >
                  {formatStatus(precatorio.status)}
                </Badge>
              </div>
              <div>
                <span className="text-muted-foreground">Data:</span>{' '}
                <span>{formatDate(precatorio.data_entrada)}</span>
              </div>
              <div>
                <span className="text-muted-foreground">Responsável:</span>{' '}
                <span>{precatorio.responsavel?.nome || 'Não atribuído'}</span>
              </div>
            </div>
            <div className="pt-1">
              <span className="text-muted-foreground text-sm">Valor:</span>{' '}
              <span className="font-semibold">{formatCurrency(precatorio.valor)}</span>
            </div>
          </CardContent>
          <CardFooter>
            <div className="flex justify-between items-center w-full">
              <Button 
                variant="link" 
                size="sm" 
                className="px-0" 
                onClick={() => onView(precatorio.id)}
              >
                Ver detalhes
              </Button>
              <div className="flex items-center gap-1">
                {canEdit && (
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    onClick={() => onEdit(precatorio.id)}
                    title="Editar"
                  >
                    <Edit size={16} />
                  </Button>
                )}
                {canDelete && (
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    onClick={() => onDelete(precatorio.id)}
                    title="Excluir"
                  >
                    <Trash2 size={16} />
                  </Button>
                )}
              </div>
            </div>
          </CardFooter>
        </Card>
      ))}
    </div>
  );
};

// Componente de modal de detalhes
const DetalhesModal = ({ 
  isOpen, 
  setIsOpen, 
  precatorioId, 
  isLoading,
  onEdit,
  canEdit
}) => {
  const [precatorio, setPrecatorio] = useState(null);
  const [loadingDetalhes, setLoadingDetalhes] = useState(false);
  const [error, setError] = useState(null);

  // Carregar detalhes do precatório
  useEffect(() => {
    const carregarDetalhes = async () => {
      if (!precatorioId || !isOpen) return;
      
      try {
        setLoadingDetalhes(true);
        setError(null);
        
        const resultado = await getPrecatorioById(precatorioId);
        setPrecatorio(resultado);
      } catch (err) {
        console.error('Erro ao carregar detalhes do precatório:', err);
        setError('Não foi possível carregar os detalhes do precatório. Tente novamente mais tarde.');
      } finally {
        setLoadingDetalhes(false);
      }
    };

    carregarDetalhes();
  }, [precatorioId, isOpen]);

  // Renderizar conteúdo com base no estado
  const renderContent = () => {
    if (loadingDetalhes) {
      return (
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Skeleton className="h-5 w-1/3" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
          </div>
          <Separator />
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-5 w-24" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-5 w-24" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-5 w-24" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-5 w-24" />
            </div>
          </div>
        </div>
      );
    }

    if (error) {
      return (
        <div className="py-4">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Erro</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </div>
      );
    }

    if (!precatorio) {
      return (
        <div className="py-4 text-center text-muted-foreground">
          Nenhum dado disponível
        </div>
      );
    }

    return (
      <div className="space-y-4 py-4">
        <div>
          <h3 className="text-lg font-semibold">{precatorio.numero}</h3>
          <p className="text-muted-foreground">{precatorio.cliente?.nome}</p>
          {precatorio.processo_judicial && (
            <p className="text-sm mt-1">
              <span className="text-muted-foreground">Processo:</span> {precatorio.processo_judicial}
            </p>
          )}
        </div>

        <Separator />

        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-sm text-muted-foreground">Tipo</p>
            <p className="font-medium">{formatStatus(precatorio.tipo)}</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Status</p>
            <p>
              <Badge 
                variant={
                  precatorio.status === 'concluido' ? 'success' : 
                  precatorio.status === 'cancelado' ? 'destructive' : 
                  precatorio.status === 'em_processamento' ? 'default' : 
                  precatorio.status === 'em_analise' ? 'secondary' : 
                  precatorio.status === 'aguardando_cliente' ? 'warning' : 
                  'outline'
                }
              >
                {formatStatus(precatorio.status)}
              </Badge>
            </p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Data de Entrada</p>
            <p className="font-medium">{formatDate(precatorio.data_entrada)}</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Data de Vencimento</p>
            <p className="font-medium">
              {precatorio.data_vencimento ? formatDate(precatorio.data_vencimento) : 'Não definida'}
            </p>
          </div>
          <DataVisibilityGuard requiredPermission="visualizar_relatorios">
            <div>
              <p className="text-sm text-muted-foreground">Valor</p>
              <p className="font-medium">{formatCurrency(precatorio.valor)}</p>
            </div>
          </DataVisibilityGuard>
          <div>
            <p className="text-sm text-muted-foreground">Responsável</p>
            <p className="font-medium">{precatorio.responsavel?.nome || 'Não atribuído'}</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Prioridade</p>
            <p className="font-medium">{precatorio.prioridade ? 'Sim' : 'Não'}</p>
          </div>
        </div>

        {(precatorio.vara || precatorio.comarca || precatorio.tribunal) && (
          <>
            <Separator />
            <div>
              <h4 className="text-sm font-medium mb-2">Informações Judiciais</h4>
              <div className="grid grid-cols-3 gap-4">
                {precatorio.vara && (
                  <div>
                    <p className="text-sm text-muted-foreground">Vara</p>
                    <p className="font-medium">{precatorio.vara}</p>
                  </div>
                )}
                {precatorio.comarca && (
                  <div>
                    <p className="text-sm text-muted-foreground">Comarca</p>
                    <p className="font-medium">{precatorio.comarca}</p>
                  </div>
                )}
                {precatorio.tribunal && (
                  <div>
                    <p className="text-sm text-muted-foreground">Tribunal</p>
                    <p className="font-medium">{precatorio.tribunal}</p>
                  </div>
                )}
              </div>
            </div>
          </>
        )}

        {precatorio.descricao && (
          <>
            <Separator />
            <div>
              <h4 className="text-sm font-medium mb-2">Descrição</h4>
              <p className="text-sm">{precatorio.descricao}</p>
            </div>
          </>
        )}

        {precatorio.observacoes && (
          <>
            <Separator />
            <div>
              <h4 className="text-sm font-medium mb-2">Observações</h4>
              <p className="text-sm">{precatorio.observacoes}</p>
            </div>
          </>
        )}
      </div>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle>Detalhes do Precatório</DialogTitle>
          <DialogDescription>
            Informações completas do precatório selecionado.
          </DialogDescription>
        </DialogHeader>
        
        {renderContent()}
        
        <DialogFooter>
          {canEdit && precatorio && (
            <Button onClick={() => {
              setIsOpen(false);
              onEdit(precatorioId);
            }}>
              Editar
            </Button>
          )}
          <Button variant="outline" onClick={() => setIsOpen(false)}>
            Fechar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

// Componente de formulário de precatório
const PrecatorioForm = ({ 
  isOpen, 
  setIsOpen, 
  precatorioId, 
  onSuccess,
  clientes,
  responsaveis,
  isLoading
}) => {
  const [loadingForm, setLoadingForm] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState(null);
  
  // Configurar form
  const methods = useForm<PrecatorioFormValues>({
    resolver: zodResolver(precatorioSchema),
    defaultValues: {
      numero: '',
      cliente_id: '',
      tipo: '',
      valor: '',
      status: 'novo',
      prioridade: false
    }
  });

  // Carregar dados do precatório para edição
  useEffect(() => {
    const carregarPrecatorio = async () => {
      if (!precatorioId || !isOpen) return;
      
      try {
        setLoadingForm(true);
        setError(null);
        
        const precatorio = await getPrecatorioById(precatorioId);
        
        if (precatorio) {
          // Converter datas para objetos Date
          const dataEntrada = precatorio.data_entrada ? new Date(precatorio.data_entrada) : undefined;
          const dataVencimento = precatorio.data_vencimento ? new Date(precatorio.data_vencimento) : undefined;
          
          // Preencher formulário
          methods.reset({
            numero: precatorio.numero,
            cliente_id: precatorio.cliente_id,
            tipo: precatorio.tipo,
            valor: String(precatorio.valor),
            data_entrada: dataEntrada,
            data_vencimento: dataVencimento,
            status: precatorio.status,
            responsavel_id: precatorio.responsavel_id,
            descricao: precatorio.descricao,
            processo_judicial: precatorio.processo_judicial,
            vara: precatorio.vara,
            comarca: precatorio.comarca,
            tribunal: precatorio.tribunal,
            observacoes: precatorio.observacoes,
            prioridade: precatorio.prioridade
          });
        }
      } catch (err) {
        console.error('Erro ao carregar precatório:', err);
        setError('Não foi possível carregar os dados do precatório. Tente novamente mais tarde.');
      } finally {
        setLoadingForm(false);
      }
    };

    if (isOpen) {
      // Resetar formulário para valores padrão ao abrir
      methods.reset({
        numero: '',
        cliente_id: '',
        tipo: '',
        valor: '',
        status: 'novo',
        prioridade: false
      });
      
      // Carregar dados se for edição
      if (precatorioId) {
        carregarPrecatorio();
      }
    }
  }, [precatorioId, isOpen, methods]);

  // Função para salvar precatório
  const onSubmit = async (data: PrecatorioFormValues) => {
    try {
      setSubmitting(true);
      setError(null);
      
      // Converter valor para número
      const valorNumerico = parseFloat(data.valor.replace(/[^\d.,]/g, '').replace(',', '.'));
      
      const precatorioData = {
        ...data,
        valor: valorNumerico
      };
      
      if (precatorioId) {
        // Atualizar precatório existente
        await updatePrecatorio(precatorioId, precatorioData);
        toast.success('Precatório atualizado com sucesso!');
      } else {
        // Criar novo precatório
        await createPrecatorio(precatorioData);
        toast.success('Precatório criado com sucesso!');
      }
      
      // Fechar modal e notificar sucesso
      setIsOpen(false);
      onSuccess();
    } catch (err) {
      console.error('Erro ao salvar precatório:', err);
      setError('Ocorreu um erro ao salvar o precatório. Verifique os dados e tente novamente.');
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !submitting && setIsOpen(open)}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle>{precatorioId ? 'Editar Precatório' : 'Novo Precatório'}</DialogTitle>
          <DialogDescription>
            {precatorioId ? 'Atualize os dados do precatório.' : 'Preencha os dados para criar um novo precatório.'}
          </DialogDescription>
        </DialogHeader>
        
        {loadingForm ? (
          <div className="space-y-4 py-4">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <div className="grid grid-cols-2 gap-4">
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
            </div>
            <Skeleton className="h-10 w-full" />
          </div>
        ) : error ? (
          <div className="py-4">
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Erro</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          </div>
        ) : (
          <FormProvider {...methods}>
            <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="numero">Número do Precatório *</Label>
                <Input
                  id="numero"
                  {...methods.register('numero')}
                  placeholder="Ex: PRE-12345/2023"
                />
                {methods.formState.errors.numero && (
                  <p className="text-sm text-destructive">{methods.formState.errors.numero.message}</p>
                )}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="cliente_id">Cliente *</Label>
                <Controller
                  name="cliente_id"
                  control={methods.control}
                  render={({ field }) => (
                    <Select
                      value={field.value}
                      onValueChange={field.onChange}
                      disabled={isLoading}
                    >
                      <SelectTrigger id="cliente_id">
                        <SelectValue placeholder="Selecione um cliente" />
                      </SelectTrigger>
                      <SelectContent>
                        {clientes?.map(cliente => (
                          <SelectItem key={cliente.id} value={cliente.id}>
                            {cliente.nome}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
                {methods.formState.errors.cliente_id && (
                  <p className="text-sm text-destructive">{methods.formState.errors.cliente_id.message}</p>
                )}
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="tipo">Tipo *</Label>
                  <Controller
                    name="tipo"
                    control={methods.control}
                    render={({ field }) => (
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                      >
                        <SelectTrigger id="tipo">
                          <SelectValue placeholder="Selecione o tipo" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="federal">Federal</SelectItem>
                          <SelectItem value="estadual">Estadual</SelectItem>
                          <SelectItem value="municipal">Municipal</SelectItem>
                          <SelectItem value="rpv">RPV</SelectItem>
                        </SelectContent>
                      </Select>
                    )}
                  />
                  {methods.formState.errors.tipo && (
                    <p className="text-sm text-destructive">{methods.formState.errors.tipo.message}</p>
                  )}
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="valor">Valor *</Label>
                  <Input
                    id="valor"
                    {...methods.register('valor')}
                    placeholder="Ex: 10000,00"
                  />
                  {methods.formState.errors.valor && (
                    <p className="text-sm text-destructive">{methods.formState.errors.valor.message}</p>
                  )}
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="data_entrada">Data de Entrada</Label>
                  <Controller
                    name="data_entrada"
                    control={methods.control}
                    render={({ field }) => (
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className="w-full justify-start text-left font-normal"
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {field.value ? format(field.value, 'dd/MM/yyyy') : 'Selecione a data'}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            initialFocus
                            locale={ptBR}
                          />
                        </PopoverContent>
                      </Popover>
                    )}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="data_vencimento">Data de Vencimento</Label>
                  <Controller
                    name="data_vencimento"
                    control={methods.control}
                    render={({ field }) => (
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className="w-full justify-start text-left font-normal"
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {field.value ? format(field.value, 'dd/MM/yyyy') : 'Selecione a data'}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            initialFocus
                            locale={ptBR}
                          />
                        </PopoverContent>
                      </Popover>
                    )}
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="status">Status *</Label>
                  <Controller
                    name="status"
                    control={methods.control}
                    render={({ field }) => (
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                      >
                        <SelectTrigger id="status">
                          <SelectValue placeholder="Selecione o status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="novo">Novo</SelectItem>
                          <SelectItem value="em_analise">Em Análise</SelectItem>
                          <SelectItem value="em_processamento">Em Processamento</SelectItem>
                          <SelectItem value="aguardando_cliente">Aguardando Cliente</SelectItem>
                          <SelectItem value="concluido">Concluído</SelectItem>
                          <SelectItem value="cancelado">Cancelado</SelectItem>
                        </SelectContent>
                      </Select>
                    )}
                  />
                  {methods.formState.errors.status && (
                    <p className="text-sm text-destructive">{methods.formState.errors.status.message}</p>
                  )}
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="responsavel_id">Responsável</Label>
                  <Controller
                    name="responsavel_id"
                    control={methods.control}
                    render={({ field }) => (
                      <Select
                        value={field.value || ''}
                        onValueChange={field.onChange}
                        disabled={isLoading}
                      >
                        <SelectTrigger id="responsavel_id">
                          <SelectValue placeholder="Selecione um responsável" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="">Sem responsável</SelectItem>
                          {responsaveis?.map(resp => (
                            <SelectItem key={resp.id} value={resp.id}>
                              {resp.nome}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    )}
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="processo_judicial">Número do Processo Judicial</Label>
                <Input
                  id="processo_judicial"
                  {...methods.register('processo_judicial')}
                  placeholder="Ex: 1234567-12.2023.8.26.0100"
                />
              </div>
              
              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="vara">Vara</Label>
                  <Input
                    id="vara"
                    {...methods.register('vara')}
                    placeholder="Ex: 1ª Vara"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="comarca">Comarca</Label>
                  <Input
                    id="comarca"
                    {...methods.register('comarca')}
                    placeholder="Ex: São Paulo"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="tribunal">Tribunal</Label>
                  <Input
                    id="tribunal"
                    {...methods.register('tribunal')}
                    placeholder="Ex: TJSP"
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="descricao">Descrição</Label>
                <Input
                  id="descricao"
                  {...methods.register('descricao')}
                  placeholder="Descrição do precatório"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="observacoes">Observações</Label>
                <Input
                  id="observacoes"
                  {...methods.register('observacoes')}
                  placeholder="Observações adicionais"
                />
              </div>
              
              <div className="flex items-center space-x-2">
                <Controller
                  name="prioridade"
                  control={methods.control}
                  render={({ field }) => (
                    <Checkbox
                      id="prioridade"
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  )}
                />
                <Label htmlFor="prioridade">Marcar como prioritário</Label>
              </div>
              
              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setIsOpen(false)} disabled={submitting}>
                  Cancelar
                </Button>
                <Button type="submit" disabled={submitting || loadingForm}>
                  {submitting ? 'Salvando...' : precatorioId ? 'Atualizar' : 'Criar'}
                </Button>
              </DialogFooter>
            </form>
          </FormProvider>
        )}
      </DialogContent>
    </Dialog>
  );
};

// Componente de modal de confirmação de exclusão
const ConfirmDeleteModal = ({ 
  isOpen, 
  setIsOpen, 
  precatorioId, 
  onConfirm, 
  isLoading 
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Confirmar Exclusão</DialogTitle>
          <DialogDescription>
            Tem certeza que deseja excluir este precatório? Esta ação não pode ser desfeita.
          </DialogDescription>
        </DialogHeader>
        <div className="py-4">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Atenção</AlertTitle>
            <AlertDescription>
              A exclusão é permanente e todos os dados relacionados a este precatório serão perdidos.
            </AlertDescription>
          </Alert>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => setIsOpen(false)} disabled={isLoading}>
            Cancelar
          </Button>
          <Button 
            variant="destructive" 
            onClick={() => onConfirm(precatorioId)} 
            disabled={isLoading}
          >
            {isLoading ? 'Excluindo...' : 'Excluir'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

// Componente principal da página de Precatórios
export default function Precatorios() {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { hasPermission } = usePermissions();
  
  // Estados
  const [precatorios, setPrecatorios] = useState<Precatorio[]>([]);
  const [clientes, setClientes] = useState([]);
  const [responsaveis, setResponsaveis] = useState([]);
  const [estatisticas, setEstatisticas] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingEstatisticas, setIsLoadingEstatisticas] = useState(true);
  const [error, setError] = useState(null);
  const [viewMode, setViewMode] = useState<'tabela' | 'cards'>('tabela');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [selecionados, setSelecionados] = useState<string[]>([]);
  const [sortConfig, setSortConfig] = useState({ key: 'data_entrada', direction: 'desc' });
  const [filtros, setFiltros] = useState<PrecatorioFilter>({
    termo: '',
    status: [],
    tipo: [],
    cliente_id: '',
    responsavel_id: '',
    dataInicio: undefined,
    dataFim: undefined,
    valorMinimo: undefined,
    valorMaximo: undefined,
    prioridade: undefined
  });
  
  // Estados para modais
  const [detalhesModalOpen, setDetalhesModalOpen] = useState(false);
  const [precatorioIdDetalhes, setPrecatorioIdDetalhes] = useState(null);
  const [formModalOpen, setFormModalOpen] = useState(false);
  const [precatorioIdForm, setPrecatorioIdForm] = useState(null);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [precatorioIdDelete, setPrecatorioIdDelete] = useState(null);
  const [isDeleting, setIsDeleting] = useState(false);
  
  // Verificar permissões
  const canCreate = hasPermission('criar_precatorios');
  const canEdit = hasPermission('editar_precatorios');
  const canDelete = hasPermission('excluir_precatorios');
  const canExport = hasPermission('visualizar_relatorios');
  
  // Referência para debounce da busca
  const searchTimeout = useRef<NodeJS.Timeout | null>(null);
  
  // Carregar dados iniciais
  useEffect(() => {
    const carregarDados = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        // Carregar precatórios com filtros, paginação e ordenação
        const resultado = await getPrecatorios({
          ...filtros,
          page,
          limit: itemsPerPage,
          sortBy: sortConfig.key,
          sortDirection: sortConfig.direction
        });
        
        setPrecatorios(resultado.data);
        setTotalPages(resultado.totalPages);
        
        // Carregar clientes e responsáveis para os formulários
        const clientesData = await getClientes();
        setClientes(clientesData);
        
        // Aqui você precisaria implementar um serviço para buscar usuários/responsáveis
        // Por enquanto, vamos simular com dados do usuário atual
        if (user) {
          setResponsaveis([
            { id: user.id, nome: user.nome || user.email }
          ]);
        }
      } catch (err) {
        console.error('Erro ao carregar precatórios:', err);
        setError('Não foi possível carregar os precatórios. Tente novamente mais tarde.');
      } finally {
        setIsLoading(false);
      }
    };
    
    carregarDados();
  }, [page, itemsPerPage, sortConfig, filtros, user]);
  
  // Carregar estatísticas
  useEffect(() => {
    const carregarEstatisticas = async () => {
      try {
        setIsLoadingEstatisticas(true);
        const stats = await getEstatisticasPrecatorios();
        setEstatisticas(stats);
      } catch (err) {
        console.error('Erro ao carregar estatísticas:', err);
        // Não mostrar erro para estatísticas, apenas log
      } finally {
        setIsLoadingEstatisticas(false);
      }
    };
    
    carregarEstatisticas();
  }, []);
  
  // Função para busca com debounce
  const handleSearch = (termo: string) => {
    if (searchTimeout.current) {
      clearTimeout(searchTimeout.current);
    }
    
    searchTimeout.current = setTimeout(() => {
      setFiltros(prev => ({ ...prev, termo }));
      setPage(1); // Voltar para a primeira página ao buscar
    }, 500);
  };
  
  // Função para exportar dados
  const handleExport = async () => {
    try {
      toast.info('Preparando exportação...');
      
      // Exportar com os filtros atuais, mas sem paginação
      const result = await exportarPrecatorios(filtros);
      
      if (result && result.url) {
        // Se o backend retornar uma URL para download
        window.open(result.url, '_blank');
        toast.success('Exportação concluída!');
      } else if (result && result.data) {
        // Se o backend retornar os dados diretamente
        // Aqui você pode implementar a lógica para criar um arquivo CSV/Excel no frontend
        // Por enquanto, vamos apenas mostrar um toast
        toast.success('Exportação concluída!');
      }
    } catch (err) {
      console.error('Erro ao exportar precatórios:', err);
      toast.error('Não foi possível exportar os precatórios. Tente novamente mais tarde.');
    }
  };
  
  // Função para excluir precatório
  const handleDelete = async (id: string) => {
    try {
      setIsDeleting(true);
      await deletePrecatorio(id);
      
      // Atualizar lista após exclusão
      setPrecatorios(prevPrecatorios => prevPrecatorios.filter(p => p.id !== id));
      setSelecionados(prevSelecionados => prevSelecionados.filter(itemId => itemId !== id));
      
      toast.success('Precatório excluído com sucesso!');
      setDeleteModalOpen(false);
    } catch (err) {
      console.error('Erro ao excluir precatório:', err);
      toast.error('Não foi possível excluir o precatório. Tente novamente mais tarde.');
    } finally {
      setIsDeleting(false);
    }
  };
  
  // Função para excluir múltiplos precatórios
  const handleBulkDelete = async () => {
    if (selecionados.length === 0) return;
    
    try {
      setIsLoading(true);
      
      // Excluir cada precatório selecionado
      for (const id of selecionados) {
        await deletePrecatorio(id);
      }
      
      // Atualizar lista após exclusão
      setPrecatorios(prevPrecatorios => 
        prevPrecatorios.filter(p => !selecionados.includes(p.id))
      );
      setSelecionados([]);
      
      toast.success(`${selecionados.length} precatórios excluídos com sucesso!`);
    } catch (err) {
      console.error('Erro ao excluir precatórios em massa:', err);
      toast.error('Ocorreu um erro ao excluir os precatórios selecionados.');
    } finally {
      setIsLoading(false);
    }
  };
  
  // Função para atualizar status de múltiplos precatórios
  const handleBulkUpdateStatus = async (status: StatusPrecatorio) => {
    if (selecionados.length === 0) return;
    
    try {
      setIsLoading(true);
      
      // Atualizar cada precatório selecionado
      for (const id of selecionados) {
        await updatePrecatorio(id, { status });
      }
      
      // Atualizar lista após atualização
      setPrecatorios(prevPrecatorios => 
        prevPrecatorios.map(p => 
          selecionados.includes(p.id) ? { ...p, status } : p
        )
      );
      
      toast.success(`Status de ${selecionados.length} precatórios atualizado para "${formatStatus(status)}"!`);
    } catch (err) {
      console.error('Erro ao atualizar status em massa:', err);
      toast.error('Ocorreu um erro ao atualizar o status dos precatórios selecionados.');
    } finally {
      setIsLoading(false);
    }
  };
  
  // Função para atribuir responsável a múltiplos precatórios
  const handleBulkAssignResponsible = async (responsavelId: string) => {
    if (selecionados.length === 0) return;
    
    try {
      setIsLoading(true);
      
      // Atualizar cada precatório selecionado
      for (const id of selecionados) {
        await updatePrecatorio(id, { responsavel_id: responsavelId });
      }
      
      // Atualizar lista após atualização
      setPrecatorios(prevPrecatorios => 
        prevPrecatorios.map(p => 
          selecionados.includes(p.id) ? { ...p, responsavel_id: responsavelId } : p
        )
      );
      
      const responsavel = responsaveis.find(r => r.id === responsavelId);
      toast.success(`${selecionados.length} precatórios atribuídos a ${responsavel?.nome || 'responsável'}!`);
    } catch (err) {
      console.error('Erro ao atribuir responsável em massa:', err);
      toast.error('Ocorreu um erro ao atribuir responsável aos precatórios selecionados.');
    } finally {
      setIsLoading(false);
    }
  };
  
  // Função para lidar com sucesso no formulário
  const handleFormSuccess = () => {
    // Recarregar dados
    setPage(1);
    setFiltros(prev => ({ ...prev })); // Trigger para recarregar
  };
  
  // Renderizar conteúdo com base no estado
  const renderContent = () => {
    if (error) {
      return (
        <div className="rounded-md border p-8 text-center">
          <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-destructive/10">
            <AlertTriangle className="h-6 w-6 text-destructive" />
          </div>
          <h3 className="mt-4 text-lg font-semibold">Erro ao carregar precatórios</h3>
          <p className="mt-2 text-sm text-muted-foreground">
            {error}
          </p>
          <Button 
            variant="outline" 
            className="mt-4" 
            onClick={() => window.location.reload()}
          >
            Tentar novamente
          </Button>
        </div>
      );
    }
    
    return (
      <>
        {/* Estatísticas */}
        <EstatisticasPrecatorios 
          estatisticas={estatisticas} 
          isLoading={isLoadingEstatisticas} 
        />
        
        {/* Barra de ações */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <div className="flex items-center gap-2 w-full sm:w-auto">
            <div className="relative w-full sm:w-64">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Buscar precatórios..."
                className="pl-8"
                onChange={(e) => handleSearch(e.target.value)}
              />
            </div>
            <FiltrosAvancados 
              filtros={filtros} 
              setFiltros={setFiltros} 
              isLoading={isLoading}
              clientes={clientes}