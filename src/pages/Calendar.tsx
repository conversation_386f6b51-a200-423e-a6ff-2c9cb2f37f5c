import { useEffect, useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';
import CalendarComponent from "@/components/Calendar";
import { ensureValidSession } from "@/lib/auth-helpers";
import { Button } from "@/components/ui/button";
import { Calendar as CalendarIcon } from "lucide-react";
import { TopNav } from "@/components/top-nav";

export default function CalendarPage() {
  const navigate = useNavigate();
  const { user, isLoading } = useAuth();
  const [verifyingAuth, setVerifyingAuth] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Altura estimada da barra de navegação superior
  const topNavHeight = "65px";

  // Abordagem mais tolerante de verificação de autenticação
  useEffect(() => {
    const verifyAuthentication = async () => {
      try {
        console.log("CalendarPage: Verificando autenticação...");

        // 1. Verificar se o contexto de autenticação já está pronto
        if (!isLoading) {
          if (user) {
            console.log("CalendarPage: Usuário autenticado via contexto:", user.email);
            setIsAuthenticated(true);
            setVerifyingAuth(false);
            return;
          }

          console.log("CalendarPage: Usuário não encontrado no contexto, verificando outras fontes...");

          // 2. Tentar garantir uma sessão válida usando a função auxiliar melhorada
          const authResult = await ensureValidSession();

          if (authResult.success) {
            if (authResult.session) {
              console.log("CalendarPage: Sessão válida encontrada");
              setIsAuthenticated(true);
              setVerifyingAuth(false);
              return;
            }

            if (authResult.limited && authResult.userProfile) {
              console.log("CalendarPage: Acesso limitado com perfil local");
              // Permitir acesso mesmo sem sessão válida, mas mostrar aviso
              toast.warning("Acesso limitado", {
                description: "Você está usando o sistema com autenticação limitada. Algumas funcionalidades podem estar restritas."
              });
              setIsAuthenticated(true);
              setVerifyingAuth(false);
              return;
            }
          }

          // 3. Verificar se temos um perfil no localStorage como última tentativa
          const userProfileStr = localStorage.getItem("userProfile");
          if (userProfileStr) {
            console.log("CalendarPage: Perfil encontrado no localStorage como última tentativa");
            // Permitir acesso mesmo sem sessão válida, mas mostrar aviso
            toast.warning("Modo offline", {
              description: "Você está usando o sistema em modo offline. Algumas funcionalidades podem não estar disponíveis."
            });
            setIsAuthenticated(true);
            setVerifyingAuth(false);
            return;
          }

          // 4. Se chegamos aqui, não conseguimos autenticar o usuário
          console.error("CalendarPage: Não foi possível verificar autenticação por nenhum método");
          toast.error("Problema de autenticação", {
            description: "Não conseguimos verificar sua autenticação. Tente fazer login novamente."
          });

          setIsAuthenticated(false);
          setVerifyingAuth(false);
        }
      } catch (error) {
        console.error("CalendarPage: Erro na verificação:", error);

        // Mesmo com erro, verificar se temos um perfil no localStorage como última tentativa
        try {
          const userProfileStr = localStorage.getItem("userProfile");
          if (userProfileStr) {
            console.log("CalendarPage: Perfil encontrado no localStorage após erro");
            toast.warning("Modo de recuperação", {
              description: "Ocorreu um erro, mas você pode continuar usando o sistema com funcionalidades limitadas."
            });
            setIsAuthenticated(true);
            setVerifyingAuth(false);
            return;
          }
        } catch (e) {
          console.error("CalendarPage: Erro ao verificar perfil no localStorage:", e);
        }

        setIsAuthenticated(false);
        setVerifyingAuth(false);
      }
    };

    verifyAuthentication();
  }, [user, isLoading, navigate]);

  const renderContent = () => {
    // Mostrar estado de carregamento apenas se estiver verificando
    if (verifyingAuth) {
      return (
        <div className="flex flex-col items-center justify-center flex-1 p-4">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mb-4"></div>
          <p className="text-sm text-gray-500">Verificando autenticação...</p>
        </div>
      );
    }

    // Se não estiver autenticado, mostrar botão para fazer login
    if (!isAuthenticated) {
      return (
        <div className="flex flex-col items-center justify-center flex-1 p-4">
          <div className="text-center mb-6">
            <h2 className="text-2xl font-bold text-red-600 mb-2">Problema de Autenticação</h2>
            <p className="text-gray-600 mb-4">Não foi possível verificar sua autenticação para acessar o calendário.</p>
          </div>
          <Button
            onClick={() => navigate('/login')}
            className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors"
          >
            Fazer Login Novamente
          </Button>
        </div>
      );
    }

    // Se estiver autenticado, mostrar o calendário com lista de compromissos ao lado
    return (
      <div className="p-4 md:p-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
          <div className="lg:col-span-2">
            <CalendarComponent layout="compact" />
          </div>
          <div className="bg-card rounded-lg shadow-sm border overflow-hidden">
            <div className="p-4 border-b bg-muted/30 flex-shrink-0">
              <h2 className="text-lg font-medium">Compromissos</h2>
            </div>
            <CalendarComponent layout="list-only" />
          </div>
        </div>
      </div>
    );
  };

  // Se estiver autenticado, mostrar o calendário
  return (
    <div className="flex flex-col h-screen w-screen overflow-hidden">
      <TopNav
        title="Calendário"
        icon={<CalendarIcon className="h-6 w-6 text-primary" />}
      />

      {/* Área Principal de Conteúdo */}
      <main
        className="flex-1 overflow-auto bg-transparent pt-[65px] flex flex-col"
      >
        {renderContent()}
      </main>
    </div>
  );
}