import { useState, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { FileSpreadsheet, Plus, Search, Filter, MoreHorizontal } from "lucide-react";
import { SharedPrecatorioForm } from "@/components/Precatorios/SharedPrecatorioForm";
import { TopNav } from "@/components/top-nav";
import { toast } from "sonner";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/lib/supabase";
import { formatarMoeda } from "@/lib/utils";
import { NoPrecatoriosMessage } from "@/components/ui/no-results-message";

// Componentes UI
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  NavigationMenu,
  NavigationMenuItem,
  NavigationMenuList,
  NavigationMenuTrigger,
} from "@/components/ui/navigation-menu";
import { cn } from "@/lib/utils";
import { Skeleton } from "@/components/ui/skeleton";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";

// Interface para o tipo Precatorio
interface Precatorio {
  id: string;
  numero: string;
  tribunal: string;
  valor: number;
  status: string;
  tipo: 'PRECATORIO' | 'RPV';
  cliente: {
    id: string;
    nome: string;
  };
  responsavel?: {
    id: string;
    nome: string;
  };
  dataCriacao: string;
  dataAtualizacao: string;
}

function PrecatoriosLista() {
  const [precatorios, setPrecatorios] = useState<Precatorio[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [searchParams, setSearchParams] = useSearchParams();
  const [tipoVisualizacao, setTipoVisualizacao] = useState<'PRECATORIO' | 'RPV' | null>(
    searchParams.get('tipo') as 'PRECATORIO' | 'RPV' | null
  );
  const [isFormModalOpen, setIsFormModalOpen] = useState(false);
  const [authError, setAuthError] = useState(false);
  const navigate = useNavigate();
  const { isAdmin } = useAuth();

  // Função para carregar os precatórios
  const carregarPrecatorios = async () => {
    setLoading(true);
    setAuthError(false);
    try {
      // Verificar se a sessão está válida
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
      if (sessionError || !sessionData.session) {
        console.error("Erro de autenticação ao carregar precatórios:", sessionError);
        setAuthError(true);
        setLoading(false);
        return;
      }

      let query = supabase
        .from('precatorios')
        .select(`
          id,
          numero_precatorio,
          valor_total,
          status,
          status_id,
          tipo,
          created_at,
          updated_at,
          tribunal_id(id, nome),
          beneficiario_id(id, nome),
          responsavel_id(id, nome)
        `)
        .or('is_deleted.is.null,is_deleted.eq.false')
        .order('created_at', { ascending: false });

      // Filtrar por tipo se especificado
      if (tipoVisualizacao) {
        query = query.eq('tipo', tipoVisualizacao);
      }

      const { data, error } = await query;

      console.log("Resultado da busca de precatórios:", {
        dataLength: data?.length || 0,
        error,
        query: 'or(is_deleted.is.null,is_deleted.eq.false)'
      });

      if (error) {
        // Verificar se é um erro de autenticação
        if (error.code === 'PGRST301' || error.message?.includes('JWT')) {
          setAuthError(true);
          throw new Error("Erro de autenticação: " + error.message);
        }
        throw error;
      }

      if (!data || data.length === 0) {
        setPrecatorios([]);
        setLoading(false);
        return;
      }

      // Converter os dados para o formato da interface
      const precatoriosFormatados = data.map((item: any): Precatorio => ({
        id: item.id,
        numero: item.numero_precatorio || '',
        tribunal: item.tribunal_id?.nome || 'Não especificado',
        valor: item.valor_total || 0,
        status: item.status || 'NOVO',
        tipo: item.tipo || 'PRECATORIO',
        cliente: {
          id: item.beneficiario_id?.id || '',
          nome: item.beneficiario_id?.nome || 'Cliente não especificado'
        },
        responsavel: item.responsavel_id ? {
          id: item.responsavel_id.id,
          nome: item.responsavel_id.nome
        } : undefined,
        dataCriacao: item.created_at,
        dataAtualizacao: item.updated_at
      }));

      setPrecatorios(precatoriosFormatados);
      setLoading(false);
    } catch (error) {
      console.error("Erro ao carregar precatórios:", error);

      // Verificar se é um erro de autenticação
      if (error instanceof Error &&
          (error.message.includes('auth') ||
           error.message.includes('JWT') ||
           error.message.includes('session'))) {
        setAuthError(true);
        toast.error("Erro de autenticação. Tente recarregar a página ou fazer login novamente.");
      } else {
        toast.error("Erro ao carregar precatórios. Tente novamente mais tarde.");
      }

      setLoading(false);
    }
  };

  // Função para recarregar a página
  const handleRefresh = () => {
    window.location.reload();
  };

  // Carregar precatórios quando o componente montar ou o tipo mudar
  useEffect(() => {
    carregarPrecatorios();
  }, [tipoVisualizacao]);

  // Atualizar o tipo de visualização quando o parâmetro de URL mudar
  useEffect(() => {
    const tipo = searchParams.get('tipo') as 'PRECATORIO' | 'RPV' | null;
    setTipoVisualizacao(tipo);
  }, [searchParams]);

  // Função para filtrar precatórios com base no termo de busca
  const precatoriosFiltrados = precatorios.filter(precatorio => {
    const searchLower = searchTerm.toLowerCase();
    return (
      precatorio.numero.toLowerCase().includes(searchLower) ||
      precatorio.tribunal.toLowerCase().includes(searchLower) ||
      precatorio.cliente.nome.toLowerCase().includes(searchLower) ||
      (precatorio.responsavel?.nome?.toLowerCase().includes(searchLower) || false) ||
      precatorio.status.toLowerCase().includes(searchLower)
    );
  });

  // Função para navegar para a página de detalhes
  const navegarParaDetalhes = (id: string) => {
    navigate(`/precatorios/${id}`);
  };

  // Função para navegar para a página de edição
  const navegarParaEdicao = (id: string) => {
    navigate(`/precatorios/editar/${id}`);
  };

  // Função para salvar um novo precatório
  const handleSavePrecatorio = async (precatorio: any) => {
    try {
      // Aqui você pode adicionar lógica para salvar o precatório no Supabase
      console.log('Precatório salvo:', precatorio);
      toast.success('Precatório criado com sucesso!');

      // Recarregar a lista de precatórios
      carregarPrecatorios();
    } catch (error) {
      console.error('Erro ao salvar precatório:', error);
      toast.error('Erro ao criar precatório. Tente novamente.');
    }
  };

  // Função para mudar o tipo de visualização
  const mudarTipoVisualizacao = (tipo: 'PRECATORIO' | 'RPV' | null) => {
    if (tipo) {
      setSearchParams({ tipo });
    } else {
      setSearchParams({});
    }
    setTipoVisualizacao(tipo);
  };

  // Renderizar esqueletos durante o carregamento
  const renderizarEsqueletos = () => {
    return Array(5).fill(0).map((_, index) => (
      <TableRow key={index}>
        <TableCell><Skeleton className="h-4 w-[100px]" /></TableCell>
        <TableCell><Skeleton className="h-4 w-[150px]" /></TableCell>
        <TableCell><Skeleton className="h-4 w-[120px]" /></TableCell>
        <TableCell><Skeleton className="h-4 w-[80px]" /></TableCell>
        <TableCell><Skeleton className="h-4 w-[100px]" /></TableCell>
        <TableCell><Skeleton className="h-4 w-[80px]" /></TableCell>
        <TableCell><Skeleton className="h-8 w-8 rounded-full" /></TableCell>
      </TableRow>
    ));
  };

  // Função para obter a cor do status
  const getStatusColor = (status: string) => {
    const statusMap: Record<string, string> = {
      'NOVO': 'bg-blue-100 text-blue-800 dark:bg-blue-950/70 dark:text-blue-200',
      'EM_ANALISE': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-950/70 dark:text-yellow-200',
      'APROVADO': 'bg-green-100 text-green-800 dark:bg-green-950/70 dark:text-green-200',
      'REJEITADO': 'bg-red-100 text-red-800 dark:bg-red-950/70 dark:text-red-200',
      'EM_PROCESSAMENTO': 'bg-purple-100 text-purple-800 dark:bg-purple-950/70 dark:text-purple-200',
      'CONCLUIDO': 'bg-teal-100 text-teal-800 dark:bg-teal-950/70 dark:text-teal-200',
      'CANCELADO': 'bg-gray-100 text-gray-800 dark:bg-gray-950/70 dark:text-gray-200',
    };

    return statusMap[status] || 'bg-gray-100 text-gray-800 dark:bg-gray-950/70 dark:text-gray-200';
  };

  // Função para formatar o status para exibição
  const formatarStatus = (status: string) => {
    const statusMap: Record<string, string> = {
      'NOVO': 'Novo',
      'EM_ANALISE': 'Em Análise',
      'APROVADO': 'Aprovado',
      'REJEITADO': 'Rejeitado',
      'EM_PROCESSAMENTO': 'Em Processamento',
      'CONCLUIDO': 'Concluído',
      'CANCELADO': 'Cancelado',
    };

    return statusMap[status] || status;
  };

  return (
    <div className="flex flex-col h-screen w-screen overflow-hidden">
      <TopNav
        title="Precatórios/RPVs"
        icon={<FileSpreadsheet className="h-6 w-6 text-primary" />}
      >
        {/* Navegação Principal (Precatórios/RPVs) */}
        <NavigationMenu>
          <NavigationMenuList>
            <NavigationMenuItem>
              <NavigationMenuTrigger
                className={cn(tipoVisualizacao === 'PRECATORIO' && "bg-accent text-accent-foreground")}
                onClick={() => mudarTipoVisualizacao('PRECATORIO')}
              >
                Precatórios
              </NavigationMenuTrigger>
            </NavigationMenuItem>
            <NavigationMenuItem>
               <NavigationMenuTrigger
                className={cn(tipoVisualizacao === 'RPV' && "bg-accent text-accent-foreground")}
                onClick={() => mudarTipoVisualizacao('RPV')}
               >
                 RPVs
               </NavigationMenuTrigger>
            </NavigationMenuItem>
            <NavigationMenuItem>
               <NavigationMenuTrigger
                className={cn(tipoVisualizacao === null && "bg-accent text-accent-foreground")}
                onClick={() => mudarTipoVisualizacao(null)}
               >
                 Todos
               </NavigationMenuTrigger>
            </NavigationMenuItem>
          </NavigationMenuList>
        </NavigationMenu>
      </TopNav>

      <div className="flex-1 p-6 overflow-auto">
        <div className="flex justify-between items-center mb-6">
          <div className="relative w-full max-w-sm">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Buscar precatórios..."
              className="pl-8 w-full"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              <Filter className="h-4 w-4 mr-2" />
              Filtrar
            </Button>
            <Button size="sm" onClick={() => setIsFormModalOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Novo Precatório
            </Button>
          </div>
        </div>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Lista de {tipoVisualizacao === 'PRECATORIO' ? 'Precatórios' : tipoVisualizacao === 'RPV' ? 'RPVs' : 'Precatórios/RPVs'}</CardTitle>
            <CardDescription>
              {precatoriosFiltrados.length} {tipoVisualizacao === 'PRECATORIO' ? 'precatórios' : tipoVisualizacao === 'RPV' ? 'RPVs' : 'registros'} encontrados
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Número</TableHead>
                    <TableHead>Cliente</TableHead>
                    <TableHead>Tribunal</TableHead>
                    <TableHead>Valor</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Tipo</TableHead>
                    <TableHead className="text-right">Ações</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    renderizarEsqueletos()
                  ) : precatoriosFiltrados.length > 0 ? (
                    precatoriosFiltrados.map((precatorio) => (
                      <TableRow key={precatorio.id} className="cursor-pointer hover:bg-muted/50" onClick={() => navegarParaDetalhes(precatorio.id)}>
                        <TableCell className="font-medium">{precatorio.numero}</TableCell>
                        <TableCell>{precatorio.cliente.nome}</TableCell>
                        <TableCell>{precatorio.tribunal}</TableCell>
                        <TableCell>{formatarMoeda(precatorio.valor)}</TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(precatorio.status)}>
                            {formatarStatus(precatorio.status)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            {precatorio.tipo === 'PRECATORIO' ? 'Precatório' : 'RPV'}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">Abrir menu</span>
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Ações</DropdownMenuLabel>
                              <DropdownMenuItem onClick={(e) => {
                                e.stopPropagation();
                                navegarParaDetalhes(precatorio.id);
                              }}>
                                Ver detalhes
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={(e) => {
                                e.stopPropagation();
                                navegarParaEdicao(precatorio.id);
                              }}>
                                Editar
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                className="text-red-600 dark:text-red-400"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  // Implementar lógica de exclusão
                                  toast.error("Funcionalidade não implementada");
                                }}
                              >
                                Excluir
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={7} className="h-24">
                        <NoPrecatoriosMessage
                          showClearFiltersButton={searchTerm !== "" && !authError}
                          onClearFilters={() => setSearchTerm("")}
                          showRefreshButton={true}
                          onRefresh={handleRefresh}
                          isAuthError={authError}
                        />
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Modal de novo precatório/RPV */}
      <SharedPrecatorioForm
        isOpen={isFormModalOpen}
        onOpenChange={setIsFormModalOpen}
        onSave={handleSavePrecatorio}
        initialData={{
          tipo: tipoVisualizacao || 'PRECATORIO'
        }}
      />
    </div>
  );
}

export default PrecatoriosLista;
