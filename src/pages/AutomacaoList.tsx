import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { GitBranch, Plus, Search, Clock, Star, PenTool, AlertCircle, CheckCircle, Filter, Eye, EyeOff, MoreHorizontal, Play, Pause, Settings } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Too<PERSON><PERSON>, Toolt<PERSON>Content, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { TopNav } from '@/components/top-nav';

// Exemplos de automações pré-definidas
const automacaoTemplates = [
  {
    id: 'template-1',
    titulo: 'Revisão de Petição',
    descricao: 'Fluxo de trabalho para revisão e aprovação de petições antes do protocolo',
    categoria: 'Processos',
    criador: 'Carlos Mendes',
    dataCriacao: '10/05/2023',
    favorito: true,
  },
  {
    id: 'template-2',
    titulo: 'Onboarding de Cliente',
    descricao: 'Processo de cadastro e integração de novos clientes',
    categoria: 'Clientes',
    criador: 'Ana Silva',
    dataCriacao: '22/06/2023',
    favorito: false,
  },
  {
    id: 'template-3',
    titulo: 'Distribuição de Tarefas',
    descricao: 'Automação para distribuir tarefas entre a equipe baseado em especialidade',
    categoria: 'Tarefas',
    criador: 'Roberto Santos',
    dataCriacao: '15/07/2023',
    favorito: true,
  },
];

// Exemplos de automações do usuário com mais detalhes
const minhasAutomacoes = [
  {
    id: 'auto-1',
    titulo: 'Fluxo de Análise de Precatórios',
    descricao: 'Automação para análise e acompanhamento de precatórios',
    categoria: 'Precatórios',
    ultimaModificacao: '02/02/2024',
    status: 'Ativo',
    ativo: true,
    erros: 0,
    execucoes: 28,
    ultimaExecucao: '15/02/2024',
    tempoMedio: '2 min',
  },
  {
    id: 'auto-2',
    titulo: 'Aprovação de Documentos',
    descricao: 'Fluxo de aprovação interna de documentos jurídicos',
    categoria: 'Documentos',
    ultimaModificacao: '28/01/2024',
    status: 'Rascunho',
    ativo: false,
    erros: 0,
    execucoes: 0,
    ultimaExecucao: '-',
    tempoMedio: '-',
  },
  {
    id: 'auto-3',
    titulo: 'Notificação de Prazos',
    descricao: 'Sistema de alerta para prazos processuais próximos do vencimento',
    categoria: 'Prazos',
    ultimaModificacao: '15/01/2024',
    status: 'Ativo',
    ativo: true,
    erros: 2,
    execucoes: 45,
    ultimaExecucao: '16/02/2024',
    tempoMedio: '1 min',
  },
  {
    id: 'auto-4',
    titulo: 'Distribuição de Processos',
    descricao: 'Automação para distribuir novos processos entre a equipe',
    categoria: 'Processos',
    ultimaModificacao: '05/02/2024',
    status: 'Ativo',
    ativo: true,
    erros: 0,
    execucoes: 15,
    ultimaExecucao: '14/02/2024',
    tempoMedio: '3 min',
  },
];

// Componente de card para templates de automação
function AutomacaoTemplateCard({ template, onSelect }: any) {
  return (
    <Card className="h-full hover:shadow-md transition-shadow">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <CardTitle className="text-lg">{template.titulo}</CardTitle>
          {template.favorito ? <Star className="w-5 h-5 text-yellow-500 fill-yellow-500" /> : null}
        </div>
        <CardDescription className="text-sm text-muted-foreground">{template.categoria}</CardDescription>
      </CardHeader>
      <CardContent className="pb-2">
        <p className="text-sm">{template.descricao}</p>
        <div className="flex items-center mt-3 text-xs text-muted-foreground">
          <span>Criado por {template.criador}</span>
          <span className="mx-2">•</span>
          <span>{template.dataCriacao}</span>
        </div>
      </CardContent>
      <CardFooter>
        <Button size="sm" onClick={() => onSelect(template.id)} className="w-full">
          Usar este modelo
        </Button>
      </CardFooter>
    </Card>
  );
}

// Componente de card para automações do usuário melhorado
function MinhaAutomacaoCard({ automacao, onOpen, onToggleActive }: any) {
  return (
    <Card className="h-full hover:shadow-md transition-shadow">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div className="flex items-center gap-2">
            <CardTitle className="text-lg">{automacao.titulo}</CardTitle>
            {automacao.erros > 0 && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="cursor-help">
                      <AlertCircle className="w-4 h-4 text-red-500" />
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Este fluxo possui {automacao.erros} erro(s) recente(s)</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </div>
          <div className="flex items-center gap-2">
            <span className={`text-xs px-2 py-1 rounded-full ${
              automacao.status === 'Ativo' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
              'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200'
            }`}>
              {automacao.status}
            </span>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => onOpen(automacao.id)}>
                  <Eye className="mr-2 h-4 w-4" />
                  <span>Ver detalhes</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onToggleActive(automacao.id)}>
                  {automacao.ativo ? (
                    <>
                      <Pause className="mr-2 h-4 w-4" />
                      <span>Desativar</span>
                    </>
                  ) : (
                    <>
                      <Play className="mr-2 h-4 w-4" />
                      <span>Ativar</span>
                    </>
                  )}
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <AlertCircle className="mr-2 h-4 w-4" />
                  <span>Ver logs de erro</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="text-xs">
            {automacao.categoria}
          </Badge>
          {automacao.ativo ? (
            <Badge variant="secondary" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 text-xs">
              <CheckCircle className="w-3 h-3 mr-1" /> Ativo
            </Badge>
          ) : (
            <Badge variant="secondary" className="bg-neutral-100 text-neutral-800 dark:bg-neutral-800 dark:text-neutral-200 text-xs">
              <EyeOff className="w-3 h-3 mr-1" /> Inativo
            </Badge>
          )}
        </div>
      </CardHeader>
      <CardContent className="pb-2">
        <p className="text-sm">{automacao.descricao}</p>

        <div className="grid grid-cols-2 gap-x-4 gap-y-2 mt-3 text-xs">
          <div>
            <span className="text-muted-foreground">Execuções:</span> {automacao.execucoes}
          </div>
          <div>
            <span className="text-muted-foreground">Tempo médio:</span> {automacao.tempoMedio}
          </div>
          <div>
            <span className="text-muted-foreground">Última execução:</span> {automacao.ultimaExecucao}
          </div>
          <div>
            <span className="text-muted-foreground">Erros:</span> {automacao.erros > 0 ? (
              <span className="text-red-500 font-medium">{automacao.erros}</span>
            ) : (
              <span>{automacao.erros}</span>
            )}
          </div>
        </div>

        <div className="flex items-center mt-3 text-xs text-muted-foreground">
          <Clock className="w-3 h-3 mr-1" />
          <span>Modificado em {automacao.ultimaModificacao}</span>
        </div>
      </CardContent>
      <CardFooter className="flex gap-2">
        <Button size="sm" onClick={() => onOpen(automacao.id)} variant="outline" className="flex-1">
          Abrir fluxo
        </Button>
        <Button
          size="sm"
          onClick={() => onToggleActive(automacao.id)}
          variant={automacao.ativo ? "destructive" : "default"}
          className="flex-1"
        >
          {automacao.ativo ? 'Desativar' : 'Ativar'}
        </Button>
      </CardFooter>
    </Card>
  );
}

export default function AutomacaoList() {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('todos');
  const [categoriaFilter, setCategoriaFilter] = useState<string>('todas');
  const [showErrors, setShowErrors] = useState<boolean>(false);
  const [automacoes, setAutomacoes] = useState(minhasAutomacoes);

  // Extrair categorias únicas para o filtro
  const categorias = ['todas', ...new Set(minhasAutomacoes.map(a => a.categoria.toLowerCase()))];

  // Filtragem de automações com base nos filtros
  const filteredTemplates = automacaoTemplates.filter(template =>
    template.titulo.toLowerCase().includes(searchTerm.toLowerCase()) ||
    template.descricao.toLowerCase().includes(searchTerm.toLowerCase()) ||
    template.categoria.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredAutomacoes = automacoes.filter(automacao => {
    // Filtro de busca
    const matchesSearch =
      automacao.titulo.toLowerCase().includes(searchTerm.toLowerCase()) ||
      automacao.descricao.toLowerCase().includes(searchTerm.toLowerCase()) ||
      automacao.categoria.toLowerCase().includes(searchTerm.toLowerCase());

    // Filtro de status
    const matchesStatus =
      statusFilter === 'todos' ||
      (statusFilter === 'ativos' && automacao.ativo) ||
      (statusFilter === 'inativos' && !automacao.ativo);

    // Filtro de categoria
    const matchesCategoria =
      categoriaFilter === 'todas' ||
      automacao.categoria.toLowerCase() === categoriaFilter;

    // Filtro de erros
    const matchesErrors =
      !showErrors ||
      (showErrors && automacao.erros > 0);

    return matchesSearch && matchesStatus && matchesCategoria && matchesErrors;
  });

  // Funções para navegação
  const handleSelectTemplate = (id: string) => {
    navigate('/automacao/nova', { state: { templateId: id } });
  };

  const handleOpenAutomacao = (id: string) => {
    navigate(`/automacao/${id}`);
  };

  const handleCreateNew = () => {
    navigate('/automacao/nova');
  };

  // Função para ativar/desativar automação
  const handleToggleActive = (id: string) => {
    setAutomacoes(prev =>
      prev.map(automacao =>
        automacao.id === id
          ? {
              ...automacao,
              ativo: !automacao.ativo,
              status: !automacao.ativo ? 'Ativo' : automacao.status
            }
          : automacao
      )
    );
  };

  return (
    <div className="flex flex-col h-screen w-screen overflow-hidden">
      <TopNav
        title="Automações"
        icon={<Settings className="h-6 w-6 text-primary" />}
      />

      <div className="container mx-auto py-0 pt-[65px] max-w-7xl overflow-auto">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Automações de Fluxo</h1>
            <p className="text-muted-foreground mt-1">
              Crie e gerencie fluxos de trabalho automatizados para seu escritório
            </p>
          </div>
          <Button onClick={handleCreateNew} size="default">
            <Plus className="w-4 h-4 mr-2" /> Nova Automação
          </Button>
        </div>

      <Tabs defaultValue="minhas">
        <TabsList className="mb-4">
          <TabsTrigger value="minhas" className="flex items-center">
            <PenTool className="w-4 h-4 mr-2" /> Minhas Automações
          </TabsTrigger>
          <TabsTrigger value="templates" className="flex items-center">
            <GitBranch className="w-4 h-4 mr-2" /> Modelos
          </TabsTrigger>
        </TabsList>

        <TabsContent value="minhas" className="mt-0">
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
              <Input
                className="pl-10"
                placeholder="Buscar automações..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            <div className="flex flex-wrap gap-3">
              <div className="w-40">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="todos">Todos os status</SelectItem>
                    <SelectItem value="ativos">Ativos</SelectItem>
                    <SelectItem value="inativos">Inativos</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="w-40">
                <Select value={categoriaFilter} onValueChange={setCategoriaFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Categoria" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="todas">Todas categorias</SelectItem>
                    {categorias.filter(c => c !== 'todas').map(categoria => (
                      <SelectItem key={categoria} value={categoria}>
                        {categoria.charAt(0).toUpperCase() + categoria.slice(1)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="show-errors"
                  checked={showErrors}
                  onCheckedChange={setShowErrors}
                />
                <Label htmlFor="show-errors" className="cursor-pointer">
                  <div className="flex items-center">
                    <AlertCircle className="w-4 h-4 mr-1 text-red-500" />
                    Mostrar erros
                  </div>
                </Label>
              </div>
            </div>
          </div>

          {filteredAutomacoes.length === 0 ? (
            <div className="text-center py-12">
              <Filter className="w-12 h-12 mx-auto text-muted-foreground" />
              <h3 className="mt-4 text-lg font-medium">Nenhuma automação encontrada</h3>
              <p className="mt-2 text-muted-foreground">
                {searchTerm || statusFilter !== 'todos' || categoriaFilter !== 'todas' || showErrors
                  ? 'Tente ajustar os filtros para ver mais resultados.'
                  : 'Você ainda não tem automações. Crie sua primeira automação agora!'}
              </p>
              <Button onClick={handleCreateNew} className="mt-4">
                <Plus className="w-4 h-4 mr-2" /> Nova Automação
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredAutomacoes.map((automacao) => (
                <MinhaAutomacaoCard
                  key={automacao.id}
                  automacao={automacao}
                  onOpen={handleOpenAutomacao}
                  onToggleActive={handleToggleActive}
                />
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="templates" className="mt-0">
          <div className="relative mb-6">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
            <Input
              className="pl-10"
              placeholder="Buscar modelos..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredTemplates.map((template) => (
              <AutomacaoTemplateCard
                key={template.id}
                template={template}
                onSelect={handleSelectTemplate}
              />
            ))}
          </div>
        </TabsContent>
      </Tabs>
      </div>
    </div>
  );
}