-- Configuração das políticas de segurança do Supabase para autenticação e autorização
-- Execute este script no SQL Editor do Supabase

-- 1. <PERSON><PERSON><PERSON><PERSON> que todos os usuários se tornem profiles ao se cadastrar
CREATE OR REPLACE FUNCTION public.handle_new_user() 
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, email, nome, role, status, created_at)
  VALUES (new.id, new.email, new.raw_user_meta_data->>'nome', 'assistente', 'ativo', now());
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger para criar um profile quando um novo usuário é cadastrado
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 2. Configurar políticas de segurança para controle de acesso

-- Políticas para tabela profiles
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Permissão para visualizar o próprio perfil
CREATE POLICY "Usuários podem ver seu próprio perfil"
  ON public.profiles FOR SELECT
  USING (auth.uid() = id);

-- Permissão para administradores verem todos os perfis
CREATE POLICY "Administradores podem ver todos os perfis"
  ON public.profiles FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Permissão para gerentes verem perfis de assistentes
CREATE POLICY "Gerentes podem ver perfis de assistentes"
  ON public.profiles FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE id = auth.uid() AND (role = 'gerente_precatorio' OR role = 'gerente_operacional')
    ) AND role = 'assistente'
  );

-- Permissão para atualizar seu próprio perfil (exceto role)
CREATE POLICY "Usuários podem atualizar seu próprio perfil"
  ON public.profiles FOR UPDATE
  USING (auth.uid() = id)
  WITH CHECK (
    auth.uid() = id AND 
    (SELECT role FROM public.profiles WHERE id = auth.uid()) = role -- não pode mudar a própria role
  );

-- Permissão para administradores atualizarem qualquer perfil
CREATE POLICY "Administradores podem atualizar qualquer perfil"
  ON public.profiles FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- 3. Políticas para controle de acesso a precatórios

-- Administradores podem ver todos os precatórios
CREATE POLICY "Administradores podem ver todos os precatórios"
  ON public.precatorios FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Gerentes podem ver precatórios associados a eles
CREATE POLICY "Gerentes veem precatórios associados"
  ON public.precatorios FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE id = auth.uid() AND (role = 'gerente_precatorio' OR role = 'gerente_operacional')
    ) AND (
      responsavel_id = auth.uid() OR
      EXISTS (
        SELECT 1 FROM public.tarefas_atribuicoes ta
        JOIN public.tasks t ON ta.tarefa_id = t.id
        WHERE ta.user_id = auth.uid() AND t.precatorio_id = precatorios.id
      )
    )
  );

-- Usuários comuns podem ver precatórios onde estão atribuídos
CREATE POLICY "Usuários veem precatórios onde estão atribuídos"
  ON public.precatorios FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE id = auth.uid() AND role = 'assistente'
    ) AND
    EXISTS (
      SELECT 1 FROM public.tarefas_atribuicoes ta
      JOIN public.tasks t ON ta.tarefa_id = t.id
      WHERE ta.user_id = auth.uid() AND t.precatorio_id = precatorios.id
    )
  );

-- 4. Políticas para controle de acesso a tarefas

-- Administradores podem ver todas as tarefas
CREATE POLICY "Administradores podem ver todas as tarefas"
  ON public.tasks FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Gerentes podem ver tarefas associadas a eles ou a seus precatórios
CREATE POLICY "Gerentes veem tarefas associadas"
  ON public.tasks FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE id = auth.uid() AND (role = 'gerente_precatorio' OR role = 'gerente_operacional')
    ) AND (
      created_by = auth.uid() OR
      EXISTS (
        SELECT 1 FROM public.task_assignments
        WHERE user_id = auth.uid() AND task_id = tasks.id
      ) OR
      EXISTS (
        SELECT 1 FROM public.tarefas_atribuicoes ta
        JOIN public.tasks t ON ta.tarefa_id = t.id
        WHERE ta.user_id = auth.uid() AND t.precatorio_id = tasks.precatorio_id
      )
    )
  );

-- Usuários comuns podem ver tarefas atribuídas a eles
CREATE POLICY "Usuários veem tarefas atribuídas a eles"
  ON public.tasks FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE id = auth.uid() AND role = 'assistente'
    ) AND
    EXISTS (
      SELECT 1 FROM public.task_assignments
      WHERE user_id = auth.uid() AND task_id = tasks.id
    )
  );

-- 5. Políticas para controle de acesso a clientes

-- Administradores podem ver todos os clientes
CREATE POLICY "Administradores podem ver todos os clientes"
  ON public.clientes FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Gerentes podem ver todos os clientes
CREATE POLICY "Gerentes podem ver todos os clientes"
  ON public.clientes FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE id = auth.uid() AND (role = 'gerente_precatorio' OR role = 'gerente_operacional')
    )
  );

-- Usuários comuns podem ver clientes associados a suas tarefas
CREATE POLICY "Usuários veem clientes de suas tarefas"
  ON public.clientes FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE id = auth.uid() AND role = 'assistente'
    ) AND
    EXISTS (
      SELECT 1 FROM public.tarefas_atribuicoes ta
      JOIN public.tasks t ON ta.tarefa_id = t.id
      WHERE ta.user_id = auth.uid() AND t.cliente_id = clientes.id
    )
  );
