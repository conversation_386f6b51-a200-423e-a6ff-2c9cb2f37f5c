-- Adiciona a coluna created_by para permitir controle de acesso por usuário
ALTER TABLE task_templates ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES auth.users(id);

-- At<PERSON>iza as políticas de segurança para a tabela task_templates
BEGIN;
  -- Remove políticas existentes, se houver
  DROP POLICY IF EXISTS "Usuários podem ver seus próprios templates" ON task_templates;
  DROP POLICY IF EXISTS "Usuários podem criar seus próprios templates" ON task_templates;
  DROP POLICY IF EXISTS "Usu<PERSON>rios podem atualizar seus próprios templates" ON task_templates;
  DROP POLICY IF EXISTS "Usuários podem excluir seus próprios templates" ON task_templates;

  -- Ativa RLS na tabela
  ALTER TABLE task_templates ENABLE ROW LEVEL SECURITY;

  -- Cria novas políticas
  CREATE POLICY "Usuários podem ver seus próprios templates"
    ON task_templates FOR SELECT
    USING (auth.uid() = created_by);

  CREATE POLICY "Usuários podem criar seus próprios templates"
    ON task_templates FOR INSERT
    WITH CHECK (auth.uid() = created_by);

  CREATE POLICY "Usuários podem atualizar seus próprios templates"
    ON task_templates FOR UPDATE
    USING (auth.uid() = created_by);

  CREATE POLICY "Usuários podem excluir seus próprios templates"
    ON task_templates FOR DELETE
    USING (auth.uid() = created_by);
COMMIT;

-- Atualiza os registros existentes para usar o primeiro usuário disponível (opcional)
UPDATE task_templates 
SET created_by = (SELECT id FROM auth.users LIMIT 1)
WHERE created_by IS NULL;
