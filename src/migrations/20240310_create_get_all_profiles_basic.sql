-- Função para contornar problema de recursão infinita nas políticas de segurança
-- Esta função permite buscar perfis de usuários de forma segura

-- <PERSON>iro, vamos remover a função se ela já existir
DROP FUNCTION IF EXISTS public.get_all_profiles_basic();

-- Agora criamos a função
CREATE OR REPLACE FUNCTION public.get_all_profiles_basic()
RETURNS SETOF profiles
LANGUAGE sql
SECURITY DEFINER
AS $$
  -- Consulta simples que evita recursão infinita nas políticas de segurança
  SELECT 
    id, 
    email, 
    nome, 
    role, 
    status, 
    foto_url, 
    created_at,
    updated_at
  FROM 
    profiles
  ORDER BY 
    nome ASC
  LIMIT 100;
$$;

-- Conceder permiss<PERSON> para todos os usuários autenticados
GRANT EXECUTE ON FUNCTION public.get_all_profiles_basic() TO authenticated;

-- Nota: Esta função usa SECURITY DEFINER, o que significa que ela
-- é executada com os privilégios do usuário que a criou (geralmente um admin),
-- igno<PERSON>o as políticas de segurança RLS (Row Level Security).
