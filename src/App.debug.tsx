import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { ThemeProvider } from "next-themes";
import { BrowserRouter, Routes, Route } from "react-router-dom";

const AppDebug = () => {
  return (
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <div className="flex flex-col items-center justify-center min-h-screen p-4 bg-background">
            <h1 className="text-2xl font-bold mb-4">Página de Debug</h1>
            <p className="text-muted-foreground mb-4">
              Se esta página estiver carregando, o problema está na autenticação ou em componentes específicos.
            </p>
            <Routes>
              <Route path="*" element={
                <div className="p-4 border rounded-md">
                  <h2 className="text-xl font-semibold mb-2">Teste de Rota</h2>
                  <p>Rota atual funcionando corretamente</p>
                </div>
              } />
            </Routes>
          </div>
        </BrowserRouter>
      </TooltipProvider>
    </ThemeProvider>
  );
};

export default AppDebug; 