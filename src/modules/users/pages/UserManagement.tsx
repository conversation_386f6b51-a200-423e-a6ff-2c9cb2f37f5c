import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { UserPlus, Pencil, UserCheck, Users, AlertCircle, RefreshCw } from "lucide-react";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/lib/supabase";
import { toast } from "sonner";
import { useAuth } from "@/contexts/AuthContext";

// Novos tipos de papel conforme a estrutura solicitada
type UserRole = 
  | "admin"  // Administrador (ADC)
  | "gerente_geral"  // Gerente Geral de Precatórios e RPVs
  | "gerente_precatorio"  // Gerente de Precatório
  | "gerente_rpv"  // Gerente de RPV
  | "captador"  // Captador
  | "operacional_precatorio"  // Funcionário Operacional de Precatório
  | "operacional_rpv"  // Funcionário Operacional de RPV
  | "operacional_completo";  // Funcionário Operacional Completo (Precatório + RPV)

interface UserProfile {
  id: string;
  email: string;
  nome?: string;
  role: UserRole;
  foto_url?: string;
  status?: string;
  created_at?: string;
}

export default function UserManagement() {
  const [users, setUsers] = useState<UserProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [isAddUserOpen, setIsAddUserOpen] = useState(false);
  const [isEditRoleOpen, setIsEditRoleOpen] = useState(false);
  const [currentUser, setCurrentUser] = useState<UserProfile | null>(null);
  const [selectedUser, setSelectedUser] = useState<UserProfile | null>(null);
  const [newRole, setNewRole] = useState<UserRole>("captador");
  const [newUser, setNewUser] = useState<{
    nome: string;
    email: string;
    password: string;
    role: UserRole;
  }>({
    nome: "",
    email: "",
    password: "",
    role: "captador",
  });
  const [addingUser, setAddingUser] = useState(false);
  const navigate = useNavigate();
  const { getAllUsers, updateUserPermissions, createUser } = useAuth();

  // Verificar permissões do usuário atual
  useEffect(() => {
    try {
      const userProfileStr = localStorage.getItem('userProfile');
      if (userProfileStr) {
        const userProfile = JSON.parse(userProfileStr);
        setCurrentUser(userProfile);
        
        // Verificar se é administrador
        if (userProfile.role !== 'admin') {
          toast.error("Acesso negado", {
            description: "Você não tem permissão para acessar esta página."
          });
          navigate("/dashboard");
        } else {
          // Carregar lista de usuários se for admin
          console.log("UserManagement: Usuário é admin, carregando usuários...");
          loadUsers();
        }
      } else {
        navigate("/login");
      }
    } catch (error) {
      console.error("Erro ao verificar permissões:", error);
      navigate("/dashboard");
    }
  }, [navigate]);
  
  // Efeito para carregar usuários ao montar o componente
  useEffect(() => {
    // Limpar o cache para garantir que as alterações sejam aplicadas
    localStorage.removeItem('cached_users');
    
    loadUsers();
  }, []);

  // Adicionar um efeito adicional para tentar novamente após 2 segundos
  useEffect(() => {
    // Se não houver usuários mas o usuário atual é admin, tentar carregar novamente
    if (users.length === 0 && currentUser?.role === 'admin' && !loading) {
      console.log("UserManagement: Tentando carregar usuários novamente após delay...");
      const timer = setTimeout(() => {
        loadUsers();
      }, 2000);
      
      return () => clearTimeout(timer);
    }
  }, [users.length, currentUser, loading]);
  
  // Carregar lista de usuários de forma segura (evitando recursão infinita)
  const loadUsers = async () => {
    console.log("UserManagement: Iniciando carregamento de usuários...");
    try {
      setLoading(true);
      
      // Verificar se há usuários em cache
      const cachedUsersStr = localStorage.getItem('cached_users');
      if (cachedUsersStr) {
        try {
          const cachedUsers = JSON.parse(cachedUsersStr);
          console.log("UserManagement: Usando", cachedUsers.length, "usuários em cache");
          
          // Verificar se os IDs dos usuários em cache são válidos
          const validatedUsers = cachedUsers.map(user => {
            // Verificar se o ID é um UUID válido
            const isValidUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(user.id);
            
            // Se não for válido e for um número ou string simples, converter para formato UUID
            if (!isValidUUID) {
              console.log("UserManagement: Convertendo ID inválido:", user.id);
              // Criar um UUID baseado no ID original
              const mockUUID = `00000000-0000-0000-0000-${user.id.toString().padStart(12, '0')}`;
              return { ...user, id: mockUUID };
            }
            
            return user;
          });
          
          if (validatedUsers.length > 0) {
            // Atualizar o cache com os IDs corrigidos
            localStorage.setItem('cached_users', JSON.stringify(validatedUsers));
            setUsers(validatedUsers);
            setLoading(false);
            return;
          }
        } catch (e) {
          console.error("UserManagement: Erro ao analisar usuários em cache:", e);
        }
      }
      
      // Tentar buscar usuários do AuthContext
      try {
        console.log("UserManagement: Chamando getAllUsers do AuthContext...");
        const { data: fetchedUsers, error } = await getAllUsers();
        
        if (error) {
          console.error("UserManagement: Erro ao carregar usuários:", error);
          throw new Error(typeof error === 'string' ? error : "Permissão negada");
        }
        
        if (!fetchedUsers || fetchedUsers.length === 0) {
          console.log("UserManagement: Nenhum usuário encontrado, usando dados mockados");
          throw new Error("Nenhum usuário encontrado");
        }
        
        console.log("UserManagement: Usuários carregados com sucesso:", fetchedUsers.length);
        
        // Salvar usuários no cache
        localStorage.setItem('cached_users', JSON.stringify(fetchedUsers));
        
        // Mapear os usuários para o formato esperado
        setUsers(fetchedUsers);
        return;
      } catch (error) {
        console.error("UserManagement: Erro ao carregar usuários do AuthContext:", error);
        
        // Criar usuários mockados como fallback
        console.log("UserManagement: Criando usuários mockados como fallback");
        
        // Incluir o usuário atual
        const mockUsers: UserProfile[] = [];
        
        if (currentUser) {
          // Verificar se o ID do usuário atual é um UUID válido
          const isValidUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(currentUser.id);
          
          if (isValidUUID) {
            mockUsers.push(currentUser);
          } else {
            // Converter para UUID válido
            const mockUUID = `00000000-0000-0000-0000-${currentUser.id.toString().padStart(12, '0')}`;
            mockUsers.push({
              ...currentUser,
              id: mockUUID
            });
          }
        }
        
        // Adicionar alguns usuários mockados para demonstração
        if (!mockUsers.some(u => u.role === 'admin')) {
          mockUsers.push({
            id: '00000000-0000-0000-0000-000000000001',
            email: '<EMAIL>',
            nome: 'Administrador',
            role: 'admin',
            status: 'ativo',
            created_at: new Date().toISOString()
          });
        }
        
        mockUsers.push({
          id: '00000000-0000-0000-0000-000000000002',
          email: '<EMAIL>',
          nome: 'Gerente',
          role: 'gerente_geral',
          status: 'ativo',
          created_at: new Date().toISOString()
        });
        
        mockUsers.push({
          id: '00000000-0000-0000-0000-000000000003',
          email: '<EMAIL>',
          nome: 'Operacional',
          role: 'operacional_completo',
          status: 'ativo',
          created_at: new Date().toISOString()
        });
        
        // Salvar no cache para uso futuro
        localStorage.setItem('cached_users', JSON.stringify(mockUsers));
        
        setUsers(mockUsers);
      }
    } catch (error: any) {
      console.error("UserManagement: Erro ao carregar usuários:", error);
      toast.error("Erro ao carregar usuários", {
        description: error instanceof Error ? error.message : "Ocorreu um erro desconhecido ao tentar carregar a lista de usuários."
      });
      
      // Usar array vazio em caso de erro
      setUsers([]);
    } finally {
      setLoading(false);
    }
  };

  // Adicionar novo usuário
  const handleAddUser = async () => {
    // Validar campos obrigatórios
    if (!newUser.nome || !newUser.email || !newUser.password || !newUser.role) {
      toast.error("Campos incompletos", {
        description: "Preencha todos os campos obrigatórios."
      });
      return;
    }
    
    // Validar formato de e-mail
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(newUser.email)) {
      toast.error("E-mail inválido", {
        description: "O e-mail informado não tem um formato válido."
      });
      return;
    }
    
    // Validar senha
    if (newUser.password.length < 6) {
      toast.error("Senha muito curta", {
        description: "A senha deve ter pelo menos 6 caracteres."
      });
      return;
    }
    
    try {
      setAddingUser(true);
      
      // Utilizar a função createUser do AuthContext
      const { error, user: createdUser } = await createUser(
        newUser.email,
        newUser.password,
        {
          nome: newUser.nome,
          role: newUser.role,
          status: "ativo"
        }
      );
      
      if (error) {
        console.error("Erro ao criar usuário:", error);
        toast.error("Erro ao criar usuário", {
          description: error
        });
        return;
      }
      
      if (!createdUser) {
        toast.error("Erro ao criar usuário", {
          description: "Não foi possível obter os dados do usuário criado."
        });
        return;
      }
      
      toast.success("Usuário criado com sucesso!", {
        description: `${newUser.nome} foi adicionado como ${roleDisplayName(newUser.role)}.`
      });
      
      // Resetar formulário
      setNewUser({
        nome: "",
        email: "",
        password: "",
        role: "captador",
      });
      
      // Fechar modal e atualizar lista
      setIsAddUserOpen(false);
      
      // Adicionar o novo usuário à lista local para evitar ter que recarregar
      const novoUsuario: UserProfile = {
        id: createdUser.id,
        email: createdUser.email,
        nome: createdUser.nome,
        role: createdUser.role,
        status: "ativo",
        created_at: new Date().toISOString()
      };
      
      setUsers(prevUsers => [...prevUsers, novoUsuario]);
      
    } catch (error: any) {
      console.error("Erro ao adicionar usuário:", error);
      toast.error("Erro ao adicionar usuário", {
        description: error instanceof Error ? error.message : "Ocorreu um erro desconhecido"
      });
    } finally {
      setAddingUser(false);
    }
  };

  // Atualizar o papel do usuário
  const handleUpdateRole = async () => {
    if (!selectedUser) return;
    
    try {
      console.log("Atualizando cargo do usuário:", selectedUser);
      
      // Se o cargo não mudou, não fazer nada
      if (selectedUser.role === newRole) {
        toast.info("Nenhuma alteração necessária", {
          description: `${selectedUser.nome || selectedUser.email} já possui o cargo ${roleDisplayName(newRole)}.`
        });
        setIsEditRoleOpen(false);
        return;
      }
      
      // Verificar se o ID é válido
      const isValidUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(selectedUser.id);
      
      if (!isValidUUID) {
        console.error("ID de usuário inválido:", selectedUser.id);
        toast.error("Erro ao atualizar cargo", {
          description: "ID de usuário inválido. Não é possível atualizar no banco de dados."
        });
        return;
      }

      // Usar a função do contexto de autenticação
      const { error, success } = await updateUserPermissions(selectedUser.id, newRole);
      
      if (error) {
        console.error("Erro ao atualizar cargo:", error);
        toast.error("Erro ao atualizar cargo", {
          description: error
        });
        
        // Atualizar apenas localmente se houver erro no backend
        toast.warning("Atualização parcial", {
          description: "Falha ao salvar no banco de dados, mas o cargo foi atualizado localmente."
        });
      } else {
        console.log("Cargo atualizado com sucesso no banco de dados!");
        toast.success("Cargo atualizado com sucesso!", {
          description: `${selectedUser.nome || selectedUser.email} agora é ${roleDisplayName(newRole)}.`
        });
      }
      
      // Atualizar o usuário na lista local independentemente do resultado do backend
      // Isso garante que a UI permaneça consistente
      setUsers(prevUsers => {
        return prevUsers.map(user => 
          user.id === selectedUser.id 
            ? { ...user, role: newRole } 
            : user
        );
      });
      
      // Fechar modal e recarregar usuários
      setIsEditRoleOpen(false);
      
    } catch (error: any) {
      console.error("Erro ao atualizar cargo:", error);
      toast.error("Erro ao atualizar cargo", {
        description: error instanceof Error ? error.message : "Ocorreu um erro desconhecido"
      });
    }
  };

  // Abrir o modal de edição de papel
  const handleOpenEditRole = (user: UserProfile) => {
    // Verificar se o ID do usuário é um UUID válido
    const isValidUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(user.id);
    
    // Se não for válido, converter para UUID válido
    if (!isValidUUID) {
      console.log("Convertendo ID inválido para edição:", user.id);
      const mockUUID = `00000000-0000-0000-0000-${user.id.toString().padStart(12, '0')}`;
      user = { ...user, id: mockUUID };
      
      // Atualizar o usuário na lista
      setUsers(prevUsers => 
        prevUsers.map(u => 
          u.id === user.id 
            ? user 
            : u
        )
      );
    }
    
    setSelectedUser(user);
    setNewRole(user.role);
    setIsEditRoleOpen(true);
  };

  // Tradução para exibição amigável
  const roleTranslation: Record<UserRole, string> = {
    admin: "Administrador (ADC)",
    gerente_geral: "Gerente Geral",
    gerente_precatorio: "Gerente de Precatório",
    gerente_rpv: "Gerente de RPV",
    captador: "Captador",
    operacional_precatorio: "Operacional - Precatório",
    operacional_rpv: "Operacional - RPV",
    operacional_completo: "Operacional - Completo"
  };

  // Cores para os badges de papel
  const roleBadgeColors: Record<UserRole, string> = {
    admin: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",
    gerente_geral: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
    gerente_precatorio: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
    gerente_rpv: "bg-teal-100 text-teal-800 dark:bg-teal-900 dark:text-teal-200",
    captador: "bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200",
    operacional_precatorio: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
    operacional_rpv: "bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200",
    operacional_completo: "bg-lime-100 text-lime-800 dark:bg-lime-900 dark:text-lime-200"
  };

  // Função para exibir nome amigável do papel
  const roleDisplayName = (role: UserRole) => {
    const displayNames = {
      admin: "Administrador (ADC)",
      gerente_geral: "Gerente Geral",
      gerente_precatorio: "Gerente de Precatório",
      gerente_rpv: "Gerente de RPV",
      captador: "Captador",
      operacional_precatorio: "Operacional - Precatório",
      operacional_rpv: "Operacional - RPV",
      operacional_completo: "Operacional - Completo"
    };
    return displayNames[role] || role;
  };

  // Função para atualizar a lista manualmente
  const refreshUserList = () => {
    console.log("UserManagement: Atualizando lista de usuários manualmente...");
    setLoading(true);
    loadUsers();
  };

  // Verificar se não é admin
  if (currentUser?.role !== 'admin') {
    return (
      <div className="p-6">
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Acesso negado</AlertTitle>
          <AlertDescription>
            Você não tem permissão para acessar esta página.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-3xl font-bold">Gerenciar Usuários</h2>
        
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={refreshUserList}
            className="flex items-center gap-1"
            disabled={loading}
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-current"></div>
                <span>Carregando...</span>
              </>
            ) : (
              <>
                <RefreshCw className="h-4 w-4" />
                <span>Atualizar Lista</span>
              </>
            )}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsAddUserOpen(true)}
          >
            <UserPlus className="h-4 w-4 mr-2" />
            Adicionar Usuário
          </Button>
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center my-12">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      ) : (
        <>
          <Card>
            <CardHeader>
              <CardTitle>Lista de Usuários {users.length > 0 ? `(${users.length})` : ''}</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Nome</TableHead>
                    <TableHead>E-mail</TableHead>
                    <TableHead>Cargo</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Ações</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {users.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-6">
                        <div className="flex flex-col items-center justify-center space-y-3">
                          <Users className="h-10 w-10 text-gray-400" />
                          <p>Nenhum usuário encontrado</p>
                          <Button size="sm" variant="outline" onClick={refreshUserList}>
                            Tentar novamente
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    users.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell className="font-medium">
                          <div className="flex items-center gap-3">
                            <Avatar>
                              <AvatarImage src={user.foto_url || ""} />
                              <AvatarFallback>
                                {user.nome ? user.nome.slice(0, 2).toUpperCase() : "U"}
                              </AvatarFallback>
                            </Avatar>
                            <div>{user.nome || "Usuário sem nome"}</div>
                          </div>
                        </TableCell>
                        <TableCell>{user.email}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              user.role === 'admin' 
                                ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300' 
                                : user.role === 'gerente_geral' 
                                  ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'
                                  : user.role === 'operacional_completo'
                                    ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                                    : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
                            }`}>
                              {roleDisplayName(user.role)}
                            </span>
                            <span className="text-xs text-muted-foreground hidden sm:inline">
                              (ID: {user.id.substring(0, 8)}...)
                            </span>
                          </div>
                        </TableCell>
                        <TableCell className="text-center">
                          <Badge 
                            variant={
                              user.status === "ativo" ? "secondary" : 
                              user.status === "inativo" ? "outline" : 
                              "destructive"
                            }
                          >
                            {user.status || "Desconhecido"}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleOpenEditRole(user)}
                            >
                              Editar Cargo
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => navigate(`/usuario/${user.id}`)}
                            >
                              Ver Perfil
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </>
      )}

      {/* Modal de adicionar usuário */}
      <Dialog open={isAddUserOpen} onOpenChange={setIsAddUserOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Adicionar Novo Usuário</DialogTitle>
            <DialogDescription>
              Preencha os dados para criar um novo usuário no sistema.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="nome" className="text-right">
                Nome
              </Label>
              <Input
                id="nome"
                value={newUser.nome}
                onChange={(e) => setNewUser({ ...newUser, nome: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="email" className="text-right">
                E-mail
              </Label>
              <Input
                id="email"
                type="email"
                value={newUser.email}
                onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="password" className="text-right">
                Senha
              </Label>
              <Input
                id="password"
                type="password"
                value={newUser.password}
                onChange={(e) => setNewUser({ ...newUser, password: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="role" className="text-right">
                Cargo
              </Label>
              <Select value={newUser.role} onValueChange={(value: UserRole) => setNewUser({ ...newUser, role: value })}>
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Selecione o cargo" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="admin">Administrador (ADC)</SelectItem>
                  <SelectItem value="gerente_geral">Gerente Geral</SelectItem>
                  <SelectItem value="gerente_precatorio">Gerente de Precatório</SelectItem>
                  <SelectItem value="gerente_rpv">Gerente de RPV</SelectItem>
                  <SelectItem value="captador">Captador</SelectItem>
                  <SelectItem value="operacional_precatorio">Operacional - Precatório</SelectItem>
                  <SelectItem value="operacional_rpv">Operacional - RPV</SelectItem>
                  <SelectItem value="operacional_completo">Operacional - Completo</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter className="mt-6">
            <Button variant="outline" onClick={() => setIsAddUserOpen(false)}>Cancelar</Button>
            <Button onClick={handleAddUser} disabled={addingUser}>
              {addingUser ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 mr-2 border-t-2 border-b-2 border-white"></div>
                  Adicionando...
                </>
              ) : "Adicionar Usuário"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Modal de editar papel */}
      <Dialog open={isEditRoleOpen} onOpenChange={setIsEditRoleOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Alterar Cargo</DialogTitle>
            <DialogDescription>
              Selecione o novo cargo para {selectedUser?.nome || selectedUser?.email}.
              <div className="mt-2 text-sm">
                <span className="font-medium">Cargo atual: </span>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  selectedUser?.role === 'admin' 
                    ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300' 
                    : selectedUser?.role === 'gerente_geral' 
                      ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'
                      : selectedUser?.role === 'operacional_completo'
                        ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                        : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
                }`}>
                  {selectedUser ? roleDisplayName(selectedUser.role) : ''}
                </span>
              </div>
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="role" className="text-right">
                Novo Cargo
              </Label>
              <Select 
                value={newRole} 
                onValueChange={(value) => {
                  console.log("Selecionando novo cargo:", value);
                  setNewRole(value as UserRole);
                }}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Selecione um cargo" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="admin">Administrador</SelectItem>
                  <SelectItem value="gerente_geral">Gerente Geral</SelectItem>
                  <SelectItem value="gerente_precatorio">Gerente de Precatório</SelectItem>
                  <SelectItem value="gerente_rpv">Gerente de RPV</SelectItem>
                  <SelectItem value="captador">Captador</SelectItem>
                  <SelectItem value="operacional_precatorio">Operacional - Precatório</SelectItem>
                  <SelectItem value="operacional_rpv">Operacional - RPV</SelectItem>
                  <SelectItem value="operacional_completo">Operacional - Completo</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter className="mt-6">
            <Button variant="outline" onClick={() => setIsEditRoleOpen(false)}>Cancelar</Button>
            <Button onClick={handleUpdateRole}>Salvar Alterações</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}