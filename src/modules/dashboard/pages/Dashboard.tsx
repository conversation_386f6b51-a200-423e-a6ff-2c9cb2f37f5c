"use client";

import { useState } from "react";
import {
  Activity,
  ArrowDownRight,
  ArrowUpRight,
  Banknote,
  Calendar,
  CheckCircle2,
  CircleDollarSign,
  Clock,
  FileText,
  History,
  LayoutDashboard,
  Loader2,
  MoreHorizontal,
  User,
  Users,
  AlertTriangle,
  Target,
  TrendingUp,
  Download,
  Filter,
  ChevronRight,
  BarChart as BarChartIcon,
  PieChart as PieChartIcon,
  LineChart as LineChartIcon,
  DollarSign,
  Building2,
  Scale,
  CalendarRange,
  Timer,
  TrendingDown,
  CheckSquare,
  Bell,
  Info,
  Plus,
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useNavigate } from "react-router-dom";
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  BarChart,
  Bar,
  Legend
} from "recharts";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

// Dados mockados - poderia ser movido para um arquivo separado
import { 
  estatisticasGerais, historicoValores, distribuicaoStatus, precatoriosRecentes,
  tarefasUrgentes, distribuicaoTipo, desempenhoMensal, tempoMedioPorEtapa,
  metasDesempenho, notificacoes, desempenhoEquipe, projecoes
} from "./dashboard/dashboardData";

// Formatadores
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(value);
};

const formatCompactCurrency = (value: number) => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
    notation: 'compact',
    maximumFractionDigits: 1
  }).format(value);
};

// Componentes
const DashboardHeader = () => {
  return (
    <div className="flex flex-col md:flex-row gap-4 items-start justify-between">
      <div>
        <h1 className="text-3xl font-bold">Dashboard</h1>
        <p className="text-muted-foreground">
          Visão geral dos precatórios e tarefas do sistema
        </p>
        <div className="flex items-center gap-3 mt-2">
          <Badge variant="outline" className="flex items-center gap-1 px-3 py-1">
            <Activity className="h-3 w-3" />
            <span>Status: Operacional</span>
          </Badge>
          <Badge variant="secondary" className="flex items-center gap-1 px-3 py-1">
            <CheckCircle2 className="h-3 w-3" />
            <span>Última atualização: {new Date().toLocaleDateString('pt-BR')}</span>
          </Badge>
        </div>
      </div>
      <DashboardActions />
    </div>
  );
};

const DashboardActions = () => {
  return (
    <div className="flex flex-col sm:flex-row items-center gap-3">
      <NotificacoesDropdown />
      <div className="flex items-center gap-2">
        <Button variant="outline" className="gap-2">
          <Filter size={16} />
          Filtros
        </Button>
        <Button variant="outline" className="gap-2">
          <Download size={16} />
          Exportar
        </Button>
      </div>
    </div>
  );
};

const NotificacoesDropdown = () => {
  const naoLidas = notificacoes.filter(n => !n.lida).length;
  
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className="gap-2">
          <Bell className="h-4 w-4" />
          Notificações
          {naoLidas > 0 && (
            <Badge variant="destructive" className="h-5 w-5 flex items-center justify-center p-0">
              {naoLidas}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-72">
        <DropdownMenuLabel>Notificações Recentes</DropdownMenuLabel>
        {notificacoes.map((notificacao) => (
          <DropdownMenuItem key={notificacao.id} className={`${!notificacao.lida ? 'font-medium' : ''}`}>
            <div className="flex gap-2 items-start w-full">
              {notificacao.tipo === 'alerta' && <AlertTriangle className="h-4 w-4 text-yellow-500 mt-0.5" />}
              {notificacao.tipo === 'info' && <Info className="h-4 w-4 text-blue-500 mt-0.5" />}
              {notificacao.tipo === 'sucesso' && <CheckCircle2 className="h-4 w-4 text-green-500 mt-0.5" />}
              <div className="flex-1">
                <p className="text-sm">{notificacao.mensagem}</p>
                <p className="text-xs text-muted-foreground">{notificacao.data.toLocaleTimeString('pt-BR')}</p>
              </div>
            </div>
          </DropdownMenuItem>
        ))}
        <DropdownMenuSeparator />
        <DropdownMenuItem className="justify-center">
          Ver todas as notificações
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

const PeriodoSelector = ({ periodoAtivo, setPeriodoAtivo }: { periodoAtivo: string, setPeriodoAtivo: (periodo: string) => void }) => {
  return (
    <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
      <h2 className="text-lg font-semibold">Período de análise</h2>
      <div className="bg-muted rounded-lg p-1 inline-flex">
        <Button 
          variant={periodoAtivo === "semana" ? "default" : "ghost"} 
          size="sm"
          onClick={() => setPeriodoAtivo("semana")}
          className="rounded-lg"
        >
          Semana
        </Button>
        <Button 
          variant={periodoAtivo === "mes" ? "default" : "ghost"} 
          size="sm"
          onClick={() => setPeriodoAtivo("mes")}
          className="rounded-lg"
        >
          Mês
        </Button>
        <Button 
          variant={periodoAtivo === "trimestre" ? "default" : "ghost"} 
          size="sm"
          onClick={() => setPeriodoAtivo("trimestre")}
          className="rounded-lg"
        >
          Trimestre
        </Button>
        <Button 
          variant={periodoAtivo === "ano" ? "default" : "ghost"} 
          size="sm"
          onClick={() => setPeriodoAtivo("ano")}
          className="rounded-lg"
        >
          Ano
        </Button>
      </div>
    </div>
  );
};

const StatCard = ({ 
  title, value, trend, trendValue, subtitle, subtitleValue, progress, icon, color 
}: { 
  title: string, 
  value: string | number, 
  trend?: "up" | "down", 
  trendValue?: string | number,
  subtitle?: string, 
  subtitleValue?: number, 
  progress?: number,
  icon: React.ReactNode,
  color: string
}) => {
  return (
    <Card className="overflow-hidden border-l-4 transition-all duration-300 hover:shadow-md" style={{ borderLeftColor: color }}>
      <CardContent className="p-0">
        <div className="flex flex-row items-center justify-between p-6">
          <div className="flex flex-col gap-1">
            <p className="text-sm text-muted-foreground">{title}</p>
            <div className="flex items-center gap-2">
              <p className="text-2xl font-bold">{value}</p>
              {trend && trendValue && (
                <div className={`flex items-center gap-0.5 ${trend === "up" ? "text-green-500" : "text-red-500"}`}>
                  {trend === "up" ? <ArrowUpRight className="h-3 w-3" /> : <ArrowDownRight className="h-3 w-3" />}
                  <span className="text-xs font-medium">{trendValue}</span>
                </div>
              )}
            </div>
            {subtitle && subtitleValue !== undefined && progress !== undefined && (
              <div className="flex items-center gap-1">
                <span className="text-xs text-muted-foreground">
                  {subtitle} {subtitleValue}
                </span>
                <Progress value={progress} className="h-1 w-16" />
              </div>
            )}
          </div>
          <div className="rounded-full p-3" style={{ backgroundColor: `${color}20`, color: color }}>
            {icon}
          </div>
        </div>
        <div className="bg-muted/50 px-6 py-3">
          <Button variant="ghost" className="h-auto p-0 text-xs text-muted-foreground hover:text-foreground">
            Ver detalhes
            <ChevronRight className="ml-1 h-3 w-3" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

const StatsCards = () => {
  return (
    <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
      <StatCard 
        title="Total de Precatórios"
        value={estatisticasGerais.total_precatorios}
        trend="up"
        trendValue={`+${estatisticasGerais.novos_mes}`}
        subtitle={`${estatisticasGerais.precatorios_ativos} ativos`}
        subtitleValue={estatisticasGerais.precatorios_ativos}
        progress={(estatisticasGerais.precatorios_ativos / estatisticasGerais.total_precatorios) * 100}
        icon={<Banknote className="w-5 h-5" />}
        color="#3b82f6"
      />
      
      <StatCard 
        title="Valor Total"
        value={formatCompactCurrency(estatisticasGerais.valor_total)}
        trend="up"
        trendValue={`${estatisticasGerais.crescimento_mensal}%`}
        subtitle={`${formatCompactCurrency(estatisticasGerais.valor_pago)} pagos`}
        subtitleValue={estatisticasGerais.valor_pago}
        progress={(estatisticasGerais.valor_pago / estatisticasGerais.valor_total) * 100}
        icon={<CircleDollarSign className="w-5 h-5" />}
        color="#22c55e"
      />
      
      <StatCard 
        title="Tarefas"
        value={estatisticasGerais.total_tarefas}
        subtitle={`${estatisticasGerais.tarefas_concluidas} concluídas`}
        subtitleValue={estatisticasGerais.tarefas_concluidas}
        progress={(estatisticasGerais.tarefas_concluidas / estatisticasGerais.total_tarefas) * 100}
        icon={<CheckSquare className="w-5 h-5" />}
        color="#8b5cf6"
      />
      
      <StatCard 
        title="Taxa de Sucesso"
        value={`${estatisticasGerais.taxa_sucesso}%`}
        trend="up"
        trendValue={`${estatisticasGerais.crescimento_mensal}%`}
        subtitle={`Média de ${estatisticasGerais.media_prazo} dias`}
        subtitleValue={estatisticasGerais.taxa_sucesso}
        progress={estatisticasGerais.taxa_sucesso}
        icon={<Target className="w-5 h-5" />}
        color="#f59e0b"
      />
    </div>
  );
};

const ProjectionCard = ({ title, value, trend, trendValue, description, icon, iconColor }: {
  title: string,
  value: string | number,
  trend?: "up" | "down",
  trendValue?: string,
  description: string,
  icon: React.ReactNode,
  iconColor: string
}) => {
  return (
    <Card className="border border-dashed border-muted-foreground/20">
      <CardContent className="pt-6">
        <div className="flex justify-between items-start">
          <div>
            <p className="text-sm text-muted-foreground">{title}</p>
            <div className="flex items-center mt-1">
              <p className="text-2xl font-bold">{value}%</p>
              {trend && trendValue && (
                <span className={`${trend === "up" ? "text-green-500" : "text-red-500"} ml-2 flex items-center text-xs`}>
                  {trend === "up" ? <ArrowUpRight className="h-3 w-3 mr-0.5" /> : <ArrowDownRight className="h-3 w-3 mr-0.5" />}
                  {trendValue}
                </span>
              )}
            </div>
          </div>
          <div className={`h-4 w-4 ${iconColor}`}>{icon}</div>
        </div>
        <p className="text-xs text-muted-foreground mt-2">{description}</p>
      </CardContent>
    </Card>
  );
};

const ProjectionsCards = () => {
  return (
    <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
      <ProjectionCard
        title="Crescimento Previsto"
        value={projecoes.crescimento_trimestral}
        trend="up"
        trendValue="+3.5%"
        description="Projeção para o próximo trimestre"
        icon={<TrendingUp />}
        iconColor="text-green-500"
      />
      <ProjectionCard
        title="Economia Processual"
        value={projecoes.economia_processamento}
        trend="up"
        trendValue="+2.8%"
        description="Redução nos custos processuais"
        icon={<Scale />}
        iconColor="text-blue-500"
      />
      <ProjectionCard
        title="Redução no Tempo"
        value={projecoes.reducao_tempo_medio}
        trend="down"
        trendValue="-4.2%"
        description="Redução no tempo médio de conclusão"
        icon={<CalendarRange />}
        iconColor="text-violet-500"
      />
      <ProjectionCard
        title="Aumento de Eficiência"
        value={projecoes.aumento_eficiencia}
        trend="up"
        trendValue="+1.8%"
        description="Aumento na eficiência operacional"
        icon={<Timer />}
        iconColor="text-amber-500"
      />
    </div>
  );
};

const ValoresChart = () => {
  const [chartType, setChartType] = useState<"area" | "line" | "bar">("area");
  
  return (
    <Card className="transition-all duration-300 hover:shadow-md">
      <CardHeader className="pb-0">
        <div className="flex justify-between items-center">
          <CardTitle className="text-base font-medium">Evolução de Valores</CardTitle>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              Últimos 6 meses
            </Badge>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="h-8">
                  <BarChartIcon className="h-4 w-4 mr-2" />
                  Visualização
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => setChartType("area")}>
                  <LineChartIcon className="h-4 w-4 mr-2" />
                  Área
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setChartType("line")}>
                  <LineChartIcon className="h-4 w-4 mr-2" />
                  Linha
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setChartType("bar")}>
                  <BarChartIcon className="h-4 w-4 mr-2" />
                  Barras
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-[300px]">
          {chartType === "area" && (
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={historicoValores}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="mes" />
                <YAxis
                  tickFormatter={(value) =>
                    formatCompactCurrency(value as number)
                  }
                />
                <Tooltip
                  formatter={(value: number) => [formatCurrency(value), ""]}
                />
                <Area
                  type="monotone"
                  dataKey="valor"
                  name="Valor Total"
                  stroke="#3b82f6"
                  fill="#3b82f6"
                  fillOpacity={0.1}
                />
                <Area
                  type="monotone"
                  dataKey="pagamentos"
                  name="Pagamentos"
                  stroke="#22c55e"
                  fill="#22c55e"
                  fillOpacity={0.1}
                />
              </AreaChart>
            </ResponsiveContainer>
          )}
          {chartType === "line" && (
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={historicoValores}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="mes" />
                <YAxis
                  tickFormatter={(value) =>
                    formatCompactCurrency(value as number)
                  }
                />
                <Tooltip
                  formatter={(value: number) => [formatCurrency(value), ""]}
                />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="valor"
                  name="Valor Total"
                  stroke="#3b82f6"
                  strokeWidth={2}
                  dot={{ r: 4 }}
                  activeDot={{ r: 6 }}
                />
                <Line
                  type="monotone"
                  dataKey="pagamentos"
                  name="Pagamentos"
                  stroke="#22c55e"
                  strokeWidth={2}
                  dot={{ r: 4 }}
                  activeDot={{ r: 6 }}
                />
              </LineChart>
            </ResponsiveContainer>
          )}
          {chartType === "bar" && (
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={historicoValores}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="mes" />
                <YAxis
                  tickFormatter={(value) =>
                    formatCompactCurrency(value as number)
                  }
                />
                <Tooltip
                  formatter={(value: number) => [formatCurrency(value), ""]}
                />
                <Legend />
                <Bar dataKey="valor" name="Valor Total" fill="#3b82f6" />
                <Bar dataKey="pagamentos" name="Pagamentos" fill="#22c55e" />
              </BarChart>
            </ResponsiveContainer>
          )}
        </div>
        <div className="flex flex-col md:flex-row justify-between items-center mt-4 text-sm">
          <div className="flex items-center gap-2 p-2 rounded-md bg-blue-50 dark:bg-blue-950/30">
            <CircleDollarSign className="h-4 w-4 text-blue-500" />
            <div>
              <p className="text-muted-foreground">Valor acumulado:</p>
              <p className="font-medium">
                {formatCurrency(estatisticasGerais.valor_total)}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2 p-2 rounded-md bg-green-50 dark:bg-green-950/30">
            <CircleDollarSign className="h-4 w-4 text-green-500" />
            <div>
              <p className="text-muted-foreground">Pagamentos realizados:</p>
              <p className="font-medium">
                {formatCurrency(estatisticasGerais.valor_pago)}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2 p-2 rounded-md bg-amber-50 dark:bg-amber-950/30">
            <TrendingUp className="h-4 w-4 text-amber-500" />
            <div>
              <p className="text-muted-foreground">Crescimento:</p>
              <p className="font-medium text-green-500">+{estatisticasGerais.crescimento_mensal}%</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

const StatusDonutChart = () => {
  const [chartType, setChartType] = useState<"pie" | "donut" | "bar">("donut");
  
  return (
    <Card className="transition-all duration-300 hover:shadow-md">
      <CardHeader className="pb-0">
        <div className="flex justify-between items-center">
          <CardTitle className="text-base font-medium">Distribuição por Status</CardTitle>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="h-8">
                <BarChartIcon className="h-4 w-4 mr-2" />
                Visualizar como
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => setChartType("pie")}>
                <PieChartIcon className="h-4 w-4 mr-2" />
                Gráfico de Pizza
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setChartType("donut")}>
                <PieChartIcon className="h-4 w-4 mr-2" />
                Gráfico Donut
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setChartType("bar")}>
                <BarChartIcon className="h-4 w-4 mr-2" />
                Gráfico de Barras
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-[300px]">
          {chartType === "pie" && (
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={distribuicaoStatus}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={80}
                  paddingAngle={5}
                  dataKey="value"
                  label={({ name, value }) => `${name}: ${value}`}
                >
                  {distribuicaoStatus.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip
                  formatter={(value: number) => [value, "Precatórios"]}
                  labelFormatter={(index: number) => distribuicaoStatus[index]?.name}
                />
              </PieChart>
            </ResponsiveContainer>
          )}
          {chartType === "donut" && (
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={distribuicaoStatus}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={80}
                  paddingAngle={5}
                  dataKey="value"
                  label={({ name, value }) => `${name}: ${value}`}
                >
                  {distribuicaoStatus.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip
                  formatter={(value: number) => [value, "Precatórios"]}
                  labelFormatter={(index: number) => distribuicaoStatus[index]?.name}
                />
              </PieChart>
            </ResponsiveContainer>
          )}
          {chartType === "bar" && (
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={distribuicaoStatus} layout="vertical">
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" />
                <YAxis dataKey="name" type="category" width={150} />
                <Tooltip formatter={(value: number) => [value, "Precatórios"]} />
                <Bar dataKey="value">
                  {distribuicaoStatus.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          )}
          <div className="grid grid-cols-2 sm:flex sm:justify-center gap-4 mt-4">
            {distribuicaoStatus.map((status, index) => (
              <div key={index} className="flex items-center gap-2 p-1 px-2 rounded-md" style={{ backgroundColor: `${status.color}10` }}>
                <div
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: status.color }}
                />
                <span className="text-sm">
                  {status.name} <span className="font-medium">({status.value})</span>
                </span>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

const EquipePerformanceCard = () => {
  return (
    <Card className="mt-4">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-base font-medium">Desempenho da Equipe</CardTitle>
          <Button variant="outline" size="sm">Ver Equipe Completa</Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {desempenhoEquipe.map((membro, index) => (
            <div 
              key={index}
              className="flex flex-col rounded-lg border p-4 transition-all hover:shadow-md"
            >
              <div className="flex items-center gap-3 mb-3">
                <Avatar className="h-10 w-10">
                  <AvatarImage src={membro.avatar} />
                  <AvatarFallback>
                    {membro.membro.split(" ").map((n) => n[0]).join("")}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <h3 className="font-medium">{membro.membro}</h3>
                  <p className="text-xs text-muted-foreground">{membro.cargo}</p>
                </div>
              </div>
              <div className="space-y-3 mt-1">
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-muted-foreground">Precatórios Processados</span>
                    <span className="font-medium">{membro.precatorios_processados}</span>
                  </div>
                  <Progress value={membro.precatorios_processados / 60 * 100} className="h-1.5" />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-muted-foreground">Taxa de Conclusão</span>
                    <span className="font-medium">{membro.taxa_conclusao}%</span>
                  </div>
                  <Progress value={membro.taxa_conclusao} className="h-1.5" />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-muted-foreground">Tempo Médio (dias)</span>
                    <span className="font-medium">{membro.tempo_medio}</span>
                  </div>
                  <Progress value={(20 - membro.tempo_medio) / 20 * 100} className="h-1.5" />
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

const PrecatoriosRecentes = ({ navigate }: { navigate: (path: string) => void }) => {
  return (
    <Card className="mt-4">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-base font-medium flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Precatórios Recentes
          </CardTitle>
          <Button variant="ghost" className="gap-2" onClick={() => navigate("/precatorios-management")}>
            Ver Todos
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {precatoriosRecentes.map((precatorio, index) => (
            <div
              key={index}
              className="flex items-center justify-between p-4 border rounded-lg hover:bg-accent/50 transition-colors cursor-pointer"
              onClick={() => navigate(`/precatorio/${precatorio.numero}`)}
            >
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <h3 className="font-medium">{precatorio.numero}</h3>
                  <Badge variant="outline">{precatorio.tipo}</Badge>
                  <Badge
                    variant={
                      precatorio.status === "Concluído"
                        ? "default"
                        : precatorio.status === "Em Processamento"
                        ? "secondary"
                        : "outline"
                    }
                  >
                    {precatorio.status}
                  </Badge>
                </div>
                <div className="flex items-center gap-4 mt-2 text-sm text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <User className="w-4 h-4" />
                    {precatorio.beneficiario}
                  </div>
                  <div className="flex items-center gap-1">
                    <Calendar className="w-4 h-4" />
                    {new Date(precatorio.data_entrada).toLocaleDateString('pt-BR')}
                  </div>
                  <div className="flex items-center gap-1">
                    <Building2 className="w-4 h-4" />
                    {precatorio.tribunal}
                  </div>
                </div>
              </div>
              <div className="text-right">
                <p className="font-medium">
                  {formatCurrency(precatorio.valor)}
                </p>
                <div className="flex items-center gap-2 mt-2">
                  <span className="text-sm text-muted-foreground">{precatorio.progresso}%</span>
                  <Progress value={precatorio.progresso} className="w-24 h-2" />
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

const DesempenhoMensalChart = () => {
  const [chartType, setChartType] = useState<"bar" | "line">("bar");
  
  return (
    <Card className="transition-all duration-300 hover:shadow-md">
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle className="text-base font-medium">Desempenho Mensal</CardTitle>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="h-8">
                <BarChartIcon className="h-4 w-4 mr-2" />
                Visualização
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => setChartType("bar")}>
                <BarChartIcon className="h-4 w-4 mr-2" />
                Barras
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setChartType("line")}>
                <LineChartIcon className="h-4 w-4 mr-2" />
                Linha
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-[300px]">
          {chartType === "bar" && (
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={desempenhoMensal}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="mes" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="concluidos" name="Concluídos" fill="#22c55e" />
                <Bar dataKey="novos" name="Novos" fill="#3b82f6" />
                <Bar dataKey="meta" name="Meta" fill="#8b5cf6" />
              </BarChart>
            </ResponsiveContainer>
          )}
          {chartType === "line" && (
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={desempenhoMensal}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="mes" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Line 
                  type="monotone" 
                  dataKey="concluidos" 
                  name="Concluídos" 
                  stroke="#22c55e" 
                  strokeWidth={2}
                  dot={{ r: 4 }}
                  activeDot={{ r: 6 }}
                />
                <Line 
                  type="monotone" 
                  dataKey="novos" 
                  name="Novos" 
                  stroke="#3b82f6" 
                  strokeWidth={2}
                  dot={{ r: 4 }}
                  activeDot={{ r: 6 }}
                />
                <Line 
                  type="monotone" 
                  dataKey="meta" 
                  name="Meta" 
                  stroke="#8b5cf6" 
                  strokeWidth={2}
                  strokeDasharray="5 5"
                  dot={{ r: 4 }}
                  activeDot={{ r: 6 }}
                />
              </LineChart>
            </ResponsiveContainer>
          )}
        </div>
        <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center gap-2 p-2 rounded-md bg-green-50 dark:bg-green-950/30">
            <CheckCircle2 className="h-4 w-4 text-green-500" />
            <div>
              <p className="text-xs text-muted-foreground">Concluídos (média):</p>
              <p className="font-medium">{desempenhoMensal.reduce((acc, item) => acc + item.concluidos, 0) / desempenhoMensal.length}</p>
            </div>
          </div>
          <div className="flex items-center gap-2 p-2 rounded-md bg-blue-50 dark:bg-blue-950/30">
            <Plus className="h-4 w-4 text-blue-500" />
            <div>
              <p className="text-xs text-muted-foreground">Novos (média):</p>
              <p className="font-medium">{desempenhoMensal.reduce((acc, item) => acc + item.novos, 0) / desempenhoMensal.length}</p>
            </div>
          </div>
          <div className="flex items-center gap-2 p-2 rounded-md bg-violet-50 dark:bg-violet-950/30">
            <Target className="h-4 w-4 text-violet-500" />
            <div>
              <p className="text-xs text-muted-foreground">Meta atingida:</p>
              <p className="font-medium">{desempenhoMensal.filter(item => item.concluidos >= item.meta).length} de {desempenhoMensal.length} meses</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

const TipoPieChart = () => {
  const [chartType, setChartType] = useState<"pie" | "donut" | "bar">("pie");
  
  return (
    <Card className="transition-all duration-300 hover:shadow-md">
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle className="text-base font-medium">Distribuição por Tipo</CardTitle>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="h-8">
                <BarChartIcon className="h-4 w-4 mr-2" />
                Visualização
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => setChartType("pie")}>
                <PieChartIcon className="h-4 w-4 mr-2" />
                Gráfico de Pizza
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setChartType("donut")}>
                <PieChartIcon className="h-4 w-4 mr-2" />
                Gráfico Donut
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setChartType("bar")}>
                <BarChartIcon className="h-4 w-4 mr-2" />
                Gráfico de Barras
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-[300px]">
          {chartType === "pie" && (
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={distribuicaoTipo}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={80}
                  paddingAngle={5}
                  dataKey="value"
                  label={({ name, value }) => `${name}: ${value}`}
                >
                  {distribuicaoTipo.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip
                  formatter={(value: number) => [value, "Precatórios"]}
                  labelFormatter={(index: number) => distribuicaoTipo[index]?.name}
                />
              </PieChart>
            </ResponsiveContainer>
          )}
          {chartType === "donut" && (
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={distribuicaoTipo}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={80}
                  paddingAngle={5}
                  dataKey="value"
                  label={({ name, value }) => `${name}: ${value}`}
                >
                  {distribuicaoTipo.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip
                  formatter={(value: number) => [value, "Precatórios"]}
                  labelFormatter={(index: number) => distribuicaoTipo[index]?.name}
                />
              </PieChart>
            </ResponsiveContainer>
          )}
          {chartType === "bar" && (
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={distribuicaoTipo} layout="vertical">
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" />
                <YAxis dataKey="name" type="category" width={150} />
                <Tooltip formatter={(value: number) => [value, "Precatórios"]} />
                <Bar dataKey="value">
                  {distribuicaoTipo.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          )}
          <div className="flex flex-wrap justify-center gap-4 mt-4">
            {distribuicaoTipo.map((tipo, index) => (
              <div key={index} className="flex items-center gap-2 p-1 px-3 rounded-md" style={{ backgroundColor: `${tipo.color}10` }}>
                <div
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: tipo.color }}
                />
                <div>
                  <span className="text-sm font-medium">{tipo.name}</span>
                  <p className="text-xs text-muted-foreground">{tipo.value} precatórios ({Math.round((tipo.value / distribuicaoTipo.reduce((acc, item) => acc + item.value, 0)) * 100)}%)</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

const MetasDesempenhoCard = () => {
  return (
    <Card className="lg:col-span-2">
      <CardHeader>
        <CardTitle className="text-base font-medium">Metas de Desempenho</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Tempo Médio de Conclusão</span>
              <Badge variant={metasDesempenho.tempo_medio_conclusao.variacao < 0 ? "default" : "destructive"}>
                {metasDesempenho.tempo_medio_conclusao.variacao}%
              </Badge>
            </div>
            <Progress 
              value={(metasDesempenho.tempo_medio_conclusao.atual / metasDesempenho.tempo_medio_conclusao.meta) * 100} 
              className="h-2"
            />
            <div className="flex items-center justify-between text-sm">
              <span>{metasDesempenho.tempo_medio_conclusao.atual} dias</span>
              <span className="text-muted-foreground">Meta: {metasDesempenho.tempo_medio_conclusao.meta} dias</span>
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Taxa de Conclusão</span>
              <Badge variant="default">
                +{metasDesempenho.taxa_conclusao.variacao}%
              </Badge>
            </div>
            <Progress 
              value={(metasDesempenho.taxa_conclusao.atual / metasDesempenho.taxa_conclusao.meta) * 100} 
              className="h-2"
            />
            <div className="flex items-center justify-between text-sm">
              <span>{metasDesempenho.taxa_conclusao.atual}%</span>
              <span className="text-muted-foreground">Meta: {metasDesempenho.taxa_conclusao.meta}%</span>
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Eficiência de Processamento</span>
              <Badge variant="default">
                +{metasDesempenho.eficiencia_processamento.variacao}%
              </Badge>
            </div>
            <Progress 
              value={(metasDesempenho.eficiencia_processamento.atual / metasDesempenho.eficiencia_processamento.meta) * 100} 
              className="h-2"
            />
            <div className="flex items-center justify-between text-sm">
              <span>{metasDesempenho.eficiencia_processamento.atual}%</span>
              <span className="text-muted-foreground">Meta: {metasDesempenho.eficiencia_processamento.meta}%</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

const TempoMedioEtapaChart = () => {
  const [chartType, setChartType] = useState<"bar" | "horizontal">("horizontal");
  
  return (
    <Card className="transition-all duration-300 hover:shadow-md">
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle className="text-base font-medium">Tempo Médio por Etapa</CardTitle>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="h-8">
                <BarChartIcon className="h-4 w-4 mr-2" />
                Visualização
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => setChartType("horizontal")}>
                <BarChartIcon className="h-4 w-4 mr-2" />
                Barras Horizontais
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setChartType("bar")}>
                <BarChartIcon className="h-4 w-4 mr-2" />
                Barras Verticais
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-[300px]">
          {chartType === "horizontal" && (
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={tempoMedioPorEtapa}
                layout="vertical"
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" label={{ value: 'Dias', position: 'insideBottom', offset: -5 }} />
                <YAxis dataKey="etapa" type="category" width={100} />
                <Tooltip labelFormatter={(label) => `Etapa: ${label}`} formatter={(value) => [`${value} dias`, "Tempo médio"]} />
                <Bar dataKey="tempo" fill="#3b82f6">
                  {tempoMedioPorEtapa.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.tempo > 30 ? "#ef4444" : entry.tempo > 20 ? "#f59e0b" : "#22c55e"} />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          )}
          {chartType === "bar" && (
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={tempoMedioPorEtapa}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="etapa" />
                <YAxis label={{ value: 'Dias', angle: -90, position: 'insideLeft' }} />
                <Tooltip labelFormatter={(label) => `Etapa: ${label}`} formatter={(value) => [`${value} dias`, "Tempo médio"]} />
                <Bar dataKey="tempo" fill="#3b82f6">
                  {tempoMedioPorEtapa.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.tempo > 30 ? "#ef4444" : entry.tempo > 20 ? "#f59e0b" : "#22c55e"} />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          )}
        </div>
        <div className="mt-4 flex justify-between p-3 bg-blue-50 dark:bg-blue-950/30 rounded-md">
          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4 text-blue-500" />
            <div>
              <p className="text-xs text-muted-foreground">Tempo total médio:</p>
              <p className="font-medium">{tempoMedioPorEtapa.reduce((acc, item) => acc + item.tempo, 0)} dias</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Activity className="h-4 w-4 text-blue-500" />
            <div>
              <p className="text-xs text-muted-foreground">Etapa mais longa:</p>
              <p className="font-medium">{tempoMedioPorEtapa.reduce((prev, current) => (prev.tempo > current.tempo) ? prev : current).etapa}</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

const AnalisesPrazosCard = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-base font-medium">Análise de Prazos</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Timer className="w-4 h-4 text-blue-500" />
                <span className="text-sm font-medium">Tempo Médio de Processamento</span>
              </div>
              <Badge variant="outline">180 dias</Badge>
            </div>
            <Progress value={75} className="h-2" />
            <p className="text-sm text-muted-foreground">75% dentro do prazo esperado</p>
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <CalendarRange className="w-4 h-4 text-green-500" />
                <span className="text-sm font-medium">Cumprimento de Prazos</span>
              </div>
              <Badge variant="outline">92%</Badge>
            </div>
            <Progress value={92} className="h-2" />
            <p className="text-sm text-muted-foreground">Meta: 95%</p>
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <TrendingDown className="w-4 h-4 text-red-500" />
                <span className="text-sm font-medium">Atrasos Críticos</span>
              </div>
              <Badge variant="destructive">8%</Badge>
            </div>
            <Progress value={8} className="h-2" />
            <p className="text-sm text-muted-foreground">12 precatórios com atraso</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Dashboard principal - agora com componentes reutilizáveis
export default function Dashboard() {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState("geral");
  const [periodoAtivo, setPeriodoAtivo] = useState("mes");

  return (
    <div className="flex flex-col gap-6 p-6 bg-[#f8f9fc] dark:bg-gray-950">
      <DashboardHeader />
      <PeriodoSelector periodoAtivo={periodoAtivo} setPeriodoAtivo={setPeriodoAtivo} />
      <StatsCards />
      <ProjectionsCards />

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4 mb-4">
          <TabsTrigger value="geral">Visão Geral</TabsTrigger>
          <TabsTrigger value="desempenho">Desempenho</TabsTrigger>
          <TabsTrigger value="financeiro">Financeiro</TabsTrigger>
          <TabsTrigger value="prazos">Prazos</TabsTrigger>
        </TabsList>

        <TabsContent value="geral" className="space-y-6">
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
            <ValoresChart />
            <StatusDonutChart />
          </div>
          <EquipePerformanceCard />
          <PrecatoriosRecentes navigate={navigate} />
        </TabsContent>

        <TabsContent value="desempenho" className="space-y-6">
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
            <DesempenhoMensalChart />
            <TipoPieChart />
            <MetasDesempenhoCard />
          </div>
        </TabsContent>

        <TabsContent value="financeiro" className="space-y-6">
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
            <ValoresChart />
            <Card className="transition-all duration-300 hover:shadow-md">
              <CardHeader>
                <CardTitle className="text-base font-medium">Indicadores Financeiros</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-6">
                  <div className="space-y-2 p-4 rounded-md bg-green-50 dark:bg-green-950/30">
                    <div className="flex items-center gap-2">
                      <DollarSign className="w-4 h-4 text-green-500" />
                      <span className="text-sm font-medium">Valor Médio</span>
                    </div>
                    <p className="text-2xl font-bold">
                      {formatCompactCurrency(estatisticasGerais.valor_total / estatisticasGerais.total_precatorios)}
                    </p>
                    <p className="text-sm text-muted-foreground">Por precatório</p>
                  </div>

                  <div className="space-y-2 p-4 rounded-md bg-blue-50 dark:bg-blue-950/30">
                    <div className="flex items-center gap-2">
                      <TrendingUp className="w-4 h-4 text-blue-500" />
                      <span className="text-sm font-medium">Taxa de Crescimento</span>
                    </div>
                    <p className="text-2xl font-bold">+15.8%</p>
                    <p className="text-sm text-muted-foreground">Últimos 6 meses</p>
                  </div>

                  <div className="space-y-2 p-4 rounded-md bg-purple-50 dark:bg-purple-950/30">
                    <div className="flex items-center gap-2">
                      <Scale className="w-4 h-4 text-purple-500" />
                      <span className="text-sm font-medium">Eficiência</span>
                    </div>
                    <p className="text-2xl font-bold">92%</p>
                    <p className="text-sm text-muted-foreground">Pagamentos no prazo</p>
                  </div>

                  <div className="space-y-2 p-4 rounded-md bg-amber-50 dark:bg-amber-950/30">
                    <div className="flex items-center gap-2">
                      <Building2 className="w-4 h-4 text-amber-500" />
                      <span className="text-sm font-medium">Entidades</span>
                    </div>
                    <p className="text-2xl font-bold">12</p>
                    <p className="text-sm text-muted-foreground">Devedoras ativas</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="prazos" className="space-y-6">
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
            <TempoMedioEtapaChart />
            <AnalisesPrazosCard />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}