import { useState, useEffect } from "react";
import { KanbanColuna } from "../types";
import { ColunaForm } from "./ColunaForm";
import { 
  buscarTodasColunas, 
  criarColuna, 
  atualizarColuna, 
  excluirColuna, 
  reordenarColunas 
} from "@/services/kanbanColunasService";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import { Loader2, Plus, Edit, Trash2, ArrowUp, ArrowDown } from "lucide-react";
import { Badge } from "@/components/ui/badge";

interface GerenciadorColunasProps {
  onColumnsChange?: () => void;
  tipoInicial?: 'TODOS' | 'PRECATORIO' | 'RPV' | 'AMBOS';
}

export function GerenciadorColunas({ 
  onColumnsChange,
  tipoInicial = 'TODOS'
}: GerenciadorColunasProps = {}) {
  const [colunas, setColunas] = useState<Omit<KanbanColuna, 'alerts'>[]>([]);
  const [statusOptions, setStatusOptions] = useState<{ id: string; nome: string }[]>([]);
  const [loading, setLoading] = useState(true);
  const [formOpen, setFormOpen] = useState(false);
  const [colunaParaEditar, setColunaParaEditar] = useState<Omit<KanbanColuna, 'alerts'> | undefined>(undefined);
  const [colunaParaExcluir, setColunaParaExcluir] = useState<string | null>(null);
  const [tipoFiltro, setTipoFiltro] = useState<'TODOS' | 'PRECATORIO' | 'RPV' | 'AMBOS'>(tipoInicial);

  // Carregar dados iniciais - com efeito de limpeza para evitar race conditions
  useEffect(() => {
    let isMounted = true;
    const carregarDados = async () => {
      try {
        setLoading(true);
        
        // Buscar todas as colunas
        const colunasData = await buscarTodasColunas();
        
        if (isMounted) {
          setColunas(colunasData.map(({ alerts, ...rest }) => rest));
        }
        
        // Definir opções de status padrão
        // Não dependemos mais da tabela status_precatorios
        const statusPadrao = [
          { id: 'analise', nome: 'Análise' },
          { id: 'proposta_tmj', nome: 'Proposta TMJ' },
          { id: 'proposta_btg', nome: 'Proposta BTG' },
          { id: 'negociacao', nome: 'Negociação' },
          { id: 'documentacao', nome: 'Documentação' },
          { id: 'pagamento', nome: 'Pagamento' },
          { id: 'concluido', nome: 'Concluído' },
          { id: 'cancelado', nome: 'Cancelado' }
        ];
        
        // Adicionar status das colunas existentes que não estão na lista padrão
        const statusExistentes = colunasData
          .filter(coluna => !statusPadrao.some(s => s.id === coluna.status_id))
          .map(coluna => ({ id: coluna.status_id, nome: coluna.name }));
        
        setStatusOptions([...statusPadrao, ...statusExistentes]);
      } catch (error) {
        console.error('Erro ao carregar dados:', error);
        if (isMounted) {
          toast.error('Erro ao carregar dados das colunas');
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };
    
    // Executa imediatamente o carregamento de dados
    carregarDados();
    
    // Função de limpeza para evitar atualizações de estado em componentes desmontados
    return () => {
      isMounted = false;
    };
  }, [tipoFiltro]); // Adicionar tipoFiltro como dependência para recarregar quando mudar

  // Filtrar colunas por tipo
  const colunasFiltradas = tipoFiltro === 'TODOS' 
    ? colunas 
    : colunas.filter(coluna => coluna.tipo === tipoFiltro || (tipoFiltro === 'AMBOS' && coluna.tipo === 'AMBOS'));

  // Salvar coluna (criar ou atualizar)
  const handleSaveColuna = async (coluna: Omit<KanbanColuna, 'alerts'>) => {
    try {
      setLoading(true);
      console.log('Iniciando salvamento da coluna com dados:', coluna);
      
      // Verificar se dados estão completos e válidos
      if (!coluna.status_id || !coluna.name) {
        console.error('Dados incompletos para salvar coluna:', { status_id: coluna.status_id, name: coluna.name });
        toast.error('Dados incompletos para salvar coluna. Verifique o identificador e o nome.');
        return;
      }
      
      // Normalizar o status_id - remover espaços e transformar em minúsculas
      // para maior compatiblidade com o banco
      const statusIdNormalizado = coluna.status_id.trim().toLowerCase();
      
      let colunaSalva;
      
      if (colunaParaEditar) {
        // Atualizar coluna existente
        const colunaAtualizada = {
          ...coluna,
          status_id: statusIdNormalizado, // Usar o ID normalizado
          id: colunaParaEditar.id // Manter o ID original da coluna
        };
        
        colunaSalva = await atualizarColuna(colunaAtualizada);
        toast.success(`Coluna "${coluna.name}" atualizada com sucesso!`);
      } else {
        // Criar nova coluna com status_id normalizado
        const novaColuna = {
          ...coluna,
          status_id: statusIdNormalizado, // Usar o ID normalizado
        };
        
        colunaSalva = await criarColuna(novaColuna);
        toast.success(`Coluna "${coluna.name}" criada com sucesso!`);
      }
      
      // Atualizar lista de colunas de forma segura
      setColunas(prev => {
        if (!colunaSalva) {
          console.warn('Dados da coluna salva estão vazios ou inválidos');
          return prev;
        }
        
        // Extrair propriedades da coluna salva
        const { alerts = [], ...rest } = colunaSalva;
        
        // Verificar se é atualização ou nova coluna
        let novaLista;
        if (colunaParaEditar) {
          // Substituir a coluna existente
          novaLista = prev.map(c => c.id === rest.id ? { ...rest, alerts } : c);
        } else {
          // Adicionar nova coluna
          novaLista = [...prev, { ...rest, alerts }];
        }
        
        // Ordenar a lista atualizada por ordem
        return novaLista.sort((a, b) => a.ordem - b.ordem);
      });
      
      // Notificar sobre a mudança com pequeno delay para garantir que os dados estejam no banco
      setTimeout(() => {
        if (onColumnsChange) {
          onColumnsChange();
        }
      }, 1000); // Aumentado para 1000ms para garantir melhor sincronização
      
      // Fechar formulário
      setFormOpen(false);
      setColunaParaEditar(undefined);
    } catch (error) {
      console.error('Erro ao salvar coluna:', error);
      toast.error('Erro ao salvar coluna');
    } finally {
      setLoading(false);
    }
  };

  // Abrir formulário para editar coluna
  const handleEditColuna = (coluna: Omit<KanbanColuna, 'alerts'>) => {
    // Primeiro fechamos qualquer diálogo que esteja aberto
    setFormOpen(false);
    
    // Definimos a coluna para edição
    setColunaParaEditar({
      ...coluna,
      // Garantir que todos os campos estejam corretos
      id: coluna.id || coluna.status_id,
      name: coluna.name || '',
      color: coluna.color || '#3b82f6',
      tipo: coluna.tipo || 'AMBOS',
      ordem: typeof coluna.ordem === 'number' ? coluna.ordem : 0,
      status_id: coluna.status_id || '',
      ativo: coluna.ativo !== undefined ? coluna.ativo : true,
    });
    
    // Esperamos a próxima atualização do estado antes de abrir o diálogo
    setTimeout(() => {
      setFormOpen(true);
    }, 50);
  };

  // Confirmar exclusão de coluna
  const handleConfirmDelete = async () => {
    if (!colunaParaExcluir) return;
    
    try {
      setLoading(true);
      
      await excluirColuna(colunaParaExcluir);
      
      // Remover coluna da lista
      setColunas(prev => prev.filter(c => c.id !== colunaParaExcluir));
      
      // Notificar sobre a mudança com delay para garantir sincronização
      setTimeout(() => {
        if (onColumnsChange) {
          onColumnsChange();
        }
      }, 1000);
      
      toast.success('Coluna excluída com sucesso!');
    } catch (error) {
      console.error('Erro ao excluir coluna:', error);
      toast.error('Erro ao excluir coluna');
    } finally {
      setLoading(false);
      setColunaParaExcluir(null);
    }
  };

  // Mover coluna para cima ou para baixo
  const handleMoveColuna = async (id: string, direcao: 'up' | 'down') => {
    const index = colunas.findIndex(c => c.id === id);
    if (index === -1) return;
    
    // Não permitir mover para cima se já estiver no topo
    if (direcao === 'up' && index === 0) return;
    
    // Não permitir mover para baixo se já estiver no final
    if (direcao === 'down' && index === colunas.length - 1) return;
    
    // Criar cópia da lista de colunas
    const novaLista = [...colunas];
    
    // Trocar a posição com a coluna adjacente
    const novoIndex = direcao === 'up' ? index - 1 : index + 1;
    [novaLista[index], novaLista[novoIndex]] = [novaLista[novoIndex], novaLista[index]];
    
    // Atualizar a ordem
    const colunasAtualizadas = novaLista.map((coluna, idx) => ({
      ...coluna,
      ordem: idx
    }));
    
    try {
      setLoading(true);
      
      // Atualizar no banco de dados
      const colunasReordenadas = await reordenarColunas(colunasAtualizadas);
      
      // Atualizar estado
      setColunas(colunasReordenadas.map(({ alerts, ...rest }) => rest));
      
      // Notificar sobre a mudança
      if (onColumnsChange) {
        onColumnsChange();
      }
      
      toast.success('Ordem das colunas atualizada!');
    } catch (error) {
      console.error('Erro ao reordenar colunas:', error);
      toast.error('Erro ao reordenar colunas');
    } finally {
      setLoading(false);
    }
  };

  // Renderizar badge de tipo
  const renderTipoBadge = (tipo: 'PRECATORIO' | 'RPV' | 'AMBOS') => {
    switch (tipo) {
      case 'PRECATORIO':
        return <Badge className="bg-blue-500">Precatórios</Badge>;
      case 'RPV':
        return <Badge className="bg-green-500">RPVs</Badge>;
      case 'AMBOS':
        return <Badge className="bg-purple-500">Ambos</Badge>;
      default:
        return null;
    }
  };

  // Mostrar indicador de carregamento mais claro
  if (loading && colunas.length === 0) {
    return (
      <div className="flex flex-col h-full w-full items-center justify-center gap-4 py-12">
        <div className="bg-primary/10 p-6 rounded-full">
          <Loader2 className="h-12 w-12 animate-spin text-primary" />
        </div>
        <div className="text-center">
          <h3 className="text-lg font-medium mb-1">Carregando colunas...</h3>
          <p className="text-muted-foreground">Aguarde enquanto buscamos os dados no servidor</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-xl font-bold flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
              <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path>
              <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>
            </svg>
            Colunas do Kanban
          </h2>
          <p className="text-sm text-muted-foreground">
            Configure as colunas para exibição no quadro Kanban
          </p>
        </div>
        
        <Dialog open={formOpen} onOpenChange={(open) => {
          setFormOpen(open);
          if (!open) setColunaParaEditar(undefined);
        }}>
          <DialogTrigger asChild>
            <Button 
              onClick={() => {
                setColunaParaEditar(undefined);
                setFormOpen(true);
              }}
              className="gap-1"
            >
              <Plus className="h-4 w-4" />
              Adicionar Coluna
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[550px]">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                {colunaParaEditar ? (
                  <>
                    <Edit className="h-5 w-5 text-amber-500" />
                    Editar Coluna: {colunaParaEditar.name}
                  </>
                ) : (
                  <>
                    <Plus className="h-5 w-5 text-green-500" />
                    Adicionar Nova Coluna
                  </>
                )}
              </DialogTitle>
            </DialogHeader>
            <ColunaForm
              coluna={colunaParaEditar}
              onSave={handleSaveColuna}
              onCancel={() => {
                setFormOpen(false);
                setColunaParaEditar(undefined);
              }}
              statusOptions={statusOptions}
            />
          </DialogContent>
        </Dialog>
      </div>
      
      <Tabs defaultValue="TODOS" onValueChange={(value) => setTipoFiltro(value as any)}>
        <TabsList className="mb-4">
          <TabsTrigger value="TODOS">Todas</TabsTrigger>
          <TabsTrigger value="PRECATORIO">Precatórios</TabsTrigger>
          <TabsTrigger value="RPV">RPVs</TabsTrigger>
          <TabsTrigger value="AMBOS">Ambos</TabsTrigger>
        </TabsList>
        
        <TabsContent value={tipoFiltro} className="mt-0">
          {loading && colunas.length > 0 && (
            <div className="flex items-center justify-center py-3 bg-muted/30 rounded-md mb-3">
              <Loader2 className="h-5 w-5 animate-spin text-primary mr-2" />
              <span className="text-sm">Atualizando dados...</span>
            </div>
          )}
          
          <div className="rounded-md border shadow-sm">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[60px] text-center">Ordem</TableHead>
                  <TableHead>Nome</TableHead>
                  <TableHead className="w-[120px]">Cor</TableHead>
                  <TableHead className="w-[120px]">Tipo</TableHead>
                  <TableHead className="w-[150px]">Status</TableHead>
                  <TableHead className="w-[80px] text-center">Ativo</TableHead>
                  <TableHead className="text-right w-[140px]">Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {colunasFiltradas.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="h-24 text-center">
                      Nenhuma coluna encontrada
                    </TableCell>
                  </TableRow>
                ) : (
                  colunasFiltradas.map((coluna) => (
                    <TableRow key={coluna.id}>
                      <TableCell className="font-medium text-center">{coluna.ordem}</TableCell>
                      <TableCell className="font-medium">{coluna.name}</TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <div 
                            className="w-6 h-6 rounded-full shadow-sm" 
                            style={{ backgroundColor: coluna.color }}
                          />
                          <span className="text-xs text-muted-foreground">{coluna.color}</span>
                        </div>
                      </TableCell>
                      <TableCell>{renderTipoBadge(coluna.tipo)}</TableCell>
                      <TableCell>{statusOptions.find(s => s.id === coluna.status_id)?.nome || coluna.status_id}</TableCell>
                      <TableCell>
                        <Badge variant={coluna.ativo ? "default" : "outline"}>
                          {coluna.ativo ? "Sim" : "Não"}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end space-x-1">
                          <Button
                            variant="outline"
                            size="icon"
                            onClick={() => handleMoveColuna(coluna.id, 'up')}
                            disabled={colunas.indexOf(coluna) === 0}
                          >
                            <ArrowUp className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="icon"
                            onClick={() => handleMoveColuna(coluna.id, 'down')}
                            disabled={colunas.indexOf(coluna) === colunas.length - 1}
                          >
                            <ArrowDown className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="icon"
                            onClick={() => handleEditColuna(coluna)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button
                                variant="outline"
                                size="icon"
                                className="text-destructive"
                                onClick={() => setColunaParaExcluir(coluna.id)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Excluir Coluna</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Tem certeza que deseja excluir a coluna "{coluna.name}"?
                                  Esta ação não pode ser desfeita.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel onClick={() => setColunaParaExcluir(null)}>
                                  Cancelar
                                </AlertDialogCancel>
                                <AlertDialogAction onClick={handleConfirmDelete}>
                                  Excluir
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
