import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { FileText } from "lucide-react";
import { Precatorio } from "./types";
import { X } from "lucide-react";

interface DetalhesModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  precatorio: Precatorio | null;
  onEdit: (precatorio: Precatorio) => void;
}

export function DetalhesModal({ isOpen, onOpenChange, precatorio, onEdit }: DetalhesModalProps) {
  if (!precatorio) return null;

  const handleClose = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto" aria-describedby="detalhes-precatorio-description">
        <DialogHeader>
          <DialogTitle>Detalhes do Precatório</DialogTitle>
          <DialogDescription id="detalhes-precatorio-description">
            Informações detalhadas sobre o precatório selecionado.
          </DialogDescription>
          <button onClick={handleClose} className="absolute right-4 top-4">
            <X className="h-4 w-4" />
          </button>
        </DialogHeader>
        
        <div className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h3 className="font-medium mb-2">Informações Gerais</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-neutral-500">Número:</span>
                  <span>{precatorio.numero}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-neutral-500">Valor:</span>
                  <span>R$ {precatorio.valor.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-neutral-500">Desconto:</span>
                  <span>{precatorio.desconto}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-neutral-500">Status:</span>
                  <Badge variant="outline">{precatorio.status}</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-neutral-500">Prioridade:</span>
                  <Badge
                    variant={
                      precatorio.prioridade === "alta"
                        ? "destructive"
                        : precatorio.prioridade === "media"
                        ? "secondary"
                        : "outline"
                    }
                  >
                    {precatorio.prioridade}
                  </Badge>
                </div>
              </div>
            </div>

            <div>
              <h3 className="font-medium mb-2">Cliente</h3>
              <div className="space-y-2">
                <div className="flex items-center gap-2 mb-3">
                  <Avatar>
                    <AvatarImage src={precatorio.cliente.avatar} />
                    <AvatarFallback>
                      {precatorio.cliente.nome.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium">{precatorio.cliente.nome}</p>
                    <p className="text-sm text-neutral-500">
                      {precatorio.cliente.email}
                    </p>
                  </div>
                </div>
                <div className="flex justify-between">
                  <span className="text-neutral-500">Telefone:</span>
                  <span>{precatorio.cliente.telefone}</span>
                </div>
              </div>
            </div>
          </div>

          {precatorio.documentos && precatorio.documentos.length > 0 && (
            <div>
              <h3 className="font-medium mb-2">Documentos</h3>
              <div className="space-y-2">
                {precatorio.documentos.map((doc, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-2 border rounded-lg"
                  >
                    <div className="flex items-center gap-2">
                      <FileText className="w-4 h-4 text-neutral-500" />
                      <span>{doc.nome}</span>
                    </div>
                    <Badge
                      variant={
                        doc.status === "aprovado"
                          ? "default"
                          : doc.status === "rejeitado"
                          ? "destructive"
                          : "secondary"
                      }
                    >
                      {doc.status}
                    </Badge>
                  </div>
                ))}
              </div>
            </div>
          )}

          {precatorio.historico && precatorio.historico.length > 0 && (
            <div>
              <h3 className="font-medium mb-2">Histórico</h3>
              <div className="space-y-2">
                {precatorio.historico.map((hist, index) => (
                  <div
                    key={index}
                    className="flex items-start gap-2 p-2 border rounded-lg"
                  >
                    <div className="w-2 h-2 mt-2 rounded-full bg-neutral-400" />
                    <div>
                      <p className="font-medium">{hist.acao}</p>
                      <p className="text-sm text-neutral-500">{hist.detalhes}</p>
                      <div className="flex items-center gap-2 mt-1 text-xs text-neutral-500">
                        <span>{hist.usuario}</span>
                        <span>•</span>
                        <span>{new Date(hist.data).toLocaleDateString("pt-BR")}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          <div>
            <h3 className="font-medium mb-2">Observações</h3>
            <p className="text-neutral-600">{precatorio.observacoes || ""}</p>
          </div>
          
          {precatorio.tags && precatorio.tags.length > 0 && (
            <div>
              <h3 className="font-medium mb-2">Tags</h3>
              <div className="flex flex-wrap gap-2">
                {precatorio.tags.map((tag, index) => (
                  <Badge key={index} variant="secondary">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Fechar
          </Button>
          <Button onClick={() => {
            handleClose();
            onEdit(precatorio);
          }}>
            Editar Precatório
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 