export interface Precatorio {
  id: string;
  numero: string;
  numero_precatorio?: string; // Alias para numero (compatibilidade)
  valor: number;
  desconto: number;
  // Tornar o tipo status mais flexível para lidar com valores dinâmicos do banco de dados
  status: string;  // Aceita qualquer string para maior flexibilidade com o banco
  status_id?: string; // UUID do status correspondente no banco de dados
  status_nome?: string; // Nome formatado do status
  status_info?: any; // Informações adicionais do status (join)
  cliente: {
    nome: string;
    avatar: string;
    email: string;
    telefone: string;
  };
  responsavel: {
    nome: string;
    avatar: string;
    cargo: string;
  };
  dataCriacao: string;
  dataAtualizacao: string;
  dataVencimento: string;
  tribunal: string;
  natureza: string;
  documentos: {
    nome: string;
    tipo: string;
    status: "pendente" | "aprovado" | "rejeitado";
    data: string;
  }[];
  historico: {
    acao: string;
    data: string;
    usuario: string;
    detalhes: string;
  }[];
  observacoes: string;
  tags: string[];
  prioridade: "baixa" | "media" | "alta";
}

export interface FiltrosAvancados {
  prioridade: string;
  tribunal: string;
  natureza: string;
  valorMin?: number;
  valorMax?: number;
  dataInicio?: Date;
  dataFim?: Date;
  dataVencimentoInicio?: Date;
  dataVencimentoFim?: Date;
  tags: string[];
  responsavel: string;
}

export interface KanbanColuna {
  id: string;
  name: string;
  nome?: string; // Alias para name (compatibilidade)
  color: string;
  cor?: string; // Alias para color (compatibilidade)
  tipo: 'PRECATORIO' | 'RPV' | 'AMBOS'; // Tipo de visualização a que pertence
  ordem: number; // Ordem de exibição na visualização
  status_id: string; // ID do status correspondente no banco de dados (código)
  status_uuid?: string; // UUID do status correspondente no banco de dados (UUID)
  ativo: boolean; // Se a coluna está ativa ou não
  count?: number; // Número de precatórios/RPVs na coluna (para exibição)
  alerts: {
    tipo: string;
    mensagem: string;
  }[];
  status?: any; // Informações adicionais do status (join)
}

export interface DashboardStats {
  total: number;
  valorTotal: number;
  concluidos: number;
  emAndamento: number;
  taxaConclusao: number;
  porStatus: {
    status: string;
    valor: number;
    cor: string;
  }[];
  porPrioridade: {
    prioridade: string;
    valor: number;
  }[];
  valoresPorTribunal: {
    tribunal: string;
    valor: number;
  }[];
}

export const CORES_STATUS = {
  analise: "#3b82f6",
  proposta_tmj: "#8b5cf6",
  proposta_btg: "#ec4899",
  negociacao: "#f59e0b",
  documentacao: "#10b981",
  pagamento: "#6366f1",
  concluido: "#22c55e",
  cancelado: "#ef4444",
};

export const COLORS_CHART = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#A020F0', '#20B2AA'];