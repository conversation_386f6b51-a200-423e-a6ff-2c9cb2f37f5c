import { Precatorio, KanbanC<PERSON><PERSON> as KanbanColunaType } from "./types";
import { PrecatorioCard } from "./PrecatorioCard";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { useDroppable } from "@dnd-kit/core";
import { AlertTriangle, PlusCircle, AlertCircle, ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";

interface KanbanColunaProps {
  coluna: KanbanColunaType;
  precatorios: Precatorio[];
  onVerDetalhes: (precatorio: Precatorio) => void;
  onEdit: (precatorio: Precatorio) => void;
  onDelete: (id: string) => void;
  onNovoPrecatorio: () => void;
}

export function KanbanColuna({
  coluna,
  precatorios,
  onVerDetalhes,
  onEdit,
  onDelete,
  onNovoPrecatorio,
}: KanbanColunaProps) {
  const [compacto, setCompacto] = useState(false);
  const navigate = useNavigate();

  // Estado local da coluna com a contagem de precatórios e normalização de dados
  const [colunaLocal, setColunaLocal] = useState(() => {
    // Normalizar os dados ao inicializar o estado local
    const statusIdNormalizado = coluna.status_id ? coluna.status_id.trim().toLowerCase() : '';
    const statusUuidNormalizado = coluna.status_uuid ? coluna.status_uuid.trim().toLowerCase() : '';

    console.log(`Inicializando coluna ${coluna.name}: status_id=${statusIdNormalizado}, status_uuid=${statusUuidNormalizado}`);

    return {
      ...coluna,
      status_id: statusIdNormalizado,
      status_uuid: statusUuidNormalizado,
      count: precatorios.length,
      alerts: coluna.alerts || []  // Garantir que alerts existe
    };
  });

  // Referência para rastrear se a coluna foi montada/desmontada
  const colunaMontada = useRef(true);

  useEffect(() => {
    // Marcamos que a coluna está montada
    colunaMontada.current = true;

    return () => {
      // Se o componente for desmontado, marcamos
      colunaMontada.current = false;
    };
  }, []);

  // Atualizar dados da coluna quando mudar - considerando colunas dinâmicas
  useEffect(() => {
    if (!colunaMontada.current) return;

    // Normalizar o status_id e status_uuid para evitar problemas de comparação
    const statusIdNormalizado = coluna.status_id ? coluna.status_id.trim().toLowerCase() : '';
    const statusUuidNormalizado = coluna.status_uuid ? coluna.status_uuid.trim().toLowerCase() : '';

    setColunaLocal(prev => ({
      ...prev,
      ...coluna,
      status_id: statusIdNormalizado,
      status_uuid: statusUuidNormalizado,
      count: precatorios.length,
      alerts: coluna.alerts || prev.alerts || []
    }));

    // Log para debug da atualização da coluna
    console.log(`KanbanColuna ${coluna.name} (${coluna.id || statusIdNormalizado}) atualizada:`, {
      original: coluna,
      normalizada: {
        status_id: statusIdNormalizado,
        status_uuid: statusUuidNormalizado
      },
      qtdPrecatorios: precatorios.length
    });
  }, [coluna, precatorios]);

  const { setNodeRef, isOver } = useDroppable({
    id: coluna.id,
    data: {
      type: "column",
      coluna,
    },
  });

  const getAlertColor = (tipo: string) => {
    switch (tipo) {
      case "atrasado":
        return "text-red-500";
      case "vencendo":
        return "text-amber-500";
      case "importante":
        return "text-blue-500";
      default:
        return "text-neutral-500";
    }
  };

  const getAlertIcon = (tipo: string) => {
    switch (tipo) {
      case "atrasado":
      case "vencendo":
        return <AlertTriangle className="w-4 h-4 mr-1" />;
      case "importante":
        return <AlertCircle className="w-4 h-4 mr-1" />;
      default:
        return null;
    }
  };

  const toggleCompacto = (e: React.MouseEvent) => {
    e.stopPropagation();
    setCompacto(!compacto);
  };

  const containerRef = useRef<HTMLDivElement>(null);
  const scrollInterval = useRef<NodeJS.Timeout>();

  useEffect(() => {
    return () => {
      if (scrollInterval.current) {
        clearInterval(scrollInterval.current);
      }
    };
  }, []);

  const scrollThreshold = 50;
  const scrollSpeed = 5;

  const handleScroll = (direction: 'up' | 'down', clientY: number) => {
    if (!containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();

    let speed = scrollSpeed;

    if (direction === 'up' && clientY < rect.top + scrollThreshold) {
      speed = Math.max(1, ((scrollThreshold - (clientY - rect.top)) / scrollThreshold) * scrollSpeed);
      containerRef.current.scrollBy({
        top: -speed,
        behavior: 'auto'
      });
    } else if (direction === 'down' && clientY > rect.bottom - scrollThreshold) {
      speed = Math.max(1, ((scrollThreshold - (rect.bottom - clientY)) / scrollThreshold) * scrollSpeed);
      containerRef.current.scrollBy({
        top: speed,
        behavior: 'auto'
      });
    }
  };

  const startAutoScroll = (direction: 'up' | 'down') => {
    stopAutoScroll();
    scrollInterval.current = setInterval(() => {
      if (containerRef.current) {
        if (direction === 'up') {
          containerRef.current.scrollBy({ top: -scrollSpeed, behavior: 'auto' });
        } else {
          containerRef.current.scrollBy({ top: scrollSpeed, behavior: 'auto' });
        }
      }
    }, 16);
  };

  const stopAutoScroll = () => {
    if (scrollInterval.current) {
      clearInterval(scrollInterval.current);
      scrollInterval.current = undefined;
    }
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    const { clientY } = e;

    if (clientY < rect.top + scrollThreshold) {
      handleScroll('up', clientY);
    } else if (clientY > rect.bottom - scrollThreshold) {
      handleScroll('down', clientY);
    } else {
      stopAutoScroll();
    }
  };

  return (
    <div
      ref={setNodeRef}
      className={cn(
        "flex flex-col h-full shrink-0 rounded-md border bg-background p-3 transition-all duration-200",
        isOver && "ring-2 ring-primary/50 ring-offset-1 shadow-lg",
        coluna.alerts && coluna.alerts.length > 0 && coluna.color === "red" && "border-l-4 border-l-red-500",
        coluna.alerts && coluna.alerts.length > 0 && coluna.color === "yellow" && "border-l-4 border-l-yellow-500",
        coluna.alerts && coluna.alerts.length > 0 && coluna.color === "green" && "border-l-4 border-l-green-500",
        coluna.alerts && coluna.alerts.length > 0 && coluna.color === "blue" && "border-l-4 border-l-blue-500",
        coluna.alerts && coluna.alerts.length > 0 && coluna.color === "purple" && "border-l-4 border-l-purple-500",
        coluna.alerts && coluna.alerts.length > 0 && coluna.color === "orange" && "border-l-4 border-l-orange-500",
        coluna.alerts && coluna.alerts.length > 0 && coluna.color === "gray" && "border-l-4 border-l-gray-500",
        compacto ? "w-[60px]" : "w-[280px]"
      )}
    >
      <div className={cn(
        "flex items-center justify-between mb-3",
        compacto && "flex-col h-full"
      )}>
        <div className={cn(
          "flex items-center",
          compacto && "flex-col rotate-90 origin-center whitespace-nowrap mt-20"
        )}>
          {/* Cor da coluna em formato de círculo */}
          <div
            className={cn("rounded-full", compacto ? "w-4 h-4 mb-2" : "w-3 h-3 mr-2")}
            style={{ backgroundColor: coluna.color }}
          />
          {/* Nome da coluna vindo do banco de dados */}
          <span className={cn("font-semibold text-sm", compacto && "transform -rotate-180")}>
            {colunaLocal.name}
          </span>

          {/* Badge com a contagem de precatórios */}
          {!compacto && (
            <div className="flex items-center ml-2 gap-1">
              <Badge variant="outline" className="text-xs">
                {precatorios.length}
              </Badge>

              {/* Badge de identificação da coluna (status_id e status_uuid) */}
              <Badge
                variant="secondary"
                className="text-xs px-1"
                title={`Status ID: ${colunaLocal.status_id}${colunaLocal.status_uuid ? `\nStatus UUID: ${colunaLocal.status_uuid}` : ''}`}
              >
                ID: {colunaLocal.status_id.substring(0, 4)}{colunaLocal.status_id.length > 4 ? '...' : ''}
              </Badge>
            </div>
          )}
        </div>

        <Button
          variant="ghost"
          size="sm"
          className={cn(
            "h-6 w-6 p-0",
            compacto && "mt-auto mb-4"
          )}
          onClick={toggleCompacto}
          title={compacto ? "Expandir coluna" : "Reduzir coluna"}
        >
          {compacto ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
        </Button>
      </div>

      {!compacto && colunaLocal.alerts && colunaLocal.alerts.length > 0 && (
        <div className="mb-3">
          {colunaLocal.alerts.map((alerta, index) => (
            <div
              key={index}
              className={cn(
                "flex items-center text-xs p-2 rounded-md mb-1",
                "bg-neutral-50 dark:bg-neutral-900 border",
                getAlertColor(alerta.tipo)
              )}
            >
              {getAlertIcon(alerta.tipo)}
              <span className="font-medium mr-1">{alerta.tipo}:</span>
              <span>{alerta.mensagem}</span>
            </div>
          ))}
        </div>
      )}

      {!compacto && (
        <>
          <div
            ref={containerRef}
            className={cn(
              'flex-1 overflow-y-auto space-y-3 pb-0.5 pr-1',
              'scrollbar-thin scrollbar-track-transparent scrollbar-thumb-neutral-200 dark:scrollbar-thumb-neutral-700',
              'hover:scrollbar-thumb-neutral-300 dark:hover:scrollbar-thumb-neutral-600',
              'transition-colors duration-200',
              'relative',
              '[&>*:not(:first-child)]:mt-3', // Espaçamento entre cards
              isOver && 'bg-primary/5' // Feedback visual mais sutil quando arrastando sobre a coluna
            )}
            onMouseMove={handleMouseMove}
            onMouseLeave={stopAutoScroll}
          >
            {precatorios.length > 0 ? (
              <div className="space-y-3">
                {precatorios.map((precatorio, index) => (
                  <div
                    key={precatorio.id}
                    className={cn(
                      "transition-all duration-200",
                      // Animação de entrada estilo cascata - cada card aparece com um pequeno delay
                      "motion-safe:animate-in motion-safe:fade-in-50 motion-safe:slide-in-from-left-3",
                      { "motion-safe:delay-100": index === 1 },
                      { "motion-safe:delay-150": index === 2 },
                      { "motion-safe:delay-200": index === 3 },
                      { "motion-safe:delay-250": index >= 4 }
                    )}
                  >
                    <PrecatorioCard
                      precatorio={precatorio}
                      onVerDetalhes={onVerDetalhes}
                      onEdit={onEdit}
                      onDelete={onDelete}
                    />
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center h-24 border border-dashed rounded-md p-4 transition-opacity motion-safe:animate-in motion-safe:fade-in-50 motion-safe:duration-300">
                <p className="text-sm text-muted-foreground text-center mb-2">
                  Nenhum precatório nesta coluna
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  className="h-8 w-full"
                  onClick={() => navigate('/precatorios/novo')}
                >
                  <PlusCircle className="h-4 w-4 mr-1" />
                  Adicionar
                </Button>
              </div>
            )}
          </div>

          {precatorios.length > 0 && (
            <Button
              variant="ghost"
              size="sm"
              className="mt-3 text-xs justify-start p-2 h-auto group"
              onClick={() => {
                // Passar o status_id como parâmetro para pré-selecionar o status do novo precatório
                navigate(`/precatorios/novo?status=${colunaLocal.status_id}`);
              }}
            >
              <PlusCircle className="h-4 w-4 mr-1 group-hover:text-primary transition-colors" />
              <span>
                Adicionar <span className="font-medium">novo</span> precatório
                <span className="text-muted-foreground">({colunaLocal.name})</span>
              </span>
            </Button>
          )}
        </>
      )}
    </div>
  );
}