import {
  Dialog,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Card, CardContent } from "@/components/ui/card";
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  PieChart,
  Pie,
  Cell,
  ResponsiveContainer,
} from "recharts";
import { CircleDollarSign, TrendingUp, Timer, CheckCircle2, AlertTriangle } from "lucide-react";
import { DashboardStats, COLORS_CHART } from "./types";

interface EstatisticasModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  stats: DashboardStats;
}

export function EstatisticasModal({ isOpen, onOpenChange, stats }: EstatisticasModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Estatísticas de Precatórios</DialogTitle>
          <DialogDescription>
            Visão geral e análise dos precatórios do sistema
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid grid-cols-2 gap-4 mb-6">
          <Card>
            <CardContent className="p-3 flex items-center justify-between">
              <div>
                <p className="text-xs text-neutral-500">Total de Precatórios</p>
                <p className="text-lg font-semibold">{stats.total}</p>
              </div>
              <CircleDollarSign className="w-8 h-8 text-blue-500 opacity-70" />
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-3 flex items-center justify-between">
              <div>
                <p className="text-xs text-neutral-500">Valor Total</p>
                <p className="text-lg font-semibold">
                  R$ {stats.valorTotal.toLocaleString('pt-BR', { 
                    notation: 'compact',
                    maximumFractionDigits: 2 
                  })}
                </p>
              </div>
              <TrendingUp className="w-8 h-8 text-emerald-500 opacity-70" />
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-3 flex items-center justify-between">
              <div>
                <p className="text-xs text-neutral-500">Em Andamento</p>
                <p className="text-lg font-semibold">{stats.emAndamento}</p>
              </div>
              <Timer className="w-8 h-8 text-amber-500 opacity-70" />
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-3 flex items-center justify-between">
              <div>
                <p className="text-xs text-neutral-500">Concluídos</p>
                <p className="text-lg font-semibold">{stats.concluidos}</p>
              </div>
              <CheckCircle2 className="w-8 h-8 text-emerald-500 opacity-70" />
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-3 flex items-center justify-between">
              <div>
                <p className="text-xs text-neutral-500">Alta Prioridade</p>
                <p className="text-lg font-semibold">
                  {stats.porPrioridade.find(p => p.prioridade === "Alta")?.valor || 0}
                </p>
              </div>
              <AlertTriangle className="w-8 h-8 text-red-500 opacity-70" />
            </CardContent>
          </Card>
        </div>
        
        <div className="grid grid-cols-1 gap-4">
          <Card>
            <CardContent className="pt-6">
              <h3 className="text-base font-medium mb-4">Distribuição por Status</h3>
              <div className="h-[180px]">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={stats.porStatus}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="valor"
                      nameKey="status"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {stats.porStatus.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.cor || COLORS_CHART[index % COLORS_CHART.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => [`${value} precatórios`, 'Quantidade']} />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <h3 className="text-base font-medium mb-4">Valor Total por Tribunal</h3>
              <div className="h-[250px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={stats.valoresPorTribunal}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="tribunal" />
                    <YAxis 
                      tickFormatter={(value) => `R$ ${value.toLocaleString('pt-BR', { notation: 'compact' })}`}
                    />
                    <Tooltip 
                      formatter={(value) => [
                        `R$ ${Number(value).toLocaleString('pt-BR')}`, 
                        'Valor Total'
                      ]} 
                    />
                    <Bar dataKey="valor" fill="#3b82f6" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
} 