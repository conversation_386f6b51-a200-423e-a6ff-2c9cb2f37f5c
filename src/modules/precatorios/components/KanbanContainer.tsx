import { useState, useEffect, useMemo, useRef, useCallback } from "react";
import { KanbanColuna } from "./KanbanColuna";
import { DetalhesModal } from "./DetalhesModal";
import { FormularioPrecatorio } from "./FormularioPrecatorio";
import { EstatisticasModal } from "./EstatisticasModal";
import { FiltrosModal } from "./FiltrosModal";
import { Precatorio, KanbanColuna as KanbanColunaType, FiltrosAvancados, DashboardStats, CORES_STATUS } from "./types";
import {
  DndContext,
  DragOverlay,
  useSensors,
  useSensor,
  PointerSensor,
  MouseSensor,
  TouchSensor,
  KeyboardSensor,
  defaultDropAnimationSideEffects,
  DragStartEvent,
  DragEndEvent,
  DragOverEvent,
  DragCancelEvent,
  MeasuringStrategy,
  DropAnimation,
  Modifier,
} from "@dnd-kit/core";
import { restrictToWindowEdges } from "@dnd-kit/modifiers";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { PrecatorioCard } from "./PrecatorioCard";
import {
  FilterX,
  Plus,
  Filter,
  BarChart,
  Search,
  SlidersHorizontal,
  BarChart2,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface KanbanContainerProps {
  todosPrecatorios: Precatorio[];
  colunas: KanbanColunaType[];
  onSavePrecatorio: (precatorio: Precatorio) => void;
  onDeletePrecatorio: (id: string) => void;
  onMovePrecatorio: (precatorioId: string, novoStatus: string) => void;
}

// Configuração otimizada para a animação de drop
// Simplificando a animação de drop para evitar problemas de tipagem
const dropAnimation: DropAnimation = {
  sideEffects: defaultDropAnimationSideEffects({
    styles: {
      active: {
        opacity: '0',  // Card original fica invisível durante drag
      },
      dragOverlay: {
        opacity: '1',
        scale: '1.03',
        boxShadow: '0 8px 20px rgba(0, 0, 0, 0.12)',
        filter: 'drop-shadow(0px 4px 8px rgba(0, 0, 0, 0.08))',
        zIndex: '1200',
      },
    }
  }),
  duration: 150, // Duração padronizada para todas as transições
  easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
};

// Modificadores para restringir o movimento
const modifiers: Modifier[] = [restrictToWindowEdges];

export function KanbanContainer({
  todosPrecatorios,
  colunas,
  onSavePrecatorio,
  onDeletePrecatorio,
  onMovePrecatorio,
}: KanbanContainerProps) {
  // Estados para os modais
  const [precatorioSelecionado, setPrecatorioSelecionado] = useState<Precatorio | null>(null);
  const [isDetalhesModalOpen, setIsDetalhesModalOpen] = useState(false);
  const [isFormModalOpen, setIsFormModalOpen] = useState(false);
  const [isFiltrosModalOpen, setIsFiltrosModalOpen] = useState(false);
  const [isEstatisticasModalOpen, setIsEstatisticasModalOpen] = useState(false);
  const [isConfirmDeleteOpen, setIsConfirmDeleteOpen] = useState(false);
  const [precatorioIdToDelete, setPrecatorioIdToDelete] = useState<string | null>(null);
  const [modoEdicao, setModoEdicao] = useState(false);

  // Estados para filtros e busca
  const [filtrosAvancados, setFiltrosAvancados] = useState<FiltrosAvancados>({
    prioridade: "",
    tribunal: "",
    natureza: "",
    valorMin: undefined,
    valorMax: undefined,
    dataVencimentoInicio: undefined,
    dataVencimentoFim: undefined,
    tags: [],
    responsavel: "",
  });
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredPrecatorios, setFilteredPrecatorios] = useState<Precatorio[]>(todosPrecatorios);

  // Estado para drag and drop
  const [activePrecatorio, setActivePrecatorio] = useState<Precatorio | null>(null);

  // Sensores para drag and drop - otimizados para melhor desempenho
  const sensors = useSensors(
    useSensor(MouseSensor, {
      // Aumentar a distância mínima para iniciar o arrasto
      activationConstraint: {
        distance: 10, // Aumentado para evitar ativações acidentais
      },
    }),
    useSensor(TouchSensor, {
      // Configurações otimizadas para touch
      activationConstraint: {
        delay: 150, // Reduzido para melhor responsividade
        tolerance: 10, // Aumentado para maior precisão
      },
    }),
    useSensor(KeyboardSensor, {
      // Configurações padrão para acessibilidade
    })
  );

  const containerRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const [scrollLeft, setScrollLeft] = useState(0);

  // Filtrar precatórios
  useEffect(() => {
    let filtered = [...todosPrecatorios];

    // Aplicar filtro de busca textual
    if (searchTerm.trim()) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(p =>
        p.numero.toLowerCase().includes(searchLower) ||
        p.natureza?.toLowerCase().includes(searchLower) ||
        p.observacoes?.toLowerCase().includes(searchLower) ||
        p.cliente?.nome.toLowerCase().includes(searchLower) ||
        p.tribunal?.toLowerCase().includes(searchLower) ||
        p.responsavel?.nome.toLowerCase().includes(searchLower)
      );
    }

    // Aplicar filtros avançados
    if (filtrosAvancados.prioridade) {
      filtered = filtered.filter(p => p.prioridade === filtrosAvancados.prioridade);
    }

    if (filtrosAvancados.tribunal) {
      filtered = filtered.filter(p => p.tribunal === filtrosAvancados.tribunal);
    }

    if (filtrosAvancados.natureza) {
      filtered = filtered.filter(p => p.natureza === filtrosAvancados.natureza);
    }

    if (filtrosAvancados.valorMin !== undefined) {
      filtered = filtered.filter(p => p.valor >= filtrosAvancados.valorMin);
    }

    if (filtrosAvancados.valorMax !== undefined) {
      filtered = filtered.filter(p => p.valor <= filtrosAvancados.valorMax);
    }

    if (filtrosAvancados.responsavel) {
      filtered = filtered.filter(p => p.responsavel?.nome === filtrosAvancados.responsavel);
    }

    if (filtrosAvancados.tags.length > 0) {
      filtered = filtered.filter(p =>
        filtrosAvancados.tags.some(tag => p.tags?.includes(tag))
      );
    }

    setFilteredPrecatorios(filtered);
  }, [todosPrecatorios, searchTerm, filtrosAvancados]);

  // Calcular estatísticas
  const dashboardStats = useMemo(() => {
    // Ajustar para comparar com os valores corretos do banco de dados
    const totalPrecatorios = todosPrecatorios.length;
    const concluidos = todosPrecatorios.filter(p => p.status === "concluido").length;

    const stats: DashboardStats = {
      total: totalPrecatorios,
      valorTotal: todosPrecatorios.reduce((sum, p) => sum + p.valor, 0),
      emAndamento: todosPrecatorios.filter(p => p.status !== "concluido" && p.status !== "cancelado").length,
      concluidos: concluidos,
      taxaConclusao: totalPrecatorios > 0 ? (concluidos / totalPrecatorios) * 100 : 0,
      porStatus: [],
      porPrioridade: [],
      valoresPorTribunal: [],
    };

    // Agrupar por status
    const statusMap = new Map<string, number>();
    todosPrecatorios.forEach(p => {
      statusMap.set(p.status, (statusMap.get(p.status) || 0) + 1);
    });

    stats.porStatus = Array.from(statusMap.entries()).map(([status, valor]) => ({
      status,
      valor,
      cor: CORES_STATUS[status as keyof typeof CORES_STATUS] || "gray",
    }));

    // Agrupar por prioridade
    const prioridadeMap = new Map<string, number>();
    todosPrecatorios.forEach(p => {
      prioridadeMap.set(p.prioridade, (prioridadeMap.get(p.prioridade) || 0) + 1);
    });

    stats.porPrioridade = Array.from(prioridadeMap.entries()).map(([prioridade, valor]) => ({
      prioridade,
      valor,
    }));

    // Agrupar valores por tribunal
    const tribunalMap = new Map<string, number>();
    todosPrecatorios.forEach(p => {
      tribunalMap.set(p.tribunal, (tribunalMap.get(p.tribunal) || 0) + p.valor);
    });

    stats.valoresPorTribunal = Array.from(tribunalMap.entries()).map(([tribunal, valor]) => ({
      tribunal,
      valor,
    }));

    return stats;
  }, [todosPrecatorios]);

  // Ações dos botões e handlers
  const handleVerDetalhes = (precatorio: Precatorio) => {
    setPrecatorioSelecionado(precatorio);
    setIsDetalhesModalOpen(true);
  };

  const handleEditarPrecatorio = (precatorio: Precatorio) => {
    setPrecatorioSelecionado(precatorio);
    setModoEdicao(true);
    setIsFormModalOpen(true);
  };

  const handleNovoPrecatorio = () => {
    setPrecatorioSelecionado(null);
    setModoEdicao(false);
    setIsFormModalOpen(true);
  };

  const handleDeletePrecatorio = (id: string) => {
    setPrecatorioIdToDelete(id);
    setIsConfirmDeleteOpen(true);
  };

  const confirmDeletePrecatorio = () => {
    if (precatorioIdToDelete) {
      onDeletePrecatorio(precatorioIdToDelete);
      setIsConfirmDeleteOpen(false);
      setPrecatorioIdToDelete(null);
    }
  };

  // Handlers otimizados para drag and drop
  const handleDragStart = useCallback((event: DragStartEvent) => {
    const { active } = event;
    const precatorio = filteredPrecatorios.find(p => p.id === active.id);

    if (precatorio) {
      setActivePrecatorio(precatorio);
      document.body.style.cursor = 'grabbing';

      // Monitora o movimento do cursor para auto-scroll
      const handleDragMove = (e: MouseEvent) => {
        handleAutoScroll(e.clientX);
      };

      document.addEventListener('mousemove', handleDragMove);

      // Limpa o evento quando o arrastar terminar
      const cleanup = () => {
        document.removeEventListener('mousemove', handleDragMove);
        stopAutoScroll();
      };

      document.addEventListener('mouseup', cleanup, { once: true });
      document.addEventListener('dragend', cleanup, { once: true });
    }
  }, [filteredPrecatorios]);

  const handleDragEnd = useCallback((event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      const precatorioId = active.id as string;
      const novoStatus = over.id as string;

      // Atualiza imediatamente o estado local antes da chamada de API
      // Isso evita o efeito visual do card voltando para a posição original
      const precatorio = filteredPrecatorios.find(p => p.id === precatorioId);
      if (precatorio) {
        const updatedPrecatorios = filteredPrecatorios.map(p =>
          p.id === precatorioId ? { ...p, status: novoStatus } : p
        );
        setFilteredPrecatorios(updatedPrecatorios);
      }

      // Chama a API para atualizar no servidor
      onMovePrecatorio(precatorioId, novoStatus);
    }

    setActivePrecatorio(null);
    document.body.style.cursor = '';
    stopAutoScroll();
  }, [onMovePrecatorio, filteredPrecatorios]);

  const handleDragCancel = useCallback(() => {
    setActivePrecatorio(null);
    document.body.style.cursor = '';
    stopAutoScroll();
  }, []);

  // Agrupamento de precatórios por status usando colunas dinâmicas do banco de dados
  const precatoriosPorStatus = useMemo(() => {
    const result: Record<string, Precatorio[]> = {};

    console.log('Criando agrupamento de precatórios por status usando colunas dinâmicas:');
    console.log('Colunas disponíveis:', colunas.map(c => `${c.name} (${c.status_id})`));
    console.log('Total de precatórios filtrados:', filteredPrecatorios.length);

    // Criar mapeamento de status para colunas
    const statusParaColuna = new Map<string, string>();
    colunas.forEach(coluna => {
      // Mapear status_id para o id da coluna
      if (coluna.status_id) {
        statusParaColuna.set(String(coluna.status_id).toLowerCase().trim(), coluna.id);
      }

      // Mapear status_uuid para o id da coluna
      if (coluna.status_uuid) {
        statusParaColuna.set(String(coluna.status_uuid).toLowerCase().trim(), coluna.id);
      }

      // Também mapear o id da coluna para ele mesmo para garantir compatibilidade
      statusParaColuna.set(String(coluna.id).toLowerCase().trim(), coluna.id);

      // Mapear o nome da coluna (em minúsculas) para o id da coluna
      statusParaColuna.set(String(coluna.name || coluna.nome || '').toLowerCase().trim(), coluna.id);
    });

    console.log('Mapeamento de status para colunas:', Object.fromEntries(statusParaColuna));

    // Log detalhado das colunas para debug
    console.log('Detalhes das colunas:');
    colunas.forEach((coluna, index) => {
      console.log(`Coluna ${index + 1}: ID=${coluna.id}, Nome=${coluna.name || coluna.nome}, status_id=${coluna.status_id}, status_uuid=${coluna.status_uuid}`);
    });

    // Log de status para debug
    const statusSet = new Set(filteredPrecatorios.map(p => p.status));
    console.log('Status encontrados nos precatórios:', Array.from(statusSet));

    // Inicializar todas as colunas com arrays vazios
    colunas.forEach(coluna => {
      // Usar o ID da coluna como chave no resultado
      result[coluna.id] = [];
    });

    // Distribuir precatórios nas colunas correspondentes
    filteredPrecatorios.forEach(precatorio => {
      // Log detalhado para debug
      console.log(`Processando precatório: ID=${precatorio.id}, status=${precatorio.status}, status_id=${precatorio.status_id}`);

      // Verificar se o status está definido
      if (!precatorio.status && !precatorio.status_id) {
        console.warn(`Precatório sem status definido:`, precatorio);
        return;
      }

      // Tentar corresponder por status_id primeiro (mais confiável)
      if (precatorio.status_id) {
        // Verificar se alguma coluna tem status_uuid correspondente
        const colunaPorStatusId = colunas.find(c => c.status_uuid === precatorio.status_id);

        if (colunaPorStatusId) {
          console.log(`✅ MATCH por status_id! Precatório ${precatorio.numero} corresponde à coluna ${colunaPorStatusId.name}`);
          result[colunaPorStatusId.id].push(precatorio);
          return;
        }
      }

      // Se não encontrou por status_id, tentar por status
      // Normalizar o status (substituir 'ativo' por 'analise' se necessário)
      const statusNormalizado = precatorio.status === 'ativo' ? 'analise' : String(precatorio.status || '').toLowerCase().trim();

      // Encontrar a coluna correspondente usando o mapeamento
      const colunaId = statusParaColuna.get(statusNormalizado);

      if (colunaId && result[colunaId]) {
        // Se encontrou uma coluna correspondente, adicionar o precatório
        console.log(`✅ MATCH por status! Precatório ${precatorio.numero} com status "${statusNormalizado}" corresponde à coluna ID=${colunaId}`);
        result[colunaId].push(precatorio);
      } else {
        // Se não encontrou, tentar encontrar uma coluna com nome similar
        const colunaPorNome = colunas.find(c =>
          (c.name || '').toLowerCase().includes(statusNormalizado.toLowerCase()) ||
          statusNormalizado.toLowerCase().includes((c.name || '').toLowerCase())
        );

        if (colunaPorNome) {
          console.log(`✅ MATCH por nome similar! Precatório ${precatorio.numero} com status "${statusNormalizado}" mapeado para coluna "${colunaPorNome.name}"`);
          result[colunaPorNome.id].push(precatorio);
        } else {
          // Se ainda não encontrou, usar a primeira coluna como fallback
          console.warn(`❌ Coluna não encontrada para status "${statusNormalizado}", usando fallback:`, precatorio);
          const primeiraColuna = colunas[0]?.id;
          if (primeiraColuna && result[primeiraColuna]) {
            result[primeiraColuna].push(precatorio);
          }
        }
      }
    });

    // Log detalhado do resultado para cada coluna
    colunas.forEach(coluna => {
      const count = result[coluna.id]?.length || 0;
      console.log(`Coluna "${coluna.name}" (${coluna.id}): ${count} precatórios`);
    });

    return result;
  }, [filteredPrecatorios, colunas]);

  // Handlers para scroll horizontal do container
  const handleMouseDown = (e: React.MouseEvent) => {
    // Previne que o drag seja iniciado em elementos de card
    if (e.target && (e.target as HTMLElement).closest('[data-draggable="card"]')) {
      return;
    }

    if (containerRef.current) {
      setIsDragging(true);
      setStartX(e.pageX - containerRef.current.offsetLeft);
      setScrollLeft(containerRef.current.scrollLeft);
    }
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    // Se estiver arrastando um card, ative o auto-scroll nas bordas
    if (activePrecatorio && containerRef.current) {
      handleAutoScroll(e.clientX);
      return;
    }

    // Se estiver arrastando o container (não um card)
    if (isDragging && containerRef.current && !activePrecatorio) {
      // Ignora se o alvo for um card
      if (e.target && (e.target as HTMLElement).closest('[data-draggable="card"]')) {
        return;
      }

      const x = e.pageX - containerRef.current.offsetLeft;
      const walk = (x - startX) * 2;
      containerRef.current.scrollLeft = scrollLeft - walk;
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
    // Removido qualquer comportamento de reset do scroll
  };

  const handleMouseLeave = () => {
    setIsDragging(false);
    // Removido o stopAutoScroll daqui para evitar que afete o scroll normal
  };

  // Implementação de auto-scroll nas bordas durante o arrastar de cards
  const autoScrollThreshold = 120; // pixels da borda para iniciar o auto-scroll
  const autoScrollSpeed = 10; // velocidade do auto-scroll
  let scrollInterval: number | null = null;

  const handleAutoScroll = (clientX: number) => {
    if (!containerRef.current) return;

    const containerRect = containerRef.current.getBoundingClientRect();

    // Se o cursor estiver próximo da borda esquerda
    if (clientX < containerRect.left + autoScrollThreshold) {
      if (scrollInterval) return; // Já está rolando

      // Calcula a velocidade baseada na distância da borda
      const distance = clientX - containerRect.left;
      const speed = Math.max(5, (autoScrollThreshold - distance) / 3);

      scrollInterval = window.setInterval(() => {
        if (containerRef.current) {
          containerRef.current.scrollLeft -= speed;
        }
      }, 16) as unknown as number;
    }
    // Se o cursor estiver próximo da borda direita
    else if (clientX > containerRect.right - autoScrollThreshold) {
      if (scrollInterval) return; // Já está rolando

      // Calcula a velocidade baseada na distância da borda
      const distance = containerRect.right - clientX;
      const speed = Math.max(5, (autoScrollThreshold - distance) / 3);

      scrollInterval = window.setInterval(() => {
        if (containerRef.current) {
          containerRef.current.scrollLeft += speed;
        }
      }, 16) as unknown as number;
    }
    // Se o cursor estiver no meio, para o auto-scroll
    else {
      stopAutoScroll();
    }
  };

  const stopAutoScroll = () => {
    if (scrollInterval) {
      window.clearInterval(scrollInterval);
      scrollInterval = null;
    }
  };

  // Garantir que o auto-scroll seja interrompido quando o componente desmontar
  useEffect(() => {
    return () => {
      stopAutoScroll();
    };
  }, []);

  return (
    <div className="flex flex-col h-full">
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center space-x-2">
          <div className="relative w-64">
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Buscar precatórios..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            {searchTerm && (
              <Button
                variant="ghost"
                size="sm"
                className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
                onClick={() => setSearchTerm("")}
              >
                <FilterX className="h-4 w-4" />
              </Button>
            )}
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsFiltrosModalOpen(true)}
          >
            <Filter className="h-4 w-4 mr-2" />
            Filtros
            {Object.values(filtrosAvancados).some(
              (v) => v !== "" && v !== undefined && (Array.isArray(v) ? v.length > 0 : true)
            ) && (
              <Badge variant="secondary" className="ml-2">
                Ativos
              </Badge>
            )}
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsEstatisticasModalOpen(true)}
          >
            <BarChart className="h-4 w-4 mr-2" />
            Estatísticas
          </Button>
        </div>

        <Button onClick={handleNovoPrecatorio}>
          <Plus className="h-4 w-4 mr-2" />
          Novo Precatório
        </Button>
      </div>

      {Object.values(filtrosAvancados).some(
        (v) => v !== "" && v !== undefined && (Array.isArray(v) ? v.length > 0 : true)
      ) && (
        <Alert className="mb-4">
          <SlidersHorizontal className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            <span>
              Filtros avançados aplicados. {filteredPrecatorios.length} de {todosPrecatorios.length} precatórios exibidos.
            </span>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setFiltrosAvancados({
                  prioridade: "",
                  tribunal: "",
                  natureza: "",
                  valorMin: undefined,
                  valorMax: undefined,
                  dataVencimentoInicio: undefined,
                  dataVencimentoFim: undefined,
                  tags: [],
                  responsavel: "",
                });
              }}
            >
              <FilterX className="h-4 w-4 mr-2" />
              Limpar Filtros
            </Button>
          </AlertDescription>
        </Alert>
      )}

      <div
        ref={containerRef}
        className="flex-1 overflow-x-auto pb-4 relative"
        onMouseDown={handleMouseDown}
        onMouseUp={handleMouseUp}
        onMouseMove={handleMouseMove}
        onMouseLeave={handleMouseLeave}
        style={{ cursor: isDragging ? 'grabbing' : 'grab' }}
      >
        <DndContext
          sensors={sensors}
          onDragStart={handleDragStart}
          onDragEnd={handleDragEnd}
          onDragCancel={handleDragCancel}
          modifiers={modifiers}
          measuring={{
            droppable: {
              strategy: MeasuringStrategy.Always,
            },
          }}
        >
          <div className="flex gap-4 h-full">
            {colunas.map((coluna) => (
              <KanbanColuna
                key={coluna.id}
                coluna={coluna}
                precatorios={precatoriosPorStatus[coluna.id] || []}
                onVerDetalhes={handleVerDetalhes}
                onEdit={handleEditarPrecatorio}
                onDelete={handleDeletePrecatorio}
                onNovoPrecatorio={handleNovoPrecatorio}
              />
            ))}
          </div>

          <DragOverlay dropAnimation={dropAnimation}>
            {activePrecatorio ? (
              <div className="transform-gpu motion-safe:animate-in motion-safe:fade-in motion-safe:zoom-in-95 motion-safe:duration-150">
                <PrecatorioCard
                  precatorio={activePrecatorio}
                  onVerDetalhes={handleVerDetalhes}
                  onEdit={handleEditarPrecatorio}
                  onDelete={handleDeletePrecatorio}
                />
              </div>
            ) : null}
          </DragOverlay>
        </DndContext>
      </div>

      {/* Modais */}
      {isDetalhesModalOpen && precatorioSelecionado && (
        <DetalhesModal
          precatorio={precatorioSelecionado}
          isOpen={isDetalhesModalOpen}
          onOpenChange={setIsDetalhesModalOpen}
          onEdit={() => {
            setIsDetalhesModalOpen(false);
            handleEditarPrecatorio(precatorioSelecionado);
          }}
        />
      )}

      {isFormModalOpen && (
        <FormularioPrecatorio
          precatorio={modoEdicao ? precatorioSelecionado : undefined}
          isOpen={isFormModalOpen}
          onOpenChange={setIsFormModalOpen}
          modoEdicao={modoEdicao}
          onSave={(precatorio) => {
            onSavePrecatorio(precatorio);
            setIsFormModalOpen(false);
          }}
        />
      )}

      {isFiltrosModalOpen && (
        <FiltrosModal
          filtros={filtrosAvancados}
          isOpen={isFiltrosModalOpen}
          onOpenChange={setIsFiltrosModalOpen}
          onAplicarFiltros={(novosFiltros) => {
            setFiltrosAvancados(novosFiltros);
            setIsFiltrosModalOpen(false);
          }}
          onLimparFiltros={() => {
            setFiltrosAvancados({
              prioridade: "",
              tribunal: "",
              natureza: "",
              valorMin: undefined,
              valorMax: undefined,
              dataVencimentoInicio: undefined,
              dataVencimentoFim: undefined,
              tags: [],
              responsavel: "",
            });
          }}
        />
      )}

      {isEstatisticasModalOpen && (
        <EstatisticasModal
          stats={dashboardStats}
          isOpen={isEstatisticasModalOpen}
          onOpenChange={setIsEstatisticasModalOpen}
        />
      )}

      <AlertDialog open={isConfirmDeleteOpen} onOpenChange={setIsConfirmDeleteOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirmar exclusão</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir este precatório? Esta ação não pode ser desfeita.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDeletePrecatorio} className="bg-red-600 hover:bg-red-700">
              Excluir
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}