import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  Calendar,
  Clock,
  MoreHorizontal,
  Al<PERSON><PERSON>riangle,
  ClipboardList,
  FileText,
  User,
  Tag,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Precatorio, CORES_STATUS } from "./types";
import { useDraggable } from "@dnd-kit/core";
import { cn } from "@/lib/utils";
import { memo } from "react";

interface PrecatorioCardProps {
  precatorio: Precatorio;
  onVerDetalhes: (precatorio: Precatorio) => void;
  onEdit: (precatorio: Precatorio) => void;
  onDelete: (id: string) => void;
}

// Usando memo para evitar re-renderizações desnecessárias
export const PrecatorioCard = memo(function PrecatorioCard({
  precatorio,
  onVerDetalhes,
  onEdit,
  onDelete,
}: PrecatorioCardProps) {
  // Log mais detalhado para depurar os dados recebidos do banco
  console.log('PrecatorioCard recebeu dados:', {
    id: precatorio.id,
    numero: precatorio.numero, 
    valor: precatorio.valor,
    desconto: precatorio.desconto,
    status: precatorio.status,
    cliente: precatorio.cliente,
    tribunal: precatorio.tribunal,
    origem: "Diretamente do Supabase" // Marcador para identificar a origem dos dados
  });

  const {
    attributes,
    listeners,
    setNodeRef,
    isDragging,
    transform,
    transition,
  } = useDraggable({
    id: precatorio.id,
    data: {
      type: "precatorio",
      precatorio,
    },
  });

  const formatarValor = (valor: number) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(valor);
  };

  // Função segura para formatar datas
  const formatarDataSegura = (data: Date | string | undefined) => {
    if (!data) return "N/A";
    try {
      const dataObj = data instanceof Date ? data : new Date(data);
      if (isNaN(dataObj.getTime())) return "Data inválida";
      return format(dataObj, "dd/MM/yyyy", { locale: ptBR });
    } catch (error) {
      console.error("Erro ao formatar data:", error);
      return "Data inválida";
    }
  };

  const formattedDate = precatorio.dataVencimento
    ? formatarDataSegura(precatorio.dataVencimento)
    : "";

  // Calculamos o estilo com base no transform e transition
  const style = transform
    ? {
        transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
        transition,
        zIndex: isDragging ? 1000 : 1,
        // Adiciona uma sombra mais forte durante o arrasto
        boxShadow: isDragging ? '0 8px 16px rgba(0, 0, 0, 0.2)' : undefined,
      }
    : undefined;

  const corStatus = CORES_STATUS[precatorio.status as keyof typeof CORES_STATUS] || "gray";

  const getBadgeVariantForPrioridade = (prioridade: string) => {
    switch (prioridade) {
      case "alta":
        return "destructive";
      case "media":
        return "secondary";
      case "baixa":
        return "outline";
      default:
        return "secondary";
    }
  };

  return (
    <div
      ref={setNodeRef}
      {...attributes}
      {...listeners}
      data-draggable="card"
      className={cn(
        "precatorio-card bg-card border rounded-md p-3 shadow-sm hover:shadow-md",
        "relative cursor-grab active:cursor-grabbing",
        "transition-all duration-200 ease-in-out",
        isDragging ? "opacity-0" : "opacity-100 transform-none",
        !isDragging && "motion-safe:animate-in motion-safe:fade-in-50 motion-safe:slide-in-from-bottom-1 motion-safe:duration-300"
      )}
      style={style}
      onClick={() => onVerDetalhes(precatorio)}
    >
      <div className="flex justify-between items-start">
        <div>
          <h3 className="font-medium text-sm">{precatorio.numero}</h3>
          <p className="text-xs text-muted-foreground">{precatorio.tribunal}</p>
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              className="h-8 w-8 p-0"
              aria-label="Ações"
              onClick={(e) => e.stopPropagation()}
            >
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" onClick={(e) => e.stopPropagation()}>
            <DropdownMenuLabel>Ações</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={(e) => {
                e.stopPropagation();
                onVerDetalhes(precatorio);
              }}
            >
              <FileText className="mr-2 h-4 w-4" />
              Ver Detalhes
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={(e) => {
                e.stopPropagation();
                onEdit(precatorio);
              }}
            >
              <ClipboardList className="mr-2 h-4 w-4" />
              Editar
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              className="text-red-600 focus:text-red-600"
              onClick={(e) => {
                e.stopPropagation();
                onDelete(precatorio.id);
              }}
            >
              <AlertTriangle className="mr-2 h-4 w-4" />
              Excluir
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <div className="flex gap-1 items-center">
        <Badge
          variant={getBadgeVariantForPrioridade(precatorio.prioridade)}
          className="mr-1"
        >
          {precatorio.prioridade}
        </Badge>

        {precatorio.natureza && (
          <Badge variant="outline" className="text-xs">
            {precatorio.natureza}
          </Badge>
        )}
      </div>

      <div>
        <div className="flex items-center gap-1 mb-1">
          <span className="font-semibold">
            {formatarValor(precatorio.valor)}
          </span>
          {precatorio.desconto > 0 && (
            <Badge variant="outline" className="text-xs text-green-600">
              -{precatorio.desconto}%
            </Badge>
          )}
        </div>

        <div className="flex flex-col gap-1 text-xs text-muted-foreground">
          {precatorio.dataVencimento && (
            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              <span>Vence em {formattedDate}</span>
            </div>
          )}

          {precatorio.responsavel && precatorio.responsavel.nome && (
            <div className="flex items-center gap-1">
              <User className="h-3 w-3" />
              <span>{precatorio.responsavel.nome}</span>
            </div>
          )}

          <div className="flex items-center gap-1">
            <Clock className="h-3 w-3" />
            <span>
              Atualizado em{" "}
              {formatarDataSegura(
                precatorio.dataAtualizacao
              )}
            </span>
          </div>
        </div>
      </div>

      {precatorio.tags && precatorio.tags.length > 0 && (
        <div className="flex flex-wrap gap-1 mt-2">
          {precatorio.tags.map((tag) => (
            <div
              key={tag}
              className="flex items-center text-xs px-2 py-0.5 rounded-full bg-neutral-100 dark:bg-neutral-700 text-neutral-600 dark:text-neutral-300"
            >
              <Tag className="h-2.5 w-2.5 mr-1" />
              {tag}
            </div>
          ))}
        </div>
      )}

      <div
        className="absolute inset-x-0 bottom-0 h-1 rounded-b-md"
        style={{ backgroundColor: corStatus }}
      />
    </div>
  );
});