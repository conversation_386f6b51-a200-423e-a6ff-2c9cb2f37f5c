import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Check,
  ChevronDown,
  FileText,
  Filter,
  Info,
  Loader2,
  Plus,
  PlusCircle,
  Search,
  SlidersHorizontal,
  User,
  UserPlus,
  UserX,
  X,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { supabase } from "@/lib/supabase";
import { formatDate, formatCurrency } from "@/lib/utils";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

const PrecatoriosManagement = () => {
  const navigate = useNavigate();
  const [loadingPrecatorios, setLoadingPrecatorios] = useState(true);
  const [precatorios, setPrecatorios] = useState([]);
  const [filteredPrecatorios, setFilteredPrecatorios] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [prioridadeFilter, setPrioridadeFilter] = useState("");
  const [clienteFilter, setClienteFilter] = useState("");
  const [tribunalFilter, setTribunalFilter] = useState("");
  const [showFiltersDialog, setShowFiltersDialog] = useState(false);
  const [clientes, setClientes] = useState([]);
  const [tribunais, setTribunais] = useState([]);
  const [showNewPrecatorioModal, setShowNewPrecatorioModal] = useState(false);
  const [newPrecatorioData, setNewPrecatorioData] = useState({
    numero_precatorio: "",
    beneficiario: { nome: "", cpf: "", email: "", telefone: "" },
    tribunal: { nome: "" },
    status: "Novo",
    valor_total: "",
    prioridade: "normal",
    entidade_devedora: "",
    natureza: "",
    data_previsao_pagamento: "",
  });
  const [clienteSearch, setClienteSearch] = useState("");
  const [clientesEncontrados, setClientesEncontrados] = useState([]);
  const [clienteSelecionado, setClienteSelecionado] = useState(null);
  const [novoCliente, setNovoCliente] = useState({
    nome: "",
    cpf: "",
    email: "",
    telefone: "",
    endereco: ""
  });
  const [criandoCliente, setCriandoCliente] = useState(false);
  const [criandoPrecatorio, setCriandoPrecatorio] = useState(false);
  const [tabAtivo, setTabAtivo] = useState("precatorio");
  const [loadingClientes, setLoadingClientes] = useState(false);
  const [tipoSelecionado, setTipoSelecionado] = useState<'PRECATORIO' | 'RPV'>('PRECATORIO');
  const [precatoriosStats, setPrecatoriosStats] = useState({
    total: 0,
    aguardandoPagamento: 0,
    emProcessamento: 0,
    pagos: 0,
    suspensos: 0,
    valorTotal: 0,
  });

  useEffect(() => {
    fetchPrecatorios();
    fetchClientesETribunais();
  }, []);

  useEffect(() => {
    if (searchQuery || statusFilter || prioridadeFilter || clienteFilter || tribunalFilter) {
      filterPrecatorios();
    } else {
      setFilteredPrecatorios(precatorios);
    }
  }, [searchQuery, statusFilter, prioridadeFilter, clienteFilter, tribunalFilter, precatorios]);

  // Função para buscar precatórios do Supabase
  const fetchPrecatorios = async (statusFilter = null) => {
    try {
      setLoadingPrecatorios(true);
      console.log("Buscando precatórios...");

      // Criar consulta base
      let query = supabase.from("precatorios").select(`
        id,
        numero_precatorio,
        status,
        data_entrada,
        data_previsao_pagamento,
        valor_total,
        prioridade,
        entidade_devedora,
        beneficiario_id,
        tribunal_id,
        prazo_atual,
        natureza
      `);

      // Aplicar filtro por status, se fornecido
      if (statusFilter) {
        query = query.eq("status", statusFilter);
      }

      // Executar consulta
      const { data: precatoriosData, error: precatoriosError } = await query;

      if (precatoriosError) {
        console.error("Erro ao buscar precatórios:", precatoriosError);
        toast.error("Erro ao carregar precatórios");
        setLoadingPrecatorios(false);
        return;
      }

      // Verificar se temos dados válidos
      if (!precatoriosData || !Array.isArray(precatoriosData)) {
        console.error("Dados de precatórios inválidos:", precatoriosData);
        toast.error("Erro ao carregar precatórios");
        setLoadingPrecatorios(false);
        return;
      }

      // Buscar clientes e tribunais relacionados
      const clientesIds = precatoriosData
        .filter((p) => p && p.beneficiario_id)
        .map((p) => p.beneficiario_id);

      const tribunaisIds = precatoriosData
        .filter((p) => p && p.tribunal_id)
        .map((p) => p.tribunal_id);

      // Buscar dados de clientes
      const { data: clientesData } = await supabase
        .from("clientes")
        .select("id, nome, cpf, email, telefone")
        .in("id", clientesIds.length > 0 ? clientesIds : [0]);

      // Buscar dados de tribunais
      const { data: tribunaisData } = await supabase
        .from("tribunais")
        .select("id, nome, cidade, estado, tipo")
        .in("id", tribunaisIds.length > 0 ? tribunaisIds : [0]);

      // Garantir que os dados relacionados são arrays
      const clientesArray = Array.isArray(clientesData) ? clientesData : [];
      const tribunaisArray = Array.isArray(tribunaisData) ? tribunaisData : [];

      // Associar clientes e tribunais aos precatórios
      const precatoriosFormatados = precatoriosData.map((precatorio) => {
        const cliente = clientesArray.find((b) => b && b.id === precatorio.beneficiario_id) || { nome: "Não informado" };
        const tribunal = tribunaisArray.find((t) => t && t.id === precatorio.tribunal_id) || { nome: "Não informado" };

        return {
          ...precatorio,
          cliente,
          tribunal,
        };
      });

      // Calcular estatísticas com verificação de segurança
      const stats = {
        total: precatoriosFormatados.length,
        aguardandoPagamento: precatoriosFormatados.filter((p) => p && p.status === "Aguardando Pagamento").length,
        emProcessamento: precatoriosFormatados.filter((p) => p && (p.status === "Em Análise" || p.status === "Em Processamento")).length,
        pagos: precatoriosFormatados.filter((p) => p && p.status === "Pago").length,
        suspensos: precatoriosFormatados.filter((p) => p && p.status === "Suspenso").length,
        valorTotal: precatoriosFormatados.reduce((total, p) => {
          if (!p || !p.valor_total) return total;
          const valor = parseFloat(p.valor_total);
          return isNaN(valor) ? total : total + valor;
        }, 0),
      };

      setPrecatoriosStats(stats);
      setPrecatorios(precatoriosFormatados);
      setFilteredPrecatorios(precatoriosFormatados);
    } catch (error) {
      console.error("Erro ao buscar precatórios:", error);
      toast.error("Erro ao carregar precatórios");
    } finally {
      setLoadingPrecatorios(false);
    }
  };

  // Função para buscar clientes e tribunais para os filtros
  const fetchClientesETribunais = async () => {
    try {
      // Buscar clientes
      const { data: clientesData, error: clientesError } = await supabase
        .from("clientes")
        .select("id, nome")
        .order("nome");

      if (clientesError) {
        console.error("Erro ao buscar clientes:", clientesError);
      } else {
        setClientes(clientesData || []);
      }

      // Buscar tribunais
      const { data: tribunaisData, error: tribunaisError } = await supabase
        .from("tribunais")
        .select("id, nome")
        .order("nome");

      if (tribunaisError) {
        console.error("Erro ao buscar tribunais:", tribunaisError);
      } else {
        setTribunais(tribunaisData || []);
      }
    } catch (error) {
      console.error("Erro ao buscar dados para filtros:", error);
    }
  };

  // Filtrar precatórios com base na pesquisa e filtro de status
  const filterPrecatorios = () => {
    try {
      if (!Array.isArray(precatorios)) {
        console.error("Erro: precatorios não é um array", precatorios);
        setFilteredPrecatorios([]);
        return;
      }

      let filtered = [...precatorios];

      // Filtrar por texto de pesquisa
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        filtered = filtered.filter((precatorio) =>
          (precatorio.numero_precatorio && precatorio.numero_precatorio.toLowerCase().includes(query)) ||
          (precatorio.cliente?.nome && precatorio.cliente.nome.toLowerCase().includes(query)) ||
          (precatorio.tribunal?.nome && precatorio.tribunal.nome.toLowerCase().includes(query)) ||
          (precatorio.entidade_devedora && precatorio.entidade_devedora.toLowerCase().includes(query))
        );
      }

      // Filtrar por status
      if (statusFilter && statusFilter !== "all") {
        filtered = filtered.filter((precatorio) => precatorio.status === statusFilter);
      }

      // Filtrar por prioridade
      if (prioridadeFilter) {
        filtered = filtered.filter((precatorio) => precatorio.prioridade === prioridadeFilter);
      }

      // Filtrar por cliente
      if (clienteFilter) {
        filtered = filtered.filter((precatorio) =>
          precatorio.cliente && precatorio.cliente.nome === clienteFilter
        );
      }

      // Filtrar por tribunal
      if (tribunalFilter) {
        filtered = filtered.filter((precatorio) =>
          precatorio.tribunal && precatorio.tribunal.nome === tribunalFilter
        );
      }

      setFilteredPrecatorios(filtered);
    } catch (error) {
      console.error("Erro ao filtrar precatórios:", error);
      setFilteredPrecatorios([]);
    }
  };

  // Função para lidar com mudanças no novo precatório
  const handleNewPrecatorioChange = (field, value) => {
    if (field.includes(".")) {
      const [parent, child] = field.split(".");
      setNewPrecatorioData({
        ...newPrecatorioData,
        [parent]: {
          ...newPrecatorioData[parent],
          [child]: value,
        },
      });
    } else {
      setNewPrecatorioData({
        ...newPrecatorioData,
        [field]: value,
      });
    }
  };

  // Handler para abrir o modal de novo precatório
  const handleOpenNewPrecatorioModal = () => {
    setNewPrecatorioData({
      numero_precatorio: "",
      beneficiario: { nome: "", cpf: "", email: "", telefone: "" },
      tribunal: { nome: "" },
      status: "Novo",
      valor_total: "",
      prioridade: "normal",
      entidade_devedora: "",
      natureza: "",
      data_previsao_pagamento: "",
    });
    setClienteSelecionado(null);
    setClienteSearch("");
    setClientesEncontrados([]);
    setShowNewPrecatorioModal(true);
    setTipoSelecionado('PRECATORIO'); // Resetar para Precatório por padrão
  };

  // Função para visualizar detalhes do precatório
  const handleViewPrecatorio = (id) => {
    navigate(`/precatorios/${id}`);
  };

  // Função para buscar clientes no Supabase
  const buscarClientes = async () => {
    if (!clienteSearch.trim()) return;

    try {
      setLoadingClientes(true);
      console.log('Buscando clientes com termo:', clienteSearch);

      const { data, error } = await supabase
        .from('clientes')
        .select('*')
        .or(`nome.ilike.%${clienteSearch}%,cpf_cnpj.ilike.%${clienteSearch}%,email.ilike.%${clienteSearch}%`)
        .limit(10);

      if (error) {
        console.error('Erro ao buscar clientes:', error);
        toast.error('Erro ao buscar clientes. Tente novamente.');
        return;
      }

      console.log('Clientes encontrados:', data);
      setClientesEncontrados(Array.isArray(data) ? data : []);
    } catch (err) {
      console.error('Erro ao buscar clientes:', err);
      toast.error('Erro ao buscar clientes. Tente novamente.');
    } finally {
      setLoadingClientes(false);
    }
  };

  // Função para selecionar um cliente
  const selecionarCliente = (cliente) => {
    if (!cliente) return;

    console.log('Cliente selecionado:', cliente);
    setClienteSelecionado(cliente);
    setNewPrecatorioData({
      ...newPrecatorioData,
      cliente: {
        nome: cliente.nome || "",
        cpf: cliente.cpf_cnpj || "",
        email: cliente.email || "",
        telefone: cliente.telefone || ""
      }
    });
  };

  // Função para criar um novo cliente
  const criarNovoCliente = async () => {
    if (!novoCliente.nome || !novoCliente.cpf) {
      toast.error('Nome e CPF são obrigatórios para criar um novo cliente.');
      return;
    }

    try {
      setCriandoCliente(true);
      console.log('Criando novo cliente:', novoCliente);

      const { data, error } = await supabase
        .from('clientes')
        .insert([
          {
            nome: novoCliente.nome.trim(),
            cpf_cnpj: novoCliente.cpf.trim(),
            email: novoCliente.email.trim(),
            telefone: novoCliente.telefone.trim(),
            endereco: novoCliente.endereco.trim()
          }
        ])
        .select();

      if (error) {
        console.error('Erro ao criar cliente:', error);
        toast.error('Erro ao criar cliente. Tente novamente.');
        return;
      }

      if (data && data.length > 0) {
        const novoClienteCriado = data[0];
        toast.success('Cliente criado com sucesso!');

        // Seleciona o cliente recém-criado
        selecionarCliente(novoClienteCriado);

        // Resetar formulário
        setNovoCliente({ nome: "", cpf: "", email: "", telefone: "", endereco: "" });

        // Mudar para a aba de dados do precatório
        setTabAtivo("precatorio");
      }
    } catch (err) {
      console.error('Erro ao criar cliente:', err);
      toast.error('Erro ao criar cliente. Tente novamente.');
    } finally {
      setCriandoCliente(false);
    }
  };

  // Função para criar um novo precatório
  const criarPrecatorio = async () => {
    if (!newPrecatorioData.numero_precatorio ||
        !newPrecatorioData.tribunal.nome ||
        !clienteSelecionado ||
        !newPrecatorioData.valor_total) {
      toast.error('Preencha todos os campos obrigatórios.');
      return;
    }

    try {
      setCriandoPrecatorio(true);

      // Criar novo precatório
      console.log('Criando novo precatório:', {
        numero_precatorio: newPrecatorioData.numero_precatorio,
        beneficiario_id: clienteSelecionado.id,
        tribunal_id: newPrecatorioData.tribunal.nome,
        status: newPrecatorioData.status,
        valor_total: parseFloat(newPrecatorioData.valor_total),
        prioridade: newPrecatorioData.prioridade,
        entidade_devedora: newPrecatorioData.entidade_devedora,
        natureza: newPrecatorioData.natureza,
        data_previsao_pagamento: newPrecatorioData.data_previsao_pagamento || null,
        tipo: tipoSelecionado,
        categoria: tipoSelecionado
      });

      const { data, error } = await supabase
        .from('precatorios')
        .insert([
          {
            numero_precatorio: newPrecatorioData.numero_precatorio,
            beneficiario_id: clienteSelecionado.id,
            tribunal_id: newPrecatorioData.tribunal.nome, // Idealmente, isso seria um ID, não o nome
            status: newPrecatorioData.status,
            valor_total: parseFloat(newPrecatorioData.valor_total),
            prioridade: newPrecatorioData.prioridade,
            entidade_devedora: newPrecatorioData.entidade_devedora,
            natureza: newPrecatorioData.natureza,
            data_previsao_pagamento: newPrecatorioData.data_previsao_pagamento || null,
            tipo: tipoSelecionado, // Adicionar o tipo (PRECATORIO ou RPV)
            categoria: tipoSelecionado // Manter consistente com o tipo
          }
        ])
        .select();

      if (error) {
        console.error('Erro ao criar precatório:', error);
        toast.error('Erro ao criar precatório. Tente novamente.');
        return;
      }

      toast.success(tipoSelecionado === 'PRECATORIO' ? 'Precatório criado com sucesso!' : 'RPV criado com sucesso!');
      setShowNewPrecatorioModal(false);
      fetchPrecatorios();

    } catch (error) {
      console.error('Erro ao criar precatório:', error);
      toast.error('Ocorreu um erro inesperado. Tente novamente.');
    } finally {
      setCriandoPrecatorio(false);
    }
  };

  // Renderizar cards de estatísticas
  const renderStatCards = () => (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total de Precatórios</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{precatoriosStats.total}</div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Aguardando Pagamento</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{precatoriosStats.aguardandoPagamento}</div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Em Processamento</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{precatoriosStats.emProcessamento}</div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Valor Total</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {new Intl.NumberFormat("pt-BR", { style: "currency", currency: "BRL" }).format(
              precatoriosStats.valorTotal
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Gerenciamento de Precatórios</h1>
        <div className="flex items-center gap-2 ml-auto">
          <Button
            onClick={handleOpenNewPrecatorioModal}
            className="gap-2"
          >
            <Plus className="h-4 w-4" />
            Novo Precatório
          </Button>
        </div>
      </div>

      {renderStatCards()}

      <div className="space-y-4">
        <div className="flex justify-between mb-4">
          <div className="flex gap-2">
            <Input
              placeholder="Buscar precatórios..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-64"
            />
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value=" ">Todos</SelectItem>
                <SelectItem value="Novo">Novo</SelectItem>
                <SelectItem value="Em Análise">Em Análise</SelectItem>
                <SelectItem value="Em Processamento">Em Processamento</SelectItem>
                <SelectItem value="Aguardando Pagamento">Aguardando Pagamento</SelectItem>
                <SelectItem value="Pago">Pago</SelectItem>
                <SelectItem value="Suspenso">Suspenso</SelectItem>
              </SelectContent>
            </Select>
            <Select value={prioridadeFilter} onValueChange={setPrioridadeFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Prioridade" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value=" ">Todas</SelectItem>
                <SelectItem value="alta">Alta</SelectItem>
                <SelectItem value="media">Média</SelectItem>
                <SelectItem value="normal">Normal</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" onClick={() => setShowFiltersDialog(true)}>
              <Filter className="h-4 w-4 mr-2" />
              Mais filtros
            </Button>
            <Button variant="outline" onClick={() => {
              setSearchQuery("");
              setStatusFilter(" ");
              setPrioridadeFilter(" ");
              setClienteFilter(" ");
              setTribunalFilter(" ");
              setFilteredPrecatorios(precatorios);
            }}>
              <X className="h-4 w-4 mr-2" />
              Limpar filtros
            </Button>
          </div>
        </div>

        <Card>
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Número do Precatório</TableHead>
                  <TableHead>Cliente</TableHead>
                  <TableHead>Tribunal</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Valor</TableHead>
                  <TableHead>Data de Entrada</TableHead>
                  <TableHead>Prioridade</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loadingPrecatorios ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-4">
                      <div className="flex justify-center">
                        <Loader2 className="h-6 w-6 animate-spin text-primary" />
                      </div>
                      <p className="mt-2 text-sm text-muted-foreground">Carregando precatórios...</p>
                    </TableCell>
                  </TableRow>
                ) : filteredPrecatorios.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-4">
                      <p className="text-sm text-muted-foreground">Nenhum precatório encontrado.</p>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredPrecatorios.map((precatorio) => (
                    <TableRow
                      key={precatorio.id}
                      className="cursor-pointer hover:bg-muted"
                      onClick={() => handleViewPrecatorio(precatorio.id)}
                    >
                      <TableCell className="font-medium">{precatorio.numero_precatorio}</TableCell>
                      <TableCell>{precatorio.cliente?.nome}</TableCell>
                      <TableCell>{precatorio.tribunal?.nome}</TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            precatorio.status === "Pago"
                              ? "success"
                              : precatorio.status === "Aguardando Pagamento"
                                ? "warning"
                                : precatorio.status === "Suspenso"
                                  ? "destructive"
                                  : "default"
                          }
                        >
                          {precatorio.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {typeof precatorio.valor_total === "number"
                          ? new Intl.NumberFormat("pt-BR", { style: "currency", currency: "BRL" }).format(
                              precatorio.valor_total
                            )
                          : "R$ 0,00"}
                      </TableCell>
                      <TableCell>
                        {precatorio.data_entrada
                          ? new Date(precatorio.data_entrada).toLocaleDateString("pt-BR")
                          : "-"}
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            precatorio.prioridade === "alta"
                              ? "destructive"
                              : precatorio.prioridade === "media"
                                ? "warning"
                                : "outline"
                          }
                        >
                          {precatorio.prioridade === "alta"
                            ? "Alta"
                            : precatorio.prioridade === "media"
                              ? "Média"
                              : "Normal"}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>

      <Dialog open={showNewPrecatorioModal} onOpenChange={setShowNewPrecatorioModal}>
        <DialogContent className="sm:max-w-[700px]" aria-describedby="dialog-description">
          <span id="dialog-description" className="sr-only">
            Pressione Escape para fechar este diálogo.
          </span>
          <DialogHeader>
            <DialogTitle className="text-xl font-bold flex items-center gap-2">
              <FileText className="w-5 h-5" />
              Novo Precatório
            </DialogTitle>
            <DialogDescription>
              Preencha os dados para criar um novo precatório no sistema.
            </DialogDescription>
          </DialogHeader>

          <Tabs value={tabAtivo} onValueChange={setTabAtivo} className="mt-2">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="precatorio" className="flex items-center gap-2">
                <FileText className="w-4 h-4" />
                Dados do Precatório
              </TabsTrigger>
              <TabsTrigger value="cliente" className="flex items-center gap-2">
                <User className="w-4 h-4" />
                Dados do Cliente
              </TabsTrigger>
            </TabsList>

            <TabsContent value="precatorio" className="space-y-4 mt-4">
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="tipo" className="text-right">
                    Tipo
                  </Label>
                  <Select
                    value={tipoSelecionado}
                    onValueChange={(value) => setTipoSelecionado(value as 'PRECATORIO' | 'RPV')}
                  >
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Selecione o tipo" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="PRECATORIO">Precatório</SelectItem>
                      <SelectItem value="RPV">RPV</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="numero" className="text-right">
                    Número do {tipoSelecionado === 'PRECATORIO' ? 'Precatório' : 'RPV'}
                  </Label>
                  <Input
                    id="numero"
                    value={newPrecatorioData.numero_precatorio}
                    onChange={(e) => handleNewPrecatorioChange('numero_precatorio', e.target.value)}
                    className="col-span-3"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="tribunal">Tribunal</Label>
                  <Select
                    value={newPrecatorioData.tribunal.nome}
                    onValueChange={value => setNewPrecatorioData({
                      ...newPrecatorioData,
                      tribunal: { nome: value }
                    })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione o tribunal" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="TJSP">Tribunal de Justiça de São Paulo</SelectItem>
                      <SelectItem value="TRF3">Tribunal Regional Federal da 3ª Região</SelectItem>
                      <SelectItem value="TRT2">Tribunal Regional do Trabalho da 2ª Região</SelectItem>
                      <SelectItem value="TJRJ">Tribunal de Justiça do Rio de Janeiro</SelectItem>
                      <SelectItem value="TRF1">Tribunal Regional Federal da 1ª Região</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="entidade_devedora">Entidade Devedora</Label>
                  <Input
                    id="entidade_devedora"
                    placeholder="Ex: Estado de São Paulo"
                    value={newPrecatorioData.entidade_devedora}
                    onChange={e => setNewPrecatorioData({
                      ...newPrecatorioData,
                      entidade_devedora: e.target.value
                    })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="valor_total">Valor Total (R$)</Label>
                  <Input
                    id="valor_total"
                    type="text"
                    placeholder="Ex: 50000.00"
                    value={newPrecatorioData.valor_total}
                    onChange={e => {
                      const value = e.target.value.replace(/[^0-9.]/g, '');
                      setNewPrecatorioData({
                        ...newPrecatorioData,
                        valor_total: value
                      });
                    }}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="natureza">Natureza</Label>
                  <Select
                    value={newPrecatorioData.natureza || ""}
                    onValueChange={value => setNewPrecatorioData({
                      ...newPrecatorioData,
                      natureza: value
                    })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione a natureza" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Alimentar">Alimentar</SelectItem>
                      <SelectItem value="Comum">Comum</SelectItem>
                      <SelectItem value="Honorários">Honorários</SelectItem>
                      <SelectItem value="Trabalhista">Trabalhista</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="prioridade">Prioridade</Label>
                  <Select
                    value={newPrecatorioData.prioridade}
                    onValueChange={value => setNewPrecatorioData({
                      ...newPrecatorioData,
                      prioridade: value
                    })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione a prioridade" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="alta">Alta</SelectItem>
                      <SelectItem value="normal">Normal</SelectItem>
                      <SelectItem value="baixa">Baixa</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="status">Status</Label>
                  <Select
                    value={newPrecatorioData.status}
                    onValueChange={value => setNewPrecatorioData({
                      ...newPrecatorioData,
                      status: value
                    })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione o status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Novo">Novo</SelectItem>
                      <SelectItem value="Em Andamento">Em Andamento</SelectItem>
                      <SelectItem value="Em Análise">Em Análise</SelectItem>
                      <SelectItem value="Pagamento Agendado">Pagamento Agendado</SelectItem>
                      <SelectItem value="Pago">Pago</SelectItem>
                      <SelectItem value="Cancelado">Cancelado</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="data_previsao_pagamento">Data de Previsão de Pagamento</Label>
                  <Input
                    id="data_previsao_pagamento"
                    type="date"
                    value={newPrecatorioData.data_previsao_pagamento || ""}
                    onChange={e => setNewPrecatorioData({
                      ...newPrecatorioData,
                      data_previsao_pagamento: e.target.value
                    })}
                  />
                </div>
              </div>

              {/* Campo adicional específico para RPV */}
              {tipoSelecionado === 'RPV' && (
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="valor_limite" className="text-right">
                    Valor Limite RPV
                  </Label>
                  <Input
                    id="valor_limite"
                    type="number"
                    className="col-span-3"
                    onChange={(e) => handleNewPrecatorioChange('valor_limite_rpv', e.target.value)}
                  />
                </div>
              )}

              <Card className="mt-2">
                <CardHeader className="py-3">
                  <CardTitle className="text-md flex items-center gap-2">
                    <Info className="w-4 h-4 text-muted-foreground" />
                    Detalhes do Cliente Selecionado
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0 pb-3">
                  {clienteSelecionado ? (
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground mb-1">Nome do Cliente</p>
                        <p className="text-md font-medium">{clienteSelecionado.nome}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-muted-foreground mb-1">CPF</p>
                        <p className="text-md font-medium">{clienteSelecionado.cpf}</p>
                      </div>
                    </div>
                  ) : (
                    <p className="text-sm text-muted-foreground italic">
                      Nenhum cliente selecionado. Por favor, selecione ou crie um cliente na aba "Dados do Cliente".
                    </p>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="cliente" className="mt-4">
              <Tabs defaultValue="buscar" className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="buscar" className="flex items-center gap-2">
                    <Search className="w-4 h-4" />
                    Buscar Cliente Existente
                  </TabsTrigger>
                  <TabsTrigger value="criar" className="flex items-center gap-2">
                    <PlusCircle className="w-4 h-4" />
                    Criar Novo Cliente
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="buscar" className="mt-4 space-y-4">
                  <div className="flex items-center gap-2">
                    <div className="flex-1">
                      <Input
                        placeholder="Buscar por nome, CPF ou email..."
                        value={clienteSearch}
                        onChange={e => setClienteSearch(e.target.value)}
                        className="w-full"
                      />
                    </div>
                    <Button
                      variant="secondary"
                      size="icon"
                      onClick={buscarClientes}
                    >
                      <Search className="w-4 h-4" />
                    </Button>
                  </div>

                  {loadingClientes ? (
                    <div className="flex justify-center p-4">
                      <Loader2 className="w-6 h-6 text-primary animate-spin" />
                    </div>
                  ) : (
                    <div className="border rounded-md overflow-hidden">
                      {clientesEncontrados.length > 0 ? (
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Nome</TableHead>
                              <TableHead>CPF</TableHead>
                              <TableHead>Contato</TableHead>
                              <TableHead className="text-right">Ações</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {clientesEncontrados.map((cliente) => (
                              <TableRow key={cliente.id} className={cliente.id === clienteSelecionado?.id ? "bg-muted" : ""}>
                                <TableCell className="font-medium">{cliente.nome}</TableCell>
                                <TableCell>{cliente.cpf_cnpj}</TableCell>
                                <TableCell>
                                  <div className="flex flex-col text-xs">
                                    <span>{cliente.email}</span>
                                    <span>{cliente.telefone}</span>
                                  </div>
                                </TableCell>
                                <TableCell className="text-right">
                                  <Button
                                    variant={cliente.id === clienteSelecionado?.id ? "default" : "ghost"}
                                    size="sm"
                                    onClick={() => selecionarCliente(cliente)}
                                  >
                                    {cliente.id === clienteSelecionado?.id ? (
                                      <>
                                        <Check className="w-4 h-4 mr-1" />
                                        Selecionado
                                      </>
                                    ) : (
                                      "Selecionar"
                                    )}
                                  </Button>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      ) : clienteSearch && !loadingClientes ? (
                        <div className="text-center p-6 space-y-2">
                          <UserX className="w-8 h-8 text-muted-foreground mx-auto" />
                          <p className="text-lg font-medium">Nenhum cliente encontrado</p>
                          <p className="text-sm text-muted-foreground mb-2">Tente com outro termo ou crie um novo cliente.</p>
                          <Button variant="outline" onClick={() => document.querySelector('[data-value="criar"]')?.click()}>
                            Criar Novo Cliente
                          </Button>
                        </div>
                      ) : (
                        <div className="text-center p-6 space-y-2">
                          <Search className="w-8 h-8 text-muted-foreground mx-auto" />
                          <p className="text-lg font-medium">Busque por um cliente</p>
                          <p className="text-sm text-muted-foreground">Digite um nome, CPF ou email para buscar clientes existentes.</p>
                        </div>
                      )}
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="criar" className="mt-4 space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="nome_cliente">Nome Completo*</Label>
                      <Input
                        id="nome_cliente"
                        placeholder="Nome completo do cliente"
                        value={novoCliente.nome}
                        onChange={e => setNovoCliente({...novoCliente, nome: e.target.value})}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="cpf_cliente">CPF*</Label>
                      <Input
                        id="cpf_cliente"
                        placeholder="000.000.000-00"
                        value={novoCliente.cpf}
                        onChange={e => {
                          const value = e.target.value.replace(/\D/g, '');
                          setNovoCliente({...novoCliente, cpf: value});
                        }}
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="email_cliente">Email</Label>
                      <Input
                        id="email_cliente"
                        type="email"
                        placeholder="<EMAIL>"
                        value={novoCliente.email}
                        onChange={e => setNovoCliente({...novoCliente, email: e.target.value})}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="telefone_cliente">Telefone</Label>
                      <Input
                        id="telefone_cliente"
                        placeholder="(00) 00000-0000"
                        value={novoCliente.telefone}
                        onChange={e => {
                          const value = e.target.value.replace(/\D/g, '');
                          setNovoCliente({...novoCliente, telefone: value});
                        }}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="endereco_cliente">Endereço</Label>
                    <Textarea
                      id="endereco_cliente"
                      placeholder="Endereço completo do cliente"
                      value={novoCliente.endereco || ""}
                      onChange={e => setNovoCliente({...novoCliente, endereco: e.target.value})}
                      rows={3}
                    />
                  </div>

                  <div className="flex justify-end gap-2 mt-2">
                    <Button
                      variant="default"
                      disabled={!novoCliente.nome || !novoCliente.cpf || criandoCliente}
                      onClick={criarNovoCliente}
                    >
                      {criandoCliente ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          Criando...
                        </>
                      ) : (
                        <>
                          <UserPlus className="w-4 h-4 mr-2" />
                          Criar e Selecionar
                        </>
                      )}
                    </Button>
                  </div>
                </TabsContent>
              </Tabs>
            </TabsContent>
          </Tabs>

          <DialogFooter className="mt-4 gap-2">
            <Button
              variant="outline"
              onClick={() => setShowNewPrecatorioModal(false)}
            >
              Cancelar
            </Button>
            <Button
              onClick={criarPrecatorio}
              disabled={
                !newPrecatorioData.numero_precatorio ||
                !newPrecatorioData.tribunal.nome ||
                !clienteSelecionado ||
                !newPrecatorioData.valor_total ||
                criandoPrecatorio
              }
            >
              {criandoPrecatorio ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Criando...
                </>
              ) : (
                <>
                  <Plus className="w-4 h-4 mr-2" />
                  Criar Precatório
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={showFiltersDialog} onOpenChange={setShowFiltersDialog}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Filtros adicionais</DialogTitle>
            <DialogDescription>
              Selecione os filtros adicionais para aplicar na lista de precatórios.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="clienteFilter">Cliente</Label>
              <Select
                value={clienteFilter}
                onValueChange={setClienteFilter}
              >
                <SelectTrigger id="clienteFilter">
                  <SelectValue placeholder="Selecione o cliente" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value=" ">Todos</SelectItem>
                  {clientes.map((cliente) => (
                    <SelectItem key={cliente.id} value={cliente.nome}>
                      {cliente.nome}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="tribunalFilter">Tribunal</Label>
              <Select
                value={tribunalFilter}
                onValueChange={setTribunalFilter}
              >
                <SelectTrigger id="tribunalFilter">
                  <SelectValue placeholder="Selecione o tribunal" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value=" ">Todos</SelectItem>
                  {tribunais.map((tribunal) => (
                    <SelectItem key={tribunal.id} value={tribunal.nome}>
                      {tribunal.nome}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowFiltersDialog(false)}>
              Cancelar
            </Button>
            <Button onClick={() => setShowFiltersDialog(false)}>Aplicar filtros</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default PrecatoriosManagement;