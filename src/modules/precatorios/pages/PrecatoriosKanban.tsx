import { useState, useEffect } from "react";
import { useNavigate, Link } from "react-router-dom";
import { 
  KanbanContainer, 
  Precatorio, 
  KanbanColuna as KanbanColunaType 
} from "@/components/Precatorios";
import { GerenciadorColunas } from "@/components/Precatorios/KanbanConfig/GerenciadorColunas";
import { 
  buscarTodosPrecatorios, 
  salvarPrecatorio, 
  excluirPrecatorio, 
  atualizarStatusPrecatorio,
  limparCachePrecatorios
} from "@/services/precatoriosService";
import { 
  buscarColunasPorTipo,
  atualizarContagemColunas
} from "@/services/kanbanColunasService";
import { Loader2, Settings, Plus, X } from "lucide-react";
import { toast } from "sonner";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/contexts/AuthContext";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

function PrecatoriosKanban() {
  const [precatorios, setPrecatorios] = useState<Precatorio[]>([]);
  const [colunas, setColunas] = useState<KanbanColunaType[]>([]);
  const [loading, setLoading] = useState(true);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [tipoVisualizacao, setTipoVisualizacao] = useState<'PRECATORIO' | 'RPV'>('PRECATORIO');
  const [gerenciadorAberto, setGerenciadorAberto] = useState(false);
  const navigate = useNavigate();
  const { isAdmin } = useAuth();
  const [isMounted, setIsMounted] = useState(true);

  // Função para carregar dados com retry e melhor sincronização
  const carregarDados = async (retry = 0) => {
      try {
        if (!isMounted) return;
        
        setLoading(true);
        setErrorMessage(null);
        
        const tipoLabel = tipoVisualizacao === 'PRECATORIO' ? 'precatórios' : 'RPVs';
        console.log(`[PrecatoriosKanban] Iniciando carregamento de dados do Kanban para ${tipoLabel}...`);
        
        // Carregar precatórios e colunas em paralelo com melhor tratamento de erros
        let precatoriosData: Precatorio[] = [];
        let colunasData: KanbanColunaType[] = [];
        
        try {
          // Primeiro, garantir que temos as colunas
          colunasData = await buscarColunasPorTipo(tipoVisualizacao);
          console.log(`${colunasData.length} colunas para ${tipoLabel} carregadas`);
          
          // Log para facilitar debug
          colunasData.forEach((col, index) => {
            console.log(`Coluna ${index+1}: ${col.name} (ID: ${col.status_id})`);
          });
          
          // Depois, carregamos os precatórios
          precatoriosData = await buscarTodosPrecatorios(tipoVisualizacao);
          console.log(`${precatoriosData.length} ${tipoLabel} carregados`);
        } catch (loadError: any) {
          console.error(`Erro ao carregar dados do Supabase:`, loadError);
          
          // Se falhar e ainda não atingimos o máximo de tentativas
          if (retry < 2) {
            console.log(`Tentando novamente... (tentativa ${retry + 1}/2)`);
            setTimeout(() => carregarDados(retry + 1), 1000); // Esperar 1s e tentar novamente
            return;
          }
          
          throw loadError; // Re-throw se esgotou as tentativas
        }
        
        // Verificar se os dados têm o formato esperado
        if (!Array.isArray(precatoriosData)) {
          console.error(`Erro: dados de ${tipoLabel} não é um array`, precatoriosData);
          throw new Error(`Os dados de ${tipoLabel} não estão no formato esperado`);
        }
        
        if (!Array.isArray(colunasData)) {
          console.error(`Erro: colunasData para ${tipoLabel} não é um array`, colunasData);
          throw new Error(`Os dados de colunas para ${tipoLabel} não estão no formato esperado`);
        }
        
        // Atualizar contagem nas colunas
        const colunasAtualizadas = await atualizarContagemColunas(colunasData, tipoVisualizacao);
        console.log('Colunas atualizadas com contagem:', colunasAtualizadas);
        
        // Atualizar estado com dados novos
        if (isMounted) {
          setPrecatorios(precatoriosData);
          setColunas(colunasAtualizadas);
          
          toast.success(`${precatoriosData.length} ${tipoLabel} carregados em ${colunasAtualizadas.length} colunas`);
        }
      } catch (error: any) {
        console.error(`Erro ao carregar ${tipoVisualizacao === 'PRECATORIO' ? 'precatórios' : 'RPVs'}:`, error);
        if (isMounted) {
          setErrorMessage(error.message || 'Erro ao carregar dados');
          toast.error(`Erro ao carregar ${tipoVisualizacao === 'PRECATORIO' ? 'precatórios' : 'RPVs'}`);
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
  };

  // Carregar dados inicialmente
  useEffect(() => {
    carregarDados();

    // Limpar cache quando o componente for desmontado
    return () => {
      setIsMounted(false);
      limparCachePrecatorios();
    };
  }, [tipoVisualizacao]);

  // Limpar cache quando o tipo de visualização mudar
  useEffect(() => {
    limparCachePrecatorios();
  }, [tipoVisualizacao]);

  // Adicionar listener para limpar cache quando a aba for focada
  useEffect(() => {
    const handleFocus = () => {
      limparCachePrecatorios();
      carregarDados();
    };

    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, []);

  // Salvar precatório (criar ou atualizar)
  const handleSavePrecatorio = async (precatorio: Precatorio) => {
    try {
      setLoading(true);
      await salvarPrecatorio(precatorio);
      
      // Recarregar os dados para atualizar a UI
      const [precatoriosData, colunasData] = await Promise.all([
        buscarTodosPrecatorios(tipoVisualizacao),
        buscarColunasPorTipo(tipoVisualizacao)
      ]);
      
      // Atualizar contagem nas colunas
      const colunasAtualizadas = await atualizarContagemColunas(colunasData, tipoVisualizacao);
      
      setPrecatorios(precatoriosData);
      setColunas(colunasAtualizadas);
      
      toast.success(`${tipoVisualizacao === 'PRECATORIO' ? 'Precatório' : 'RPV'} ${precatorio.numero} salvo com sucesso!`);
    } catch (error) {
      console.error(`Erro ao salvar ${tipoVisualizacao === 'PRECATORIO' ? 'precatório' : 'RPV'}:`, error);
      toast.error(`Erro ao salvar ${tipoVisualizacao === 'PRECATORIO' ? 'precatório' : 'RPV'}`);
    } finally {
      setLoading(false);
    }
  };

  // Excluir precatório
  const handleDeletePrecatorio = async (id: string) => {
    try {
      setLoading(true);
      await excluirPrecatorio(id);
      
      // Remover precatório da lista local
      setPrecatorios(precatorios.filter(p => p.id !== id));
      
      // Atualizar contagem nas colunas
      const colunasData = await buscarColunasPorTipo(tipoVisualizacao);
      const colunasAtualizadas = await atualizarContagemColunas(colunasData, tipoVisualizacao);
      setColunas(colunasAtualizadas);
      
      toast.success(`${tipoVisualizacao === 'PRECATORIO' ? 'Precatório' : 'RPV'} excluído com sucesso!`);
    } catch (error) {
      console.error(`Erro ao excluir ${tipoVisualizacao === 'PRECATORIO' ? 'precatório' : 'RPV'}:`, error);
      toast.error(`Erro ao excluir ${tipoVisualizacao === 'PRECATORIO' ? 'precatório' : 'RPV'}`);
    } finally {
      setLoading(false);
    }
  };

  // Mover precatório entre colunas (atualizar status)
  const handleMovePrecatorio = async (precatorioId: string, novoStatus: string) => {
    try {
      // Atualizar localmente primeiro para UI responsiva
      setPrecatorios(precatorios.map(p => 
        p.id === precatorioId ? { ...p, status: novoStatus as any } : p
      ));
      
      // Atualizar no banco de dados
      const precatorioAtualizado = await atualizarStatusPrecatorio(precatorioId, novoStatus);
      
      // Atualizar o precatório na lista local com os dados atualizados
      setPrecatorios(precatorios.map(p => 
        p.id === precatorioId ? precatorioAtualizado : p
      ));
      
      // Atualizar contagem nas colunas
      const colunasData = await buscarColunasPorTipo(tipoVisualizacao);
      const colunasAtualizadas = await atualizarContagemColunas(colunasData, tipoVisualizacao);
      setColunas(colunasAtualizadas);
      
      toast.success('Status atualizado com sucesso!');
    } catch (error) {
      console.error(`Erro ao mover ${tipoVisualizacao === 'PRECATORIO' ? 'precatório' : 'RPV'}:`, error);
      toast.error(`Erro ao atualizar status do ${tipoVisualizacao === 'PRECATORIO' ? 'precatório' : 'RPV'}`);
      
      // Reverter mudanças locais em caso de erro
      const precatoriosData = await buscarTodosPrecatorios(tipoVisualizacao);
      setPrecatorios(precatoriosData);
    }
  };

  if (loading && precatorios.length === 0) {
    return (
      <div className="flex h-[calc(100vh-64px)] w-full items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2 text-lg font-medium">
          Carregando {tipoVisualizacao === 'PRECATORIO' ? 'precatórios' : 'RPVs'}...
        </span>
      </div>
    );
  }

  if (errorMessage && precatorios.length === 0) {
    return (
      <div className="flex h-[calc(100vh-64px)] w-full flex-col items-center justify-center p-4">
        <div className="mb-4 text-xl font-semibold text-destructive">{errorMessage}</div>
        <div className="flex flex-col gap-2 sm:flex-row">
          <button
            onClick={() => window.location.reload()}
            className="rounded-md bg-primary px-4 py-2 text-primary-foreground hover:bg-primary/90"
          >
            Tentar novamente
          </button>
          
          {isAdmin && errorMessage.toString().includes('404') && (
            <button
              onClick={() => navigate('/migracao-banco')}
              className="rounded-md bg-secondary px-4 py-2 text-secondary-foreground hover:bg-secondary/90"
            >
              Configurar Banco de Dados
            </button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Kanban de {tipoVisualizacao === 'PRECATORIO' ? 'Precatórios' : 'RPVs'}</h1>
        
        <div className="flex items-center space-x-2">
          {isAdmin && (
            <Button variant="outline" size="sm" onClick={() => setGerenciadorAberto(true)}>
              <Settings className="h-4 w-4 mr-2" />
              Gerenciar Colunas
            </Button>
          )}
        </div>
      </div>
      
      {/* Dialog para gerenciamento de colunas (apenas para administradores) */}
      {isAdmin && (
        <Dialog open={gerenciadorAberto} onOpenChange={setGerenciadorAberto}>
          <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
            <DialogHeader className="pb-4 border-b">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-md bg-primary/10">
                  <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
                    <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path>
                    <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>
                  </svg>
                </div>
                <DialogTitle className="text-xl">Configuração de Colunas do Kanban</DialogTitle>
              </div>
              <DialogDescription>
                Configure as colunas que serão exibidas no quadro Kanban. Você pode adicionar, editar, reordenar e excluir colunas.
              </DialogDescription>
            </DialogHeader>
            <GerenciadorColunas 
              onColumnsChange={() => {
                console.log('[PrecatoriosKanban] Recebido evento de mudança nas colunas, recarregando dados...');
                // Forçar recarregamento completo dos dados
                carregarDados();
              }}
              tipoInicial={tipoVisualizacao === 'PRECATORIO' ? 'PRECATORIO' : 'RPV'}
            />
          </DialogContent>
        </Dialog>
      )}
      
      <Tabs 
        defaultValue="PRECATORIO" 
        value={tipoVisualizacao}
        onValueChange={(value) => setTipoVisualizacao(value as 'PRECATORIO' | 'RPV')}
        className="mb-6"
      >
        <TabsList>
          <TabsTrigger value="PRECATORIO">Precatórios</TabsTrigger>
          <TabsTrigger value="RPV">RPVs</TabsTrigger>
        </TabsList>
      </Tabs>
      
      <KanbanContainer
        todosPrecatorios={precatorios}
        colunas={colunas}
        onSavePrecatorio={handleSavePrecatorio}
        onDeletePrecatorio={handleDeletePrecatorio}
        onMovePrecatorio={handleMovePrecatorio}
      />
    </div>
  );
}

export default PrecatoriosKanban;
