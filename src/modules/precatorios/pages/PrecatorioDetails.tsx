"use client";

import {
  Activity,
  AlertCircle,
  AlertTriangle,
  ArrowLeft,
  Bookmark,
  Calendar,
  CalendarRange,
  Check,
  CheckCircle,
  CheckCircle2,
  Circle,
  CircleDollarSign,
  Clock,
  DollarSign,
  Download,
  Edit,
  FileCheck,
  FileSignature,
  FileText,
  Gavel,
  History,
  Link,
  Link2,
  Loader,
  Loader2,
  Mail,
  MoreHorizontal,
  Phone,
  Plus,
  Printer,
  RefreshCcw,
  Save,
  Share,
  Share2,
  Trash,
  Trash2,
  Upload,
  User,
} from "lucide-react";
import { useParams, useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";
import {
  Button
} from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { supabase } from "@/lib/supabase";

// Definindo cores e ícones para tipos de histórico
const tipoHistoricoColors = {
  criacao: "bg-green-100 text-green-600",
  atualizacao: "bg-blue-100 text-blue-600",
  pagamento: "bg-purple-100 text-purple-600",
  documento: "bg-yellow-100 text-yellow-600",
  alerta: "bg-red-100 text-red-600",
  status: "bg-orange-100 text-orange-600",
};

const tipoHistoricoIcons = {
  criacao: <Plus className="w-4 h-4" />,
  atualizacao: <RefreshCcw className="w-4 h-4" />,
  pagamento: <Check className="w-4 h-4" />,
  documento: <FileSignature className="w-4 h-4" />,
  alerta: <AlertTriangle className="w-4 h-4" />,
  status: <Activity className="w-4 h-4" />,
};

// Interface para documento
interface Documento {
  nome: string;
  data: string;
  tipo: string;
  tamanho: string;
  status: 'aprovado' | 'pendente';
}

// Interface para evento de histórico
interface HistoricoEvento {
  tipo: string;
  acao: string;
  data: string;
  usuario: string;
  detalhes: string;
}

// Interface para beneficiário
interface Beneficiario {
  nome: string;
  cpf: string;
  email: string;
  telefone: string;
  endereco?: string;
}

// Interface para precatório com campos adicionais
interface PrecatorioDetalhado {
  id: string;
  numero_precatorio: string;
  beneficiario: Beneficiario;
  tribunal: { nome: string };
  status: string;
  valor_total: string;
  valor_original?: string;
  valor_juros?: string;
  valor_honorarios?: string;
  prioridade: string;
  entidade_devedora: string;
  natureza?: string;
  data_previsao_pagamento?: string;
  progresso?: number;
  documentos_lista: Documento[];
  historico: HistoricoEvento[];
}

export function PrecatorioDetails() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState("detalhes");
  const [loading, setLoading] = useState(false);
  const [precatorio, setPrecatorio] = useState<PrecatorioDetalhado | null>(null);
  const [formValues, setFormValues] = useState<PrecatorioDetalhado>({});
  
  // Verificar se é um novo precatório
  const isNewPrecatorio = id === 'novo';

  // Se for um novo precatório, criar um objeto vazio com valores padrão
  const newPrecatorioTemplate: PrecatorioDetalhado = {
    id: "novo",
    numero_precatorio: "Novo Precatório",
    beneficiario: { 
      nome: "",
      cpf: "",
      email: "",
      telefone: "",
      endereco: ""
    },
    tribunal: { 
      nome: "",
      vara: "",
      processo_origem: ""
    },
    tipo: { nome: "Comum" },
    status: "Novo",
    responsavel: { 
      nome: "",
      cargo: "",
      email: "",
      telefone: ""
    },
    data_entrada: new Date().toISOString().split('T')[0],
    valor_total: "0",
    valor_original: "0",
    valor_juros: "0",
    valor_honorarios: "0",
    prioridade: "normal",
    entidade_devedora: "",
    data_previsao_pagamento: "",
    prazo_atual: "",
    natureza: "",
    documentos: 0,
    ultima_movimentacao: new Date().toISOString().split('T')[0],
    detalhes_movimentacao: "Precatório criado",
    progresso: 0,
    etapas_concluidas: 0,
    total_etapas: 6,
    historico: [
      {
        data: new Date().toISOString().split('T')[0],
        usuario: "Sistema",
        acao: "Cadastro",
        detalhes: "Precatório iniciado",
        tipo: "cadastro"
      }
    ],
    documentos_lista: [],
    etapas: [
      { nome: "Cadastro Inicial", status: "em_andamento", data: new Date().toISOString().split('T')[0] },
      { nome: "Análise Documental", status: "pendente", data: null },
      { nome: "Validação de Cálculos", status: "pendente", data: null },
      { nome: "Análise Jurídica", status: "pendente", data: null },
      { nome: "Processamento", status: "pendente", data: null },
      { nome: "Pagamento", status: "pendente", data: null }
    ],
    observacoes: []
  };

  // Buscar precatório do Supabase
  const fetchPrecatorio = async () => {
    if (isNewPrecatorio) {
      setPrecatorio(newPrecatorioTemplate);
      return;
    }

    // Verificar se o ID é válido antes de fazer a consulta
    if (!id || id === 'undefined') {
      console.error("ID inválido:", id);
      toast.error("ID do precatório inválido");
      navigate('/precatorios-management');
      return;
    }

    try {
      setLoading(true);
      console.log("Iniciando busca de precatório...");
      
      // Buscar dados do precatório
      const { data: precatorioData, error: precatorioError } = await supabase
        .from('precatorios')
        .select(`
          id,
          numero_precatorio,
          status,
          data_entrada,
          valor_total,
          prioridade,
          entidade_devedora,
          data_previsao_pagamento,
          prazo_atual,
          natureza,
          beneficiario_id,
          tribunal_id,
          tipo_id,
          responsavel_id
        `)
        .eq('id', id)
        .single();

      if (precatorioError) {
        console.error("Erro ao buscar precatório:", precatorioError);
        toast.error("Erro ao carregar dados do precatório");
        setLoading(false);
        return;
      }

      if (!precatorioData) {
        console.error("Precatório não encontrado");
        toast.error("Precatório não encontrado");
        navigate('/precatorios-management');
        setLoading(false);
        return;
      }

      console.log("Precatório encontrado:", precatorioData);

      // Variáveis para armazenar dados relacionados
      let beneficiarioData = null;
      let tribunalData = null;
      let tipoData = null;
      let responsavelData = null;

      // Buscar dados relacionados separadamente
      // Buscar beneficiário
      if (precatorioData.beneficiario_id) {
        console.log("Buscando beneficiário...");
        const { data, error } = await supabase
          .from('clientes')
          .select('id, nome, email, telefone')
          .eq('id', precatorioData.beneficiario_id)
          .single();
          
        if (error) {
          console.error("Erro ao buscar beneficiário:", error);
        } else {
          beneficiarioData = data;
          console.log("Beneficiário encontrado:", beneficiarioData);
        }
      }

      // Buscar tribunal
      if (precatorioData.tribunal_id) {
        console.log("Buscando tribunal...");
        const { data, error } = await supabase
          .from('tribunais')
          .select('id, nome')
          .eq('id', precatorioData.tribunal_id)
          .single();
          
        if (error) {
          console.error("Erro ao buscar tribunal:", error);
        } else {
          tribunalData = data;
          console.log("Tribunal encontrado:", tribunalData);
        }
      }

      // Buscar tipo
      if (precatorioData.tipo_id) {
        console.log("Buscando tipo...");
        const { data, error } = await supabase
          .from('tipos_precatorio')
          .select('id, nome')
          .eq('id', precatorioData.tipo_id)
          .single();
          
        if (error) {
          console.error("Erro ao buscar tipo:", error);
        } else {
          tipoData = data;
          console.log("Tipo encontrado:", tipoData);
        }
      }

      // Buscar responsável
      if (precatorioData.responsavel_id) {
        console.log("Buscando responsável...");
        const { data, error } = await supabase
          .from('profiles')
          .select('id, nome')
          .eq('id', precatorioData.responsavel_id)
          .single();
          
        if (error) {
          console.error("Erro ao buscar responsável:", error);
        } else {
          responsavelData = data;
          console.log("Responsável encontrado:", responsavelData);
        }
      }

      // Buscar movimentações do precatório
      console.log("Buscando movimentações...");
      const { data: movimentacoesData, error: movimentacoesError } = await supabase
        .from('movimentacoes_precatorio')
        .select('*')
        .eq('precatorio_id', id)
        .order('data', { ascending: false });

      if (movimentacoesError) {
        console.error("Erro ao buscar movimentações:", movimentacoesError);
        throw movimentacoesError;
      }

      console.log(`Encontradas ${movimentacoesData?.length || 0} movimentações`);

      // Formatar os dados para o formato esperado pela UI
      const formattedPrecatorio: PrecatorioDetalhado = {
        id: precatorioData.id,
        numero_precatorio: precatorioData.numero_precatorio,
        beneficiario: { 
          nome: beneficiarioData?.nome || "",
          cpf: "",
          email: beneficiarioData?.email || "",
          telefone: beneficiarioData?.telefone || "",
          endereco: ""
        },
        tribunal: { 
          nome: tribunalData?.nome || "",
          vara: "",
          processo_origem: ""
        },
        tipo: { nome: tipoData?.nome || "" },
        status: precatorioData.status || "",
        responsavel: { 
          nome: responsavelData?.nome || "",
          cargo: "",
          email: "",
          telefone: ""
        },
        data_entrada: precatorioData.data_entrada,
        valor_total: precatorioData.valor_total?.toString() || "0",
        valor_original: "0", // Valor padrão
        valor_juros: "0", // Valor padrão
        valor_honorarios: "0", // Valor padrão
        prioridade: precatorioData.prioridade || "normal",
        entidade_devedora: precatorioData.entidade_devedora || "",
        data_previsao_pagamento: precatorioData.data_previsao_pagamento,
        prazo_atual: precatorioData.prazo_atual || "",
        natureza: precatorioData.natureza || "",
        documentos: 0,
        ultima_movimentacao: movimentacoesData && movimentacoesData.length > 0 
          ? movimentacoesData[0].data 
          : precatorioData.data_entrada,
        detalhes_movimentacao: movimentacoesData && movimentacoesData.length > 0 
          ? movimentacoesData[0].descricao 
          : "Sem movimentações",
        progresso: 0, // Será calculado
        etapas_concluidas: 0, // Será calculado
        total_etapas: 6,
        historico: movimentacoesData ? movimentacoesData.map(mov => ({
          data: mov.data,
          usuario: "Sistema", // Valor padrão
          acao: mov.status_novo ? `Alteração de Status: ${mov.status_anterior} → ${mov.status_novo}` : "Movimentação",
          detalhes: mov.descricao,
          tipo: "status"
        })) : [],
        documentos_lista: [],
        etapas: [
          { nome: "Cadastro Inicial", status: "concluido", data: precatorioData.data_entrada },
          { nome: "Análise Documental", status: "pendente", data: null },
          { nome: "Validação de Cálculos", status: "pendente", data: null },
          { nome: "Análise Jurídica", status: "pendente", data: null },
          { nome: "Processamento", status: "pendente", data: null },
          { nome: "Pagamento", status: "pendente", data: null }
        ],
        observacoes: []
      };

      // Calcular progresso com base no status
      const statusIndex = {
        "Novo": 0,
        "Em Processamento": 2,
        "Aguardando Pagamento": 4,
        "Pago": 5,
        "Suspenso": 2
      };

      const currentStatusIndex = statusIndex[precatorioData.status as keyof typeof statusIndex] || 0;
      formattedPrecatorio.etapas_concluidas = currentStatusIndex;
      formattedPrecatorio.progresso = Math.round((currentStatusIndex / 6) * 100);

      // Atualizar etapas com base no status
      for (let i = 0; i <= currentStatusIndex; i++) {
        if (i < formattedPrecatorio.etapas.length) {
          formattedPrecatorio.etapas[i].status = "concluido";
        }
      }

      if (currentStatusIndex < formattedPrecatorio.etapas.length) {
        formattedPrecatorio.etapas[currentStatusIndex].status = "em_andamento";
      }

      setPrecatorio(formattedPrecatorio);
      setLoading(false);
    } catch (error) {
      console.error("Erro ao buscar precatório:", error);
      toast.error("Erro ao carregar dados do precatório");
      setLoading(false);
    }
  };

  // Carregar dados ao iniciar o componente
  useEffect(() => {
    fetchPrecatorio();
  }, [id]);

  useEffect(() => {
    if (precatorio) {
      setFormValues(precatorio);
    }
  }, [precatorio]);

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center h-[80vh] gap-4">
        <Loader2 className="w-12 h-12 animate-spin text-primary" />
        <h1 className="text-2xl font-bold">Carregando precatório...</h1>
      </div>
    );
  }

  if (!precatorio && !isNewPrecatorio) {
    return (
      <div className="flex flex-col items-center justify-center h-[80vh] gap-4">
        <AlertCircle className="w-12 h-12 text-red-500" />
        <h1 className="text-2xl font-bold">Precatório não encontrado</h1>
        <Button onClick={() => navigate("/precatorios-management")}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          Voltar
        </Button>
      </div>
    );
  }

  const statusColor = {
    "Em Processamento": "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400",
    "Aguardando Pagamento": "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400",
    "Pago": "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
    "Suspenso": "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400",
    "Novo": "bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400"
  };

  const handleShare = () => {
    navigator.clipboard.writeText(window.location.href);
    toast.success("Link copiado para a área de transferência!");
  };

  const handlePrint = () => {
    window.print();
  };

  const handleSavePrecatorio = async () => {
    try {
      setLoading(true);
      
      // Verificar campos obrigatórios
      if (!formValues.numero_precatorio) {
        toast.error("Número do precatório é obrigatório");
        setLoading(false);
        return;
      }
      
      // Para novo precatório
      if (isNewPrecatorio) {
        console.log("Criando novo precatório:", formValues);
        
        // 1. Buscar ou criar beneficiário
        let beneficiarioId = null;
        if (formValues.beneficiario?.nome) {
          // Verificar se o beneficiário já existe
          const { data: beneficiarioExistente } = await supabase
            .from('clientes')
            .select('id, nome, email, telefone')
            .ilike('nome', formValues.beneficiario.nome)
            .limit(1);

          if (beneficiarioExistente && beneficiarioExistente.length > 0) {
            beneficiarioId = beneficiarioExistente[0].id;
          } else {
            // Criar novo beneficiário
            const { data: novoBeneficiario, error: erroBeneficiario } = await supabase
              .from('clientes')
              .insert([{ 
                nome: formValues.beneficiario.nome,
                cpf: formValues.beneficiario.cpf || null,
                email: formValues.beneficiario.email || null,
                telefone: formValues.beneficiario.telefone || null,
                endereco: formValues.beneficiario.endereco || null
              }])
              .select();

            if (erroBeneficiario) {
              console.error("Erro ao criar beneficiário:", erroBeneficiario);
              toast.error("Erro ao criar beneficiário");
              setLoading(false);
              return;
            }

            beneficiarioId = novoBeneficiario[0].id;
          }
        }

        // 2. Buscar ou criar tribunal
        let tribunalId = null;
        if (formValues.tribunal?.nome) {
          // Verificar se o tribunal já existe
          const { data: tribunalExistente } = await supabase
            .from('tribunais')
            .select('id, nome')
            .ilike('nome', formValues.tribunal.nome)
            .limit(1);

          if (tribunalExistente && tribunalExistente.length > 0) {
            tribunalId = tribunalExistente[0].id;
          } else {
            // Criar novo tribunal
            const { data: novoTribunal, error: erroTribunal } = await supabase
              .from('tribunais')
              .insert([{ 
                nome: formValues.tribunal.nome,
                vara: formValues.tribunal.vara || null
              }])
              .select();

            if (erroTribunal) {
              console.error("Erro ao criar tribunal:", erroTribunal);
              toast.error("Erro ao criar tribunal");
              setLoading(false);
              return;
            }

            tribunalId = novoTribunal[0].id;
          }
        }

        // 3. Preparar dados do precatório
        const novoPrecatorioData = {
          numero_precatorio: formValues.numero_precatorio,
          status: formValues.status || "Novo",
          valor_total: formValues.valor_total || 0,
          prioridade: formValues.prioridade || "normal",
          entidade_devedora: formValues.entidade_devedora || "",
          data_entrada: formValues.data_entrada || new Date().toISOString(),
          data_previsao_pagamento: formValues.data_previsao_pagamento || null,
          prazo_atual: formValues.prazo_atual || null,
          natureza: formValues.natureza || null
        };

        // Adicionar IDs de relacionamento se existirem
        if (beneficiarioId) {
          novoPrecatorioData.beneficiario_id = beneficiarioId;
        }
        
        if (tribunalId) {
          novoPrecatorioData.tribunal_id = tribunalId;
        }

        // 4. Salvar precatório no Supabase
        const { data, error } = await supabase
          .from('precatorios')
          .insert([novoPrecatorioData])
          .select();

        if (error) {
          console.error("Erro ao salvar precatório:", error);
          toast.error(`Erro ao salvar precatório: ${error.message}`);
          setLoading(false);
          return;
        }

        toast.success("Precatório criado com sucesso!");
        navigate('/precatorios-management');
      } 
      // Para edição de precatório existente
      else {
        // Implementação para atualizar um precatório existente
        // Lógica similar à criação, mas usando .update() em vez de .insert()
        // A ser implementada conforme necessário
        toast.error("Função de edição não implementada");
      }
      
      setLoading(false);
    } catch (error) {
      console.error("Erro ao salvar:", error);
      toast.error("Erro ao salvar precatório");
      setLoading(false);
    }
  };

  const handleFormChange = (field: string, value: any) => {
    // Para campos aninhados como beneficiario.nome
    if (field.includes('.')) {
      const [parentField, childField] = field.split('.');
      setFormValues(prev => ({
        ...prev,
        [parentField]: {
          ...(prev[parentField] || {}),
          [childField]: value
        }
      }));
    } else {
      setFormValues(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  return (
    <div className="flex flex-col gap-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between bg-card shadow-sm rounded-lg p-4">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" onClick={() => navigate("/precatorios-management")}>
            <ArrowLeft className="w-4 h-4" />
          </Button>
          <div>
            <div className="flex items-center gap-3">
              <h1 className="text-2xl font-bold">
                {isNewPrecatorio ? "Novo Precatório" : `Precatório ${precatorio.numero_precatorio}`}
              </h1>
              <Badge className={statusColor[precatorio.status as keyof typeof statusColor]}>
                {precatorio.status}
              </Badge>
              {precatorio.prioridade === "alta" && (
                <Badge variant="destructive" className="gap-1">
                  <AlertTriangle className="w-3 h-3" />
                  Prioritário
                </Badge>
              )}
            </div>
            <div className="flex items-center gap-2 mt-1 text-muted-foreground">
              <Calendar className="w-4 h-4" />
              <span>Criado em {new Date(precatorio.data_entrada).toLocaleDateString('pt-BR')}</span>
              <Separator orientation="vertical" className="h-4" />
              <User className="w-4 h-4" />
              <span>Por {precatorio.responsavel.nome || "Sistema"}</span>
            </div>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" className="gap-2" onClick={handleShare}>
            <Share2 className="w-4 h-4" />
            Compartilhar
          </Button>
          <Button variant="outline" className="gap-2" onClick={handlePrint}>
            <Printer className="w-4 h-4" />
            Imprimir
          </Button>
          <Button 
            variant={isNewPrecatorio ? "default" : "outline"} 
            className="gap-2" 
            onClick={handleSavePrecatorio}
          >
            {isNewPrecatorio ? <FileCheck className="w-4 h-4" /> : <Edit className="w-4 h-4" />}
            {isNewPrecatorio ? "Criar Precatório" : "Salvar"}
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="icon">
                <MoreHorizontal className="w-4 h-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>
                <Download className="w-4 h-4 mr-2" />
                Exportar PDF
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Upload className="w-4 h-4 mr-2" />
                Anexar Documento
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Link2 className="w-4 h-4 mr-2" />
                Vincular Processo
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Bookmark className="w-4 h-4 mr-2" />
                Adicionar aos Favoritos
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="text-red-500">
                <Trash2 className="w-4 h-4 mr-2" />
                Excluir Precatório
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Progress Bar */}
      <Card className="shadow-sm">
        <CardHeader className="pb-2">
          <div className="flex justify-between items-center">
            <CardTitle className="text-lg font-medium">Progresso do Precatório</CardTitle>
            <Badge variant="outline">{precatorio.progresso}% Completo</Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="w-full bg-secondary rounded-full h-2.5">
              <div 
                className="bg-primary h-2.5 rounded-full" 
                style={{ width: `${precatorio.progresso}%` }}
              ></div>
            </div>
            
            <div className="grid grid-cols-6 gap-1">
              {precatorio.etapas.map((etapa, index) => (
                <div key={index} className="flex flex-col items-center text-center">
                  <div className={`
                    flex items-center justify-center rounded-full w-8 h-8
                    ${etapa.status === 'concluido' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' : 
                      etapa.status === 'em_andamento' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400' : 
                      'bg-secondary text-muted-foreground'}
                  `}>
                    {etapa.status === 'concluido' ? (
                      <CheckCircle2 className="w-4 h-4" />
                    ) : etapa.status === 'em_andamento' ? (
                      <Circle className="w-4 h-4" />
                    ) : (
                      <Circle className="w-4 h-4" />
                    )}
                  </div>
                  <p className="text-xs mt-1">{etapa.nome}</p>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Content - Tabs */}
      <Tabs defaultValue="detalhes" className="w-full" onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-5 w-full">
          <TabsTrigger value="detalhes">
            <FileText className="w-4 h-4 mr-2" />
            Detalhes
          </TabsTrigger>
          <TabsTrigger value="beneficiario">
            <User className="w-4 h-4 mr-2" />
            Beneficiário
          </TabsTrigger>
          <TabsTrigger value="financeiro">
            <CircleDollarSign className="w-4 h-4 mr-2" />
            Financeiro
          </TabsTrigger>
          <TabsTrigger value="documentos">
            <FileSignature className="w-4 h-4 mr-2" />
            Documentos
          </TabsTrigger>
          <TabsTrigger value="historico">
            <History className="w-4 h-4 mr-2" />
            Histórico
          </TabsTrigger>
        </TabsList>
        <TabsContent value="detalhes" className="mt-4">
          <Card>
            <CardContent className="p-6">
              <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold pb-3 border-b">Informações Gerais</h3>
                    <div className="space-y-4 mt-4">
                      <div>
                        <Label htmlFor="numero_precatorio" className="text-sm font-medium text-muted-foreground">Número do Precatório</Label>
                        {isNewPrecatorio ? (
                          <Input 
                            id="numero_precatorio" 
                            className="mt-1" 
                            placeholder="Digite o número do precatório" 
                            defaultValue={precatorio?.numero_precatorio}
                            onChange={(e) => handleFormChange('numero_precatorio', e.target.value)}
                          />
                        ) : (
                          <p className="text-lg">{precatorio?.numero_precatorio}</p>
                        )}
                      </div>
                      <div>
                        <Label htmlFor="entidade_devedora" className="text-sm font-medium text-muted-foreground">Entidade Devedora</Label>
                        {isNewPrecatorio ? (
                          <Input 
                            id="entidade_devedora" 
                            className="mt-1" 
                            placeholder="Digite a entidade devedora" 
                            defaultValue={precatorio?.entidade_devedora}
                            onChange={(e) => handleFormChange('entidade_devedora', e.target.value)}
                          />
                        ) : (
                          <p className="text-lg">{precatorio?.entidade_devedora}</p>
                        )}
                      </div>
                      <div>
                        <Label htmlFor="natureza" className="text-sm font-medium text-muted-foreground">Natureza</Label>
                        {isNewPrecatorio ? (
                          <Input 
                            id="natureza" 
                            className="mt-1" 
                            placeholder="Digite a natureza" 
                            defaultValue={precatorio?.natureza}
                            onChange={(e) => handleFormChange('natureza', e.target.value)}
                          />
                        ) : (
                          <p className="text-lg">{precatorio?.natureza}</p>
                        )}
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground">Tipo</h3>
                        {isNewPrecatorio ? (
                          <Select 
                            defaultValue={precatorio?.tipo.nome}
                            onValueChange={(value) => handleFormChange('tipo.nome', value)}
                          >
                            <SelectTrigger className="mt-1">
                              <SelectValue placeholder="Selecione o tipo" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="Comum">Comum</SelectItem>
                              <SelectItem value="Alimentício">Alimentício</SelectItem>
                              <SelectItem value="Honorário">Honorário</SelectItem>
                            </SelectContent>
                          </Select>
                        ) : (
                          <p className="text-lg">{precatorio?.tipo.nome}</p>
                        )}
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground">Status</h3>
                        {isNewPrecatorio ? (
                          <Select 
                            defaultValue={precatorio?.status}
                            onValueChange={(value) => handleFormChange('status', value)}
                          >
                            <SelectTrigger className="mt-1">
                              <SelectValue placeholder="Selecione o status" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="Novo">Novo</SelectItem>
                              <SelectItem value="Em Processamento">Em Processamento</SelectItem>
                              <SelectItem value="Aguardando Pagamento">Aguardando Pagamento</SelectItem>
                              <SelectItem value="Pago">Pago</SelectItem>
                              <SelectItem value="Suspenso">Suspenso</SelectItem>
                            </SelectContent>
                          </Select>
                        ) : (
                          <Badge className={statusColor[precatorio?.status as keyof typeof statusColor]}>
                            {precatorio?.status}
                          </Badge>
                        )}
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground">Prioridade</h3>
                        {isNewPrecatorio ? (
                          <Select 
                            defaultValue={precatorio?.prioridade}
                            onValueChange={(value) => handleFormChange('prioridade', value)}
                          >
                            <SelectTrigger className="mt-1">
                              <SelectValue placeholder="Selecione a prioridade" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="baixa">Baixa</SelectItem>
                              <SelectItem value="normal">Normal</SelectItem>
                              <SelectItem value="alta">Alta</SelectItem>
                              <SelectItem value="urgente">Urgente</SelectItem>
                            </SelectContent>
                          </Select>
                        ) : (
                          <Badge variant={
                            precatorio?.prioridade === 'urgente' ? 'destructive' : 
                            precatorio?.prioridade === 'alta' ? 'default' : 
                            precatorio?.prioridade === 'normal' ? 'secondary' : 'outline'
                          }>
                            {precatorio?.prioridade?.charAt(0).toUpperCase() + precatorio?.prioridade?.slice(1)}
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
                <div className="space-y-6">
                  {/* Beneficiário */}
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <User className="w-5 h-5 text-blue-500" />
                      <h3 className="text-lg font-medium">Dados do Beneficiário</h3>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label>Nome</Label>
                        {isNewPrecatorio ? (
                          <Input 
                            className="mt-1" 
                            placeholder="Nome do beneficiário" 
                            defaultValue={precatorio?.beneficiario?.nome}
                            onChange={(e) => handleFormChange('beneficiario.nome', e.target.value)}
                          />
                        ) : (
                          <p className="text-lg font-medium">{precatorio?.beneficiario?.nome}</p>
                        )}
                      </div>
                      
                      <div>
                        <Label>CPF/CNPJ</Label>
                        {isNewPrecatorio ? (
                          <Input 
                            className="mt-1" 
                            placeholder="CPF ou CNPJ do beneficiário" 
                            defaultValue={precatorio?.beneficiario?.cpf}
                            onChange={(e) => handleFormChange('beneficiario.cpf', e.target.value)}
                          />
                        ) : (
                          <p>{precatorio?.beneficiario?.cpf || "Não informado"}</p>
                        )}
                      </div>
                      
                      <div>
                        <Label>Email</Label>
                        {isNewPrecatorio ? (
                          <Input 
                            className="mt-1" 
                            placeholder="Email do beneficiário" 
                            defaultValue={precatorio?.beneficiario?.email}
                            onChange={(e) => handleFormChange('beneficiario.email', e.target.value)}
                          />
                        ) : (
                          <p>{precatorio?.beneficiario?.email || "Não informado"}</p>
                        )}
                      </div>
                      
                      <div>
                        <Label>Telefone</Label>
                        {isNewPrecatorio ? (
                          <Input 
                            className="mt-1" 
                            placeholder="Telefone do beneficiário" 
                            defaultValue={precatorio?.beneficiario?.telefone}
                            onChange={(e) => handleFormChange('beneficiario.telefone', e.target.value)}
                          />
                        ) : (
                          <p>{precatorio?.beneficiario?.telefone || "Não informado"}</p>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  {/* Tribunal */}
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <Gavel className="w-5 h-5 text-red-500" />
                      <h3 className="text-lg font-medium">Dados do Tribunal</h3>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label>Nome</Label>
                        {isNewPrecatorio ? (
                          <Input 
                            className="mt-1" 
                            placeholder="Nome do tribunal" 
                            defaultValue={precatorio?.tribunal?.nome}
                            onChange={(e) => handleFormChange('tribunal.nome', e.target.value)}
                          />
                        ) : (
                          <p className="text-lg font-medium">{precatorio?.tribunal?.nome}</p>
                        )}
                      </div>
                      
                      <div>
                        <Label>Vara</Label>
                        {isNewPrecatorio ? (
                          <Input 
                            className="mt-1" 
                            placeholder="Vara ou seção" 
                            defaultValue={precatorio?.tribunal?.vara}
                            onChange={(e) => handleFormChange('tribunal.vara', e.target.value)}
                          />
                        ) : (
                          <p>{precatorio?.tribunal?.vara || "Não informado"}</p>
                        )}
                      </div>
                      
                      <div>
                        <Label>Processo de Origem</Label>
                        {isNewPrecatorio ? (
                          <Input 
                            className="mt-1" 
                            placeholder="Número do processo de origem" 
                            defaultValue={precatorio?.tribunal?.processo_origem}
                            onChange={(e) => handleFormChange('tribunal.processo_origem', e.target.value)}
                          />
                        ) : (
                          <p>{precatorio?.tribunal?.processo_origem || "Não informado"}</p>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  {/* Dados Financeiros */}
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <DollarSign className="w-5 h-5 text-green-500" />
                      <h3 className="text-lg font-medium">Dados Financeiros</h3>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label>Valor Total</Label>
                        {isNewPrecatorio ? (
                          <Input 
                            className="mt-1" 
                            type="number"
                            placeholder="Valor total do precatório" 
                            defaultValue={precatorio?.valor_total?.toString()}
                            onChange={(e) => handleFormChange('valor_total', parseFloat(e.target.value))}
                          />
                        ) : (
                          <p className="text-lg font-medium">
                            {precatorio?.valor_total ? 
                              new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }).format(precatorio.valor_total) : 
                              "R$ 0,00"}
                          </p>
                        )}
                      </div>
                      
                      <div>
                        <Label>Data de Previsão de Pagamento</Label>
                        {isNewPrecatorio ? (
                          <Input 
                            className="mt-1" 
                            type="date"
                            placeholder="Data de previsão" 
                            defaultValue={precatorio?.data_previsao_pagamento?.split('T')[0]}
                            onChange={(e) => handleFormChange('data_previsao_pagamento', e.target.value)}
                          />
                        ) : (
                          <p>
                            {precatorio?.data_previsao_pagamento ? 
                              new Date(precatorio.data_previsao_pagamento).toLocaleDateString('pt-BR') : 
                              "Não definida"}
                          </p>
                        )}
                      </div>
                      
                      <div>
                        <Label>Prazo Atual</Label>
                        {isNewPrecatorio ? (
                          <Input 
                            className="mt-1" 
                            placeholder="Prazo atual" 
                            defaultValue={precatorio?.prazo_atual}
                            onChange={(e) => handleFormChange('prazo_atual', e.target.value)}
                          />
                        ) : (
                          <p>{precatorio?.prazo_atual || "Não informado"}</p>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  {/* Botões de Ação (apenas em modo de edição/criação) */}
                  {isNewPrecatorio && (
                    <div className="flex justify-end gap-2 mt-6">
                      <Button variant="outline" onClick={() => navigate('/precatorios-management')}>
                        Cancelar
                      </Button>
                      <Button onClick={handleSavePrecatorio} disabled={loading}>
                        {loading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Save className="mr-2 h-4 w-4" />}
                        Salvar Precatório
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="beneficiario" className="mt-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold">Dados do Beneficiário</h3>
                <Button variant="outline" className="gap-2">
                  <Edit className="w-4 h-4" />
                  Editar Beneficiário
                </Button>
              </div>

              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="space-y-1">
                    <h4 className="text-sm font-medium text-muted-foreground">Nome Completo</h4>
                    <p className="text-lg font-medium">{precatorio.beneficiario.nome || "Não informado"}</p>
                  </div>
                  
                  <div className="space-y-1">
                    <h4 className="text-sm font-medium text-muted-foreground">CPF</h4>
                    <p className="text-lg font-medium">{precatorio.beneficiario.cpf || "Não informado"}</p>
                  </div>
                  
                  <div className="space-y-1">
                    <h4 className="text-sm font-medium text-muted-foreground">Endereço</h4>
                    <p className="text-lg font-medium">{precatorio.beneficiario.endereco || "Não informado"}</p>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <div className="space-y-1">
                    <h4 className="text-sm font-medium text-muted-foreground">Email</h4>
                    <div className="flex items-center gap-2">
                      <Mail className="w-4 h-4 text-muted-foreground" />
                      <p className="text-lg font-medium">{precatorio.beneficiario.email || "Não informado"}</p>
                    </div>
                  </div>
                  
                  <div className="space-y-1">
                    <h4 className="text-sm font-medium text-muted-foreground">Telefone</h4>
                    <div className="flex items-center gap-2">
                      <Phone className="w-4 h-4 text-muted-foreground" />
                      <p className="text-lg font-medium">{precatorio.beneficiario.telefone || "Não informado"}</p>
                    </div>
                  </div>
                  
                  <div className="space-y-1">
                    <h4 className="text-sm font-medium text-muted-foreground">Outros Precatórios</h4>
                    <Badge variant="outline" className="text-sm">
                      <Link2 className="w-3 h-3 mr-1" />
                      Ver todos os precatórios do beneficiário
                    </Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="financeiro" className="mt-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold">Dados Financeiros</h3>
                <Button variant="outline" className="gap-2">
                  <Edit className="w-4 h-4" />
                  Editar Valores
                </Button>
              </div>

              <div className="grid md:grid-cols-3 gap-6">
                <div className="p-4 border rounded-lg space-y-2">
                  <h4 className="text-sm font-medium text-muted-foreground">Valor Total</h4>
                  <p className="text-2xl font-bold text-primary">
                    {new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' })
                      .format(Number(precatorio.valor_total) || 0)}
                  </p>
                </div>
                
                <div className="p-4 border rounded-lg space-y-2">
                  <h4 className="text-sm font-medium text-muted-foreground">Valor Original</h4>
                  <p className="text-xl font-semibold">
                    {new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' })
                      .format(Number(precatorio.valor_original) || 0)}
                  </p>
                </div>
                
                <div className="p-4 border rounded-lg space-y-2">
                  <h4 className="text-sm font-medium text-muted-foreground">Juros</h4>
                  <p className="text-xl font-semibold">
                    {new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' })
                      .format(Number(precatorio.valor_juros) || 0)}
                  </p>
                </div>
              </div>
              
              <Separator className="my-6" />
              
              <div className="space-y-4">
                <h4 className="text-lg font-medium">Detalhes Adicionais</h4>
                
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="space-y-1">
                    <h5 className="text-sm font-medium text-muted-foreground">Data de Previsão de Pagamento</h5>
                    <div className="flex items-center gap-2">
                      <CalendarRange className="w-4 h-4 text-muted-foreground" />
                      <p className="text-base">{precatorio.data_previsao_pagamento ? new Date(precatorio.data_previsao_pagamento).toLocaleDateString('pt-BR') : "Não definida"}</p>
                    </div>
                  </div>
                  
                  <div className="space-y-1">
                    <h5 className="text-sm font-medium text-muted-foreground">Honorários</h5>
                    <p className="text-base">
                      {new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' })
                        .format(Number(precatorio.valor_honorarios) || 0)}
                    </p>
                  </div>
                  
                  <div className="space-y-1">
                    <h5 className="text-sm font-medium text-muted-foreground">Entidade Devedora</h5>
                    <p className="text-base">{precatorio.entidade_devedora || "Não informada"}</p>
                  </div>
                  
                  <div className="space-y-1">
                    <h5 className="text-sm font-medium text-muted-foreground">Natureza</h5>
                    <p className="text-base">{precatorio.natureza || "Não informada"}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="documentos" className="mt-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h3 className="text-xl font-semibold">Documentos</h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    {precatorio.documentos_lista?.filter(d => d.status === 'aprovado')?.length || 0} de {precatorio.documentos_lista?.length || 0} documentos aprovados
                  </p>
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" className="gap-2">
                    <Download className="w-4 h-4" />
                    Baixar Todos
                  </Button>
                  <Button className="gap-2">
                    <Upload className="w-4 h-4" />
                    Novo Documento
                  </Button>
                </div>
              </div>

              <div className="space-y-3">
                {precatorio.documentos_lista && precatorio.documentos_lista.length > 0 ? (
                  precatorio.documentos_lista.map((doc, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-4 border rounded-lg hover:bg-accent/50 transition-colors"
                    >
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-primary/10 rounded-md text-primary">
                          <FileText className="w-5 h-5" />
                        </div>
                        <div>
                          <p className="font-medium">{doc.nome}</p>
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <span>{new Date(doc.data).toLocaleDateString('pt-BR')}</span>
                            <Separator orientation="vertical" className="h-3" />
                            <span>{doc.tipo}</span>
                            <Separator orientation="vertical" className="h-3" />
                            <span>{doc.tamanho}</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <Badge
                          variant={doc.status === 'aprovado' ? 'default' : 'secondary'}
                          className="gap-1"
                        >
                          {doc.status === 'aprovado' ? (
                            <CheckCircle2 className="w-3 h-3" />
                          ) : (
                            <Clock className="w-3 h-3" />
                          )}
                          {doc.status === 'aprovado' ? 'Aprovado' : 'Pendente'}
                        </Badge>
                        <Button variant="ghost" size="icon">
                          <Download className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-10 border rounded-lg">
                    <FileText className="w-10 h-10 text-muted-foreground mx-auto mb-3" />
                    <h4 className="text-lg font-medium mb-1">Nenhum documento anexado</h4>
                    <p className="text-sm text-muted-foreground mb-4">Adicione documentos a este precatório para visualizá-los aqui.</p>
                    <Button>
                      <Upload className="w-4 h-4 mr-2" />
                      Anexar Documento
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="historico" className="mt-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold">Histórico de Movimentações</h3>
                <Button className="gap-2">
                  <Plus className="w-4 h-4" />
                  Nova Movimentação
                </Button>
              </div>

              <div className="space-y-1">
                {precatorio.historico && precatorio.historico.length > 0 ? (
                  precatorio.historico.map((evento, index) => (
                    <div
                      key={index}
                      className="flex pt-6 pb-6 border-b last:border-0"
                    >
                      <div className="mr-4">
                        <div className={`rounded-full p-2 ${tipoHistoricoColors[evento.tipo as keyof typeof tipoHistoricoColors] || "text-gray-500"}`}>
                          {tipoHistoricoIcons[evento.tipo as keyof typeof tipoHistoricoIcons] || <History className="w-4 h-4" />}
                        </div>
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-1">
                          <p className="font-medium">{evento.acao}</p>
                          <p className="text-sm text-muted-foreground">
                            {new Date(evento.data).toLocaleDateString('pt-BR')}
                          </p>
                        </div>
                        <p className="text-sm text-muted-foreground mb-2">Por {evento.usuario}</p>
                        <p className="text-sm bg-secondary/50 p-2 rounded-md">{evento.detalhes}</p>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-10 border rounded-lg">
                    <History className="w-10 h-10 text-muted-foreground mx-auto mb-3" />
                    <h4 className="text-lg font-medium mb-1">Nenhuma movimentação registrada</h4>
                    <p className="text-sm text-muted-foreground mb-4">Registre as movimentações deste precatório para visualizá-las aqui.</p>
                    <Button>
                      <Plus className="w-4 h-4 mr-2" />
                      Registrar Movimentação
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default PrecatorioDetails;