import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { supabase, resetSupabaseClient } from "@/lib/supabase";
import { checkAndRestoreAuth } from "@/lib/auth-helpers";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { toast } from "sonner";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Separator } from "@/components/ui/separator";

const formSchema = z.object({
  email: z.string().email({ message: "Email inválido" }),
  password: z.string().min(6, { message: "Senha deve ter pelo menos 6 caracteres" }),
});

export default function Login() {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);

  // Verificar se já existe uma sessão ao carregar a página
  useEffect(() => {
    const checkExistingSession = async () => {
      try {
        const { success, session } = await checkAndRestoreAuth();
        
        if (success && session) {
          console.log("Sessão existente encontrada, redirecionando para o dashboard...");
          
          // Buscar perfil do usuário
          const { data: profile } = await supabase
            .from("profiles")
            .select("*")
            .eq("id", session.user.id)
            .single();
            
          if (profile) {
            localStorage.setItem("userProfile", JSON.stringify(profile));
          }
          
          navigate("/dashboard");
        }
      } catch (error) {
        console.error("Erro ao verificar sessão existente:", error);
      }
    };
    
    checkExistingSession();
  }, [navigate]);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    try {
      setIsLoading(true);
      toast.loading("Autenticando...");
      
      // Primeiro, limpar quaisquer tokens antigos
      localStorage.removeItem("sb-ubwzukpsqcrwzfbppoux-auth-token");
      await resetSupabaseClient();
      
      // Tentar login com Supabase
      const { data, error } = await supabase.auth.signInWithPassword({
        email: values.email,
        password: values.password,
      });
      
      if (error) {
        console.error("Erro de autenticação:", error);
        
        if (error.status === 400) {
          toast.dismiss();
          toast.error("Email ou senha incorretos", {
            description: "Verifique suas credenciais e tente novamente."
          });
        } else {
          toast.dismiss();
          toast.error("Erro ao fazer login", {
            description: error.message || "Ocorreu um erro durante a autenticação."
          });
        }
        
        setIsLoading(false);
        return;
      }
      
      if (!data || !data.user) {
        toast.dismiss();
        toast.error("Erro de autenticação", {
          description: "Dados do usuário não encontrados."
        });
        setIsLoading(false);
        return;
      }
      
      console.log("Login bem-sucedido:", data.user.email);
      
      // Buscar perfil do usuário
      const { data: profile, error: profileError } = await supabase
        .from("profiles")
        .select("*")
        .eq("id", data.user.id)
        .single();
        
      if (profileError) {
        console.warn("Aviso: Erro ao buscar perfil do usuário:", profileError);
      }
      
      // Se encontrou o perfil, armazená-lo no localStorage
      if (profile) {
        localStorage.setItem("userProfile", JSON.stringify(profile));
      } else {
        console.warn("Aviso: Perfil do usuário não encontrado.");
      }
      
      toast.dismiss();
      toast.success("Login realizado com sucesso!", {
        description: "Redirecionando para o dashboard..."
      });
      
      // Redirecionar para o dashboard
      setTimeout(() => {
        navigate("/dashboard");
      }, 1000);
    } catch (error: any) {
      console.error("Erro inesperado durante o login:", error);
      
      toast.dismiss();
      toast.error("Erro ao fazer login", {
        description: "Ocorreu um erro inesperado. Tente novamente mais tarde."
      });
      
      setIsLoading(false);
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="flex min-h-screen items-center justify-center bg-slate-50 dark:bg-slate-950">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold text-center">Login</CardTitle>
          <CardDescription className="text-center">
            Entre com seu email e senha para acessar o sistema
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="<EMAIL>" 
                        type="email" 
                        {...field} 
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Senha</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="•••••••" 
                        type="password" 
                        {...field} 
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <Button 
                type="submit" 
                className="w-full" 
                disabled={isLoading}
              >
                {isLoading ? "Autenticando..." : "Entrar"}
              </Button>
            </form>
          </Form>
          <div className="mt-4">
            <Separator className="my-4" />
            <p className="text-sm text-center text-gray-500 dark:text-gray-400">
              Ao fazer login, você concorda com nossos termos de serviço.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
