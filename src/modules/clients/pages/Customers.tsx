"use client";

import { useState, useEffect } from 'react';
import { 
  Users, 
  UserPlus, 
  Search, 
  Filter,
  Mail,
  Phone,
  Building2,
  MoreVertical,
  ArrowUpRight,
  ArrowDownRight,
  Target,
  Wallet,
  Clock,
  TrendingUp,
  MoreHorizontal,
  FileText,
  DollarSign,
  AlertCircle,
  CheckCircle2,
  MapPin,
  CircleDollarSign,
  CalendarRange,
  Download,
  Upload,
  Trash2,
  ChevronDown,
  ChevronUp,
  GripVertical,
  Loader2,
} from 'lucide-react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useNavigate } from 'react-router-dom';
import { AreaChart, Area, XAxis, YAxis, ResponsiveContainer, Tooltip } from 'recharts';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import type { ColumnDef } from "@tanstack/react-table";
import {
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  useReactTable,
  type SortingState,
} from "@tanstack/react-table";
import {
  DndContext,
  KeyboardSensor,
  MouseSensor,
  TouchSensor,
  closestCenter,
  useSensor,
  useSensors,
  type DragEndEvent,
} from "@dnd-kit/core";
import { restrictToHorizontalAxis } from "@dnd-kit/modifiers";
import {
  SortableContext,
  arrayMove,
  horizontalListSortingStrategy,
  useSortable,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { CSSProperties } from 'react';

import { supabase } from '@/lib/supabase';
import { ClienteComTotais, buscarClientes } from '@/services/clientesService';
import { toast } from 'sonner';

interface Cliente {
  id: string;
  nome: string;
  email: string;
  telefone: string;
  tipo: "pessoa_fisica" | "pessoa_juridica";
  documento: string; // Corresponde a cpf_cnpj no banco
  status: string;
  total_precatorios: number;
  valor_total: number;
  data_cadastro: string;
  ultima_atualizacao: string; // Corresponde a updated_at no banco
}

const columns: ColumnDef<Cliente>[] = [
  {
    id: "nome",
    header: "Nome",
    accessorKey: "nome",
    cell: ({ row }) => (
      <div className="flex items-center gap-3">
        <Avatar className="h-8 w-8">
          <AvatarImage src={`/avatars/${row.original.nome.toLowerCase().replace(' ', '_')}.jpg`} />
          <AvatarFallback>{row.original.nome.split(' ').map(n => n[0]).join('')}</AvatarFallback>
        </Avatar>
        <div>
          <div className="font-medium">{row.getValue("nome")}</div>
          <div className="text-sm text-muted-foreground">{row.original.documento}</div>
        </div>
      </div>
    ),
  },
  {
    id: "tipo",
    header: "Tipo",
    accessorKey: "tipo",
    cell: ({ row }) => (
      <Badge variant="outline">
        {row.getValue("tipo") === "pessoa_fisica" ? "Pessoa Física" : "Pessoa Jurídica"}
      </Badge>
    ),
  },
  {
    id: "contato",
    header: "Contato",
    accessorKey: "contato",
    cell: ({ row }) => (
      <div className="flex flex-col gap-1">
        <div className="flex items-center gap-2 text-sm">
          <Mail className="h-4 w-4 text-muted-foreground" />
          {row.original.email}
        </div>
        <div className="flex items-center gap-2 text-sm">
          <Phone className="h-4 w-4 text-muted-foreground" />
          {row.original.telefone}
        </div>
      </div>
    ),
  },
  {
    id: "precatorios",
    header: "Precatórios",
    accessorKey: "precatorios",
    cell: ({ row }) => (
      <div className="flex items-center gap-2">
        <FileText className="h-4 w-4 text-blue-500" />
        <span>{row.original.total_precatorios}</span>
      </div>
    ),
  },
  {
    id: "valor_total",
    header: "Valor Total",
    accessorKey: "valor_total",
    cell: ({ row }) => (
      <div className="font-medium">
        {new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' })
          .format(row.getValue("valor_total"))}
      </div>
    ),
  },
  {
    id: "status",
    header: "Status",
    accessorKey: "status",
    cell: ({ row }) => (
      <Badge variant={row.getValue("status") === "Ativo" ? "default" : "secondary"}>
        {row.getValue("status")}
      </Badge>
    ),
  },
  {
    id: "ultima_atualizacao",
    header: "Última Atualização",
    accessorKey: "ultima_atualizacao",
    cell: ({ row }) => (
      <div className="text-sm">
        {new Date(row.getValue("ultima_atualizacao")).toLocaleDateString('pt-BR')}
      </div>
    ),
  },
];

const DraggableTableHeader = ({ header }: { header: any }) => {
  const { attributes, isDragging, listeners, setNodeRef, transform, transition } = useSortable({
    id: header.column.id,
  });

  const style: CSSProperties = {
    opacity: isDragging ? 0.8 : 1,
    position: "relative",
    transform: CSS.Translate.toString(transform),
    transition,
    whiteSpace: "nowrap",
    width: header.column.getSize(),
    zIndex: isDragging ? 1 : 0,
  };

  return (
    <TableHead
      ref={setNodeRef}
      className="relative h-10 border-t before:absolute before:inset-y-0 before:start-0 before:w-px before:bg-border first:before:bg-transparent"
      style={style}
    >
      <div className="flex items-center justify-start gap-0.5">
        <Button
          size="icon"
          variant="ghost"
          className="-ml-2 size-7 shadow-none"
          {...attributes}
          {...listeners}
        >
          <GripVertical className="opacity-60" size={16} strokeWidth={2} />
        </Button>
        <span className="grow truncate">
          {header.isPlaceholder
            ? null
            : flexRender(header.column.columnDef.header, header.getContext())}
        </span>
        <Button
          size="icon"
          variant="ghost"
          className="group -mr-1 size-7 shadow-none"
          onClick={header.column.getToggleSortingHandler()}
        >
          {{
            asc: <ChevronUp className="shrink-0 opacity-60" size={16} strokeWidth={2} />,
            desc: <ChevronDown className="shrink-0 opacity-60" size={16} strokeWidth={2} />,
          }[header.column.getIsSorted() as string] ?? (
            <ChevronUp className="shrink-0 opacity-0 group-hover:opacity-60" size={16} strokeWidth={2} />
          )}
        </Button>
      </div>
    </TableHead>
  );
};

const DragAlongCell = ({ cell }: { cell: any }) => {
  const { isDragging, setNodeRef, transform, transition } = useSortable({
    id: cell.column.id,
  });

  const style: CSSProperties = {
    opacity: isDragging ? 0.8 : 1,
    position: "relative",
    transform: CSS.Translate.toString(transform),
    transition,
    width: cell.column.getSize(),
    zIndex: isDragging ? 1 : 0,
  };

  return (
    <TableCell ref={setNodeRef} className="truncate" style={style}>
      {flexRender(cell.column.columnDef.cell, cell.getContext())}
    </TableCell>
  );
};

// Função para converter dados do banco para o formato da interface Cliente
const converterParaCliente = (clienteDB: any): Cliente => {
  return {
    id: clienteDB.id,
    nome: clienteDB.nome,
    email: clienteDB.email,
    telefone: clienteDB.telefone,
    tipo: clienteDB.tipo as "pessoa_fisica" | "pessoa_juridica",
    documento: clienteDB.cpf_cnpj,
    status: clienteDB.status,
    total_precatorios: clienteDB.total_precatorios || 0,
    valor_total: clienteDB.valor_total || 0,
    data_cadastro: clienteDB.data_cadastro,
    ultima_atualizacao: clienteDB.updated_at || clienteDB.created_at,
  };
};

// Mantendo as estatísticas gerais por enquanto
const estatisticasGerais = {
  total_clientes: 150,
  clientes_ativos: 120,
  novos_mes: 8,
  valor_total_precatorios: 25000000,
  precatorios_ativos: 180,
  media_valor: 138888.89,
  taxa_sucesso: 92
};

export default function Customers() {
  const navigate = useNavigate();
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnOrder, setColumnOrder] = useState<string[]>(columns.map((column) => column.id as string));
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState("todos");
  const [filterTipo, setFilterTipo] = useState("todos");
  const [isLoading, setIsLoading] = useState(true);
  const [clientes, setClientes] = useState<Cliente[]>([]);
  const [clientesFiltrados, setClientesFiltrados] = useState<Cliente[]>([]);
  const [estatisticas, setEstatisticas] = useState(estatisticasGerais);

  // Função para carregar os clientes do banco de dados
  const carregarClientes = async () => {
    try {
      setIsLoading(true);
      console.log("Carregando clientes do banco de dados...");
      
      // Usar função do serviço para buscar clientes
      const clientesData = await buscarClientes();
      console.log("Clientes carregados:", clientesData);
      
      if (clientesData && Array.isArray(clientesData)) {
        // Converter para o formato usado pelo componente
        const clientesConvertidos = clientesData.map(converterParaCliente);
        console.log("Clientes convertidos:", clientesConvertidos);
        
        setClientes(clientesConvertidos);
        setClientesFiltrados(clientesConvertidos);

        // Atualiza as estatísticas com base nos dados reais
        if (clientesData.length > 0) {
          const clientesAtivos = clientesData.filter(c => c.status === "Ativo").length;
          const totalPrecatorios = clientesData.reduce((sum, cliente) => sum + (cliente.total_precatorios || 0), 0);
          const valorTotal = clientesData.reduce((sum, cliente) => sum + (cliente.valor_total || 0), 0);
          const mediaValor = totalPrecatorios > 0 ? valorTotal / totalPrecatorios : 0;
          
          setEstatisticas({
            total_clientes: clientesData.length,
            clientes_ativos: clientesAtivos,
            novos_mes: estatisticas.novos_mes, // Mantém o valor original por enquanto
            valor_total_precatorios: valorTotal,
            precatorios_ativos: totalPrecatorios,
            media_valor: mediaValor,
            taxa_sucesso: estatisticas.taxa_sucesso // Mantém o valor original por enquanto
          });
        }
      } else {
        console.error("Formato de dados de clientes inválido:", clientesData);
        toast.error("Os dados não estão no formato esperado.");
      }
    } catch (error) {
      console.error("Erro ao carregar clientes:", error);
      toast.error("Não foi possível carregar os clientes do banco de dados.");
    } finally {
      setIsLoading(false);
    }
  };

  // Carregar clientes ao montar o componente
  useEffect(() => {
    carregarClientes();
  }, []);

  // Recarregar quando os filtros mudarem
  useEffect(() => {
    if (clientes.length === 0) return;
    
    // Filtrar clientes com base nos termos de busca e filtros
    const filtrarClientes = () => {
      let clientesFiltrados = [...clientes];
      
      // Filtrar por termo de busca
      if (searchTerm) {
        const termoBusca = searchTerm.toLowerCase();
        clientesFiltrados = clientesFiltrados.filter(cliente => 
          cliente.nome.toLowerCase().includes(termoBusca) || 
          cliente.email.toLowerCase().includes(termoBusca) || 
          cliente.documento.toLowerCase().includes(termoBusca)
        );
      }
      
      // Filtrar por status
      if (filterStatus !== "todos") {
        clientesFiltrados = clientesFiltrados.filter(cliente => 
          cliente.status.toLowerCase() === filterStatus.toLowerCase()
        );
      }
      
      // Filtrar por tipo
      if (filterTipo !== "todos") {
        clientesFiltrados = clientesFiltrados.filter(cliente => 
          cliente.tipo.toLowerCase() === filterTipo.toLowerCase()
        );
      }
      
      setClientesFiltrados(clientesFiltrados);
    }
    
    filtrarClientes();
  }, [clientes, searchTerm, filterStatus, filterTipo]);

  const sensors = useSensors(
    useSensor(MouseSensor, {
      activationConstraint: {
        distance: 10,
      },
    }),
    useSensor(TouchSensor, {
      activationConstraint: {
        delay: 250,
        tolerance: 5,
      },
    }),
    useSensor(KeyboardSensor)
  );

  function handleDragEnd(event: DragEndEvent) {
    const { active, over } = event;
    if (active.id !== over?.id) {
      setColumnOrder((items) => {
        const oldIndex = items.indexOf(active.id as string);
        const newIndex = items.indexOf(over?.id as string);
        return arrayMove(items, oldIndex, newIndex);
      });
    }
  }

  const handleClientClick = (clientId: string) => {
    navigate(`/customers/${clientId}`);
  };

  const table = useReactTable({
    data: clientesFiltrados,
    columns,
    state: { sorting },
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
  });

  return (
    <div className="flex flex-col gap-6 p-6">
      {/* Header */}
      <div className="flex flex-col gap-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Clientes</h1>
            <p className="text-muted-foreground">
              Gerencie os clientes e seus precatórios
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button 
              variant="outline" 
              className="gap-2"
              onClick={() => carregarClientes()}
            >
              <Filter size={16} />
              Filtros
            </Button>
            <Button className="gap-2" onClick={() => navigate('/customers/new')}>
              <UserPlus size={16} />
              Novo Cliente
            </Button>
          </div>
        </div>

        {/* Estatísticas */}
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardContent className="flex flex-row items-center justify-between p-6">
              <div className="flex flex-col gap-1">
                <p className="text-sm text-muted-foreground">Total de Clientes</p>
                <div className="flex items-center gap-2">
                  <p className="text-2xl font-bold">{estatisticas.total_clientes}</p>
                  <Badge variant="default" className="text-xs">
                    +{estatisticas.novos_mes}
                  </Badge>
                </div>
                <p className="text-xs text-muted-foreground">
                  {estatisticas.clientes_ativos} ativos
                </p>
              </div>
              <div className="rounded-full bg-muted p-3">
                <Users className="w-4 h-4" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="flex flex-row items-center justify-between p-6">
              <div className="flex flex-col gap-1">
                <p className="text-sm text-muted-foreground">Valor Total em Precatórios</p>
                <div className="flex items-center gap-2">
                  <p className="text-2xl font-bold">
                    {new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' })
                      .format(estatisticas.valor_total_precatorios)}
                  </p>
                </div>
                <p className="text-xs text-muted-foreground">
                  {estatisticas.precatorios_ativos} precatórios ativos
                </p>
              </div>
              <div className="rounded-full bg-muted p-3">
                <CircleDollarSign className="w-4 h-4" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="flex flex-row items-center justify-between p-6">
              <div className="flex flex-col gap-1">
                <p className="text-sm text-muted-foreground">Valor Médio</p>
                <div className="flex items-center gap-2">
                  <p className="text-2xl font-bold">
                    {new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' })
                      .format(estatisticas.media_valor)}
                  </p>
                </div>
                <p className="text-xs text-muted-foreground">
                  Por precatório
                </p>
              </div>
              <div className="rounded-full bg-muted p-3">
                <ArrowUpRight className="w-4 h-4" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="flex flex-row items-center justify-between p-6">
              <div className="flex flex-col gap-1">
                <p className="text-sm text-muted-foreground">Taxa de Sucesso</p>
                <div className="flex items-center gap-2">
                  <p className="text-2xl font-bold">{estatisticas.taxa_sucesso}%</p>
                </div>
                <p className="text-xs text-muted-foreground">
                  Precatórios pagos
                </p>
              </div>
              <div className="rounded-full bg-muted p-3">
                <CheckCircle2 className="w-4 h-4" />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Filtros e Tabela */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col gap-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 flex-1">
                <Input
                  placeholder="Buscar cliente..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="max-w-[300px]"
                />
                <Select value={filterStatus} onValueChange={setFilterStatus}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="todos">Todos os Status</SelectItem>
                    <SelectItem value="Ativo">Ativos</SelectItem>
                    <SelectItem value="Inativo">Inativos</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={filterTipo} onValueChange={setFilterTipo}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Tipo" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="todos">Todos os Tipos</SelectItem>
                    <SelectItem value="pessoa_fisica">Pessoa Física</SelectItem>
                    <SelectItem value="pessoa_juridica">Pessoa Jurídica</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="gap-2">
                    <MoreHorizontal size={16} />
                    Ações
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem>
                    <Download className="w-4 h-4 mr-2" />
                    Exportar Lista
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Upload className="w-4 h-4 mr-2" />
                    Importar Clientes
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin" />
              </div>
            ) : clientes.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-8 text-center">
                <AlertCircle className="h-8 w-8 text-muted-foreground mb-2" />
                <h3 className="text-lg font-medium">Nenhum cliente encontrado</h3>
                <p className="text-sm text-muted-foreground mt-1">
                  Tente ajustar os filtros ou adicione um novo cliente.
                </p>
              </div>
            ) : (
              <div className="rounded-md border">
                <DndContext
                  sensors={sensors}
                  collisionDetection={closestCenter}
                  modifiers={[restrictToHorizontalAxis]}
                  onDragEnd={handleDragEnd}
                >
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <SortableContext
                          items={columnOrder}
                          strategy={horizontalListSortingStrategy}
                        >
                          {table.getFlatHeaders().map((header) => (
                            <DraggableTableHeader key={header.id} header={header} />
                          ))}
                        </SortableContext>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {table.getRowModel().rows.length > 0 ? (
                        table.getRowModel().rows.map((row) => (
                          <TableRow
                            key={row.id}
                            className="cursor-pointer"
                            onClick={() => handleClientClick(row.original.id)}
                          >
                            {row.getVisibleCells().map((cell) => (
                              <DragAlongCell key={cell.id} cell={cell} />
                            ))}
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={columns.length} className="h-24 text-center">
                            Nenhum resultado encontrado.
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </DndContext>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}