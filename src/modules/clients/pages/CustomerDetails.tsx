"use client";

import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";
import {
  ArrowLeft,
  FileText,
  User,
  Mail,
  Phone,
  MapPin,
  Building,
  Calendar,
  DollarSign,
  Clock,
  AlertCircle,
  CheckCircle2,
  MessageSquare,
  History,
  Edit,
  Trash2,
  Download,
  Upload,
  MoreHorizontal,
  AlertTriangle,
  CalendarRange,
  CircleDollarSign,
  Activity,
  Share2,
  Printer,
  Link2,
  Bookmark,
  FileSignature,
  ClipboardCheck,
  Loader2,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import { supabase } from "@/lib/supabase";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";

// Interface para o cliente
interface Cliente {
  id: string;
  nome: string;
  email: string;
  telefone: string;
  endereco?: string;
  cidade?: string;
  estado?: string;
  cpf_cnpj: string;
  tipo: "pessoa_fisica" | "pessoa_juridica";
  status: string;
  created_at: string;
  updated_at?: string;
  notas?: string;
  data_cadastro: string;
  total_precatorios?: number;
  valor_total?: number;
}

// Interface para precatórios
interface Precatorio {
  id: string;
  numero_precatorio: string;
  tipo_id: string;
  valor_total: number;
  status: string;
  data_entrada: string;
  data_previsao_pagamento?: string;
  tribunal_id: string;
  entidade_devedora: string;
  beneficiario_id: string;
  tipos_precatorio?: { nome: string };
  tribunais?: { nome: string };
  tipo?: string;
  tribunal?: string;
}

// Dados estatísticos do cliente
interface EstatisticasCliente {
  total_precatorios: number;
  em_andamento: number;
  concluidos: number;
  valor_total: number;
  valor_recebido: number;
  media_prazo: number;
  taxa_sucesso: number;
}

const tipoHistoricoIcons = {
  precatorio: <FileText className="w-4 h-4" />,
  documento: <FileSignature className="w-4 h-4" />,
  analise: <ClipboardCheck className="w-4 h-4" />,
};

const tipoHistoricoColors = {
  precatorio: "text-blue-500",
  documento: "text-green-500",
  analise: "text-purple-500",
};

const formSchema = z.object({
  nome: z.string().min(2, { message: "Nome deve ter pelo menos 2 caracteres" }),
  email: z.string().email({ message: "E-mail inválido" }),
  telefone: z.string().min(10, { message: "Telefone deve ter pelo menos 10 caracteres" }),
  endereco: z.string().optional(),
  cidade: z.string().optional(),
  estado: z.string().optional(),
  status: z.string(),
  notas: z.string().optional(),
});

export default function CustomerDetails() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState("precatorios");
  const [cliente, setCliente] = useState<Cliente | null>(null);
  const [precatorios, setPrecatorios] = useState<Precatorio[]>([]);
  const [estatisticas, setEstatisticas] = useState<EstatisticasCliente>({
    total_precatorios: 0,
    em_andamento: 0,
    concluidos: 0,
    valor_total: 0,
    valor_recebido: 0,
    media_prazo: 120,
    taxa_sucesso: 85,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [modalOpen, setModalOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [confirmDeleteOpen, setConfirmDeleteOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Form para edição do cliente
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      nome: "",
      email: "",
      telefone: "",
      endereco: "",
      cidade: "",
      estado: "",
      status: "Ativo",
      notas: "",
    },
  });

  // Atualizar valores do formulário quando o cliente é carregado
  useEffect(() => {
    if (cliente) {
      form.reset({
        nome: cliente.nome || "",
        email: cliente.email || "",
        telefone: cliente.telefone || "",
        endereco: cliente.endereco || "",
        cidade: cliente.cidade || "",
        estado: cliente.estado || "",
        status: cliente.status || "Ativo",
        notas: cliente.notas || "",
      });
    }
  }, [cliente, form]);

  // Função para atualizar o cliente
  const atualizarCliente = async (data: z.infer<typeof formSchema>) => {
    if (!cliente) return;

    setIsSubmitting(true);

    try {
      const { error } = await supabase
        .from("clientes")
        .update({
          nome: data.nome,
          email: data.email,
          telefone: data.telefone,
          endereco: data.endereco,
          cidade: data.cidade,
          estado: data.estado,
          status: data.status,
          notas: data.notas,
          updated_at: new Date().toISOString(),
        })
        .eq("id", cliente.id);

      if (error) throw error;

      // Atualiza o cliente na tela
      setCliente({
        ...cliente,
        nome: data.nome,
        email: data.email,
        telefone: data.telefone,
        endereco: data.endereco,
        cidade: data.cidade,
        estado: data.estado,
        status: data.status,
        notas: data.notas,
        updated_at: new Date().toISOString(),
      });

      toast.success("Cliente atualizado com sucesso!");
      setModalOpen(false);
    } catch (error) {
      console.error("Erro ao atualizar cliente:", error);
      toast.error("Erro ao atualizar cliente. Tente novamente.");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Dados mockados para histórico e documentos (a serem substituídos por dados reais no futuro)
  const [historico, setHistorico] = useState([
    {
      data: "2024-03-20",
      tipo: "precatorio",
      acao: "Atualização de Status",
      detalhes: "Precatório atualizado para 'Em Processamento'",
      usuario: "Ana Costa",
    },
    {
      data: "2024-03-18",
      tipo: "documento",
      acao: "Upload de Documento",
      detalhes: "Adicionada certidão atualizada ao precatório",
      usuario: "Carlos Santos",
    },
    {
      data: "2024-03-17",
      tipo: "analise",
      acao: "Análise Técnica",
      detalhes: "Realizada análise dos cálculos do precatório",
      usuario: "Maria Oliveira",
    },
  ]);

  const [documentos, setDocumentos] = useState([
    {
      nome: "RG",
      tipo: "Documento Pessoal",
      status: "Aprovado",
      data: "2024-01-15",
      usuario: "Ana Costa",
    },
    {
      nome: "CPF",
      tipo: "Documento Pessoal",
      status: "Aprovado",
      data: "2024-01-15",
      usuario: "Ana Costa",
    },
    {
      nome: "Comprovante de Residência",
      tipo: "Documento Pessoal",
      status: "Pendente",
      data: "2024-03-18",
      usuario: "Carlos Santos",
    },
  ]);

  // Função para carregar os dados do cliente
  const carregarDadosCliente = async () => {
    if (!id) return;

    // Se o ID for 'new', redirecionar para o formulário de criação
    if (id === "new") {
      navigate("/customers/new");
      return;
    }

    try {
      setIsLoading(true);

      // Buscar dados do cliente
      const { data: clienteData, error: clienteError } = await supabase
        .from("view_clientes_com_totais")
        .select("*")
        .eq("id", id)
        .single();

      if (clienteError) {
        console.error("Erro ao buscar cliente:", clienteError);
        toast.error("Erro ao carregar dados do cliente");
        throw clienteError;
      }

      if (!clienteData) {
        toast.error("Cliente não encontrado");
        return;
      }

      setCliente(clienteData);

      // Buscar precatórios do cliente
      const { data: precatoriosData, error: precatoriosError } = await supabase
        .from("precatorios")
        .select(`
          *,
          tipos_precatorio (nome),
          tribunais (nome)
        `)
        .eq("beneficiario_id", id);

      if (precatoriosError) {
        console.error("Erro ao buscar precatórios:", precatoriosError);
        toast.error("Erro ao carregar precatórios");
        throw precatoriosError;
      }

      // Formatar os dados dos precatórios
      const precatoriosFormatados = precatoriosData?.map((p) => ({
        ...p,
        tipo: p.tipos_precatorio?.nome || "Não especificado",
        tribunal: p.tribunais?.nome || "Não especificado",
      })) || [];

      setPrecatorios(precatoriosFormatados);

      // Calcular estatísticas
      if (precatoriosFormatados.length > 0) {
        const emAndamento = precatoriosFormatados.filter((p) => p.status !== "Concluído").length;
        const concluidos = precatoriosFormatados.filter((p) => p.status === "Concluído").length;
        const valorTotal = precatoriosFormatados.reduce((sum, p) => sum + (p.valor_total || 0), 0);
        const valorRecebido = precatoriosFormatados
          .filter((p) => p.status === "Concluído")
          .reduce((sum, p) => sum + (p.valor_total || 0), 0);

        setEstatisticas({
          total_precatorios: precatoriosFormatados.length,
          em_andamento: emAndamento,
          concluidos: concluidos,
          valor_total: valorTotal,
          valor_recebido: valorRecebido,
          media_prazo: 120, // Valor mockado por enquanto
          taxa_sucesso: 85, // Valor mockado por enquanto
        });
      }
    } catch (error) {
      console.error("Erro ao carregar dados do cliente:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Carregar dados ao montar o componente
  useEffect(() => {
    carregarDadosCliente();
  }, [id]);

  // Compartilhar cliente (mockado por enquanto)
  const handleShare = () => {
    navigator.clipboard.writeText(window.location.href);
    toast.success("Link copiado para a área de transferência!");
  };

  // Imprimir detalhes do cliente (mockado por enquanto)
  const handlePrint = () => {
    window.print();
  };

  // Função para excluir cliente
  const handleDeleteCliente = async () => {
    if (!cliente) return;

    setIsDeleting(true);

    try {
      // Verificar se o cliente tem precatórios
      if (precatorios.length > 0) {
        toast.error("Este cliente possui precatórios associados. Exclua-os primeiro.");
        setConfirmDeleteOpen(false);
        setIsDeleting(false);
        return;
      }

      // Excluir cliente do Supabase
      const { error } = await supabase
        .from("clientes")
        .delete()
        .eq("id", cliente.id);

      if (error) throw error;

      toast.success("Cliente excluído com sucesso!");
      setConfirmDeleteOpen(false);

      // Redirecionar para a lista de clientes
      navigate("/customers");
    } catch (error) {
      console.error("Erro ao excluir cliente:", error);
      toast.error("Erro ao excluir cliente. Tente novamente.");
    } finally {
      setIsDeleting(false);
    }
  };

  // Função para carregar os dados do cliente
  const handlePrecatorioClick = (precatorioId: string) => {
    navigate(`/precatorio/${precatorioId}`);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!cliente) {
    return (
      <div className="flex flex-col items-center justify-center h-screen">
        <AlertCircle className="h-12 w-12 text-destructive mb-4" />
        <h2 className="text-2xl font-bold mb-2">Cliente não encontrado</h2>
        <p className="text-muted-foreground mb-4">O cliente solicitado não existe ou foi removido.</p>
        <Button onClick={() => navigate("/customers")}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Voltar para a lista de clientes
        </Button>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" onClick={() => navigate("/customers")}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <div className="flex items-center gap-3">
              <h1 className="text-2xl font-bold">{cliente.nome}</h1>
              <Badge variant="outline">
                {cliente.tipo === "pessoa_fisica" ? "Pessoa Física" : "Pessoa Jurídica"}
              </Badge>
              <Badge variant={cliente.status === "Ativo" ? "default" : "secondary"}>
                {cliente.status}
              </Badge>
            </div>
            <div className="flex items-center gap-2 mt-1 text-muted-foreground">
              <span>Cliente desde {new Date(cliente.created_at).toLocaleDateString("pt-BR")}</span>
            </div>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" className="gap-2" onClick={handleShare}>
            <Share2 className="w-4 h-4" />
            Compartilhar
          </Button>
          <Button variant="outline" className="gap-2" onClick={handlePrint}>
            <Printer className="w-4 h-4" />
            Imprimir
          </Button>
          <Dialog open={modalOpen} onOpenChange={setModalOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" className="gap-2">
                <Edit className="w-4 h-4" />
                Editar
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle>Editar Cliente</DialogTitle>
                <DialogDescription>
                  Atualize as informações do cliente aqui. Clique em salvar quando terminar.
                </DialogDescription>
              </DialogHeader>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(atualizarCliente)} className="space-y-4">
                  <FormField
                    control={form.control}
                    name="nome"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Nome</FormLabel>
                        <FormControl>
                          <Input placeholder="Nome do cliente" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>E-mail</FormLabel>
                          <FormControl>
                            <Input placeholder="Email" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="telefone"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Telefone</FormLabel>
                          <FormControl>
                            <Input placeholder="Telefone" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  <FormField
                    control={form.control}
                    name="endereco"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Endereço</FormLabel>
                        <FormControl>
                          <Input placeholder="Endereço" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="cidade"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Cidade</FormLabel>
                          <FormControl>
                            <Input placeholder="Cidade" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="estado"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Estado</FormLabel>
                          <FormControl>
                            <Input placeholder="Estado" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Status</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Selecione o status" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="Ativo">Ativo</SelectItem>
                            <SelectItem value="Inativo">Inativo</SelectItem>
                            <SelectItem value="Suspenso">Suspenso</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="notas"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Notas</FormLabel>
                        <FormControl>
                          <Textarea placeholder="Observações" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <DialogFooter>
                    <Button type="button" variant="outline" onClick={() => setModalOpen(false)}>
                      Cancelar
                    </Button>
                    <Button type="submit" disabled={isSubmitting}>
                      {isSubmitting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Salvando...
                        </>
                      ) : (
                        "Salvar alterações"
                      )}
                    </Button>
                  </DialogFooter>
                </form>
              </Form>
            </DialogContent>
          </Dialog>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="icon">
                <MoreHorizontal className="w-4 h-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>
                <Download className="w-4 h-4 mr-2" />
                Exportar Dados
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Upload className="w-4 h-4 mr-2" />
                Anexar Documento
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Link2 className="w-4 h-4 mr-2" />
                Vincular Precatório
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Bookmark className="w-4 h-4 mr-2" />
                Adicionar aos Favoritos
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <AlertDialog open={confirmDeleteOpen} onOpenChange={setConfirmDeleteOpen}>
                <AlertDialogTrigger asChild>
                  <DropdownMenuItem className="text-red-600" onSelect={(e) => {
                    e.preventDefault();
                    setConfirmDeleteOpen(true);
                  }}>
                    <Trash2 className="w-4 h-4 mr-2" />
                    Excluir Cliente
                  </DropdownMenuItem>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Tem certeza?</AlertDialogTitle>
                    <AlertDialogDescription>
                      Esta ação não pode ser desfeita. Isso excluirá permanentemente o cliente <strong>{cliente?.nome}</strong> e todos os seus dados associados.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancelar</AlertDialogCancel>
                    <AlertDialogAction disabled={isDeleting} onClick={(e) => {
                      e.preventDefault();
                      handleDeleteCliente();
                    }} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
                      {isDeleting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Excluindo...
                        </>
                      ) : (
                        "Sim, excluir cliente"
                      )}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Informações Básicas */}
      <div className="grid grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <Avatar className="h-16 w-16">
                <AvatarImage src={`/avatars/cliente.jpg`} />
                <AvatarFallback>{cliente.nome.charAt(0)}</AvatarFallback>
              </Avatar>
              <div>
                <div className="flex items-center gap-3">
                  <h2 className="text-lg font-bold">{cliente.nome}</h2>
                  <Badge variant="outline">
                    {cliente.tipo === "pessoa_fisica" ? "Pessoa Física" : "Pessoa Jurídica"}
                  </Badge>
                  <Badge variant={cliente.status === "Ativo" ? "default" : "secondary"}>
                    {cliente.status}
                  </Badge>
                </div>
                <div className="flex items-center gap-2 mt-1 text-muted-foreground">
                  <span>Cliente desde {new Date(cliente.created_at).toLocaleDateString("pt-BR")}</span>
                </div>
              </div>
            </div>
            <Separator className="my-4" />
            <div className="space-y-3">
              <div className="flex items-center gap-2 text-sm">
                <Mail className="w-4 h-4 text-muted-foreground" />
                <span>{cliente.email}</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Phone className="w-4 h-4 text-muted-foreground" />
                <span>{cliente.telefone}</span>
              </div>
              {cliente.endereco && (
                <div className="flex items-center gap-2 text-sm">
                  <MapPin className="w-4 h-4 text-muted-foreground" />
                  <span>
                    {cliente.endereco}
                    {cliente.cidade && cliente.estado ? `, ${cliente.cidade} - ${cliente.estado}` : ""}
                  </span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Estatísticas de Precatórios</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-muted-foreground">Total</p>
                <div className="flex items-center gap-2">
                  <FileText className="w-4 h-4 text-blue-500" />
                  <span className="text-2xl font-bold">{estatisticas.total_precatorios}</span>
                </div>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Em Andamento</p>
                <div className="flex items-center gap-2">
                  <Activity className="w-4 h-4 text-yellow-500" />
                  <span className="text-2xl font-bold">{estatisticas.em_andamento}</span>
                </div>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Valor Total</p>
                <div className="flex items-center gap-2">
                  <CircleDollarSign className="w-4 h-4 text-green-500" />
                  <span className="text-lg font-bold">
                    R$ {estatisticas.valor_total.toLocaleString("pt-BR", { minimumFractionDigits: 2 })}
                  </span>
                </div>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Valor Recebido</p>
                <div className="flex items-center gap-2">
                  <DollarSign className="w-4 h-4 text-green-500" />
                  <span className="text-lg font-bold">
                    R$ {estatisticas.valor_recebido.toLocaleString("pt-BR", { minimumFractionDigits: 2 })}
                  </span>
                </div>
              </div>
            </div>
            <Separator className="my-4" />
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Taxa de Sucesso</span>
                <Badge variant="outline">{estatisticas.taxa_sucesso}%</Badge>
              </div>
              <Progress value={estatisticas.taxa_sucesso} className="h-2" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Observações</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm">{cliente.notas || "Sem observações"}</p>
            <Separator className="my-4" />
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Última Atualização</span>
                <span>{cliente.updated_at ? new Date(cliente.updated_at).toLocaleDateString("pt-BR") : "N/A"}</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Prazo Médio</span>
                <span>{estatisticas.media_prazo} dias</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList>
          <TabsTrigger value="precatorios">Precatórios</TabsTrigger>
          <TabsTrigger value="historico">Histórico</TabsTrigger>
          <TabsTrigger value="documentos">Documentos</TabsTrigger>
        </TabsList>

        <TabsContent value="precatorios" className="mt-4">
          <div className="space-y-4">
            {precatorios.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-12">
                <FileText className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">Nenhum precatório encontrado</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Este cliente ainda não possui precatórios cadastrados.
                </p>
                <Button onClick={() => navigate("/precatorios/novo")}>
                  Cadastrar Precatório
                </Button>
              </div>
            ) : (
              precatorios.map((precatorio) => (
                <Card
                  key={precatorio.id}
                  className="cursor-pointer hover:bg-accent/50 transition-colors"
                  onClick={() => handlePrecatorioClick(precatorio.id)}
                >
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div>
                        <div className="flex items-center gap-2">
                          <h3 className="font-medium">{precatorio.numero_precatorio}</h3>
                          <Badge variant="outline">{precatorio.tipo}</Badge>
                          <Badge
                            variant={
                              precatorio.status === "Concluído"
                                ? "default"
                                : precatorio.status === "Em Processamento"
                                ? "secondary"
                                : "outline"
                            }
                          >
                            {precatorio.status}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-4 mt-2 text-sm text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <Building className="w-4 h-4" />
                            {precatorio.tribunal}
                          </div>
                          <div className="flex items-center gap-1">
                            <User className="w-4 h-4" />
                            {precatorio.entidade_devedora}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">
                          {new Intl.NumberFormat("pt-BR", {
                            style: "currency",
                            currency: "BRL",
                          }).format(precatorio.valor_total)}
                        </p>
                        {precatorio.data_previsao_pagamento && (
                          <div className="flex items-center gap-2 mt-1 text-sm text-muted-foreground">
                            <CalendarRange className="w-4 h-4" />
                            <span>
                              Previsão:{" "}
                              {new Date(precatorio.data_previsao_pagamento).toLocaleDateString("pt-BR")}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                    <Separator className="my-4" />
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Progresso</span>
                        <span>
                          {precatorio.status === "Concluído" ? "100" : "50"}%
                        </span>
                      </div>
                      <Progress value={precatorio.status === "Concluído" ? 100 : 50} className="h-2" />
                      <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center gap-1 text-muted-foreground">
                          <History className="w-4 h-4" />
                          <span>
                            Entrada:{" "}
                            {new Date(precatorio.data_entrada).toLocaleDateString("pt-BR")}
                          </span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </TabsContent>

        <TabsContent value="historico" className="mt-4">
          <Card>
            <CardContent className="p-6">
              <ScrollArea className="h-[500px] pr-4">
                {/* Histórico do cliente */}
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="documentos" className="mt-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-medium">Documentos do Cliente</h3>
                <Button>
                  <Upload className="w-4 h-4 mr-2" />
                  Novo Documento
                </Button>
              </div>
              {/* Lista de documentos */}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}