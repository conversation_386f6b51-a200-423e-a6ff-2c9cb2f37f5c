import { motion } from "framer-motion";
import { <PERSON>, Lock, User, Globe, Moon, Sun } from "lucide-react";
import { useTheme } from "next-themes";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/components/ui/use-toast";

const Settings = () => {
  const { setTheme, theme } = useTheme();
  const { toast } = useToast();

  const handleThemeChange = () => {
    const newTheme = theme === "dark" ? "light" : "dark";
    setTheme(newTheme);
    toast({
      title: "Theme Updated",
      description: `Changed to ${newTheme} mode`,
      duration: 2000,
    });
  };

  const sections = [
    {
      title: "Profile Settings",
      icon: User,
      fields: ["Name", "Email", "Phone", "Address"],
    },
    {
      title: "Security",
      icon: Lock,
      fields: ["Password", "Two-factor Authentication", "Login History"],
    },
    {
      title: "Notifications",
      icon: <PERSON>,
      fields: ["Email Notifications", "Push Notifications", "SMS Alerts"],
    },
    {
      title: "Preferences",
      icon: Globe,
      fields: ["Language", "Time Zone", "Date Format"],
    },
  ];

  return (
    <div className="flex flex-1">
      <div className="p-2 md:p-10 rounded-tl-2xl border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-900 flex flex-col gap-4 flex-1 w-full h-full">
        <h1 className="text-2xl font-bold">Settings</h1>
        
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-6 p-4 bg-gray-50 dark:bg-neutral-800 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {theme === 'dark' ? (
                <Moon className="h-5 w-5 text-gray-600 dark:text-gray-400" />
              ) : (
                <Sun className="h-5 w-5 text-gray-600" />
              )}
              <span className="font-medium">Dark Mode</span>
            </div>
            <Switch
              checked={theme === 'dark'}
              onCheckedChange={handleThemeChange}
              className="data-[state=checked]:bg-gray-600"
            />
          </div>
        </motion.div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {sections.map((section, sectionIndex) => (
            <motion.div
              key={section.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: sectionIndex * 0.1 }}
              className="space-y-4"
            >
              <div className="flex items-center gap-2">
                <section.icon className="h-5 w-5" />
                <h2 className="text-lg font-semibold">{section.title}</h2>
              </div>
              <div className="space-y-4">
                {section.fields.map((field, fieldIndex) => (
                  <motion.div
                    key={field}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: (sectionIndex * 0.1) + (fieldIndex * 0.05) }}
                    className="p-4 bg-gray-50 dark:bg-neutral-800 rounded-lg"
                  >
                    <div className="flex justify-between items-center">
                      <span>{field}</span>
                      <div className="h-8 w-32 bg-gray-200 dark:bg-neutral-700 rounded"></div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Settings;