import { useEffect, useState, useRef } from "react";
import { TaskManager, TaskManagerHandles } from "@/features/tasks/components/TaskManager";
import { useLocation } from "react-router-dom";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { PlusCircle, ListChecks, CheckSquare, FileText } from "lucide-react";

export default function Tasks() {
  const location = useLocation();
  const [area, setArea] = useState<'PRECATORIO' | 'RPV' | 'AMBOS' | null>(null);
  const [activeTab, setActiveTab] = useState<string>("todas");
  const taskManagerRef = useRef<TaskManagerHandles>(null);
  
  // Usar o parâmetro de área da URL
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const areaParam = params.get("area");
    
    if (areaParam === "PRECATORIO" || areaParam === "RPV" || areaParam === "AMBOS") {
      setArea(areaParam);
    } else {
      setArea(null); // Configurar como null para mostrar todas as tarefas
    }
  }, [location.search]);

  // Função para abrir o modal de criação de tarefa
  const handleOpenCreateTask = () => {
    if (taskManagerRef.current) {
      taskManagerRef.current.openCreateTask();
    }
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Gerenciamento de Tarefas</h1>
          <p className="text-muted-foreground mt-1">
            Organize, acompanhe e gerencie todas as suas tarefas em um só lugar
          </p>
        </div>
        {area && (
          <Badge variant="outline" className="text-md px-3 py-1 bg-muted">
            {area === "PRECATORIO" ? "Precatórios" : area === "RPV" ? "RPVs" : "Todas as Áreas"}
          </Badge>
        )}
      </div>
      
      <Tabs defaultValue="todas" value={activeTab} onValueChange={setActiveTab} className="mb-6">
        <div className="flex justify-between items-center">
          <TabsList>
            <TabsTrigger value="todas" className="flex items-center gap-1">
              <ListChecks className="h-4 w-4" />
              Todas as Tarefas
            </TabsTrigger>
            <TabsTrigger value="precatorios" className="flex items-center gap-1">
              <FileText className="h-4 w-4" />
              Precatórios
            </TabsTrigger>
            <TabsTrigger value="rpvs" className="flex items-center gap-1">
              <CheckSquare className="h-4 w-4" />
              RPVs
            </TabsTrigger>
          </TabsList>
          
          <Button className="gap-1" onClick={handleOpenCreateTask}>
            <PlusCircle className="h-4 w-4" />
            Nova Tarefa
          </Button>
        </div>
        
        <TabsContent value="todas" className="mt-6">
          <TaskManager 
            ref={taskManagerRef}
            areaFilter={null}
            defaultView="lista"
            showHeader={false}
            showFilters={true}
          />
        </TabsContent>
        
        <TabsContent value="precatorios" className="mt-6">
          <TaskManager 
            areaFilter="PRECATORIO"
            defaultView="lista"
            showHeader={false}
            showFilters={true}
          />
        </TabsContent>
        
        <TabsContent value="rpvs" className="mt-6">
          <TaskManager 
            areaFilter="RPV"
            defaultView="lista"
            showHeader={false}
            showFilters={true}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
} 