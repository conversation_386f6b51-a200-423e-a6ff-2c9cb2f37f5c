'use client';

import { Card } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import {
  DndContext,
  rectIntersection,
  useDraggable,
  useDroppable,
  DragOverlay,
  defaultDropAnimationSideEffects,
  DragStartEvent,
  DragOverEvent,
} from '@dnd-kit/core';
import type { DragEndEvent } from '@dnd-kit/core';
import type { ReactNode } from 'react';
import { createPortal } from 'react-dom';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { MoreHorizontal, Filter, SortAsc, AlertCircle, ChevronLeft, ChevronRight } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

export type Status = {
  id: string;
  name: string;
  color: string;
};

export type Feature = {
  id: string;
  name: string;
  startAt: Date;
  endAt: Date;
  status: Status;
};

export type KanbanBoardProps = {
  id: Status['id'];
  children: ReactNode;
  className?: string;
};

export const KanbanBoard = ({ id, children, className }: KanbanBoardProps) => {
  const { isOver, setNodeRef } = useDroppable({ id });

  return (
    <div
      className={cn(
        'flex flex-col gap-1 rounded-lg border bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm p-2 text-xs transition-all w-[240px] shrink-0 min-h-[500px]',
        isOver ? 'ring-2 ring-primary ring-offset-2' : 'ring-0',
        className
      )}
      ref={setNodeRef}
    >
      {children}
    </div>
  );
};

export type KanbanCardProps = Pick<Feature, 'id' | 'name'> & {
  index: number;
  parent: string;
  children?: ReactNode;
  className?: string;
  onClick?: () => void;
};

export const KanbanCard = ({
  id,
  name,
  index,
  parent,
  children,
  className,
  onClick,
}: KanbanCardProps) => {
  const { attributes, listeners, setNodeRef, transform, isDragging } =
    useDraggable({
      id,
      data: { index, parent },
    });

  return (
    <Card
      className={cn(
        'rounded-lg p-3 shadow-sm transition-all cursor-grab active:cursor-grabbing hover:shadow-md',
        isDragging ? 'opacity-50 scale-105' : 'opacity-100 scale-100',
        className
      )}
      style={{
        transform: transform
          ? `translate3d(${transform.x}px, ${transform.y}px, 0) scale(${isDragging ? 1.05 : 1})`
          : undefined,
      }}
      {...listeners}
      {...attributes}
      ref={setNodeRef}
      onClick={onClick}
    >
      {children ?? <p className="m-0 font-medium text-sm">{name}</p>}
    </Card>
  );
};

export type KanbanCardsProps = {
  children: ReactNode;
  className?: string;
};

export const KanbanCards = ({ children, className }: KanbanCardsProps) => (
  <div className={cn('flex-1 flex flex-col gap-2 overflow-y-auto pr-1 min-h-0 max-h-full', className)}>
    {children}
  </div>
);

export type KanbanHeaderProps =
  | {
      children: ReactNode;
    }
  | {
      name: Status['name'];
      color: Status['color'];
      className?: string;
      count?: number;
      total?: number;
      onSort?: () => void;
      onFilter?: () => void;
      onConfig?: () => void;
      alerts?: number;
      isRecolhida?: boolean;
      onToggleRecolher?: () => void;
    };

export const KanbanHeader = (props: KanbanHeaderProps) =>
  'children' in props ? (
    props.children
  ) : (
    <div className={cn('flex flex-col gap-2 sticky top-0 bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm py-2 z-10', props.className)}>
      {props.isRecolhida ? (
        <div className="flex flex-col h-full min-h-[500px]">
          <div className="flex items-center justify-between mb-2">
            <div
              className="h-3 w-3 rounded-full shrink-0"
              style={{ backgroundColor: props.color }}
            />
            <Button 
              variant="ghost" 
              size="icon" 
              className="h-6 w-6"
              onClick={props.onToggleRecolher}
            >
              <ChevronRight className="w-4 h-4" />
            </Button>
          </div>
          <div className="flex-1 flex flex-col justify-between items-center py-4">
            <div className="flex-1 flex items-center justify-center">
              <div className="rotate-[-90deg] whitespace-nowrap origin-center flex items-center gap-2 translate-y-8">
                <p className="m-0 font-semibold text-sm">{props.name}</p>
                <span className="text-xs text-neutral-500">({props.count || 0})</span>
              </div>
            </div>
            {props.alerts && props.alerts > 0 && (
              <div className="mt-auto text-amber-500">
                <AlertCircle className="w-4 h-4" />
              </div>
            )}
          </div>
        </div>
      ) : (
        <>
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2 min-w-0">
              <div
                className="h-3 w-3 rounded-full shrink-0"
                style={{ backgroundColor: props.color }}
              />
              <p className="m-0 font-semibold text-sm truncate">{props.name}</p>
              {props.alerts && props.alerts > 0 && (
                <div className="flex items-center gap-1 text-amber-500">
                  <AlertCircle className="w-4 h-4" />
                  <span className="text-xs">{props.alerts}</span>
                </div>
              )}
            </div>
            <div className="flex items-center gap-1">
              <Button 
                variant="ghost" 
                size="icon" 
                className="h-6 w-6"
                onClick={props.onToggleRecolher}
              >
                <ChevronLeft className="w-4 h-4" />
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-6 w-6">
                    <MoreHorizontal className="w-4 h-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  <DropdownMenuLabel className="text-xs">Opções da Coluna</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem className="text-xs gap-2" onClick={props.onSort}>
                    <SortAsc className="w-4 h-4" />
                    Ordenar por Data
                  </DropdownMenuItem>
                  <DropdownMenuItem className="text-xs gap-2" onClick={props.onSort}>
                    <SortAsc className="w-4 h-4" />
                    Ordenar por Valor
                  </DropdownMenuItem>
                  <DropdownMenuItem className="text-xs gap-2" onClick={props.onFilter}>
                    <Filter className="w-4 h-4" />
                    Filtrar Cards
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem className="text-xs" onClick={props.onConfig}>
                    Configurar Coluna
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
          <div className="flex items-center justify-between px-1">
            <div className="flex items-center gap-2">
              <span className="text-xs font-medium">{props.count || 0}</span>
              <span className="text-xs text-neutral-500">cards</span>
            </div>
            {props.total && (
              <div className="text-xs text-neutral-500">
                R$ {props.total.toLocaleString()}
              </div>
            )}
          </div>
          <div className="h-1 w-full bg-neutral-100 dark:bg-neutral-700 rounded-full overflow-hidden">
            <div 
              className="h-full rounded-full transition-all" 
              style={{ 
                width: `${props.count && props.count > 0 ? (props.count / 10) * 100 : 0}%`,
                backgroundColor: props.color 
              }}
            />
          </div>
        </>
      )}
    </div>
  );

export type KanbanProviderProps = {
  children: ReactNode;
  onDragEnd: (event: DragEndEvent) => void;
  className?: string;
};

const dropAnimation = {
  sideEffects: defaultDropAnimationSideEffects({
    styles: {
      active: {
        opacity: '0.5',
      },
    },
  }),
};

export const KanbanProvider = ({
  children,
  onDragEnd,
  className,
}: KanbanProviderProps) => (
  <DndContext 
    onDragEnd={onDragEnd}
  >
    <div
      className={cn('flex gap-4 min-w-full pb-20', className)}
    >
      {children}
    </div>
  </DndContext>
); 