"use client"

import * as React from "react"
import { useEffect, useRef, useState } from "react";
import { motion } from "framer-motion";
import { X } from "lucide-react";

type Tag = {
  id: string;
  label: string;
};

type TagsSelectorProps = {
  tags: Tag[];
  selectedTags: string[];
  onTagSelect: (tags: string[]) => void;
};

export function TagsSelector({ tags, selectedTags: selectedTagIds, onTagSelect }: TagsSelectorProps) {
  const selectedsContainerRef = useRef<HTMLDivElement>(null);

  const removeSelectedTag = (id: string) => {
    onTagSelect(selectedTagIds.filter(tagId => tagId !== id));
  };

  const addSelectedTag = (tag: Tag) => {
    onTagSelect([...selectedTagIds, tag.id]);
  };

  const selectedTags = tags.filter(tag => selectedTagIds.includes(tag.id));

  useEffect(() => {
    if (selectedsContainerRef.current) {
      selectedsContainerRef.current.scrollTo({
        left: selectedsContainerRef.current.scrollWidth,
        behavior: "smooth",
      });
    }
  }, [selectedTagIds]);

  return (
    <div className="p-4 w-full flex flex-col">
      <motion.h2 layout className="text-sm font-medium text-muted-foreground mb-2">
        Filter by tags
      </motion.h2>
      <motion.div
        className="w-full flex items-center justify-start gap-1 bg-white dark:bg-neutral-800 border dark:border-neutral-700 h-10 mb-2 overflow-x-auto p-1 no-scrollbar"
        style={{
          borderRadius: 8,
        }}
        ref={selectedsContainerRef}
        layout
      >
        {selectedTags.map((tag) => (
          <motion.div
            key={tag.id}
            className="flex items-center gap-1 pl-2 pr-1 py-0.5 bg-white dark:bg-neutral-800 shadow-sm border dark:border-neutral-700 h-full shrink-0"
            style={{
              borderRadius: 6,
            }}
            layoutId={`tag-${tag.id}`}
          >
            <motion.span
              layoutId={`tag-${tag.id}-label`}
              className="text-sm text-gray-700 dark:text-gray-300 font-medium"
            >
              {tag.label}
            </motion.span>
            <button
              onClick={() => removeSelectedTag(tag.id)}
              className="p-0.5 rounded-full hover:bg-gray-100 dark:hover:bg-neutral-700"
            >
              <X className="size-3.5 text-gray-500 dark:text-gray-400" />
            </button>
          </motion.div>
        ))}
      </motion.div>
      {tags.length > selectedTags.length && (
        <motion.div
          className="bg-white dark:bg-neutral-800 shadow-sm border dark:border-neutral-700 w-full"
          style={{
            borderRadius: 8,
          }}
          layout
        >
          <motion.div className="flex flex-wrap gap-1 p-1.5">
            {tags
              .filter(tag => !selectedTagIds.includes(tag.id))
              .map((tag) => (
                <motion.button
                  key={tag.id}
                  layoutId={`tag-${tag.id}`}
                  className="flex items-center gap-1 px-3 py-1.5 bg-gray-100/60 dark:bg-neutral-700/60 hover:bg-gray-200/60 dark:hover:bg-neutral-600/60 rounded-md shrink-0 transition-colors"
                  onClick={() => addSelectedTag(tag)}
                >
                  <motion.span
                    layoutId={`tag-${tag.id}-label`}
                    className="text-sm text-gray-700 dark:text-gray-300 font-medium"
                  >
                    {tag.label}
                  </motion.span>
                </motion.button>
              ))}
          </motion.div>
        </motion.div>
      )}
    </div>
  );
}