import { TableCell } from "@/components/ui/table";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Cell } from "@tanstack/react-table";
import { CSSProperties } from "react";

interface DragAlongCellProps {
  cell: Cell<any, unknown>;
}

export function DragAlongCell({ cell }: DragAlongCellProps) {
  const { isDragging, setNodeRef, transform, transition } = useSortable({
    id: cell.column.id,
  });

  const style: CSSProperties = {
    opacity: isDragging ? 0.8 : 1,
    position: "relative",
    transform: CSS.Translate.toString(transform),
    transition,
    width: cell.column.getSize(),
    zIndex: isDragging ? 1 : 0,
  };

  return (
    <TableCell ref={setNodeRef} className="truncate" style={style}>
      {cell.getValue()?.toString()}
    </TableCell>
  );
}