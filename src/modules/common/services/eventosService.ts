import { supabase, refreshSupabaseSession } from '@/lib/supabase';
import { Evento } from '@/utils/calendarHelpers';
import { v4 as uuidv4 } from 'uuid';

// Função utilitária para validar se uma string é um UUID válido
const isValidUUID = (str: string): boolean => {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(str);
};

// Interface para o evento como armazenado no banco de dados
interface EventoDB {
  id: string;
  tipo: string;
  titulo: string;
  descricao: string | null;
  data: string;
  hora: string;
  duracao: number;
  status: string;
  prioridade: string;
  local: string | null;
  responsavel: string | null;
  tipo_entidade: string | null;
  entidade_id: string | null;
  created_at: string;
  updated_at: string;
  created_by: string | null;
  updated_by: string | null;
}

// Obter o ID do usuário atual
const getUserId = async (): Promise<string | null> => {
  try {
    // Verificar a sessão atual
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session || !session.user) {
      console.warn("getUserId: Nenhuma sessão ou usuário encontrado");
      // Se não há sessão, tentar renovar
      try {
        const { data: refreshData } = await supabase.auth.refreshSession();
        if (refreshData && refreshData.session && refreshData.session.user) {
          console.log("getUserId: Sessão renovada com sucesso");
          return refreshData.session.user.id;
        }
      } catch (refreshError) {
        console.error("getUserId: Erro ao tentar renovar sessão", refreshError);
      }
      return null;
    }
    
    return session.user.id;
  } catch (error) {
    console.error("getUserId: Erro ao obter ID do usuário", error);
    return null;
  }
};

// Converter de EventoDB para Evento (frontend)
const mapEventoDBToEvento = (eventoDB: EventoDB): Evento => {
  return {
    id: eventoDB.id,
    tipo: eventoDB.tipo,
    titulo: eventoDB.titulo,
    descricao: eventoDB.descricao || '',
    data: eventoDB.data,
    hora: eventoDB.hora,
    duracao: eventoDB.duracao,
    status: eventoDB.status,
    prioridade: eventoDB.prioridade,
    local: eventoDB.local || '',
    responsavel: eventoDB.responsavel || ''
  };
};

// Buscar todos os eventos
export const getEventos = async (): Promise<Evento[]> => {
  try {
    // Verificar autenticação
    const userId = await getUserId();
    if (!userId) {
      // Em vez de lançar um erro, tentamos verificar se há um token no localStorage
      console.log("getEventos: Usuário não autenticado, tentando recuperar token");
      
      // Verificar se o token supabase existe no localStorage
      const token = localStorage.getItem('sb-ubwzukpsqcrwzfbppoux-auth-token');
      if (!token) {
        console.error("getEventos: Token não encontrado no localStorage");
        return []; // Retornar array vazio ao invés de lançar erro
      }
      
      try {
        // Tentar renovar a sessão
        await supabase.auth.refreshSession();
        console.log("getEventos: Sessão renovada com sucesso");
      } catch (refreshError) {
        console.error("getEventos: Erro ao renovar sessão", refreshError);
        return []; // Retornar array vazio ao invés de lançar erro
      }
    }
    
    // Buscar eventos com autenticação renovada
    const { data, error } = await supabase
      .from('eventos_calendario')
      .select('*');

    if (error) {
      console.error('Erro ao buscar eventos:', error);
      // Retornar array vazio ao invés de lançar erro
      return [];
    }

    return (data as EventoDB[]).map(mapEventoDBToEvento);
  } catch (error) {
    console.error('Erro ao buscar eventos:', error);
    // Retornar array vazio ao invés de lançar erro
    return [];
  }
};

// Buscar eventos por intervalo de data
export const getEventosPorPeriodo = async (dataInicio: string, dataFim: string): Promise<Evento[]> => {
  try {
    // Verificar autenticação
    const userId = await getUserId();
    if (!userId) {
      // Em vez de lançar um erro, tentamos verificar se há um token no localStorage
      console.log("getEventosPorPeriodo: Usuário não autenticado, tentando recuperar token");
      
      // Verificar se o token supabase existe no localStorage
      const token = localStorage.getItem('sb-ubwzukpsqcrwzfbppoux-auth-token');
      if (!token) {
        console.error("getEventosPorPeriodo: Token não encontrado no localStorage");
        return []; // Retornar array vazio ao invés de lançar erro
      }
      
      try {
        // Tentar renovar a sessão
        await supabase.auth.refreshSession();
        console.log("getEventosPorPeriodo: Sessão renovada com sucesso");
      } catch (refreshError) {
        console.error("getEventosPorPeriodo: Erro ao renovar sessão", refreshError);
        return []; // Retornar array vazio ao invés de lançar erro
      }
    }
    
    // Buscar eventos com autenticação renovada
    const { data, error } = await supabase
      .from('eventos_calendario')
      .select('*')
      .gte('data', dataInicio)
      .lte('data', dataFim);

    if (error) {
      console.error('Erro ao buscar eventos por período:', error);
      // Retornar array vazio ao invés de lançar erro
      return [];
    }

    return (data as EventoDB[]).map(mapEventoDBToEvento);
  } catch (error) {
    console.error('Erro ao buscar eventos por período:', error);
    // Retornar array vazio ao invés de lançar erro
    return [];
  }
};

// Buscar evento por ID
export const getEventoPorId = async (id: string): Promise<Evento | null> => {
  try {
    const { data, error } = await supabase
      .from('eventos_calendario')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Erro ao buscar evento:', error);
      throw new Error(`Erro ao buscar evento: ${error.message}`);
    }

    return mapEventoDBToEvento(data as EventoDB);
  } catch (error) {
    console.error('Erro ao buscar evento:', error);
    return null;
  }
};

// Criar novo evento
export const criarEvento = async (eventoData: EventoFormData): Promise<Evento | null> => {
  const { data: { user } } = await supabase.auth.getUser();
  
  try {
    const { data, error } = await supabase.rpc('criar_evento_calendario', {
      titulo: eventoData.titulo,
      descricao: eventoData.descricao,
      data_inicio: eventoData.dataInicio.toISOString(),
      duracao: `PT${eventoData.duracao}H`, // Formato ISO 8601 para interval
      user_id: user?.id
    });

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Erro ao criar evento:', error);
    throw new Error('Erro ao salvar evento no calendário');
  }
};

// Atualizar evento existente
export const atualizarEvento = async (id: string, evento: Partial<Evento>): Promise<Evento | null> => {
  try {
    // Tentar renovar a sessão antes de verificar o usuário
    const { success, session } = await refreshSupabaseSession();
    
    // Obter ID do usuário de diferentes fontes
    let userId = null;
    
    // 1. Da sessão, se disponível
    if (success && session && session.user) {
      userId = session.user.id;
      console.log('atualizarEvento: Usando ID do usuário da sessão:', userId);
    } 
    // 2. Tentar obter do localStorage
    else {
      console.warn('atualizarEvento: Sessão inválida, tentando obter usuário do localStorage');
      const userProfileStr = localStorage.getItem("userProfile");
      if (userProfileStr) {
        try {
          const userProfile = JSON.parse(userProfileStr);
          userId = userProfile.id;
          console.log('atualizarEvento: Usando ID do usuário do localStorage:', userId);
        } catch (parseError) {
          console.error('atualizarEvento: Erro ao analisar perfil do localStorage:', parseError);
        }
      }
    }
    
    // Validar se o userId é um UUID válido
    if (userId && !isValidUUID(userId)) {
      console.warn(`atualizarEvento: ID do usuário '${userId}' não é um UUID válido, será definido como null`);
      userId = null;
    }
    
    // Se ainda não temos ID, tentaremos atualizar sem updated_by
    if (!userId) {
      console.warn('atualizarEvento: Não foi possível obter ID do usuário válido, updated_by será omitido');
    }
    
    const now = new Date().toISOString();
    
    // Preparar objeto para atualização
    const eventoParaAtualizar: any = {
      ...evento,
      updated_at: now,
    };
    
    // Adicionar updated_by apenas se tivermos um ID válido
    if (userId) {
      eventoParaAtualizar.updated_by = userId;
    }
    
    console.log('atualizarEvento: Tentando atualizar evento com ID:', id);
    
    try {
      // Primeira tentativa: atualização direta
      const { data, error } = await supabase
        .from('eventos_calendario')
        .update(eventoParaAtualizar)
        .eq('id', id)
        .select();
  
      if (error) {
        // Se for erro de política de segurança, tentar abordagem alternativa
        if (error.code === '42501' || error.message.includes('row-level security policy')) {
          console.warn('Erro de política de segurança, tentando abordagem alternativa...');
          
          // Tentar usar uma função RPC (se disponível no banco)
          try {
            const { data: rpcData, error: rpcError } = await supabase
              .rpc('atualizar_evento_calendario', { 
                evento_id: id,
                ...eventoParaAtualizar
              });
              
            if (rpcError) {
              console.error('Erro ao chamar RPC para atualizar evento:', rpcError);
              throw new Error(`Erro ao atualizar evento via RPC: ${rpcError.message}`);
            }
            
            if (rpcData) {
              console.log('Evento atualizado com sucesso via RPC');
              // Buscar o evento atualizado
              const { data: fetchedData } = await supabase
                .from('eventos_calendario')
                .select('*')
                .eq('id', id)
                .single();
                
              if (fetchedData) {
                return mapEventoDBToEvento(fetchedData as EventoDB);
              }
            }
          } catch (rpcError) {
            console.error('Erro ao tentar abordagem alternativa:', rpcError);
            
            // Se a função RPC não existir, tentar uma terceira abordagem: service role
            console.warn('Tentando abordagem com service role (se disponível)...');
            
            // Aqui você poderia chamar uma API serverless que usa o service role
            // Isso requer implementação no backend
          }
          
          // Se chegamos aqui, todas as tentativas falharam
          throw new Error(`Erro de permissão: ${error.message}. Você não tem permissão para atualizar eventos.`);
        }
        
        // Outros erros
        console.error('Erro ao atualizar evento:', error);
        throw new Error(`Erro ao atualizar evento: ${error.message}`);
      }
  
      if (!data || data.length === 0) {
        throw new Error('Nenhum dado retornado ao atualizar evento');
      }
  
      return mapEventoDBToEvento(data[0] as EventoDB);
    } catch (updateError) {
      console.error('Erro na atualização:', updateError);
      throw updateError;
    }
  } catch (error) {
    console.error('Erro ao atualizar evento:', error);
    throw error; // Propagar o erro para ser tratado pelo componente
  }
};

// Excluir evento
export const excluirEvento = async (id: string): Promise<boolean> => {
  try {
    // Tentar renovar a sessão, mas não bloquear a operação se falhar
    try {
      await refreshSupabaseSession();
    } catch (refreshError) {
      console.warn('excluirEvento: Erro ao renovar sessão, continuando mesmo assim:', refreshError);
    }
    
    console.log('excluirEvento: Tentando excluir evento:', id);
    
    const { error } = await supabase
      .from('eventos_calendario')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Erro ao excluir evento:', error);
      throw new Error(`Erro ao excluir evento: ${error.message}`);
    }

    return true;
  } catch (error) {
    console.error('Erro ao excluir evento:', error);
    throw error; // Propagar o erro para ser tratado pelo componente
  }
}; 