import { supabase } from '@/lib/supabase';
import { Precatorio, KanbanColuna } from '@/components/Precatorios/types';

// Buscar todos os precatórios
export async function buscarTodosPrecatorios(tipo: 'PRECATORIO' | 'RPV' = 'PRECATORIO'): Promise<Precatorio[]> {
  try {
    console.log(`Iniciando busca de ${tipo === 'PRECATORIO' ? 'precatórios' : 'RPVs'} no Supabase...`);
    
    // Buscar os dados principais dos precatórios sem joins aninhados para evitar recursão
    const { data: precatoriosData, error: precatoriosError } = await supabase
      .from('precatorios')
      .select('*')
      .eq('tipo', tipo);

    console.log('Resposta do Supabase (precatórios):', { 
      count: precatoriosData?.length,
      error: precatoriosError?.message 
    });

    if (precatoriosError) {
      console.error('Erro ao buscar precatórios:', precatoriosError);
      throw precatoriosError;
    }

    if (!precatoriosData || precatoriosData.length === 0) {
      console.log('Nenhum precatório encontrado no banco de dados');
      return [];
    }

    console.log(`Encontrados ${precatoriosData.length} precatórios no banco de dados`);

    // Coletar IDs únicos para buscar dados relacionados
    const beneficiarioIds = [...new Set(precatoriosData.filter(p => p.beneficiario_id).map(p => p.beneficiario_id))];
    const responsavelIds = [...new Set(precatoriosData.filter(p => p.responsavel_id).map(p => p.responsavel_id))];
    const tribunalIds = [...new Set(precatoriosData.filter(p => p.tribunal_id).map(p => p.tribunal_id))];
    const tipoIds = [...new Set(precatoriosData.filter(p => p.tipo_id).map(p => p.tipo_id))];
    
    console.log('IDs coletados:', {
      beneficiarios: beneficiarioIds.length,
      responsaveis: responsavelIds.length,
      tribunais: tribunalIds.length,
      tipos: tipoIds.length
    });

    // Buscar dados relacionados em consultas separadas
    let beneficiariosMap = new Map();
    let responsaveisMap = new Map();
    let tribunaisMap = new Map();
    let tiposMap = new Map();
    
    // Buscar beneficiários (clientes)
    if (beneficiarioIds.length > 0) {
      const { data: beneficiariosData } = await supabase
        .from('clientes')
        .select('id, nome, email, telefone, cpf_cnpj')
        .in('id', beneficiarioIds);
      
      if (beneficiariosData) {
        beneficiariosMap = new Map(beneficiariosData.map(b => [b.id, b]));
      }
    }
    
    // Buscar responsáveis (profiles)
    if (responsavelIds.length > 0) {
      const { data: responsaveisData } = await supabase
        .from('profiles')
        .select('id, nome, email, foto_url')
        .in('id', responsavelIds);
      
      if (responsaveisData) {
        responsaveisMap = new Map(responsaveisData.map(r => [r.id, r]));
      }
    }
    
    // Buscar tribunais
    if (tribunalIds.length > 0) {
      const { data: tribunaisData } = await supabase
        .from('tribunais')
        .select('id, nome')
        .in('id', tribunalIds);
      
      if (tribunaisData) {
        tribunaisMap = new Map(tribunaisData.map(t => [t.id, t]));
      }
    }
    
    // Buscar tipos
    if (tipoIds.length > 0) {
      const { data: tiposData } = await supabase
        .from('tipos_precatorio')
        .select('id, nome')
        .in('id', tipoIds);
      
      if (tiposData) {
        tiposMap = new Map(tiposData.map(t => [t.id, t]));
      }
    }

    // Buscar documentos e histórico para cada precatório em lote (se possível)
    const documentosPorPrecatorio = new Map();
    const historicoPorPrecatorio = new Map();
    
    const { data: todosDocumentos } = await supabase
      .from('precatorios_documentos')
      .select('*')
      .in('precatorio_id', precatoriosData.map(p => p.id));
    
    if (todosDocumentos) {
      // Agrupar documentos por precatório
      todosDocumentos.forEach(doc => {
        if (!documentosPorPrecatorio.has(doc.precatorio_id)) {
          documentosPorPrecatorio.set(doc.precatorio_id, []);
        }
        documentosPorPrecatorio.get(doc.precatorio_id).push(doc);
      });
    }
    
    const { data: todoHistorico } = await supabase
      .from('precatorios_historico')
      .select('*')
      .in('precatorio_id', precatoriosData.map(p => p.id));
    
    if (todoHistorico) {
      // Agrupar histórico por precatório
      todoHistorico.forEach(hist => {
        if (!historicoPorPrecatorio.has(hist.precatorio_id)) {
          historicoPorPrecatorio.set(hist.precatorio_id, []);
        }
        historicoPorPrecatorio.get(hist.precatorio_id).push(hist);
      });
    }

    // Transformar os dados para o formato esperado pelo componente
    const precatorios: Precatorio[] = precatoriosData.map((precatorio) => {
      console.log('Mapeando precatório com id:', precatorio.id);
      
      // Obter entidades relacionadas dos mapas
      const beneficiario = beneficiariosMap.get(precatorio.beneficiario_id);
      const responsavel = responsaveisMap.get(precatorio.responsavel_id);
      const tribunal = tribunaisMap.get(precatorio.tribunal_id);
      const tipo = tiposMap.get(precatorio.tipo_id);
      
      // Obter documentos e histórico para este precatório
      const documentos = (documentosPorPrecatorio.get(precatorio.id) || []).map(doc => ({
        nome: doc.nome || 'Documento sem nome',
        tipo: doc.tipo || 'Outro',
        status: doc.status || 'pendente',
        data: doc.data_upload || new Date().toISOString()
      }));
      
      const historico = (historicoPorPrecatorio.get(precatorio.id) || []).map(hist => ({
        acao: hist.acao || 'Ação desconhecida',
        data: hist.data || new Date().toISOString(),
        usuario: hist.usuario || 'Sistema',
        detalhes: hist.detalhes || ''
      }));
      
      // Mapear para o formato Precatorio esperado pelo componente
      return {
        id: precatorio.id,
        numero: precatorio.numero_precatorio || `PR-${precatorio.id.substring(0, 4)}`,
        valor: parseFloat(String(precatorio.valor_total || 0)),
        desconto: parseFloat(String(precatorio.desconto || 0)),
        status: precatorio.status as any || 'analise',
        cliente: {
          nome: beneficiario?.nome || 'Cliente não identificado',
          avatar: '',  // Não temos esse campo no banco
          email: beneficiario?.email || '',
          telefone: beneficiario?.telefone || ''
        },
        responsavel: {
          nome: responsavel?.nome || 'Não atribuído',
          avatar: responsavel?.foto_url || '',
          cargo: 'Responsável'  // Não temos esse campo no banco
        },
        dataCriacao: precatorio.created_at || new Date().toISOString(),
        dataAtualizacao: precatorio.updated_at || new Date().toISOString(),
        dataVencimento: precatorio.data_previsao_pagamento || new Date().toISOString(),
        tribunal: tribunal?.nome || 'Tribunal não especificado',
        natureza: precatorio.natureza || '',
        documentos,
        historico,
        observacoes: precatorio.observacoes || '',
        tags: precatorio.tags || [],
        prioridade: (precatorio.prioridade as any) || 'media'
      };
    });

    return precatorios;
  } catch (error) {
    console.error('Erro na função buscarTodosPrecatorios:', error);
    throw error;
  }
}

// Buscar um precatório específico por ID
export async function buscarPrecatorioPorId(id: string): Promise<Precatorio | null> {
  try {
    console.log(`Iniciando busca de precatório com ID ${id} no Supabase...`);
    
    const { data, error } = await supabase
      .from('precatorios')
      .select('*')
      .eq('id', id)
      .single();

    console.log('Resposta do Supabase (precatório):', { data, error });

    if (error) {
      console.error(`Erro ao buscar precatório com ID ${id}:`, error);
      throw error;
    }

    if (!data) {
      console.log(`Precatório com ID ${id} não encontrado no banco de dados`);
      return null;
    }

    console.log(`Precatório com ID ${id} encontrado no banco de dados`);

    // Buscar dados do beneficiário (cliente) relacionado
    let clienteData = null;
    if (data.beneficiario_id) {
      const { data: cliente, error: clienteError } = await supabase
        .from('clientes')
        .select('*')
        .eq('id', data.beneficiario_id)
        .single();
      
      console.log('Resposta do Supabase (cliente):', { data: cliente, error: clienteError });

      if (clienteError) {
        console.error(`Erro ao buscar cliente ${data.beneficiario_id}:`, clienteError);
      } else {
        clienteData = cliente;
      }
    }

    // Buscar dados do responsável relacionado
    let responsavelData = null;
    if (data.responsavel_id) {
      const { data: responsavel, error: responsavelError } = await supabase
        .from('responsaveis')
        .select('*')
        .eq('id', data.responsavel_id)
        .single();
      
      console.log('Resposta do Supabase (responsável):', { data: responsavel, error: responsavelError });

      if (responsavelError) {
        console.error(`Erro ao buscar responsável ${data.responsavel_id}:`, responsavelError);
      } else {
        responsavelData = responsavel;
      }
    }

    // Buscar dados do tribunal
    let tribunalData = null;
    if (data.tribunal_id) {
      const { data: tribunal, error: tribunalError } = await supabase
        .from('tribunais')
        .select('*')
        .eq('id', data.tribunal_id)
        .single();

      console.log('Resposta do Supabase (tribunal):', { data: tribunal, error: tribunalError });

      if (tribunalError) {
        console.error(`Erro ao buscar tribunal ${data.tribunal_id}:`, tribunalError);
      } else {
        tribunalData = tribunal;
      }
    }

    // Buscar histórico do precatório
    const { data: historicoData, error: historicoError } = await supabase
      .from('precatorios_historico')
      .select('*')
      .eq('precatorio_id', data.id);
    
    console.log('Resposta do Supabase (histórico):', { data: historicoData, error: historicoError });

    if (historicoError) {
      console.error(`Erro ao buscar histórico do precatório ${data.id}:`, historicoError);
    }

    // Buscar documentos do precatório
    const { data: documentosData, error: documentosError } = await supabase
      .from('precatorios_documentos')
      .select('*')
      .eq('precatorio_id', data.id);
    
    console.log('Resposta do Supabase (documentos):', { data: documentosData, error: documentosError });

    if (documentosError) {
      console.error(`Erro ao buscar documentos do precatório ${data.id}:`, documentosError);
    }

    // Mapear documentos para o formato esperado pelo componente
    const documentos = documentosData ? documentosData.map(doc => ({
      nome: doc.nome || 'Documento sem nome',
      tipo: doc.tipo || 'Outros',
      status: doc.status || 'pendente',
      data: doc.data_upload ? new Date(doc.data_upload).toISOString() : new Date().toISOString()
    })) : [];
    
    // Mapear histórico para o formato esperado pelo componente
    const historico = historicoData ? historicoData.map(hist => ({
      acao: hist.acao || 'Ação desconhecida',
      data: hist.data ? new Date(hist.data).toISOString() : new Date().toISOString(),
      usuario: hist.usuario || 'Sistema',
      detalhes: hist.detalhes || ''
    })) : [];

    return {
      id: data.id,
      numero: data.numero_precatorio || `PR-${data.id.substring(0, 4)}/${new Date().getFullYear()}`,
      valor: parseFloat(String(data.valor_total || 0)),
      desconto: parseFloat(String(data.desconto || 0)),
      status: data.status || 'analise',
      cliente: clienteData ? {
        nome: clienteData.nome || 'Cliente não identificado',
        avatar: clienteData.avatar || '',
        email: clienteData.email || '',
        telefone: clienteData.telefone || ''
      } : {
        nome: 'Cliente não identificado',
        avatar: '',
        email: '',
        telefone: ''
      },
      responsavel: responsavelData ? {
        nome: responsavelData.nome || 'Não atribuído',
        avatar: responsavelData.avatar || '',
        cargo: responsavelData.cargo || ''
      } : {
        nome: 'Não atribuído',
        avatar: '',
        cargo: ''
      },
      dataCriacao: data.data_entrada ? new Date(data.data_entrada).toISOString() : (
        data.created_at ? new Date(data.created_at).toISOString() : new Date().toISOString()
      ),
      dataAtualizacao: data.updated_at ? new Date(data.updated_at).toISOString() : new Date().toISOString(),
      dataVencimento: data.data_previsao_pagamento ? new Date(data.data_previsao_pagamento).toISOString() : '',
      tribunal: tribunalData ? tribunalData.nome : (data.tribunal_nome || 'Tribunal não especificado'),
      natureza: data.natureza || 'Não especificada',
      documentos,
      historico,
      observacoes: data.observacoes || '',
      tags: data.tags || [],
      prioridade: data.prioridade || 'media'
    };
  } catch (error) {
    console.error(`Erro ao buscar precatório com ID ${id}:`, error);
    throw error;
  }
}

// Salvar um precatório (criar ou atualizar)
export async function salvarPrecatorio(precatorio: Partial<Precatorio>): Promise<Precatorio> {
  try {
    // Verificar se é uma atualização ou criação
    const isUpdate = !!precatorio.id && precatorio.id !== 'novo';
    
    console.log(`Iniciando ${isUpdate ? 'atualização' : 'criação'} de precatório no Supabase...`);
    
    // Converter os dados do formato do componente para o formato do banco
    const precatorioData = {
      numero_precatorio: precatorio.numero,
      valor_total: precatorio.valor,
      desconto: precatorio.desconto,
      status: precatorio.status,
      beneficiario_id: precatorio.cliente?.nome ? (await buscarClientePorNome(precatorio.cliente.nome))?.id : null,
      responsavel_id: precatorio.responsavel?.nome ? (await buscarResponsavelPorNome(precatorio.responsavel.nome))?.id : null,
      data_previsao_pagamento: precatorio.dataVencimento,
      tribunal_id: precatorio.tribunal ? (await buscarTribunalPorNome(precatorio.tribunal))?.id : null,
      natureza: precatorio.natureza,
      observacoes: precatorio.observacoes,
      tags: precatorio.tags,
      prioridade: precatorio.prioridade,
      entidade_devedora: null // Campo existente mas não está no tipo Precatorio
    };

    console.log('Dados do precatório para salvar:', precatorioData);

    let result;
    if (isUpdate) {
      // Atualizar precatório existente
      const { data, error } = await supabase
        .from('precatorios')
        .update(precatorioData)
        .eq('id', precatorio.id)
        .select()
        .single();

      console.log('Resposta do Supabase (atualização de precatório):', { data, error });

      if (error) {
        console.error(`Erro ao atualizar precatório com ID ${precatorio.id}:`, error);
        throw error;
      }
      
      result = data;
    } else {
      // Criar novo precatório
      const { data, error } = await supabase
        .from('precatorios')
        .insert([precatorioData])
        .select()
        .single();

      console.log('Resposta do Supabase (criação de precatório):', { data, error });

      if (error) {
        console.error('Erro ao criar precatório:', error);
        throw error;
      }
      
      result = data;
    }

    console.log('Precatório salvo com sucesso');

    // Atualizar documentos, se fornecidos
    if (precatorio.documentos && precatorio.documentos.length > 0 && result?.id) {
      // Primeiro, excluir documentos existentes (se for uma atualização)
      if (isUpdate) {
        await supabase
          .from('precatorios_documentos')
          .delete()
          .eq('precatorio_id', result.id);
      }

      // Depois, inserir os novos documentos
      const documentosData = precatorio.documentos.map(doc => ({
        precatorio_id: result.id,
        nome: doc.nome,
        tipo: doc.tipo,
        status: doc.status,
        data_upload: doc.data,
      }));

      await supabase
        .from('precatorios_documentos')
        .insert(documentosData);
    }

    console.log('Documentos do precatório atualizados');

    // Adicionar entrada de histórico
    if (result?.id) {
      const historicoData = {
        precatorio_id: result.id,
        acao: isUpdate ? 'Atualização' : 'Criação',
        data: new Date().toISOString(),
        usuario: 'Sistema', // Idealmente, deveria ser o usuário logado
        detalhes: isUpdate ? 'Precatório atualizado' : 'Novo precatório criado',
      };

      await supabase
        .from('precatorios_historico')
        .insert([historicoData]);
    }

    console.log('Entrada de histórico adicionada');

    // Buscar o precatório completo após a criação/atualização
    return buscarPrecatorioPorId(result.id);
  } catch (error) {
    console.error('Erro ao salvar precatório:', error);
    throw error;
  }
}

// Excluir um precatório
export async function excluirPrecatorio(id: string): Promise<void> {
  try {
    console.log(`Iniciando exclusão de precatório com ID ${id} no Supabase...`);
    
    // Primeiro excluir documentos e histórico relacionados
    await supabase
      .from('precatorios_documentos')
      .delete()
      .eq('precatorio_id', id);

    console.log('Documentos do precatório excluídos');

    await supabase
      .from('precatorios_historico')
      .delete()
      .eq('precatorio_id', id);

    console.log('Histórico do precatório excluído');

    // Depois excluir o precatório
    const { error } = await supabase
      .from('precatorios')
      .delete()
      .eq('id', id);

    console.log('Resposta do Supabase (exclusão de precatório):', { error });

    if (error) {
      console.error(`Erro ao excluir precatório com ID ${id}:`, error);
      throw error;
    }

    console.log('Precatório excluído com sucesso');
  } catch (error) {
    console.error(`Erro ao excluir precatório com ID ${id}:`, error);
    throw error;
  }
}

// Atualizar o status de um precatório
export async function atualizarStatusPrecatorio(id: string, novoStatus: string): Promise<Precatorio> {
  try {
    console.log(`Iniciando atualização de status do precatório com ID ${id} no Supabase...`);
    
    // Atualizar o status
    const { data, error } = await supabase
      .from('precatorios')
      .update({ status: novoStatus })
      .eq('id', id)
      .select()
      .single();

    console.log('Resposta do Supabase (atualização de status):', { data, error });

    if (error) {
      console.error(`Erro ao atualizar status do precatório com ID ${id}:`, error);
      throw error;
    }

    console.log('Status do precatório atualizado');

    // Adicionar entrada no histórico
    await supabase
      .from('precatorios_historico')
      .insert([{
        precatorio_id: id,
        acao: 'Atualização de Status',
        data: new Date().toISOString(),
        usuario: 'Sistema', // Idealmente, deveria ser o usuário logado
        detalhes: `Status alterado para "${novoStatus}"`,
      }]);

    console.log('Entrada de histórico adicionada');

    // Buscar o precatório completo após a atualização
    return buscarPrecatorioPorId(id);
  } catch (error) {
    console.error(`Erro ao atualizar status do precatório com ID ${id}:`, error);
    throw error;
  }
}

// Buscar colunas para o Kanban
export async function buscarColunasKanban(): Promise<KanbanColuna[]> {
  try {
    console.log('Iniciando busca de colunas do Kanban no Supabase...');
    
    // Buscar todos os precatórios para contar por status
    const { data: precatoriosData, error } = await supabase
      .from('precatorios')
      .select('status');

    console.log('Resposta do Supabase (precatórios para kanban):', { 
      count: precatoriosData?.length,
      error: error?.message
    });

    if (error) {
      console.error('Erro ao buscar dados para o Kanban:', error);
      throw error;
    }

    // Contar precatórios por status
    const statusCount: Record<string, number> = {
      'analise': 0,
      'proposta_tmj': 0,
      'proposta_btg': 0,
      'negociacao': 0,
      'documentacao': 0,
      'pagamento': 0,
      'concluido': 0,
      'cancelado': 0
    };
    
    if (precatoriosData && precatoriosData.length > 0) {
      precatoriosData.forEach(precatorio => {
        // Mapear status 'ativo' para 'analise'
        const status = precatorio.status === 'ativo' ? 'analise' : (precatorio.status || 'analise');
        
        // Garantir que o status é uma chave válida no objeto statusCount
        if (statusCount.hasOwnProperty(status)) {
          statusCount[status] += 1;
        } else {
          // Status desconhecido, colocar em análise por padrão
          statusCount['analise'] += 1;
        }
      });
    }

    console.log('Contagem de precatórios por status após mapeamento:', statusCount);

    // Definir as colunas do Kanban com o formato esperado pelo componente
    const colunas: KanbanColuna[] = [
      { 
        id: 'analise', 
        name: `Análise (${statusCount['analise'] || 0})`, 
        color: '#3b82f6', // Cor azul em hexadecimal
        tipo: 'AMBOS',
        ordem: 1,
        status_id: 'analise',
        ativo: true,
        alerts: statusCount['analise'] > 0 ? [
          { tipo: 'info', mensagem: `${statusCount['analise']} precatório(s) em análise` }
        ] : []
      },
      { 
        id: 'proposta_tmj', 
        name: `Proposta TMJ (${statusCount['proposta_tmj'] || 0})`, 
        color: '#8b5cf6', // Cor roxa em hexadecimal
        tipo: 'AMBOS',
        ordem: 2,
        status_id: 'proposta_tmj',
        ativo: true,
        alerts: statusCount['proposta_tmj'] > 0 ? [
          { tipo: 'info', mensagem: `${statusCount['proposta_tmj']} proposta(s) da TMJ` }
        ] : []
      },
      { 
        id: 'proposta_btg', 
        name: `Proposta BTG (${statusCount['proposta_btg'] || 0})`, 
        color: '#ec4899', // Cor rosa em hexadecimal
        tipo: 'AMBOS',
        ordem: 3,
        status_id: 'proposta_btg',
        ativo: true,
        alerts: statusCount['proposta_btg'] > 0 ? [
          { tipo: 'info', mensagem: `${statusCount['proposta_btg']} proposta(s) do BTG` }
        ] : []
      },
      { 
        id: 'negociacao', 
        name: `Negociação (${statusCount['negociacao'] || 0})`, 
        color: '#f59e0b', // Cor âmbar em hexadecimal
        tipo: 'AMBOS',
        ordem: 4,
        status_id: 'negociacao',
        ativo: true,
        alerts: statusCount['negociacao'] > 0 ? [
          { tipo: 'info', mensagem: `${statusCount['negociacao']} em negociação` }
        ] : []
      },
      { 
        id: 'documentacao', 
        name: `Documentação (${statusCount['documentacao'] || 0})`, 
        color: '#10b981', // Cor verde esmeralda em hexadecimal
        tipo: 'AMBOS',
        ordem: 5,
        status_id: 'documentacao',
        ativo: true,
        alerts: statusCount['documentacao'] > 0 ? [
          { tipo: 'info', mensagem: `${statusCount['documentacao']} em fase de documentação` }
        ] : []
      },
      { 
        id: 'pagamento', 
        name: `Pagamento (${statusCount['pagamento'] || 0})`, 
        color: '#6366f1', // Cor índigo em hexadecimal
        tipo: 'AMBOS',
        ordem: 6,
        status_id: 'pagamento',
        ativo: true,
        alerts: statusCount['pagamento'] > 0 ? [
          { tipo: 'info', mensagem: `${statusCount['pagamento']} aguardando pagamento` }
        ] : []
      },
      { 
        id: 'concluido', 
        name: `Concluído (${statusCount['concluido'] || 0})`, 
        color: '#22c55e', // Cor verde em hexadecimal
        tipo: 'AMBOS',
        ordem: 7,
        status_id: 'concluido',
        ativo: true,
        alerts: statusCount['concluido'] > 0 ? [
          { tipo: 'sucesso', mensagem: `${statusCount['concluido']} concluído(s)` }
        ] : []
      },
      { 
        id: 'cancelado', 
        name: `Cancelado (${statusCount['cancelado'] || 0})`, 
        color: '#ef4444', // Cor vermelha em hexadecimal
        tipo: 'AMBOS',
        ordem: 8,
        status_id: 'cancelado',
        ativo: true,
        alerts: statusCount['cancelado'] > 0 ? [
          { tipo: 'alerta', mensagem: `${statusCount['cancelado']} cancelado(s)` }
        ] : []
      }
    ];

    console.log('Colunas geradas para o Kanban:', colunas);
    return colunas;
  } catch (error) {
    console.error('Erro na função buscarColunasKanban:', error);
    throw error;
  }
}

// Funções auxiliares
async function buscarClientePorNome(nome: string) {
  const { data, error } = await supabase
    .from('clientes')
    .select('id')
    .ilike('nome', `%${nome}%`)
    .limit(1)
    .single();

  console.log('Resposta do Supabase (cliente):', { data, error });

  if (error || !data) {
    console.error(`Cliente com nome ${nome} não encontrado:`, error);
    return null;
  }

  return data;
}

async function buscarResponsavelPorNome(nome: string) {
  const { data, error } = await supabase
    .from('responsaveis')
    .select('id')
    .ilike('nome', `%${nome}%`)
    .limit(1)
    .single();

  console.log('Resposta do Supabase (responsável):', { data, error });

  if (error || !data) {
    console.error(`Responsável com nome ${nome} não encontrado:`, error);
    return null;
  }

  return data;
}

async function buscarTribunalPorNome(nome: string) {
  const { data, error } = await supabase
    .from('tribunais')
    .select('id')
    .ilike('nome', `%${nome}%`)
    .limit(1)
    .single();

  console.log('Resposta do Supabase (tribunal):', { data, error });

  if (error || !data) {
    console.error(`Tribunal com nome ${nome} não encontrado:`, error);
    return null;
  }

  return data;
}
