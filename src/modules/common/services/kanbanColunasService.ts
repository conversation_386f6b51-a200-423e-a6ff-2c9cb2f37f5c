import { supabase } from '@/lib/supabase';
import { KanbanColuna } from '@/components/Precatorios/types';
import { toast } from 'sonner';

// Variável para controlar o modo offline - sempre desativado
let modoOffline = false;

// Definir explicitamente o modo offline - agora sempre força modo online
export function setModoOffline(offline: boolean): void {
  // Forçar modo online independente do parâmetro
  modoOffline = false;
  console.log('Modo online ativado. Sempre usando Supabase.');
  toast.info('Modo online garantido. As alterações serão salvas no banco de dados.');
}

// Verificar se a tabela kanban_colunas existe e criar se necessário
export async function verificarECriarTabelaKanbanColunas(): Promise<boolean> {
  try {
    // Forçar modo online para garantir conexão com Supabase
    modoOffline = false;
    
    // Tentar buscar dados da tabela para verificar se ela existe
    const { data, error } = await supabase
      .from('kanban_colunas')
      .select('count')
      .limit(1);
    
    // Se não houver erro, a tabela existe
    if (!error) {
      return true;
    }
    
    // Se o erro indicar que a tabela não existe, tentamos criar
    if (error.message.includes('404') || error.message.includes('does not exist')) {
      toast.info('Criando tabela kanban_colunas no Supabase...');
      
      try {
        // Como não podemos criar a tabela diretamente, vamos inserir um registro
        // e o Supabase criará a tabela automaticamente
        const colunasIniciais = getColunasDefault('AMBOS').map(coluna => ({
          id: coluna.id,
          name: coluna.name,
          color: coluna.color,
          tipo: coluna.tipo,
          ordem: coluna.ordem,
          status_id: coluna.status_id,
          ativo: coluna.ativo,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }));
        
        // Inserir primeiro registro para criar a tabela
        const { error: createError } = await supabase
          .from('kanban_colunas')
          .insert(colunasIniciais[0]);
        
        if (!createError) {
          toast.success('Tabela kanban_colunas criada com sucesso!');
          return true;
        } else {
          console.error('Erro ao criar tabela kanban_colunas:', createError);
          return true; // Retornamos true mesmo assim para forçar tentativa de uso do Supabase
        }
      } catch (createError) {
        console.error('Erro ao criar tabela kanban_colunas:', createError);
        return true; // Retornamos true mesmo assim para forçar tentativa de uso do Supabase
      }
    }
    
    console.error('Erro ao verificar tabela kanban_colunas:', error);
    return true; // Retornamos true mesmo assim para forçar tentativa de uso do Supabase
  } catch (error) {
    console.error('Erro ao verificar tabela kanban_colunas:', error);
    return true; // Retornamos true mesmo assim para forçar tentativa de uso do Supabase
  }
}

// Buscar todas as colunas do Kanban
export async function buscarTodasColunas(): Promise<KanbanColuna[]> {
  try {
    console.log('Buscando todas as colunas do Kanban...');
    
    // Verificar se a tabela existe
    const tabelaExiste = await verificarECriarTabelaKanbanColunas();
    
    // Mesmo se a verificação da tabela falhou, tentamos usar o Supabase
    if (!tabelaExiste) {
      console.log('Tentando usar Supabase mesmo sem confirmação da tabela...');
      // Vamos inserir as colunas padrão no Supabase
      try {
        const colunasDefault = getColunasDefault('AMBOS');
        await supabase.from('kanban_colunas').upsert(colunasDefault);
      } catch (e) {
        console.error('Erro ao inserir colunas padrão:', e);
      }
    }
    
    // Buscar dados da tabela
    const { data, error } = await supabase
      .from('kanban_colunas')
      .select('*')
      .order('ordem', { ascending: true });
    
    if (error) {
      console.error('Erro ao buscar colunas do Kanban:', error);
      // Se houver erro, usar colunas padrão
      console.log('Usando colunas padrão devido a erro...');
      return getColunasDefault('AMBOS');
    }
    
    if (!data || data.length === 0) {
      console.log('Nenhuma coluna encontrada no banco. Usando colunas padrão...');
      return getColunasDefault('AMBOS');
    }
    
    // Mapear os dados para o formato esperado pelo componente
    const colunas: KanbanColuna[] = data.map(coluna => ({
      id: coluna.status_id,
      name: coluna.nome,
      color: coluna.cor,
      tipo: coluna.tipo,
      ordem: coluna.ordem,
      status_id: coluna.status_id,
      ativo: coluna.ativo,
      alerts: []
    }));
    
    console.log(`${colunas.length} colunas encontradas.`);
    return colunas;
  } catch (error) {
    console.error('Erro ao buscar colunas do Kanban:', error);
    toast.error('Erro ao carregar colunas do Kanban');
    throw error;
  }
}

// Buscar colunas do Kanban por tipo (PRECATORIO, RPV ou AMBOS)
export async function buscarColunasPorTipo(tipo: 'PRECATORIO' | 'RPV' | 'AMBOS'): Promise<KanbanColuna[]> {
  try {
    console.log(`Buscando colunas do Kanban para o tipo: ${tipo}...`);
    
    // Verificar se a tabela existe
    const tabelaExiste = await verificarECriarTabelaKanbanColunas();
    
    // Se a tabela não existe, retornar colunas padrão
    if (!tabelaExiste) {
      console.log('Usando colunas padrão do Kanban...');
      return getColunasDefault(tipo);
    }
    
    const { data, error } = await supabase
      .from('kanban_colunas')
      .select('*')
      .or(`tipo.eq.${tipo},tipo.eq.AMBOS`)
      .eq('ativo', true)
      .order('ordem', { ascending: true });
    
    if (error) {
      console.error(`Erro ao buscar colunas do Kanban para o tipo ${tipo}:`, error);
      console.log('Usando colunas padrão do Kanban devido a erro...');
      return getColunasDefault(tipo);
    }
    
    // Se não houver dados, retornar colunas padrão
    if (!data || data.length === 0) {
      console.log(`Nenhuma coluna encontrada para o tipo ${tipo}. Usando colunas padrão...`);
      return getColunasDefault(tipo);
    }
    
    // Mapear os dados para o formato esperado pelo componente com logs explícitos
    console.log('Dados brutos do banco de dados para mapeamento:', data);
    
    const colunas: KanbanColuna[] = data.map(coluna => {
      // Verificar se todos os campos necessários existem
      if (!coluna.nome || !coluna.status_id) {
        console.warn('Coluna com dados incompletos:', coluna);
      }
      
      // Criar objeto de coluna com mapeamento explícito
      const colunaFormatada: KanbanColuna = {
        id: coluna.status_id,       // Usamos status_id como ID principal 
        name: coluna.nome,          // Campo 'nome' do banco
        color: coluna.cor,          // Campo 'cor' do banco
        tipo: coluna.tipo as 'PRECATORIO' | 'RPV' | 'AMBOS', // Tipagem segura
        ordem: coluna.ordem,        // Campo 'ordem' do banco
        status_id: coluna.status_id, // Campo 'status_id' do banco
        ativo: coluna.ativo,        // Campo 'ativo' do banco
        alerts: []                 // Alertas vazios por padrão
      };
      
      console.log(`Coluna mapeada: ${coluna.nome} (${coluna.status_id})`);
      return colunaFormatada;
    });
    
    console.log(`${colunas.length} colunas do Kanban para o tipo ${tipo} recuperadas:`, colunas);
    return colunas;
  } catch (error) {
    console.error(`Erro ao buscar colunas do Kanban para o tipo ${tipo}:`, error);
    toast.error('Erro ao carregar colunas do Kanban');
    throw error;
  }
}

// Criar nova coluna do Kanban

// Criar nova coluna do Kanban
export async function criarColuna(coluna: Omit<KanbanColuna, 'id' | 'alerts'>): Promise<KanbanColuna> {
  try {
    // Normalizar o status_id para evitar problemas de compatibilidade
    const statusIdNormalizado = coluna.status_id.trim().toLowerCase();
    
    // Criar uma cópia da coluna com o status_id normalizado
    const colunaNormalizada = {
      ...coluna,
      status_id: statusIdNormalizado
    };
    
    console.log('Criando nova coluna do Kanban:', {
      original: coluna,
      normalizada: colunaNormalizada
    });
    
    // Verificar se a tabela existe ou se estamos em modo offline
    const tabelaExiste = await verificarECriarTabelaKanbanColunas();
    
    // Forçar modo online e tentar usar o Supabase
    if (!tabelaExiste) {
      toast.info('Tentando conectar ao Supabase...');
    }
    
    try {
      // Verificar se já existe uma coluna com o mesmo status_id (usando o status_id normalizado)
      const { data: existingData, error: checkError } = await supabase
        .from('kanban_colunas')
        .select('*')
        .eq('status_id', statusIdNormalizado);
      
      console.log('Verificando existência da coluna:', { 
        statusIdBuscado: statusIdNormalizado, 
        resultado: existingData, 
        erro: checkError 
      });
      
      // Se houver erro na consulta, tentamos novamente sem ativar modo offline
      if (checkError) {
        console.error('Erro ao verificar coluna existente:', checkError);
        toast.error('Erro ao verificar coluna existente. Tentando novamente...');
        // Continuar com a criação mesmo assim
      }
      
      // Se a coluna já existe, atualizamos em vez de criar
      if (existingData && existingData.length > 0) {
        console.log('Coluna já existe, atualizando dados...', {
          colunaExistente: existingData[0],
          novosValores: colunaNormalizada
        });
        
        // Preparar dados para atualização mantendo o ID existente
        const colunaAtualizada = {
          ...colunaNormalizada,
          id: existingData[0].id || existingData[0].status_id
        };
        
        console.log('Encaminhando para atualização:', colunaAtualizada);
        return atualizarColuna(colunaAtualizada);
      }
      
      // Inserir a nova coluna com o status_id normalizado e user_id para contornar RLS
      const { data, error } = await supabase
        .from('kanban_colunas')
        .insert({
          nome: colunaNormalizada.name,
          cor: colunaNormalizada.color,
          tipo: colunaNormalizada.tipo,
          ordem: colunaNormalizada.ordem,
          status_id: statusIdNormalizado, // Usar o status_id normalizado
          ativo: colunaNormalizada.ativo !== undefined ? colunaNormalizada.ativo : true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select();
      
      console.log('Resultado da criação de coluna:', { data, error });
      
      if (error) {
        console.error('Erro ao criar coluna do Kanban:', error);
        // Verificar se é erro de constraint/unique
        if (error.code === '23505') {
          console.log('Erro de chave duplicada. Tentando buscar a coluna existente...');
          
          // Tentar buscar a coluna existente
          const { data: existingCol } = await supabase
            .from('kanban_colunas')
            .select('*')
            .eq('status_id', statusIdNormalizado)
            .single();
            
          if (existingCol) {
            // Mapear para o formato esperado
            return {
              id: existingCol.id || existingCol.status_id,
              name: existingCol.nome,
              color: existingCol.cor,
              tipo: existingCol.tipo,
              ordem: existingCol.ordem,
              status_id: existingCol.status_id,
              ativo: existingCol.ativo,
              alerts: []
            };
          }
        }
        
        setModoOffline(true);
        return criarColuna(colunaNormalizada); // Recursivo, agora em modo offline
      }
      
      if (!data || data.length === 0) {
        console.log('Nenhum dado retornado após a criação, usando modo offline');
        setModoOffline(true);
        return criarColuna(colunaNormalizada); // Recursivo, agora em modo offline
      }
      
      // Mapear para o formato esperado pelo componente
      const novaColuna: KanbanColuna = {
        id: data[0].status_id,
        name: data[0].nome,
        color: data[0].cor,
        tipo: data[0].tipo,
        ordem: data[0].ordem,
        status_id: data[0].status_id,
        ativo: data[0].ativo,
        alerts: []
      };
      
      console.log('Nova coluna do Kanban criada:', novaColuna);
      return novaColuna;
    } catch (dbError) {
      console.error('Erro durante operação no banco de dados:', dbError);
      // Não entrar em modo offline, tentar criar novamente
      toast.error('Erro ao criar coluna. Tentando novamente...');
      return criarColuna(coluna); // Tenta novamente
    }
  } catch (error) {
    console.error('Erro ao criar coluna do Kanban:', error);
    toast.error('Erro ao criar coluna do Kanban');
    // Não entrar em modo offline, criar objeto para retorno com alerta
    const novaColuna: KanbanColuna = {
      id: coluna.status_id,
      name: coluna.name,
      color: coluna.color,
      tipo: coluna.tipo,
      ordem: coluna.ordem,
      status_id: coluna.status_id,
      ativo: coluna.ativo !== undefined ? coluna.ativo : true,
      alerts: [{
        tipo: 'error',
        mensagem: 'Erro ao salvar coluna no Supabase'
      }]
    };
    return novaColuna;
  }
}

// Atualizar coluna do Kanban
export async function atualizarColuna(coluna: Omit<KanbanColuna, 'alerts'>): Promise<KanbanColuna> {
  try {
    // Normalizar o status_id para evitar problemas de compatibilidade
    const statusIdNormalizado = coluna.status_id.trim().toLowerCase();
    
    // Criar uma cópia da coluna com o status_id normalizado
    const colunaNormalizada = {
      ...coluna,
      status_id: statusIdNormalizado
    };
    
    // Verificar se a tabela existe ou se estamos em modo offline
    const tabelaExiste = await verificarECriarTabelaKanbanColunas();
    
    // Forçar o uso do Supabase mesmo se verificarECriarTabelaKanbanColunas retornar falso
    if (!tabelaExiste) {
      toast.info('Tentando conectar ao Supabase...');
    }
    
    try {
      // Quando a tabela existe e não estamos em modo offline, tentamos atualizar no banco de dados
      console.log('Tentando atualizar a coluna no banco de dados...');
      
      // Dados para atualização - mapeamento explícito para corresponder ao esquema do banco
      // RLS exige que user_id seja definido para permitir atualizações
      const updateData = {
        nome: colunaNormalizada.name,         // Campo 'nome' no banco
        cor: colunaNormalizada.color,         // Campo 'cor' no banco
        tipo: colunaNormalizada.tipo,         // Campo 'tipo' no banco
        ordem: colunaNormalizada.ordem,       // Campo 'ordem' no banco
        ativo: colunaNormalizada.ativo !== undefined ? colunaNormalizada.ativo : true,  // Campo 'ativo' no banco
        updated_at: new Date().toISOString()     // Atualizar timestamp como string ISO
      };
      
      // Verificar primeiro se a coluna existe consultando pelo status_id normalizado
      const { data: checkData, error: checkError } = await supabase
        .from('kanban_colunas')
        .select('id, status_id')
        .eq('status_id', statusIdNormalizado)
        .single();
      
      // Verificar como fazer a atualização
      let resultado;
      
      if (checkData?.id) {
        // Se encontramos a coluna, atualizar pelo ID (mais seguro)
        resultado = await supabase
          .from('kanban_colunas')
          .update({
            ...updateData,
            status_id: statusIdNormalizado, // Manter o mesmo status_id
            nome: colunaNormalizada.name,    // Garantir que nome correto seja usado
            cor: colunaNormalizada.color     // Garantir que cor correta seja usada
          })
          .eq('id', checkData.id)
          .select('*');
      } else {
        // Se não encontramos, usar upsert para criar/atualizar
        resultado = await supabase
          .from('kanban_colunas')
          .upsert({
            ...updateData,
            status_id: statusIdNormalizado // Usar status_id normalizado
          }, { onConflict: 'status_id' })
          .select('*');
      }
      
      const { data, error } = resultado;
      
      // Se houver erro na atualização
      if (error) {
        console.error('Erro ao atualizar coluna do Kanban:', error);
        
        // Tentar tratar diferentes tipos de erro
        if (error.code === '23505' || error.code === '406' || error.code === 'PGRST116') {
          // Erro de chave duplicada ou coluna não encontrada
          try {
            // Tentar forçar uma atualização direta na tabela
            const novaTentativa = await supabase
              .from('kanban_colunas')
              .upsert({
                ...updateData,
                status_id: statusIdNormalizado,
                nome: colunaNormalizada.name,
                cor: colunaNormalizada.color
              })
              .select('*');
              
            if (!novaTentativa.error && novaTentativa.data?.[0]) {
              const colunaAtualizada = mapearColunaDB(novaTentativa.data[0]);
              return colunaAtualizada;
            }
            
            // Se ainda falhar, tentar criar a coluna
            const novaColuna = await criarColuna(colunaNormalizada);
            return novaColuna;
          } catch (retryError) {
            console.error('Erro na segunda tentativa de atualização:', retryError);
          }
        }
        
        // Para outros tipos de erro, tentar novamente sem entrar em modo offline
        toast.error('Erro ao salvar no banco de dados. Tentando novamente...');
        return atualizarColuna(colunaNormalizada); // Tentar novamente
      }
      
      // Nenhum dado retornado após uma atualização bem-sucedida, algo está errado
      if (!data || data.length === 0) {
        // Verificar novamente se a coluna existe e recuperar seus dados
        const { data: checkAgain } = await supabase
          .from('kanban_colunas')
          .select('*')
          .eq('status_id', statusIdNormalizado)
          .maybeSingle();
          
        if (checkAgain) {
          // Coluna existe, retornar seus dados
          return mapearColunaDB(checkAgain);
        }
        
        // Se ainda não conseguimos encontrar, tentar criar uma nova
        return criarColuna(colunaNormalizada);
      }
      
      console.log('Dados retornados após atualização:', data);
      
      // Mapear para o formato esperado pelo componente
      const colunaAtualizada: KanbanColuna = mapearColunaDB(data[0]);
      
      // Função auxiliar para mapear dados do banco para o formato da coluna
      function mapearColunaDB(dadosDB: any): KanbanColuna {
        return {
          id: dadosDB.id || dadosDB.status_id,
          name: dadosDB.nome || dadosDB.name || '',
          color: dadosDB.cor || dadosDB.color || '#3b82f6',
          tipo: dadosDB.tipo || 'AMBOS',
          ordem: dadosDB.ordem || 0,
          status_id: dadosDB.status_id || '',
          ativo: dadosDB.ativo !== undefined ? dadosDB.ativo : true,
          alerts: []
        };
      }
      
      console.log('Coluna do Kanban atualizada com sucesso:', colunaAtualizada);
      return colunaAtualizada;
    } catch (dbError) {
      console.error('Erro durante operação no banco de dados:', dbError);
      // Em caso de erro durante operações no DB, tentar novamente sem entrar em modo offline
      toast.error('Erro durante operação no banco de dados. Tentando novamente...');
      return atualizarColuna(coluna); // Tentar novamente
    }
  } catch (error) {
    console.error('Erro ao atualizar coluna do Kanban:', error);
    toast.error(`Erro ao atualizar coluna: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    
    // Em caso de erro não tratado, retornar uma coluna com alerta, sem entrar em modo offline
    return {
      ...coluna,
      alerts: [{
        tipo: 'error',
        mensagem: 'Erro ao conectar ao Supabase. Tente novamente.'
      }]
    };
  }
}

// Excluir coluna do Kanban
export async function excluirColuna(status_id: string): Promise<void> {
  try {
    // Normalizar o status_id para evitar problemas de compatibilidade
    const statusIdNormalizado = status_id.trim().toLowerCase();
    
    console.log(`Excluindo coluna do Kanban com status_id: ${status_id} (normalizado: ${statusIdNormalizado})...`);
    
    // Verificar se a tabela existe ou se estamos em modo offline
    const tabelaExiste = await verificarECriarTabelaKanbanColunas();
    
    // Forçar modo online e tentar usar o Supabase
    if (!tabelaExiste) {
      toast.info('Tentando conectar ao Supabase...');
    }
    
    try {
      // Antes de excluir, verificar se a coluna existe com o status_id normalizado
      const { data: checkData, error: checkError } = await supabase
        .from('kanban_colunas')
        .select('id, status_id')
        .eq('status_id', statusIdNormalizado)
        .maybeSingle();
      
      console.log('Verificando se a coluna existe antes de excluir:', { 
        statusIdBuscado: statusIdNormalizado, 
        resultado: checkData, 
        erro: checkError 
      });
      
      // Se não encontrou a coluna, pode ser considerada como já excluída
      if (!checkData || checkError) {
        console.log(`Coluna com status_id ${statusIdNormalizado} não encontrada ou erro na busca.`);
        toast.info('A coluna já não existe no banco de dados.');
        return;
      }
      
      // Tentar atualizar apenas o timestamp antes de excluir
      await supabase
        .from('kanban_colunas')
        .update({
          updated_at: new Date().toISOString()
        })
        .eq('status_id', statusIdNormalizado);
      
      // Agora tentar excluir a coluna usando o status_id normalizado
      const { error } = await supabase
        .from('kanban_colunas')
        .delete()
        .eq('status_id', statusIdNormalizado);
      
      // Se houver erro na exclusão
      if (error) {
        console.error(`Erro ao excluir coluna do Kanban com status_id ${status_id}:`, error);
        
        // Verificar o tipo de erro
        if (error.code === '406' || error.code === 'PGRST116') {
          // Coluna não encontrada, já pode ser considerada como excluída
          console.log(`Coluna com status_id ${status_id} não encontrada na base de dados.`);
          toast.info('A coluna já não existe no banco de dados.');
          return;
        }
        
        // Para outros tipos de erro, tente excluir pelo ID se disponível
        if (checkData?.id) {
          console.log(`Tentando excluir coluna pelo ID: ${checkData.id} após falha na exclusão por status_id`);
          // Tentar atualizar apenas o timestamp antes de excluir
          await supabase
            .from('kanban_colunas')
            .update({
              updated_at: new Date().toISOString()
            })
            .eq('id', checkData.id);
            
          // Agora tentar excluir pelo ID
          const { error: idError } = await supabase
            .from('kanban_colunas')
            .delete()
            .eq('id', checkData.id);
            
          if (!idError) {
            console.log(`Coluna excluída com sucesso pelo ID: ${checkData.id}`);
            toast.success('Coluna excluída com sucesso!');
            return;
          }
          
          console.error(`Erro ao excluir coluna pelo ID ${checkData.id}:`, idError);
        }
        
        // Se todas as tentativas falharem, ativar modo offline
        console.error(`Ativando modo offline devido a erro na exclusão da coluna: ${statusIdNormalizado}`);
        setModoOffline(true);
        return excluirColuna(statusIdNormalizado); // Tentar novamente em modo offline com o status_id normalizado
      }
      
      console.log(`Coluna do Kanban com status_id ${status_id} excluída com sucesso`);
      toast.success('Coluna excluída com sucesso!');
    } catch (dbError) {
      console.error(`Erro durante operação de exclusão no banco de dados:`, dbError);
      // Em caso de erro durante operações no DB, ativar modo offline
      setModoOffline(true);
      return excluirColuna(status_id); // Tentar novamente em modo offline
    }
  } catch (error) {
    console.error(`Erro ao excluir coluna do Kanban com status_id ${status_id}:`, error);
    toast.error(`Erro ao excluir coluna: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    
    // Em caso de erro não tratado, ativar modo offline
    setModoOffline(true);
    toast.info('Continuando em modo offline...');
    return;
  }
}

// Reordenar colunas do Kanban
export async function reordenarColunas(colunas: Omit<KanbanColuna, 'alerts'>[]): Promise<KanbanColuna[]> {
  try {
    console.log('Reordenando colunas do Kanban:', colunas);
    
    // Verificar se a tabela existe ou se estamos em modo offline
    const tabelaExiste = await verificarECriarTabelaKanbanColunas();
    
    // Se a tabela não existe ou estamos em modo offline, apenas retornar as colunas reordenadas
    if (!tabelaExiste || modoOffline) {
      console.log('Operando em modo offline para reordenamento de colunas.');
      toast.warning('Operando em modo offline. As alterações serão salvas apenas localmente.');
      toast.success('Colunas reordenadas com sucesso em modo offline.');
      
      // Retornar as colunas com a nova ordem e um alerta de modo offline
      return colunas.map(coluna => ({
        ...coluna,
        alerts: [{
          tipo: 'info',
          mensagem: 'Reordenada em modo offline'
        }]
      }));
    }
    
    try {
      // Atualizar a ordem de cada coluna em uma única transação
      const updates = colunas.map(coluna => ({
        status_id: coluna.status_id,
        ordem: coluna.ordem
      }));
      
      // Verificar se a função RPC existe
      try {
        const { error } = await supabase.rpc('atualizar_ordem_colunas', { colunas_atualizadas: updates });
        
        if (error) {
          // Se a função RPC não existir, fazer atualizações individuais
          if (error.message.includes('does not exist') || error.message.includes('404')) {
            console.log('Função RPC não existe, fazendo atualizações individuais...');
            await atualizarOrdemIndividualmente(colunas);
          } else {
            console.error('Erro ao reordenar colunas do Kanban:', error);
            // Em caso de outros erros, tentar atualizações individuais antes de desistir
            console.log('Tentando atualizações individuais como fallback...');
            try {
              await atualizarOrdemIndividualmente(colunas);
            } catch (individualError) {
              console.error('Erro nas atualizações individuais:', individualError);
              // Ativar modo offline após falha na atualização individual também
              setModoOffline(true);
              return reordenarColunas(colunas); // Tentar novamente em modo offline
            }
          }
        }
      } catch (rpcError) {
        console.log('Erro ao chamar RPC, fazendo atualizações individuais...', rpcError);
        try {
          await atualizarOrdemIndividualmente(colunas);
        } catch (individualError) {
          console.error('Erro nas atualizações individuais após falha no RPC:', individualError);
          // Ativar modo offline após falha na atualização individual também
          setModoOffline(true);
          return reordenarColunas(colunas); // Tentar novamente em modo offline
        }
      }
      
      // Buscar as colunas atualizadas
      try {
        const colunasAtualizadas = await buscarTodasColunas();
        toast.success('Colunas reordenadas com sucesso!');
        return colunasAtualizadas;
      } catch (fetchError) {
        console.error('Erro ao buscar colunas atualizadas:', fetchError);
        // Se não conseguir buscar, retornar as colunas que foram passadas como parâmetro
        return colunas.map(coluna => ({
          ...coluna,
          alerts: []
        }));
      }
    } catch (dbError) {
      console.error('Erro durante operação de reordenamento no banco de dados:', dbError);
      // Em caso de erro durante operações no DB, ativar modo offline
      setModoOffline(true);
      return reordenarColunas(colunas); // Tentar novamente em modo offline
    }
  } catch (error) {
    console.error('Erro ao reordenar colunas do Kanban:', error);
    toast.error(`Erro ao reordenar colunas: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    
    // Em caso de erro não tratado, ativar modo offline
    setModoOffline(true);
    toast.info('Continuando em modo offline...');
    
    // Retornar as colunas originais com alerta
    return colunas.map(coluna => ({
      ...coluna,
      alerts: [{
        tipo: 'warning',
        mensagem: 'Reordenada em modo offline devido a erro'
      }]
    }));
  }
}

// Função auxiliar para atualizar a ordem das colunas individualmente
async function atualizarOrdemIndividualmente(colunas: Omit<KanbanColuna, 'alerts'>[]): Promise<void> {
  let sucessos = 0;
  let falhas = 0;
  
  for (const coluna of colunas) {
    try {
      // Verificar se a coluna existe antes de tentar atualizar
      const { data: checkData, error: checkError } = await supabase
        .from('kanban_colunas')
        .select('*')
        .eq('status_id', coluna.status_id)
        .single();
      
      if (checkError || !checkData) {
        console.warn(`Coluna ${coluna.status_id} não encontrada para atualização de ordem:`, checkError);
        falhas++;
        continue; // Continuar com a próxima coluna
      }
      
      // Atualizar a ordem da coluna
      const { error } = await supabase
        .from('kanban_colunas')
        .update({ ordem: coluna.ordem })
        .eq('status_id', coluna.status_id);
      
      if (error) {
        // Em caso de erro 406 (Not Acceptable) ou similar, verificar se a coluna existe
        if (error.code === '406' || error.code === 'PGRST116') {
          console.warn(`Coluna ${coluna.status_id} não encontrada para atualização de ordem.`);
          falhas++;
        } else {
          console.error(`Erro ao atualizar ordem da coluna ${coluna.status_id}:`, error);
          falhas++;
        }
      } else {
        sucessos++;
      }
    } catch (updateError) {
      console.error(`Erro ao processar atualização da coluna ${coluna.status_id}:`, updateError);
      falhas++;
    }
  }
  
  // Registrar resultados finais
  console.log(`Atualização de ordem finalizada: ${sucessos} sucessos, ${falhas} falhas.`);
  
  // Se houver mais falhas do que sucessos, ativar modo offline
  if (falhas > sucessos && colunas.length > 0) {
    console.warn('Muitas falhas na atualização individual, ativando modo offline para próximas operações');
    setModoOffline(true);
  }
  
  // Se todas falharem, lançar erro para que o chamador possa tratar
  if (falhas === colunas.length && colunas.length > 0) {
    throw new Error('Todas as atualizações de ordem individuais falharam');
  }
}

// Função para obter colunas padrão
function getColunasDefault(tipo: 'PRECATORIO' | 'RPV' | 'AMBOS'): KanbanColuna[] {
  const colunasPadrao: KanbanColuna[] = [
    { 
      id: 'analise', 
      name: 'Análise', 
      color: '#3b82f6',
      tipo: 'AMBOS',
      ordem: 1,
      status_id: 'analise',
      ativo: true,
      alerts: []
    },
    { 
      id: 'proposta_tmj', 
      name: 'Proposta TMJ', 
      color: '#8b5cf6',
      tipo: 'AMBOS',
      ordem: 2,
      status_id: 'proposta_tmj',
      ativo: true,
      alerts: []
    },
    { 
      id: 'proposta_btg', 
      name: 'Proposta BTG', 
      color: '#ec4899',
      tipo: 'AMBOS',
      ordem: 3,
      status_id: 'proposta_btg',
      ativo: true,
      alerts: []
    },
    { 
      id: 'negociacao', 
      name: 'Negociação', 
      color: '#f59e0b',
      tipo: 'AMBOS',
      ordem: 4,
      status_id: 'negociacao',
      ativo: true,
      alerts: []
    },
    { 
      id: 'documentacao', 
      name: 'Documentação', 
      color: '#10b981',
      tipo: 'AMBOS',
      ordem: 5,
      status_id: 'documentacao',
      ativo: true,
      alerts: []
    },
    { 
      id: 'pagamento', 
      name: 'Pagamento', 
      color: '#6366f1',
      tipo: 'AMBOS',
      ordem: 6,
      status_id: 'pagamento',
      ativo: true,
      alerts: []
    },
    { 
      id: 'concluido', 
      name: 'Concluído', 
      color: '#22c55e',
      tipo: 'AMBOS',
      ordem: 7,
      status_id: 'concluido',
      ativo: true,
      alerts: []
    },
    { 
      id: 'cancelado', 
      name: 'Cancelado', 
      color: '#ef4444',
      tipo: 'AMBOS',
      ordem: 8,
      status_id: 'cancelado',
      ativo: true,
      alerts: []
    }
  ];
  
  return colunasPadrao;
}

// Função auxiliar para mapear uma coluna do formato do banco para o formato da aplicação
function mapearColunaDB(colunaDB: any): KanbanColuna {
  if (!colunaDB) return null;
  
  return {
    id: colunaDB.id || colunaDB.status_id,
    name: colunaDB.nome,
    color: colunaDB.cor,
    tipo: colunaDB.tipo,
    ordem: colunaDB.ordem,
    status_id: colunaDB.status_id,
    ativo: colunaDB.ativo,
    alerts: []
  };
}

// Atualizar contagem nas colunas do Kanban
export async function atualizarContagemColunas(colunas: KanbanColuna[], tipo: 'PRECATORIO' | 'RPV' | 'AMBOS'): Promise<KanbanColuna[]> {
  try {
    // Em vez de usar group, vamos buscar todos os precatórios do tipo e contar manualmente
    const tipoFiltro = tipo === 'AMBOS' ? undefined : tipo;
    
    // Consulta diferente para cada caso
    let precatoriosData: any[] = [];
    let precatoriosError = null;
    
    if (tipoFiltro) {
      // Buscar precatórios apenas do tipo especificado
      const result = await supabase
        .from('precatorios')
        .select('id, status, tipo')
        .eq('tipo', tipoFiltro);
        
      precatoriosData = result.data || [];
      precatoriosError = result.error;
    } else {
      // Buscar todos os precatórios para AMBOS
      const result = await supabase
        .from('precatorios')
        .select('id, status, tipo');
        
      precatoriosData = result.data || [];
      precatoriosError = result.error;
    }
    
    if (precatoriosError) {
      console.error(`Erro ao buscar precatórios para o tipo ${tipo}:`, precatoriosError);
      throw precatoriosError;
    }
    
    // Contar manualmente os precatórios por status
    const contagemPorStatus: Record<string, number> = {};
    
    precatoriosData.forEach(precatorio => {
      // Normalizar o status para evitar problemas com maiúsculas/minúsculas
      if (precatorio.status) {
        const statusNormalizado = precatorio.status.trim().toLowerCase();
        contagemPorStatus[statusNormalizado] = (contagemPorStatus[statusNormalizado] || 0) + 1;
      }
    });
    
    // Atualizar a contagem nas colunas
    const colunasAtualizadas = colunas.map(coluna => {
      // Normalizar status_id da coluna para compatibilidade com a contagem
      const statusIdNormalizado = coluna.status_id.trim().toLowerCase();
      const count = contagemPorStatus[statusIdNormalizado] || 0;
      
      // Remover contagem anterior do nome, se existir
      const originalName = coluna.name.includes(' (') ? coluna.name.split(' (')[0] : coluna.name;
      
      return {
        ...coluna,
        name: `${originalName} (${count})`,
        count, // Adicionar contagem explicitamente
        alerts: count > 0 ? [
          { tipo: 'info', mensagem: `${count} ${tipo === 'PRECATORIO' ? 'precatório(s)' : 'RPV(s)'}` }
        ] : []
      };
    });
    
    return colunasAtualizadas;
  } catch (error) {
    console.error(`Erro ao atualizar contagem nas colunas do Kanban:`, error);
    // Em caso de erro, retornar as colunas originais mas com alerts de erro
    return colunas.map(coluna => ({
      ...coluna,
      alerts: [{ tipo: 'erro', mensagem: 'Erro ao contar precatórios' }]
    }));
  }
}
