import { supabase } from '@/lib/supabase';

export interface Cliente {
  id: string;
  nome: string;
  email: string;
  telefone: string;
  cpf_cnpj: string;
  endereco: string;
  cidade: string;
  estado: string;
  data_cadastro: string;
  notas: string;
  created_at: string;
  tipo: 'pessoa_fisica' | 'pessoa_juridica';
  status: string;
  updated_at: string;
}

export interface ClienteComTotais extends Cliente {
  total_precatorios: number;
  valor_total: number;
}

// Buscar todos os clientes com informações de precatórios
export async function buscarClientes(): Promise<ClienteComTotais[]> {
  try {
    const { data, error } = await supabase
      .from('view_clientes_com_totais')
      .select('*');

    if (error) {
      console.error('Erro ao buscar clientes:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Erro ao buscar clientes:', error);
    throw error;
  }
}

// Buscar um cliente específico por ID
export async function buscarClientePorId(id: string): Promise<ClienteComTotais | null> {
  try {
    const { data, error } = await supabase
      .from('view_clientes_com_totais')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error(`Erro ao buscar cliente com ID ${id}:`, error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error(`Erro ao buscar cliente com ID ${id}:`, error);
    throw error;
  }
}

// Criar um novo cliente
export async function criarCliente(cliente: Omit<Cliente, 'id' | 'created_at' | 'updated_at'>): Promise<Cliente> {
  try {
    const { data, error } = await supabase
      .from('clientes')
      .insert([cliente])
      .select()
      .single();

    if (error) {
      console.error('Erro ao criar cliente:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Erro ao criar cliente:', error);
    throw error;
  }
}

// Atualizar um cliente existente
export async function atualizarCliente(id: string, cliente: Partial<Omit<Cliente, 'id' | 'created_at' | 'updated_at'>>): Promise<Cliente> {
  try {
    const { data, error } = await supabase
      .from('clientes')
      .update(cliente)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error(`Erro ao atualizar cliente com ID ${id}:`, error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error(`Erro ao atualizar cliente com ID ${id}:`, error);
    throw error;
  }
}

// Excluir um cliente
export async function excluirCliente(id: string): Promise<void> {
  try {
    const { error } = await supabase
      .from('clientes')
      .delete()
      .eq('id', id);

    if (error) {
      console.error(`Erro ao excluir cliente com ID ${id}:`, error);
      throw error;
    }
  } catch (error) {
    console.error(`Erro ao excluir cliente com ID ${id}:`, error);
    throw error;
  }
}

// Buscar clientes com filtros
export async function buscarClientesComFiltros(filtros: {
  termo?: string;
  status?: string;
  tipo?: string;
}): Promise<ClienteComTotais[]> {
  try {
    let query = supabase
      .from('view_clientes_com_totais')
      .select('*');

    // Aplicar filtro de termo de busca (nome, email, cpf_cnpj)
    if (filtros.termo) {
      query = query.or(`nome.ilike.%${filtros.termo}%,email.ilike.%${filtros.termo}%,cpf_cnpj.ilike.%${filtros.termo}%`);
    }

    // Aplicar filtro de status
    if (filtros.status && filtros.status !== 'todos') {
      query = query.eq('status', filtros.status);
    }

    // Aplicar filtro de tipo
    if (filtros.tipo && filtros.tipo !== 'todos') {
      query = query.eq('tipo', filtros.tipo);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Erro ao buscar clientes com filtros:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Erro ao buscar clientes com filtros:', error);
    throw error;
  }
}
