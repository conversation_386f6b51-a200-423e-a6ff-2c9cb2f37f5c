import React, { use<PERSON>emo } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Search, Filter, Calendar as CalendarIcon, User, Pie<PERSON><PERSON>, Clock } from "lucide-react";
import { tipoEventoConfig, eventColors, statusConfig, prioridadeConfig } from "../../data/eventConfig";
import { Filtro, Evento } from "../../utils/calendarHelpers";
import { format, startOfMonth, endOfMonth, eachDayOfInterval, isSameMonth, isToday, isSameDay } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Separator } from "@/components/ui/separator";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { getInitials, getColorFromName } from "../../utils/calendarHelpers";
import { ScrollArea } from "@/components/ui/scroll-area";

interface CalendarFiltersProps {
  filtros: Filtro;
  setFiltros: React.Dispatch<React.SetStateAction<Filtro>>;
  eventos?: Evento[]; // Adicionando eventos como prop opcional
}

export const CalendarFilters: React.FC<CalendarFiltersProps> = ({
  filtros,
  setFiltros,
  eventos = [] // Valor padrão caso não seja fornecido
}) => {
  // Função para alternar tipo de evento no filtro
  const toggleTipoEvento = (tipo: string) => {
    if (filtros.tipo.includes(tipo)) {
      setFiltros(prev => ({
        ...prev,
        tipo: prev.tipo.filter(t => t !== tipo)
      }));
    } else {
      setFiltros(prev => ({
        ...prev,
        tipo: [...prev.tipo, tipo]
      }));
    }
  };

  // Função para alternar status no filtro
  const toggleStatus = (status: string) => {
    if (filtros.status.includes(status)) {
      setFiltros(prev => ({
        ...prev,
        status: prev.status.filter(s => s !== status)
      }));
    } else {
      setFiltros(prev => ({
        ...prev,
        status: [...prev.status, status]
      }));
    }
  };

  // Selecionar todos os tipos
  const selectAllTipos = () => {
    setFiltros(prev => ({
      ...prev,
      tipo: Object.keys(tipoEventoConfig)
    }));
  };

  // Limpar todos os tipos
  const clearAllTipos = () => {
    setFiltros(prev => ({
      ...prev,
      tipo: []
    }));
  };

  // Atualizar texto de pesquisa
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFiltros(prev => ({
      ...prev,
      texto: e.target.value
    }));
  };

  // Mini-calendário
  const currentDate = new Date();
  const firstDayOfMonth = startOfMonth(currentDate);
  const lastDayOfMonth = endOfMonth(currentDate);
  const daysInMonth = eachDayOfInterval({ start: firstDayOfMonth, end: lastDayOfMonth });
  
  // Dias da semana em português
  const weekDays = ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'];
  
  // Estatísticas de eventos
  const estatisticas = useMemo(() => {
    // Total de eventos
    const total = eventos.length;
    
    // Eventos por tipo
    const porTipo = Object.keys(tipoEventoConfig).reduce((acc, tipo) => {
      acc[tipo] = eventos.filter(e => e.tipo === tipo).length;
      return acc;
    }, {} as Record<string, number>);
    
    // Eventos por status
    const porStatus = ['pendente', 'confirmado', 'cancelado', 'concluido'].reduce((acc, status) => {
      acc[status] = eventos.filter(e => e.status === status).length;
      return acc;
    }, {} as Record<string, number>);
    
    // Eventos por prioridade
    const porPrioridade = ['baixa', 'media', 'alta', 'urgente'].reduce((acc, prioridade) => {
      acc[prioridade] = eventos.filter(e => e.prioridade === prioridade).length;
      return acc;
    }, {} as Record<string, number>);
    
    // Eventos para hoje
    const hoje = format(new Date(), 'yyyy-MM-dd');
    const eventosHoje = eventos.filter(e => e.data === hoje).length;
    
    return { total, porTipo, porStatus, porPrioridade, eventosHoje };
  }, [eventos]);
  
  // Lista de responsáveis únicos
  const responsaveis = useMemo(() => {
    const lista = [...new Set(eventos.map(e => e.responsavel))].sort();
    return lista;
  }, [eventos]);

  return (
    <Card className="shadow-sm">
      <CardHeader className="pb-3 sticky top-0 bg-white dark:bg-gray-950 z-10">
        <CardTitle className="text-lg flex items-center">
          <Filter className="w-5 h-5 mr-2" />
          Filtros
        </CardTitle>
      </CardHeader>
      <ScrollArea className="h-[calc(100vh-180px)]">
        <CardContent className="space-y-6 pb-6">
          {/* Pesquisa */}
          <div className="relative">
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-500 h-4 w-4" />
            <Input
              placeholder="Pesquisar eventos..."
              className="pl-8"
              value={filtros.texto}
              onChange={handleSearchChange}
            />
          </div>
          
          {/* Mini-calendário */}
          <div>
            <h3 className="font-medium mb-2 flex items-center">
              <CalendarIcon className="w-4 h-4 mr-2" />
              Calendário
            </h3>
            <div className="bg-gray-50 dark:bg-gray-800 rounded-md p-3">
              <div className="text-center mb-2 font-medium">
                {format(currentDate, 'MMMM yyyy', { locale: ptBR })}
              </div>
              <div className="grid grid-cols-7 gap-1 mb-1">
                {weekDays.map(day => (
                  <div key={day} className="text-center text-xs text-gray-500">
                    {day}
                  </div>
                ))}
              </div>
              <div className="grid grid-cols-7 gap-1">
                {Array.from({ length: firstDayOfMonth.getDay() }).map((_, i) => (
                  <div key={`empty-${i}`} className="h-7" />
                ))}
                {daysInMonth.map(day => {
                  const isCurrentMonth = isSameMonth(day, currentDate);
                  const isCurrentDay = isToday(day);
                  
                  // Verificar se há eventos neste dia
                  const dayFormatted = format(day, 'yyyy-MM-dd');
                  const hasEvents = eventos.some(e => e.data === dayFormatted);
                  
                  return (
                    <div 
                      key={day.toString()}
                      className={`
                        h-7 w-7 flex items-center justify-center rounded-full text-xs
                        ${!isCurrentMonth ? 'text-gray-300 dark:text-gray-600' : ''}
                        ${isCurrentDay ? 'bg-blue-500 text-white' : ''}
                        ${hasEvents && !isCurrentDay ? 'font-bold' : ''}
                        relative
                      `}
                    >
                      {format(day, 'd')}
                      {hasEvents && !isCurrentDay && (
                        <span className="absolute bottom-0 w-1 h-1 bg-blue-500 rounded-full"></span>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
          
          {/* Estatísticas */}
          <div>
            <h3 className="font-medium mb-2 flex items-center">
              <PieChart className="w-4 h-4 mr-2" />
              Estatísticas
            </h3>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm">Total de eventos:</span>
                <Badge variant="outline">{estatisticas.total}</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Eventos hoje:</span>
                <Badge variant="outline" className="bg-blue-50 dark:bg-blue-900">
                  {estatisticas.eventosHoje}
                </Badge>
              </div>
              
              {/* Eventos por prioridade */}
              <div className="pt-2">
                <span className="text-sm font-medium">Por prioridade:</span>
                <div className="flex flex-wrap gap-2 mt-1">
                  {Object.entries(estatisticas.porPrioridade).map(([prioridade, count]) => (
                    count > 0 && (
                      <Badge 
                        key={prioridade} 
                        variant="outline"
                        className={`text-xs ${prioridadeConfig[prioridade as keyof typeof prioridadeConfig]}`}
                      >
                        {prioridade}: {count}
                      </Badge>
                    )
                  ))}
                </div>
              </div>
            </div>
          </div>
          
          <Separator />
          
          {/* Tipos de Evento */}
          <div>
            <h3 className="font-medium mb-2">Tipos de Evento</h3>
            <div className="flex flex-wrap gap-2 mb-2">
              {Object.keys(tipoEventoConfig).map(tipo => (
                <Badge
                  key={tipo}
                  variant={filtros.tipo.includes(tipo) ? "default" : "outline"}
                  className={`cursor-pointer ${filtros.tipo.includes(tipo) ? eventColors[tipo] : ''}`}
                  onClick={() => toggleTipoEvento(tipo)}
                >
                  <span className="mr-1">{tipoEventoConfig[tipo].icon}</span>
                  {tipoEventoConfig[tipo].nome}
                  {estatisticas.porTipo[tipo] > 0 && (
                    <span className="ml-1 text-xs">({estatisticas.porTipo[tipo]})</span>
                  )}
                </Badge>
              ))}
            </div>
            <div className="flex gap-2 mt-2">
              <Button variant="outline" size="sm" onClick={selectAllTipos}>
                Selecionar Todos
              </Button>
              <Button variant="outline" size="sm" onClick={clearAllTipos}>
                Limpar
              </Button>
            </div>
          </div>
          
          {/* Status */}
          <div>
            <h3 className="font-medium mb-2">Status</h3>
            <div className="space-y-2">
              {['pendente', 'confirmado', 'cancelado', 'concluido'].map(status => (
                <div key={status} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      id={`status-${status}`} 
                      checked={filtros.status.includes(status)}
                      onCheckedChange={() => toggleStatus(status)}
                    />
                    <Label htmlFor={`status-${status}`} className="capitalize">
                      {status}
                    </Label>
                  </div>
                  {estatisticas.porStatus[status] > 0 && (
                    <Badge 
                      variant="outline" 
                      className={statusConfig[status as keyof typeof statusConfig]}
                    >
                      {estatisticas.porStatus[status]}
                    </Badge>
                  )}
                </div>
              ))}
            </div>
          </div>
          
          {/* Responsáveis */}
          <div>
            <h3 className="font-medium mb-2 flex items-center">
              <User className="w-4 h-4 mr-2" />
              Responsáveis
            </h3>
            <div className="space-y-2 max-h-40 overflow-y-auto pr-2">
              {responsaveis.map(responsavel => {
                const count = eventos.filter(e => e.responsavel === responsavel).length;
                return (
                  <div key={responsavel} className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Avatar className="h-6 w-6">
                        <AvatarFallback className={getColorFromName(responsavel)}>
                          {getInitials(responsavel)}
                        </AvatarFallback>
                      </Avatar>
                      <span className="text-sm truncate max-w-[150px]">{responsavel}</span>
                    </div>
                    <Badge variant="outline">{count}</Badge>
                  </div>
                );
              })}
            </div>
          </div>
          
          {/* Próximos eventos */}
          <div>
            <h3 className="font-medium mb-2 flex items-center">
              <Clock className="w-4 h-4 mr-2" />
              Próximos Eventos
            </h3>
            <div className="space-y-2">
              {eventos
                .filter(e => {
                  const eventDate = new Date(e.data + 'T' + e.hora);
                  return eventDate >= new Date();
                })
                .sort((a, b) => {
                  const dateA = new Date(a.data + 'T' + a.hora);
                  const dateB = new Date(b.data + 'T' + b.hora);
                  return dateA.getTime() - dateB.getTime();
                })
                .slice(0, 3)
                .map(evento => (
                  <Card key={evento.id} className="p-2 text-xs">
                    <div className="flex items-start gap-2">
                      <div className={`w-1 self-stretch rounded-sm ${eventColors[evento.tipo]}`} />
                      <div>
                        <div className="font-medium">{evento.titulo}</div>
                        <div className="text-gray-500 flex items-center mt-1">
                          <CalendarIcon className="w-3 h-3 mr-1" />
                          {format(new Date(evento.data), 'dd/MM/yyyy')} às {evento.hora}
                        </div>
                      </div>
                    </div>
                  </Card>
                ))}
              {eventos.filter(e => new Date(e.data + 'T' + e.hora) >= new Date()).length === 0 && (
                <div className="text-center text-gray-500 text-xs py-2">
                  Nenhum evento próximo
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </ScrollArea>
    </Card>
  );
}; 