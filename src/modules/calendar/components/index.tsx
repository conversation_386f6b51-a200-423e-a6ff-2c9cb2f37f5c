import React, { useState, useEffect, useCallback } from 'react';
import { format, addDays, subDays, addMonths, subMonths, parseISO, startOfMonth, endOfMonth, startOfDay, endOfDay, startOfWeek, endOfWeek } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { ChevronLeft, ChevronRight, CalendarDays, Calendar as CalendarIcon, List, Grid, Loader2, Plus } from 'lucide-react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { CalendarDay } from './CalendarDay';
import { CalendarWeek } from './CalendarWeek';
import { CalendarMonth } from './CalendarMonth';
import { CalendarAgenda } from './CalendarAgenda';
import { CalendarFilters } from './CalendarFilters';
import { EventDialog } from './EventDialog';
import { Evento, Visualizacao, Filtro } from '../../utils/calendarHelpers';
// import { eventos as eventosData } from '../../data/mockEvents';
import { getEventosPorPeriodo } from '@/services/eventosService';
import { useToast } from '@/components/ui/use-toast';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/contexts/AuthContext';

const Calendar = () => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [visualizacao, setVisualizacao] = useState<Visualizacao>('dia');
  const [eventos, setEventos] = useState<Evento[]>([]);
  const [filteredEvents, setFilteredEvents] = useState<Evento[]>([]);
  const [filtros, setFiltros] = useState<Filtro>({
    tipo: [],
    status: [],
    texto: '',
  });
  const [selectedEvent, setSelectedEvent] = useState<Evento | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isCreatingEvent, setIsCreatingEvent] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const { user } = useAuth();

  // Configurar altura da viewport para dispositivos móveis (corrigir altura em iOS)
  useEffect(() => {
    const setVh = () => {
      const vh = window.innerHeight * 0.01;
      document.documentElement.style.setProperty('--vh', `${vh}px`);
    };
    
    setVh();
    window.addEventListener('resize', setVh);
    
    return () => {
      window.removeEventListener('resize', setVh);
    };
  }, []);

  // Função melhorada para renovar a sessão a cada 5 minutos
  useEffect(() => {
    let mounted = true;
    
    // Verificar tokens existentes para depuração
    console.log("Calendar: Tokens disponíveis:", 
      Object.keys(localStorage).filter(k => k.includes('sb-') || k.includes('supabase')));
    
    // Função para renovar a sessão de forma mais robusta
    const refreshSession = async () => {
      if (!mounted) return;
      
      try {
        console.log('Calendar: Renovando sessão do Supabase...');
        
        // Verificar sessão atual antes de tentar renovar
        const { data: sessionData } = await supabase.auth.getSession();
        if (!sessionData.session) {
          console.warn('Calendar: Sessão não encontrada, tentando recuperar...');
        }
        
        // Tentar renovar a sessão
        const { data, error } = await supabase.auth.refreshSession();
        
        if (error) {
          console.error('Calendar: Erro ao renovar sessão:', error);
          toast.error("Erro de autenticação", {
            description: "Houve um problema ao renovar sua sessão. Os dados continuarão disponíveis, mas você pode precisar fazer login novamente em breve."
          });
        } else if (data.session) {
          console.log('Calendar: Sessão renovada com sucesso');
          
          // Se não tivermos perfil no localStorage, mas tivermos sessão, tentar criar
          const userProfileStr = localStorage.getItem("userProfile");
          if (!userProfileStr && data.session.user) {
            try {
              console.log('Calendar: Tentando recuperar perfil para localStorage');
              const { data: profileData } = await supabase
                .from("profiles")
                .select("*")
                .eq("id", data.session.user.id)
                .single();
                
              if (profileData) {
                localStorage.setItem("userProfile", JSON.stringify(profileData));
                console.log('Calendar: Perfil recuperado para localStorage');
              }
            } catch (profileError) {
              console.warn('Calendar: Erro ao recuperar perfil:', profileError);
            }
          }
        }
      } catch (error) {
        console.error('Calendar: Erro inesperado ao renovar sessão:', error);
      }
    };

    // Renovar imediatamente e depois a cada 5 minutos
    refreshSession();
    const interval = setInterval(refreshSession, 5 * 60 * 1000);
    
    return () => {
      mounted = false;
      clearInterval(interval);
    };
  }, []);

  // Buscar eventos do banco de dados
  const fetchEventos = useCallback(async () => {
    try {
      setIsLoading(true);
      console.log("Calendar: Buscando eventos...");
      
      // Sempre tentar renovar a sessão antes de buscar eventos
      try {
        await supabase.auth.refreshSession();
      } catch (refreshError) {
        console.warn("Calendar: Não foi possível renovar a sessão antes de buscar eventos:", refreshError);
        // Continuar mesmo assim, talvez a sessão atual ainda seja válida
      }
      
      // Calcular intervalo de datas
      let dataInicio: Date, dataFim: Date;
      
      if (visualizacao === 'dia') {
        dataInicio = startOfDay(currentDate);
        dataFim = endOfDay(currentDate);
      } else if (visualizacao === 'semana') {
        dataInicio = startOfWeek(currentDate, { weekStartsOn: 0 });
        dataFim = endOfWeek(currentDate, { weekStartsOn: 0 });
      } else if (visualizacao === 'mes') {
        dataInicio = startOfMonth(currentDate);
        dataFim = endOfMonth(currentDate);
      } else if (visualizacao === 'agenda') {
        // Para agenda, mostrar 30 dias a partir de hoje
        dataInicio = startOfDay(new Date());
        dataFim = addDays(dataInicio, 30);
      } else {
        dataInicio = startOfWeek(currentDate, { weekStartsOn: 0 });
        dataFim = endOfWeek(currentDate, { weekStartsOn: 0 });
      }
      
      // Formatar datas
      const dataInicioStr = format(dataInicio, 'yyyy-MM-dd');
      const dataFimStr = format(dataFim, 'yyyy-MM-dd');
      
      console.log(`Calendar: Buscando eventos de ${dataInicioStr} até ${dataFimStr}`);
      
      // Buscar eventos com tratamento de erros aprimorado
      try {
        const eventosCarregados = await getEventosPorPeriodo(dataInicioStr, dataFimStr);
        setEventos(eventosCarregados);
        console.log(`Calendar: ${eventosCarregados.length} eventos carregados com sucesso`);
      } catch (fetchError: any) {
        console.error("Calendar: Erro ao carregar eventos:", fetchError);
        
        // Verificar se é erro de autenticação (401)
        if (fetchError.code === "PGRST301" || 
            (fetchError.message && fetchError.message.includes("JWT")) || 
            fetchError.status === 401) {
          console.warn("Calendar: Erro de autenticação ao buscar eventos, tentando renovar sessão");
          
          // Tentar renovar sessão e buscar novamente
          try {
            const { data } = await supabase.auth.refreshSession();
            if (data.session) {
              console.log("Calendar: Sessão renovada, tentando buscar eventos novamente");
              const eventosRetentativa = await getEventosPorPeriodo(dataInicioStr, dataFimStr);
              setEventos(eventosRetentativa);
              console.log(`Calendar: ${eventosRetentativa.length} eventos carregados após renovação da sessão`);
            }
          } catch (retryError) {
            console.error("Calendar: Falha na segunda tentativa:", retryError);
            toast({
              title: "Erro de autenticação",
              description: "Não foi possível validar sua sessão. Por favor, recarregue a página ou faça login novamente.",
              variant: "destructive"
            });
          }
        } else {
          // Outro tipo de erro
          toast({
            title: "Erro",
            description: "Não foi possível carregar os eventos. Por favor, tente novamente.",
            variant: "destructive"
          });
        }
      }
    } catch (error) {
      console.error("Calendar: Erro não tratado:", error);
      toast({
        title: "Erro inesperado",
        description: "Ocorreu um erro ao processar sua solicitação.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }, [currentDate, visualizacao, toast]);

  // Carregar eventos quando a data ou visualização mudar
  useEffect(() => {
    fetchEventos();
  }, [currentDate, visualizacao, fetchEventos]);

  // Atualizar eventos filtrados quando os filtros ou eventos mudarem
  useEffect(() => {
    console.log("Aplicando filtros:", filtros);
    
    let result = [...eventos];
    console.log("Eventos antes da filtragem:", result);

    // Filtrar por tipo
    if (filtros.tipo.length > 0) {
      result = result.filter(evento => filtros.tipo.includes(evento.tipo));
    }

    // Filtrar por status
    if (filtros.status.length > 0) {
      result = result.filter(evento => filtros.status.includes(evento.status));
    }

    // Filtrar por texto
    if (filtros.texto) {
      const termo = filtros.texto.toLowerCase();
      result = result.filter(
        evento =>
          evento.titulo.toLowerCase().includes(termo) ||
          evento.descricao.toLowerCase().includes(termo) ||
          (evento.local && evento.local.toLowerCase().includes(termo))
      );
    }

    console.log("Eventos após filtragem:", result);
    setFilteredEvents(result);
  }, [filtros, eventos]);

  const handleDateChange = (direction: 'prev' | 'next') => {
    if (visualizacao === 'dia') {
      setCurrentDate(prev => (direction === 'prev' ? subDays(prev, 1) : addDays(prev, 1)));
    } else if (visualizacao === 'semana') {
      setCurrentDate(prev => (direction === 'prev' ? subDays(prev, 7) : addDays(prev, 7)));
    } else if (visualizacao === 'mes' || visualizacao === 'agenda') {
      setCurrentDate(prev => (direction === 'prev' ? subMonths(prev, 1) : addMonths(prev, 1)));
    }
  };

  const handleEventClick = (event: Evento) => {
    setSelectedEvent(event);
    setIsCreatingEvent(false);
    setIsDialogOpen(true);
  };

  const handleDateClick = (date: Date) => {
    setCurrentDate(date);
    setVisualizacao('dia');
  };

  const handleTodayClick = () => {
    setCurrentDate(new Date());
  };

  const handleEventUpdated = () => {
    fetchEventos();
  };

  const handleAddEvent = () => {
    if (isLoading) {
      toast({
        title: "Aguarde",
        description: "Carregando dados do calendário. Tente novamente em alguns instantes.",
        variant: "default",
      });
      return;
    }
    
    setSelectedEvent(null);
    setIsCreatingEvent(true);
    setIsDialogOpen(true);
  };

  // Modificar a renderização do componente CalendarAgenda
  const renderAgendaView = () => {
    console.log("Renderizando visualização de agenda");
    console.log("Eventos filtrados para agenda:", filteredEvents);
    
    if (isLoading) {
      return (
        <div className="p-4 text-center">
          <Loader2 className="w-12 h-12 text-gray-500 animate-spin mb-4 mx-auto" />
          <p className="text-gray-500">Carregando eventos...</p>
        </div>
      );
    }
    
    // Verificar se há eventos válidos
    if (!filteredEvents || !Array.isArray(filteredEvents) || filteredEvents.length === 0) {
      console.log("Nenhum evento para exibir na agenda");
      return (
        <div className="p-4 text-center">
          <CalendarIcon className="w-12 h-12 text-gray-300 dark:text-gray-600 mb-4 mx-auto" />
          <p className="text-gray-500">Nenhum evento encontrado para exibir na agenda.</p>
        </div>
      );
    }
    
    // Verificar se todos os eventos têm data válida
    const eventosValidos = filteredEvents.filter(evento => 
      evento && evento.data && typeof evento.data === 'string');
    
    if (eventosValidos.length !== filteredEvents.length) {
      console.warn("Alguns eventos têm formato de data inválido:", 
        filteredEvents.filter(e => !e || !e.data || typeof e.data !== 'string'));
    }
    
    return (
      <CalendarAgenda 
        currentDate={currentDate}
        filteredEvents={eventosValidos}
        onEventClick={handleEventClick}
      />
    );
  };

  return (
    <div className="calendar-layout">
      <div className="flex flex-col md:flex-row justify-between gap-4 p-4 pb-0">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" onClick={handleTodayClick}>
            <CalendarIcon className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="icon" onClick={() => handleDateChange('prev')}>
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="icon" onClick={() => handleDateChange('next')}>
            <ChevronRight className="h-4 w-4" />
          </Button>
          <div className="text-lg font-semibold">
            {visualizacao === 'dia' && format(currentDate, "EEEE, dd 'de' MMMM 'de' yyyy", { locale: ptBR })}
            {visualizacao === 'semana' && (
              <>Semana de {format(currentDate, "MMMM yyyy", { locale: ptBR })}</>
            )}
            {visualizacao === 'mes' && format(currentDate, "MMMM yyyy", { locale: ptBR })}
            {visualizacao === 'agenda' && format(currentDate, "MMMM yyyy", { locale: ptBR })}
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button 
            variant="default" 
            size="sm" 
            onClick={handleAddEvent}
            className="mr-2"
          >
            <Plus className="h-4 w-4 mr-1" />
            Novo Evento
          </Button>
          <Tabs 
            defaultValue={visualizacao} 
            className="w-full md:w-auto"
            onValueChange={(value) => setVisualizacao(value as Visualizacao)}
          >
            <TabsList>
              <TabsTrigger value="dia">
                <Grid className="h-4 w-4 mr-2" />
                Dia
              </TabsTrigger>
              <TabsTrigger value="semana">
                <CalendarDays className="h-4 w-4 mr-2" />
                Semana
              </TabsTrigger>
              <TabsTrigger value="mes">
                <CalendarIcon className="h-4 w-4 mr-2" />
                Mês
              </TabsTrigger>
              <TabsTrigger value="agenda">
                <List className="h-4 w-4 mr-2" />
                Agenda
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </div>

      <div className="calendar-content grid grid-cols-1 lg:grid-cols-4 gap-4 p-4 pt-2">
        <div className="calendar-panel lg:col-span-1">
          <CalendarFilters 
            filtros={filtros} 
            setFiltros={setFiltros} 
            eventos={eventos} 
          />
        </div>
        <div className="lg:col-span-3 overflow-auto custom-scrollbar">
          {isLoading ? (
            <div className="flex items-center justify-center h-[50vh]">
              <Loader2 className="w-12 h-12 text-gray-500 animate-spin" />
              <span className="ml-2 text-gray-500">Carregando eventos...</span>
            </div>
          ) : (
            <>
              {visualizacao === 'dia' && (
                <CalendarDay 
                  currentDate={currentDate}
                  filteredEvents={filteredEvents}
                  onEventClick={handleEventClick}
                />
              )}
              {visualizacao === 'semana' && (
                <CalendarWeek 
                  currentDate={currentDate}
                  filteredEvents={filteredEvents}
                  onEventClick={handleEventClick}
                  onDateClick={handleDateClick}
                />
              )}
              {visualizacao === 'mes' && (
                <CalendarMonth 
                  currentDate={currentDate}
                  filteredEvents={filteredEvents}
                  onEventClick={handleEventClick}
                  onDateClick={handleDateClick}
                />
              )}
              {visualizacao === 'agenda' && renderAgendaView()}
            </>
          )}
        </div>
      </div>

      <EventDialog 
        evento={isCreatingEvent ? null : selectedEvent} 
        isOpen={isDialogOpen || isCreatingEvent} 
        onClose={() => {
          setIsDialogOpen(false);
          setIsCreatingEvent(false);
          setSelectedEvent(null);
        }}
        onEventUpdated={handleEventUpdated}
      />
    </div>
  );
};

export default Calendar; 