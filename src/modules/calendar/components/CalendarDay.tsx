import React from "react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Evento } from "../../utils/calendarHelpers";
import { getEventosDoDia } from "../../utils/calendarHelpers";
import { tipoEventoConfig, eventColors, statusConfig } from "../../data/eventConfig";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { CalendarIcon, Clock, MapPin, User } from "lucide-react";

interface CalendarDayProps {
  currentDate: Date;
  filteredEvents: Evento[];
  onEventClick: (event: Evento) => void;
}

export const CalendarDay: React.FC<CalendarDayProps> = ({
  currentDate,
  filteredEvents,
  onEventClick
}) => {
  const eventosHoje = getEventosDoDia(currentDate, filteredEvents);
  const formattedDate = format(currentDate, 'EEEE, d MMMM yyyy', { locale: ptBR });
  const isToday = new Date().toDateString() === currentDate.toDateString();

  // Horários para exibir na grade (das 7h às 22h)
  const visibleHours = { start: 7, end: 22 };

  // Horas a serem mostradas
  const hoursToShow = [];
  for (let i = 0; i < 24; i++) {
    hoursToShow.push(i);
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between flex-wrap gap-2">
        <h2 className="text-2xl font-bold">{formattedDate}</h2>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className={isToday ? "bg-blue-100 dark:bg-blue-900/20" : ""}>
            {isToday ? "Hoje" : ""}
          </Badge>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                // Aqui poderia abrir um modal para criar novo evento
                alert("Funcionalidade de criar evento será implementada em breve!");
              }}
            >
              Criar evento
            </Button>
          </div>
        </div>
      </div>

      <ScrollArea className="h-[700px]">
        <div className="relative min-h-[1400px] bg-gray-50 dark:bg-gray-900 rounded-lg border shadow-sm">
          {/* Timeline for hours */}
          <div className="sticky top-0 bg-white dark:bg-gray-900 z-10 p-2 border-b text-center shadow-sm">
            <span className="text-sm font-medium">
              Mostrando {visibleHours.start}:00 - {visibleHours.end}:00
            </span>
          </div>
          
          <div className="absolute inset-0 mt-10 grid grid-cols-[60px_1fr] divide-x">
            {/* Hours */}
            <div className="space-y-16 pt-6 pr-4">
              {hoursToShow.map((hour) => (
                <div key={hour} className="text-right text-sm text-gray-500">
                  {hour}:00
                </div>
              ))}
            </div>

            {/* Event container */}
            <div className="relative">
              {/* Horizontal hour lines */}
              {hoursToShow.map((hour) => (
                <div 
                  key={hour} 
                  className="absolute w-full h-px bg-gray-200 dark:bg-gray-800"
                  style={{ top: `${(hour / 24) * 100}%` }}
                ></div>
              ))}
              
              {/* Current hour highlight for better visibility */}
              {isToday && (
                <div 
                  className="absolute w-full opacity-10 bg-blue-200 dark:bg-blue-800 z-0" 
                  style={{
                    top: `${(new Date().getHours() / 24) * 100}%`,
                    height: `${(1 / 24) * 100}%`
                  }}
                ></div>
              )}
              
              {/* Time indicator for current time */}
              {isToday && (
                <div 
                  className="absolute w-full h-0.5 bg-red-500 z-10 flex items-center" 
                  style={{
                    top: `${(new Date().getHours() * 60 + new Date().getMinutes()) / (24 * 60) * 100}%`
                  }}
                >
                  <span className="px-1 py-0.5 text-xs text-white bg-red-500 rounded-md ml-1">
                    {format(new Date(), 'HH:mm')}
                  </span>
                </div>
              )}

              {/* Render events */}
              {eventosHoje.map(evento => {
                const [hour, minute] = evento.hora.split(':').map(Number);
                const top = (hour * 60 + minute) / (24 * 60) * 100;
                const height = (evento.duracao / (24 * 60)) * 100;
                const opacity = (hour < visibleHours.start || hour > visibleHours.end) ? 'opacity-50' : 'opacity-100';
                
                return (
                  <TooltipProvider key={evento.id}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div 
                          className={`absolute rounded-md p-2 shadow-sm ${eventColors[evento.tipo]} hover:brightness-90 transition-all cursor-pointer overflow-hidden ${opacity}`}
                          style={{
                            top: `${top}%`,
                            height: `${height}%`,
                            left: '8px',
                            right: '8px',
                            minHeight: '24px', // Ensure very short events are still clickable
                          }}
                          onClick={() => onEventClick(evento)}
                        >
                          <div className="flex items-start space-x-2">
                            <span className="text-lg">{tipoEventoConfig[evento.tipo].icon}</span>
                            <div className="flex-1">
                              <h3 className="font-medium truncate">{evento.titulo}</h3>
                              {height > 2 && (
                                <>
                                  <p className="text-sm opacity-80 truncate">{evento.descricao}</p>
                                  <div className="flex items-center gap-1 text-xs mt-1">
                                    <Clock className="w-3 h-3" />
                                    {evento.hora} - {format(new Date(new Date().setHours(hour, minute + evento.duracao)), 'HH:mm')}
                                  </div>

                                  {height > 5 && (
                                    <div className="flex items-center gap-1 text-xs mt-1">
                                      <MapPin className="w-3 h-3" />
                                      {evento.local}
                                    </div>
                                  )}
                                </>
                              )}
                            </div>
                          </div>
                        </div>
                      </TooltipTrigger>
                      <TooltipContent side="right">
                        <div className="p-2 space-y-2 max-w-xs">
                          <div className="font-bold">{evento.titulo}</div>
                          <div className="text-sm">{evento.descricao}</div>
                          <div className="flex items-center text-xs">
                            <Clock className="w-3 h-3 mr-1" />
                            {evento.hora} - {format(new Date(new Date().setHours(hour, minute + evento.duracao)), 'HH:mm')}
                          </div>
                          <div className="flex items-center text-xs">
                            <MapPin className="w-3 h-3 mr-1" />
                            {evento.local}
                          </div>
                          <div className="flex items-center text-xs">
                            <User className="w-3 h-3 mr-1" />
                            {evento.responsavel}
                          </div>
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                );
              })}
            </div>
          </div>
        </div>
      </ScrollArea>
      
      {eventosHoje.length === 0 && (
        <div className="flex flex-col items-center justify-center p-6 text-center text-gray-500 border rounded-lg bg-white dark:bg-gray-800">
          <CalendarIcon className="w-12 h-12 mb-2 text-gray-400" />
          <h3 className="text-lg font-medium">Nenhum evento para este dia</h3>
          <p className="max-w-sm mt-1 text-sm">
            {isToday 
              ? "Você não tem nenhum evento agendado para hoje." 
              : `Você não tem nenhum evento agendado para ${format(currentDate, 'd MMMM', { locale: ptBR })}.`
            }
          </p>
          <Button
            variant="outline"
            className="mt-4"
            onClick={() => {
              // Aqui poderia abrir um modal para criar novo evento
              alert("Funcionalidade de criar evento será implementada em breve!");
            }}
          >
            Criar evento
          </Button>
        </div>
      )}
    </div>
  );
}; 