import React, { useEffect, useState } from 'react';
import Calendar from '@/components/Calendar';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';
import CalendarComponent from "@/components/Calendar";
import { ensureValidSession } from "@/lib/auth-helpers";

export default function CalendarPage() {
  const navigate = useNavigate();
  const { user, isLoading } = useAuth();
  const [verifyingAuth, setVerifyingAuth] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  
  // Abordagem mais tolerante de verificação de autenticação
  useEffect(() => {
    const verifyAuthentication = async () => {
      try {
        console.log("CalendarPage: Verificando autenticação...");
        
        // 1. Verificar se o contexto de autenticação já está pronto
        if (!isLoading) {
          if (user) {
            console.log("CalendarPage: Usuário autenticado via contexto:", user.email);
            setIsAuthenticated(true);
            setVerifyingAuth(false);
            return;
          }
          
          console.log("CalendarPage: Usuário não encontrado no contexto, verificando outras fontes...");
          
          // 2. Tentar garantir uma sessão válida usando a função auxiliar
          const { success, session } = await ensureValidSession();
          
          if (success && session) {
            console.log("CalendarPage: Sessão válida encontrada");
            setIsAuthenticated(true);
            setVerifyingAuth(false);
            return;
          }
          
          // 3. Verificar se temos um perfil no localStorage
          const userProfileStr = localStorage.getItem("userProfile");
          if (userProfileStr) {
            console.log("CalendarPage: Perfil encontrado no localStorage, mas sem sessão válida");
            // Permitir acesso mesmo sem sessão válida, mas mostrar aviso
            toast.warning("Aviso de autenticação", {
              description: "Você está usando o sistema em modo offline ou com autenticação limitada."
            });
            setIsAuthenticated(true);
            setVerifyingAuth(false);
            return;
          }
          
          // 4. Se chegamos aqui, não conseguimos autenticar o usuário
          console.error("CalendarPage: Não foi possível verificar autenticação por nenhum método");
          toast.error("Problema de autenticação", {
            description: "Não conseguimos verificar sua autenticação. Tente fazer login novamente."
          });
          
          setIsAuthenticated(false);
          setVerifyingAuth(false);
        }
      } catch (error) {
        console.error("CalendarPage: Erro na verificação:", error);
        setIsAuthenticated(false);
        setVerifyingAuth(false);
      }
    };
    
    verifyAuthentication();
  }, [user, isLoading, navigate]);
  
  // Mostrar estado de carregamento apenas se estiver verificando
  if (verifyingAuth) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mb-4"></div>
        <p className="text-sm text-gray-500">Verificando autenticação...</p>
      </div>
    );
  }
  
  // Se não estiver autenticado, mostrar botão para fazer login
  if (!isAuthenticated) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold text-red-600 mb-2">Problema de Autenticação</h2>
          <p className="text-gray-600 mb-4">Não foi possível verificar sua autenticação para acessar o calendário.</p>
        </div>
        <button 
          onClick={() => navigate('/login')}
          className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors"
        >
          Fazer Login Novamente
        </button>
      </div>
    );
  }
  
  // Se estiver autenticado, mostrar o calendário
  return <Calendar />;
} 