import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { PermissionRoute } from "./components/permissions/PermissionRoute";
import { ThemeProvider } from "next-themes";
import ErrorBoundary from "@/components/ErrorBoundary";
import { AuthProvider } from "@/contexts/AuthContext";
import { PermissionsProvider } from "@/contexts/PermissionsContext";
import SessionManager from "@/components/SessionManager";
import SessionRecoveryManager from "@/components/SessionRecoveryManager";
import Login from "./pages/Login";
import Index from "./pages/Index";
import Dashboard from "./pages/Dashboard";
import EmployeeAnalysis from "./pages/EmployeeAnalysis";
import EmployeeDetails from "./pages/EmployeeDetails";
import EmployeeForm from "./pages/EmployeeForm";
import Settings from "./pages/Settings";
import Customers from "./pages/Customers";
import CustomerDetails from "./pages/CustomerDetails";
import CustomerForm from "./pages/CustomerForm";
import UserProfile from "./pages/UserProfile";
import UserManagement from "./pages/UserManagement";
import Tasks from "./pages/Tasks";
import PrecatoriosManagement from "./pages/PrecatoriosManagement";
// import PrecatoriosKanban from "./pages/PrecatoriosKanban"; // Não utilizado
import PrecatoriosLista from "./pages/PrecatoriosLista";
import PrecatoriosTable from "./pages/PrecatoriosTable";
import PrecatorioDetails from "./pages/PrecatorioDetails";
import Calendar from "./pages/Calendar";
import Automacao from "./pages/Automacao";
import AutomacaoList from "./pages/AutomacaoList";
import MigracaoBancoDados from "./pages/MigracaoBancoDados";
import Documents from "./pages/Documents";
import DocumentView from "./pages/DocumentView";
import DocumentEditorPage from "./pages/DocumentEditor";
import AccessDenied from "./pages/AccessDenied";
import KanbanSettings from "./pages/KanbanSettings";
import PrecatoriosKanbanNew from "./pages/PrecatoriosKanbanNew";
import PrecatoriosKanbanFixed from "./pages/PrecatoriosKanbanFixed";
import RolesManagement from "./pages/RolesManagement";
import { PermissionsAdmin } from "./pages/PermissionsAdmin";
import { PrecatorioFormTest } from "./tests/PrecatorioFormTest";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <ErrorBoundary>
          <BrowserRouter>
            <AuthProvider>
              <SessionManager />
              <SessionRecoveryManager />
              <PermissionsProvider>
                <Routes>
                {/* Rotas acessíveis sem autenticação */}
                <Route path="/login" element={<Login />} />
                <Route path="/acesso-negado" element={<AccessDenied />} />

                {/* Todas as outras rotas estão contidas no Index */}
                <Route path="/" element={<Index />}>
                  <Route index element={<Dashboard />} />
                  <Route path="dashboard" element={<Dashboard />} />
                  <Route path="employees" element={<EmployeeAnalysis />} />
                  <Route path="employees/:id" element={<EmployeeDetails />} />
                  <Route path="employees/new" element={<EmployeeForm />} />
                  <Route path="employees/:id/edit" element={<EmployeeForm />} />
                  <Route path="customers" element={<Customers />} />
                  <Route path="customers/new" element={<CustomerForm />} />
                  <Route path="customers/:id" element={<CustomerDetails />} />
                  <Route path="automacao" element={<Automacao />} />
                  <Route path="automacao-list" element={<AutomacaoList />} />
                  <Route path="automacoes" element={<AutomacaoList />} />
                  <Route path="automacao/:id" element={<Automacao />} />
                  <Route path="automacao/nova" element={<Automacao />} />
                  <Route path="profile" element={<UserProfile />} />
                  <Route path="settings" element={<Settings />} />
                  <Route path="users" element={<UserManagement />} />
                  <Route path="user-management" element={<UserManagement />} />
                  <Route path="usuario/:id" element={<UserProfile />} />
                  <Route path="tasks" element={<Tasks />} />
                  <Route path="tarefas" element={<Tasks />} />
                  <Route path="precatorios" element={<PrecatoriosKanbanFixed />} />
                  <Route path="precatorios-lista" element={<PrecatoriosLista />} />
                  <Route path="precatorios-rpv" element={<PrecatoriosLista />} />
                  <Route path="precatorios-table" element={<PrecatoriosTable />} />
                  <Route path="precatorios-management" element={<PrecatoriosManagement />} />
                  <Route path="precatorios/novo" element={<PrecatorioDetails />} />
                  <Route path="precatorios/:id" element={<PrecatorioDetails />} />
                  <Route path="precatorios-settings" element={<KanbanSettings />} />
                  <Route path="kanban-settings" element={<KanbanSettings />} />
                  <Route path="calendar" element={<Calendar />} />
                  <Route path="calendario" element={<Calendar />} />
                  <Route path="documents/novo" element={<DocumentEditorPage />} />
                  <Route path="documentos/novo" element={<DocumentEditorPage />} />
                  <Route path="documents/editar/:id" element={<DocumentEditorPage />} />
                  <Route path="documentos/editar/:id" element={<DocumentEditorPage />} />
                  <Route path="documents/:id" element={<DocumentView />} />
                  <Route path="documentos/:id" element={<DocumentView />} />
                  <Route path="documents" element={<Documents />} />
                  <Route path="documentos" element={<Documents />} />
                  <Route path="migracao-banco" element={<MigracaoBancoDados />} />
                  <Route path="roles" element={
                    <PermissionRoute action="manage_roles" resource="system" showMessage={true}>
                      <RolesManagement />
                    </PermissionRoute>
                  } />
                  <Route path="cargos" element={
                    <PermissionRoute action="manage_roles" resource="system" showMessage={true}>
                      <RolesManagement />
                    </PermissionRoute>
                  } />
                  <Route path="permissions" element={
                    <PermissionRoute action="gerenciar_permissoes" resource="system" showMessage={true}>
                      <PermissionsAdmin />
                    </PermissionRoute>
                  } />
                  <Route path="permissoes" element={
                    <PermissionRoute action="gerenciar_permissoes" resource="system" showMessage={true}>
                      <PermissionsAdmin />
                    </PermissionRoute>
                  } />
                  <Route path="test/precatorio-form" element={<PrecatorioFormTest />} />
                  {/* Página de diagnóstico removida e integrada à página de configurações */}
                </Route>

                {/* Redirecionar qualquer outra rota para o dashboard */}
                <Route path="*" element={<Navigate to="/dashboard" replace />} />
              </Routes>
              </PermissionsProvider>
            </AuthProvider>
          </BrowserRouter>
        </ErrorBoundary>
      </TooltipProvider>
    </ThemeProvider>
  </QueryClientProvider>
);

export default App;