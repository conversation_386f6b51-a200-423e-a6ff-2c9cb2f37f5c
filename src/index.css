@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --brand: 0 0% 9%;
    --brand-foreground: 0 0% 98%;

    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;

    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;

    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;

    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;

    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --brand: 0 0% 98%;
    --brand-foreground: 0 0% 9%;

    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;

    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;

    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;

    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;

    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;

    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Configuração de scroll para todas as páginas */
html, body, #root {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
  overflow: auto; /* Permitir scroll por padrão em todas as páginas */
  min-height: 100vh;
  min-width: 100vw;
}

/* Configuração específica para páginas sem scroll global */
body[data-page="precatorios"] #root,
body[data-page="calendar"] #root {
  overflow: hidden; /* Remover scroll para páginas específicas */
}

/* Componentes com scroll interno devem ter altura delimitada */
.overflow-auto {
  -webkit-overflow-scrolling: touch;
}

/* ScrollArea com altura específica para não causar scroll na página */
.h-\[calc\(100vh-180px\)\] {
  height: calc(100vh - 180px);
  height: calc(var(--vh, 1vh) * 100 - 180px);
}

/* Estilos específicos para o calendário e painéis */
.calendar-layout {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.calendar-content {
  flex: 1;
  overflow: hidden;
  display: grid;
}

.calendar-panel {
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Estilos para barras de scroll personalizadas */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(155, 155, 155, 0.5) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(155, 155, 155, 0.5);
  border-radius: 20px;
  border: transparent;
}

.dark .custom-scrollbar {
  scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2);
}

@layer utilities {
  /* Estilização da barra de rolagem */
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: #9ca3af;
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: #6b7280;
  }

  /* Modo escuro */
  .dark .scrollbar-thin::-webkit-scrollbar-thumb {
    background: #4b5563;
  }

  .dark .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: #6b7280;
  }
}

/* Estilos para os nós do fluxograma */
.diamond-node {
  clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
  background: #fff;
  border: 1px solid #ccc;
  will-change: transform;
}

.react-flow__node {
  border: 1px solid #ccc;
  border-radius: 4px;
  background: #fff;
  padding: 8px;
  box-shadow: 0 2px 4px #0001;
  will-change: transform;
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  -webkit-font-smoothing: subpixel-antialiased;
}

.react-flow__node:hover {
  box-shadow: 0 4px 8px #0002;
}

.react-flow__node.selected {
  border-color: #1a73e8;
}

.react-flow__edge-path {
  stroke: #555;
  stroke-width: 2;
  pointer-events: none;
}

.react-flow__edge.selected .react-flow__edge-path {
  stroke: #1a73e8;
  stroke-width: 3;
}

.react-flow__handle {
  width: 8px;
  height: 8px;
  background: #fff;
  border: 2px solid #555;
  transition: transform 0.1s ease;
}

.react-flow__handle:hover {
  transform: scale(1.2);
  background: #1a73e8;
  border-color: #1a73e8;
}

.react-flow__minimap {
  background: #fff;
  border: 1px solid #ccc;
}

.react-flow__controls {
  box-shadow: none;
  border: 1px solid #ccc;
  background: #fff;
}

.react-flow__controls button {
  background: #fff;
  border: none;
  border-bottom: 1px solid #ccc;
}

.react-flow__controls button:hover {
  background: #f5f5f5;
}

.dark .react-flow__node {
  background: #1a1a1a;
  border-color: #333;
  color: #fff;
}

.dark .react-flow__edge-path {
  stroke: #666;
}

.dark .react-flow__handle {
  background: #1a1a1a;
  border-color: #666;
}

.dark .react-flow__minimap,
.dark .react-flow__controls {
  background: #1a1a1a;
  border-color: #333;
}

.dark .react-flow__controls button {
  background: #1a1a1a;
  border-color: #333;
  color: #fff;
}

.dark .react-flow__controls button:hover {
  background: #2a2a2a;
}

/* Estilos para os diferentes tipos de nós */
.node-tarefa {
  @apply bg-blue-50 border-blue-200 dark:bg-blue-900 dark:border-blue-700;
}

.node-condicao {
  @apply bg-amber-50 border-amber-200 dark:bg-amber-900 dark:border-amber-700;
}

.node-inicioFim {
  @apply bg-green-50 border-green-200 dark:bg-green-900 dark:border-green-700;
}

.node-aprovacao {
  @apply bg-purple-50 border-purple-200 dark:bg-purple-900 dark:border-purple-700;
}

.node-notificacao {
  @apply bg-pink-50 border-pink-200 dark:bg-pink-900 dark:border-pink-700;
}

.node-integracao {
  @apply bg-cyan-50 border-cyan-200 dark:bg-cyan-900 dark:border-cyan-700;
}

/* Estilos para as conexões */
.react-flow__edge {
  @apply transition-all duration-200;
  pointer-events: none;
}

.react-flow__edge:hover {
  @apply cursor-pointer;
}

.react-flow__edge.selected {
  @apply stroke-[4];
}

.react-flow__edge-text {
  @apply fill-neutral-600 dark:fill-neutral-400 text-sm font-medium;
}

.react-flow__edge-textbg {
  @apply fill-white dark:fill-neutral-800;
}

/* Estilos para os handles de conexão */
.react-flow__handle-top {
  @apply -top-1;
}

.react-flow__handle-bottom {
  @apply -bottom-1;
}

.react-flow__handle-left {
  @apply -left-1;
}

.react-flow__handle-right {
  @apply -right-1;
}

.react-flow__node:hover .react-flow__handle {
  @apply opacity-100;
}

.react-flow__handle:hover {
  @apply scale-150;
}

.react-flow__handle-connecting {
  @apply opacity-100 scale-150;
}

.react-flow__connection-path {
  @apply stroke-[3];
}

.react-flow__edge-text {
  @apply fill-neutral-600 dark:fill-neutral-400 text-sm font-medium;
}

.react-flow__edge-textbg {
  @apply fill-white dark:fill-neutral-800;
}

/* Estilos para os controles e minimapa */
.react-flow__controls {
  @apply shadow-lg border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800 rounded-lg overflow-hidden;
}

.react-flow__controls-button {
  @apply border-neutral-200 dark:border-neutral-700 hover:bg-neutral-50 dark:hover:bg-neutral-700 transition-colors;
}

.react-flow__minimap {
  @apply shadow-lg border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800 rounded-lg overflow-hidden;
}

.react-flow__panel {
  @apply shadow-none;
  z-index: 5;
}

.react-flow__panel.top-left {
  margin: max(16px, 2vh) 0 0 max(16px, 2vw);
}

.react-flow__panel.top-right {
  margin: max(16px, 2vh) max(16px, 2vw) 0 0;
}

.react-flow__panel.top-center {
  margin: max(16px, 2vh) auto 0;
  width: fit-content;
  max-width: 90vw;
}

.react-flow__minimap {
  max-width: min(200px, 20vw);
  max-height: min(150px, 20vh);
  background: transparent !important;
  border: none !important;
  border-radius: 8px;
  overflow: hidden;
}

.react-flow__controls {
  @apply shadow-lg border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800 rounded-lg;
  padding: min(4px, 1vw);
}

.react-flow__controls button {
  @apply border-neutral-200 dark:border-neutral-700 hover:bg-neutral-50 dark:hover:bg-neutral-700 transition-colors;
  width: min(32px, 8vw);
  height: min(32px, 8vw);
  display: flex;
  align-items: center;
  justify-content: center;
}

.react-flow__edge:hover .edge-delete-button {
  @apply opacity-100;
}

.edge-delete-button {
  @apply transition-opacity duration-200;
  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
  pointer-events: all;
}

/* Responsividade para telas menores */
@media screen and (max-width: 768px) {
  .react-flow__panel.top-center {
    margin-top: 5px;
  }

  .react-flow__panel.top-left,
  .react-flow__panel.top-right {
    transform: scale(0.9);
  }

  .react-flow__controls button {
    width: 28px;
    height: 28px;
  }

  .react-flow__minimap {
    max-width: 150px;
    max-height: 100px;
  }
}

@media screen and (max-width: 480px) {
  .react-flow__panel.top-center {
    margin-top: 2px;
  }

  .react-flow__panel.top-left,
  .react-flow__panel.top-right {
    transform: scale(0.8);
  }

  .react-flow__controls button {
    width: 24px;
    height: 24px;
  }

  .react-flow__minimap {
    max-width: 100px;
    max-height: 80px;
  }
}

/* Animações para os nós */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

.node-active {
  animation: pulse 2s infinite;
}

/* Estilos para o background */
.react-flow__background {
  @apply bg-white dark:bg-neutral-900;
}

.react-flow__background-pattern {
  @apply stroke-neutral-200 dark:stroke-neutral-800;
}

/* Estilos para seleção */
.react-flow__node.selected {
  @apply ring-2 ring-blue-500 ring-offset-2;
}

/* Estilos para arrastar e soltar */
.react-flow__node.dragging {
  cursor: grabbing !important;
  z-index: 1000;
}

.react-flow__pane {
  cursor: grab;
  will-change: transform;
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  perspective: 1000px;
  -webkit-font-smoothing: subpixel-antialiased;
}

.react-flow__pane.dragging {
  cursor: grabbing;
}

/* Ajustar posição do painel de controles */
.react-flow__panel.bottom-center {
  @apply mb-32 !important;
}

/* Melhorar aparência dos botões */
.react-flow__panel button {
  @apply transition-all duration-200;
}

.react-flow__panel button:hover {
  @apply transform scale-105;
}

/* Estilo para separador vertical */
.react-flow__panel .vertical-separator {
  @apply mx-2 h-8 w-px bg-neutral-200 dark:bg-neutral-700;
}

/* Responsividade para telas menores */
@media screen and (max-width: 640px) {
  .react-flow__panel.bottom-center {
    @apply mb-24 !important;
  }

  .react-flow__panel button {
    @apply p-2;
  }
}

/* Melhorar feedback visual durante reorganização */
.react-flow__node {
  @apply transition-all duration-300 ease-in-out;
}

.react-flow__edge {
  @apply transition-all duration-300 ease-in-out;
}

/* Ajustar posição do MiniMap */
.react-flow__minimap {
  @apply !top-4 !right-4 bg-transparent border-none rounded-lg overflow-hidden;
}

/* Melhorar aparência dos painéis */
.react-flow__panel {
  @apply z-50;
}

.react-flow__panel > div {
  @apply shadow-lg;
}

/* Estilo para os botões do painel */
.react-flow__panel button {
  @apply border-neutral-200 dark:border-neutral-700 hover:bg-neutral-50 dark:hover:bg-neutral-700;
}

/* Melhorar visibilidade no modo escuro */
.dark .react-flow__panel button {
  @apply border-neutral-700 bg-neutral-800 text-neutral-200;
}

.dark .react-flow__panel button:hover {
  @apply bg-neutral-700;
}

/* Animações suaves para reorganização */
.react-flow__viewport {
  @apply transition-transform duration-300 ease-in-out;
  will-change: transform;
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  perspective: 1000px;
  transition: none !important;
}

/* Melhorar aparência das conexões */
.react-flow__edge-path {
  @apply transition-all duration-300;
}

.react-flow__handle {
  @apply transition-all duration-200;
  transition: transform 0.1s ease, background-color 0.1s ease, border-color 0.1s ease;
}

/* Ajustar espaçamento dos handles */
.react-flow__handle-top {
  @apply -top-3;
}

.react-flow__handle-bottom {
  @apply -bottom-3;
}

/* Estilo para o botão de excluir na linha */
.edge-delete-button {
  @apply opacity-0 transition-opacity duration-200;
  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
}

.react-flow__edge:hover .edge-delete-button {
  @apply opacity-100;
}

/* Otimizações de performance */
.react-flow__node * {
  pointer-events: none;
}

.react-flow__node button,
.react-flow__node a,
.react-flow__handle {
  pointer-events: all;
}

/* Melhorar performance de animações */
.react-flow__edge {
  pointer-events: none;
}

.react-flow__edge:hover {
  @apply cursor-pointer;
}

/* Otimizar transições */
.react-flow__node {
  transition: box-shadow 0.2s ease;
}

.react-flow__handle {
  transition: transform 0.1s ease, background-color 0.1s ease, border-color 0.1s ease;
}

.react-flow__container {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  will-change: transform;
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  perspective: 1000px;
  touch-action: none;
}

.react-flow__renderer {
  contain: strict;
}

.react-flow__selection {
  will-change: transform;
  transform: translate3d(0, 0, 0);
}

.react-flow__edges {
  pointer-events: none;
  will-change: transform;
  transform: translate3d(0, 0, 0);
}

.kanban-container {
  scroll-behavior: smooth;
  transition: cursor 0.2s;
}

.kanban-container::-webkit-scrollbar {
  height: 8px;
}

.kanban-container::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

.kanban-container::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

.kanban-container::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* Remover o scroll-behavior quando estiver arrastando para tornar o movimento mais direto */
.kanban-container.is-dragging {
  scroll-behavior: auto;
}

/* Estilo global para quando estiver arrastando um card */
body.dragging {
  cursor: grabbing !important;
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  touch-action: none !important;
}

/* Melhorar a experiência de arrasto no Kanban */
.precatorio-card[data-draggable="card"] {
  cursor: grab;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  will-change: transform;
}

.precatorio-card[data-draggable="card"]:active {
  cursor: grabbing;
  z-index: 100;
}

/* Estilo para quando estiver arrastando o scroll lateral */
body.scrolling {
  cursor: grabbing !important;
  user-select: none !important;
}

/* Melhorar a experiência de arrasto do scroll lateral */
.kanban-container {
  cursor: grab;
  scroll-snap-type: x proximity;
}

.kanban-container:active {
  cursor: grabbing;
}

/* Melhorar a experiência de arrasto */
.precatorio-card {
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

/* Estilo para os cards */
.precatorio-card {
  transition: transform 0.2s, box-shadow 0.2s;
}

/* Melhorias de design e animações */

/* Efeito de hover para cards */
.precatorio-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Efeito de clique para cards */
.precatorio-card:active:not(.dragging) {
  transform: scale(0.98);
  transition: transform 0.1s ease;
}

/* Adicionar efeito de pulso para indicar que é clicável */
.precatorio-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 0.5rem;
  box-shadow: 0 0 0 0 rgba(var(--primary) / 0.3);
  opacity: 0;
  transition: all 0.3s ease;
  pointer-events: none; /* Importante: permite que os cliques passem pelo pseudo-elemento */
}

.precatorio-card:hover::after {
  opacity: 1;
  animation: pulse-border 2s infinite;
}

/* Cursor personalizado para indicar que é clicável */
.precatorio-card {
  cursor: pointer !important; /* Forçar cursor pointer por padrão */
}

.precatorio-card.dragging {
  cursor: grabbing !important; /* Forçar cursor grabbing quando arrastando */
}

/* Estilos para o Kanban melhorado */
.column-highlight {
  background-color: rgba(var(--primary) / 0.05);
  box-shadow: 0 0 0 2px rgba(var(--primary) / 0.2);
  transition: all 0.3s ease;
}

/* Animação para o card sendo arrastado */
.card-dragging {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
  transform: scale(1.02);
  z-index: 100;
}

/* Estilo para o toast do Kanban */
.kanban-toast {
  background-color: rgba(var(--primary) / 0.9);
  backdrop-filter: blur(8px);
  border-radius: 8px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
  color: white;
  font-weight: 500;
}

/* Animação de entrada para o card */
@keyframes card-enter {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.precatorio-card {
  animation: card-enter 0.3s ease-out;
}

/* Animação para o card sendo movido */
@keyframes card-move {
  0% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
  100% { transform: translateY(0); }
}

.precatorio-card.moving {
  animation: card-move 0.5s ease-in-out;
}

/* Melhorar a visualização das colunas do Kanban */
[data-column-id] {
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
  border-radius: 8px;
}

/* Efeito de hover para as colunas */
[data-column-id]:hover {
  background-color: rgba(var(--primary) / 0.02);
}

/* Adicionar feedback visual para o modo de arrasto */
.precatorio-card[data-drag-mode="true"]::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 2px dashed rgba(var(--primary) / 0.5);
  border-radius: 0.5rem;
  pointer-events: none;
  animation: pulse-border-drag 1s infinite;
}

@keyframes pulse-border-drag {
  0% {
    border-color: rgba(var(--primary) / 0.3);
  }
  50% {
    border-color: rgba(var(--primary) / 0.7);
  }
  100% {
    border-color: rgba(var(--primary) / 0.3);
  }
}

/* Estilo para o botão de arrasto */
.drag-handle {
  opacity: 0.5;
  transition: all 0.2s ease;
}

.precatorio-card:hover .drag-handle {
  opacity: 0.8;
}

.drag-handle:hover {
  opacity: 1 !important;
  background-color: rgba(var(--primary) / 0.2);
  transform: scale(1.1);
}

/* Adicionar tooltip visual para o botão de arrasto */
.drag-handle::after {
  content: 'Arrastar';
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%) scale(0);
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  opacity: 0;
  transition: all 0.2s ease;
  pointer-events: none;
}

.drag-handle:hover::after {
  opacity: 1;
  transform: translateX(-50%) scale(1);
}

/* Adicionar ícone de clique no canto superior direito */
.precatorio-card:not(.dragging)::before {
  content: '';
  position: absolute;
  top: 8px;
  right: 8px;
  width: 16px;
  height: 16px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z'%3E%3C/path%3E%3Cpolyline points='14 2 14 8 20 8'%3E%3C/polyline%3E%3Cline x1='16' y1='13' x2='8' y2='13'%3E%3C/line%3E%3Cline x1='16' y1='17' x2='8' y2='17'%3E%3C/line%3E%3Cpolyline points='10 9 9 9 8 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  opacity: 0.3;
  transition: opacity 0.2s ease;
  pointer-events: none; /* Importante: permite que os cliques passem pelo pseudo-elemento */
}

.precatorio-card:hover:not(.dragging)::before {
  opacity: 0.7;
}

@keyframes pulse-border {
  0% {
    box-shadow: 0 0 0 0 rgba(var(--primary) / 0.3);
  }
  70% {
    box-shadow: 0 0 0 4px rgba(var(--primary) / 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(var(--primary) / 0);
  }
}

/* Animações de transição para elementos da interface */
.card-transition {
  transition: all 0.3s ease;
}

.card-transition:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
}

/* Efeito de hover para botões */
button:not(.no-animation):hover {
  transform: translateY(-1px);
  transition: transform 0.2s ease;
}

/* Animação de entrada para elementos */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Efeito de pulsação para notificações */
.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* Melhorias de scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.5);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.7);
}

/* Efeito de glassmorphism para elementos flutuantes */
.glass-effect {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.dark .glass-effect {
  background: rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.1);
}