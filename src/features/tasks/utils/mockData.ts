import { Participante, Tarefa, TaskTemplate, TarefaRecorrente } from "../types";

// Dados iniciais de participantes
export const participantesIniciais: Participante[] = [
  { id: "1", nome: "<PERSON> Silva", avatar: "/avatars/ana.jpg" },
  { id: "2", nome: "<PERSON>", avatar: "/avatars/carlos.jpg" },
  { id: "3", nome: "Maria Costa", avatar: "/avatars/maria.jpg" },
];

// Dados iniciais de tarefas
export const tarefasIniciais: Tarefa[] = [
  {
    id: "1",
    titulo: "Análise de Contrato",
    descricao: "Revisar termos e condições do contrato de prestação de serviços",
    status: "em_andamento",
    prioridade: "alta",
    responsavel: participantesIniciais[0],
    participantes: [participantesIniciais[1]],
    prazo: "2024-03-25",
    tags: ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"],
    progresso: 60,
    subtarefas: [
      { id: "1.1", titulo: "Revisar cláusulas", concluida: true, prazo: "2024-03-23" },
      { id: "1.2", titulo: "Atualizar termos", concluida: false, prazo: "2024-03-24" }
    ],
    dataCriacao: "2024-03-20",
    comentarios: [],
    processoVinculado: "Contrato de Prestação de Serviços"
  },
  {
    id: "2",
    titulo: "Preparação de Documentos",
    descricao: "Organizar documentação para processo judicial",
    status: "pendente",
    prioridade: "media",
    responsavel: participantesIniciais[1],
    participantes: [participantesIniciais[2]],
    prazo: "2024-03-28",
    tags: ["Documentação", "Processo"],
    progresso: 30,
    subtarefas: [
      { id: "2.1", titulo: "Separar documentos", concluida: false, prazo: "2024-03-26" }
    ],
    dataCriacao: "2024-03-21",
    comentarios: [],
    processoVinculado: "Processo Judicial"
  },
  {
    id: "3",
    titulo: "Audiência de Conciliação",
    descricao: "Preparar para audiência de conciliação do caso Silva vs Empresa XYZ",
    status: "pendente",
    prioridade: "alta",
    responsavel: participantesIniciais[2],
    participantes: [participantesIniciais[0], participantesIniciais[1]],
    prazo: "2024-03-26",
    tags: ["Audiência", "Urgente"],
    progresso: 45,
    subtarefas: [
      { id: "3.1", titulo: "Revisar processo", concluida: true, prazo: "2024-03-25" },
      { id: "3.2", titulo: "Preparar argumentação", concluida: false, prazo: "2024-03-25" }
    ],
    dataCriacao: "2024-03-22",
    comentarios: [],
    processoVinculado: "Processo nº 2024/789"
  },
  {
    id: "4",
    titulo: "Elaboração de Recurso",
    descricao: "Preparar recurso de apelação para o caso Costa",
    status: "em_andamento",
    prioridade: "alta",
    responsavel: participantesIniciais[0],
    participantes: [participantesIniciais[2]],
    prazo: "2024-03-27",
    tags: ["Recurso", "Processo"],
    progresso: 20,
    subtarefas: [
      { id: "4.1", titulo: "Analisar decisão", concluida: true, prazo: "2024-03-26" },
      { id: "4.2", titulo: "Pesquisar jurisprudência", concluida: false, prazo: "2024-03-26" }
    ],
    dataCriacao: "2024-03-23",
    comentarios: [],
    processoVinculado: "Processo nº 2024/456"
  },
  {
    id: "5",
    titulo: "Reunião com Cliente VIP",
    descricao: "Reunião estratégica com Empresa ABC sobre novos contratos",
    status: "pendente",
    prioridade: "media",
    responsavel: participantesIniciais[1],
    participantes: [participantesIniciais[0]],
    prazo: "2024-03-29",
    tags: ["Reunião", "Cliente VIP"],
    progresso: 0,
    subtarefas: [
      { id: "5.1", titulo: "Preparar apresentação", concluida: false, prazo: "2024-03-28" }
    ],
    dataCriacao: "2024-03-24",
    comentarios: [],
    processoVinculado: "Contrato Comercial"
  },
  {
    id: "6",
    titulo: "Análise de Compliance",
    descricao: "Verificar conformidade dos procedimentos internos",
    status: "pendente",
    prioridade: "baixa",
    responsavel: participantesIniciais[2],
    participantes: [],
    prazo: "2024-03-30",
    tags: ["Compliance", "Interno"],
    progresso: 10,
    subtarefas: [
      { id: "6.1", titulo: "Revisar políticas", concluida: false, prazo: "2024-03-29" }
    ],
    dataCriacao: "2024-03-24",
    comentarios: [],
    processoVinculado: "Procedimentos Internos"
  },
  {
    id: "7",
    titulo: "Petição Inicial - Caso Santos",
    descricao: "Elaborar petição inicial para novo processo trabalhista",
    status: "em_andamento",
    prioridade: "media",
    responsavel: participantesIniciais[0],
    participantes: [participantesIniciais[1]],
    prazo: "2024-03-31",
    tags: ["Trabalhista", "Petição"],
    progresso: 35,
    subtarefas: [
      { id: "7.1", titulo: "Coletar documentos", concluida: true, prazo: "2024-03-29" },
      { id: "7.2", titulo: "Redigir petição", concluida: false, prazo: "2024-03-30" }
    ],
    dataCriacao: "2024-03-25",
    comentarios: [],
    processoVinculado: "Novo Processo Trabalhista"
  },
  {
    id: "8",
    titulo: "Due Diligence Empresarial",
    descricao: "Conduzir due diligence para aquisição empresarial",
    status: "pendente",
    prioridade: "alta",
    responsavel: participantesIniciais[1],
    participantes: [participantesIniciais[0], participantesIniciais[2]],
    prazo: "2024-04-01",
    tags: ["Due Diligence", "Empresarial"],
    progresso: 15,
    subtarefas: [
      { id: "8.1", titulo: "Análise documental", concluida: false, prazo: "2024-03-31" }
    ],
    dataCriacao: "2024-03-25",
    comentarios: [],
    processoVinculado: "Processo de Aquisição"
  }
];

// Tarefas recorrentes
export const tarefasRecorrentes: TarefaRecorrente[] = [
  {
    ...tarefasIniciais[0],
    id: "recorrente1",
    titulo: "Verificar e-mails do escritório",
    descricao: "Verificar e responder e-mails importantes do escritório",
    status: "pendente",
    prioridade: "alta",
    prazo: new Date().toISOString(),
    recorrencia: {
      tipo: "diaria",
      horario: "09:00",
      proximaOcorrencia: new Date().toISOString()
    }
  },
  {
    ...tarefasIniciais[0],
    id: "recorrente2",
    titulo: "Atualizar relatório diário",
    descricao: "Atualizar o relatório de atividades do dia",
    status: "pendente",
    prioridade: "media",
    prazo: new Date().toISOString(),
    recorrencia: {
      tipo: "diaria",
      horario: "17:00",
      proximaOcorrencia: new Date().toISOString()
    }
  },
  {
    ...tarefasIniciais[0],
    id: "recorrente3",
    titulo: "Reunião de equipe semanal",
    descricao: "Reunião de alinhamento com a equipe",
    status: "pendente",
    prioridade: "alta",
    prazo: new Date().toISOString(),
    recorrencia: {
      tipo: "semanal",
      diasDaSemana: [1], // Segunda-feira
      horario: "10:00",
      proximaOcorrencia: new Date().toISOString()
    }
  }
];

// Array de templates de tarefas
export const taskTemplatesIniciais: TaskTemplate[] = [
  {
    id: "template1",
    name: "Petição Inicial",
    description: "Template para criação de nova petição inicial",
    category: "Processos",
    defaultPriority: "alta",
    defaultTags: ["Petição", "Documento"],
    defaultChecklist: [
      { text: "Verificar documentação do cliente", required: true },
      { text: "Analisar jurisprudência", required: true },
      { text: "Redigir petição", required: true },
      { text: "Revisar ortografia", required: true },
      { text: "Anexar documentos", required: true }
    ],
    requiredDocuments: [
      { name: "Documentos Pessoais", type: "pdf", required: true },
      { name: "Procuração", type: "pdf", required: true },
      { name: "Comprovantes", type: "pdf", required: true }
    ]
  },
  {
    id: "template2",
    name: "Preparação para Audiência",
    description: "Template para preparação de audiência",
    category: "Audiências",
    defaultPriority: "alta",
    defaultTags: ["Audiência", "Preparação"],
    defaultChecklist: [
      { text: "Revisar processo", required: true },
      { text: "Preparar argumentação", required: true },
      { text: "Organizar documentos", required: true },
      { text: "Confirmar com cliente", required: true },
      { text: "Verificar local e horário", required: true }
    ],
    requiredDocuments: [
      { name: "Petição Inicial", type: "pdf", required: true },
      { name: "Documentos do Processo", type: "pdf", required: true }
    ]
  }
];

// Estatísticas expandidas para tarefas
export const estatisticasTarefas = [
  {
    titulo: "Total de Tarefas",
    valor: "45",
    variacao: "+8",
    descricao: "Últimos 30 dias",
    icone: "FileText",
    progresso: 75,
    detalhes: {
      concluidas: 32,
      emAndamento: 10,
      atrasadas: 3
    }
  },
  {
    titulo: "Em Andamento",
    valor: "12",
    variacao: "+3",
    descricao: "Tarefas ativas",
    icone: "Activity",
    progresso: 60,
    detalhes: {
      noPrazo: 8,
      atrasadas: 4
    }
  },
  {
    titulo: "Taxa de Conclusão",
    valor: "85%",
    variacao: "+5%",
    descricao: "Meta mensal",
    icone: "Target",
    progresso: 85,
    detalhes: {
      metaAtingida: true,
      tendencia: "crescente"
    }
  },
  {
    titulo: "Produtividade",
    valor: "92%",
    variacao: "+2%",
    descricao: "Eficiência",
    icone: "TrendingUp",
    progresso: 92,
    detalhes: {
      tarefasPorDia: 5.2,
      tempoMedioConclusao: "2.3 dias"
    }
  }
]; 