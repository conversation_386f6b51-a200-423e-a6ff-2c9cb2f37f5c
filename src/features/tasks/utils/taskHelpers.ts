import { Tarefa, TaskGroup, TaskMetrics } from "../types";
import { Circle, Clock, CheckCircle2, AlertCircle, User, List } from "lucide-react";

// Sistema de priorização automática
export const calculateTaskPriority = (task: Tarefa) => {
  let score = 0;
  
  // Fatores de priorização
  const deadline = new Date(task.prazo);
  const today = new Date();
  const daysUntilDeadline = Math.ceil((deadline.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
  
  // Pontuação baseada no prazo
  if (daysUntilDeadline <= 1) score += 5;
  else if (daysUntilDeadline <= 3) score += 4;
  else if (daysUntilDeadline <= 7) score += 3;
  else if (daysUntilDeadline <= 14) score += 2;
  else score += 1;
  
  // Pontuação baseada nas tags
  if (task.tags.includes("Urgente")) score += 3;
  if (task.tags.includes("Cliente VIP")) score += 2;
  
  // Pontuação baseada no progresso
  if (task.progresso < 20) score += 2;
  if (task.subtarefas.length > 0) {
    const completedSubtasks = task.subtarefas.filter(s => s.concluida).length;
    if (completedSubtasks === 0) score += 1;
  }
  
  // Determina a prioridade baseada na pontuação
  if (score >= 7) return "alta";
  if (score >= 4) return "media";
  return "baixa";
};

// Cálculo de métricas de tarefas
export const calculateTaskMetrics = (tarefas: Tarefa[]): TaskMetrics => {
  const hoje = new Date();
  return {
    total: tarefas.length,
    concluidas: tarefas.filter(t => t.status === "concluida").length,
    emAndamento: tarefas.filter(t => t.status === "em_andamento").length,
    atrasadas: tarefas.filter(t => new Date(t.prazo) < hoje && t.status !== "concluida").length,
    proximasDoPrazo: tarefas.filter(t => {
      const prazo = new Date(t.prazo);
      const diasRestantes = Math.ceil((prazo.getTime() - hoje.getTime()) / (1000 * 60 * 60 * 24));
      return diasRestantes <= 3 && diasRestantes >= 0 && t.status !== "concluida";
    }).length,
    semResponsavel: tarefas.filter(t => !t.responsavel).length,
    prioridadeAlta: tarefas.filter(t => t.prioridade === "alta" && t.status !== "concluida").length
  };
};

// Agrupamento de tarefas
export const groupTasks = (tarefas: Tarefa[], groupBy: string): TaskGroup[] => {
  switch (groupBy) {
    case "status":
      return [
        {
          id: "status-pendente",
          title: "Pendentes",
          tasks: tarefas.filter(t => t.status === "pendente"),
          icon: "circle",
          color: "text-yellow-500"
        },
        {
          id: "status-em_andamento",
          title: "Em Andamento",
          tasks: tarefas.filter(t => t.status === "em_andamento"),
          icon: "clock",
          color: "text-blue-500"
        },
        {
          id: "status-concluida",
          title: "Concluídas",
          tasks: tarefas.filter(t => t.status === "concluida"),
          icon: "check-circle",
          color: "text-green-500"
        }
      ];
    case "priority":
      return [
        {
          id: "prioridade-alta",
          title: "Alta Prioridade",
          tasks: tarefas.filter(t => t.prioridade === "alta"),
          icon: "alert-circle",
          color: "text-red-500"
        },
        {
          id: "prioridade-media",
          title: "Média Prioridade",
          tasks: tarefas.filter(t => t.prioridade === "media"),
          icon: "alert-circle",
          color: "text-yellow-500"
        },
        {
          id: "prioridade-baixa",
          title: "Baixa Prioridade",
          tasks: tarefas.filter(t => t.prioridade === "baixa"),
          icon: "alert-circle",
          color: "text-green-500"
        }
      ];
    case "assignee":
      const porResponsavel = tarefas.reduce((acc, task) => {
        const key = task.responsavel?.nome || "Sem Responsável";
        if (!acc[key]) {
          acc[key] = [];
        }
        acc[key].push(task);
        return acc;
      }, {} as Record<string, Tarefa[]>);

      return Object.entries(porResponsavel).map(([nome, tasks]) => ({
        id: `responsavel-${nome.replace(/\s+/g, '-').toLowerCase()}`,
        title: nome,
        tasks,
        icon: "user",
        color: "text-purple-500"
      }));
    default:
      return [{
        id: "todas",
        title: "Todas as Tarefas",
        tasks: tarefas,
        icon: "list",
        color: "text-neutral-500"
      }];
  }
}; 