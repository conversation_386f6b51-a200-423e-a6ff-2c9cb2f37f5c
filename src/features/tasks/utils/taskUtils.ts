import { CheckCircle2, Clock, AlertCircle, Flag } from "lucide-react";
import { 
  Tarefa, 
  TaskGroup, 
  TaskMetrics 
} from "../types";

// Calcula a prioridade da tarefa com base em diversos fatores
export const calculateTaskPriority = (task: Tarefa) => {
  const today = new Date();
  const taskDeadline = new Date(task.prazo);
  const daysDifference = Math.floor((taskDeadline.getTime() - today.getTime()) / (1000 * 3600 * 24));
  
  // Prioridade alta se estiver atrasada ou com menos de 2 dias para o prazo
  if (daysDifference < 0 || (daysDifference <= 2 && task.status !== "concluida")) {
    return "alta";
  }
  
  // Prioridade média se estiver com menos de 7 dias para o prazo
  if (daysDifference <= 7 && task.status !== "concluida") {
    return "media";
  }
  
  // Considera a prioridade já definida na tarefa
  return task.prioridade;
};

// Calcula métricas para as tarefas
export const calculateTaskMetrics = (tarefas: Tarefa[]): TaskMetrics => {
  const hoje = new Date();
  
  return {
    total: tarefas.length,
    concluidas: tarefas.filter(t => t.status === "concluida").length,
    emAndamento: tarefas.filter(t => t.status === "em_andamento").length,
    atrasadas: tarefas.filter(t => new Date(t.prazo) < hoje && t.status !== "concluida").length,
    proximasDoPrazo: tarefas.filter(t => {
      const prazo = new Date(t.prazo);
      const diffDias = Math.ceil((prazo.getTime() - hoje.getTime()) / (1000 * 60 * 60 * 24));
      return diffDias >= 0 && diffDias <= 3 && t.status !== "concluida";
    }).length,
    semResponsavel: tarefas.filter(t => !t.responsavel || !t.responsavel.id).length,
    prioridadeAlta: tarefas.filter(t => t.prioridade === "alta" && t.status !== "concluida").length
  };
};

// Agrupa tarefas por diferentes critérios
export const groupTasks = (tarefas: Tarefa[], groupBy: string): TaskGroup[] => {
  const hoje = new Date();
  
  switch (groupBy) {
    case "status":
      return [
        {
          title: "Pendentes",
          tasks: tarefas.filter(task => task.status === "pendente"),
          icon: <AlertCircle size={18} />,
          color: "#f97316"
        },
        {
          title: "Em Andamento",
          tasks: tarefas.filter(task => task.status === "em_andamento"),
          icon: <Clock size={18} />,
          color: "#3b82f6"
        },
        {
          title: "Concluídas",
          tasks: tarefas.filter(task => task.status === "concluida"),
          icon: <CheckCircle2 size={18} />,
          color: "#22c55e"
        }
      ];
      
    case "prioridade":
      return [
        {
          title: "Alta Prioridade",
          tasks: tarefas.filter(task => task.prioridade === "alta"),
          icon: <Flag size={18} />,
          color: "#ef4444"
        },
        {
          title: "Média Prioridade",
          tasks: tarefas.filter(task => task.prioridade === "media"),
          icon: <Flag size={18} />,
          color: "#f97316"
        },
        {
          title: "Baixa Prioridade",
          tasks: tarefas.filter(task => task.prioridade === "baixa"),
          icon: <Flag size={18} />,
          color: "#3b82f6"
        }
      ];
      
    case "prazo":
      return [
        {
          title: "Atrasadas",
          tasks: tarefas.filter(task => 
            new Date(task.prazo) < hoje && task.status !== "concluida"
          ),
          icon: <AlertCircle size={18} />,
          color: "#ef4444"
        },
        {
          title: "Próximas",
          tasks: tarefas.filter(task => {
            const prazo = new Date(task.prazo);
            const diffDias = Math.ceil((prazo.getTime() - hoje.getTime()) / (1000 * 60 * 60 * 24));
            return diffDias >= 0 && diffDias <= 7 && task.status !== "concluida";
          }),
          icon: <Clock size={18} />,
          color: "#f97316"
        },
        {
          title: "Futuras",
          tasks: tarefas.filter(task => {
            const prazo = new Date(task.prazo);
            const diffDias = Math.ceil((prazo.getTime() - hoje.getTime()) / (1000 * 60 * 60 * 24));
            return diffDias > 7 && task.status !== "concluida";
          }),
          icon: <Clock size={18} />,
          color: "#3b82f6"
        }
      ];
      
    default:
      return [];
  }
};

// Formata data para exibição
export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });
};

// Calcula status de prazo de uma tarefa
export const getDeadlineStatus = (prazo: string): "atrasada" | "proxima" | "futura" => {
  const hoje = new Date();
  const dataPrazo = new Date(prazo);
  const diffDias = Math.ceil((dataPrazo.getTime() - hoje.getTime()) / (1000 * 60 * 60 * 24));
  
  if (diffDias < 0) return "atrasada";
  if (diffDias <= 3) return "proxima";
  return "futura";
};

// Formata o resumo de subtarefas
export const formatSubtaskSummary = (subtarefas: Subtarefa[]): string => {
  if (!subtarefas || subtarefas.length === 0) return "Sem subtarefas";
  
  const concluidas = subtarefas.filter(sub => sub.concluida).length;
  return `${concluidas}/${subtarefas.length} concluídas`;
};

// Exportadores para diferentes formatos
export const exportUtils = {
  prepareDataForExport: (tarefas: Tarefa[]) => {
    return tarefas.map(tarefa => ({
      ID: tarefa.id,
      Título: tarefa.titulo,
      Descrição: tarefa.descricao,
      Status: tarefa.status,
      Prioridade: tarefa.prioridade,
      Responsável: tarefa.responsavel?.nome || 'Não atribuído',
      Prazo: formatDate(tarefa.prazo),
      "Data de Criação": formatDate(tarefa.dataCriacao),
      Progresso: `${tarefa.progresso}%`,
      Tags: tarefa.tags.join(', '),
      "Número de Subtarefas": tarefa.subtarefas.length,
      "Processo Vinculado": tarefa.processoVinculado || 'Nenhum'
    }));
  }
}; 