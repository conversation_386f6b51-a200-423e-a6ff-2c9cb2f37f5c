import { useState } from "react";
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON>, 
  Edit3, 
  MoreHorizontal, 
  Tag, 
  Trash2,
  UserCircle,
  Check,
  X
} from "lucide-react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Progress } from "@/components/ui/progress";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Tarefa } from "../types";
import { formatDate, getDeadlineStatus, formatSubtaskSummary } from "../utils/taskUtils";

interface SortConfig {
  key: keyof Tarefa | "responsavel.nome" | null;
  direction: "asc" | "desc";
}

interface ListViewProps {
  tasks: Tarefa[];
  onTaskClick: (task: Tarefa) => void;
  onStatusChange: (taskId: string, newStatus: "pendente" | "em_andamento" | "concluida") => void;
  onEdit: (taskId: string) => void;
  onDelete: (taskId: string) => void;
}

export function ListView({ tasks, onTaskClick, onStatusChange, onEdit, onDelete }: ListViewProps) {
  const [sortConfig, setSortConfig] = useState<SortConfig>({ key: null, direction: "asc" });

  const requestSort = (key: keyof Tarefa | "responsavel.nome") => {
    let direction: "asc" | "desc" = "asc";
    if (sortConfig.key === key && sortConfig.direction === "asc") {
      direction = "desc";
    }
    setSortConfig({ key, direction });
  };

  const getSortedTasks = () => {
    const sortableTasks = [...tasks];
    if (sortConfig.key === null) return sortableTasks;

    return sortableTasks.sort((a, b) => {
      if (sortConfig.key === "responsavel.nome") {
        const aName = a.responsavel?.nome || "";
        const bName = b.responsavel?.nome || "";
        if (aName < bName) return sortConfig.direction === "asc" ? -1 : 1;
        if (aName > bName) return sortConfig.direction === "asc" ? 1 : -1;
        return 0;
      }
      
      const key = sortConfig.key as keyof Tarefa;
      if (a[key] < b[key]) return sortConfig.direction === "asc" ? -1 : 1;
      if (a[key] > b[key]) return sortConfig.direction === "asc" ? 1 : -1;
      return 0;
    });
  };

  const getSortIcon = (key: keyof Tarefa | "responsavel.nome") => {
    if (sortConfig.key !== key) return null;
    return sortConfig.direction === "asc" ? <ArrowUp className="h-3 w-3 ml-1" /> : <ArrowDown className="h-3 w-3 ml-1" />;
  };

  const sortedTasks = getSortedTasks();

  const statusColor = {
    pendente: "text-orange-500 bg-orange-100 dark:bg-orange-900/20 dark:text-orange-300",
    em_andamento: "text-blue-500 bg-blue-100 dark:bg-blue-900/20 dark:text-blue-300",
    concluida: "text-green-500 bg-green-100 dark:bg-green-900/20 dark:text-green-300"
  };
  
  const prioridadeColor = {
    alta: "text-red-500 bg-red-100 dark:bg-red-900/20 dark:text-red-300",
    media: "text-orange-500 bg-orange-100 dark:bg-orange-900/20 dark:text-orange-300",
    baixa: "text-blue-500 bg-blue-100 dark:bg-blue-900/20 dark:text-blue-300"
  };

  const renderSortableHeader = (label: string, key: keyof Tarefa | "responsavel.nome") => (
    <div 
      className="flex items-center cursor-pointer hover:text-foreground" 
      onClick={() => requestSort(key)}
    >
      {label}
      {getSortIcon(key)}
    </div>
  );

  return (
    <div className="w-full overflow-x-auto border rounded-md">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[40px]">#</TableHead>
            <TableHead className="w-[300px]">{renderSortableHeader("Título", "titulo")}</TableHead>
            <TableHead>{renderSortableHeader("Status", "status")}</TableHead>
            <TableHead>{renderSortableHeader("Prazo", "prazo")}</TableHead>
            <TableHead>{renderSortableHeader("Prioridade", "prioridade")}</TableHead>
            <TableHead>{renderSortableHeader("Responsável", "responsavel.nome")}</TableHead>
            <TableHead>{renderSortableHeader("Progresso", "progresso")}</TableHead>
            <TableHead className="text-right">Ações</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {sortedTasks.map((task, index) => {
            const deadlineStatus = getDeadlineStatus(task.prazo);
            const deadlineColor = deadlineStatus === "atrasada" ? "text-red-500" : 
                                  deadlineStatus === "proxima" ? "text-orange-500" : "text-muted-foreground";
            
            return (
              <TableRow 
                key={task.id} 
                className="cursor-pointer hover:bg-muted/50"
                onClick={() => onTaskClick(task)}
              >
                <TableCell className="font-medium">{index + 1}</TableCell>
                <TableCell>
                  <div className="font-medium">{task.titulo}</div>
                  {task.subtarefas.length > 0 && (
                    <div className="text-xs text-muted-foreground flex items-center gap-1 mt-1">
                      <CheckSquare className="h-3 w-3" />
                      {formatSubtaskSummary(task.subtarefas)}
                    </div>
                  )}
                </TableCell>
                <TableCell>
                  <Badge variant="outline" className={statusColor[task.status]}>
                    {task.status === "pendente" ? "Pendente" : 
                     task.status === "em_andamento" ? "Em Andamento" : "Concluída"}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className={`flex items-center gap-1 ${deadlineColor}`}>
                    <Clock className="h-4 w-4" />
                    <span>{formatDate(task.prazo)}</span>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant="outline" className={prioridadeColor[task.prioridade]}>
                    {task.prioridade === "baixa" ? "Baixa" : 
                     task.prioridade === "media" ? "Média" : "Alta"}
                  </Badge>
                </TableCell>
                <TableCell>
                  {task.responsavel ? (
                    <div className="flex items-center gap-2">
                      <Avatar className="h-6 w-6">
                        <AvatarImage src={task.responsavel.avatar} />
                        <AvatarFallback>
                          {task.responsavel.nome.split(" ").map(n => n[0]).join("")}
                        </AvatarFallback>
                      </Avatar>
                      <span>{task.responsavel.nome}</span>
                    </div>
                  ) : (
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <UserCircle className="h-4 w-4" />
                      <span>Não atribuído</span>
                    </div>
                  )}
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Progress value={task.progresso} className="h-2 w-[100px]" />
                    <span className="text-xs font-medium w-[30px]">{task.progresso}%</span>
                  </div>
                </TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Ações</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      {task.status !== "em_andamento" && (
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          onStatusChange(task.id, "em_andamento");
                        }}>
                          <Clock className="h-4 w-4 mr-2" />
                          Iniciar
                        </DropdownMenuItem>
                      )}
                      {task.status !== "concluida" && (
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          onStatusChange(task.id, "concluida");
                        }}>
                          <Check className="h-4 w-4 mr-2" />
                          Marcar como Concluída
                        </DropdownMenuItem>
                      )}
                      {task.status !== "pendente" && (
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          onStatusChange(task.id, "pendente");
                        }}>
                          <X className="h-4 w-4 mr-2" />
                          Marcar como Pendente
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={(e) => {
                        e.stopPropagation();
                        onEdit(task.id);
                      }}>
                        <Edit3 className="h-4 w-4 mr-2" />
                        Editar
                      </DropdownMenuItem>
                      <DropdownMenuItem 
                        onClick={(e) => {
                          e.stopPropagation();
                          onDelete(task.id);
                        }} 
                        className="text-red-600"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Excluir
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
} 