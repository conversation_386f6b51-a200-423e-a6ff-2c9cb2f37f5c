import { 
  <PERSON>, 
  MoreH<PERSON>zontal, 
  Tag, 
  <PERSON><PERSON>quare, 
  Edit3, 
  Trash2, 
  AlertCircle,
  MessageSquare,
  Check,
  UserPlus,
  Link as LinkIcon
} from "lucide-react";
import { 
  <PERSON>, 
  CardContent, 
  CardFooter, 
  CardHeader 
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { 
  Tarefa 
} from "../types";
import {
  formatDate,
  getDeadlineStatus,
  formatSubtaskSummary
} from "../utils/taskUtils";

interface TaskCardProps {
  task: Tarefa;
  onClick: (task: Tarefa) => void;
  onStatusChange: (taskId: string, newStatus: "pendente" | "em_andamento" | "concluida") => void;
  onEdit: (taskId: string) => void;
  onDelete: (taskId: string) => void;
}

export function TaskCard({ task, onClick, onStatusChange, onEdit, onDelete }: TaskCardProps) {
  // Determinar status de prazo
  const deadlineStatus = getDeadlineStatus(task.prazo);
  
  const statusColor = {
    pendente: "text-orange-500 bg-orange-100 dark:bg-orange-900/20 dark:text-orange-300",
    em_andamento: "text-blue-500 bg-blue-100 dark:bg-blue-900/20 dark:text-blue-300",
    concluida: "text-green-500 bg-green-100 dark:bg-green-900/20 dark:text-green-300"
  };
  
  const prioridadeColor = {
    alta: "text-red-500 bg-red-100 dark:bg-red-900/20 dark:text-red-300",
    media: "text-orange-500 bg-orange-100 dark:bg-orange-900/20 dark:text-orange-300",
    baixa: "text-blue-500 bg-blue-100 dark:bg-blue-900/20 dark:text-blue-300"
  };
  
  const deadlineColor = {
    atrasada: "text-red-500",
    proxima: "text-orange-500",
    futura: "text-blue-500"
  };

  return (
    <Card className="hover:shadow-md transition-shadow border border-muted hover:border-muted-foreground/20">
      <CardHeader className="p-4 pb-2 flex flex-row items-start justify-between space-y-0">
        <div className="space-y-1.5 cursor-pointer" onClick={() => onClick(task)}>
          <div className="flex items-center gap-2">
            <h3 className="font-medium text-sm line-clamp-1">{task.titulo}</h3>
            {task.prioridade === "alta" && (
              <AlertCircle className="h-4 w-4 text-red-500" />
            )}
          </div>
          <div className="flex items-center gap-2 flex-wrap">
            <Badge variant="outline" className={statusColor[task.status]}>
              {task.status === "pendente" ? "Pendente" : 
               task.status === "em_andamento" ? "Em Andamento" : "Concluída"}
            </Badge>
            <Badge variant="outline" className={prioridadeColor[task.prioridade]}>
              {task.prioridade === "baixa" ? "Baixa" : task.prioridade === "media" ? "Média" : "Alta"}
            </Badge>
            {task.processoVinculado && (
              <Badge variant="outline" className="flex items-center gap-1">
                <LinkIcon className="h-3 w-3" />
                {task.processoVinculado}
              </Badge>
            )}
          </div>
        </div>
        
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Ações</DropdownMenuLabel>
            <DropdownMenuSeparator />
            {task.status !== "em_andamento" && (
              <DropdownMenuItem onClick={() => onStatusChange(task.id, "em_andamento")}>
                <Clock className="h-4 w-4 mr-2" />
                Iniciar
              </DropdownMenuItem>
            )}
            {task.status !== "concluida" && (
              <DropdownMenuItem onClick={() => onStatusChange(task.id, "concluida")}>
                <Check className="h-4 w-4 mr-2" />
                Marcar como Concluída
              </DropdownMenuItem>
            )}
            {task.status !== "pendente" && (
              <DropdownMenuItem onClick={() => onStatusChange(task.id, "pendente")}>
                <AlertCircle className="h-4 w-4 mr-2" />
                Marcar como Pendente
              </DropdownMenuItem>
            )}
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => onEdit(task.id)}>
              <Edit3 className="h-4 w-4 mr-2" />
              Editar
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onDelete(task.id)} className="text-red-600">
              <Trash2 className="h-4 w-4 mr-2" />
              Excluir
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </CardHeader>
      
      <CardContent className="p-4 pt-0 space-y-3">
        {task.descricao && (
          <p className="text-xs text-muted-foreground line-clamp-2 cursor-pointer" 
             onClick={() => onClick(task)}>
            {task.descricao}
          </p>
        )}
        
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Avatar className="h-6 w-6">
              <AvatarImage src={task.responsavel?.avatar} />
              <AvatarFallback>
                {task.responsavel?.nome ? task.responsavel.nome.split(" ").map(n => n[0]).join("") : "?"}
              </AvatarFallback>
            </Avatar>
            <span className="text-xs truncate max-w-[150px]">{task.responsavel?.nome || "Não atribuído"}</span>
          </div>
          
          <div className="flex items-center gap-1">
            <Clock className={`h-3.5 w-3.5 ${deadlineColor[deadlineStatus]}`} />
            <span className="text-xs">{formatDate(task.prazo)}</span>
          </div>
        </div>
        
        {task.subtarefas.length > 0 && (
          <div className="space-y-1 cursor-pointer" onClick={() => onClick(task)}>
            <div className="flex justify-between text-xs">
              <div className="flex items-center gap-1">
                <CheckSquare className="h-3.5 w-3.5 text-muted-foreground" />
                <span className="text-muted-foreground">{formatSubtaskSummary(task.subtarefas)}</span>
              </div>
              <span>{Math.round((task.subtarefas.filter(s => s.concluida).length / task.subtarefas.length) * 100)}%</span>
            </div>
            <Progress 
              value={(task.subtarefas.filter(s => s.concluida).length / task.subtarefas.length) * 100} 
              className="h-1"
            />
          </div>
        )}
        
        {task.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 cursor-pointer" onClick={() => onClick(task)}>
            {task.tags.slice(0, 3).map((tag, index) => (
              <div key={index} className="flex items-center rounded-full bg-muted px-2 py-0.5 text-xs">
                <Tag className="h-3 w-3 mr-1 text-muted-foreground" />
                <span className="truncate max-w-[80px]">{tag}</span>
              </div>
            ))}
            {task.tags.length > 3 && (
              <div className="flex items-center rounded-full bg-muted px-2 py-0.5 text-xs">
                +{task.tags.length - 3}
              </div>
            )}
          </div>
        )}
      </CardContent>
      
      <CardFooter className="p-2 pt-0 flex justify-between items-center">
        <div className="flex -space-x-2">
          {task.participantes.slice(0, 3).map((participante, index) => (
            <Avatar key={index} className="h-6 w-6 border-2 border-background">
              <AvatarImage src={participante.avatar} />
              <AvatarFallback>
                {participante.nome.split(" ").map(n => n[0]).join("")}
              </AvatarFallback>
            </Avatar>
          ))}
          {task.participantes.length > 3 && (
            <div className="h-6 w-6 rounded-full bg-muted flex items-center justify-center text-xs border-2 border-background">
              +{task.participantes.length - 3}
            </div>
          )}
          {task.participantes.length === 0 && (
            <Button size="icon" variant="ghost" className="h-6 w-6 rounded-full">
              <UserPlus className="h-3.5 w-3.5" />
            </Button>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          {task.comentarios.length > 0 && (
            <div className="flex items-center text-xs text-muted-foreground">
              <MessageSquare className="h-3.5 w-3.5 mr-1" />
              {task.comentarios.length}
            </div>
          )}
          
          <div className="flex items-center gap-1">
            <span className="text-xs font-medium">{task.progresso}%</span>
          </div>
        </div>
      </CardFooter>
    </Card>
  );
} 