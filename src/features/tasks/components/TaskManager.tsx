import React, { useState, useEffect, useCallback, forwardRef, useImperativeHandle } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  LayoutGrid,
  List,
  Plus,
  Filter,
  Clock,
  BarChart,
  Search,
  X,
  FileDown,
  FileUp,
  Info,
  FileText,
  Copy,
  CheckSquare
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { TaskList } from './TaskList';
import { TaskBoard } from './TaskBoard';
import { TaskDetail } from './TaskDetail';
import { TaskMetrics } from './TaskMetrics';
import { TaskForm } from './TaskForm';
import { TaskTemplates } from './TaskTemplates';
import { taskTemplatesIniciais } from '../utils/mockData';
import { Tarefa, TaskManagerProps, TaskGroup, TaskTemplate } from '../types';
import { groupTasks, calculateTaskMetrics } from '../utils/taskHelpers';
import { v4 as uuidv4 } from 'uuid';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { toast } from "@/components/ui/use-toast";
import {
  fetchTasks,
  fetchTasksByClient,
  fetchTasksByPrecatorio,
  fetchTasksByAssignee,
  fetchTaskStatuses,
  fetchUsers,
  createTask as apiCreateTask,
  updateTask as apiUpdateTask,
  deleteTask as apiDeleteTask,
  assignTask as apiAssignTask,
  Task,
  TaskStatus,
  User,
  fetchTaskTemplates,
  createTaskTemplate,
  updateTaskTemplate,
  deleteTaskTemplate,
  fetchTaskSubtasks,
  fetchTaskComments,
  addTaskSubtask as apiAddTaskSubtask
} from '@/services/tasksService';

// Definir a interface para as refs expostas
export interface TaskManagerHandles {
  openCreateTask: () => void;
}

// Modificar a exportação do componente para usar forwardRef
export const TaskManager = forwardRef<TaskManagerHandles, TaskManagerProps>(({
  defaultView = "lista",
  showHeader = true,
  processoId,
  clienteId,
  usuarioId,
  showFilters = true,
  areaFilter = null
}, ref) => {
  const [tarefas, setTarefas] = useState<Tarefa[]>([]);
  const [taskTemplates, setTaskTemplates] = useState<TaskTemplate[]>([]);
  const [tarefasFiltradas, setTarefasFiltradas] = useState<Tarefa[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('todos_status');
  const [prioridadeFilter, setPrioridadeFilter] = useState<string>('todas_prioridades');
  const [responsavelFilter, setResponsavelFilter] = useState<string>('todos_responsaveis');
  const [activeView, setActiveView] = useState<string>(defaultView);
  const [taskGroups, setTaskGroups] = useState<TaskGroup[]>([]);
  const [metrics, setMetrics] = useState(calculateTaskMetrics(tarefas));
  const [selectedTask, setSelectedTask] = useState<Tarefa | null>(null);
  const [isCreateTaskOpen, setIsCreateTaskOpen] = useState(false);
  const [isTaskDetailOpen, setIsTaskDetailOpen] = useState(false);
  const [isTaskEditOpen, setIsTaskEditOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [statusOptions, setStatusOptions] = useState<TaskStatus[]>([]);
  const [responsaveis, setResponsaveis] = useState<User[]>([]);

  // Buscar tarefas do Supabase
  const loadTasks = async () => {
    setIsLoading(true);
    try {
      // Buscar usuários para associar às tarefas
      const usersData = await fetchUsers();
      setResponsaveis(usersData);

      // Buscar status disponíveis
      const statusData = await fetchTaskStatuses();
      setStatusOptions(statusData);

      // Buscar tarefas com base nos filtros
      let tasksData: Task[] = [];

      if (usuarioId) {
        tasksData = await fetchTasksByAssignee(usuarioId);
      } else if (processoId) {
        tasksData = await fetchTasksByPrecatorio(processoId);
      } else if (clienteId) {
        tasksData = await fetchTasksByClient(clienteId);
      } else {
        tasksData = await fetchTasks();
      }

      // Filtrar por área se o filtro estiver definido
      if (areaFilter) {
        tasksData = tasksData.filter(task =>
          !task.area || task.area === areaFilter || task.area === 'AMBOS'
        );
      }

      // Converter para o formato da UI
      const mappedTasks = mapTasksToTarefas(tasksData, usersData, statusData);

      // Atualizar estado
      setTarefas(mappedTasks);
      setTarefasFiltradas(mappedTasks);
      setMetrics(calculateTaskMetrics(mappedTasks));

      // Agrupar tarefas se necessário
      if (activeView === 'quadro') {
        setTaskGroups(groupTasks(mappedTasks, 'status'));
      }
    } catch (error) {
      console.error('Erro ao carregar tarefas:', error);

      // More specific error handling
      const errorMessage = error instanceof Error
        ? error.message
        : 'Falha ao carregar tarefas. Tente novamente mais tarde.';

      toast({
        title: 'Erro ao carregar tarefas',
        description: errorMessage,
        variant: 'destructive'
      });

      // Set empty arrays to prevent UI issues
      setTarefas([]);
      setTarefasFiltradas([]);
      setMetrics(calculateTaskMetrics([]));
    } finally {
      setIsLoading(false);
    }
  };

  // Mapear o formato da API para o formato usado na interface
  const mapTasksToTarefas = (tasks: Task[], users: User[], statuses: TaskStatus[]): Tarefa[] => {
    return tasks.map(task => {
      // Encontrar o responsável
      const responsavel = task.assignee_id
        ? users.find(user => user.id === task.assignee_id)
        : undefined;

      // Encontrar o status com a cor correspondente
      const statusInfo = statuses.find(s => s.name.toLowerCase() === task.status.toLowerCase());

      return {
        id: task.id,
        titulo: task.title,
        descricao: task.description,
        status: task.status,
        prioridade: task.priority,
        responsavel: responsavel ? {
          id: responsavel.id,
          nome: responsavel.nome || responsavel.email,
          email: responsavel.email,
          avatar: (responsavel as any).avatar_url // Type assertion para resolver o erro de TypeScript
        } : undefined,
        prazo: task.due_date,
        dataCriacao: task.created_at,
        progresso: 0, // Inicialmente sem progresso
        participantes: [], // Será preenchido posteriormente
        subtarefas: [], // Será preenchido posteriormente
        comentarios: [], // Será preenchido posteriormente
        arquivosAnexos: [], // Será preenchido posteriormente
        tags: [], // Será preenchido posteriormente
        statusCor: statusInfo?.color || '#888888',
        precatorioId: task.precatorio_id,
        clienteId: task.cliente_id,
        area: task.area || 'AMBOS' // Adicionar a área da tarefa
      };
    });
  };

  // Efeito para carregar tarefas
  useEffect(() => {
    loadTasks();
  }, [clienteId, processoId, usuarioId]);

  // Efeito para filtrar tarefas
  useEffect(() => {
    let filtered = [...tarefas];

    // Filtros específicos passados como props foram aplicados na API

    // Filtros da interface
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(t =>
        t.titulo.toLowerCase().includes(term) ||
        t.descricao.toLowerCase().includes(term) ||
        (t.tags && t.tags.some(tag => tag.toLowerCase().includes(term)))
      );
    }

    if (statusFilter && statusFilter !== 'todos_status') {
      filtered = filtered.filter(t => t.status === statusFilter);
    }

    if (prioridadeFilter && prioridadeFilter !== 'todas_prioridades') {
      filtered = filtered.filter(t => t.prioridade === prioridadeFilter);
    }

    if (responsavelFilter && responsavelFilter !== 'todos_responsaveis') {
      filtered = filtered.filter(t => t.responsavel?.id === responsavelFilter);
    }

    setTarefasFiltradas(filtered);

    // Atualiza grupos de tarefas para o quadro Kanban
    const groups = groupTasks(filtered, 'status');
    setTaskGroups(groups);

    // Atualiza métricas
    setMetrics(calculateTaskMetrics(filtered));
  }, [tarefas, searchTerm, statusFilter, prioridadeFilter, responsavelFilter]);

  // Função para abrir o modal de criar tarefa
  const handleOpenCreateTask = () => {
    try {
      // Verificar se o usuário tem permissão para criar tarefas
      // Esta é uma verificação básica que pode ser expandida conforme necessário
      const userProfileStr = localStorage.getItem('userProfile');
      if (!userProfileStr) {
        toast({
          title: "Permissão negada",
          description: "Você precisa estar logado para criar uma tarefa.",
          variant: "destructive"
        });
        return;
      }

      const userProfile = JSON.parse(userProfileStr);

      // Lista de papéis que podem criar tarefas
      const allowedRoles = ['admin', 'gerente_geral', 'gerente_precatorio', 'gerente_rpv', 'operacional_completo'];

      if (!allowedRoles.includes(userProfile.role)) {
        toast({
          title: "Permissão negada",
          description: "Você não tem permissão para criar tarefas. Entre em contato com um administrador.",
          variant: "destructive"
        });
        return;
      }

      setIsCreateTaskOpen(true);
    } catch (error) {
      console.error("Erro ao verificar permissões:", error);
      toast({
        title: "Erro",
        description: "Não foi possível verificar suas permissões. Tente novamente mais tarde.",
        variant: "destructive"
      });
    }
  };

  // Função para adicionar nova tarefa
  const handleAddTask = async (novaTarefa: Tarefa) => {
    try {
      setIsLoading(true);
      console.log("Criando nova tarefa:", novaTarefa);

      // Obter ID do usuário atual do localStorage
      let userId = '';
      try {
        const userProfileStr = localStorage.getItem('userProfile');
        if (userProfileStr) {
          const userProfile = JSON.parse(userProfileStr);
          userId = userProfile.id;
        }
      } catch (e) {
        console.error("Erro ao obter ID do usuário:", e);
      }

      // Converter para o formato da API
      const taskData = {
        title: novaTarefa.titulo,
        description: novaTarefa.descricao,
        status: novaTarefa.status,
        priority: novaTarefa.prioridade,
        created_by: userId || novaTarefa.responsavel?.id || '',
        assignee_id: novaTarefa.responsavel?.id,
        cliente_id: novaTarefa.clienteId,
        precatorio_id: novaTarefa.precatorioId,
        // Garantir que datas vazias sejam convertidas para undefined
        due_date: novaTarefa.prazo === '' ? undefined : novaTarefa.prazo
        // Removida propriedade area pois a coluna não existe no banco de dados
      };

      // Log para debug
      console.log("Enviando dados para API:", taskData);

      // Mostrar toast de criação iniciada
      toast({
        title: "Criando tarefa...",
        description: "Aguarde enquanto a tarefa é criada.",
      });

      const createdTask = await apiCreateTask(taskData);

      if (!createdTask || !createdTask.id) {
        throw new Error("Não foi possível criar a tarefa. Resposta inválida do servidor.");
      }

      console.log("Tarefa criada com sucesso:", createdTask);

      // Adicionar subtarefas se houver
      if (novaTarefa.subtarefas && novaTarefa.subtarefas.length > 0) {
        try {
          toast({
            title: "Adicionando subtarefas...",
            description: `Adicionando ${novaTarefa.subtarefas.length} subtarefas.`,
          });

          for (const subtarefa of novaTarefa.subtarefas) {
            await apiAddTaskSubtask(createdTask.id, {
              id: subtarefa.id,
              titulo: subtarefa.titulo,
              concluida: subtarefa.concluida
            });
          }
          console.log("Subtarefas adicionadas com sucesso");
        } catch (subtaskError) {
          console.error("Erro ao adicionar subtarefas:", subtaskError);
          // Não impedir a criação da tarefa principal se as subtarefas falharem
          toast({
            title: "Atenção",
            description: "A tarefa foi criada, mas houve um erro ao adicionar subtarefas.",
            variant: "default"
          });
        }
      }

      // Converter de volta para o formato da interface
      const mappedTasks = mapTasksToTarefas([createdTask], responsaveis, statusOptions);
      const createdTarefa = mappedTasks[0];

      if (!createdTarefa) {
        throw new Error("Erro ao mapear tarefa criada");
      }

      // Adicionar a tarefa criada à lista local
      setTarefas(prevTarefas => [createdTarefa, ...prevTarefas]);
      setTarefasFiltradas(prevTarefas => [createdTarefa, ...prevTarefas]);

      // Atualizar grupos de tarefas se estiver na visualização de quadro
      if (activeView === 'quadro') {
        const updatedGroups = [...taskGroups];
        const groupIndex = updatedGroups.findIndex(
          group => group.title.toLowerCase() === createdTask.status.toLowerCase()
        );

        if (groupIndex >= 0) {
          updatedGroups[groupIndex].tasks.push(createdTarefa);
          setTaskGroups(updatedGroups);
        }
      }

      // Atualizar métricas
      setMetrics(calculateTaskMetrics([...tarefas, createdTarefa]));

      // Fechar o modal de criação
      setIsCreateTaskOpen(false);

      toast({
        title: "Tarefa criada com sucesso!",
        description: `A tarefa "${createdTask.title}" foi criada com sucesso.`,
        variant: "default"
      });
    } catch (error) {
      console.error('Erro ao criar tarefa:', error);
      toast({
        title: "Erro ao criar tarefa",
        description: error instanceof Error ? error.message : "Não foi possível criar a tarefa. Tente novamente mais tarde.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Função para atualizar tarefa
  const handleUpdateTask = async (tarefaAtualizada: Tarefa) => {
    try {
      setIsLoading(true);

      // Converter para o formato da API
      const taskData = {
        title: tarefaAtualizada.titulo,
        description: tarefaAtualizada.descricao,
        status: tarefaAtualizada.status,
        priority: tarefaAtualizada.prioridade,
        assignee_id: tarefaAtualizada.responsavel?.id,
        // Garantir que datas vazias sejam convertidas para undefined
        due_date: tarefaAtualizada.prazo === '' ? undefined : tarefaAtualizada.prazo
        // Removida propriedade area pois a coluna não existe no banco de dados
      };

      const updatedTask = await apiUpdateTask(tarefaAtualizada.id, taskData);

      // Atualizar estado local mantendo todas as informações
      setTarefas(prevTarefas =>
        prevTarefas.map(t =>
          t.id === tarefaAtualizada.id
            ? {
                ...t,
                ...tarefaAtualizada,
                responsavel: tarefaAtualizada.responsavel,
                subtarefas: tarefaAtualizada.subtarefas || t.subtarefas,
                comentarios: tarefaAtualizada.comentarios || t.comentarios,
                participantes: tarefaAtualizada.participantes || t.participantes,
                tags: tarefaAtualizada.tags || t.tags
              }
            : t
        )
      );

      // Se a tarefa atualizada for a selecionada, atualiza também
      if (selectedTask && selectedTask.id === tarefaAtualizada.id) {
        setSelectedTask(tarefaAtualizada);
      }

      // Atualizar grupos de tarefas se estiver na visualização de quadro
      if (activeView === 'quadro') {
        const updatedGroups = taskGroups.map(group => ({
          ...group,
          tasks: group.tasks.map(t =>
            t.id === tarefaAtualizada.id ? tarefaAtualizada : t
          )
        }));
        setTaskGroups(updatedGroups);
      }

      toast({
        title: "Tarefa atualizada",
        description: "A tarefa foi atualizada com sucesso!",
      });

      // Fechar o modal de edição
      setIsTaskDetailOpen(false);
    } catch (error: any) {
      console.error('Erro ao atualizar tarefa:', error);

      toast({
        title: "Erro ao atualizar tarefa",
        description: error.message || "Não foi possível atualizar a tarefa. Tente novamente mais tarde.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Função para excluir tarefa
  const handleDeleteTask = async (tarefaId: string) => {
    try {
      await apiDeleteTask(tarefaId);

      setTarefas(tarefas.filter(t => t.id !== tarefaId));

      // Se a tarefa excluída for a selecionada, limpa a seleção
      if (selectedTask && selectedTask.id === tarefaId) {
        setSelectedTask(null);
        setIsTaskDetailOpen(false);
      }

      toast({
        title: "Tarefa excluída",
        description: "A tarefa foi excluída com sucesso!",
      });
    } catch (error) {
      console.error('Erro ao excluir tarefa:', error);
      toast({
        title: "Erro ao excluir tarefa",
        description: "Não foi possível excluir a tarefa. Tente novamente mais tarde.",
        variant: "destructive"
      });
    }
  };

  // Função para visualizar detalhes da tarefa
  const handleViewTaskDetails = (tarefa: Tarefa) => {
    setSelectedTask(tarefa);
    setIsTaskDetailOpen(true);
    setIsTaskEditOpen(false);
  };

  // Função para editar tarefa
  const handleEditTask = () => {
    setIsTaskDetailOpen(false);
    setIsTaskEditOpen(true);
  };

  // Função para criar um modelo de tarefa a partir de uma tarefa existente
  const handleCreateTemplate = async (templateData: TaskTemplate) => {
    try {
      // Criar template no Supabase
      const createdTemplate = await createTaskTemplate(templateData);

      if (createdTemplate) {
        setTaskTemplates(prev => [createdTemplate, ...prev]);

        toast({
          title: "Sucesso",
          description: "Modelo de tarefa criado com sucesso!",
        });
      }
    } catch (error) {
      console.error("Erro ao criar modelo:", error);
      toast({
        title: "Erro",
        description: "Não foi possível criar o modelo de tarefa. Tente novamente.",
        variant: "destructive"
      });
    }
  };

  // Função para atualizar um modelo de tarefa
  const handleUpdateTemplate = async (templateId: string, templateData: TaskTemplate) => {
    try {
      // Atualizar template no Supabase
      const updatedTemplate = await updateTaskTemplate(templateId, templateData);

      if (updatedTemplate) {
        setTaskTemplates(prev =>
          prev.map(template => template.id === templateId ? updatedTemplate : template)
        );

        toast({
          title: "Sucesso",
          description: "Modelo de tarefa atualizado com sucesso!",
        });
      }
    } catch (error) {
      console.error("Erro ao atualizar modelo:", error);
      toast({
        title: "Erro",
        description: "Não foi possível atualizar o modelo de tarefa. Tente novamente.",
        variant: "destructive"
      });
    }
  };

  // Função para excluir um modelo de tarefa
  const handleDeleteTemplate = async (templateId: string) => {
    try {
      // Excluir template no Supabase
      await deleteTaskTemplate(templateId);

      // Atualizar estado local
      setTaskTemplates(prev => prev.filter(template => template.id !== templateId));

      toast({
        title: "Sucesso",
        description: "Modelo de tarefa excluído com sucesso!",
      });
    } catch (error) {
      console.error("Erro ao excluir modelo:", error);
      toast({
        title: "Erro",
        description: "Não foi possível excluir o modelo de tarefa. Tente novamente.",
        variant: "destructive"
      });
    }
  };

  // Função para criar uma tarefa a partir de um modelo
  const handleCreateTaskFromTemplate = async (template: TaskTemplate) => {
    try {
      setIsLoading(true);
      // Obter ID do usuário atual
      let userId = '';
      try {
        const userProfileStr = localStorage.getItem('userProfile');
        if (userProfileStr) {
          const userProfile = JSON.parse(userProfileStr);
          userId = userProfile.id;
        }
      } catch (e) {
        console.error("Erro ao obter ID do usuário:", e);
      }

      // Criar nova tarefa com base no template
      const taskData = {
        title: template.titulo,
        description: template.descricao || '',
        status: template.status || 'pendente',
        priority: template.prioridade || 'media',
        created_by: userId,
        cliente_id: clienteId,
        precatorio_id: processoId,
        area: areaFilter || 'AMBOS'
      };

      toast({
        title: "Criando tarefa a partir do modelo...",
        description: `Criando "${template.titulo}"`,
      });

      // Usar a função apiCreateTask do serviço
      const createdTask = await apiCreateTask(taskData);

      // Adicionar subtarefas do template
      if (template.subtarefas && template.subtarefas.length > 0) {
        for (const subtarefa of template.subtarefas) {
          await apiAddTaskSubtask(createdTask.id, {
            id: uuidv4(),
            titulo: subtarefa.titulo,
            concluida: false
          });
        }
      }

      // Converter para o formato da interface
      const mappedTasks = mapTasksToTarefas([createdTask], responsaveis, statusOptions);
      const createdTarefa = mappedTasks[0];

      if (!createdTarefa) {
        throw new Error("Erro ao mapear tarefa criada a partir do template");
      }

      // Adicionar a tarefa criada à lista local
      setTarefas(prevTarefas => [createdTarefa, ...prevTarefas]);
      setTarefasFiltradas(prevTarefas => [createdTarefa, ...prevTarefas]);

      // Atualizar métricas
      setMetrics(calculateTaskMetrics([...tarefas, createdTarefa]));

      toast({
        title: "Sucesso!",
        description: `Tarefa "${template.titulo}" criada a partir do modelo`,
        variant: "default"
      });

      return createdTarefa;
    } catch (error) {
      console.error("Erro ao criar tarefa a partir do modelo:", error);
      toast({
        title: "Erro",
        description: "Não foi possível criar a tarefa. Tente novamente.",
        variant: "destructive"
      });
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Função para carregar subtarefas e comentários para uma tarefa
  const carregarDetalhesAdicionais = async (tarefas: Tarefa[]): Promise<Tarefa[]> => {
    const tarefasAtualizadas = [...tarefas];

    for (const tarefa of tarefasAtualizadas) {
      try {
        // Buscar subtarefas
        const subtarefas = await fetchTaskSubtasks(tarefa.id);
        if (subtarefas && subtarefas.length > 0) {
          tarefa.subtarefas = subtarefas.map(st => ({
            id: st.id,
            titulo: st.title,
            concluida: st.completed,
            prazo: st.due_date || undefined
          }));
        }

        // Buscar comentários
        const comentarios = await fetchTaskComments(tarefa.id);
        if (comentarios && comentarios.length > 0) {
          tarefa.comentarios = comentarios.map(c => {
            // Criar um objeto autor com base nos dados de perfil
            const autor = {
              id: c.user_id,
              nome: c.profiles?.nome || 'Usuário',
              email: c.profiles?.email || '',
              avatar: c.profiles?.avatar_url
            };

            return {
              id: c.id,
              texto: c.content,
              autor: autor,
              dataCriacao: c.created_at,
              anexos: []
            };
          });
        }
      } catch (error) {
        console.error(`Erro ao carregar detalhes da tarefa ${tarefa.id}:`, error);
      }
    }

    return tarefasAtualizadas;
  };

  const buscarUsuarios = useCallback(async () => {
    try {
      // A função fetchUsers já foi modificada para evitar a recursão
      return await fetchUsers();
    } catch (error) {
      console.error("Erro ao buscar usuários:", error);
      toast({
        title: "Erro",
        description: "Não foi possível carregar a lista de usuários.",
        variant: "destructive"
      });
      return [];
    }
  }, [toast]);

  const carregarTarefas = useCallback(async () => {
    try {
      let tarefasData;

      // Buscar tarefas com base nos filtros adequados
      if (processoId) {
        tarefasData = await fetchTasksByPrecatorio(processoId);
      } else if (clienteId) {
        tarefasData = await fetchTasksByClient(clienteId);
      } else {
        tarefasData = await fetchTasks();
      }

      // Adaptar dados para o formato esperado pelo frontend
      const tarefasFormatadas = tarefasData.map(tarefa => ({
        id: tarefa.id,
        titulo: tarefa.title,
        descricao: tarefa.description,
        status: tarefa.status,
        prioridade: tarefa.priority,
        responsavelId: tarefa.assignee_id,
        processoVinculado: tarefa.precatorio_id,
        clienteId: tarefa.cliente_id,
        prazo: tarefa.due_date,
        dataCriacao: tarefa.created_at,
        dataAtualizacao: tarefa.updated_at,
        subtarefas: [],
        participantesIds: [],
        comentarios: [],
        tags: []
      }));

      return tarefasFormatadas;
    } catch (error) {
      console.error("Erro ao carregar tarefas:", error);
      toast({
        title: "Erro",
        description: "Não foi possível carregar as tarefas.",
        variant: "destructive"
      });
      return [];
    }
  }, [processoId, clienteId, toast]);

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Executar chamadas em paralelo para melhorar o desempenho
        const [tarefasCarregadas, templates, usuarios] = await Promise.all([
          carregarTarefas(),
          fetchTaskTemplates(),
          buscarUsuarios()
        ]);

        // Carregar detalhes adicionais para as tarefas
        const tarefasCompletas = await carregarDetalhesAdicionais(tarefasCarregadas);

        // Atualizar estados
        setTarefas(tarefasCompletas);
        setTarefasFiltradas(tarefasCompletas);
        setTaskTemplates(templates);
        setResponsaveis(usuarios);

        // Atualizar grupos e métricas
        const grupos = groupTasks(tarefasCompletas, 'status');
        setTaskGroups(grupos);
        setMetrics(calculateTaskMetrics(tarefasCompletas));

      } catch (error) {
        console.error("Erro ao buscar dados:", error);
        toast({
          title: "Erro",
          description: "Não foi possível carregar os dados. Tente novamente mais tarde.",
          variant: "destructive"
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [carregarTarefas, buscarUsuarios]);

  // Expor a função openCreateTask através da ref
  useImperativeHandle(ref, () => ({
    openCreateTask: () => {
      handleOpenCreateTask();
    }
  }), [handleOpenCreateTask]);

  return (
    <div className="space-y-4 p-4 md:p-6">
      {showHeader && (
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-3xl font-bold tracking-tight">Tarefas</h2>
          <Button onClick={handleOpenCreateTask} size="default">
            <Plus className="mr-2 h-4 w-4" /> Nova Tarefa
          </Button>
        </div>
      )}

      {showFilters && (
        <Card className="mb-6">
          <CardContent className="p-4 pt-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Buscar tarefas..."
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>

              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="todos_status">Todos os status</SelectItem>
                  {statusOptions.map(status => (
                    <SelectItem key={status.id} value={status.name}>{status.name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={prioridadeFilter} onValueChange={setPrioridadeFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Prioridade" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="todas_prioridades">Todas as prioridades</SelectItem>
                  <SelectItem value="alta">Alta</SelectItem>
                  <SelectItem value="media">Média</SelectItem>
                  <SelectItem value="baixa">Baixa</SelectItem>
                </SelectContent>
              </Select>

              <Select value={responsavelFilter} onValueChange={setResponsavelFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Responsável" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="todos_responsaveis">Todos os responsáveis</SelectItem>
                  {responsaveis.map(user => (
                    <SelectItem key={user.id} value={user.id}>{user.nome || user.email}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {(searchTerm || statusFilter !== 'todos_status' || prioridadeFilter !== 'todas_prioridades' || responsavelFilter !== 'todos_responsaveis') && (
              <div className="flex items-center gap-2 mt-4">
                <div className="text-sm text-muted-foreground">
                  Filtros ativos:
                </div>

                {searchTerm && (
                  <Badge variant="secondary" className="flex items-center gap-1">
                    Busca: {searchTerm}
                    <X
                      className="h-3 w-3 cursor-pointer"
                      onClick={() => setSearchTerm('')}
                    />
                  </Badge>
                )}

                {statusFilter && statusFilter !== 'todos_status' && (
                  <Badge variant="secondary" className="flex items-center gap-1">
                    Status: {statusFilter}
                    <X
                      className="h-3 w-3 cursor-pointer"
                      onClick={() => setStatusFilter('todos_status')}
                    />
                  </Badge>
                )}

                {prioridadeFilter && prioridadeFilter !== 'todas_prioridades' && (
                  <Badge variant="secondary" className="flex items-center gap-1">
                    Prioridade: {prioridadeFilter}
                    <X
                      className="h-3 w-3 cursor-pointer"
                      onClick={() => setPrioridadeFilter('todas_prioridades')}
                    />
                  </Badge>
                )}

                {responsavelFilter && responsavelFilter !== 'todos_responsaveis' && (
                  <Badge variant="secondary" className="flex items-center gap-1">
                    Responsável: {responsaveis.find(user => user.id === responsavelFilter)?.nome || responsavelFilter}
                    <X
                      className="h-3 w-3 cursor-pointer"
                      onClick={() => setResponsavelFilter('todos_responsaveis')}
                    />
                  </Badge>
                )}

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setSearchTerm('');
                    setStatusFilter('todos_status');
                    setPrioridadeFilter('todas_prioridades');
                    setResponsavelFilter('todos_responsaveis');
                  }}
                  className="ml-auto"
                >
                  Limpar filtros
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      <div className="rounded-lg border bg-card shadow-sm">
        <div className="flex justify-between items-center px-4 py-3 border-b">
          <Tabs value={activeView} className="w-full" onValueChange={setActiveView}>
            <div className="flex justify-between items-center">
              <TabsList>
                <TabsTrigger value="todo" className="flex items-center gap-1">
                  <CheckSquare className="h-4 w-4" />
                  Todo
                </TabsTrigger>
                <TabsTrigger value="lista" className="flex items-center gap-1">
                  <List className="h-4 w-4" />
                  Lista
                </TabsTrigger>
                <TabsTrigger value="quadro" className="flex items-center gap-1">
                  <LayoutGrid className="h-4 w-4" />
                  Quadro
                </TabsTrigger>
                <TabsTrigger value="modelos" className="flex items-center gap-1">
                  <Copy className="h-4 w-4" />
                  Modelos
                </TabsTrigger>
                <TabsTrigger value="metricas" className="flex items-center gap-1">
                  <BarChart className="h-4 w-4" />
                  Métricas
                </TabsTrigger>
              </TabsList>

              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" className="flex items-center gap-1">
                  <FileDown className="h-4 w-4" />
                  Exportar
                </Button>
                <Button variant="outline" size="sm" className="flex items-center gap-1">
                  <FileUp className="h-4 w-4" />
                  Importar
                </Button>
              </div>
            </div>

            {isLoading ? (
              <div className="w-full p-8 flex justify-center">
                <div className="animate-spin h-8 w-8 border-4 border-primary rounded-full border-t-transparent"></div>
              </div>
            ) : (
              <>
                <TabsContent value="lista" className="mt-2 p-2">
                  <div className="rounded-md bg-background">
                    <TaskList
                      tarefas={tarefasFiltradas}
                      onUpdateTask={handleUpdateTask}
                      onDeleteTask={handleDeleteTask}
                      onViewTaskDetails={handleViewTaskDetails}
                      showSubtasksInCascade={true}
                      showSimpleList={false}
                    />
                  </div>
                </TabsContent>

                <TabsContent value="todo" className="mt-2 p-2">
                  <div className="rounded-md bg-background">
                    <TaskList
                      tarefas={tarefasFiltradas}
                      onUpdateTask={handleUpdateTask}
                      onDeleteTask={handleDeleteTask}
                      onViewTaskDetails={handleViewTaskDetails}
                      showSubtasksInCascade={false}
                      showSimpleList={true}
                    />
                  </div>
                </TabsContent>

                <TabsContent value="quadro" className="mt-2 p-2">
                  <div className="rounded-md bg-background">
                    <TaskBoard
                      taskGroups={taskGroups}
                      onUpdateTask={handleUpdateTask}
                      onDeleteTask={handleDeleteTask}
                      onViewTaskDetails={handleViewTaskDetails}
                    />
                  </div>
                </TabsContent>

                <TabsContent value="modelos" className="mt-2 p-2">
                  <div className="rounded-md bg-background p-4">
                    <TaskTemplates
                      templates={taskTemplates}
                      onCreateTemplate={handleCreateTemplate}
                      onUpdateTemplate={handleUpdateTemplate}
                      onDeleteTemplate={handleDeleteTemplate}
                      onCreateTaskFromTemplate={handleCreateTaskFromTemplate}
                    />
                  </div>
                </TabsContent>

                <TabsContent value="metricas" className="mt-2 p-2">
                  <div className="rounded-md bg-background">
                    <TaskMetrics metrics={metrics} />
                  </div>
                </TabsContent>
              </>
            )}
          </Tabs>
        </div>
      </div>

      {/* Modal de criação de tarefa */}
      {isCreateTaskOpen && (
        <Dialog open={isCreateTaskOpen} onOpenChange={setIsCreateTaskOpen}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto" aria-describedby="dialog-description">
            <DialogHeader>
              <DialogTitle>Nova Tarefa</DialogTitle>
              <DialogDescription id="dialog-description">Crie uma nova tarefa preenchendo os campos abaixo.</DialogDescription>
            </DialogHeader>
            <TaskForm
              onSubmit={handleAddTask}
              mode="create"
              onCancel={() => setIsCreateTaskOpen(false)}
            />
          </DialogContent>
        </Dialog>
      )}

      {/* Modal para detalhes da tarefa */}
      {isTaskDetailOpen && selectedTask && (
        <Dialog open={isTaskDetailOpen} onOpenChange={setIsTaskDetailOpen}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto" aria-describedby="task-detail-description">
            <DialogHeader>
              <DialogTitle>Detalhes da Tarefa</DialogTitle>
              <DialogDescription id="task-detail-description">Visualize os detalhes da tarefa.</DialogDescription>
            </DialogHeader>
            <TaskDetail
              tarefa={selectedTask}
              onUpdateTask={handleUpdateTask}
              onDeleteTask={handleDeleteTask}
              onBack={() => setIsTaskDetailOpen(false)}
              onEdit={handleEditTask}
            />
          </DialogContent>
        </Dialog>
      )}

      {/* Modal para edição da tarefa */}
      {isTaskEditOpen && selectedTask && (
        <Dialog open={isTaskEditOpen} onOpenChange={setIsTaskEditOpen}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto" aria-describedby="edit-dialog-description">
            <DialogHeader>
              <DialogTitle>Editar Tarefa</DialogTitle>
              <DialogDescription id="edit-dialog-description">Edite os detalhes da tarefa conforme necessário.</DialogDescription>
            </DialogHeader>
            <TaskForm
              tarefa={selectedTask}
              onSubmit={(tarefaAtualizada) => {
                handleUpdateTask(tarefaAtualizada);
                setIsTaskEditOpen(false);
                setSelectedTask(tarefaAtualizada);
                setIsTaskDetailOpen(true);
              }}
              onDelete={handleDeleteTask}
              mode="edit"
              onCancel={() => {
                setIsTaskEditOpen(false);
                setIsTaskDetailOpen(true);
              }}
            />
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
});