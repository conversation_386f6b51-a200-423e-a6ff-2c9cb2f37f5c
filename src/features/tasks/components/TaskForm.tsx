import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  <PERSON>Content, 
  <PERSON><PERSON><PERSON>er, 
  Card<PERSON><PERSON>er, 
  CardTitle 
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { CalendarIcon, X, Plus, Loader2, AlertCircle, Trash2 } from "lucide-react";
import { Tarefa } from '../types';
import { v4 as uuidv4 } from 'uuid';
import { fetchUsers, fetchTaskStatuses, User, TaskStatus } from '@/services/tasksService';
import { toast } from "@/components/ui/use-toast";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";

const formSchema = z.object({
  titulo: z.string().min(1, "O título é obrigatório"),
  descricao: z.string().optional(),
  status: z.string().min(1, "O status é obrigatório"),
  prioridade: z.string().min(1, "A prioridade é obrigatória"),
  responsavel: z.string().optional(),
  prazo: z.string().optional(),
  area: z.string().optional(),
  tags: z.array(z.string()).default([]),
  subtarefas: z.array(z.object({
    id: z.string(),
    titulo: z.string(),
    concluida: z.boolean()
  })).default([])
});

interface TaskFormProps {
  tarefa?: Tarefa;
  onSubmit: (tarefa: Tarefa) => void;
  onCancel: () => void;
  clienteId?: string;
  precatorioId?: string;
  onDelete?: (id: string) => void;
  mode?: "create" | "edit";
}

export const TaskForm: React.FC<TaskFormProps> = ({ 
  tarefa, 
  onSubmit, 
  onCancel,
  clienteId,
  precatorioId,
  onDelete,
  mode = "create"
}) => {
  const isEditing = !!tarefa;
  
  const [titulo, setTitulo] = useState(tarefa?.titulo || '');
  const [descricao, setDescricao] = useState(tarefa?.descricao || '');
  const [prioridade, setPrioridade] = useState(tarefa?.prioridade || 'media');
  const [status, setStatus] = useState(tarefa?.status || 'pendente');
  const [dataLimite, setDataLimite] = useState<Date | undefined>(
    tarefa?.prazo ? new Date(tarefa.prazo) : undefined
  );
  const [responsavel, setResponsavel] = useState(tarefa?.responsavel?.id || '');
  const [tags, setTags] = useState<string[]>(tarefa?.tags || []);
  const [novaTag, setNovaTag] = useState('');
  const [subtarefas, setSubtarefas] = useState<Array<{id: string, titulo: string, concluida: boolean}>>(
    tarefa?.subtarefas || []
  );
  const [novaSubtarefa, setNovaSubtarefa] = useState('');
  const [usuarios, setUsuarios] = useState<User[]>([]);
  const [statusOptions, setStatusOptions] = useState<TaskStatus[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [area, setArea] = useState<'PRECATORIO' | 'RPV' | 'AMBOS'>(tarefa?.area || 'AMBOS');
  const [formErrors, setFormErrors] = useState<{[key: string]: string}>({});
  
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      titulo: tarefa?.titulo || "",
      descricao: tarefa?.descricao || "",
      status: tarefa?.status || "PENDENTE",
      prioridade: tarefa?.prioridade || "MEDIA",
      responsavel: tarefa?.responsavel?.id || "",
      prazo: tarefa?.prazo ? new Date(tarefa.prazo).toISOString().split('T')[0] : "",
      area: tarefa?.area || "AMBOS"
    },
  });

  // Carregar usuários e status
  useEffect(() => {
    const carregarDados = async () => {
      try {
        setIsLoading(true);
        const [usersData, statusesData] = await Promise.all([
          fetchUsers(),
          fetchTaskStatuses()
        ]);
        
        // Filtra usuários e status para garantir que não haja valores vazios
        const usuariosFiltrados = usersData.filter(user => 
          user && user.id && (user.nome || user.email)
        );
        const statusFiltrados = statusesData.filter(status => 
          status && typeof status.name === 'string' && status.name.length > 0
        );
        
        setUsuarios(usuariosFiltrados);
        setStatusOptions(statusFiltrados);
        
        // Se estiver editando, atualizar os valores do formulário
        if (isEditing && tarefa) {
          form.reset({
            titulo: tarefa.titulo,
            descricao: tarefa.descricao,
            status: tarefa.status,
            prioridade: tarefa.prioridade,
            responsavel: tarefa.responsavel?.id || "",
            prazo: tarefa.prazo ? new Date(tarefa.prazo).toISOString().split('T')[0] : "",
            area: tarefa.area || "AMBOS"
          });
        }
        
        setIsLoading(false);
      } catch (error) {
        console.error('Erro ao carregar dados:', error);
        toast({
          title: "Erro ao carregar dados",
          description: "Não foi possível carregar usuários ou status. Tente novamente mais tarde.",
          variant: "destructive"
        });
        setIsLoading(false);
      }
    };
    
    carregarDados();
  }, [isEditing, tarefa, form]);

  // Usar o clienteId e precatorioId passados como props, se disponíveis
  useEffect(() => {
    if (clienteId && !isEditing) {
      console.log("Setting clienteId from props:", clienteId);
    }
    if (precatorioId && !isEditing) {
      console.log("Setting precatorioId from props:", precatorioId);
    }
  }, [clienteId, precatorioId, isEditing]);
  
  const validateForm = (): boolean => {
    const errors: {[key: string]: string} = {};
    
    if (!titulo.trim()) {
      errors.titulo = "O título da tarefa é obrigatório";
    }
    
    if (!status) {
      errors.status = "O status da tarefa é obrigatório";
    }
    
    if (!prioridade) {
      errors.prioridade = "A prioridade da tarefa é obrigatória";
    }
    
    if (!area) {
      errors.area = "A área da tarefa é obrigatória";
    }
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };
  
  const handleSubmit = (data: z.infer<typeof formSchema>) => {
    const responsavelObj = data.responsavel && data.responsavel !== 'sem_responsavel'
      ? usuarios.find(u => u.id === data.responsavel) 
      : undefined;
    
    const novaTarefa: Tarefa = {
      id: tarefa?.id || uuidv4(),
      titulo: data.titulo,
      descricao: data.descricao || '',
      prioridade: data.prioridade as 'ALTA' | 'MEDIA' | 'BAIXA' | 'URGENTE',
      status: data.status,
      prazo: data.prazo,
      dataCriacao: tarefa?.dataCriacao || new Date().toISOString(),
      responsavel: responsavelObj ? {
        id: responsavelObj.id,
        nome: responsavelObj.nome || responsavelObj.email,
        email: responsavelObj.email
      } : undefined,
      tags: data.tags || [],
      subtarefas: data.subtarefas || [],
      participantes: tarefa?.participantes || [],
      progresso: tarefa?.progresso || 0,
      comentarios: tarefa?.comentarios || [],
      arquivosAnexos: tarefa?.arquivosAnexos || [],
      clienteId: clienteId || tarefa?.clienteId,
      precatorioId: precatorioId || tarefa?.precatorioId,
      area: data.area as 'PRECATORIO' | 'RPV' | 'AMBOS',
      statusCor: tarefa?.statusCor || getStatusColor(data.status)
    };
    
    onSubmit(novaTarefa);
  };
  
  const getStatusColor = (statusName: string): string => {
    const statusInfo = statusOptions.find(s => s.name === statusName);
    return statusInfo?.color || '#888888';
  };
  
  const handleAddTag = () => {
    if (novaTag?.trim() && !tags.includes(novaTag.trim())) {
      const novasTags = [...tags, novaTag.trim()];
      setTags(novasTags);
      setNovaTag('');
      form.setValue('tags', novasTags);
    }
  };
  
  const handleRemoveTag = (tagToRemove: string) => {
    const novasTags = tags.filter(tag => tag !== tagToRemove);
    setTags(novasTags);
    form.setValue('tags', novasTags);
  };
  
  const handleAddSubtarefa = () => {
    if (novaSubtarefa?.trim()) {
      const novaSubtarefaObj = {
        id: uuidv4(),
        titulo: novaSubtarefa.trim(),
        concluida: false
      };
      const novasSubtarefas = [...subtarefas, novaSubtarefaObj];
      setSubtarefas(novasSubtarefas);
      setNovaSubtarefa('');
      form.setValue('subtarefas', novasSubtarefas);
    }
  };
  
  const handleRemoveSubtarefa = (id: string) => {
    const novasSubtarefas = subtarefas.filter(st => st.id !== id);
    setSubtarefas(novasSubtarefas);
    form.setValue('subtarefas', novasSubtarefas);
  };
  
  if (isLoading) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>{isEditing ? 'Editar Tarefa' : 'Nova Tarefa'}</CardTitle>
        </CardHeader>
        <CardContent className="flex justify-center items-center py-8">
          <div className="flex flex-col items-center space-y-2">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <p className="text-sm text-muted-foreground">Carregando formulário...</p>
          </div>
        </CardContent>
      </Card>
    );
  }
  
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>{isEditing ? 'Editar Tarefa' : 'Nova Tarefa'}</CardTitle>
      </CardHeader>
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">
          {mode === "create" ? "Nova Tarefa" : "Editar Tarefa"}
        </h2>
        {mode === "edit" && onDelete && tarefa && (
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button variant="destructive" size="icon">
                <Trash2 className="h-4 w-4" />
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Excluir Tarefa</AlertDialogTitle>
                <AlertDialogDescription>
                  Tem certeza que deseja excluir esta tarefa? Esta ação não pode ser desfeita.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancelar</AlertDialogCancel>
                <AlertDialogAction onClick={() => onDelete(tarefa.id)} className="bg-destructive text-destructive-foreground">
                  Excluir
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        )}
      </div>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        <Form {...form}>
          <CardContent className="space-y-4">
            {Object.keys(formErrors).length > 0 && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Erro no formulário</AlertTitle>
                <AlertDescription>
                  Por favor, corrija os erros abaixo antes de continuar.
                </AlertDescription>
              </Alert>
            )}
            
            <FormField
              control={form.control}
              name="titulo"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Título*</FormLabel>
                  <FormControl>
                    <Input 
                      {...field}
                      placeholder="Título da tarefa"
                      className={formErrors.titulo ? "border-destructive" : ""}
                    />
                  </FormControl>
                  {formErrors.titulo && (
                    <FormMessage className="text-destructive">{formErrors.titulo}</FormMessage>
                  )}
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="descricao"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Descrição</FormLabel>
                  <FormControl>
                    <Textarea 
                      {...field}
                      placeholder="Descrição detalhada da tarefa"
                      rows={4}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Status</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value || ""}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione o status" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {statusOptions.map((status) => (
                          status && status.name ? (
                            <SelectItem key={status.name} value={status.name}>
                              {status.name}
                            </SelectItem>
                          ) : null
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="prioridade"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Prioridade*</FormLabel>
                    <FormControl>
                      <Select 
                        {...field}
                        onValueChange={(value) => {
                          field.onChange(value);
                          setPrioridade(value);
                        }}
                      >
                        <SelectTrigger className={formErrors.prioridade ? "border-destructive" : ""}>
                          <SelectValue placeholder="Selecione a prioridade" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="baixa">Baixa</SelectItem>
                          <SelectItem value="media">Média</SelectItem>
                          <SelectItem value="alta">Alta</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    {formErrors.prioridade && (
                      <FormMessage className="text-destructive">{formErrors.prioridade}</FormMessage>
                    )}
                  </FormItem>
                )}
              />
            </div>
            
            <FormField
              control={form.control}
              name="area"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Área*</FormLabel>
                  <FormControl>
                    <Select 
                      {...field}
                      onValueChange={(value: 'PRECATORIO' | 'RPV' | 'AMBOS') => {
                        field.onChange(value);
                        setArea(value);
                      }}
                    >
                      <SelectTrigger className={formErrors.area ? "border-destructive" : ""}>
                        <SelectValue placeholder="Selecione a área" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="PRECATORIO">Precatório</SelectItem>
                        <SelectItem value="RPV">RPV</SelectItem>
                        <SelectItem value="AMBOS">Ambos</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormControl>
                  {formErrors.area && (
                    <FormMessage className="text-destructive">{formErrors.area}</FormMessage>
                  )}
                </FormItem>
              )}
            />
            
            <div className="space-y-2">
              <FormField
                control={form.control}
                name="responsavel"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Responsável</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value || "sem_responsavel"}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione o responsável" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="sem_responsavel">Sem responsável</SelectItem>
                        {usuarios.map((user) => (
                          user && user.id ? (
                            <SelectItem key={user.id} value={user.id}>
                              {user.nome || user.email}
                            </SelectItem>
                          ) : null
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            
            <div className="space-y-2">
              <FormField
                control={form.control}
                name="prazo"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Data Limite</FormLabel>
                    <FormControl>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full justify-start text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {field.value ? (
                              format(new Date(field.value), "dd/MM/yyyy", { locale: ptBR })
                            ) : (
                              <span>Selecione uma data</span>
                            )}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value ? new Date(field.value) : undefined}
                            onSelect={(date) => {
                              field.onChange(date?.toISOString().split('T')[0]);
                            }}
                            initialFocus
                            locale={ptBR}
                          />
                        </PopoverContent>
                      </Popover>
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
            
            <div className="space-y-2">
              <FormField
                control={form.control}
                name="tags"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tags</FormLabel>
                    <FormControl>
                      <div className="flex space-x-2">
                        <Input
                          value={novaTag}
                          onChange={(e) => setNovaTag(e.target.value)}
                          placeholder="Adicionar tag"
                          className="flex-1"
                        />
                        <Button 
                          type="button" 
                          onClick={handleAddTag} 
                          disabled={!novaTag?.trim()}
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>
                    </FormControl>
                    {tags.length > 0 && (
                      <div className="flex flex-wrap gap-2 mt-2">
                        {tags.map((tag) => (
                          <div
                            key={tag}
                            className="bg-muted text-muted-foreground px-2 py-1 rounded-md flex items-center text-sm"
                          >
                            {tag}
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              className="h-4 w-4 p-0 ml-1"
                              onClick={() => handleRemoveTag(tag)}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    )}
                  </FormItem>
                )}
              />
            </div>
            
            <div className="space-y-2">
              <FormField
                control={form.control}
                name="subtarefas"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Subtarefas</FormLabel>
                    <FormControl>
                      <div className="flex space-x-2">
                        <Input
                          value={novaSubtarefa}
                          onChange={(e) => setNovaSubtarefa(e.target.value)}
                          placeholder="Adicionar subtarefa"
                          className="flex-1"
                        />
                        <Button 
                          type="button" 
                          onClick={handleAddSubtarefa} 
                          disabled={!novaSubtarefa?.trim()}
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>
                    </FormControl>
                    {subtarefas.length > 0 && (
                      <div className="space-y-2 mt-2">
                        {subtarefas.map((subtarefa) => (
                          <div
                            key={subtarefa.id}
                            className="bg-muted p-2 rounded-md flex items-center justify-between"
                          >
                            <span>{subtarefa.titulo}</span>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => handleRemoveSubtarefa(subtarefa.id)}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    )}
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancelar
            </Button>
            <Button type="submit" disabled={isLoading}>
              {mode === "create" ? "Criar Tarefa" : "Salvar Alterações"}
            </Button>
          </CardFooter>
        </Form>
      </form>
    </Card>
  );
};