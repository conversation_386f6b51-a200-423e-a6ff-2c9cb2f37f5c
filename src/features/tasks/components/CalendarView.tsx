import { useState } from "react";
import { 
  Calendar, 
  ChevronLeft, 
  ChevronRight, 
  MoreHorizontal 
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Tarefa } from "../types";
import { formatDate } from "../utils/taskUtils";

interface CalendarViewProps {
  tasks: Tarefa[];
  onTaskClick: (task: Tarefa) => void;
}

export function CalendarView({ tasks, onTaskClick }: CalendarViewProps) {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  
  // Obter o primeiro dia do mês atual
  const firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
  
  // Obter o último dia do mês atual
  const lastDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
  
  // Obter o dia da semana do primeiro dia do mês (0 = Domingo, 1 = Segunda, etc.)
  const firstDayOfWeek = firstDayOfMonth.getDay();
  
  // Obter o número de dias no mês
  const daysInMonth = lastDayOfMonth.getDate();
  
  // Calcular o número de dias no mês anterior
  const prevMonthLastDay = new Date(currentDate.getFullYear(), currentDate.getMonth(), 0).getDate();

  // Gerar dias do mês anterior para completar a primeira semana
  const prevMonthDays = [];
  for (let i = firstDayOfWeek - 1; i >= 0; i--) {
    prevMonthDays.push(prevMonthLastDay - i);
  }
  
  // Gerar dias do mês atual
  const currentMonthDays = [];
  for (let i = 1; i <= daysInMonth; i++) {
    currentMonthDays.push(i);
  }
  
  // Gerar dias do próximo mês para completar a última semana
  const nextMonthDays = [];
  const remainingDays = (7 - ((prevMonthDays.length + currentMonthDays.length) % 7)) % 7;
  for (let i = 1; i <= remainingDays; i++) {
    nextMonthDays.push(i);
  }

  const prevMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1));
    setSelectedDate(null);
  };

  const nextMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1));
    setSelectedDate(null);
  };

  const goToCurrentMonth = () => {
    setCurrentDate(new Date());
    setSelectedDate(null);
  };

  // Verificar se uma data específica tem tarefas
  const getTasksForDate = (day: number, isPrevMonth = false, isNextMonth = false) => {
    let year = currentDate.getFullYear();
    let month = currentDate.getMonth();
    
    if (isPrevMonth) {
      if (month === 0) {
        month = 11;
        year--;
      } else {
        month--;
      }
    } else if (isNextMonth) {
      if (month === 11) {
        month = 0;
        year++;
      } else {
        month++;
      }
    }
    
    const dateToCheck = new Date(year, month, day);
    
    return tasks.filter(task => {
      const taskDate = new Date(task.prazo);
      return (
        taskDate.getDate() === dateToCheck.getDate() && 
        taskDate.getMonth() === dateToCheck.getMonth() && 
        taskDate.getFullYear() === dateToCheck.getFullYear()
      );
    });
  };

  const dayNames = ["Dom", "Seg", "Ter", "Qua", "Qui", "Sex", "Sáb"];
  const monthNames = [
    "Janeiro", "Fevereiro", "Março", "Abril", "Maio", "Junho",
    "Julho", "Agosto", "Setembro", "Outubro", "Novembro", "Dezembro"
  ];

  return (
    <div className="w-full">
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center">
          <Calendar className="h-5 w-5 mr-2" />
          <h2 className="text-xl font-semibold">
            {monthNames[currentDate.getMonth()]} {currentDate.getFullYear()}
          </h2>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={goToCurrentMonth}>
            Hoje
          </Button>
          <Button variant="outline" size="icon" onClick={prevMonth}>
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="icon" onClick={nextMonth}>
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
      
      <div className="grid grid-cols-7 gap-1">
        {dayNames.map((day, index) => (
          <div 
            key={index} 
            className="text-center font-medium py-2 border-b text-sm"
          >
            {day}
          </div>
        ))}
        
        {/* Dias do mês anterior */}
        {prevMonthDays.map((day) => {
          const tasksForDay = getTasksForDate(day, true);
          return (
            <div 
              key={`prev-${day}`} 
              className="min-h-[100px] p-1 border rounded-md text-muted-foreground bg-muted/30"
            >
              <div className="text-right mb-1">{day}</div>
              <div className="space-y-1">
                {tasksForDay.slice(0, 2).map((task) => (
                  <div 
                    key={task.id}
                    className="text-xs p-1 rounded-md bg-background cursor-pointer border truncate"
                    onClick={() => onTaskClick(task)}
                  >
                    {task.titulo}
                  </div>
                ))}
                {tasksForDay.length > 2 && (
                  <div className="text-xs text-center text-muted-foreground">
                    +{tasksForDay.length - 2} mais
                  </div>
                )}
              </div>
            </div>
          );
        })}
        
        {/* Dias do mês atual */}
        {currentMonthDays.map((day) => {
          const date = new Date(currentDate.getFullYear(), currentDate.getMonth(), day);
          const isToday = new Date().toDateString() === date.toDateString();
          const isSelected = selectedDate && selectedDate.toDateString() === date.toDateString();
          const tasksForDay = getTasksForDate(day);
          
          const statusColors = {
            pendente: "bg-orange-200 text-orange-700 dark:bg-orange-900/30 dark:text-orange-300",
            em_andamento: "bg-blue-200 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300",
            concluida: "bg-green-200 text-green-700 dark:bg-green-900/30 dark:text-green-300",
          };
          
          return (
            <div 
              key={`current-${day}`}
              className={`
                min-h-[100px] p-1 border rounded-md 
                ${isToday ? 'border-primary' : ''}
                ${isSelected ? 'bg-muted' : ''}
              `}
              onClick={() => setSelectedDate(date)}
            >
              <div className="flex justify-between items-center mb-1">
                <div 
                  className={`
                    h-6 w-6 flex items-center justify-center rounded-full text-sm
                    ${isToday ? 'bg-primary text-primary-foreground' : ''}
                  `}
                >
                  {day}
                </div>
                
                {tasksForDay.length > 0 && (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Tarefas para {day}/{currentDate.getMonth() + 1}</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      {tasksForDay.map((task) => (
                        <DropdownMenuItem 
                          key={task.id} 
                          onClick={() => onTaskClick(task)}
                        >
                          {task.titulo}
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                )}
              </div>
              
              <div className="space-y-1">
                {tasksForDay.slice(0, 3).map((task) => (
                  <div 
                    key={task.id}
                    className={`
                      text-xs p-1 rounded-md cursor-pointer truncate
                      ${statusColors[task.status]}
                    `}
                    onClick={(e) => {
                      e.stopPropagation();
                      onTaskClick(task);
                    }}
                  >
                    {task.titulo}
                  </div>
                ))}
                {tasksForDay.length > 3 && (
                  <div className="text-xs text-center text-muted-foreground">
                    +{tasksForDay.length - 3} mais
                  </div>
                )}
              </div>
            </div>
          );
        })}
        
        {/* Dias do próximo mês */}
        {nextMonthDays.map((day) => {
          const tasksForDay = getTasksForDate(day, false, true);
          return (
            <div 
              key={`next-${day}`} 
              className="min-h-[100px] p-1 border rounded-md text-muted-foreground bg-muted/30"
            >
              <div className="text-right mb-1">{day}</div>
              <div className="space-y-1">
                {tasksForDay.slice(0, 2).map((task) => (
                  <div 
                    key={task.id}
                    className="text-xs p-1 rounded-md bg-background cursor-pointer border truncate"
                    onClick={() => onTaskClick(task)}
                  >
                    {task.titulo}
                  </div>
                ))}
                {tasksForDay.length > 2 && (
                  <div className="text-xs text-center text-muted-foreground">
                    +{tasksForDay.length - 2} mais
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>
      
      {selectedDate && (
        <div className="mt-4 border rounded-md p-4">
          <h3 className="font-medium mb-2">
            Tarefas para {selectedDate.getDate()}/{selectedDate.getMonth() + 1}/{selectedDate.getFullYear()}
          </h3>
          <div className="space-y-2">
            {getTasksForDate(
              selectedDate.getDate(),
              selectedDate.getMonth() < currentDate.getMonth(),
              selectedDate.getMonth() > currentDate.getMonth()
            ).map((task) => (
              <div 
                key={task.id} 
                className="flex items-center justify-between p-2 border rounded-md hover:bg-muted cursor-pointer"
                onClick={() => onTaskClick(task)}
              >
                <div>
                  <div className="font-medium">{task.titulo}</div>
                  {task.descricao && (
                    <div className="text-sm text-muted-foreground line-clamp-1">
                      {task.descricao}
                    </div>
                  )}
                </div>
                <Badge variant="outline" className={
                  task.status === "pendente" ? "text-orange-500 bg-orange-100 dark:bg-orange-900/20 dark:text-orange-300" :
                  task.status === "em_andamento" ? "text-blue-500 bg-blue-100 dark:bg-blue-900/20 dark:text-blue-300" :
                  "text-green-500 bg-green-100 dark:bg-green-900/20 dark:text-green-300"
                }>
                  {task.status === "pendente" ? "Pendente" : 
                   task.status === "em_andamento" ? "Em Andamento" : "Concluída"}
                </Badge>
              </div>
            ))}
            {getTasksForDate(
              selectedDate.getDate(),
              selectedDate.getMonth() < currentDate.getMonth(),
              selectedDate.getMonth() > currentDate.getMonth()
            ).length === 0 && (
              <div className="text-center text-muted-foreground py-4">
                Nenhuma tarefa para esta data
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
} 