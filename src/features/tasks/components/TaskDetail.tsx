import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import {
  ArrowLeft,
  CheckCircle2,
  Circle,
  Clock,
  Copy,
  Edit,
  FileText,
  MessageSquare,
  MoreHorizontal,
  Paperclip,
  Send,
  Trash2,
  UserCircle2,
  X,
  UserPlus
} from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Tarefa, Subtarefa, Comentario, Usuario, ArquivoAnexo } from '../types';
import { format, isPast, isToday } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { participantesIniciais } from "../utils/mockData";
import { fetchUsers } from "@/services/tasksService";

interface TaskDetailProps {
  tarefa: Tarefa;
  onUpdateTask: (tarefa: Tarefa) => void;
  onDeleteTask: (tarefaId: string) => void;
  onBack: () => void;
  onEdit?: () => void;
}

export const TaskDetail: React.FC<TaskDetailProps> = ({
  tarefa,
  onUpdateTask,
  onDeleteTask,
  onBack,
  onEdit
}) => {
  const [activeTab, setActiveTab] = useState<string>("detalhes");
  const [novoComentario, setNovoComentario] = useState<string>("");
  const [novaSubtarefa, setNovaSubtarefa] = useState<string>("");
  const [showTransferModal, setShowTransferModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState<Usuario | null>(null);
  const [usuarios, setUsuarios] = useState<Usuario[]>([]);

  // Carregar usuários para transferência
  useEffect(() => {
    const carregarUsuarios = async () => {
      try {
        const users = await fetchUsers();
        const usuariosFormatados = users.map(user => ({
          id: user.id,
          nome: user.nome || user.email,
          email: user.email,
          avatar: "/avatars/default.jpg" // Avatar padrão
        }));
        setUsuarios(usuariosFormatados);
      } catch (error) {
        console.error("Erro ao carregar usuários:", error);
      }
    };

    carregarUsuarios();
  });

  // Função para formatar data
  const formatarData = (dateString: string) => {
    try {
      const data = new Date(dateString);
      if (isToday(data)) {
        return 'Hoje, ' + format(data, 'HH:mm', { locale: ptBR });
      } else if (isPast(data)) {
        return format(data, "dd 'de' MMMM 'às' HH:mm", { locale: ptBR });
      } else {
        return format(data, "dd 'de' MMMM 'às' HH:mm", { locale: ptBR });
      }
    } catch (error) {
      return 'Data inválida';
    }
  };

  // Função para atualizar status da tarefa
  const handleToggleTaskStatus = () => {
    const novoStatus = tarefa.status === 'concluida' ? 'pendente' : 'concluida';
    onUpdateTask({
      ...tarefa,
      status: novoStatus,
      progresso: novoStatus === 'concluida' ? 100 : tarefa.progresso
    });
  };

  // Função para atualizar status de subtarefa
  const handleToggleSubtarefaStatus = (subtarefa: Subtarefa) => {
    const subtarefasAtualizadas = tarefa.subtarefas.map(st =>
      st.id === subtarefa.id ? { ...st, concluida: !st.concluida } : st
    );

    // Calcular novo progresso geral com base nas subtarefas
    const total = subtarefasAtualizadas.length;
    const concluidas = subtarefasAtualizadas.filter(st => st.concluida).length;
    const novoProgresso = total > 0 ? Math.round((concluidas / total) * 100) : tarefa.progresso;

    onUpdateTask({
      ...tarefa,
      subtarefas: subtarefasAtualizadas,
      progresso: novoProgresso
    });
  };

  // Função para adicionar subtarefa
  const handleAddSubtarefa = () => {
    if (!novaSubtarefa.trim()) return;

    const novaSubtarefaObj: Subtarefa = {
      id: `subtarefa-${Date.now()}`,
      titulo: novaSubtarefa,
      concluida: false
    };

    const subtarefasAtualizadas = [...tarefa.subtarefas, novaSubtarefaObj];

    // Recalcular progresso
    const total = subtarefasAtualizadas.length;
    const concluidas = subtarefasAtualizadas.filter(st => st.concluida).length;
    const novoProgresso = Math.round((concluidas / total) * 100);

    onUpdateTask({
      ...tarefa,
      subtarefas: subtarefasAtualizadas,
      progresso: novoProgresso
    });

    setNovaSubtarefa("");
  };

  // Função para remover subtarefa
  const handleRemoveSubtarefa = (subtarefaId: string) => {
    const subtarefasAtualizadas = tarefa.subtarefas.filter(st => st.id !== subtarefaId);

    // Recalcular progresso
    const total = subtarefasAtualizadas.length;
    const concluidas = subtarefasAtualizadas.filter(st => st.concluida).length;
    const novoProgresso = total > 0 ? Math.round((concluidas / total) * 100) : tarefa.progresso;

    onUpdateTask({
      ...tarefa,
      subtarefas: subtarefasAtualizadas,
      progresso: novoProgresso
    });
  };

  // Função para adicionar comentário
  const handleAddComentario = () => {
    if (!novoComentario.trim()) return;

    // Mock do usuário (idealmente seria o usuário logado)
    const autor = {
      id: "user-1",
      nome: "Carlos Silva",
      email: "<EMAIL>",
      avatar: "/avatars/carlos.jpg"
    };

    const novoComentarioObj: Comentario = {
      id: `comentario-${Date.now()}`,
      texto: novoComentario,
      autor: autor,
      dataCriacao: new Date().toISOString()
    };

    onUpdateTask({
      ...tarefa,
      comentarios: [...tarefa.comentarios, novoComentarioObj]
    });

    setNovoComentario("");
  };

  // Função para renderizar ícone de status
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'concluida':
        return <CheckCircle2 className="h-5 w-5 text-green-500" />;
      case 'em_andamento':
        return <Clock className="h-5 w-5 text-blue-500" />;
      case 'pendente':
      default:
        return <Circle className="h-5 w-5 text-gray-400" />;
    }
  };

  // Função para obter texto do status
  const getStatusText = (status: string) => {
    switch (status) {
      case 'concluida':
        return 'Concluída';
      case 'em_andamento':
        return 'Em andamento';
      case 'pendente':
        return 'Pendente';
      default:
        return status;
    }
  };

  // Renderizar badge de prioridade
  const renderPrioridadeBadge = (prioridade: string) => {
    switch (prioridade) {
      case 'alta':
        return <Badge variant="destructive">Alta</Badge>;
      case 'media':
        return <Badge variant="default" className="bg-orange-500">Média</Badge>;
      case 'baixa':
        return <Badge variant="outline" className="text-blue-500 border-blue-500">Baixa</Badge>;
      default:
        return null;
    }
  };

  // Função para obter o nome de exibição do usuário
  const getUserDisplayName = (user: Usuario) => {
    return user.nome || user.email || "Usuário sem nome";
  };

  // Função para transferir tarefa
  const handleTransferTask = () => {
    if (selectedUser) {
      const updatedTask = {
        ...tarefa,
        responsavel: selectedUser
      };
      onUpdateTask(updatedTask);
      setShowTransferModal(false);
      setSelectedUser(null);
    }
  };

  return (
    <div className="space-y-4">
      <div className="mb-2">
        <div className="flex justify-between items-center">
          <h2 className="text-2xl font-bold">{tarefa.titulo}</h2>
        </div>
        {tarefa.processoVinculado && (
          <p className="text-sm text-muted-foreground">
            Processo: {tarefa.processoVinculado}
          </p>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
        <div className="lg:col-span-2 space-y-4">
          <Card>
            <CardContent className="p-4">
              <div className="space-y-6">
                <div>
                  <p className="text-sm text-muted-foreground mb-4">
                    {tarefa.descricao}
                  </p>

                  <div className="flex flex-wrap gap-2 mb-4">
                    {tarefa.tags?.map((tag, index) => (
                      <Badge key={index} variant="secondary">
                        {tag}
                      </Badge>
                    ))}
                  </div>

                  <div className="flex items-center gap-4 flex-wrap">
                    <div className="flex items-center gap-2">
                      <div
                        className="cursor-pointer"
                        onClick={handleToggleTaskStatus}
                        title={tarefa.status === 'concluida' ? 'Marcar como pendente' : 'Marcar como concluída'}
                      >
                        {getStatusIcon(tarefa.status)}
                      </div>
                      <span>{getStatusText(tarefa.status)}</span>
                    </div>

                    <Separator orientation="vertical" className="h-6" />

                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium">Prioridade:</span>
                      {renderPrioridadeBadge(tarefa.prioridade)}
                    </div>

                    <Separator orientation="vertical" className="h-6" />

                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium">Progresso:</span>
                      <div className="flex items-center gap-2 w-40">
                        <Progress value={tarefa.progresso} className="h-2" />
                        <span className="text-xs text-muted-foreground">{tarefa.progresso}%</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex justify-end gap-2">
                  <Button variant="outline" size="sm" onClick={onEdit}>
                    <Edit className="mr-2 h-4 w-4" />
                    Editar
                  </Button>

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" size="sm">
                        Ações <MoreHorizontal className="ml-2 h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem className="cursor-pointer">
                        <Copy className="mr-2 h-4 w-4" />
                        <span>Duplicar</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        className="cursor-pointer text-red-600"
                        onClick={() => {
                          onDeleteTask(tarefa.id);
                          onBack();
                        }}
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        <span>Excluir</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => setShowTransferModal(true)}>
                        <UserPlus className="mr-2 h-4 w-4" />
                        <span>Transferir tarefa</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>

                <Separator />

                <Tabs defaultValue="subtarefas" className="w-full" onValueChange={setActiveTab}>
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="subtarefas">Subtarefas</TabsTrigger>
                    <TabsTrigger value="comentarios">Comentários</TabsTrigger>
                    <TabsTrigger value="anexos">Anexos</TabsTrigger>
                  </TabsList>

                  <TabsContent value="subtarefas" className="space-y-4 pt-4">
                    <div className="flex items-center gap-2">
                      <Textarea
                        placeholder="Adicionar nova subtarefa..."
                        value={novaSubtarefa}
                        onChange={(e) => setNovaSubtarefa(e.target.value)}
                        className="h-10 py-2"
                      />
                      <Button onClick={handleAddSubtarefa}>Adicionar</Button>
                    </div>

                    <Card>
                      <CardContent className="p-4">
                        {tarefa.subtarefas.length === 0 ? (
                          <p className="text-center py-4 text-muted-foreground">
                            Nenhuma subtarefa adicionada.
                          </p>
                        ) : (
                          <ScrollArea className="h-[300px]">
                            <div className="space-y-3 pr-3">
                              {tarefa.subtarefas.map((subtarefa) => (
                                <div key={subtarefa.id} className="flex items-start gap-2 pb-3 border-b">
                                  <Checkbox
                                    checked={subtarefa.concluida}
                                    onCheckedChange={() => handleToggleSubtarefaStatus(subtarefa)}
                                    id={`subtarefa-${subtarefa.id}`}
                                  />
                                  <div className="flex-1">
                                    <label
                                      htmlFor={`subtarefa-${subtarefa.id}`}
                                      className={`text-sm font-medium cursor-pointer ${subtarefa.concluida ? 'line-through text-muted-foreground' : ''}`}
                                    >
                                      {subtarefa.titulo}
                                    </label>
                                    {subtarefa.prazo && (
                                      <div className="text-xs text-muted-foreground mt-1 flex items-center gap-1">
                                        <Clock className="h-3 w-3" />
                                        {formatarData(subtarefa.prazo)}
                                      </div>
                                    )}
                                  </div>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-6 w-6 p-0 text-muted-foreground hover:text-destructive"
                                    onClick={() => handleRemoveSubtarefa(subtarefa.id)}
                                  >
                                    <Trash2 className="h-3 w-3" />
                                  </Button>
                                </div>
                              ))}
                            </div>
                          </ScrollArea>
                        )}
                      </CardContent>
                    </Card>
                  </TabsContent>

                  <TabsContent value="comentarios" className="space-y-4 pt-4">
                    <div className="flex items-stretch gap-2">
                      <Textarea
                        placeholder="Adicionar comentário..."
                        value={novoComentario}
                        onChange={(e) => setNovoComentario(e.target.value)}
                      />
                      <Button onClick={handleAddComentario} className="h-auto">
                        <Send className="h-4 w-4" />
                      </Button>
                    </div>

                    <Card>
                      <CardContent className="p-4">
                        {tarefa.comentarios.length === 0 ? (
                          <p className="text-center py-4 text-muted-foreground">
                            Nenhum comentário adicionado.
                          </p>
                        ) : (
                          <ScrollArea className="h-[300px]">
                            <div className="space-y-4 pr-3">
                              {tarefa.comentarios.map((comentario, index) => (
                                <div key={index} className="flex gap-3 pb-4 border-b">
                                  <Avatar className="h-8 w-8">
                                    <AvatarImage src={comentario.autor.avatar} alt={comentario.autor.nome} />
                                    <AvatarFallback>
                                      {comentario.autor.nome.split(' ').map((n: string) => n[0]).join('').toUpperCase()}
                                    </AvatarFallback>
                                  </Avatar>
                                  <div className="flex-1">
                                    <div className="flex justify-between items-center">
                                      <span className="font-medium text-sm">{comentario.autor.nome}</span>
                                      <span className="text-xs text-muted-foreground">
                                        {formatarData(comentario.dataCriacao)}
                                      </span>
                                    </div>
                                    <p className="text-sm mt-1">
                                      {comentario.texto}
                                    </p>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </ScrollArea>
                        )}
                      </CardContent>
                    </Card>
                  </TabsContent>

                  <TabsContent value="anexos" className="space-y-4 pt-4">
                    <div className="flex items-center gap-2">
                      <Button>
                        <Paperclip className="h-4 w-4 mr-1" />
                        Anexar Arquivo
                      </Button>
                    </div>

                    <Card>
                      <CardContent className="p-4">
                        {(!tarefa.arquivosAnexos || tarefa.arquivosAnexos.length === 0) ? (
                          <p className="text-center py-4 text-muted-foreground">
                            Nenhum arquivo anexado.
                          </p>
                        ) : (
                          <ScrollArea className="h-[300px]">
                            <div className="space-y-3 pr-3">
                              {tarefa.arquivosAnexos.map((arquivo, index) => (
                                <div key={index} className="flex items-center gap-3 pb-3 border-b">
                                  <div className="bg-muted p-2 rounded-md">
                                    <FileText className="h-5 w-5" />
                                  </div>
                                  <div className="flex-1">
                                    <p className="text-sm font-medium">{arquivo.nome}</p>
                                    <div className="flex items-center text-xs text-muted-foreground gap-2">
                                      <span>{arquivo.tipo.toUpperCase()}</span>
                                      <span>•</span>
                                      <span>{arquivo.tamanho}</span>
                                      <span>•</span>
                                      <span>{formatarData(arquivo.dataUpload)}</span>
                                    </div>
                                  </div>
                                  <Button variant="ghost" size="sm">
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              ))}
                            </div>
                          </ScrollArea>
                        )}
                      </CardContent>
                    </Card>
                  </TabsContent>
                </Tabs>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="space-y-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-md">Responsável</CardTitle>
            </CardHeader>
            <CardContent>
              {tarefa.responsavel ? (
                <div className="flex items-center gap-3">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={tarefa.responsavel.avatar} alt={tarefa.responsavel.nome} />
                    <AvatarFallback>
                      {tarefa.responsavel.nome.split(' ').map(n => n[0]).join('').toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium">{tarefa.responsavel.nome}</p>
                  </div>
                </div>
              ) : (
                <div className="flex items-center gap-2 text-muted-foreground">
                  <UserCircle2 className="h-5 w-5" />
                  <span>Nenhum responsável atribuído</span>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-md">Participantes</CardTitle>
            </CardHeader>
            <CardContent>
              {tarefa.participantes && tarefa.participantes.length > 0 ? (
                <div className="flex flex-wrap gap-2">
                  {tarefa.participantes.map(participante => (
                    <Avatar key={participante.id} className="h-8 w-8" title={participante.nome}>
                      <AvatarImage src={participante.avatar} alt={participante.nome} />
                      <AvatarFallback>
                        {participante.nome.split(' ').map(n => n[0]).join('').toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                  ))}
                </div>
              ) : (
                <div className="text-muted-foreground">
                  Nenhum participante adicionado
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-md">Datas</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid grid-cols-2 gap-1 text-sm">
                <span className="text-muted-foreground">Criada em:</span>
                <span>{formatarData(tarefa.dataCriacao)}</span>
              </div>

              {tarefa.prazo && (
                <div className="grid grid-cols-2 gap-1 text-sm">
                  <span className="text-muted-foreground">Prazo:</span>
                  <span>{formatarData(tarefa.prazo)}</span>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="flex justify-between items-center">
                <span className="text-md">Estatísticas</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid grid-cols-2 gap-1 text-sm">
                <span className="text-muted-foreground">Subtarefas:</span>
                <span>{tarefa.subtarefas.filter(st => st.concluida).length}/{tarefa.subtarefas.length}</span>
              </div>

              <div className="grid grid-cols-2 gap-1 text-sm">
                <span className="text-muted-foreground">Comentários:</span>
                <span>{tarefa.comentarios.length}</span>
              </div>

              <div className="grid grid-cols-2 gap-1 text-sm">
                <span className="text-muted-foreground">Anexos:</span>
                <span>{tarefa.arquivosAnexos?.length || 0}</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-md">Ações Rápidas</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button className="w-full" onClick={handleToggleTaskStatus}>
                {tarefa.status === 'concluida' ? 'Marcar como Pendente' : 'Marcar como Concluída'}
              </Button>

              <Button variant="outline" className="w-full" onClick={onEdit}>
                <Edit className="h-4 w-4 mr-1" />
                Editar Tarefa
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Modal de transferência de tarefa */}
      {showTransferModal && (
        <Dialog open={showTransferModal} onOpenChange={setShowTransferModal}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Transferir tarefa</DialogTitle>
              <DialogDescription>
                Selecione o usuário para quem deseja transferir esta tarefa.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="user">Usuário</Label>
                <Select onValueChange={(value) => {
                  const user = usuarios.find(p => p.id === value);
                  if (user) setSelectedUser(user);
                }}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione um usuário" />
                  </SelectTrigger>
                  <SelectContent>
                    {usuarios.length > 0 ? (
                      usuarios.map((usuario) => (
                        <SelectItem key={usuario.id} value={usuario.id}>
                          <div className="flex items-center">
                            <Avatar className="h-6 w-6 mr-2">
                              <AvatarImage src={usuario.avatar || "/avatars/default.jpg"} alt={getUserDisplayName(usuario)} />
                              <AvatarFallback>{getUserDisplayName(usuario).charAt(0)}</AvatarFallback>
                            </Avatar>
                            {getUserDisplayName(usuario)}
                          </div>
                        </SelectItem>
                      ))
                    ) : (
                      <SelectItem value="loading" disabled>
                        Carregando usuários...
                      </SelectItem>
                    )}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowTransferModal(false)}>
                Cancelar
              </Button>
              <Button onClick={handleTransferTask} disabled={!selectedUser}>
                Transferir
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};