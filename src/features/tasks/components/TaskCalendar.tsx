import React, { useState, useEffect } from 'react';
import { Calendar } from "@/components/ui/calendar";
import { Card, CardContent } from "@/components/ui/card";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Copy, 
  Eye,
  CheckCircle2,
  Clock,
  Circle
} from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from "@/components/ui/dialog";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Tarefa } from '../types';
import { format, isSameDay, isSameMonth, parseISO } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface TaskCalendarProps {
  tarefas: Tarefa[];
  onUpdateTask: (tarefa: Tarefa) => void;
}

export const TaskCalendar: React.FC<TaskCalendarProps> = ({ tarefas, onUpdateTask }) => {
  const [date, setDate] = useState<Date>(new Date());
  const [calendarTasks, setCalendarTasks] = useState<{[key: string]: Tarefa[]}>({});
  const [selectedDayTasks, setSelectedDayTasks] = useState<Tarefa[]>([]);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedTask, setSelectedTask] = useState<Tarefa | null>(null);
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  
  // Agrupar tarefas por data
  useEffect(() => {
    const tasksMap: {[key: string]: Tarefa[]} = {};
    
    tarefas.forEach(tarefa => {
      if (tarefa.prazo) {
        try {
          const date = parseISO(tarefa.prazo);
          const dateStr = format(date, 'yyyy-MM-dd');
          
          if (!tasksMap[dateStr]) {
            tasksMap[dateStr] = [];
          }
          
          tasksMap[dateStr].push(tarefa);
        } catch (error) {
          console.error('Data inválida:', tarefa.prazo);
        }
      }
    });
    
    setCalendarTasks(tasksMap);
  }, [tarefas]);
  
  // Função para verificar se há tarefas em uma data específica
  const hasTasks = (day: Date) => {
    const dateStr = format(day, 'yyyy-MM-dd');
    return calendarTasks[dateStr] && calendarTasks[dateStr].length > 0;
  };
  
  // Função para renderizar conteúdo do dia no calendário
  const renderDayContent = (day: Date) => {
    const dateStr = format(day, 'yyyy-MM-dd');
    const tasksForDay = calendarTasks[dateStr] || [];
    
    if (tasksForDay.length === 0) return null;
    
    // Determinar prioridade mais alta para destacar
    let hasPrioridadeAlta = false;
    let hasPrioridadeMedia = false;
    
    tasksForDay.forEach(tarefa => {
      if (tarefa.prioridade === 'alta') hasPrioridadeAlta = true;
      if (tarefa.prioridade === 'media') hasPrioridadeMedia = true;
    });
    
    let badgeClass = 'bg-blue-500';
    if (hasPrioridadeAlta) {
      badgeClass = 'bg-red-500';
    } else if (hasPrioridadeMedia) {
      badgeClass = 'bg-orange-500';
    }
    
    return (
      <div className="flex flex-col items-center">
        <Badge className={`${badgeClass} -mt-1`}>{tasksForDay.length}</Badge>
      </div>
    );
  };
  
  // Função para lidar com o clique em um dia
  const handleDayClick = (day: Date) => {
    const dateStr = format(day, 'yyyy-MM-dd');
    const tasksForDay = calendarTasks[dateStr] || [];
    setSelectedDayTasks(tasksForDay);
    setSelectedDate(day);
  };
  
  // Função para abrir diálogo de detalhes da tarefa
  const handleTaskClick = (tarefa: Tarefa) => {
    setSelectedTask(tarefa);
    setIsDialogOpen(true);
  };
  
  // Função para atualizar status da tarefa
  const handleToggleTaskStatus = (tarefa: Tarefa) => {
    const novoStatus = tarefa.status === 'concluida' ? 'pendente' : 'concluida';
    onUpdateTask({
      ...tarefa,
      status: novoStatus,
      progresso: novoStatus === 'concluida' ? 100 : tarefa.progresso
    });
  };
  
  // Renderizar ícone de status
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'concluida':
        return <CheckCircle2 className="h-5 w-5 text-green-500" />;
      case 'em_andamento':
        return <Clock className="h-5 w-5 text-blue-500" />;
      case 'pendente':
      default:
        return <Circle className="h-5 w-5 text-gray-400" />;
    }
  };
  
  // Função para formatar o título da lista de tarefas do dia
  const getDayTitle = (date: Date | null) => {
    if (!date) return 'Tarefas do dia';
    
    // Verificar se é hoje, amanhã, etc.
    const today = new Date();
    if (isSameDay(date, today)) {
      return 'Tarefas de Hoje';
    } else {
      return `Tarefas de ${format(date, 'dd/MM/yyyy', { locale: ptBR })}`;
    }
  };
  
  return (
    <div className="flex flex-col md:flex-row gap-4 h-full">
      <div className="md:w-7/12">
        <Calendar
          mode="single"
          selected={date}
          onSelect={(date) => {
            if (date) {
              setDate(date);
              handleDayClick(date);
            }
          }}
          month={date}
          onMonthChange={setDate}
          className="rounded-md border h-full"
          locale={ptBR}
          components={{
            DayContent: ({ day }) => {
              // Verificar se a data é válida
              if (!day || isNaN(day.getTime())) {
                return <div>-</div>;
              }
              
              return (
                <div 
                  className="w-full h-full flex flex-col items-center"
                  onClick={() => handleDayClick(day)}
                >
                  <div>{format(day, 'd')}</div>
                  {isSameMonth(day, date) && renderDayContent(day)}
                </div>
              );
            }
          }}
          classNames={{
            day_today: "bg-primary text-primary-foreground",
            day_selected: "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground"
          }}
        />
      </div>
      
      <div className="md:w-5/12">
        <Card className="h-full">
          <CardContent className="p-4 h-full flex flex-col">
            <h3 className="font-semibold text-lg mb-4">
              {getDayTitle(selectedDate)}
            </h3>
            
            <ScrollArea className="flex-1">
              {selectedDayTasks.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">
                    Não há tarefas agendadas para este dia.
                  </p>
                </div>
              ) : (
                <div className="space-y-3">
                  {selectedDayTasks.map(tarefa => (
                    <Card key={tarefa.id} className="p-3">
                      <div className="flex gap-3">
                        <div 
                          className="cursor-pointer"
                          onClick={() => handleToggleTaskStatus(tarefa)}
                          title={tarefa.status === 'concluida' ? 'Marcar como pendente' : 'Marcar como concluída'}
                        >
                          {getStatusIcon(tarefa.status)}
                        </div>
                        
                        <div className="flex-1">
                          <div 
                            className="font-medium cursor-pointer hover:text-primary"
                            onClick={() => handleTaskClick(tarefa)}
                          >
                            {tarefa.titulo}
                          </div>
                          <div className="text-sm text-muted-foreground mt-1">
                            {tarefa.descricao}
                          </div>
                          
                          <div className="flex flex-wrap gap-2 mt-2">
                            {tarefa.tags?.map((tag, index) => (
                              <Badge key={index} variant="secondary" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                          
                          <div className="flex justify-between items-center mt-3">
                            {tarefa.responsavel ? (
                              <div className="flex items-center gap-2">
                                <Avatar className="h-6 w-6">
                                  <AvatarImage src={tarefa.responsavel.avatar} alt={tarefa.responsavel.nome} />
                                  <AvatarFallback>
                                    {tarefa.responsavel.nome.split(' ').map(n => n[0]).join('').toUpperCase()}
                                  </AvatarFallback>
                                </Avatar>
                                <span className="text-xs">{tarefa.responsavel.nome}</span>
                              </div>
                            ) : (
                              <span className="text-xs text-muted-foreground">Não atribuído</span>
                            )}
                            
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem className="cursor-pointer" onClick={() => handleTaskClick(tarefa)}>
                                  <Eye className="mr-2 h-4 w-4" />
                                  <span>Ver Detalhes</span>
                                </DropdownMenuItem>
                                <DropdownMenuItem className="cursor-pointer">
                                  <Edit className="mr-2 h-4 w-4" />
                                  <span>Editar</span>
                                </DropdownMenuItem>
                                <DropdownMenuItem className="cursor-pointer">
                                  <Copy className="mr-2 h-4 w-4" />
                                  <span>Duplicar</span>
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem className="cursor-pointer text-red-600">
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  <span>Excluir</span>
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              )}
            </ScrollArea>
          </CardContent>
        </Card>
      </div>
      
      {selectedTask && (
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>{selectedTask.titulo}</DialogTitle>
              <DialogDescription>
                {selectedTask.descricao}
              </DialogDescription>
            </DialogHeader>
            
            <div className="grid gap-4 py-4">
              <div className="flex flex-col gap-1">
                <span className="font-medium text-sm">Status:</span>
                <div className="flex items-center gap-2">
                  {getStatusIcon(selectedTask.status)}
                  <span>
                    {selectedTask.status === 'pendente' ? 'Pendente' : 
                     selectedTask.status === 'em_andamento' ? 'Em andamento' : 
                     'Concluída'}
                  </span>
                </div>
              </div>
              
              <div className="flex flex-col gap-1">
                <span className="font-medium text-sm">Responsável:</span>
                {selectedTask.responsavel ? (
                  <div className="flex items-center gap-2">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={selectedTask.responsavel.avatar} alt={selectedTask.responsavel.nome} />
                      <AvatarFallback>
                        {selectedTask.responsavel.nome.split(' ').map(n => n[0]).join('').toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <span>{selectedTask.responsavel.nome}</span>
                  </div>
                ) : (
                  <span className="text-muted-foreground">Não atribuído</span>
                )}
              </div>
              
              {selectedTask.participantes && selectedTask.participantes.length > 0 && (
                <div className="flex flex-col gap-1">
                  <span className="font-medium text-sm">Participantes:</span>
                  <div className="flex gap-2">
                    {selectedTask.participantes.map(participante => (
                      <Avatar key={participante.id} className="h-8 w-8" title={participante.nome}>
                        <AvatarImage src={participante.avatar} alt={participante.nome} />
                        <AvatarFallback>
                          {participante.nome.split(' ').map(n => n[0]).join('').toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                    ))}
                  </div>
                </div>
              )}
              
              <div className="flex flex-col gap-1">
                <span className="font-medium text-sm">Prazo:</span>
                <span>
                  {selectedTask.prazo ? format(parseISO(selectedTask.prazo), "dd 'de' MMMM 'de' yyyy", { locale: ptBR }) : 'Sem prazo definido'}
                </span>
              </div>
              
              {selectedTask.processoVinculado && (
                <div className="flex flex-col gap-1">
                  <span className="font-medium text-sm">Processo Vinculado:</span>
                  <span>{selectedTask.processoVinculado}</span>
                </div>
              )}
              
              {selectedTask.tags && selectedTask.tags.length > 0 && (
                <div className="flex flex-col gap-1">
                  <span className="font-medium text-sm">Tags:</span>
                  <div className="flex flex-wrap gap-2">
                    {selectedTask.tags.map((tag, index) => (
                      <Badge key={index} variant="secondary">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
              
              {selectedTask.subtarefas && selectedTask.subtarefas.length > 0 && (
                <div className="flex flex-col gap-1">
                  <span className="font-medium text-sm">Subtarefas:</span>
                  <div className="space-y-2">
                    {selectedTask.subtarefas.map(subtarefa => (
                      <div key={subtarefa.id} className="flex items-center gap-2">
                        {subtarefa.concluida ? (
                          <CheckCircle2 className="h-4 w-4 text-green-500" />
                        ) : (
                          <Circle className="h-4 w-4 text-gray-400" />
                        )}
                        <span className={subtarefa.concluida ? "line-through text-muted-foreground" : ""}>
                          {subtarefa.titulo}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
            
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                Fechar
              </Button>
              <Button onClick={() => {
                handleToggleTaskStatus(selectedTask);
                setIsDialogOpen(false);
              }}>
                {selectedTask.status === 'concluida' ? 'Marcar como Pendente' : 'Marcar como Concluída'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}; 