import React from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON> 
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { 
  <PERSON><PERSON>hart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line
} from 'recharts';
import { 
  CheckCircle2, 
  Clock, 
  AlertTriangle, 
  Activity, 
  Calendar, 
  User, 
  Tag, 
  BarChart2,
  FileText,
  TrendingUp,
  Target
} from "lucide-react";
import { TaskMetrics as TaskMetricsType } from '../types';
import { estatisticasTarefas } from '../utils/mockData';

interface TaskMetricsProps {
  metrics: TaskMetricsType;
}

export const TaskMetrics: React.FC<TaskMetricsProps> = ({ metrics }) => {
  // Dados para o gráfico de status
  const statusData = [
    { name: '<PERSON>dent<PERSON>', value: metrics.pendentes, fill: '#f97316' },
    { name: '<PERSON> Andamento', value: metrics.emAndamento, fill: '#3b82f6' },
    { name: 'Concluídas', value: metrics.concluidas, fill: '#22c55e' },
    { name: 'Atrasadas', value: metrics.atrasadas, fill: '#ef4444' }
  ];
  
  // Dados para o gráfico de prioridade
  const prioridadeData = [
    { name: 'Alta', value: metrics.altaPrioridade, fill: '#ef4444' },
    { name: 'Média', value: metrics.mediaPrioridade, fill: '#f97316' },
    { name: 'Baixa', value: metrics.baixaPrioridade, fill: '#3b82f6' }
  ];
  
  // Dados para o gráfico de progresso por semana
  const progressoSemanal = [
    { name: 'Seg', concluidas: 2, emAndamento: 3, pendentes: 1 },
    { name: 'Ter', concluidas: 3, emAndamento: 2, pendentes: 2 },
    { name: 'Qua', concluidas: 4, emAndamento: 1, pendentes: 1 },
    { name: 'Qui', concluidas: 1, emAndamento: 3, pendentes: 2 },
    { name: 'Sex', concluidas: 5, emAndamento: 2, pendentes: 0 },
    { name: 'Sáb', concluidas: 2, emAndamento: 1, pendentes: 0 },
    { name: 'Dom', concluidas: 0, emAndamento: 0, pendentes: 0 }
  ];
  
  // Dados para o gráfico de tarefas por mês
  const tarefasPorMes = [
    { name: 'Jan', total: 18, concluidas: 15 },
    { name: 'Fev', total: 22, concluidas: 20 },
    { name: 'Mar', total: 25, concluidas: 21 },
    { name: 'Abr', total: 20, concluidas: 16 },
    { name: 'Mai', total: 28, concluidas: 24 },
    { name: 'Jun', total: 24, concluidas: 19 },
    { name: 'Jul', total: 30, concluidas: 25 },
    { name: 'Ago', total: 28, concluidas: 23 },
    { name: 'Set', total: 32, concluidas: 27 },
    { name: 'Out', total: 35, concluidas: 30 },
    { name: 'Nov', total: 30, concluidas: 0 },
    { name: 'Dez', total: 0, concluidas: 0 }
  ];
  
  // Cores para os gráficos de pizza
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8'];
  
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {estatisticasTarefas.map((stat, index) => (
          <Card key={index}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between space-y-0 pb-2">
                <h3 className="tracking-tight text-sm font-medium">{stat.titulo}</h3>
                <div className={`p-2 rounded-full ${
                  stat.icone === 'FileText' ? 'bg-blue-100 text-blue-600' :
                  stat.icone === 'Activity' ? 'bg-orange-100 text-orange-600' :
                  stat.icone === 'Target' ? 'bg-green-100 text-green-600' :
                  'bg-purple-100 text-purple-600'
                }`}>
                  {stat.icone === 'FileText' && <FileText className="h-4 w-4" />}
                  {stat.icone === 'Activity' && <Activity className="h-4 w-4" />}
                  {stat.icone === 'Target' && <Target className="h-4 w-4" />}
                  {stat.icone === 'TrendingUp' && <TrendingUp className="h-4 w-4" />}
                </div>
              </div>
              <div className="text-2xl font-bold">{stat.valor}</div>
              <div className="text-xs text-muted-foreground">{stat.descricao}</div>
              <Progress className="mt-3" value={stat.progresso} />
              <div className="flex items-center pt-2">
                <span className={`text-xs ${
                  stat.variacao.startsWith('+') ? 'text-green-500' : 'text-red-500'
                }`}>
                  {stat.variacao}
                </span>
                <span className="text-xs text-muted-foreground ml-1">desde o último mês</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
      
      <Tabs defaultValue="visaoGeral">
        <TabsList className="mb-4">
          <TabsTrigger value="visaoGeral">
            <BarChart2 className="h-4 w-4 mr-2" />
            Visão Geral
          </TabsTrigger>
          <TabsTrigger value="status">
            <Activity className="h-4 w-4 mr-2" />
            Status
          </TabsTrigger>
          <TabsTrigger value="prioridade">
            <AlertTriangle className="h-4 w-4 mr-2" />
            Prioridade
          </TabsTrigger>
          <TabsTrigger value="semanal">
            <Calendar className="h-4 w-4 mr-2" />
            Análise Semanal
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="visaoGeral" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Tarefas por Status</CardTitle>
              </CardHeader>
              <CardContent className="h-72">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={statusData}
                      cx="50%"
                      cy="50%"
                      labelLine={true}
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      outerRadius={90}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {statusData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.fill} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => [`${value} tarefas`, 'Quantidade']} />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Progresso Mensal</CardTitle>
              </CardHeader>
              <CardContent className="h-72">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={tarefasPorMes}
                    margin={{
                      top: 5,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line type="monotone" dataKey="total" stroke="#8884d8" name="Total" />
                    <Line type="monotone" dataKey="concluidas" stroke="#82ca9d" name="Concluídas" />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
          
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Resumo de Tarefas</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="flex flex-col items-center justify-center p-4 bg-gray-50 rounded-lg">
                  <div className="mb-2 p-2 rounded-full bg-green-100">
                    <CheckCircle2 className="h-5 w-5 text-green-600" />
                  </div>
                  <div className="text-2xl font-bold">{metrics.concluidas}</div>
                  <div className="text-sm text-muted-foreground">Concluídas</div>
                </div>
                
                <div className="flex flex-col items-center justify-center p-4 bg-gray-50 rounded-lg">
                  <div className="mb-2 p-2 rounded-full bg-blue-100">
                    <Clock className="h-5 w-5 text-blue-600" />
                  </div>
                  <div className="text-2xl font-bold">{metrics.emAndamento}</div>
                  <div className="text-sm text-muted-foreground">Em Andamento</div>
                </div>
                
                <div className="flex flex-col items-center justify-center p-4 bg-gray-50 rounded-lg">
                  <div className="mb-2 p-2 rounded-full bg-orange-100">
                    <Clock className="h-5 w-5 text-orange-600" />
                  </div>
                  <div className="text-2xl font-bold">{metrics.pendentes}</div>
                  <div className="text-sm text-muted-foreground">Pendentes</div>
                </div>
                
                <div className="flex flex-col items-center justify-center p-4 bg-gray-50 rounded-lg">
                  <div className="mb-2 p-2 rounded-full bg-red-100">
                    <AlertTriangle className="h-5 w-5 text-red-600" />
                  </div>
                  <div className="text-2xl font-bold">{metrics.atrasadas}</div>
                  <div className="text-sm text-muted-foreground">Atrasadas</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="status">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Distribuição de Status</CardTitle>
            </CardHeader>
            <CardContent className="h-96">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={statusData}
                  margin={{
                    top: 20,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip formatter={(value) => [`${value} tarefas`, 'Quantidade']} />
                  <Legend />
                  <Bar dataKey="value" name="Quantidade de Tarefas">
                    {statusData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.fill} />
                    ))}
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="prioridade">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Distribuição por Prioridade</CardTitle>
            </CardHeader>
            <CardContent className="h-96">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={prioridadeData}
                  margin={{
                    top: 20,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip formatter={(value) => [`${value} tarefas`, 'Quantidade']} />
                  <Legend />
                  <Bar dataKey="value" name="Quantidade de Tarefas">
                    {prioridadeData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.fill} />
                    ))}
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="semanal">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Análise Semanal de Tarefas</CardTitle>
            </CardHeader>
            <CardContent className="h-96">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={progressoSemanal}
                  margin={{
                    top: 20,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip formatter={(value) => [`${value} tarefas`, 'Quantidade']} />
                  <Legend />
                  <Bar dataKey="concluidas" name="Concluídas" stackId="a" fill="#22c55e" />
                  <Bar dataKey="emAndamento" name="Em Andamento" stackId="a" fill="#3b82f6" />
                  <Bar dataKey="pendentes" name="Pendentes" stackId="a" fill="#f97316" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Eficiência de Tarefas</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex flex-col gap-1">
              <div className="flex justify-between">
                <span className="text-sm font-medium">Taxa de Conclusão</span>
                <span className="text-sm text-muted-foreground">
                  {metrics.total > 0 ? Math.round((metrics.concluidas / metrics.total) * 100) : 0}%
                </span>
              </div>
              <Progress value={metrics.total > 0 ? (metrics.concluidas / metrics.total) * 100 : 0} className="h-2" />
            </div>
            
            <div className="flex flex-col gap-1">
              <div className="flex justify-between">
                <span className="text-sm font-medium">Tarefas em Dia</span>
                <span className="text-sm text-muted-foreground">
                  {metrics.total > 0 ? Math.round(((metrics.total - metrics.atrasadas) / metrics.total) * 100) : 0}%
                </span>
              </div>
              <Progress value={metrics.total > 0 ? ((metrics.total - metrics.atrasadas) / metrics.total) * 100 : 0} className="h-2" />
            </div>
            
            <div className="flex flex-col gap-1">
              <div className="flex justify-between">
                <span className="text-sm font-medium">Tarefas com Responsável</span>
                <span className="text-sm text-muted-foreground">
                  {metrics.total > 0 ? Math.round(((metrics.total - metrics.semResponsavel) / metrics.total) * 100) : 0}%
                </span>
              </div>
              <Progress value={metrics.total > 0 ? ((metrics.total - metrics.semResponsavel) / metrics.total) * 100 : 0} className="h-2" />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}; 