import React from 'react';
import { DragDropContext, Draggable, Droppable, DropResult } from '@hello-pangea/dnd';
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Copy, 
  Eye,
  Clock,
  Plus,
  Circle,
  CheckCircle2,
  AlertCircle,
  User,
  List,
  CheckSquare
} from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Tarefa, TaskGroup } from '../types';
import { format, isPast, isToday } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface TaskBoardProps {
  taskGroups: TaskGroup[];
  onUpdateTask: (tarefa: Tarefa) => void;
  onDeleteTask: (tarefaId: string) => void;
  onViewTaskDetails: (tarefa: Tarefa) => void;
}

export const TaskBoard: React.FC<TaskBoardProps> = ({ 
  taskGroups, 
  onUpdateTask, 
  onDeleteTask,
  onViewTaskDetails
}) => {
  const handleDragEnd = (result: DropResult) => {
    const { source, destination, draggableId } = result;
    
    // Se não tiver destino ou for o mesmo local, não faz nada
    if (!destination || 
        (source.droppableId === destination.droppableId && 
         source.index === destination.index)) {
      return;
    }
    
    // Encontra a tarefa que foi arrastada
    const sourceGroup = taskGroups.find(group => group.id === source.droppableId);
    if (!sourceGroup) return;
    
    const tarefa = sourceGroup.tasks.find(t => t.id === draggableId);
    if (!tarefa) return;
    
    // Atualiza o status ou propriedade da tarefa com base no destino
    let tarefaAtualizada: Tarefa = { ...tarefa };
    
    // Atualiza o status ou propriedade apropriada com base no grupo de destino
    if (destination.droppableId.startsWith('status-')) {
      const novoStatus = destination.droppableId.replace('status-', '');
      tarefaAtualizada = {
        ...tarefa,
        status: novoStatus as 'pendente' | 'em_andamento' | 'concluida',
        progresso: novoStatus === 'concluida' ? 100 : tarefa.progresso
      };
    } else if (destination.droppableId.startsWith('prioridade-')) {
      const novaPrioridade = destination.droppableId.replace('prioridade-', '');
      tarefaAtualizada = {
        ...tarefa,
        prioridade: novaPrioridade as 'baixa' | 'media' | 'alta'
      };
    } else if (destination.droppableId.startsWith('responsavel-')) {
      const novoResponsavelId = destination.droppableId.replace('responsavel-', '');
      // Aqui você precisaria buscar o objeto responsável completo com base no ID
      // Esta é apenas uma implementação fictícia para demonstração
      const novoResponsavel = { 
        id: novoResponsavelId, 
        nome: "Responsável", 
        avatar: "/avatars/default.jpg" 
      };
      tarefaAtualizada = {
        ...tarefa,
        responsavel: novoResponsavel
      };
    }
    
    // Chama o callback para atualizar a tarefa
    onUpdateTask(tarefaAtualizada);
  };
  
  const getPrioridadeBadge = (prioridade: string) => {
    switch (prioridade) {
      case 'alta':
        return <Badge variant="destructive" className="absolute top-2 right-2">Alta</Badge>;
      case 'media':
        return <Badge variant="default" className="absolute top-2 right-2 bg-orange-500">Média</Badge>;
      case 'baixa':
        return <Badge variant="outline" className="absolute top-2 right-2 text-blue-500 border-blue-500">Baixa</Badge>;
      default:
        return null;
    }
  };
  
  const formatarPrazo = (prazo: string) => {
    try {
      const data = new Date(prazo);
      if (isToday(data)) {
        return 'Hoje';
      } else if (isPast(data)) {
        return <span className="text-red-500">Atrasada</span>;
      } else {
        return format(data, 'dd/MM/yyyy', { locale: ptBR });
      }
    } catch (error) {
      return 'Data inválida';
    }
  };

  const renderSubtarefasInfo = (subtarefas: any[]) => {
    if (!subtarefas || subtarefas.length === 0) return null;
    
    const concluidas = subtarefas.filter(st => st.concluida).length;
    const total = subtarefas.length;
    
    return (
      <div className="flex items-center gap-1 mt-2 border-t pt-2">
        <CheckSquare className="w-3 h-3 text-muted-foreground" />
        <span className="text-xs text-muted-foreground">{concluidas}/{total} subtarefas</span>
      </div>
    );
  };
  
  const renderTarefaCard = (tarefa: Tarefa, index: number) => (
    <Draggable key={tarefa.id} draggableId={tarefa.id} index={index}>
      {(provided) => (
        <div
          ref={provided.innerRef}
          {...provided.draggableProps}
          {...provided.dragHandleProps}
          className="mb-3"
        >
          <Card className="relative cursor-pointer hover:border-primary" onClick={() => onViewTaskDetails(tarefa)}>
            {getPrioridadeBadge(tarefa.prioridade)}
            
            <CardContent className="p-4">
              <div className="flex flex-col gap-2">
                <div className="flex justify-between items-start">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <h3 className="font-medium text-sm line-clamp-2">{tarefa.titulo}</h3>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{tarefa.titulo}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem className="cursor-pointer" onClick={(e) => {
                        e.stopPropagation();
                        onViewTaskDetails(tarefa);
                      }}>
                        <Eye className="mr-2 h-4 w-4" />
                        <span>Ver Detalhes</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem className="cursor-pointer" onClick={(e) => e.stopPropagation()}>
                        <Edit className="mr-2 h-4 w-4" />
                        <span>Editar</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem className="cursor-pointer" onClick={(e) => e.stopPropagation()}>
                        <Copy className="mr-2 h-4 w-4" />
                        <span>Duplicar</span>
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem 
                        className="cursor-pointer text-red-600"
                        onClick={(e) => {
                          e.stopPropagation();
                          onDeleteTask(tarefa.id);
                        }}
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        <span>Excluir</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <p className="text-xs text-muted-foreground line-clamp-2">
                        {tarefa.descricao}
                      </p>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-md">{tarefa.descricao}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                
                <div className="flex flex-wrap gap-1">
                  {tarefa.tags?.map((tag, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>

                {renderSubtarefasInfo(tarefa.subtarefas)}
                
                <div className="flex justify-between items-center mt-2">
                  {tarefa.responsavel ? (
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Avatar className="h-6 w-6">
                            <AvatarImage src={tarefa.responsavel.avatar} alt={tarefa.responsavel.nome} />
                            <AvatarFallback>
                              {tarefa.responsavel.nome.split(' ').map(n => n[0]).join('').toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{tarefa.responsavel.nome}</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  ) : (
                    <span className="text-xs text-muted-foreground">Não atribuído</span>
                  )}
                  
                  {tarefa.prazo && (
                    <div className="flex items-center gap-1 text-xs">
                      <Clock className="h-3 w-3" />
                      {formatarPrazo(tarefa.prazo)}
                    </div>
                  )}
                </div>
                
                <Progress value={tarefa.progresso} className="h-1 mt-1" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </Draggable>
  );
  
  // Função para renderizar o ícone correto com base no nome
  const renderIcon = (iconName: string) => {
    switch (iconName) {
      case 'circle':
        return <Circle className="w-4 h-4" />;
      case 'clock':
        return <Clock className="w-4 h-4" />;
      case 'check-circle':
        return <CheckCircle2 className="w-4 h-4" />;
      case 'alert-circle':
        return <AlertCircle className="w-4 h-4" />;
      case 'user':
        return <User className="w-4 h-4" />;
      case 'list':
        return <List className="w-4 h-4" />;
      default:
        return <Circle className="w-4 h-4" />;
    }
  };
  
  return (
    <DragDropContext onDragEnd={handleDragEnd}>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {taskGroups.map((group) => (
          <div key={group.id} className="flex flex-col h-full">
            <Card className="flex-1 flex flex-col">
              <CardHeader className="pb-2 flex flex-row items-center justify-between space-y-0">
                <CardTitle className="text-md font-medium flex items-center gap-2">
                  {renderIcon(group.icon)}
                  {group.title}
                  <Badge variant="secondary" className="ml-2">
                    {group.tasks.length}
                  </Badge>
                </CardTitle>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <Plus className="h-4 w-4" />
                </Button>
              </CardHeader>
              <CardContent className="flex-1 pb-0 overflow-hidden">
                <Droppable droppableId={group.id}>
                  {(provided) => (
                    <div 
                      ref={provided.innerRef}
                      {...provided.droppableProps}
                      className="h-[calc(100vh-300px)] min-h-[200px] space-y-3 overflow-y-auto p-1 pr-2 pb-16 scrollbar-thin"
                      style={{ scrollbarWidth: 'thin' }}
                    >
                      {group.tasks.map((tarefa, index) => 
                        renderTarefaCard(tarefa, index)
                      )}
                      {provided.placeholder}
                      
                      {group.tasks.length === 0 && (
                        <div className="flex items-center justify-center h-full min-h-[100px] border border-dashed rounded-md text-muted-foreground text-sm">
                          Sem tarefas
                        </div>
                      )}
                    </div>
                  )}
                </Droppable>
              </CardContent>
            </Card>
          </div>
        ))}
      </div>
    </DragDropContext>
  );
}; 