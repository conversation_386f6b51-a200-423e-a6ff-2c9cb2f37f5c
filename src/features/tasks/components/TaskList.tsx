import React, { useState } from 'react';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Copy, 
  Eye, 
  Clock, 
  AlertCircle,
  CheckCircle2,
  Circle,
  ChevronDown,
  ChevronRight,
  CheckSquare,
  Square
} from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Progress } from "@/components/ui/progress";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Tarefa, Subtarefa } from '../types';
import { format, isPast, isToday, addDays } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface TaskListProps {
  tarefas: Tarefa[];
  onUpdateTask: (tarefa: Tarefa) => void;
  onDeleteTask: (tarefaId: string) => void;
  onViewTaskDetails: (tarefa: Tarefa) => void;
  showSubtasksInCascade?: boolean;
  showSimpleList?: boolean;
}

export const TaskList: React.FC<TaskListProps> = ({ 
  tarefas, 
  onUpdateTask, 
  onDeleteTask,
  onViewTaskDetails,
  showSubtasksInCascade = true,
  showSimpleList = false
}) => {
  const [selectedTasks, setSelectedTasks] = useState<string[]>([]);
  const [expandedTasks, setExpandedTasks] = useState<string[]>([]);
  
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedTasks(tarefas.map(tarefa => tarefa.id));
    } else {
      setSelectedTasks([]);
    }
  };
  
  const handleSelectTask = (tarefaId: string, checked: boolean) => {
    if (checked) {
      setSelectedTasks([...selectedTasks, tarefaId]);
    } else {
      setSelectedTasks(selectedTasks.filter(id => id !== tarefaId));
    }
  };
  
  const handleCycleTaskStatus = (tarefa: Tarefa) => {
    let novoStatus = '';
    
    switch (tarefa.status) {
      case 'pendente':
        novoStatus = 'em_andamento';
        break;
      case 'em_andamento':
        novoStatus = 'concluida';
        break;
      case 'concluida':
      default:
        novoStatus = 'pendente';
        break;
    }
    
    onUpdateTask({
      ...tarefa,
      status: novoStatus,
      progresso: novoStatus === 'concluida' ? 100 : novoStatus === 'em_andamento' ? 50 : 0
    });
  };
  
  const handleToggleTaskStatus = (tarefa: Tarefa) => {
    const novoStatus = tarefa.status === 'concluida' ? 'pendente' : 'concluida';
    onUpdateTask({
      ...tarefa,
      status: novoStatus,
      progresso: novoStatus === 'concluida' ? 100 : tarefa.progresso
    });
  };
  
  const handleToggleSubtarefaStatus = (tarefa: Tarefa, subtarefa: Subtarefa) => {
    const subtarefasAtualizadas = tarefa.subtarefas.map(st => 
      st.id === subtarefa.id ? { ...st, concluida: !st.concluida } : st
    );
    
    // Calcular novo progresso geral com base nas subtarefas
    const total = subtarefasAtualizadas.length;
    const concluidas = subtarefasAtualizadas.filter(st => st.concluida).length;
    const novoProgresso = total > 0 ? Math.round((concluidas / total) * 100) : tarefa.progresso;
    
    onUpdateTask({
      ...tarefa,
      subtarefas: subtarefasAtualizadas,
      progresso: novoProgresso
    });
  };
  
  const toggleTaskExpand = (tarefaId: string) => {
    if (expandedTasks.includes(tarefaId)) {
      setExpandedTasks(expandedTasks.filter(id => id !== tarefaId));
    } else {
      setExpandedTasks([...expandedTasks, tarefaId]);
    }
  };
  
  const isTaskExpanded = (tarefaId: string) => {
    return expandedTasks.includes(tarefaId);
  };
  
  const getPrioridadeBadge = (prioridade: string) => {
    switch (prioridade) {
      case 'alta':
        return <Badge variant="destructive">Alta</Badge>;
      case 'media':
        return <Badge variant="default" className="bg-orange-500">Média</Badge>;
      case 'baixa':
        return <Badge variant="outline" className="text-blue-500 border-blue-500">Baixa</Badge>;
      default:
        return null;
    }
  };
  
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'concluida':
        return <CheckCircle2 className="h-5 w-5 text-green-500" />;
      case 'em_andamento':
        return <Clock className="h-5 w-5 text-blue-500" />;
      case 'pendente':
        return <Circle className="h-5 w-5 text-gray-400" />;
      case 'atrasada':
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      default:
        return null;
    }
  };
  
  const formatarPrazo = (prazo: string) => {
    try {
      const data = new Date(prazo);
      if (isToday(data)) {
        return 'Hoje';
      } else if (isPast(data)) {
        return <span className="text-red-500">Atrasada</span>;
      } else if (isToday(addDays(new Date(), 1)) && data.getDate() === addDays(new Date(), 1).getDate()) {
        return 'Amanhã';
      } else {
        return format(data, 'dd/MM/yyyy', { locale: ptBR });
      }
    } catch (error) {
      return 'Data inválida';
    }
  };

  const renderSubtarefasInfo = (tarefa: Tarefa) => {
    const { subtarefas } = tarefa;
    if (!subtarefas || subtarefas.length === 0 || !showSubtasksInCascade) return null;
    
    const concluidas = subtarefas.filter(st => st.concluida).length;
    const total = subtarefas.length;
    const isExpanded = isTaskExpanded(tarefa.id);
    
    return (
      <div className="space-y-1">
        <div 
          className="flex items-center gap-1 text-xs text-muted-foreground cursor-pointer" 
          onClick={(e) => {
            e.stopPropagation();
            toggleTaskExpand(tarefa.id);
          }}
        >
          {isExpanded ? (
            <ChevronDown className="h-3 w-3" />
          ) : (
            <ChevronRight className="h-3 w-3" />
          )}
          <CheckSquare className="h-3 w-3" />
          <span>{concluidas}/{total} subtarefas</span>
        </div>
        
        {isExpanded && (
          <div className="pl-5 space-y-1 mt-1 text-xs">
            {subtarefas.map(subtarefa => (
              <div key={subtarefa.id} className="flex items-center gap-2" onClick={(e) => e.stopPropagation()}>
                <div 
                  className="cursor-pointer" 
                  onClick={() => handleToggleSubtarefaStatus(tarefa, subtarefa)}
                >
                  {subtarefa.concluida ? (
                    <CheckSquare className="h-3 w-3 text-green-500" />
                  ) : (
                    <Square className="h-3 w-3 text-gray-400" />
                  )}
                </div>
                <span className={subtarefa.concluida ? 'line-through text-muted-foreground' : ''}>
                  {subtarefa.titulo}
                </span>
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };
  
  // Adiciona um resumo simples das subtarefas quando não estamos mostrando em cascata
  const renderSubtarefasSummary = (tarefa: Tarefa) => {
    const { subtarefas } = tarefa;
    if (!subtarefas || subtarefas.length === 0 || showSubtasksInCascade) return null;
    
    const concluidas = subtarefas.filter(st => st.concluida).length;
    const total = subtarefas.length;
    
    return (
      <div className="flex items-center gap-1 text-xs text-muted-foreground mt-1">
        <CheckSquare className="h-3 w-3" />
        <span>{concluidas}/{total} subtarefas</span>
      </div>
    );
  };
  
  // Função para criar uma lista completa incluindo subtarefas
  const getAllTasksAndSubtasks = () => {
    const allItems: { id: string; titulo: string; isSubtask: boolean; parentTaskId?: string; status: string; concluida?: boolean; prioridade?: string; prazo?: string; responsavel?: any }[] = [];
    
    tarefas.forEach(tarefa => {
      // Adicionar a tarefa principal
      allItems.push({
        id: tarefa.id,
        titulo: tarefa.titulo,
        isSubtask: false,
        status: tarefa.status,
        prioridade: tarefa.prioridade,
        prazo: tarefa.prazo,
        responsavel: tarefa.responsavel
      });
      
      // Adicionar suas subtarefas
      if (tarefa.subtarefas && tarefa.subtarefas.length > 0) {
        tarefa.subtarefas.forEach(subtarefa => {
          allItems.push({
            id: subtarefa.id,
            titulo: subtarefa.titulo,
            isSubtask: true,
            parentTaskId: tarefa.id,
            concluida: subtarefa.concluida,
            status: subtarefa.concluida ? 'concluida' : 'pendente',
            prioridade: tarefa.prioridade // Herda a prioridade da tarefa pai
          });
        });
      }
    });
    
    return allItems;
  };

  // Renderiza a visualização "Todo" com lista simples de todas as tarefas e subtarefas
  const renderTodoView = () => {
    const allItems = getAllTasksAndSubtasks();
    
    // Ordenar por prioridade: alta -> media -> baixa
    const sortedItems = [...allItems].sort((a, b) => {
      const prioridadeOrder = { alta: 0, media: 1, baixa: 2 };
      const prioridadeA = a.prioridade || 'media';
      const prioridadeB = b.prioridade || 'media';
      
      return prioridadeOrder[prioridadeA as keyof typeof prioridadeOrder] - 
             prioridadeOrder[prioridadeB as keyof typeof prioridadeOrder];
    });
    
    // Agrupar por prioridade
    const altaPrioridade = sortedItems.filter(item => item.prioridade === 'alta');
    const mediaPrioridade = sortedItems.filter(item => item.prioridade === 'media');
    const baixaPrioridade = sortedItems.filter(item => !item.prioridade || item.prioridade === 'baixa');
    
    return (
      <div className="w-full overflow-auto">
        {allItems.length === 0 ? (
          <div className="text-center py-8">
            <h3 className="text-lg font-medium">Nenhuma tarefa encontrada</h3>
            <p className="text-muted-foreground">
              Não há tarefas que correspondam aos critérios de filtro atuais.
            </p>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Tarefas de Alta Prioridade */}
            {altaPrioridade.length > 0 && (
              <div>
                <h3 className="text-sm font-medium text-destructive mb-2 px-2">Alta Prioridade</h3>
                <Table className="min-w-full">
                  <TableHeader>
                    <TableRow className="border-b hover:bg-transparent">
                      <TableHead className="w-[40px]">Status</TableHead>
                      <TableHead>Tarefa</TableHead>
                      <TableHead className="w-[100px]">Prioridade</TableHead>
                      <TableHead>Prazo</TableHead>
                      <TableHead className="text-right w-[80px]">Ações</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {renderTodoItems(altaPrioridade)}
                  </TableBody>
                </Table>
              </div>
            )}
            
            {/* Tarefas de Média Prioridade */}
            {mediaPrioridade.length > 0 && (
              <div>
                <h3 className="text-sm font-medium text-orange-500 mb-2 px-2">Média Prioridade</h3>
                <Table className="min-w-full">
                  <TableHeader>
                    <TableRow className="border-b hover:bg-transparent">
                      <TableHead className="w-[40px]">Status</TableHead>
                      <TableHead>Tarefa</TableHead>
                      <TableHead className="w-[100px]">Prioridade</TableHead>
                      <TableHead>Prazo</TableHead>
                      <TableHead className="text-right w-[80px]">Ações</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {renderTodoItems(mediaPrioridade)}
                  </TableBody>
                </Table>
              </div>
            )}
            
            {/* Tarefas de Baixa Prioridade */}
            {baixaPrioridade.length > 0 && (
              <div>
                <h3 className="text-sm font-medium text-blue-500 mb-2 px-2">Baixa Prioridade</h3>
                <Table className="min-w-full">
                  <TableHeader>
                    <TableRow className="border-b hover:bg-transparent">
                      <TableHead className="w-[40px]">Status</TableHead>
                      <TableHead>Tarefa</TableHead>
                      <TableHead className="w-[100px]">Prioridade</TableHead>
                      <TableHead>Prazo</TableHead>
                      <TableHead className="text-right w-[80px]">Ações</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {renderTodoItems(baixaPrioridade)}
                  </TableBody>
                </Table>
              </div>
            )}
          </div>
        )}
      </div>
    );
  };
  
  // Renderiza os itens da lista Todo
  const renderTodoItems = (items: any[]) => {
    return items.map((item) => {
      const isSubtask = item.isSubtask;
      const parentTask = isSubtask && tarefas.find(t => t.id === item.parentTaskId);
      
      return (
        <TableRow 
          key={`${isSubtask ? 'sub-' : ''}${item.id}`} 
          className={`cursor-pointer hover:bg-muted/50 ${isSubtask ? 'bg-muted/20' : ''}`}
          onClick={() => {
            if (!isSubtask) {
              onViewTaskDetails(tarefas.find(t => t.id === item.id)!);
            }
          }}
        >
          <TableCell onClick={(e) => e.stopPropagation()} className="py-3">
            <div 
              className="cursor-pointer" 
              onClick={() => {
                if (isSubtask && parentTask) {
                  // Encontrar a subtarefa dentro do parent e alternar seu estado
                  const subtarefa = parentTask.subtarefas.find(st => st.id === item.id);
                  if (subtarefa) {
                    handleToggleSubtarefaStatus(parentTask, subtarefa);
                  }
                } else if (!isSubtask) {
                  // Alternar estado da tarefa principal em ciclo
                  handleCycleTaskStatus(tarefas.find(t => t.id === item.id)!);
                }
              }}
            >
              {isSubtask ? (
                item.concluida ? (
                  <CheckSquare className="h-4 w-4 text-green-500" />
                ) : (
                  <Square className="h-4 w-4 text-gray-400" />
                )
              ) : (
                getStatusIcon(item.status)
              )}
            </div>
          </TableCell>
          <TableCell className="py-3">
            <div className={`font-medium ${isSubtask ? 'pl-6 text-sm' : ''} ${isSubtask && item.concluida ? 'line-through text-muted-foreground' : ''}`}>
              {isSubtask && <span className="text-muted-foreground mr-2">└─</span>}
              {item.titulo}
            </div>
            {!isSubtask && (
              <div className="flex flex-wrap gap-1 mt-1">
                {tarefas.find(t => t.id === item.id)?.tags?.map((tag, index) => (
                  <Badge key={index} variant="secondary" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>
            )}
          </TableCell>
          <TableCell className="py-3">
            {!isSubtask && getPrioridadeBadge(item.prioridade || 'media')}
          </TableCell>
          <TableCell className="py-3">
            {!isSubtask && item.prazo ? (
              <div className="text-sm">{formatarPrazo(item.prazo)}</div>
            ) : (
              isSubtask ? null : <span className="text-muted-foreground text-sm">Sem prazo</span>
            )}
          </TableCell>
          <TableCell className="text-right py-3" onClick={(e) => e.stopPropagation()}>
            {!isSubtask && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="h-8 w-8 p-0">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem className="cursor-pointer" onClick={() => onViewTaskDetails(tarefas.find(t => t.id === item.id)!)}>
                    <Eye className="mr-2 h-4 w-4" />
                    <span>Ver Detalhes</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem className="cursor-pointer">
                    <Edit className="mr-2 h-4 w-4" />
                    <span>Editar</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem className="cursor-pointer">
                    <Copy className="mr-2 h-4 w-4" />
                    <span>Duplicar</span>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem 
                    className="cursor-pointer text-red-600"
                    onClick={() => onDeleteTask(item.id)}
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    <span>Excluir</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </TableCell>
        </TableRow>
      );
    });
  };

  // Renderização principal baseada nos parâmetros
  return showSimpleList ? renderTodoView() : (
    <div className="w-full overflow-auto">
      {tarefas.length === 0 ? (
        <div className="text-center py-8">
          <h3 className="text-lg font-medium">Nenhuma tarefa encontrada</h3>
          <p className="text-muted-foreground">
            Não há tarefas que correspondam aos critérios de filtro atuais.
          </p>
        </div>
      ) : (
        <Table className="min-w-full">
          <TableHeader>
            <TableRow className="border-b hover:bg-transparent">
              <TableHead className="w-[40px]">
                <Checkbox 
                  checked={selectedTasks.length === tarefas.length && tarefas.length > 0} 
                  onCheckedChange={(checked) => handleSelectAll(checked as boolean)}
                />
              </TableHead>
              <TableHead className="w-[40px]">Status</TableHead>
              <TableHead className="w-[30%]">Tarefa</TableHead>
              <TableHead className="w-[100px]">Prioridade</TableHead>
              <TableHead>Responsável</TableHead>
              <TableHead>Prazo</TableHead>
              <TableHead>Progresso</TableHead>
              <TableHead className="text-right w-[80px]">Ações</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {tarefas.map((tarefa) => (
              <TableRow key={tarefa.id} className="cursor-pointer hover:bg-muted/50" onClick={() => onViewTaskDetails(tarefa)}>
                <TableCell onClick={(e) => e.stopPropagation()} className="py-3">
                  <Checkbox 
                    checked={selectedTasks.includes(tarefa.id)}
                    onCheckedChange={(checked) => handleSelectTask(tarefa.id, checked as boolean)}
                  />
                </TableCell>
                <TableCell onClick={(e) => e.stopPropagation()} className="py-3">
                  <div 
                    className="cursor-pointer" 
                    onClick={() => handleToggleTaskStatus(tarefa)}
                    title={tarefa.status === 'concluida' ? 'Marcar como pendente' : 'Marcar como concluída'}
                  >
                    {getStatusIcon(tarefa.status)}
                  </div>
                </TableCell>
                <TableCell className="py-3">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="font-medium max-w-[300px] truncate">{tarefa.titulo}</div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{tarefa.titulo}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="text-sm text-muted-foreground max-w-[300px] truncate">
                          {tarefa.descricao}
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="max-w-md">{tarefa.descricao}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  
                  <div className="flex flex-wrap gap-1 mt-1">
                    {tarefa.tags?.map((tag, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                  {renderSubtarefasInfo(tarefa)}
                  {renderSubtarefasSummary(tarefa)}
                </TableCell>
                <TableCell className="py-3">
                  {getPrioridadeBadge(tarefa.prioridade)}
                </TableCell>
                <TableCell className="py-3">
                  {tarefa.responsavel ? (
                    <div className="flex items-center gap-2">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={tarefa.responsavel.avatar} alt={tarefa.responsavel.nome} />
                        <AvatarFallback>
                          {tarefa.responsavel.nome.split(' ').map(n => n[0]).join('').toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div className="text-sm font-medium">{tarefa.responsavel.nome}</div>
                    </div>
                  ) : (
                    <span className="text-muted-foreground text-sm">Não atribuído</span>
                  )}
                </TableCell>
                <TableCell className="py-3">
                  {tarefa.prazo ? (
                    <div className="text-sm">{formatarPrazo(tarefa.prazo)}</div>
                  ) : (
                    <span className="text-muted-foreground text-sm">Sem prazo</span>
                  )}
                </TableCell>
                <TableCell className="py-3">
                  <div className="flex items-center gap-2">
                    <Progress value={tarefa.progresso} className="h-2 w-20" />
                    <span className="text-xs text-muted-foreground">{tarefa.progresso}%</span>
                  </div>
                </TableCell>
                <TableCell className="text-right py-3" onClick={(e) => e.stopPropagation()}>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem className="cursor-pointer" onClick={() => onViewTaskDetails(tarefa)}>
                        <Eye className="mr-2 h-4 w-4" />
                        <span>Ver Detalhes</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem className="cursor-pointer">
                        <Edit className="mr-2 h-4 w-4" />
                        <span>Editar</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem className="cursor-pointer">
                        <Copy className="mr-2 h-4 w-4" />
                        <span>Duplicar</span>
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem 
                        className="cursor-pointer text-red-600"
                        onClick={() => onDeleteTask(tarefa.id)}
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        <span>Excluir</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      )}
    </div>
  );
}; 