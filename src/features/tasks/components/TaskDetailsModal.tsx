import { useState, useEffect } from "react";
import {
  CheckCircle2,
  Clock,
  MessageSquare,
  Tag,
  Users,
  FileText,
  Edit3,
  Trash2,
  Link as LinkIcon,
  Activity,
  Plus,
  CheckSquare,
  X,
  Loader2
} from "lucide-react";
import { 
  <PERSON><PERSON>, 
  DialogContent, 
  Di<PERSON>Header, 
  DialogTitle, 
  DialogFooter,
  DialogDescription 
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Progress } from "@/components/ui/progress";
import { Input } from "@/components/ui/input";
import { 
  Card, 
  CardContent, 
  CardDescription,
  Card<PERSON>eader, 
  CardTitle 
} from "@/components/ui/card";
import { 
  TaskDetailsModalProps, 
  Subtarefa,
  Comentario 
} from "../types";
import { formatDate } from "../utils/taskUtils";
import { addTaskComment, updateTask, addTaskSubtask, updateTaskSubtask, deleteTaskSubtask } from "@/services/tasksService";
import { useToast } from "@/components/ui/use-toast";
import { v4 as uuidv4 } from 'uuid';

export function TaskDetailsModal({ task, isOpen, onClose, taskHistory, onTaskUpdated }: TaskDetailsModalProps) {
  const [activeTab, setActiveTab] = useState("detalhes");
  const [newComment, setNewComment] = useState("");
  const [editingSubtask, setEditingSubtask] = useState<string | null>(null);
  const [newSubtaskTitle, setNewSubtaskTitle] = useState("");
  const [editingSubtaskTitle, setEditingSubtaskTitle] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  if (!task) return null;

  const handleCommentSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newComment.trim() || isSubmitting) return;
    
    try {
      setIsSubmitting(true);
      
      const comentario: Comentario = {
        id: uuidv4(),
        texto: newComment,
        dataCriacao: new Date().toISOString(),
        autorId: "currentUser", // Isso deve ser substituído pelo ID do usuário atual
        autorNome: "Usuário Atual", // Isso deve ser substituído pelo nome do usuário atual
        autorAvatar: "" // Isso deve ser substituído pelo avatar do usuário atual
      };
      
      await addTaskComment(task.id, comentario);
      
      if (onTaskUpdated) {
        onTaskUpdated({
          ...task,
          comentarios: [...(task.comentarios || []), comentario]
        });
      }
      
      toast({
        title: "Comentário adicionado",
        description: "O comentário foi adicionado com sucesso."
      });
      
      setNewComment("");
    } catch (error) {
      console.error("Erro ao adicionar comentário:", error);
      toast({
        title: "Erro ao adicionar comentário",
        description: "Não foi possível adicionar o comentário. Tente novamente.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleToggleSubtask = async (subtaskId: string, currentStatus: boolean) => {
    if (isSubmitting) return;
    
    try {
      setIsSubmitting(true);
      
      const updatedSubtasks = task.subtarefas.map(st => 
        st.id === subtaskId ? { ...st, concluida: !currentStatus } : st
      );
      
      // Calcular novo progresso com base nas subtarefas
      const total = updatedSubtasks.length;
      const concluidas = updatedSubtasks.filter(st => st.concluida).length;
      const novoProgresso = total > 0 ? Math.round((concluidas / total) * 100) : task.progresso;
      
      const updatedTask = {
        ...task,
        subtarefas: updatedSubtasks,
        progresso: novoProgresso
      };
      
      await updateTaskSubtask(task.id, subtaskId, !currentStatus);
      
      if (onTaskUpdated) {
        onTaskUpdated(updatedTask);
      }
      
      toast({
        title: "Subtarefa atualizada",
        description: "O status da subtarefa foi atualizado com sucesso."
      });
    } catch (error) {
      console.error("Erro ao atualizar subtarefa:", error);
      toast({
        title: "Erro ao atualizar subtarefa",
        description: "Não foi possível atualizar a subtarefa. Tente novamente.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAddSubtask = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newSubtaskTitle.trim() || isSubmitting) return;
    
    try {
      setIsSubmitting(true);
      
      const novaSubtarefa: Subtarefa = {
        id: uuidv4(),
        titulo: newSubtaskTitle,
        concluida: false,
        prazo: task.prazo
      };
      
      const updatedSubtasks = [...(task.subtarefas || []), novaSubtarefa];
      
      // Calcular novo progresso com base nas subtarefas
      const total = updatedSubtasks.length;
      const concluidas = updatedSubtasks.filter(st => st.concluida).length;
      const novoProgresso = total > 0 ? Math.round((concluidas / total) * 100) : task.progresso;
      
      const updatedTask = {
        ...task,
        subtarefas: updatedSubtasks,
        progresso: novoProgresso
      };
      
      await addTaskSubtask(task.id, novaSubtarefa);
      
      if (onTaskUpdated) {
        onTaskUpdated(updatedTask);
      }
      
      toast({
        title: "Subtarefa adicionada",
        description: "A subtarefa foi adicionada com sucesso."
      });
      
      setNewSubtaskTitle("");
    } catch (error) {
      console.error("Erro ao adicionar subtarefa:", error);
      toast({
        title: "Erro ao adicionar subtarefa",
        description: "Não foi possível adicionar a subtarefa. Tente novamente.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditSubtask = (subtask: Subtarefa) => {
    setEditingSubtask(subtask.id);
    setEditingSubtaskTitle(subtask.titulo);
  };

  const handleSaveSubtaskEdit = async (subtaskId: string) => {
    if (!editingSubtaskTitle.trim() || isSubmitting) return;
    
    try {
      setIsSubmitting(true);
      
      const updatedSubtasks = task.subtarefas.map(st => 
        st.id === subtaskId ? { ...st, titulo: editingSubtaskTitle } : st
      );
      
      const updatedTask = {
        ...task,
        subtarefas: updatedSubtasks
      };
      
      const subtaskToUpdate = task.subtarefas.find(st => st.id === subtaskId);
      if (subtaskToUpdate) {
        await updateTaskSubtask(task.id, subtaskId, subtaskToUpdate.concluida, editingSubtaskTitle);
      }
      
      if (onTaskUpdated) {
        onTaskUpdated(updatedTask);
      }
      
      toast({
        title: "Subtarefa atualizada",
        description: "A subtarefa foi atualizada com sucesso."
      });
      
      setEditingSubtask(null);
      setEditingSubtaskTitle("");
    } catch (error) {
      console.error("Erro ao atualizar subtarefa:", error);
      toast({
        title: "Erro ao atualizar subtarefa",
        description: "Não foi possível atualizar a subtarefa. Tente novamente.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRemoveSubtask = async (subtaskId: string) => {
    if (isSubmitting) return;
    
    try {
      setIsSubmitting(true);
      
      const updatedSubtasks = task.subtarefas.filter(st => st.id !== subtaskId);
      
      // Calcular novo progresso com base nas subtarefas
      const total = updatedSubtasks.length;
      const concluidas = updatedSubtasks.filter(st => st.concluida).length;
      const novoProgresso = total > 0 ? Math.round((concluidas / total) * 100) : task.progresso;
      
      const updatedTask = {
        ...task,
        subtarefas: updatedSubtasks,
        progresso: novoProgresso
      };
      
      await deleteTaskSubtask(task.id, subtaskId);
      
      if (onTaskUpdated) {
        onTaskUpdated(updatedTask);
      }
      
      toast({
        title: "Subtarefa removida",
        description: "A subtarefa foi removida com sucesso."
      });
    } catch (error) {
      console.error("Erro ao remover subtarefa:", error);
      toast({
        title: "Erro ao remover subtarefa",
        description: "Não foi possível remover a subtarefa. Tente novamente.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const statusColorMap: Record<string, string> = {
    pendente: "bg-orange-500",
    em_andamento: "bg-blue-500",
    concluida: "bg-green-500"
  };

  const prioridadeColorMap: Record<string, string> = {
    baixa: "bg-blue-500",
    media: "bg-orange-500",
    alta: "bg-red-500"
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto" aria-describedby="task-details-description">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold">{task.titulo}</DialogTitle>
          <DialogDescription id="task-details-description" className="sr-only">
            Detalhes da tarefa e informações relacionadas.
          </DialogDescription>
          <div className="flex items-center gap-2 mt-2">
            <Badge className={statusColorMap[task.status]}>
              {task.status === "pendente" ? "Pendente" : 
               task.status === "em_andamento" ? "Em Andamento" : "Concluída"}
            </Badge>
            <Badge className={prioridadeColorMap[task.prioridade]}>
              {task.prioridade === "baixa" ? "Baixa Prioridade" : 
               task.prioridade === "media" ? "Média Prioridade" : "Alta Prioridade"}
            </Badge>
            {task.processoVinculado && (
              <Badge variant="outline" className="flex items-center gap-1">
                <LinkIcon className="h-3 w-3" />
                Processo {task.processoVinculado}
              </Badge>
            )}
          </div>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="detalhes">Detalhes</TabsTrigger>
            <TabsTrigger value="subtarefas">Subtarefas</TabsTrigger>
            <TabsTrigger value="comentarios">Comentários</TabsTrigger>
            <TabsTrigger value="historico">Histórico</TabsTrigger>
          </TabsList>

          <TabsContent value="detalhes" className="space-y-4 mt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Descrição</h3>
                  <p className="text-sm">{task.descricao || "Sem descrição"}</p>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Prazo</h3>
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <span>{formatDate(task.prazo)}</span>
                  </div>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Tags</h3>
                  <div className="flex flex-wrap gap-2">
                    {task.tags.length > 0 ? (
                      task.tags.map((tag, index) => (
                        <Badge key={index} variant="secondary" className="flex items-center gap-1">
                          <Tag className="h-3 w-3" />
                          {tag}
                        </Badge>
                      ))
                    ) : (
                      <span className="text-sm text-muted-foreground">Sem tags</span>
                    )}
                  </div>
                </div>

                {task.arquivosAnexos && task.arquivosAnexos.length > 0 && (
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Arquivos Anexos</h3>
                    <div className="space-y-2">
                      {task.arquivosAnexos.map((arquivo, index) => (
                        <div 
                          key={index}
                          className="flex items-center justify-between p-2 rounded-md border bg-muted/40"
                        >
                          <div className="flex items-center gap-2">
                            <FileText className="h-4 w-4 text-muted-foreground" />
                            <div>
                              <p className="text-sm font-medium">{arquivo.nome}</p>
                              <p className="text-xs text-muted-foreground">
                                {arquivo.tipo} · {arquivo.tamanho} · {arquivo.data}
                              </p>
                            </div>
                          </div>
                          <Button size="sm" variant="ghost">Baixar</Button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Responsável</h3>
                  <div className="flex items-center gap-2">
                    <Avatar className="h-6 w-6">
                      <AvatarImage src={task.responsavel.avatar} />
                      <AvatarFallback>
                        {task.responsavel.nome.split(" ").map(n => n[0]).join("")}
                      </AvatarFallback>
                    </Avatar>
                    <span>{task.responsavel.nome}</span>
                  </div>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Participantes</h3>
                  <div className="flex flex-wrap items-center gap-2">
                    {task.participantes.map((participante, index) => (
                      <Avatar key={index} className="h-8 w-8">
                        <AvatarImage src={participante.avatar} />
                        <AvatarFallback>
                          {participante.nome.split(" ").map(n => n[0]).join("")}
                        </AvatarFallback>
                      </Avatar>
                    ))}
                    <Button size="icon" variant="outline" className="h-8 w-8 rounded-full">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Progresso</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Andamento</span>
                      <span>{task.progresso}%</span>
                    </div>
                    <Progress value={task.progresso} className="h-2" />
                  </div>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Datas</h3>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Criação:</span>
                      <span>{formatDate(task.dataCriacao)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Prazo:</span>
                      <span>{formatDate(task.prazo)}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="subtarefas" className="space-y-4 mt-4">
            <div className="flex justify-between items-center">
              <h3 className="text-sm font-medium">Subtarefas ({task.subtarefas.length})</h3>
              <div className="text-sm text-muted-foreground">
                {task.subtarefas.filter(s => s.concluida).length} de {task.subtarefas.length} concluídas
              </div>
            </div>
            
            <Card>
              <CardContent className="p-4 space-y-4">
                {task.subtarefas.length > 0 ? (
                  <div className="space-y-2">
                    {task.subtarefas.map((subtask) => (
                      <div key={subtask.id} className="flex items-center justify-between p-2 rounded-md border">
                        {editingSubtask === subtask.id ? (
                          <div className="flex items-center gap-2 w-full">
                            <Input
                              value={editingSubtaskTitle}
                              onChange={(e) => setEditingSubtaskTitle(e.target.value)}
                              className="flex-1"
                            />
                            <Button size="sm" onClick={() => handleSaveSubtaskEdit(subtask.id)}>Salvar</Button>
                            <Button 
                              size="sm" 
                              variant="ghost" 
                              onClick={() => setEditingSubtask(null)}
                            >
                              Cancelar
                            </Button>
                          </div>
                        ) : (
                          <>
                            <div className="flex items-center gap-2">
                              <Checkbox 
                                checked={subtask.concluida}
                                onCheckedChange={() => handleToggleSubtask(subtask.id, subtask.concluida)}
                              />
                              <span className={subtask.concluida ? "line-through text-muted-foreground" : ""}>
                                {subtask.titulo}
                              </span>
                              {subtask.prazo && (
                                <Badge variant="outline" className="ml-2 text-xs">
                                  {formatDate(subtask.prazo)}
                                </Badge>
                              )}
                            </div>
                            <div className="flex items-center gap-1">
                              <Button 
                                size="icon" 
                                variant="ghost" 
                                className="h-8 w-8"
                                onClick={() => handleEditSubtask(subtask)}
                              >
                                <Edit3 className="h-4 w-4" />
                              </Button>
                              <Button 
                                size="icon" 
                                variant="ghost" 
                                className="h-8 w-8 text-destructive"
                                onClick={() => handleRemoveSubtask(subtask.id)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-6 text-muted-foreground">
                    <CheckSquare className="h-12 w-12 mx-auto mb-2 opacity-20" />
                    <p>Nenhuma subtarefa cadastrada</p>
                  </div>
                )}

                <form onSubmit={handleAddSubtask} className="flex items-center gap-2">
                  <Input
                    placeholder="Adicionar nova subtarefa..."
                    value={newSubtaskTitle}
                    onChange={(e) => setNewSubtaskTitle(e.target.value)}
                    className="flex-1"
                  />
                  <Button type="submit" size="sm">Adicionar</Button>
                </form>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="comentarios" className="space-y-4 mt-4">
            <div className="space-y-4">
              {task.comentarios.length > 0 ? (
                task.comentarios.map((comentario, index) => (
                  <div key={index} className="flex gap-3 p-3 rounded-lg border">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={comentario.autorAvatar} />
                      <AvatarFallback>
                        {comentario.autorNome.split(" ").map(n => n[0]).join("")}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <div className="flex justify-between">
                        <p className="font-medium">{comentario.autorNome}</p>
                        <span className="text-xs text-muted-foreground">
                          {formatDate(comentario.dataCriacao)}
                        </span>
                      </div>
                      <p className="text-sm mt-1">{comentario.texto}</p>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-6 text-muted-foreground">
                  <MessageSquare className="h-12 w-12 mx-auto mb-2 opacity-20" />
                  <p>Nenhum comentário ainda</p>
                </div>
              )}

              <form onSubmit={handleCommentSubmit} className="mt-4">
                <Textarea
                  placeholder="Adicione um comentário..."
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  className="min-h-24 mb-2"
                />
                <div className="flex justify-end">
                  <Button type="submit">Comentar</Button>
                </div>
              </form>
            </div>
          </TabsContent>

          <TabsContent value="historico" className="space-y-4 mt-4">
            <h3 className="text-sm font-medium">Histórico de Alterações</h3>
            
            <Card>
              <CardContent className="p-4">
                {taskHistory.length > 0 ? (
                  <div className="space-y-4">
                    {taskHistory
                      .filter(item => item.taskId === task.id)
                      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
                      .map((item) => (
                        <div key={item.id} className="flex gap-3">
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={item.user.avatar} />
                            <AvatarFallback>
                              {item.user.name.split(" ").map(n => n[0]).join("")}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex-1">
                            <div className="flex justify-between">
                              <div className="flex items-center gap-2">
                                <span className="font-medium">{item.user.name}</span>
                                <Badge variant="outline" className="text-xs">
                                  {item.action === "created" ? "Criou" :
                                   item.action === "updated" ? "Atualizou" :
                                   item.action === "deleted" ? "Excluiu" :
                                   item.action === "status_changed" ? "Alterou Status" :
                                   item.action === "assigned" ? "Atribuiu" : "Comentou"}
                                </Badge>
                              </div>
                              <span className="text-xs text-muted-foreground">
                                {new Date(item.timestamp).toLocaleString('pt-BR')}
                              </span>
                            </div>
                            <p className="text-sm mt-1">
                              {item.details.description}
                              {item.details.field && item.details.oldValue && item.details.newValue && (
                                <span className="text-xs block mt-1 text-muted-foreground">
                                  {item.details.field}: <span className="line-through">{item.details.oldValue}</span> → {item.details.newValue}
                                </span>
                              )}
                            </p>
                          </div>
                        </div>
                      ))}
                  </div>
                ) : (
                  <div className="text-center py-6 text-muted-foreground">
                    <Activity className="h-12 w-12 mx-auto mb-2 opacity-20" />
                    <p>Sem registro de atividades</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <DialogFooter className="mt-6">
          <Button variant="ghost" onClick={onClose}>Fechar</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}