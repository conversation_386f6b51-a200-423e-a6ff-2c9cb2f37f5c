import React, { useState } from 'react';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle,
  CardDescription,
  CardFooter 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { 
  MoreH<PERSON>zon<PERSON>, 
  Edit, 
  Trash2, 
  Co<PERSON>, 
  Plus,
  X,
  Check,
} from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { TaskTemplate } from '../types';
import { v4 as uuidv4 } from 'uuid';
import { useToast } from '@/components/ui/use-toast';
import { formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface TaskTemplatesProps {
  templates: TaskTemplate[];
  onCreateTemplate: (template: TaskTemplate) => void;
  onUpdateTemplate: (templateId: string, template: TaskTemplate) => void;
  onDeleteTemplate: (templateId: string) => void;
  onCreateTaskFromTemplate: (template: TaskTemplate) => void;
}

export const TaskTemplates: React.FC<TaskTemplatesProps> = ({
  templates,
  onCreateTemplate,
  onUpdateTemplate,
  onDeleteTemplate,
  onCreateTaskFromTemplate
}) => {
  const { toast } = useToast();
  const [selectedTemplate, setSelectedTemplate] = useState<TaskTemplate | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<TaskTemplate | null>(null);
  const [newSubtaskTitle, setNewSubtaskTitle] = useState('');
  const [newTag, setNewTag] = useState('');

  // Funções para gerenciar modelos
  const handleViewTemplate = (template: TaskTemplate) => {
    setSelectedTemplate(template);
    setIsViewDialogOpen(true);
  };

  const handleEditTemplate = (template: TaskTemplate) => {
    setEditingTemplate({...template});
    setIsEditDialogOpen(true);
  };

  const handleSaveTemplate = () => {
    if (!editingTemplate) return;
    
    if (editingTemplate.id) {
      // Atualizar modelo existente
      onUpdateTemplate(editingTemplate.id, editingTemplate);
    } else {
      // Criar novo modelo
      const newTemplate: TaskTemplate = {
        ...editingTemplate,
        id: uuidv4(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      onCreateTemplate(newTemplate);
    }
    
    setIsEditDialogOpen(false);
    setEditingTemplate(null);
    
    toast({
      title: editingTemplate.id ? "Modelo atualizado" : "Modelo criado",
      description: editingTemplate.id 
        ? "O modelo foi atualizado com sucesso!" 
        : "Um novo modelo foi criado com sucesso!",
    });
  };

  const handleNewTemplate = () => {
    const inicializarNovoTemplate = () => {
      return {
        id: '',
        titulo: '',
        descricao: '',
        status: 'pendente',
        prioridade: 'media',
        subtarefas: [],
        tags: [],
        created_at: '',
        updated_at: '',
        created_by: ''
      };
    };

    setEditingTemplate(inicializarNovoTemplate());
    setIsEditDialogOpen(true);
  };

  const handleAddSubtask = () => {
    if (!editingTemplate || !newSubtaskTitle.trim()) return;
    
    setEditingTemplate({
      ...editingTemplate,
      subtarefas: [
        ...editingTemplate.subtarefas,
        {
          id: uuidv4(),
          titulo: newSubtaskTitle,
          concluida: false
        }
      ]
    });
    
    setNewSubtaskTitle('');
  };

  const handleRemoveSubtask = (id: string) => {
    if (!editingTemplate) return;
    
    setEditingTemplate({
      ...editingTemplate,
      subtarefas: editingTemplate.subtarefas.filter(st => st.id !== id)
    });
  };

  const handleAddTag = () => {
    if (!editingTemplate || !newTag.trim()) return;
    
    const tags = editingTemplate.tags || [];
    
    setEditingTemplate({
      ...editingTemplate,
      tags: [...tags, newTag]
    });
    
    setNewTag('');
  };

  const handleRemoveTag = (tag: string) => {
    if (!editingTemplate) return;
    
    const tags = editingTemplate.tags || [];
    
    setEditingTemplate({
      ...editingTemplate,
      tags: tags.filter(t => t !== tag)
    });
  };

  const handleCreateTask = (template: TaskTemplate) => {
    onCreateTaskFromTemplate(template);
    
    toast({
      title: "Tarefa criada",
      description: "Uma nova tarefa foi criada a partir do modelo!",
    });
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">Modelos de Tarefas</h2>
        <Button onClick={handleNewTemplate} className="flex items-center gap-1">
          <Plus className="h-4 w-4" />
          Novo Modelo
        </Button>
      </div>
      
      {templates.length === 0 ? (
        <Card>
          <CardContent className="p-6 text-center">
            <p className="text-muted-foreground mb-4">Nenhum modelo de tarefa criado ainda.</p>
            <Button onClick={handleNewTemplate}>Criar Modelo</Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
          {templates.map(template => (
            <Card key={template.id} className="overflow-hidden">
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <CardTitle className="text-lg">{template.titulo}</CardTitle>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <span className="sr-only">Abrir menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleViewTemplate(template)}>
                        Visualizar
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleEditTemplate(template)}>
                        <Edit className="mr-2 h-4 w-4" />
                        Editar
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleCreateTask(template)}>
                        <Copy className="mr-2 h-4 w-4" />
                        Criar Tarefa
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem 
                        onClick={() => onDeleteTemplate(template.id)}
                        className="text-destructive focus:text-destructive"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Excluir
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>
              <CardContent className="pb-2">
                {template.descricao && (
                  <p className="text-sm text-muted-foreground line-clamp-2 mb-2">
                    {template.descricao}
                  </p>
                )}
                <div className="flex flex-wrap gap-1 mt-2">
                  {template.tags && template.tags.map(tag => (
                    <Badge key={tag} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </CardContent>
              <CardFooter className="pt-2 text-xs text-muted-foreground">
                Criado {formatDistanceToNow(new Date(template.created_at), { addSuffix: true, locale: ptBR })}
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
      
      {/* Modal de Visualização */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>{selectedTemplate?.titulo}</DialogTitle>
            <DialogDescription>
              Detalhes do modelo de tarefa
            </DialogDescription>
          </DialogHeader>
          
          {selectedTemplate && (
            <div className="space-y-4 my-4">
              {selectedTemplate.descricao && (
                <div>
                  <h4 className="font-medium mb-1">Descrição</h4>
                  <p className="text-sm">{selectedTemplate.descricao}</p>
                </div>
              )}
              
              <div>
                <h4 className="font-medium mb-1">Status</h4>
                <p className="text-sm">{selectedTemplate.status || "Não definido"}</p>
              </div>
              
              <div>
                <h4 className="font-medium mb-1">Prioridade</h4>
                <p className="text-sm">{selectedTemplate.prioridade || "Não definida"}</p>
              </div>
              
              {selectedTemplate.subtarefas.length > 0 && (
                <div>
                  <h4 className="font-medium mb-1">Subtarefas</h4>
                  <ul className="space-y-1">
                    {selectedTemplate.subtarefas.map(subtask => (
                      <li key={subtask.id} className="text-sm flex items-start gap-2">
                        <div className="h-4 w-4 mt-0.5 border rounded flex-shrink-0" />
                        {subtask.titulo}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
              
              {selectedTemplate.tags && selectedTemplate.tags.length > 0 && (
                <div>
                  <h4 className="font-medium mb-1">Tags</h4>
                  <div className="flex flex-wrap gap-1">
                    {selectedTemplate.tags.map(tag => (
                      <Badge key={tag} variant="secondary">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsViewDialogOpen(false)}>
              Fechar
            </Button>
            <Button 
              onClick={() => {
                if (selectedTemplate) {
                  handleCreateTask(selectedTemplate);
                  setIsViewDialogOpen(false);
                }
              }}
            >
              Criar Tarefa
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Modal de Edição/Criação */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>
              {editingTemplate?.id ? "Editar Modelo" : "Novo Modelo"}
            </DialogTitle>
            <DialogDescription>
              {editingTemplate?.id 
                ? "Edite os detalhes do modelo de tarefa" 
                : "Crie um novo modelo de tarefa"
              }
            </DialogDescription>
          </DialogHeader>
          
          {editingTemplate && (
            <div className="space-y-4 my-4">
              <div className="space-y-2">
                <Label htmlFor="titulo">Título</Label>
                <Input 
                  id="titulo" 
                  value={editingTemplate.titulo}
                  onChange={(e) => setEditingTemplate({
                    ...editingTemplate,
                    titulo: e.target.value
                  })}
                  placeholder="Título do modelo"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="descricao">Descrição</Label>
                <Textarea 
                  id="descricao" 
                  value={editingTemplate.descricao || ''}
                  onChange={(e) => setEditingTemplate({
                    ...editingTemplate,
                    descricao: e.target.value
                  })}
                  placeholder="Descrição do modelo"
                  rows={3}
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="status">Status</Label>
                  <Select 
                    value={editingTemplate.status || 'pendente'}
                    onValueChange={(value) => setEditingTemplate({
                      ...editingTemplate,
                      status: value
                    })}
                  >
                    <SelectTrigger id="status">
                      <SelectValue placeholder="Selecione o status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pendente">Pendente</SelectItem>
                      <SelectItem value="em_andamento">Em Andamento</SelectItem>
                      <SelectItem value="concluida">Concluída</SelectItem>
                      <SelectItem value="bloqueada">Bloqueada</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="prioridade">Prioridade</Label>
                  <Select 
                    value={editingTemplate.prioridade || 'media'}
                    onValueChange={(value) => setEditingTemplate({
                      ...editingTemplate,
                      prioridade: value
                    })}
                  >
                    <SelectTrigger id="prioridade">
                      <SelectValue placeholder="Selecione a prioridade" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="alta">Alta</SelectItem>
                      <SelectItem value="media">Média</SelectItem>
                      <SelectItem value="baixa">Baixa</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="space-y-2">
                <Label>Subtarefas</Label>
                <div className="space-y-2">
                  <div className="flex gap-2">
                    <Input 
                      value={newSubtaskTitle}
                      onChange={(e) => setNewSubtaskTitle(e.target.value)}
                      placeholder="Título da subtarefa"
                    />
                    <Button 
                      variant="outline" 
                      onClick={handleAddSubtask}
                      type="button"
                    >
                      Adicionar
                    </Button>
                  </div>
                  
                  {editingTemplate.subtarefas.length > 0 && (
                    <ul className="space-y-2 mt-2">
                      {editingTemplate.subtarefas.map(subtask => (
                        <li key={subtask.id} className="flex items-center gap-2 text-sm">
                          <div className="flex-1">{subtask.titulo}</div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRemoveSubtask(subtask.id)}
                            className="h-7 w-7 p-0"
                          >
                            <X className="h-4 w-4" />
                            <span className="sr-only">Remover</span>
                          </Button>
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              </div>
              
              <div className="space-y-2">
                <Label>Tags</Label>
                <div className="space-y-2">
                  <div className="flex gap-2">
                    <Input 
                      value={newTag}
                      onChange={(e) => setNewTag(e.target.value)}
                      placeholder="Nova tag"
                    />
                    <Button 
                      variant="outline" 
                      onClick={handleAddTag}
                      type="button"
                    >
                      Adicionar
                    </Button>
                  </div>
                  
                  {editingTemplate.tags && editingTemplate.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      {editingTemplate.tags.map(tag => (
                        <Badge 
                          key={tag} 
                          variant="secondary"
                          className="flex items-center gap-1"
                        >
                          {tag}
                          <X 
                            className="h-3 w-3 cursor-pointer" 
                            onClick={() => handleRemoveTag(tag)}
                          />
                        </Badge>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
          
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => {
                setIsEditDialogOpen(false);
                setEditingTemplate(null);
              }}
            >
              Cancelar
            </Button>
            <Button onClick={handleSaveTemplate}>
              Salvar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};