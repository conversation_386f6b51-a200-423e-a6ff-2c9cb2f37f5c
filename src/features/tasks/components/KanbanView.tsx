import { Plus } from "lucide-react";
import { Fragment, useState } from "react";
import { Button } from "@/components/ui/button";
import { 
  DragDropContext, 
  Droppable, 
  Draggable, 
  DropResult 
} from "@hello-pangea/dnd";
import { <PERSON><PERSON><PERSON> } from "../types";
import { TaskCard } from "./TaskCard";

interface KanbanColumnProps {
  title: string;
  icon: React.ReactNode;
  tasks: Tarefa[];
  status: "pendente" | "em_andamento" | "concluida";
  onTaskClick: (task: Tarefa) => void;
  onStatusChange: (taskId: string, newStatus: "pendente" | "em_andamento" | "concluida") => void;
  onEdit: (taskId: string) => void;
  onDelete: (taskId: string) => void;
}

function KanbanColumn({ 
  title, 
  icon, 
  tasks, 
  status, 
  onTaskClick, 
  onStatusChange, 
  onEdit, 
  onDelete 
}: KanbanColumnProps) {
  return (
    <div className="flex flex-col h-full min-h-[600px] min-w-[300px] max-w-[350px] bg-muted/40 rounded-lg">
      <div className="p-3 font-medium flex items-center justify-between border-b">
        <div className="flex items-center gap-2">
          {icon}
          <h3>{title}</h3>
          <span className="bg-muted rounded-full px-2 py-0.5 text-xs">
            {tasks.length}
          </span>
        </div>
        <Button variant="ghost" size="icon" className="h-8 w-8">
          <Plus className="h-4 w-4" />
        </Button>
      </div>
      
      <Droppable droppableId={status}>
        {(provided) => (
          <div 
            ref={provided.innerRef}
            {...provided.droppableProps}
            className="flex-1 overflow-y-auto p-2 space-y-2 h-full"
          >
            {tasks.map((task, index) => (
              <Draggable key={task.id} draggableId={task.id} index={index}>
                {(provided) => (
                  <div
                    ref={provided.innerRef}
                    {...provided.draggableProps}
                    {...provided.dragHandleProps}
                  >
                    <TaskCard
                      task={task}
                      onClick={onTaskClick}
                      onStatusChange={onStatusChange}
                      onEdit={onEdit}
                      onDelete={onDelete}
                    />
                  </div>
                )}
              </Draggable>
            ))}
            {provided.placeholder}
          </div>
        )}
      </Droppable>
    </div>
  );
}

interface KanbanViewProps {
  tasks: Tarefa[];
  onTaskClick: (task: Tarefa) => void;
  onDragEnd: (result: DropResult) => void;
  onStatusChange: (taskId: string, newStatus: "pendente" | "em_andamento" | "concluida") => void;
  onEdit: (taskId: string) => void;
  onDelete: (taskId: string) => void;
}

export function KanbanView({ 
  tasks, 
  onTaskClick, 
  onDragEnd, 
  onStatusChange, 
  onEdit, 
  onDelete 
}: KanbanViewProps) {
  // Agrupar tarefas por status
  const pendentes = tasks.filter(task => task.status === "pendente");
  const emAndamento = tasks.filter(task => task.status === "em_andamento");
  const concluidas = tasks.filter(task => task.status === "concluida");

  return (
    <DragDropContext onDragEnd={onDragEnd}>
      <div className="flex gap-4 overflow-x-auto pb-4 pt-2 px-1">
        <KanbanColumn
          title="Pendentes"
          icon={<div className="h-3 w-3 rounded-full bg-orange-500" />}
          tasks={pendentes}
          status="pendente"
          onTaskClick={onTaskClick}
          onStatusChange={onStatusChange}
          onEdit={onEdit}
          onDelete={onDelete}
        />
        
        <KanbanColumn
          title="Em Andamento"
          icon={<div className="h-3 w-3 rounded-full bg-blue-500" />}
          tasks={emAndamento}
          status="em_andamento"
          onTaskClick={onTaskClick}
          onStatusChange={onStatusChange}
          onEdit={onEdit}
          onDelete={onDelete}
        />
        
        <KanbanColumn
          title="Concluídas"
          icon={<div className="h-3 w-3 rounded-full bg-green-500" />}
          tasks={concluidas}
          status="concluida"
          onTaskClick={onTaskClick}
          onStatusChange={onStatusChange}
          onEdit={onEdit}
          onDelete={onDelete}
        />
      </div>
    </DragDropContext>
  );
} 