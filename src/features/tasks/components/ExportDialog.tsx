import { useState } from "react";
import { 
  Download, 
  FileText,
  FileSpreadsheet,
  FileJson,
  AlertCircle
} from "lucide-react";
import { 
  <PERSON><PERSON>, 
  DialogContent, 
  DialogHeader, 
  <PERSON><PERSON>T<PERSON>le, 
  DialogFooter,
  DialogDescription 
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { 
  Select, 
  SelectTrigger, 
  SelectValue, 
  SelectContent, 
  SelectItem 
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { 
  Card, 
  CardContent 
} from "@/components/ui/card";
import {
  Alert,
  AlertDescription
} from "@/components/ui/alert";
import { toast } from "@/components/ui/use-toast";
import { Tarefa } from "../types";

interface ExportDialogProps {
  isOpen: boolean;
  onClose: () => void;
  tarefas: Tarefa[];
}
import { exportUtils } from "../utils/taskUtils";

// Importações para exportação
import * as XLSX from 'xlsx';
import { jsPDF } from 'jspdf';
import 'jspdf-autotable';

export function ExportDialog({ isOpen, onClose, tarefas }: ExportDialogProps) {
  const [formato, setFormato] = useState("excel");
  const [incluirConcluidas, setIncluirConcluidas] = useState(true);
  const [incluirTodosOsCampos, setIncluirTodosOsCampos] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Função para exportar para Excel
  const exportToExcel = (dados: any[]) => {
    try {
      const worksheet = XLSX.utils.json_to_sheet(dados);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, "Tarefas");
      
      // Configurar larguras de coluna
      const colunas: any = [];
      if (dados.length > 0) {
        Object.keys(dados[0]).forEach(key => {
          colunas.push({ wch: Math.max(key.length, 15) });
        });
        worksheet["!cols"] = colunas;
      }
      
      // Estilo de cabeçalho
      const headerRange = XLSX.utils.decode_range(worksheet['!ref'] || "A1");
      for (let col = headerRange.s.c; col <= headerRange.e.c; col++) {
        const cellRef = XLSX.utils.encode_cell({ r: 0, c: col });
        if (worksheet[cellRef]) {
          worksheet[cellRef].s = {
            font: { bold: true },
            fill: { fgColor: { rgb: "EFEFEF" } }
          };
        }
      }
      
      // Exportar
      XLSX.writeFile(workbook, "Tarefas-Exportadas.xlsx");
      return true;
    } catch (error) {
      console.error("Erro ao exportar para Excel:", error);
      return false;
    }
  };

  // Função para exportar para PDF
  const exportToPDF = (dados: any[]) => {
    try {
      const doc = new jsPDF();
      const tableColumn = Object.keys(dados[0]);
      const tableRows: any[] = [];

      dados.forEach(item => {
        const row = Object.values(item).map(value => 
          value === null || value === undefined ? "-" : String(value)
        );
        tableRows.push(row);
      });

      doc.text("Relatório de Tarefas", 14, 16);
      doc.text(`Gerado em: ${new Date().toLocaleDateString("pt-BR")}`, 14, 22);
      
      (doc as any).autoTable({
        head: [tableColumn],
        body: tableRows,
        startY: 30,
        styles: {
          fontSize: 9,
          cellPadding: 3,
          overflow: 'linebreak'
        },
        columnStyles: {
          0: { cellWidth: 15 }, // ID
          1: { cellWidth: 30 }, // Título
          2: { cellWidth: 'auto' } // Outros
        },
        headStyles: {
          fillColor: [59, 130, 246],
          textColor: 255,
          fontStyle: 'bold'
        },
        alternateRowStyles: {
          fillColor: [245, 245, 245]
        }
      });

      doc.save("Tarefas-Exportadas.pdf");
      return true;
    } catch (error) {
      console.error("Erro ao exportar para PDF:", error);
      return false;
    }
  };

  // Função para exportar para CSV
  const exportToCSV = (dados: any[]) => {
    try {
      // Converter objetos para CSV
      const header = Object.keys(dados[0]).join(",");
      const rows = dados.map(obj => 
        Object.values(obj).map(value => 
          typeof value === "string" && value.includes(",") 
            ? `"${value}"` 
            : String(value)
        ).join(",")
      );
      
      const csv = [header, ...rows].join("\n");
      
      // Criar Blob e download
      const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.setAttribute("href", url);
      link.setAttribute("download", "Tarefas-Exportadas.csv");
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      return true;
    } catch (error) {
      console.error("Erro ao exportar para CSV:", error);
      return false;
    }
  };

  // Função para lidar com a exportação
  const handleExportar = () => {
    setIsLoading(true);
    
    try {
      // Filtrar tarefas de acordo com as opções
      let tarefasFiltradas = [...tarefas];
      if (!incluirConcluidas) {
        tarefasFiltradas = tarefasFiltradas.filter(t => t.status !== "concluida");
      }
      
      // Preparar os dados para exportação
      const dados = exportUtils.prepareDataForExport(tarefasFiltradas);
      
      // Realizar exportação conforme o formato escolhido
      let sucesso = false;
      
      switch (formato) {
        case "excel":
          sucesso = exportToExcel(dados);
          break;
        case "pdf":
          sucesso = exportToPDF(dados);
          break;
        case "csv":
          sucesso = exportToCSV(dados);
          break;
        case "json":
          // Exportar JSON
          const json = JSON.stringify(dados, null, 2);
          const blob = new Blob([json], { type: "application/json" });
          const url = URL.createObjectURL(blob);
          const link = document.createElement("a");
          link.href = url;
          link.download = "Tarefas-Exportadas.json";
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          sucesso = true;
          break;
      }
      
      if (sucesso) {
        toast({
          title: "Dados exportados com sucesso!",
          description: `Os dados foram exportados no formato ${formato.toUpperCase()}.`
        });
        onClose();
      } else {
        toast({
          variant: "destructive",
          title: "Erro na exportação",
          description: "Ocorreu um erro ao exportar os dados."
        });
      }
    } catch (error) {
      console.error("Erro na exportação:", error);
      toast({
        variant: "destructive",
        title: "Erro na exportação",
        description: "Ocorreu um erro inesperado ao exportar os dados."
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md" aria-describedby="export-dialog-description">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Exportar Tarefas
          </DialogTitle>
          <DialogDescription id="export-dialog-description">
            Selecione o formato e as opções para exportar suas tarefas.
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-6 py-4">
          <div className="space-y-2">
            <Label htmlFor="formato">Formato de Exportação</Label>
            <Select value={formato} onValueChange={setFormato}>
              <SelectTrigger>
                <SelectValue placeholder="Selecione o formato" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="excel">
                  <div className="flex items-center gap-2">
                    <FileSpreadsheet className="h-4 w-4" />
                    Excel (XLSX)
                  </div>
                </SelectItem>
                <SelectItem value="pdf">
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    PDF
                  </div>
                </SelectItem>
                <SelectItem value="csv">
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    CSV
                  </div>
                </SelectItem>
                <SelectItem value="json">
                  <div className="flex items-center gap-2">
                    <FileJson className="h-4 w-4" />
                    JSON
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <Card>
            <CardContent className="p-4 space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="incluirConcluidas" 
                  checked={incluirConcluidas}
                  onCheckedChange={() => setIncluirConcluidas(!incluirConcluidas)}
                />
                <Label htmlFor="incluirConcluidas">Incluir tarefas concluídas</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="incluirTodosCampos" 
                  checked={incluirTodosOsCampos}
                  onCheckedChange={() => setIncluirTodosOsCampos(!incluirTodosOsCampos)}
                />
                <Label htmlFor="incluirTodosCampos">Incluir todos os campos</Label>
              </div>
            </CardContent>
          </Card>
          
          <Alert className="bg-blue-50 text-blue-800 border-blue-200">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="text-xs">
              Serão exportadas {incluirConcluidas ? tarefas.length : tarefas.filter(t => t.status !== "concluida").length} tarefas.
              {formato === "excel" && " O arquivo Excel pode ser editado posteriormente."}
              {formato === "pdf" && " O arquivo PDF é ideal para impressão."}
            </AlertDescription>
          </Alert>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>Cancelar</Button>
          <Button onClick={handleExportar} disabled={isLoading}>
            {isLoading ? (
              <>Processando...</>
            ) : (
              <>
                <Download className="h-4 w-4 mr-2" />
                Exportar
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 