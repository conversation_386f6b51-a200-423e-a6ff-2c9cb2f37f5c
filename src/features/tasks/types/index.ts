// Tipos relacionados às tarefas

export interface Participante {
  id: string;
  nome: string;
  avatar: string;
}

export interface Subtarefa {
  id: string;
  titulo: string;
  concluida: boolean;
  prazo?: string;
}

export interface Tarefa {
  id: string;
  titulo: string;
  descricao: string;
  status: "pendente" | "em_andamento" | "concluida";
  prioridade: "baixa" | "media" | "alta";
  responsavel: Participante;
  participantes: Participante[];
  prazo: string;
  tags: string[];
  progresso: number;
  subtarefas: Subtarefa[];
  dataCriacao: string;
  comentarios: Array<{
    usuario: Participante;
    texto: string;
    data: string;
  }>;
  processoVinculado?: string;
  arquivosAnexos?: Array<{
    nome: string;
    tipo: string;
    tamanho: string;
    data: string;
  }>;
}

export interface TaskHistory {
  id: string;
  taskId: string;
  action: "created" | "updated" | "deleted" | "status_changed" | "assigned" | "comment_added";
  timestamp: string;
  user: {
    id: string;
    name: string;
    avatar: string;
  };
  details: {
    field?: string;
    oldValue?: string;
    newValue?: string;
    description: string;
  };
}

export interface TaskTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  defaultPriority: "baixa" | "media" | "alta";
  defaultAssignee?: string;
  defaultTags: string[];
  defaultChecklist: {
    text: string;
    required: boolean;
  }[];
  requiredDocuments: {
    name: string;
    type: string;
    required: boolean;
  }[];
}

export interface TaskReminder {
  id: string;
  taskId: string;
  type: "deadline" | "update" | "review" | "custom";
  timestamp: string;
  notificationType: "email" | "system" | "both";
  message: string;
  status: "pending" | "sent" | "cancelled";
  recipients: string[];
}

export interface TarefaRecorrente extends Tarefa {
  recorrencia: {
    tipo: "diaria" | "semanal" | "mensal";
    diasDaSemana?: number[]; // 0-6 para semanal
    diaDoMes?: number; // 1-31 para mensal
    horario: string;
    proximaOcorrencia: string;
  };
}

export interface TaskGroup {
  id: string;
  title: string;
  tasks: Tarefa[];
  icon: string;
  color: string;
}

export interface TaskMetrics {
  total: number;
  concluidas: number;
  emAndamento: number;
  atrasadas: number;
  proximasDoPrazo: number;
  semResponsavel: number;
  prioridadeAlta: number;
}

export interface TaskDetailsModalProps {
  task: Tarefa | null;
  isOpen: boolean;
  onClose: () => void;
  taskHistory: TaskHistory[];
}

export interface CreateTemplateDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateTemplate: (template: TaskTemplate) => void;
}

export interface CreateFromTemplateDialogProps {
  isOpen: boolean;
  onClose: () => void;
  template: TaskTemplate | null;
  onCreateTask: (task: Tarefa) => void;
}

export interface ExportDialogProps {
  isOpen: boolean;
  onClose: () => void;
  tarefas: Tarefa[];
}

export interface TaskManagerProps {
  defaultView?: "lista" | "quadro" | "calendario" | "metricas";
  showHeader?: boolean;
  processoId?: string;
  clienteId?: string;
  usuarioId?: string;
  showFilters?: boolean;
} 