export interface Usuario {
  id: string;
  nome: string;
  email: string;
  avatar?: string;
  cargo?: string;
}

export interface Comentario {
  id: string;
  texto: string;
  autor: Usuario;
  dataCriacao: string;
  anexos?: ArquivoAnexo[];
}

export interface ArquivoAnexo {
  id: string;
  nome: string;
  url: string;
  tipo: string;
  tamanho: number;
  dataUpload: string;
  uploadadoPor: Usuario;
}

export interface Subtarefa {
  id: string;
  titulo: string;
  concluida: boolean;
  responsavel?: Usuario;
  prazo?: string;
}

export interface Tarefa {
  id: string;
  titulo: string;
  descricao: string;
  status: string;
  statusCor?: string;
  prioridade: string;
  responsavel?: Usuario;
  prazo?: string;
  dataCriacao: string;
  dataUltimaAtualizacao?: string;
  dataConclusao?: string;
  progresso: number;
  tags: string[];
  participantes: Usuario[];
  subtarefas: Subtarefa[];
  comentarios: Comentario[];
  arquivosAnexos: ArquivoAnexo[];
  processoVinculado?: string;
  precatorioId?: string;
  clienteId?: string;
  area?: 'PRECATORIO' | 'RPV' | 'AMBOS';
}

export interface TaskGroup {
  id: string;
  title: string;
  color?: string;
  tasks: Tarefa[];
}

export interface SubtaskTemplate {
  id: string;
  titulo: string;
  concluida: boolean;
}

export interface TaskTemplate {
  id: string;
  titulo: string;
  descricao?: string;
  status?: string;
  prioridade?: string;
  subtarefas: SubtaskTemplate[];
  tags?: string[];
  created_at: string;
  updated_at?: string;
  created_by: string;
}

export interface TaskMetricsType {
  total: number;
  concluidas: number;
  emAndamento: number;
  pendentes: number;
  atrasadas: number;
  porPrioridade: Record<string, number>;
  porResponsavel: Record<string, number>;
  porTag: Record<string, number>;
}

export interface TaskManagerProps {
  defaultView?: string;
  showHeader?: boolean;
  processoId?: string;
  clienteId?: string;
  usuarioId?: string;
  showFilters?: boolean;
  areaFilter?: 'PRECATORIO' | 'RPV' | 'AMBOS' | null;
}
