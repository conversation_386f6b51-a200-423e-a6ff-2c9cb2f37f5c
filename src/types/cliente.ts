export interface Cliente {
  id: string;
  nome: string;
  email: string;
  telefone: string;
  cpf: string;
  endereco: string;
  cidade: string;
  estado: string;
  cep: string;
  status: 'ativo' | 'inativo' | 'pendente';
  data_cadastro: string;
  ultima_atualizacao: string;
}

export interface Contato {
  id: string;
  cliente_id: string;
  nome: string;
  cargo: string;
  email: string;
  telefone: string;
  principal: boolean;
}

export interface Precatorio {
  id: string;
  cliente_id: string;
  numero: string;
  valor: number;
  status: 'analise' | 'em_processamento' | 'pago' | 'cancelado';
  tribunal: string;
  data_deposito: string | null;
  data_cadastro: string;
  observacoes: string;
}

export interface Documento {
  id: string;
  cliente_id: string;
  nome: string;
  tipo: 'rg' | 'cpf' | 'comprovante_residencia' | 'procuracao' | 'outros';
  status: 'pendente' | 'aprovado' | 'rejeitado';
  data_upload: string;
  usuario_id: string;
  usuario_nome: string;
  url: string;
}

export interface Atividade {
  id: string;
  cliente_id: string;
  tipo: 'documento' | 'precatorio' | 'contato' | 'atualizacao';
  descricao: string;
  data: string;
  usuario_id: string;
  usuario_nome: string;
  metadata: Record<string, any>;
}

export interface Compromisso {
  id: string;
  cliente_id: string;
  titulo: string;
  descricao: string;
  data: string;
  status: 'agendado' | 'concluido' | 'cancelado';
  tipo: 'reuniao' | 'prazo' | 'audiencia';
  participantes: string[];
}