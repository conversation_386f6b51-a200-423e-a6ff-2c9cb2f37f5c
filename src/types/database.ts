/**
 * Comprehensive TypeScript interfaces for Supabase database tables
 * This file replaces 'any' types with specific interfaces
 */

// Base types for common fields
export interface BaseEntity {
  id: string;
  created_at: string;
  updated_at: string;
}

export interface SoftDeletable {
  is_deleted?: boolean;
  deleted_at?: string;
}

// User and Profile types
export interface UserProfile extends BaseEntity {
  name: string | null;
  email: string | null;
  avatar_url: string | null;
  role: UserRole;
  phone?: string | null;
  department?: string | null;
  position?: string | null;
  is_active: boolean;
}

export type UserRole = 'admin' | 'gerente_precatorio' | 'gerente_operacional' | 'assistente' | 'viewer';

// Client types
export interface Cliente extends BaseEntity {
  nome: string;
  email: string | null;
  telefone: string | null;
  cpf_cnpj: string | null;
  endereco: string | null;
  cidade: string | null;
  estado: string | null;
  cep: string | null;
  status: ClienteStatus;
  observacoes?: string | null;
}

export type ClienteStatus = 'ativo' | 'inativo' | 'pendente' | 'suspenso';

export interface ClienteComTotais extends Cliente {
  total_precatorios: number;
  valor_total: number;
  ultimo_contato?: string;
}

// Precatorio types
export interface Precatorio extends BaseEntity, SoftDeletable {
  numero_precatorio: string;
  valor_total: number;
  desconto?: number;
  valor_liquido?: number;
  status: string;
  status_id: string | null;
  beneficiario_id: string | null;
  responsavel_id: string | null;
  tribunal_id: string | null;
  data_previsao_pagamento: string | null;
  natureza: string | null;
  observacoes: string | null;
  tags: string[] | null;
  prioridade: PrecatorioPrioridade;
  tipo: PrecatorioTipo;
  categoria: PrecatorioCategoria;
  captador_id: string | null;
  documentos?: PrecatorioDocumento[];
}

export type PrecatorioPrioridade = 'baixa' | 'media' | 'alta' | 'urgente';
export type PrecatorioTipo = 'COMUM' | 'ALIMENTAR' | 'PEQUENO_VALOR';
export type PrecatorioCategoria = 'PRECATORIO' | 'RPV';

export interface PrecatorioDocumento {
  id: string;
  nome: string;
  tipo: string;
  url: string;
  tamanho: number;
  uploaded_at: string;
}

// Status types
export interface StatusPrecatorio extends BaseEntity {
  nome: string;
  codigo: string;
  cor: string;
  descricao?: string | null;
  ordem: number;
  ativo: boolean;
  is_final: boolean;
}

// Kanban types
export interface KanbanColuna extends BaseEntity {
  nome: string;
  cor: string;
  tipo: KanbanTipo;
  ordem: number;
  status_id: string;
  status_uuid?: string;
  ativo: boolean;
  is_default: boolean;
  user_id?: string | null;
  configuracoes?: KanbanColunaConfiguracoes;
}

export type KanbanTipo = 'PRECATORIO' | 'RPV' | 'AMBOS';

export interface KanbanColunaConfiguracoes {
  limite_cards?: number;
  auto_move?: boolean;
  notificacoes?: boolean;
  cor_personalizada?: string;
}

export interface KanbanColunaPersonalizada extends KanbanColuna {
  user_id: string;
  is_shared: boolean;
  shared_with?: string[];
}

// Permission types
export interface CustomRole extends BaseEntity {
  name: string;
  description?: string | null;
  is_system: boolean;
  permissions: string[];
}

export interface RoleDefaultPermission extends BaseEntity {
  role_id: string;
  resource_type: string;
  action: PermissionAction;
  allowed: boolean;
}

export interface UserSpecificPermission extends BaseEntity {
  user_id: string;
  resource_type: string;
  resource_id?: string | null;
  action: PermissionAction;
  allowed: boolean;
}

export type PermissionAction = 'view' | 'create' | 'edit' | 'delete' | 'manage';

export interface DataVisibilityConfig extends BaseEntity {
  user_id: string;
  resource_type: string;
  visibility_type: DataVisibilityType;
  allowed_user_ids?: string[];
  conditions?: Record<string, unknown>;
}

export type DataVisibilityType = 'own' | 'all' | 'specific_users' | 'department' | 'role_based';

// Task types
export interface Task extends BaseEntity, SoftDeletable {
  titulo: string;
  descricao?: string | null;
  status: TaskStatus;
  prioridade: TaskPrioridade;
  data_vencimento?: string | null;
  responsavel_id: string | null;
  criado_por: string;
  cliente_id?: string | null;
  precatorio_id?: string | null;
  tags?: string[];
  area: TaskArea;
  progresso: number;
  tempo_estimado?: number | null;
  tempo_gasto?: number | null;
}

export type TaskStatus = 'pendente' | 'em_andamento' | 'concluida' | 'cancelada' | 'pausada';
export type TaskPrioridade = 'baixa' | 'media' | 'alta' | 'urgente';
export type TaskArea = 'PRECATORIO' | 'RPV' | 'AMBOS' | 'GERAL';

export interface Subtask extends BaseEntity {
  task_id: string;
  titulo: string;
  concluida: boolean;
  ordem: number;
}

export interface TaskComment extends BaseEntity {
  task_id: string;
  user_id: string;
  comentario: string;
  is_internal: boolean;
}

// Document types
export interface Documento extends BaseEntity, SoftDeletable {
  nome: string;
  tipo: DocumentoTipo;
  conteudo?: string | null;
  url?: string | null;
  tamanho?: number | null;
  mime_type?: string | null;
  cliente_id?: string | null;
  precatorio_id?: string | null;
  criado_por: string;
  tags?: string[];
  is_template: boolean;
  template_data?: Record<string, unknown>;
}

export type DocumentoTipo = 'contrato' | 'procuracao' | 'certidao' | 'oficio' | 'relatorio' | 'outros';

// Event/Calendar types
export interface Evento extends BaseEntity {
  titulo: string;
  descricao?: string | null;
  data_inicio: string;
  data_fim: string;
  local?: string | null;
  tipo: EventoTipo;
  status: EventoStatus;
  participantes?: string[];
  cliente_id?: string | null;
  precatorio_id?: string | null;
  criado_por: string;
  cor?: string | null;
  is_all_day: boolean;
  recorrencia?: EventoRecorrencia;
}

export type EventoTipo = 'reuniao' | 'audiencia' | 'prazo' | 'lembrete' | 'outros';
export type EventoStatus = 'agendado' | 'confirmado' | 'cancelado' | 'concluido';

export interface EventoRecorrencia {
  tipo: 'diario' | 'semanal' | 'mensal' | 'anual';
  intervalo: number;
  dias_semana?: number[];
  data_fim?: string;
}

// Filter and search types
export interface FiltrosAvancados {
  status?: string[];
  prioridade?: PrecatorioPrioridade[];
  tipo?: PrecatorioTipo[];
  categoria?: PrecatorioCategoria[];
  valor_min?: number;
  valor_max?: number;
  data_inicio?: string;
  data_fim?: string;
  responsavel_id?: string[];
  tribunal_id?: string[];
  tags?: string[];
  cliente_id?: string[];
}

export interface SearchFilters {
  query?: string;
  filters?: Record<string, unknown>;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

// API Response types
export interface ApiResponse<T> {
  data: T | null;
  error: ApiError | null;
  count?: number;
  page?: number;
  total_pages?: number;
}

export interface ApiError {
  message: string;
  code?: string;
  details?: string;
  hint?: string;
}

// Dashboard and statistics types
export interface DashboardStats {
  total_precatorios: number;
  valor_total: number;
  precatorios_por_status: Record<string, number>;
  precatorios_por_tipo: Record<string, number>;
  crescimento_mensal: number;
  tarefas_pendentes: number;
  eventos_hoje: number;
  clientes_ativos: number;
}

export interface ChartData {
  name: string;
  value: number;
  color?: string;
}

// Custom view types
export interface CustomView extends BaseEntity {
  nome: string;
  descricao?: string | null;
  user_id: string;
  is_public: boolean;
  is_default: boolean;
  is_favorite: boolean;
  is_admin_created: boolean;
  is_system: boolean;
  layout: ViewLayout;
  icone?: string | null;
  cor?: string | null;
  colunas_selecionadas?: string[];
  filtros?: FiltrosAvancados;
  tags_selecionadas?: string[];
  configuracoes?: ViewConfiguracoes;
}

export type ViewLayout = 'kanban' | 'lista' | 'tabela' | 'calendario' | 'grafico';

export interface ViewConfiguracoes {
  items_per_page?: number;
  auto_refresh?: boolean;
  show_totals?: boolean;
  group_by?: string;
  chart_type?: string;
}
