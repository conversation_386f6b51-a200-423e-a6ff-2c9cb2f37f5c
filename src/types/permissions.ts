// Types for the advanced permissions system

// Basic permission types
export interface Permission {
  resource_type: string;
  action: string;
  allowed: boolean;
}

export interface SpecificPermission extends Permission {
  resource_id?: string;
}

// Task visibility settings
export interface TaskVisibilitySettings {
  can_see_own_tasks: boolean;
  can_see_team_tasks: boolean;
  can_see_all_tasks: boolean;
  visible_user_ids: string[];
}

// Page access settings
export interface PageAccessSetting {
  page_path: string;
  can_access: boolean;
}

// Complete user permissions data
export interface UserPermissionsData {
  role_permissions: Permission[];
  specific_permissions: SpecificPermission[];
  task_visibility: TaskVisibilitySettings;
  page_access: PageAccessSetting[];
}

// Custom role
export interface CustomRole {
  id: string;
  nome: string;
  descricao?: string;
  cor?: string;
  icone?: string;
  is_system: boolean;
  is_deleted?: boolean;
  created_at?: string;
  updated_at?: string;
  deleted_at?: string;
}

// Role default permission
export interface RoleDefaultPermission {
  id: string;
  role_id: string;
  resource_type: string;
  action: string;
  allowed: boolean;
  created_at?: string;
  updated_at?: string;
}

// User relationship (hierarchical)
export interface UserRelationship {
  id: string;
  supervisor_id: string;
  subordinate_id: string;
  created_at?: string;
  updated_at?: string;
}

// Custom view
export interface CustomView {
  id: string;
  name: string;
  description?: string;
  view_type: string;
  configuration: any;
  is_default: boolean;
  created_by: string;
  created_at?: string;
  updated_at?: string;
}

// User view assignment
export interface UserViewAssignment {
  id: string;
  user_id: string;
  view_id: string;
  created_at?: string;
}

// Permission log entry
export interface PermissionLogEntry {
  id: string;
  admin_id: string;
  user_id: string;
  action: string;
  resource_type: string;
  resource_id?: string;
  old_value?: any;
  new_value?: any;
  created_at: string;
  admin_name?: string;
  user_name?: string;
}

// Resource permission for UI
export interface ResourcePermission {
  resourceType: string;
  resourceName: string;
  icon?: React.ReactNode;
  actions: {
    action: string;
    label: string;
    allowed: boolean;
  }[];
}

// Page access for UI
export interface PageAccess {
  pagePath: string;
  pageLabel: string;
  icon?: React.ReactNode;
  canAccess: boolean;
}

// User with basic info for UI
export interface UserBasicInfo {
  id: string;
  name: string;
  email?: string;
  role?: string;
  avatar_url?: string;
}

// Permission matrix item for UI
export interface PermissionMatrixItem {
  userId: string;
  userName: string;
  userRole?: string;
  permissions: {
    [resourceType: string]: {
      [action: string]: boolean;
    };
  };
}

// User hierarchy node for UI
export interface UserHierarchyNode {
  id: string;
  name: string;
  role?: string;
  avatar_url?: string;
  children?: UserHierarchyNode[];
}

// Permissão de coluna do Kanban
export interface KanbanColumnPermission {
  id: string;
  column_id: string;
  user_id: string;
  can_view: boolean;
  can_edit: boolean;
  can_delete: boolean;
  can_move_cards: boolean;
  created_at: string;
  updated_at: string;
}

// Visualização personalizada do Kanban
export interface KanbanCustomView {
  id: string;
  nome: string;
  descricao?: string;
  user_id: string;
  is_public: boolean;
  is_default: boolean;
  is_favorite?: boolean;
  is_admin_created?: boolean;
  is_system?: boolean;
  layout?: string;
  icone?: string;
  cor?: string;
  colunas_selecionadas?: any;
  filtros?: any;
  tags_selecionadas?: any;
  created_at?: string;
  updated_at?: string;
}

// Papel de usuário
export type UserRole =
  | 'admin'
  | 'gerente_geral'
  | 'gerente_precatorio'
  | 'gerente_rpv'
  | 'captador'
  | 'operacional_precatorio'
  | 'operacional_rpv'
  | 'operacional_completo'
  | 'assistente';

// Tipo de permissão
export type PermissionType =
  // Permissões para clientes
  | 'visualizar_cliente'
  | 'criar_cliente'
  | 'editar_cliente'
  | 'excluir_cliente'

  // Permissões para precatórios
  | 'visualizar_precatorio'
  | 'criar_precatorio'
  | 'editar_precatorio'
  | 'excluir_precatorio'

  // Permissões para RPVs
  | 'visualizar_rpv'
  | 'criar_rpv'
  | 'editar_rpv'
  | 'excluir_rpv'

  // Permissões para tarefas
  | 'visualizar_tarefa'
  | 'criar_tarefa'
  | 'editar_tarefa'
  | 'excluir_tarefa'
  | 'visualizar_todas_tarefas'

  // Permissões para documentos
  | 'visualizar_documento'
  | 'criar_documento'
  | 'editar_documento'
  | 'excluir_documento'

  // Permissões para relatórios
  | 'visualizar_relatorio_precatorio'
  | 'visualizar_relatorio_rpv'
  | 'visualizar_relatorio_captacao'
  | 'visualizar_relatorio_completo'

  // Permissões para usuários e configurações
  | 'gerenciar_usuarios'
  | 'configurar_sistema'
  | 'gerenciar_permissoes';

// Tipo de recurso
export type ResourceType =
  | 'cliente'
  | 'precatorio'
  | 'rpv'
  | 'tarefa'
  | 'documento'
  | 'relatorio'
  | 'user'
  | 'system'
  | 'kanban_column'
  | 'kanban_custom_view';

// Ação de permissão
export type PermissionAction =
  | 'view'
  | 'create'
  | 'edit'
  | 'delete'
  | 'move'
  | 'view_all'
  | 'configure'
  | 'manage_roles';
