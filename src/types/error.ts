// Error types for the application

export class DatabaseError extends Error {
  public readonly code?: string;
  public readonly details?: any;

  constructor(message: string, originalError?: any) {
    super(message);
    this.name = 'DatabaseError';
    
    if (originalError) {
      this.code = originalError.code;
      this.details = originalError.details || originalError;
      this.stack = originalError.stack || this.stack;
    }
  }
}

export class ValidationError extends Error {
  public readonly field?: string;
  public readonly value?: any;

  constructor(message: string, field?: string, value?: any) {
    super(message);
    this.name = 'ValidationError';
    this.field = field;
    this.value = value;
  }
}

export class AuthenticationError extends Error {
  constructor(message: string = 'Authentication failed') {
    super(message);
    this.name = 'AuthenticationError';
  }
}

export class AuthorizationError extends Error {
  constructor(message: string = 'Access denied') {
    super(message);
    this.name = 'AuthorizationError';
  }
}

export class NetworkError extends Error {
  public readonly status?: number;

  constructor(message: string, status?: number) {
    super(message);
    this.name = 'NetworkError';
    this.status = status;
  }
}

export class CacheError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'CacheError';
  }
}

// Type guards
export function isDatabaseError(error: any): error is DatabaseError {
  return error instanceof DatabaseError;
}

export function isValidationError(error: any): error is ValidationError {
  return error instanceof ValidationError;
}

export function isAuthenticationError(error: any): error is AuthenticationError {
  return error instanceof AuthenticationError;
}

export function isAuthorizationError(error: any): error is AuthorizationError {
  return error instanceof AuthorizationError;
}

export function isNetworkError(error: any): error is NetworkError {
  return error instanceof NetworkError;
}

export function isCacheError(error: any): error is CacheError {
  return error instanceof CacheError;
}

// Error handling utilities
export function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  
  if (typeof error === 'string') {
    return error;
  }
  
  return 'An unknown error occurred';
}

export function getErrorCode(error: unknown): string | undefined {
  if (isDatabaseError(error) || isNetworkError(error)) {
    return error.code?.toString() || error.status?.toString();
  }
  
  return undefined;
}

export function formatErrorForUser(error: unknown): string {
  const message = getErrorMessage(error);
  
  if (isAuthenticationError(error)) {
    return 'Erro de autenticação. Por favor, faça login novamente.';
  }
  
  if (isAuthorizationError(error)) {
    return 'Você não tem permissão para realizar esta ação.';
  }
  
  if (isDatabaseError(error)) {
    return 'Erro no banco de dados. Tente novamente mais tarde.';
  }
  
  if (isNetworkError(error)) {
    return 'Erro de conexão. Verifique sua internet e tente novamente.';
  }
  
  if (isValidationError(error)) {
    return `Erro de validação: ${message}`;
  }
  
  return message;
}
