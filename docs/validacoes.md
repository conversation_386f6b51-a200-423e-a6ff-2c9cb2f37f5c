# Documentação de Validações de Formulários

Este documento descreve as validações implementadas nos formulários do sistema Carbonário e Carbonaro.

## Índice

1. [Introdução](#introdução)
2. [Validações de Usuários](#validações-de-usuários)
3. [Validações de Precatórios](#validações-de-precatórios)
4. [Validações de Documentos](#validações-de-documentos)
5. [Como Implementar Novas Validações](#como-implementar-novas-validações)

## Introdução

As validações de formulários são essenciais para garantir a integridade dos dados inseridos no sistema. Elas ajudam a prevenir erros e garantem que os dados estejam no formato correto antes de serem salvos no banco de dados.

## Validações de Usuários

### Campos Obrigatórios
- **Nome**: Não pode estar vazio
- **E-mail**: Não pode estar vazio e deve ser um e-mail válido
- **Senha**: Não pode estar vazia e deve ter pelo menos 6 caracteres
- **Cargo**: Deve ser selecionado

### Campos Opcionais com Validação
- **Telefone**: Deve seguir o formato (00) 00000-0000
- **Data de Entrada**: Não pode ser uma data futura

### Exemplo de Implementação

```typescript
// Validar campos obrigatórios
if (!newUser.nome) {
  errors.nome = "Nome é obrigatório";
  firstErrorField = firstErrorField || 'nome';
}

// Validar formato do telefone (opcional)
if (newUser.telefone && !/^\(?\d{2}\)?[\s-]?\d{4,5}-?\d{4}$/.test(newUser.telefone)) {
  errors.telefone = "Formato de telefone inválido. Use (00) 00000-0000";
  firstErrorField = firstErrorField || 'telefone';
}
```

## Validações de Precatórios

### Campos Obrigatórios
- **Número do Precatório**: Não pode estar vazio
- **Tribunal**: Não pode estar vazio
- **Cliente**: Deve ser selecionado
- **Valor Total**: Deve ser um número positivo

### Campos Opcionais com Validação
- **Desconto**: Deve ser um percentual entre 0 e 100
- **Data de Previsão de Pagamento**: Deve ser uma data válida

### Exemplo de Implementação

```typescript
// Validar campos obrigatórios
if (!formValues.numero_precatorio) {
  errors.numero_precatorio = "Número do precatório é obrigatório";
  hasErrors = true;
}

// Validar campos opcionais
if (formValues.desconto && (isNaN(parseFloat(formValues.desconto)) || parseFloat(formValues.desconto) < 0 || parseFloat(formValues.desconto) > 100)) {
  errors.desconto = "Desconto deve ser um percentual entre 0 e 100";
  hasErrors = true;
}
```

## Validações de Documentos

### Campos Obrigatórios
- **Nome**: Deve ter pelo menos 3 caracteres e não mais de 100
- **Conteúdo**: Não pode estar vazio
- **Tipo**: Deve ser selecionado
- **Categoria**: Deve ser selecionado (cliente ou precatório)
- **Cliente/Precatório**: Deve ser selecionado de acordo com a categoria

### Exemplo de Implementação

```typescript
const formSchema = z.object({
  nome: z.string()
    .min(3, { message: 'O nome deve ter pelo menos 3 caracteres' })
    .max(100, { message: 'O nome não pode ter mais de 100 caracteres' })
    .refine(val => val.trim().length > 0, { message: 'O nome não pode conter apenas espaços' }),
  conteudo: z.string()
    .min(1, { message: 'O conteúdo não pode estar vazio' })
    .refine(val => val.trim().length > 0, { message: 'O conteúdo não pode conter apenas espaços ou HTML vazio' }),
  // ...
});
```

## Como Implementar Novas Validações

Para implementar novas validações em formulários existentes, siga estas etapas:

1. **Identificar os Campos**: Determine quais campos precisam de validação e quais regras devem ser aplicadas.

2. **Criar Estado para Erros**: Adicione um estado para armazenar os erros de validação.

```typescript
const [formErrors, setFormErrors] = useState<{
  campo1?: string;
  campo2?: string;
}>({});
```

3. **Implementar Função de Validação**: Crie uma função para validar os campos antes de enviar o formulário.

```typescript
const validarFormulario = () => {
  const errors: {[key: string]: string} = {};
  let hasErrors = false;
  
  // Validar campos
  if (!valor) {
    errors.valor = "Campo obrigatório";
    hasErrors = true;
  }
  
  // Atualizar estado de erros
  setFormErrors(errors);
  return !hasErrors;
};
```

4. **Adicionar Feedback Visual**: Modifique os componentes de entrada para mostrar feedback visual quando houver erros.

```tsx
<Input
  value={valor}
  onChange={handleChange}
  className={`${formErrors.valor ? 'border-red-500' : ''}`}
/>
{formErrors.valor && (
  <p className="text-sm text-red-500">{formErrors.valor}</p>
)}
```

5. **Limpar Erros ao Editar**: Limpe os erros quando o usuário editar o campo.

```typescript
const handleChange = (e) => {
  setValue(e.target.value);
  if (formErrors.valor) {
    setFormErrors(prev => ({ ...prev, valor: undefined }));
  }
};
```

6. **Adicionar Tooltips**: Para melhorar a experiência do usuário, adicione tooltips explicativos.

```tsx
<div className="flex items-center gap-1">
  <label>Campo</label>
  <TooltipProvider>
    <Tooltip>
      <TooltipTrigger className="cursor-help">
        <span className="text-xs text-muted-foreground">(?)</span>
      </TooltipTrigger>
      <TooltipContent>
        <p>Explicação sobre o campo</p>
      </TooltipContent>
    </Tooltip>
  </TooltipProvider>
</div>
```

Seguindo estas etapas, você pode implementar validações consistentes em todos os formulários do sistema.
