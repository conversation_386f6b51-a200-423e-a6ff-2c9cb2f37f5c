# CHANGELOG – Sistema Carbonário e Carbonaro

> Repositório: **CaioJusto/carbonarioecarbonarosistema**  
> Equipe: **Carbonaro e Carbonário**  
> Última atualização: **28/05/2025 – 15:25**

---

## 1. Lin<PERSON> do Tempo das Implementações
| Data | Hora | Descrição |
|------|------|-----------|
| 28/05/2025 | 14:30 | <PERSON><PERSON><PERSON> das melhorias abrangentes do Dashboard |
| 28/05/2025 | 14:42 | Criação do `dashboardServiceEnhanced.ts` com métricas completas |
| 28/05/2025 | 14:53 | Criação do `useDashboardData.ts` com cache e tempo-real |
| 28/05/2025 | 15:08 | Criação da página `DashboardEnhanced.tsx` (UI analítica) |
| 28/05/2025 | 15:21 | Adição do script `sql/dashboard_functions.sql` com 5 funções RPC |
| 28/05/2025 | 15:25 | Geração deste CHANGELOG |

---

## 2. Componentes Criados/Melhorados
- **Serviço** `src/services/dashboardServiceEnhanced.ts`
- **Hook** `src/hooks/useDashboardData.ts`
- **Página** `src/pages/DashboardEnhanced.tsx`
- **Painéis de Filtros** (`AdvancedFiltersPanel`)
- **Cards de Métrica** (`MetricCard`)
- **Seções de Gráficos** (ChartsSection, TeamPerformance)
- **Funções SQL** `sql/dashboard_functions.sql`  
  - `get_dashboard_general_metrics`  
  - `get_dashboard_team_performance`  
  - `get_dashboard_evolution_data`  
  - `get_dashboard_distribution_stats`  
  - `get_dashboard_tasks_stats`

---

## 3. Funcionalidades Implementadas
- Métricas completas: precatórios, tarefas, clientes, valores financeiros.
- Gráficos interativos (Linha, Barra, Pizza, Donut) via **Recharts**.
- Filtros avançados por período, tipo, status, responsáveis, valores.
- Painel de notificações em tempo real.
- Exportação de dados do dashboard (JSON).
- Refresh automático (5 min) + WebSocket Supabase.
- Badge de filtros ativos e indicador de última atualização.

---

## 4. Conectividade com Supabase
- Todas as consultas movidas para **service layer** com Supabase JS.
- Uso de **RLS** já configurado nas tabelas `precatorios`, `tasks`, `clientes`, `profiles`.
- Funções SQL registradas como **RPC** para agregações pesadas.
- Subscrições em tempo real (`channel postgres_changes`) para 4 tabelas-chave.
- Retry automático (3 tentativas) em falhas de rede.

---

## 5. Sistema Multiusuário
- Respeito a `AuthContext` para identificar usuário e role.
- **DataVisibilityGuard** garante exibição conforme permissões:
  - `requireFinancialData`
  - `requireAdminOrManager`
- Filtros de API se adaptam ao `user.role`.
- Métricas de desempenho da equipe visíveis apenas a admin/gerentes.

---

## 6. Casos Personalizados
- **Filtros Personalizados**: painel lateral com DateRangePicker, Selects dinâmicos.
- **Notificações customizadas** (info, alert, success).
- Exibição condicional de dados financeiros e de equipe.
- Exportação paramétrica respeitando filtros aplicados.

---

## 7. Bugs Corrigidos
- Placeholders de loading faltantes em estatísticas.
- Cache não invalidava ao alterar filtros ➜ chamada `clearDashboardCache`.
- UI piscando devido a múltiplos `setState` ➜ consolidado em único hook.
- Ícones duplicados corrigidos em `StatCard`.
- Falha na ordenação de gráficos por mês (agora sort por data).

---

## 8. Próximos Passos
1. Migrar páginas **Precatorios**, **Clientes**, **Tasks** para novo padrão de serviço/hook.
2. Deploy das funções SQL no painel Supabase e criação dos índices sugeridos.
3. Testes E2E de permissões usando Cypress.
4. Implementar exportação CSV/PDF e agendamento de relatórios.
5. Adicionar monitoramento de performance via Supabase Logs + Grafana.

---

## 9. Estrutura Técnica
- **Frontend**: React 18 + TypeScript 5 + Tailwind CSS + ShadCN-UI.
- **State**: Context API + custom hooks.
- **Backend**: Supabase (PostgreSQL, Auth, Realtime, Storage).
- **Serviços**: `src/services/*` encapsulam queries; hooks consomem serviços.
- **Cache**: `cacheService.ts` (TTL, update, warmup, metrics).
- **Logger**: `lib/logger.ts` com namespaces (`authLogger`, `cacheLogger`, etc.).
- **SQL**: Funções RPC + sugestões de índices para big-data.

---

## 10. Padrões Implementados
- **Camada de Serviço** para isolar Supabase da UI.
- **Retry/Back-off** em operações críticas.
- **TTL Cache** por tipo de dado (`CACHE_TTL`).
- **Guards de Permissão** em componentes e rotas.
- **Codestyle**: ESLint + Prettier + commit lint.
- **Documentação**: CHANGELOG contínuo, comentários JSDoc e SQL.
