
    // Script para injetar o perfil de administrador no localStorage
    (function() {
      try {
        // Perfil de administrador
        const userProfile = {
  "id": "ae20f986-9e4e-4398-82ca-c4576f5f5655",
  "email": "<EMAIL>",
  "nome": "<PERSON><PERSON><PERSON>",
  "role": "admin",
  "foto_url": null,
  "status": "ativo",
  "created_at": "2025-05-17T15:54:32.470Z",
  "cargo": "Administrador Principal",
  "departamento": "Diretoria",
  "telefone": "+5511999999999",
  "data_entrada": "2025-05-17"
};
        
        // Salvar no localStorage
        localStorage.setItem('userProfile', JSON.stringify(userProfile));
        
        // Verificar se foi salvo corretamente
        const savedProfile = localStorage.getItem('userProfile');
        if (savedProfile) {
          console.log('Perfil de administrador injetado com sucesso no localStorage!');
          console.log('Perfil:', JSON.parse(savedProfile));
        } else {
          console.error('Falha ao injetar perfil no localStorage!');
        }
      } catch (error) {
        console.error('Erro ao injetar perfil:', error);
      }
    })();
    