
    <!DOCTYPE html>
    <html lang="pt-BR">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Injetar Perfil de Administrador</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
          line-height: 1.6;
        }
        h1 {
          color: #333;
        }
        .success {
          color: green;
          font-weight: bold;
        }
        .error {
          color: red;
          font-weight: bold;
        }
        pre {
          background-color: #f5f5f5;
          padding: 10px;
          border-radius: 5px;
          overflow-x: auto;
        }
        button {
          background-color: #4CAF50;
          color: white;
          padding: 10px 15px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 16px;
          margin-top: 10px;
        }
        button:hover {
          background-color: #45a049;
        }
      </style>
    </head>
    <body>
      <h1>Injetar Perfil de Administrador</h1>
      <p>Esta página irá injetar um perfil de administrador no localStorage do seu navegador.</p>
      
      <h2>Perfil a ser injetado:</h2>
      <pre id="profile-json">{
  "id": "ae20f986-9e4e-4398-82ca-c4576f5f5655",
  "email": "<EMAIL>",
  "nome": "Caio Justo",
  "role": "admin",
  "foto_url": null,
  "status": "ativo",
  "created_at": "2025-05-17T15:54:32.470Z",
  "cargo": "Administrador Principal",
  "departamento": "Diretoria",
  "telefone": "+5511999999999",
  "data_entrada": "2025-05-17"
}</pre>
      
      <button id="inject-btn">Injetar Perfil</button>
      <button id="clear-btn">Limpar Perfil</button>
      
      <h2>Status:</h2>
      <div id="status">Aguardando ação...</div>
      
      <h2>Próximos passos:</h2>
      <ol>
        <li>Clique no botão "Injetar Perfil" acima</li>
        <li>Verifique se o status indica sucesso</li>
        <li>Acesse a aplicação em <a href="http://localhost:8082/" target="_blank">http://localhost:8082/</a></li>
        <li>Faça login com as seguintes credenciais:
          <ul>
            <li>Email: <EMAIL></li>
            <li>Senha: barcelona</li>
          </ul>
        </li>
      </ol>
      
      <script>
        document.getElementById('inject-btn').addEventListener('click', function() {
          try {
            // Perfil de administrador
            const userProfile = {"id":"ae20f986-9e4e-4398-82ca-c4576f5f5655","email":"<EMAIL>","nome":"Caio Justo","role":"admin","foto_url":null,"status":"ativo","created_at":"2025-05-17T15:54:32.470Z","cargo":"Administrador Principal","departamento":"Diretoria","telefone":"+5511999999999","data_entrada":"2025-05-17"};
            
            // Salvar no localStorage
            localStorage.setItem('userProfile', JSON.stringify(userProfile));
            
            // Verificar se foi salvo corretamente
            const savedProfile = localStorage.getItem('userProfile');
            if (savedProfile) {
              document.getElementById('status').innerHTML = '<p class="success">Perfil de administrador injetado com sucesso no localStorage!</p>';
            } else {
              document.getElementById('status').innerHTML = '<p class="error">Falha ao injetar perfil no localStorage!</p>';
            }
          } catch (error) {
            document.getElementById('status').innerHTML = '<p class="error">Erro ao injetar perfil: ' + error.message + '</p>';
          }
        });
        
        document.getElementById('clear-btn').addEventListener('click', function() {
          try {
            localStorage.removeItem('userProfile');
            document.getElementById('status').innerHTML = '<p class="success">Perfil removido do localStorage!</p>';
          } catch (error) {
            document.getElementById('status').innerHTML = '<p class="error">Erro ao remover perfil: ' + error.message + '</p>';
          }
        });
      </script>
    </body>
    </html>
    